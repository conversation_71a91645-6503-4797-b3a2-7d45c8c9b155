FROM golang:1.15-alpine3.12

ARG version
ARG proxy
ARG goproxy
ARG token

# Download packages from aliyun mirrors
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk --update add --no-cache ca-certificates tzdata git

# build bat for healthcheck
RUN GO111MODULE=on GOPROXY=$goproxy GOOS=linux CGO_ENABLED=0 go get -v github.com/astaxie/bat

COPY . /go/src/github.com/MiaoSiLa/missevan-go
RUN cd /go/src/github.com/MiaoSiLa/missevan-go \
  && chmod +x gomod.sh && ./gomod.sh \
  && GO111MODULE=on GOPROXY=$goproxy GOOS=linux CGO_ENABLED=0 go install -v -tags release -ldflags "-X main.Version=$version"

# config dir
RUN mkdir /etc/missevan-go \
  && chown nobody:nobody /etc/missevan-go \
  && chmod a+rw /etc/missevan-go

FROM scratch

COPY --from=0 /usr/share/zoneinfo /usr/share/zoneinfo
#COPY --from=0 /usr/share/ca-certificates /usr/share/ca-certificates
COPY --from=0 /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=0 /etc/passwd /etc/
COPY --from=0 /go/bin/bat /go/bin/missevan-go /bin/
# nobody writable
COPY --from=0 --chown=65534:65534 /etc/missevan-go /etc/missevan-go

WORKDIR /

EXPOSE 3032
USER nobody

HEALTHCHECK --interval=30s --timeout=2s --start-period=5s \
  CMD ["/bin/bat", "-print=", "-pretty=false", "http://localhost:3032/health"]

ENTRYPOINT ["/bin/missevan-go"]
