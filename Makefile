NAME=missevan-go
VERSION=1.2.3
TOKEN=${GITHUB_TOKEN}
REGISTRY_PREFIX=$(if $(REGISTRY),$(addsuffix /, $(REGISTRY)))
APP_NAME?=${NAME}
MGOBRANCH?=release

.PHONY: build publish

check:
	@echo -e 'NAME=${NAME}\nVERSION=${VERSION}\nREGISTRY_PREFIX=${REGISTRY_PREFIX}'
	@echo -e 'PROXY=${BUILD_HTTP_PROXY}\nGOPROXY=${GOPROXY}\nTOKEN=${TOKEN}'

build-geoip-data:
	cd contrib \
	&& docker build --force-rm --build-arg proxy=${BUILD_HTTP_PROXY} \
		--build-arg goproxy=${GOPROXY} \
		-t geoip-data -f Dockerfile.geoip-data . \
	&& docker tag geoip-data ${REGISTRY_PREFIX}geoip-data \
	&& docker push ${REGISTRY_PREFIX}geoip-data

build:
	@echo -e 'NAME=${NAME}\nVERSION=${VERSION}\nPROXY=${BUILD_HTTP_PROXY}\nGOPROXY=${GOPROXY}\n'
	@docker build --force-rm --build-arg version=${VERSION} \
		--build-arg proxy=${BUILD_HTTP_PROXY} \
		--build-arg goproxy=${GOPROXY} \
		--build-arg token=${TOKEN} \
		-t ${NAME}:${VERSION} .
	sed -e "s/^\(FROM ${NAME}\):scratch/\1:${VERSION}/" Dockerfile.geoip | docker build --build-arg version=${VERSION} \
		--build-arg proxy=${BUILD_HTTP_PROXY} \
		--build-arg registry_prefix=${REGISTRY_PREFIX} \
		-t ${NAME}:${VERSION}-geoip -

publish:
	docker tag ${NAME}:${VERSION} ${REGISTRY_PREFIX}${NAME}:${VERSION}
	docker push ${REGISTRY_PREFIX}${NAME}:${VERSION}
	docker tag ${NAME}:${VERSION}-geoip ${REGISTRY_PREFIX}${NAME}:${VERSION}-geoip
	docker push ${REGISTRY_PREFIX}${NAME}:${VERSION}-geoip

version:
	@echo ${VERSION}

rider-build:
	@env token=${TOKEN} mgobranch=${MGOBRANCH} ./gomod.sh
	env GOOS=linux CGO_ENABLED=0 go install -v -tags release -ldflags "-X main.Version=${VERSION}"
	@printf '#!/bin/sh\n\nmkdir -p /etc/${NAME}\nchmod a+rw /etc/${NAME}\nexec su-exec nobody /data/app/${APP_NAME}/${NAME} "$$@"\n' > /go/bin/docker-entrypoint.sh
	chmod +x /go/bin/docker-entrypoint.sh
