{"name": "missevan-go", "main": "index.js", "scripts": {"test": "go test ./..."}, "repository": {"type": "git", "url": "git+https://github.com/MiaoSiLa/missevan-go.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/MiaoSiLa/missevan-go/issues"}, "homepage": "https://github.com/MiaoSiLa/missevan-go#readme", "devDependencies": {"remark-cli": "^6.0.1", "remark-pangu": "github:<PERSON>oSi<PERSON><PERSON>/remark-pangu", "remark-preset-lint-markdown-style-guide": "^2.1.2", "string-width": "^2.1.1", "vfile-reporter-json": "^1.0.2"}}