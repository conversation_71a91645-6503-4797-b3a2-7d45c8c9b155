package logger

import (
	"bytes"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"strings"
	"testing"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger/log"
)

func TestMain(m *testing.M) {
	flag.Parse()
	InitTestLog()

	m.Run()
}

func LogAndAssertJSON(t *testing.T, log func(), assertions func(accessFields Fields, errorFields Fields)) {
	var accessBuffer bytes.Buffer
	var errorBuffer bytes.Buffer

	type LogConfigBackup struct {
		Out       io.Writer
		Formatter logrus.Formatter
	}
	var backups [2]LogConfigBackup

	backups[0].Out = LogAccess.Out
	backups[0].Formatter = LogAccess.Formatter
	backups[1].Out = LogError.Out
	backups[1].Formatter = LogError.Formatter

	LogAccess.Out = &accessBuffer
	LogAccess.Formatter = &logrus.JSONFormatter{}
	LogError.Out = &errorBuffer
	LogError.Formatter = &logrus.JSONFormatter{}

	log()

	LogAccess.Out = backups[0].Out
	LogAccess.Formatter = backups[0].Formatter
	LogError.Out = backups[1].Out
	LogError.Formatter = backups[1].Formatter

	var fields [2]Fields
	for i := 0; i < 2; i++ {
		var buf []byte
		if i == 0 {
			buf = accessBuffer.Bytes()
		} else {
			buf = errorBuffer.Bytes()
		}
		if len(buf) == 0 {
			// no output
			continue
		}
		err := json.Unmarshal(buf, &fields[i])
		assert.Nil(t, err)
	}
	assertions(fields[0], fields[1])
}

func TestGetLevel(t *testing.T) {
	assert := assert.New(t)

	LogAccess.SetLevel(logrus.DebugLevel)
	LogError.SetLevel(logrus.WarnLevel)

	assert.Equal(DebugLevel, GetAccessLevel())
	assert.Equal(WarnLevel, GetErrorLevel())
}

func TestCategory(t *testing.T) {
	assert := assert.New(t)

	entry := Category("abc")
	assert.Equal("abc", entry.Data["category"])
}

func TestInfo(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	LogAndAssertJSON(t, func() {
		Info("test")
	}, func(accessFields Fields, errorFields Fields) {
		assert.Equal(accessFields["msg"], "test")
		assert.Equal(accessFields["level"], "info")
		assert.NotContains(errorFields, "info")
	})
}

func TestError(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	LogAndAssertJSON(t, func() {
		Error("test")
	}, func(accessFields Fields, errorFields Fields) {
		assert.NotContains(accessFields, "error")
		assert.Equal(errorFields["msg"], "test")
		assert.Equal(errorFields["level"], "error")
	})
}

func TestWithFieldInfo(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	LogAndAssertJSON(t, func() {
		WithField("key1", "value1").Info("test")
	}, func(accessFields Fields, errorFields Fields) {
		assert.Equal(accessFields["msg"], "test")
		assert.Equal(accessFields["level"], "info")
		assert.Equal(accessFields["key1"], "value1")
		assert.NotContains(errorFields, "info")
		assert.NotContains(errorFields, "value1")
	})
}

func TestWithFieldError(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	LogAndAssertJSON(t, func() {
		WithField("key1", "value1").Error("test")
	}, func(accessFields Fields, errorFields Fields) {
		assert.NotContains(accessFields, "error")
		assert.NotContains(accessFields, "value1")
		assert.Equal(errorFields["msg"], "test")
		assert.Equal(errorFields["level"], "error")
		assert.Equal(errorFields["key1"], "value1")
	})
}

func TestFormat(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	conf := *log.DefaultConfig
	conf.AccessLevel = "debug"
	assert.NoError(InitLog(&conf))

	originalOutput := LogError.Out
	var output strings.Builder
	LogError.SetOutput(&output)
	defer LogAccess.SetOutput(originalOutput)

	formatter := NewLogFileFormatter("missevan-go")
	LogError.SetFormatter(formatter)

	errSample := errors.New("foo")
	errEmpty := errors.New("")

	ErrServerInternal := NewContextError("服务器内部错误")
	assert.EqualError(ErrServerInternal, "服务器内部错误")

	err := ErrServerInternal.New(errEmpty, Fields{"key": "value", "key2": "value2"})
	err.Log(ErrorLevel, "")
	expected := fmt.Sprintf(" [%s] [%s:%d] key=value key2=value2",
		"error",
		err.caller.File, err.caller.Line)
	assert.Contains(output.String(), expected)

	err = ErrServerInternal.New(errSample, Fields{"key": "value", "key2": "value2"})
	assert.EqualError(err, "服务器内部错误: foo")

	output.Reset()
	err.Log(ErrorLevel, "")
	expected = fmt.Sprintf(" [%s] [%s:%d] foo key=value key2=value2",
		"error",
		err.caller.File, err.caller.Line)
	assert.Contains(output.String(), expected)

	output.Reset()
	err.Log(ErrorLevel, "bar")
	expected = fmt.Sprintf(" [%s] [%s:%d] bar: foo key=value key2=value2",
		"error",
		err.caller.File, err.caller.Line)
	assert.Contains(output.String(), expected)

	formatter2 := NewLogstashFormatter(logrus.Fields{})
	LogError.SetFormatter(formatter2)

	output.Reset()
	err.Log(ErrorLevel, "")
	expected = fmt.Sprintf(`"message":"[%s:%d] foo key=value key2=value2"`,
		err.caller.File, err.caller.Line)
	assert.Contains(output.String(), expected)

	output.Reset()
	err.Log(ErrorLevel, "bar")
	expected = fmt.Sprintf(`"message":"[%s:%d] bar: foo key=value key2=value2"`,
		err.caller.File, err.caller.Line)
	assert.Contains(output.String(), expected)
}

func TestHostAndInstanceID(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	conf := *log.DefaultConfig
	conf.AccessLevel = "debug"
	assert.NoError(InitLog(&conf))

	assert.NotEmpty(Host())
	assert.NotEmpty(InstanceID())
}
