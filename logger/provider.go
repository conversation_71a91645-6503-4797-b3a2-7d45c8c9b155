package logger

import (
	"github.com/sirupsen/logrus"
)

// Debug log as debug level
func Debug(args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Debug(args...)
}

// Debugf log as debug level with format
func Debugf(format string, args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Debugf(format, args...)
}

// Info log as info level
func Info(args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Info(args...)
}

// Infof log as info level with format
func Infof(format string, args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Infof(format, args...)
}

// Warn log as warn level
func Warn(args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Warn(args...)
}

// Warnf log as warn level with format
func Warnf(format string, args ...interface{}) {
	entry := logrus.NewEntry(LogAccess)
	SetCallFrame(entry, CallerSkip)
	entry.Warnf(format, args...)
}

// Error log as error level
func Error(args ...interface{}) {
	entry := logrus.NewEntry(LogError)
	SetCallFrame(entry, CallerSkip)
	entry.Error(args...)
}

// Errorf log as error level with format
func Errorf(format string, args ...interface{}) {
	entry := logrus.NewEntry(LogError)
	SetCallFrame(entry, CallerSkip)
	entry.Errorf(format, args...)
}

// Fatal log as fatal level and exit
func Fatal(args ...interface{}) {
	entry := logrus.NewEntry(LogError)
	SetCallFrame(entry, CallerSkip)
	entry.Fatal(args...)
}

// Fatalf log as fatal level with format and exit
func Fatalf(format string, args ...interface{}) {
	entry := logrus.NewEntry(LogError)
	SetCallFrame(entry, CallerSkip)
	entry.Fatalf(format, args...)
}

// WithField adds a field to the log entry, note that it doesn't log until you
// call Debug, Info, Warn, Error, Fatal or Panic. It only creates a log entry.
// If you want multiple fields, use `WithFields`.
func WithField(key string, value interface{}) *Entry {
	entry := newEntry()
	defer releaseEntry(entry)
	return entry.WithField(key, value)
}

// WithFields adds a struct of fields to the log entry. All it does is call
// `WithField` for each `Field`.
func WithFields(fields Fields) *Entry {
	entry := newEntry()
	defer releaseEntry(entry)
	return entry.WithFields(fields)
}

// Category adds a "category" field to the log entry.
func Category(value interface{}) *Entry {
	return WithField("category", value)
}
