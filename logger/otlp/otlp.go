package otlp

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"sync/atomic"
	"time"

	v1 "go.opentelemetry.io/proto/otlp/collector/logs/v1"
	common "go.opentelemetry.io/proto/otlp/common/v1"
	pb "go.opentelemetry.io/proto/otlp/logs/v1"
	resource "go.opentelemetry.io/proto/otlp/resource/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/MiaoSiLa/missevan-go/logger/env"
	mlog "github.com/MiaoSiLa/missevan-go/logger/log"
)

// Severity is the severity of the event described in a log entry.
type Severity int

const (
	SeverityUnspecified = iota
	SeverityTrace
	SeverityTrace2
	SeverityTrace3
	SeverityTrace4
	SeverityDebug
	SeverityDebug2
	SeverityDebug3
	SeverityDebug4
	SeverityInfo
	SeverityInfo2
	SeverityInfo3
	SeverityInfo4
	SeverityWarn
	SeverityWarn2
	SeverityWarn3
	SeverityWarn4
	SeverityError
	SeverityError2
	SeverityError3
	SeverityError4
	SeverityFatal
	SeverityFatal2
	SeverityFatal3
	SeverityFatal4
)

var (
	// ErrBufferFull is send buffer full.
	ErrBufferFull = errors.New("send buffer full")
	// ErrLogTooLarge represents log too large error.
	ErrLogTooLarge = errors.New("log too large")

	_sdkName = "otel"

	// otelLogExportSuccessDurationHistogram = metrics.LogExportDurationHistogram(_sdkName, true)

	// otelLogExportFailDurationHistogram = metrics.LogExportDurationHistogram(_sdkName, false)

	// otelLogProcessedDroppedCounter = metrics.LogProcessedCounter(_sdkName, true)

	// otelLogProcessedUndroppedCounter = metrics.LogProcessedCounter(_sdkName, false)

	// otelLogQueueSizeGauge = metrics.LogQueueSizeGauge(_sdkName)
)

// Entry is a log entry.
// see https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/logs/data-model.md
type Entry struct {
	// Timestamp is the time of the entry. If zero, the current time is used.
	Timestamp time.Time

	// Severity is the entry's severity level.
	// The zero value is Default. [Optional].
	Severity Severity

	// Additional attributes that describe the specific event occurrence. [Optional].
	// Attribute keys MUST be unique (it is not allowed to have more than one
	// attribute with the same key).
	Attributes map[string]interface{}

	// A value containing the body of the log record. Can be for example a human-readable
	// string message (including multi-line) describing the event in a free form or it can
	// be a structured data composed of arrays and maps of other values. [Optional].
	Body interface{}

	// Flags, a bit field. 8 least significant bits are the trace flags as
	// defined in W3C Trace Context specification. 24 most significant bits are reserved
	// and must be set to 0. Readers must not assume that 24 most significant bits
	// will be zero and must correctly mask the bits when reading 8-bit trace flag (use
	// flags & TRACE_FLAGS_MASK). [Optional].
	Flags uint32

	// TraceID is the resource name of the trace associated with the log entry,
	TraceID []byte

	// ID of the span within the trace associated with the log entry.
	SpanID []byte
}

// Option is client option.
type Option func(*options)

// WithFamily with log family.
func WithFamily(name string) Option {
	return func(o *options) {
		o.Family = name
	}
}

// WithBuffer with chan buffer.
func WithBuffer(n int) Option {
	return func(o *options) {
		o.Buffer = n
	}
}

// WithMaxRetry with max retry.
func WithMaxRetry(n int) Option {
	return func(o *options) {
		o.MaxRetry = n
	}
}

// WithBatchSize with batch size.
func WithBatchSize(n int) Option {
	return func(o *options) {
		o.BatchSize = n
	}
}

// WithBatchTimeout with batch timeout.
func WithBatchTimeout(timeout time.Duration) Option {
	return func(o *options) {
		o.BatchTimeout = timeout
	}
}

// WithSendTimeout with send timeout.
func WithSendTimeout(timeout time.Duration) Option {
	return func(o *options) {
		o.SendTimeout = timeout
	}
}

func WithLogMaxSizeByte(size int) Option {
	return func(o *options) {
		if size >= 0 {
			o.LogMaxSizeByte = size
		}
	}
}

type options struct {
	Family       string
	Buffer       int
	MaxRetry     int
	BatchSize    int
	BatchTimeout time.Duration
	SendTimeout  time.Duration

	LogMaxSizeByte int
}

// Client is OpenTelemetry client.
type Client struct {
	opts     options
	conn     *grpc.ClientConn
	client   v1.LogsServiceClient
	resource *resource.Resource
	md       metadata.MD
	bundler  chan *pb.LogRecord
	drops    int64

	ctx      context.Context
	cancelFn context.CancelFunc
}

// NewClient returns a new logging client associated with the provided options.
func NewClient(target string, opts ...Option) (*Client, error) {
	if err := env.ValidateEnv(); err != nil {
		return nil, err
	}

	opt := options{
		Family:         env.ServiceName,
		Buffer:         10240,
		MaxRetry:       3,
		BatchSize:      128,
		BatchTimeout:   5 * time.Second,
		SendTimeout:    1 * time.Second,
		LogMaxSizeByte: 32768, // 32 KB
	}
	for _, o := range opts {
		o(&opt)
	}
	ctx, cancel := context.WithTimeout(context.Background(), opt.SendTimeout)
	defer cancel()
	conn, err := grpc.DialContext(ctx, target, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}

	ctx1, cancelFn := context.WithCancel(context.Background())
	c := &Client{
		opts:     opt,
		conn:     conn,
		md:       buildMetadata(opt.Family),
		resource: buildResource(),
		client:   v1.NewLogsServiceClient(conn),
		bundler:  make(chan *pb.LogRecord, opt.Buffer),
		ctx:      ctx1,
		cancelFn: cancelFn,
	}
	go c.sendproc()
	return c, nil
}

func (c *Client) sendproc() {
	entries := make([]*pb.LogRecord, 0, c.opts.BatchSize)
	batchTick := time.NewTicker(c.opts.BatchTimeout)
	defer batchTick.Stop()
	var closed bool
	for {
		select {
		case <-c.ctx.Done():
			closed = true
		default:
		}

		select {
		case e := <-c.bundler:
			entries = append(entries, e)
			if len(entries) < c.opts.BatchSize {
				continue
			}
		case <-batchTick.C:
		}
		if len(entries) == 0 && closed {
			return
		}
		c.writeLogEntries(entries)
		entries = entries[:0]
	}
}

func (c *Client) writeLogEntries(entries []*pb.LogRecord) (err error) {
	if len(entries) == 0 {
		return
	}
	req := &v1.ExportLogsServiceRequest{
		ResourceLogs: []*pb.ResourceLogs{{
			Resource: c.resource,
			ScopeLogs: []*pb.ScopeLogs{{
				LogRecords: entries,
			}},
		}},
	}
	ctx := metadata.NewOutgoingContext(context.Background(), c.md)
	for retries := 0; retries < c.opts.MaxRetry; retries++ {
		ctx, cancel := context.WithTimeout(ctx, c.opts.SendTimeout)
		defer cancel()
		// start := time.Now()
		_, err = c.client.Export(ctx, req)
		if err == nil {
			// otelLogExportSuccessDurationHistogram.Observe(float64(time.Since(start).Milliseconds()))
			break
		}
		// otelLogExportFailDurationHistogram.Observe(float64(time.Since(start).Milliseconds()))
		log.Printf("otel: failed to send a batch message retry: %d error: %v", retries, err)

		if shouldRetryOnError(err) && retries < c.opts.MaxRetry-1 {
			time.Sleep(mlog.BackoffWait(retries))
		} else {
			// otelLogProcessedDroppedCounter.Add(float64(len(entries)))
			atomic.AddInt64(&c.drops, int64(len(entries)))
			break
		}
	}
	return
}

func shouldRetryOnError(err error) bool {
	status := status.Convert(err)
	if status.Code() == codes.ResourceExhausted && strings.Contains(status.Message(), "grpc: received message larger than max") {
		return false
	}
	if status.Code() == codes.Internal && strings.Contains(status.Message(), "grpc: error while marshaling") {
		return false
	}
	return true
}

// Log buffers the Entry for output to the OpenTelemetry service.
func (c *Client) Log(e Entry) error {
	// otelLogQueueSizeGauge.Set(float64(len(c.bundler)))
	select {
	case <-c.ctx.Done():
		// otelLogProcessedDroppedCounter.Inc()
		drops := atomic.AddInt64(&c.drops, 1)
		if drops == 1 || drops%int64(c.opts.Buffer) == 0 {
			log.Printf("otel: client has closed. drop %d messages so far\n", drops)
		}
		return nil
	default:
	}

	record := toLogEntryInternal(e)
	if c.opts.LogMaxSizeByte > 0 && proto.Size(record) > c.opts.LogMaxSizeByte {
		// otelLogProcessedDroppedCounter.Inc()
		drops := atomic.AddInt64(&c.drops, 1)
		if drops == 1 || drops%int64(c.opts.Buffer) == 0 {
			log.Printf("otel: log record size exceeds %d bytes. drop %d messages so far\n", c.opts.LogMaxSizeByte, drops)
		}
		return ErrLogTooLarge
	}
	select {
	case c.bundler <- record:
		// otelLogProcessedUndroppedCounter.Inc()
	default:
		// otelLogProcessedDroppedCounter.Inc()
		drops := atomic.AddInt64(&c.drops, 1)
		if drops == 1 || drops%int64(c.opts.Buffer) == 0 {
			log.Printf("otel: send queue full. drop %d messages so far\n", drops)
		}
		return ErrBufferFull
	}
	return nil
}

// Close waits for all opened loggers to be flushed and closes the client.
func (c *Client) Close() error {
	c.cancelFn()
	<-time.After(c.opts.SendTimeout)
	return c.conn.Close()
}

func toLogEntryInternal(e Entry) *pb.LogRecord {
	return &pb.LogRecord{
		TimeUnixNano:   uint64(e.Timestamp.UnixNano()),
		SeverityNumber: pb.SeverityNumber(e.Severity),
		SeverityText:   toLogSeverityText(e.Severity),
		Body:           toAnyValue(e.Body),
		Attributes:     toKeyValue(e.Attributes),
		Flags:          e.Flags,
		TraceId:        e.TraceID,
		SpanId:         e.SpanID,
	}
}

func toLogSeverityText(s Severity) string {
	switch {
	case s >= SeverityTrace && s <= SeverityTrace4:
		return "TRACE"
	case s >= SeverityDebug && s <= SeverityDebug4:
		return "DEBUG"
	case s >= SeverityInfo && s <= SeverityInfo4:
		return "INFO"
	case s >= SeverityWarn && s <= SeverityWarn4:
		return "WARN"
	case s >= SeverityError && s <= SeverityError4:
		return "ERROR"
	case s >= SeverityFatal && s <= SeverityFatal4:
		return "FATAL"
	}
	return ""
}

func toAnyValue(body interface{}) *common.AnyValue {
	switch v := body.(type) {
	case string:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: v}}
	case bool:
		return &common.AnyValue{Value: &common.AnyValue_BoolValue{BoolValue: v}}
	case int:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case int8:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case int16:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case int32:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case int64:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: v}}
	case uint:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case uint8:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case uint16:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case uint32:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case uint64:
		return &common.AnyValue{Value: &common.AnyValue_IntValue{IntValue: int64(v)}}
	case float32:
		return &common.AnyValue{Value: &common.AnyValue_DoubleValue{DoubleValue: float64(v)}}
	case float64:
		return &common.AnyValue{Value: &common.AnyValue_DoubleValue{DoubleValue: v}}
	case nil:
		return nil
	case error:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: v.Error()}}
	case fmt.Stringer:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: v.String()}}
	case time.Duration:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: v.String()}}
	case time.Time:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: v.Format(time.RFC3339)}}
	case []byte:
		return &common.AnyValue{Value: &common.AnyValue_BytesValue{BytesValue: v}}
	case []interface{}:
		av := &common.ArrayValue{Values: make([]*common.AnyValue, 0, len(v))}
		for _, next := range v {
			if next == nil {
				continue
			}
			av.Values = append(av.Values, toAnyValue(next))
		}
		return &common.AnyValue{Value: &common.AnyValue_ArrayValue{ArrayValue: av}}
	case map[string]interface{}:
		kvs := &common.KeyValueList{Values: make([]*common.KeyValue, 0, len(v))}
		for k, next := range v {
			if next == nil {
				continue
			}
			kvs.Values = append(kvs.Values, &common.KeyValue{Key: k, Value: toAnyValue(next)})
		}
		return &common.AnyValue{Value: &common.AnyValue_KvlistValue{KvlistValue: kvs}}
	default:
		return &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: fmt.Sprint(v)}}
	}
}

func toKeyValue(attrs map[string]interface{}) []*common.KeyValue {
	kvs := make([]*common.KeyValue, 0, len(attrs))
	for k, v := range attrs {
		if v == nil {
			continue
		}
		kvs = append(kvs, &common.KeyValue{
			Key:   k,
			Value: toAnyValue(v),
		})
	}
	return kvs
}

func buildResource() *resource.Resource {
	return &resource.Resource{
		Attributes: []*common.KeyValue{{
			Key:   "host.name",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.Hostname}},
		}, {
			Key:   "host.region",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.Region}},
		}, {
			Key:   "host.zone",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.Zone}},
		}, {
			Key:   "host.color",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.Color}},
		}, {
			Key:   "host.deploy_env",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.DeployEnv}},
		}, {
			Key:   "service.name",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.ServiceName}},
		}, {
			Key:   "service.version",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: env.ServiceVersion}},
		}, {
			Key:   "telemetry.sdk.name",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: "logging-sdk-go"}},
		}, {
			Key:   "telemetry.sdk.language",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: "go"}},
		}, {
			Key:   "telemetry.sdk.version",
			Value: &common.AnyValue{Value: &common.AnyValue_StringValue{StringValue: "1.0.0"}},
		}},
	}
}

func buildMetadata(serviceName string) metadata.MD {
	return metadata.New(
		map[string]string{
			"service.name":          serviceName,
			"bilibili.log.taskId":   env.TaskID,
			"bilibili.spec.version": "0.16.0",
		},
	)
}
