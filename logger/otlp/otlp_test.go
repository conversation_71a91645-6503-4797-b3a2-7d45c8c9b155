package otlp

import (
	"context"
	"fmt"
	"net"
	"testing"
	"time"

	v1 "go.opentelemetry.io/proto/otlp/collector/logs/v1"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

type logServer struct {
	v1.UnimplementedLogsServiceServer
}

func (s *logServer) Export(ctx context.Context, req *v1.ExportLogsServiceRequest) (*v1.ExportLogsServiceResponse, error) {
	b, err := protojson.Marshal(req)
	if err != nil {
		return nil, err
	}
	fmt.Println(string(b))
	return &v1.ExportLogsServiceResponse{}, nil
}

func TestClient(t *testing.T) {
	socketAddr := "/tmp/collector.sock"
	lis, err := net.Listen("unix", socketAddr)
	if err != nil {
		t.Fatalf("failed to listen: %v", err)
	}
	s := grpc.NewServer()
	v1.RegisterLogsServiceServer(s, &logServer{})
	go func() {
		if err = s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	cli, err := NewClient("unix://"+socketAddr,
		WithBatchTimeout(time.Second),
		//  WithLogMaxSizeByte(32768),
	)
	if err != nil {
		t.Fatal(err)
	}
	for i := 0; i < 1024; i++ {
		cli.Log(Entry{
			Severity: SeverityInfo,
			Attributes: map[string]interface{}{
				"key": "value",
			},
			Body: string([]byte{65, 227}),
		})
	}

	time.Sleep(time.Second * 5)
	cli.Close()
	s.Stop()
}

var smallLogEnt = Entry{
	Severity: SeverityInfo,
	Attributes: map[string]interface{}{
		"key": "value",
	},
	Body: "hello world",
}

var largeLogEnt = Entry{
	Severity: SeverityInfo,
	Attributes: map[string]interface{}{
		"email":       "<EMAIL>",
		"user":        "Schimmel1690",
		"word":        "which",
		"url":         "https://www.humanvirtual.name/eyeballs/distributed/collaborative",
		"status":      503,
		"hack":        "Use the solid state SQL alarm, then you can quantify the redundant program!",
		"goroutineId": 17,
		"emoji":       "🐄",
		"bracket":     "before<content>after",
		"bool":        1,
		"number":      3631,
		"method":      "POST",
		"phone":       "1028547312",
		"ip":          "************",
		"source":      "/go/src/fakelog/main.go:79",
	},
	Body: "'The SDD bandwidth is down, calculate the redundant circuit so we can synthesize the USB bandwidth!",
}

func TestLogSize(t *testing.T) {
	rec := toLogEntryInternal(largeLogEnt)
	size := proto.Size(rec)
	fmt.Println("proto.Size", size)
	bs, _ := proto.Marshal(rec)
	fmt.Println("proto.Marshal", len(bs))
	if size != len(bs) {
		t.Fail()
	}
}

// BenchmarkSmallLogSize-12         5198010               243.8 ns/op             0 B/op          0 allocs/op
// BenchmarkSmallLogSize-12         4881631               258.4 ns/op             0 B/op          0 allocs/op
// BenchmarkSmallLogSize-12         4416736               257.7 ns/op             0 B/op          0 allocs/op
func BenchmarkSmallLogSize(b *testing.B) {
	rec := toLogEntryInternal(smallLogEnt)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		proto.Size(rec)
	}
}

// BenchmarkLargeLogSize-12          895358              1507 ns/op               0 B/op          0 allocs/op
// BenchmarkLargeLogSize-12          916772              1537 ns/op               0 B/op          0 allocs/op
// BenchmarkLargeLogSize-12          862096              1725 ns/op               0 B/op          0 allocs/op
func BenchmarkLargeLogSize(b *testing.B) {
	rec := toLogEntryInternal(largeLogEnt)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		proto.Size(rec)
	}
}

// BenchmarkSmallLogMarshal-12      1773686               636.2 ns/op            48 B/op          1 allocs/op
// BenchmarkSmallLogMarshal-12      1769895               627.3 ns/op            48 B/op          1 allocs/op
// BenchmarkSmallLogMarshal-12      1795965               684.0 ns/op            48 B/op          1 allocs/op
func BenchmarkSmallLogMarshal(b *testing.B) {
	rec := toLogEntryInternal(smallLogEnt)
	for i := 0; i < b.N; i++ {
		proto.Marshal(rec)
	}
}

// BenchmarkLargeLogMarshal-12       259531              4100 ns/op             640 B/op          1 allocs/op
// BenchmarkLargeLogMarshal-12       277227              4091 ns/op             640 B/op          1 allocs/op
// BenchmarkLargeLogMarshal-12       269044              4221 ns/op             640 B/op          1 allocs/op
func BenchmarkLargeLogMarshal(b *testing.B) {
	rec := toLogEntryInternal(largeLogEnt)
	for i := 0; i < b.N; i++ {
		proto.Marshal(rec)
	}
}
