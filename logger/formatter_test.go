package logger

import (
	"os"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetRelativePath(t *testing.T) {
	assert := assert.New(t)

	filePath := "/a/b/cc/d/e/f/g.go"
	currentProjectName = "c"
	assert.Equal(filePath, getRelativePath(filePath))

	currentProjectName = "a/b"
	assert.Equal("cc/d/e/f/g.go", getRelativePath(filePath))

	// MiaoSiLa 比 maoer 优先
	currentProjectName = "h"
	filePath = "/a/b/MiaoSiLa/maoer/d/e/f/g.go"
	assert.Equal("maoer/d/e/f/g.go", getRelativePath(filePath))

	filePath = "/a/maoer/c/d/e/f/g.go"
	assert.Equal("c/d/e/f/g.go", getRelativePath(filePath))

	filePath = "/a/b/c/d/e/f/g.go"
	assert.Equal(filePath, getRelativePath(filePath))
}

func TestBillionsFormatter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const deployEnvKey = "DEPLOY_ENV"
	deployEnv := os.Getenv(deployEnvKey)
	_ = os.Setenv(deployEnvKey, "dev")
	defer func() {
		_ = os.Setenv(deployEnvKey, deployEnv)
	}()
	fields := logrus.Fields{
		"app_id":      "test-appid",
		"host":        "test-host",
		"instance_id": "test-instanceid",
	}
	f := NewBillionsFormatter(fields, false)
	assert.NotNil(f)

	loc, err := time.LoadLocation("Asia/Shanghai")
	require.NoError(err)
	ti := time.Date(2023, time.May, 22, 11, 20, 0, 100*int(time.Millisecond), loc)
	e := &logrus.Entry{
		Data:    logrus.Fields{"test": "1"},
		Time:    ti,
		Level:   logrus.ErrorLevel,
		Message: "test-log",
	}
	result, err := f.Format(e)
	require.NoError(err)
	assert.Equal([]byte(`{"app_id":"test-appid","env":"dev","host":"test-host","instance_id":"test-instanceid",`+
		`"level":"ERROR","log":"test-log test=1","time":"2023-05-22T11:20:00+0800"`+
		"}\n"), result)
}
