package logger

import (
	"bytes"
	"fmt"
	"runtime"
	"sort"

	"github.com/sirupsen/logrus"

	"github.com/MiaoSiLa/missevan-go/logger/otlp"
)

const (
	_source = "source"
)

// OtelHandler .
type OtelHandler struct {
	client *otlp.Client

	Fields           logrus.Fields
	DisableSorting   bool
	QuoteEmptyFields bool
}

func toLogSeverity(lv logrus.Level) otlp.Severity {
	switch lv {
	case logrus.DebugLevel:
		return otlp.SeverityDebug
	case logrus.InfoLevel:
		return otlp.SeverityInfo
	case logrus.WarnLevel:
		return otlp.SeverityWarn
	case logrus.ErrorLevel:
		return otlp.SeverityError
	case logrus.FatalLevel:
		return otlp.SeverityFatal
	}
	return otlp.SeverityInfo
}

// NewOtel returns logrus hook for otel output
func NewOtel(target string, fields logrus.Fields, opts ...otlp.Option) (*OtelHandler, error) {
	client, err := otlp.NewClient(target, opts...)
	if err != nil {
		return nil, err
	}
	return &OtelHandler{
		client: client,
		Fields: fields,
	}, nil
}

// Fire takes, formats and sends the entry to Logstash.
// Hook's formatter is used to format the entry into Logstash format
// and Hook's writer is used to write the formatted entry to the Logstash instance.
func (h *OtelHandler) Fire(e *logrus.Entry) error {
	entry := otlp.Entry{
		Timestamp:  e.Time,
		Severity:   toLogSeverity(e.Level),
		Attributes: make(map[string]interface{}, len(h.Fields)+2),
	}
	extras := make(logrus.Fields, len(e.Data))
	for k, v := range h.Fields {
		entry.Attributes[k] = v
	}
	for k, v := range e.Data {
		if _, ok := h.Fields[k]; ok {
			switch v := v.(type) {
			case error:
				entry.Attributes[k] = v.Error()
			default:
				entry.Attributes[k] = v
			}
		} else {
			switch v := v.(type) {
			case error:
				extras[k] = v.Error()
			default:
				extras[k] = v
			}
		}
	}
	message := e.Message
	if len(extras) > 0 {
		b := &bytes.Buffer{}
		if !h.DisableSorting {
			extraKeys := make([]string, 0, len(extras))
			for k := range extras {
				extraKeys = append(extraKeys, k)
			}
			sort.Strings(extraKeys)
			for _, k := range extraKeys {
				appendKeyValue(b, k, extras[k], h.QuoteEmptyFields)
			}
		} else {
			for k, v := range extras {
				appendKeyValue(b, k, v, h.QuoteEmptyFields)
			}
		}
		message += " " + b.String()
	}
	entry.Body = message
	if e.Context != nil {
		caller, _ := e.Context.Value(keyCaller).(*runtime.Frame)
		if caller != nil {
			entry.Attributes[_source] = fmt.Sprintf("%s:%d", caller.File, caller.Line)
		}
	}
	_ = h.client.Log(entry)
	return nil
}

// Levels returns all logrus levels.
func (h *OtelHandler) Levels() []logrus.Level {
	return logrus.AllLevels
}
