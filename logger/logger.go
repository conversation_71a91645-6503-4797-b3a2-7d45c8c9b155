package logger

import (
	"net/url"
	"os"
	"strings"

	"github.com/sirupsen/logrus"
	logrusagent "github.com/tengattack/logrus-agent-hook"

	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-go/logger/otlp"
)

var conf *log.Config

var (
	// LogAccess is log access log
	LogAccess *logrus.Logger
	// LogError is log error log
	LogError *logrus.Logger

	// CallerSkip .
	CallerSkip = 1
)

// Level type
type Level int

// logging levels
const (
	PanicLevel = Level(logrus.PanicLevel)
	FatalLevel = Level(logrus.FatalLevel)
	ErrorLevel = Level(logrus.ErrorLevel)
	WarnLevel  = Level(logrus.WarnLevel)
	InfoLevel  = Level(logrus.InfoLevel)
	DebugLevel = Level(logrus.DebugLevel)
	TraceLevel = Level(logrus.TraceLevel)
)

func getLogger(level Level) *logrus.Logger {
	if level <= WarnLevel {
		return LogError
	}
	return LogAccess
}

// GetAccessLevel returns access log level
func GetAccessLevel() Level {
	return Level(LogAccess.GetLevel())
}

// GetErrorLevel returns error log level
func GetErrorLevel() Level {
	return Level(LogError.GetLevel())
}

// Host returns current host
func Host() string {
	return conf.Agent.Host
}

// InstanceID returns current instance id
func InstanceID() string {
	return conf.Agent.InstanceID
}

// InitLog use for initial log module
// Deprecated: 使用 Init 初始化
func InitLog(logConf *log.Config) error {
	return Init(logConf.Agent.AppID, logConf)
}

// Init used for initial log module
func Init(projectName string, logConf *log.Config) error {
	currentProjectName = projectName
	err := log.InitLog(logConf)
	if err != nil {
		return err
	}
	LogAccess, LogError = log.LogAccess, log.LogError

	conf = log.GetLogConfig()

	fields := logrus.Fields{
		"app_id":      conf.Agent.AppID,
		"host":        conf.Agent.Host,
		"instance_id": conf.Agent.InstanceID,
	}
	if conf.Agent.Category != "" {
		fields["category"] = conf.Agent.Category
	}

	var logFileFormatter logrus.Formatter
	if logConf.Format == FormatBillions {
		logFileFormatter = NewBillionsFormatter(fields, false)
	} else {
		logFileFormatter = NewLogFileFormatter(conf.Agent.AppID)
	}
	if logConf.AccessLog != "" {
		LogAccess.SetFormatter(logFileFormatter)
	}
	if logConf.ErrorLog != "" {
		LogError.SetFormatter(logFileFormatter)
	}

	// configure logstash
	LogAccess.ReplaceHooks(make(logrus.LevelHooks))
	LogError.ReplaceHooks(make(logrus.LevelHooks))
	if conf != nil && conf.Agent.Enabled {
		_, err := url.Parse(conf.Agent.DSN)
		if err != nil {
			return err
		}

		const otelPrefix = "otel+"
		isOtel := strings.HasPrefix(conf.Agent.DSN, otelPrefix)
		if isOtel {
			otelFields := logrus.Fields{}
			nodeName := os.Getenv("NODE_NAME")
			if nodeName != "" {
				otelFields["node.name"] = nodeName
			}
			if conf.Agent.Category != "" {
				otelFields["category"] = conf.Agent.Category
			}
			dsn := conf.Agent.DSN[len(otelPrefix):]
			otelHook, err := NewOtel(
				dsn,
				otelFields,
				otlp.WithFamily(conf.Agent.AppID),
				otlp.WithBuffer(conf.Agent.ChannelSize),
			)
			if err != nil {
				return err
			}
			LogAccess.Hooks.Add(otelHook)
			LogError.Hooks.Add(otelHook)
		} else {
			var opt logrusagent.Options
			opt.ChannelSize = conf.Agent.ChannelSize

			var agentFormatter logrus.Formatter
			if logConf.Format == FormatBillions {
				agentFormatter = NewBillionsFormatter(fields, true)
			} else {
				agentFormatter = NewLogstashFormatter(fields)
			}
			hook, _ := logrusagent.New(conf.Agent.DSN, agentFormatter, opt)
			LogAccess.Hooks.Add(hook)
			LogError.Hooks.Add(hook)
		}
	}

	return nil
}
