//go:build !release
// +build !release

package logger

import (
	"os"
	"regexp"
	"runtime"

	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-go/util"
)

var regAppID = regexp.MustCompile("(?i)/miaosila/(.*?)/")

// InitTestLog init log for testing only
func InitTestLog() *log.Config {
	conf := *log.DefaultConfig
	for _, arg := range os.Args {
		// 因为要防止在正常编译环境引入 tutil，所以不能使用 tutil 的变量 debug 判断
		if arg == "-test.v=true" {
			conf.AccessLevel = "debug"
			break
		}
	}
	_, f, _, _ := runtime.Caller(1)
	// 使用 getRelativePath 的情况下多次调用 InitTestLog 会导致 app_id 不是项目名称
	matches := regAppID.FindStringSubmatch(f)
	if len(matches) > 1 {
		conf.Agent.AppID = matches[1]
	}
	err := Init(conf.Agent.AppID, &conf)
	if err != nil {
		panic(err)
	}
	util.InitGoroutineLogger(LogError)
	return &conf
}
