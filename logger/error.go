package logger

import (
	"context"
	"fmt"
	"runtime"

	"github.com/sirupsen/logrus"
)

// ContextLogger ContextLogger interface
type ContextLogger interface {
	Log(Level, string)
}

const defaultContextErrorSkip = 1

// ContextError implements the error interface.
type ContextError struct {
	title  string
	skip   int // 堆栈忽略层数，初始值为 1
	caller *runtime.Frame
	err    error
	data   logrus.Fields
}

type contextKey int

const (
	keyCaller contextKey = iota
)

// NewContextError new ContextError with title and default code
func NewContextError(title string) *ContextError {
	return &ContextError{title: title, skip: defaultContextErrorSkip}
}

// SetSkip 设置日志堆栈跳出层数，初始值为 1。堆栈每增加一层，skip 需要注意 +1
func (e *ContextError) SetSkip(skip int) {
	e.skip = skip
}

// New returns a new ContextError which records the call frame.
// It will panic if e.err != nil || err == nil.
// Use the Log method to print the call frame.
func (e *ContextError) New(err error, data Fields) *ContextError {
	if err == nil {
		panic("nil err")
	}
	if _, ok := err.(*ContextError); ok {
		panic("err must not to be *ContextError")
	}
	if e.err != nil {
		panic("error has already been set")
	}
	return &ContextError{
		title:  e.title,
		skip:   e.skip,
		caller: getCallFrame(e.skip),
		err:    err,
		data:   logrus.Fields(data),
	}
}

// Log logs err and data in e. Use this method to print the stored call frame (with the LogFileFormatter or LogstashFormatter).
// It will panic if the given logger is nil.
func (e *ContextError) Log(level Level, namespace string) {
	entry := getLogger(level).WithContext(context.WithValue(context.Background(), keyCaller, e.caller))
	if e.data != nil {
		entry = entry.WithFields(e.data)
	}
	if namespace != "" {
		namespace += ": "
	}
	msg := fmt.Sprintf("%s%v", namespace, e.err)
	switch level {
	case PanicLevel:
		entry.Panic(msg)
	case FatalLevel:
		entry.Fatal(msg)
	case ErrorLevel:
		entry.Error(msg)
	case WarnLevel:
		entry.Warn(msg)
	case InfoLevel:
		entry.Info(msg)
	case DebugLevel:
		entry.Debug(msg)
	case TraceLevel:
		entry.Trace(msg)
	default:
		entry.WithField("level", level).Errorf(msg)
	}
}

// Error returns the error info
func (e *ContextError) Error() string {
	if e.err == nil {
		return e.Title()
	}
	return e.Title() + ": " + e.err.Error()
}

// Title gets the title of the ContextError
func (e *ContextError) Title() string {
	return e.title
}

func getCallFrame(skip int) *runtime.Frame {
	_, file, line, _ := runtime.Caller(skip + 1)
	return &runtime.Frame{
		File: getRelativePath(file),
		Line: line,
	}
}
