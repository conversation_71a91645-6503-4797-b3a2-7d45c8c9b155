package env

import (
	"fmt"
	"os"
)

var (
	defaultTaskIDs = map[string]string{
		"dev":  "000069",
		"fat1": "000069",
		"uat":  "000069",
		"pre":  "000161",
		"prod": "000161",
	}

	Region         = os.Getenv("REGION")
	Zone           = os.Getenv("ZONE")
	Color          = os.Getenv("DEPLOY_COLOR")
	Hostname       = os.Getenv("HOSTNAME")
	DeployEnv      = os.Getenv("DEPLOY_ENV")
	ServiceName    = os.Getenv("APP_ID")
	ServiceVersion = os.Getenv("APP_VERSION")
	TaskID         = defaultTaskIDs[DeployEnv]
)

func init() {
	if Hostname == "" {
		Hostname, _ = os.Hostname()
	}
}

func ValidateEnv() error {
	if DeployEnv == "" {
		return fmt.Errorf("env DEPLOY_ENV is empty, valid values: dev/fat1/uat/pre/prod")
	}
	if TaskID == "" {
		return fmt.Errorf("TaskID is empty")
	}
	return nil
}
