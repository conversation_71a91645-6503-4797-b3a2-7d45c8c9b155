package logger

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewContestErrors(t *testing.T) {
	t.Run("NewContextError", func(t *testing.T) {
		assert := assert.New(t)
		err := NewContextError("test")
		assert.Equal("test", err.Title())
	})
}

func TestContextErrorNew(t *testing.T) {
	assert := assert.New(t)

	inErr := errors.New("test")
	e0 := NewContextError("title")
	e1 := e0.New(inErr, Fields{"a": 1})
	assert.True(e1.err != nil && e1.err.Error() == "test")
	assert.Equal("title", e1.title)
	assert.Panics(func() {
		defer func() {
			if msg, ok := recover().(string); ok && "nil err" == msg {
				panic(true)
			}
		}()
		_ = e0.New(nil, nil)
	})
	assert.Panics(func() {
		defer func() {
			if msg, ok := recover().(string); ok && "err must not to be *ContextError" == msg {
				panic(true)
			}
		}()
		_ = e1.New(e0, nil)
	})
	assert.Panics(func() {
		defer func() {
			if msg, ok := recover().(string); ok && "error has already been set" == msg {
				panic(true)
			}
		}()
		_ = e1.New(errors.New("test"), nil)
	})
}

func TestContextErrorLog(t *testing.T) {
	assert := assert.New(t)
	e := NewContextError("title").New(errors.New("test error"), Fields{"key": false})
	assert.NotPanics(func() {
		for i := Level(ErrorLevel); i < TraceLevel+2; i++ {
			e.Log(i, "")
		}
	})
	assert.Panics(func() {
		e.Log(PanicLevel, "panic")
	})
}

func TestContextErrorError(t *testing.T) {
	assert := assert.New(t)
	e0 := NewContextError("title")
	e1 := e0.New(errors.New("test error"), nil)
	e2 := NewContextError("title")
	e2.err = e0
	assert.Equal("title", e0.Error())
	assert.Equal("title: test error", e1.Error())
}
