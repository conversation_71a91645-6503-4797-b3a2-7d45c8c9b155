package log

import (
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/term"
)

// ErrLogFileNotOpen indicates the log file not open success after rotating.
var ErrLogFileNotOpen = errors.New("log file not open")

// Config is logging config.
type Config struct {
	Format      string      `yaml:"format"`
	AccessLog   string      `yaml:"access_log"`
	AccessLevel string      `yaml:"access_level"`
	ErrorLog    string      `yaml:"error_log"`
	ErrorLevel  string      `yaml:"error_level"`
	Rotate      string      `yaml:"rotate"`
	Agent       AgentConfig `yaml:"agent"`
}

// AgentConfig is sub section of LogConfig.
type AgentConfig struct {
	Enabled     bool   `yaml:"enabled"`
	DSN         string `yaml:"dsn"`
	AppID       string `yaml:"app_id"`
	Host        string `yaml:"host"`
	InstanceID  string `yaml:"instance_id"`
	Category    string `yaml:"category"`
	ChannelSize int    `yaml:"channel_size,omitempty"`
}

// EmptyFormatter output nothing
type EmptyFormatter struct {
}

// RotateFileWriter .
type RotateFileWriter struct {
	TimeFormat string

	filename string // should be set to the actual filename
	mu       sync.Mutex
	fp       *os.File
	running  bool
}

var (
	// IsTerm instructs current stdout whether is terminal
	IsTerm bool
	// LogAccess is log access log
	LogAccess *logrus.Logger
	// LogError is log error log
	LogError *logrus.Logger
	// conf package config
	conf *Config
)

// DefaultConfig is default configuration
var DefaultConfig = &Config{
	Format:      "string",
	AccessLog:   "stdout",
	AccessLevel: "info",
	ErrorLog:    "stderr",
	ErrorLevel:  "error",
	Rotate:      "",
	Agent: AgentConfig{
		Enabled: false,
	},
}

func init() {
	IsTerm = term.IsTerminal(int(os.Stdout.Fd()))
}

func formatString(str string, params map[string]string) string {
	for key, value := range params {
		str = strings.ReplaceAll(str, "${"+key+"}", value)
	}
	return str
}

// BackoffWait returns wait time for logging write retry
func BackoffWait(retry int) time.Duration {
	// After the first retry, do exponential backoff with 10% jitter.
	if retry == 0 {
		return time.Duration(0)
	}
	backoff := float64(uint(1) << (uint(retry) - 1))
	backoff += backoff * (0.1 * rand.Float64())
	// max 120s
	if backoff > 120 {
		backoff = 120
	}
	return time.Millisecond * time.Duration(backoff*1000)
}

// GetLogConfig return current log config
func GetLogConfig() *Config {
	return conf
}

// InitLog use for initial log module
func InitLog(logConf *Config) error {
	var err error

	if logConf != nil {
		conf = logConf
	} else {
		conf = DefaultConfig
	}
	// get default host and instance id from environment variables or hostname
	if conf.Agent.Host == "" || conf.Agent.InstanceID == "" {
		hostname, _ := os.Hostname()
		if conf.Agent.Host == "" {
			host := os.Getenv("NODE_NAME")
			if host == "" {
				host = os.Getenv("HOST")
				if host == "" {
					host = hostname
				}
			}
			conf.Agent.Host = host
		}
		if conf.Agent.InstanceID == "" {
			instanceID := os.Getenv("INSTANCE_ID")
			if instanceID == "" {
				instanceID = hostname
			}
			conf.Agent.InstanceID = instanceID
		}
	}
	// default channel size to 1024 if invalid or not set
	if conf.Agent.ChannelSize <= 0 {
		conf.Agent.ChannelSize = 1024
	}

	// init logger
	LogAccess = logrus.New()
	LogError = logrus.New()

	LogAccess.Formatter = &logrus.TextFormatter{
		TimestampFormat: "2006/01/02 - 15:04:05",
		FullTimestamp:   true,
	}

	LogError.Formatter = &logrus.TextFormatter{
		TimestampFormat: "2006/01/02 - 15:04:05",
		FullTimestamp:   true,
	}

	// set logger
	if err = SetLogLevel(LogAccess, conf.AccessLevel); err != nil {
		return errors.New("Set access log level error: " + err.Error())
	}

	if err = SetLogLevel(LogError, conf.ErrorLevel); err != nil {
		return errors.New("Set error log level error: " + err.Error())
	}

	if err = SetLogOut(LogAccess, conf.AccessLog, conf.Rotate, &conf.Agent); err != nil {
		return errors.New("Set access log path error: " + err.Error())
	}

	if err = SetLogOut(LogError, conf.ErrorLog, conf.Rotate, &conf.Agent); err != nil {
		return errors.New("Set error log path error: " + err.Error())
	}

	return nil
}

// SetLogOut provide log stdout and stderr output
func SetLogOut(log *logrus.Logger, outString, rotate string, agent *AgentConfig) error {
	switch outString {
	case "stdout":
		log.Out = os.Stdout
	case "stderr":
		log.Out = os.Stderr
	case "":
		log.Out = io.Discard
		log.Formatter = NewEmptyFormatter()
	default:
		fields := make(map[string]string, 2)
		if agent != nil {
			if agent.InstanceID != "" {
				fields["instance_id"] = agent.InstanceID
			}
			if agent.Category != "" {
				fields["category"] = agent.Category
			}
		}
		outFilename := formatString(outString, fields)

		// try mkdir
		dir := filepath.Dir(outFilename)
		_ = os.MkdirAll(dir, 0777)

		switch rotate {
		case "daily":
			rfw := RotateFileWriter{TimeFormat: "20060102", filename: outFilename}
			f, err := rfw.Open()
			if err != nil {
				return err
			}
			log.Out = f
		case "":
			f, err := os.OpenFile(outFilename, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
			if err != nil {
				return err
			}
			log.Out = f
		default:
			return fmt.Errorf("invalid rotate: %s", rotate)
		}
	}

	return nil
}

// SetLogLevel is define log level what you want
// log level: panic, fatal, error, warn, info and debug
func SetLogLevel(log *logrus.Logger, levelString string) error {
	level, err := logrus.ParseLevel(levelString)

	if err != nil {
		return err
	}

	log.Level = level

	return nil
}

// NewEmptyFormatter return the log format for output nothing
func NewEmptyFormatter() *EmptyFormatter {
	return &EmptyFormatter{}
}

// Format renders a single log entry
func (f *EmptyFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	return nil, nil
}

// Open the log file
func (w *RotateFileWriter) Open() (io.Writer, error) {
	w.mu.Lock()
	defer w.mu.Unlock()
	if w.fp != nil {
		return w, nil
	}
	err := w.openFile()
	if err != nil {
		return nil, err
	}
	if !w.running {
		w.running = true
		go w.workproc()
	}
	return w, nil
}

func (w *RotateFileWriter) openFile() error {
	f, err := os.OpenFile(w.filename, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		return err
	}
	w.fp = f
	return nil
}

func (w *RotateFileWriter) workproc() {
	t := time.Now()
	y, m, d := t.Date()
	// rotate at 2:00
	t1 := time.Date(y, m, d+1, 2, 0, 0, 0, t.Location())
	<-time.After(t1.Sub(t))
	_, err := w.Rotate()
	if err != nil {
		log.Printf("logger file rotating error: %v", err)
		// PASS
	}

	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()
	for {
		// TODO: stop
		<-ticker.C
		_, err = w.Rotate()
		if err != nil {
			log.Printf("logger file rotating error: %v", err)
		}
	}
}

// Write satisfies the io.Writer interface.
func (w *RotateFileWriter) Write(output []byte) (int, error) {
	w.mu.Lock()
	defer w.mu.Unlock()
	if w.fp == nil {
		return 0, ErrLogFileNotOpen
	}
	return w.fp.Write(output)
}

// Rotate performs the actual act of rotating and reopening file.
func (w *RotateFileWriter) Rotate() (rotatedFilename string, err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	// Close existing file if open
	if w.fp != nil {
		err = w.fp.Close()
		w.fp = nil
		if err != nil {
			log.Printf("logger file close error: %v", err)
			// PASS
		}
	}
	// Rename dest file if it already exists
	_, err = os.Stat(w.filename)
	if err == nil {
		rotatedFilename = w.filename + "." + time.Now().AddDate(0, 0, -1).Format(w.TimeFormat)
		err = os.Rename(w.filename, rotatedFilename)
		if err != nil {
			return
		}
	}

	// Create a new file.
	err = w.openFile()
	return
}
