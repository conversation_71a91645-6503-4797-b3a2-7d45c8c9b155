package log

import (
	"os"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	m.Run()
	os.RemoveAll("logs")
}

func TestBackoffWait(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, BackoffWait(0))
	assert.GreaterOrEqual(float64(BackoffWait(1))/float64(time.Second), float64(1))
	assert.GreaterOrEqual(float64(1.1), float64(BackoffWait(1))/float64(time.Second))
	assert.GreaterOrEqual(float64(BackoffWait(3))/float64(time.Second), float64(4))
	assert.GreaterOrEqual(float64(4.4), float64(BackoffWait(3))/float64(time.Second))

	// max 120s
	assert.Equal(float64(120), float64(BackoffWait(10))/float64(time.Second))
}

func TestSetLogLevel(t *testing.T) {
	assert := assert.New(t)
	l := logrus.New()

	err := SetLogLevel(l, "debug")
	assert.Nil(err)

	err = SetLogLevel(l, "invalid")
	assert.Equal("not a valid logrus Level: \"invalid\"", err.Error())
}

func TestSetLogOut(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	l := logrus.New()

	err := SetLogOut(l, "stdout", "", nil)
	assert.NoError(err)

	err = SetLogOut(l, "stderr", "", nil)
	assert.NoError(err)

	err = SetLogOut(l, "", "", nil)
	assert.NoError(err)

	// format
	agent := AgentConfig{InstanceID: "test1", Category: "test2"}
	err = SetLogOut(l, "logs/access-${instance_id}-${category}.log", "", &agent)
	require.NoError(err)
	assert.FileExists("logs/access-test1-test2.log")
}

func TestInitDefaultLog(t *testing.T) {
	assert := assert.New(t)
	conf := *DefaultConfig

	// no errors on default config
	assert.NoError(InitLog(&conf))

	conf.AccessLevel = "invalid"

	assert.NotNil(InitLog(&conf))
}

func TestAccessLevel(t *testing.T) {
	assert := assert.New(t)
	conf := *DefaultConfig

	conf.AccessLevel = "invalid"

	assert.NotNil(InitLog(&conf))
}

func TestErrorLevel(t *testing.T) {
	assert := assert.New(t)
	conf := *DefaultConfig

	conf.ErrorLevel = "invalid"

	assert.NotNil(InitLog(&conf))
}

func TestAccessLogPath(t *testing.T) {
	assert := assert.New(t)
	conf := *DefaultConfig

	conf.AccessLog = "logs/access.log"

	assert.NoError(InitLog(&conf))
}

func TestErrorLogPath(t *testing.T) {
	assert := assert.New(t)
	conf := *DefaultConfig

	conf.ErrorLog = "logs/error.log"

	assert.NoError(InitLog(&conf))
}

func TestRotateFileWriter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rfw := RotateFileWriter{TimeFormat: "20060102", filename: "test.log"}
	_, err := rfw.Write([]byte("\n"))
	assert.Equal(ErrLogFileNotOpen, err)

	_, err = rfw.Open()
	require.NoError(err)
	defer func() {
		os.Remove("test.log")
	}()
	_, err = rfw.Write([]byte("line1\n"))
	require.NoError(err)
	rotatedFilename, err := rfw.Rotate()
	if rotatedFilename != "" {
		err = os.Remove(rotatedFilename)
		require.NoError(err)
	}
	require.NoError(err)
	_, err = rfw.Write([]byte("line2\n"))
	require.NoError(err)
}
