package recovery

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	router := gin.New()
	router.Use(Middleware())
	router.GET("/", func(c *gin.Context) {
		panic("PANIC")
	})
	// RUN
	w := performRequest(router, "GET", "/")

	// TEST
	assert.Equal(http.StatusInternalServerError, w.Code)
	res, _ := io.ReadAll(w.Result().Body)
	var m map[string]interface{}
	assert.NoError(json.Unmarshal(res, &m))
	assert.Equal("PANIC", m["info"])
}

func TestMiddlewareV2(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	router := gin.New()
	router.Use(MiddlewareV2())
	router.GET("/", func(c *gin.Context) {
		panic("PANIC")
	})
	// RUN
	w := performRequest(router, "GET", "/")

	// TEST
	assert.Equal(http.StatusInternalServerError, w.Code)
	res, _ := io.ReadAll(w.Result().Body)
	var m map[string]interface{}
	assert.NoError(json.Unmarshal(res, &m))
	assert.Equal("PANIC", m["message"])
}

func performRequest(r http.Handler, method, path string) *httptest.ResponseRecorder {
	req, _ := http.NewRequest(method, path, nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	return w
}

func TestLogStack(t *testing.T) {
	assert.NotPanics(t, func() {
		stack := "goroutine 5 [running]:\ngithub.com/gin-gonic/gin/render.JSON.Render(...)\n     /usr/local/go/src/runtime/debug/stack.go:24 +0xc0"
		req := []byte("GET /api/v2/chatroom/open/list?p=1&type=0 HTTP/1.1")
		logStack("123", stack, req)
		logStack("write: broken pipe", stack, req)
		logStack(errors.New("write: broken pipe"), stack, req)
		logStack(fmt.Errorf("write: broken pipe"), stack, req)
	})
}
