package recovery

import (
	"fmt"
	"net/http"
	"net/http/httputil"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Middleware returns a middleware that recovers from any panics and writes a 500 if there was one.
func Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				info := logAndReturnPanicMessage(c, err)
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"code": handler.CodeUnknownError,
					"info": info,
				})
			}
		}()
		c.Next()
	}
}

// MiddlewareV2 returns a middleware that recovers from any panics and writes a 500 if there was one.
func MiddlewareV2() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				message := logAndReturnPanicMessage(c, err)
				c.AbortWithStatusJSON(http.StatusInternalServerError, handler.BasicResponseV2{
					Code:    handler.CodeUnknownError,
					Message: message,
				})
			}
		}()
		c.Next()
	}
}

func logAndReturnPanicMessage(c *gin.Context, err any) string {
	// skip the first 9 lines, for example:
	//
	// goroutine 5 [running]:
	// runtime/debug.Stack(0x41e720, 0x1, 0x1, 0x19d0b0)
	//     /usr/local/go/src/runtime/debug/stack.go:24 +0xc0
	// main.f1.func1()
	//     /tmp/sandbox946729741/main.go:36 +0xa0
	// panic(0xf12a0, 0x19d0b0)
	//     /usr/local/go/src/runtime/panic.go:513 +0x240
	// main.f2()
	//     /tmp/sandbox946729741/main.go:53 +0x20
	stack := util.IgnoreUntil(string(debug.Stack()), 9, '\n')
	httprequest, _ := httputil.DumpRequest(c.Request, false)
	logStack(err, stack, httprequest)

	if handler.Mode() == handler.ReleaseMode {
		return "服务器内部错误"
	}
	return fmt.Sprint(err)
}

func logStack(err interface{}, stack string, req []byte) {
	msg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s\n%s",
		string(req), err, stack)
	if strings.Contains(fmt.Sprint(err), "write: broken pipe") &&
		strings.Contains(stack, "github.com/gin-gonic/gin/render") {
		logger.Warn(msg)
		return
	}
	logger.Error(msg)
}
