package rpc

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)

	conf := map[string]string{"accid": "testkey2"}
	router := gin.New()
	router.Use(MiddlewareAppKey("testkey", conf))
	testFunc := func(c *handler.Context) (handler.ActionResponse, error) {
		assert.PanicsWithValue("RPC middleware doesn't support GetUser function",
			func() { c.User() })

		var m struct {
			UserID int64  `json:"user_id"`
			Name   string `json:"name"`
		}
		data, _ := c.C.GetRawData()
		assert.NoError(json.Unmarshal(data, &m))
		assert.Equal(m.Name, "一二三")
		return nil, nil
	}
	a := handler.NewAction(handler.POST, testFunc, false)
	router.POST("/", a.GetHandler())
	// post with signature signed by the default key
	signedData := util.RPCSign(map[string]any{
		"user_id": 123,
		"name":    "一二三",
	}, "testkey")
	req, _ := http.NewRequest("POST", "/", bytes.NewReader(signedData))
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	// post with signature signed by the key of "accid"
	signedData = util.RPCSign(map[string]any{
		"user_id": 123,
		"name":    "一二三",
	}, conf["accid"])
	req, _ = http.NewRequest("POST", "/", bytes.NewReader(signedData))
	req.Header.Set("X-AppKey", "accid")
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	// post without signature
	req, _ = http.NewRequest("POST", "/", strings.NewReader(`{"user_id":123}`))
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(http.StatusForbidden, w.Code)
}

func TestGetUser(t *testing.T) {
	assert := assert.New(t)

	// 不存在 user_id 及 token
	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	ctx.Request = httptest.NewRequest("POST", "/", strings.NewReader(`{"foo":"bar"}`))
	assert.PanicsWithValue("RPC middleware doesn't support GetUser function",
		func() { _, _ = GetUserPanic(ctx) })
}

func TestRPCMiddleware(t *testing.T) {
	// 整体测试一下中间件
	assert := assert.New(t)
	require := require.New(t)

	type user struct {
		UserID int64 `json:"user_id"`
	}
	action1 := func(c *handler.Context) (handler.ActionResponse, error) {
		var u user
		assert.NoError(c.BindJSON(&u))
		return true, nil
	}
	action2 := func(c *handler.Context) (handler.ActionResponse, error) {
		t.Helper()
		var u user
		assert.NoError(c.BindJSON(&u))
		return true, nil
	}
	h := handler.Handler{
		Middlewares: gin.HandlersChain{Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"bind/user": handler.NewAction(handler.POST, action1, false),
			"user/bind": handler.NewAction(handler.POST, action2, false),
		},
	}
	r := gin.Default()
	tutil.RunMockServer(r, 9032, &h)
	conf := mrpc.Config{
		"test": mrpc.ConfigEntry{
			URL: "http://127.0.0.1:9032/",
			Key: "testkey",
		},
	}
	client, err := mrpc.NewClient(conf)
	require.NoError(err)
	assert.NoError(client.Do(mrpc.UserContext{}, "test://user/bind", nil, nil))
	assert.NoError(client.Do(mrpc.UserContext{}, "test://bind/user", nil, nil))

	t.Run("action v2", func(t *testing.T) {
		hV2 := handler.HandlerV2{
			Middlewares: gin.HandlersChain{Middleware("testkey")},
			Actions: map[string]*handler.ActionV2{
				"bind/user": handler.NewActionV2(handler.POST, action1, handler.ActionOption{
					LoginRequired: false,
				}),
			},
		}
		r = gin.Default()
		tutil.RunMockServer(r, 9033, &hV2)
		conf = mrpc.Config{
			"test": mrpc.ConfigEntry{
				URL: "http://127.0.0.1:9033/",
				Key: "errorKey",
			},
		}
		client, err = mrpc.NewClient(conf)
		require.NoError(err)
		err = client.Do(mrpc.UserContext{}, "test://bind/user", nil, nil)
		var e *mrpc.ClientError
		ok := errors.As(err, &e)
		require.True(ok)
		assert.Equal("signature error", e.Message)
	})
}
