package rpc

import (
	"bytes"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/util"
)

const (
	// CodeErrSignature rpc signature error code
	// NOTICE: 和 handler.CodeErrSignature 一致
	CodeErrSignature = 100010002
)

// Middleware returns the HMAC-checking middleware
func Middleware(rpcKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		middleware(c, rpcKey, "")
	}
}

// MiddlewareAppKey returns the HMAC-checking middleware using the given config
func MiddlewareAppKey(defaultKey string, conf map[string]string) gin.HandlerFunc {
	return func(c *gin.Context) {
		appKey := c.<PERSON>er("X-AppKey")
		rpcKey := conf[appKey]
		if rpcKey == "" {
			rpcKey = defaultKey
		}
		middleware(c, rpcKey, appKey)
	}
}

func middleware(c *gin.Context, rpcKey string, appKey string) {
	data, _ := c.GetRawData()
	c.Request.Body.Close()

	body, err := util.CheckRPCSign(data, rpcKey)
	if err != nil {
		logger.WithFields(logger.Fields{
			"url":     c.Request.URL.String(),
			"data":    string(data),
			"app_key": appKey,
		}).Debugf("check signature failed: %v", err)
		if handler.IsActionV2(c) {
			c.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
				"code":    CodeErrSignature,
				"message": err.Error(),
				"data":    nil,
			})
		} else {
			c.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
				"code": CodeErrSignature,
				"info": err.Error(),
			})
		}
		return
	}
	c.Request.Body = io.NopCloser(bytes.NewReader(body))
	c.Request.ContentLength = int64(len(body))
	c.Request.Header.Set("Content-Type", "application/json")
	c.Set("user", user.GetUserFunc(GetUserPanic))
	c.Next()
}

// GetUserPanic must panic
func GetUserPanic(c *gin.Context) (*user.User, error) {
	panic("RPC middleware doesn't support GetUser function")
}
