package openapi

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
)

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)

	conf := map[string]string{
		"testkey": "testsecret",
	}
	router := gin.New()
	router.Use(Middleware(conf))
	testFunc := func(ctx *handler.Context) (handler.ActionResponse, error) {
		var params struct {
			Foo    string `form:"foo"`
			AppKey string `form:"appkey"`
			Sign   string `form:"sign"`
		}
		err := ctx.Bind(&params)
		assert.NoError(err)
		return handler.M{
			"foo": params.Foo,
		}, nil
	}
	a := handler.NewActionV2(handler.POS<PERSON>, testFunc)
	router.POST("/", a.<PERSON>())

	var params = url.Values{}
	params.Set("foo", "bar")
	params.Set("appkey", "testkey")

	signer := openAPISigner{
		AppKey:    "testkey",
		AppSecret: "testsecret",
	}
	queryStr := blademaster.Sign(signer, params)
	queryParams, err := url.ParseQuery(queryStr)
	assert.NoError(err)
	params.Set("ts", queryParams.Get("ts"))
	params.Set("sign", queryParams.Get("sign"))

	// 正常 POST 请求
	req, _ := http.NewRequest("POST", "/", nil)
	req.PostForm = params
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(http.StatusOK, w.Code)

	var res handler.BasicResponseV2
	assert.NoError(json.Unmarshal(w.Body.Bytes(), &res))
	assert.Equal(0, res.Code)
	data, ok := res.Data.(map[string]interface{})
	assert.True(ok)
	assert.Equal("bar", data["foo"])

	// 签名错误
	req, _ = http.NewRequest("POST", "/", nil)
	params.Set("sign", "abcdefg")
	req.PostForm = params
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(http.StatusForbidden, w.Code)

	var res2 handler.BasicResponseV2
	assert.NoError(json.Unmarshal(w.Body.Bytes(), &res2))
	assert.Equal(handler.CodeErrSignature, res2.Code)
	assert.Equal("sign is not valid", res2.Message)

	// 正常 GET 请求
	router.GET("/", a.GetHandler())
	req, _ = http.NewRequest(http.MethodGet, fmt.Sprintf("/?%s", queryStr), nil)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(http.StatusOK, w.Code)

	var res3 handler.BasicResponseV2
	assert.NoError(json.Unmarshal(w.Body.Bytes(), &res3))
	assert.Equal(0, res3.Code)
	data, ok = res3.Data.(map[string]interface{})
	assert.True(ok)
	assert.Equal("bar", data["foo"])

	// GET 请求签名错误
	req, _ = http.NewRequest(http.MethodGet, "/?appkey=testkey&ts="+queryParams.Get("ts")+"&sign=invalidsign", nil)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(http.StatusForbidden, w.Code)

	var res4 handler.BasicResponseV2
	assert.NoError(json.Unmarshal(w.Body.Bytes(), &res4))
	assert.Equal(handler.CodeErrSignature, res4.Code)
}
