package openapi

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
)

type openAPISigner struct {
	AppKey    string
	AppSecret string
}

// SignParams 获取 sign 参数
func (o openAPISigner) SignParams() (appKey, appSecret string) {
	return o.AppKey, o.AppSecret
}

// Middleware returns the open sign middleware
// NOTICE: 只支持 v2 的响应
func Middleware(appKeySecretMap map[string]string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		middleware(ctx, appKeySecretMap)
	}
}

// 参考：https://git.bilibili.co/platform/go-common/-/blob/master/library/net/http/blademaster/middleware/verify/verify.go
func middleware(ctx *gin.Context, appKeySecretMap map[string]string) {
	_ = ctx.Request.ParseMultipartForm(10 << 10)
	req := ctx.Request
	params := req.Form
	if req.Method == http.MethodPost {
		q := req.URL.Query()
		if q.Get("sign") != "" {
			params = q
		}
	}
	if params.Get("ts") == "" {
		ctx.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
			"code":    handler.CodeErrSignature,
			"message": "param ts is required",
			"data":    nil,
		})
		return
	}

	appKey := params.Get("appkey")
	appSecret, ok := appKeySecretMap[appKey]
	if appKey == "" || !ok || appSecret == "" {
		ctx.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
			"code":    handler.CodeErrSignature,
			"message": "cannot find target appsecret",
			"data":    nil,
		})
		return
	}

	signer := openAPISigner{
		AppKey:    appKey,
		AppSecret: appSecret,
	}
	if !blademaster.IsValidSign(signer, params) {
		ctx.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
			"code":    handler.CodeErrSignature,
			"message": "sign is not valid",
			"data":    nil,
		})
		return
	}

	ctx.Next()
}
