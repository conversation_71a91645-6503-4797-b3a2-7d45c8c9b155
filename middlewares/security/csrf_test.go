package security

import (
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestHostMatch(t *testing.T) {
	assert := assert.New(t)

	assert.False(HostMatch("\n\n\n", []string{"\n\n\n"}), "测试 url 不正确")
	assert.False(HostMatch("a/b/c/d", []string{"", "a"}), "测试没有 host")
	assert.True(HostMatch("http://localhost:1234/b/c/d", []string{"", "localhost:1234"}), "测试完整匹配")
	domains := []string{"", "a"}
	assert.True(HostMatch("http://www.a/b/c/d", domains), "测试匹配后缀")
	assert.Equal([]string{"", "a"}, domains)
	assert.False(HostMatch("http://a/b/c/d", []string{"", "b"}), "测试未通过的情况")
}

func TestCSRFMiddleware(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := CSRFMiddleware("test.com")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	m(c)
	assert.False(c.IsAborted())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/test", nil)
	m(c)
	require.True(c.IsAborted())
	assert.Equal(400, w.Code)
	assert.JSONEq(`{"code":100010007,"info":"非法请求"}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "http://www.test.com", nil)
	c.Request.Header.Set("Referer", "http://www.test.com/123")
	m(c)
	require.False(c.IsAborted())
}

func TestCSRFWithAllowURIsMiddleware(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/api/v2/test", nil)
	CSRFWithAllowURIsMiddleware([]string{"test.com"}, nil)(c)
	require.True(c.IsAborted())
	assert.Equal(400, w.Code)
	assert.JSONEq(`{"code":100010007,"info":"非法请求"}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/api/v2/test", nil)
	CSRFWithAllowURIsMiddleware([]string{"test.com"}, []string{"/api/v2/test"})(c)
	require.False(c.IsAborted())
}

func TestCheckCSRF(t *testing.T) {
	assert := assert.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest(http.MethodGet, "/test", nil)
	assert.True(checkCSRF(c, []string{"test.com"}))

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest(http.MethodPost, "/test", nil)
	assert.False(checkCSRF(c, []string{"test.com"}))
}

func TestCSRFMiddlewareV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := CSRFMiddlewareV2("test.com")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	m(c)
	assert.False(c.IsAborted())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/test", nil)
	m(c)
	require.True(c.IsAborted())
	assert.Equal(400, w.Code)
	assert.JSONEq(`{"code":100010007,"message":"非法请求","data":null}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "http://www.test.com", nil)
	c.Request.Header.Set("Referer", "http://www.test.com/123")
	m(c)
	require.False(c.IsAborted())
}

func TestCSRFWithIgnoreURIsMiddlewareV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/api/v2/test", nil)
	CSRFWithIgnoreURIsMiddlewareV2([]string{"test.com"}, nil)(c)
	require.True(c.IsAborted())
	assert.Equal(400, w.Code)
	assert.JSONEq(`{"code":100010007,"message":"非法请求","data":null}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/api/v2/test", nil)
	CSRFWithIgnoreURIsMiddlewareV2([]string{"test.com"}, []string{"/api/v2/test"})(c)
	require.False(c.IsAborted())
}
