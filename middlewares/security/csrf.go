package security

import (
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/util"
)

// CSRFMiddleware CSRF 中间件
func CSRFMiddleware(domains ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass := checkCSRF(c, domains)
		if !pass {
			c.AbortWithStatusJSON(http.StatusBadRequest, map[string]interface{}{
				"code": 100010007,
				"info": "非法请求"})
			return
		}
		c.Next()
	}
}

// CSRFWithAllowURIsMiddleware CSRF 中间件，若请求的 URI 在 ignoreURIs 白名单中，则不进行 CSRF 检查
func CSRFWithAllowURIsMiddleware(domains, ignoreURIs []string) gin.HandlerFunc {
	m := CSRFMiddleware(domains...)
	return func(c *gin.Context) {
		if util.HasElem(ignoreURIs, c.Request.URL.Path) {
			c.Next()
			return
		}
		m(c)
	}
}

// HostMatch u 的 host 是否符合，完全匹配入参的 domains, 后缀匹配 "."+domains
func HostMatch(u string, domains []string) bool {
	url, err := url.Parse(u)
	if err != nil || url.Host == "" {
		// 不记录 Parse 的错误日志
		return false
	}
	// 完全匹配
	for i := range domains {
		if domains[i] != "" && url.Host == domains[i] {
			return true
		}
	}
	// 后缀匹配
	for i := range domains {
		if domains[i] != "" && strings.HasSuffix(url.Host, "."+domains[i]) {
			return true
		}
	}
	return false
}

func checkCSRF(c *gin.Context, domains []string) bool {
	if c.Request.Method == http.MethodGet {
		return true
	}
	e := util.NewEquipment(c.Request.UserAgent())
	if !e.FromApp {
		u := c.Request.Header.Get("Origin")
		if u == "" {
			u = c.Request.Referer()
		}
		if u == "" || !HostMatch(u, domains) {
			return false
		}
	}
	return true
}

// CSRFMiddlewareV2 CSRF 中间件 v2
func CSRFMiddlewareV2(domains ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass := checkCSRF(c, domains)
		if !pass {
			c.AbortWithStatusJSON(http.StatusBadRequest, map[string]interface{}{
				"code":    100010007,
				"message": "非法请求",
				"data":    nil})
			return
		}
		c.Next()
	}
}

// CSRFWithIgnoreURIsMiddlewareV2 CSRF 中间件，若请求的 URI 在 ignoreURIs 白名单中，则不进行 CSRF 检查
func CSRFWithIgnoreURIsMiddlewareV2(domains, ignoreURIs []string) gin.HandlerFunc {
	m := CSRFMiddlewareV2(domains...)
	return func(c *gin.Context) {
		if util.HasElem(ignoreURIs, c.Request.URL.Path) {
			c.Next()
			return
		}
		m(c)
	}
}
