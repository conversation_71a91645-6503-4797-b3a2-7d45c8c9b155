// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user.proto
// !! Manually Changed: origin   ->   now
//                      IUser.Id      IUser.ID
//                      IUser.Iconurl IUser.IconURL
//                      GetId         GetID
//                      GetIconurl    GetIconURL

package user

import (
	fmt "fmt"
	math "math"

	proto "github.com/golang/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type IUser struct {
	ID                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	IconURL              string   `protobuf:"bytes,3,opt,name=iconurl,proto3" json:"iconurl,omitempty"`
	Email                string   `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Mobile               string   `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Region               int32    `protobuf:"varint,6,opt,name=region,proto3" json:"region,omitempty"`
	Confirm              int32    `protobuf:"varint,7,opt,name=confirm,proto3" json:"confirm,omitempty"`
	ExpireAt             int64    `protobuf:"varint,8,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	LoginAt              int64    `protobuf:"varint,9,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IUser) Reset()         { *m = IUser{} }
func (m *IUser) String() string { return proto.CompactTextString(m) }
func (*IUser) ProtoMessage()    {}
func (*IUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}
func (m *IUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IUser.Unmarshal(m, b)
}
func (m *IUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IUser.Marshal(b, m, deterministic)
}
func (dst *IUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IUser.Merge(dst, src)
}
func (m *IUser) XXX_Size() int {
	return xxx_messageInfo_IUser.Size(m)
}
func (m *IUser) XXX_DiscardUnknown() {
	xxx_messageInfo_IUser.DiscardUnknown(m)
}

var xxx_messageInfo_IUser proto.InternalMessageInfo

func (m *IUser) GetID() int64 {
	if m != nil {
		return m.ID
	}
	return 0
}

func (m *IUser) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *IUser) GetIconURL() string {
	if m != nil {
		return m.IconURL
	}
	return ""
}

func (m *IUser) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *IUser) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *IUser) GetRegion() int32 {
	if m != nil {
		return m.Region
	}
	return 0
}

func (m *IUser) GetConfirm() int32 {
	if m != nil {
		return m.Confirm
	}
	return 0
}

func (m *IUser) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

func (m *IUser) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

func init() {
	proto.RegisterType((*IUser)(nil), "user.IUser")
}

func init() { proto.RegisterFile("user.proto", fileDescriptor_116e343673f7ffaf) }

var fileDescriptor_116e343673f7ffaf = []byte{
	// 200 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x3c, 0x8f, 0xbd, 0x6a, 0xc3, 0x30,
	0x10, 0x80, 0x91, 0x13, 0xd9, 0xf2, 0x0d, 0x1d, 0x8e, 0x52, 0xae, 0xed, 0x62, 0x3a, 0x79, 0xea,
	0xd2, 0x27, 0xc8, 0xd8, 0xd5, 0xd0, 0xb9, 0x38, 0xc9, 0x35, 0x1c, 0xe8, 0x27, 0x28, 0x0a, 0xf4,
	0x85, 0xfb, 0x1e, 0x45, 0x17, 0x3b, 0x9b, 0xbe, 0xef, 0x13, 0xc7, 0x1d, 0xc0, 0xf5, 0xc2, 0xf9,
	0xfd, 0x9c, 0x53, 0x49, 0xb8, 0xad, 0xef, 0xb7, 0x3f, 0x03, 0xf6, 0xf3, 0xeb, 0xc2, 0x19, 0x1f,
	0xa0, 0x91, 0x23, 0x99, 0xc1, 0x8c, 0x9b, 0xa9, 0x91, 0x23, 0xbe, 0x80, 0xab, 0x3f, 0xe2, 0x1c,
	0x98, 0x9a, 0xc1, 0x8c, 0xfd, 0x74, 0x67, 0x24, 0xe8, 0xe4, 0x90, 0xe2, 0x35, 0x7b, 0xda, 0x68,
	0x5a, 0x11, 0x1f, 0xc1, 0x72, 0x98, 0xc5, 0xd3, 0x56, 0xfd, 0x0d, 0xf0, 0x09, 0xda, 0x90, 0xf6,
	0xe2, 0x99, 0xac, 0xea, 0x85, 0xaa, 0xcf, 0x7c, 0x92, 0x14, 0xa9, 0x1d, 0xcc, 0x68, 0xa7, 0x85,
	0xea, 0xfc, 0x43, 0x8a, 0x3f, 0x92, 0x03, 0x75, 0x1a, 0x56, 0xc4, 0x57, 0xe8, 0xf9, 0xf7, 0x2c,
	0x99, 0xbf, 0xe7, 0x42, 0x4e, 0x97, 0x75, 0x37, 0xb1, 0x2b, 0xf8, 0x0c, 0xce, 0xa7, 0x93, 0xc4,
	0xda, 0x7a, 0x6d, 0x9d, 0xf2, 0xae, 0xec, 0x5b, 0x3d, 0xfa, 0xe3, 0x3f, 0x00, 0x00, 0xff, 0xff,
	0x67, 0x36, 0x6d, 0xd8, 0x02, 0x01, 0x00, 0x00,
}
