package user

import (
	"encoding/base64"
	"testing"

	proto "github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/require"
)

func TestProtobufData(t *testing.T) {
	require := require.New(t)

	b64data := "CAoSBWJsZXNzGldodHRwOi8vc3RhdGljLm1pc3NldmFuLmNvbS9wcm9maWxlLzIwMTcwNC8wNy85YjM1MjlhMDgxMzBlNzRkYTFkY2Q4YjUzZmViNTBjNTE1NTAwNy5wbmciFnRlbmdhdHRhY2tAZm94bWFpbC5jb20qCzE4NjgyMDYwMjMyMFY4AkCb6cD3BUibrJ32BQ=="
	data, err := base64.StdEncoding.DecodeString(b64data)
	require.NoError(err)

	t.Logf("%v\n", data)

	var u IUser
	err = proto.Unmarshal(data, &u)
	require.NoError(err)
	t.Logf("%v\n", u)

	data2, err := proto.Marshal(&u)
	b64data2 := base64.StdEncoding.EncodeToString(data2)
	t.Logf("data2 = %v %v\n", data2, err)
	require.Equal(b64data, b64data2)
}
