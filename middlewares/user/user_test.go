package user

import (
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

var testIP = "127.0.0.1"

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	m := Middleware()
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	m(ctx)
	assert.Equal(http.StatusOK, w.Result().StatusCode)
	u2, ok := ctx.Get("user")
	require.True(ok)
	assert.NotPanics(func() { _ = u2.(GetUserFunc) })
}

func TestGetUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u, err := service.SSO.LoginByThirdOpenID(sso.ThirdTypeWeibo, "testweibouid",
		sso.AgeForTwoHours, testIP, "", "")
	require.NoError(err)
	defer func() {
		assert.NoError(service.SSO.Logout(u.Token))
	}()
	require.NotEmpty(u.Token)
	require.NotEmpty(u.User)

	newC := func(token string) *gin.Context {
		c, _ := gin.CreateTestContext(httptest.NewRecorder())
		c.Request, _ = http.NewRequest("GET", "/", nil)
		c.Request.AddCookie(&http.Cookie{Name: "token", Value: token})
		return c
	}

	// 没有 cookie
	user, err := GetUser(newC(""))
	assert.True(err == nil && user == nil, err)
	// 错误的 token
	user, err = GetUser(newC("123"))
	assert.True(err == nil && user == nil, err)
	// 正确，填充 memcache
	user, err = GetUser(newC(u.Token))
	require.True(err == nil && user != nil, err)
	assert.Equal(u.User.UserID, user.ID)
	assert.Equal(u.ExpireAt, user.ExpireAt)
	assert.Equal(u.LoginAt, user.LoginAt)
	_, err = service.LRURedis.Get(keys.KeyUserTokenGo1.Format(u.Token)).Result()
	assert.NoError(err)
	// 正确，从 memcache 获取
	user, err = GetUser(newC(u.Token))
	require.True(err == nil && user != nil, err)
	assert.Equal(u.User.UserID, user.ID)
	assert.Equal(u.ExpireAt, user.ExpireAt)
	assert.Equal(u.LoginAt, user.LoginAt)
	_, err = service.LRURedis.Get(keys.KeyUserTokenGo1.Format(u.Token)).Result()
	assert.NoError(err)
}

func TestIsRole(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testID    int64 = 10
		testIDstr       = "10"
	)

	assignment := role.AuthAssignment{
		UserID:   testIDstr,
		ItemName: string(role.LiveAdmin),
	}
	err := service.DB.FirstOrCreate(&assignment, assignment).Error
	require.NoError(err)

	// u is nil
	var u *User
	b, err := u.IsRole(role.LiveAdmin)
	assert.NoError(err)
	assert.False(b)

	// u is not nil
	u = new(User)
	u.ID = testID
	assert.True(u.IsRole(role.LiveAdmin))

	err = role.DeleteAuthAssignment(testID, role.LiveAdmin)
	require.NoError(err)

	assert.False(u.IsRole(role.LiveAdmin))
}

func TestCheckRole(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var u User
	u.ID = 12
	token := "test-token"
	tokenKey := keys.KeyUserTokenGo1.Format(token)
	userSessionData, err := proto.Marshal(&u.IUser)
	require.NoError(err)
	err = service.LRURedis.Set(tokenKey, string(userSessionData), sessionTTL).Err()
	require.NoError(err)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	pass, err := checkRole(c, []role.Role{role.LiveAdmin})
	require.NoError(err)
	assert.False(pass)

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: token})
	pass, err = checkRole(c, []role.Role{role.LiveAdmin})
	require.NoError(err)
	assert.True(pass)
}

func TestIsRoleMiddleware(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var u User
	u.ID = 12
	token := "test-token"
	tokenKey := keys.KeyUserTokenGo1.Format(token)
	userSessionData, err := proto.Marshal(&u.IUser)
	require.NoError(err)
	err = service.LRURedis.Set(tokenKey, string(userSessionData), sessionTTL).Err()
	require.NoError(err)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	h := IsRole(role.LiveAdmin)
	h(c)
	assert.Equal(http.StatusForbidden, w.Code)
	assert.JSONEq(`{"code":200020003,"info":"用户没有权限"}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: token})
	h = IsRole(role.LiveAdmin)
	h(c)
	assert.Equal(200, w.Code)
}

func TestIsRoleV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var u User
	u.ID = 12
	token := "test-token"
	tokenKey := keys.KeyUserTokenGo1.Format(token)
	userSessionData, err := proto.Marshal(&u.IUser)
	require.NoError(err)
	err = service.LRURedis.Set(tokenKey, string(userSessionData), sessionTTL).Err()
	require.NoError(err)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	h := IsRoleV2(role.LiveAdmin)
	h(c)
	assert.Equal(http.StatusForbidden, w.Code)
	assert.JSONEq(`{"code":200020003,"message":"用户没有权限","data":null}`, w.Body.String())

	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request, _ = http.NewRequest("GET", "/", nil)
	c.Request.AddCookie(&http.Cookie{Name: "token", Value: token})
	h = IsRoleV2(role.LiveAdmin)
	h(c)
	assert.Equal(200, w.Code)
}

func TestInBlacklist(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := &User{
		IUser: IUser{
			ID: 233322,
		},
	}
	key := serviceredis.KeyBlackList0.Format()
	_, err := service.Redis.ZAdd(key,
		&redis.Z{Member: strconv.FormatInt(u.ID, 10), Score: user.BanTimeForever}).Result()
	require.NoError(err)
	inBlacklist, err := u.InBlacklist(user.BlackTypeUserComment)
	require.NoError(err)
	assert.True(inBlacklist)

	key = serviceredis.KeyBlackList0.Format()
	_, err = service.Redis.ZRem(key, strconv.FormatInt(u.ID, 10)).Result()
	require.NoError(err)
	inBlacklist, err = u.InBlacklist(user.BlackTypeUserComment)
	require.NoError(err)
	assert.False(inBlacklist)
}
