package user

import (
	"encoding/base64"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/proto" // nolint:staticcheck
	"gopkg.in/guregu/null.v3"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// constants
const (
	sessionTTL = 5 * time.Minute
)

//go:generate protoc -I . user.proto --go_out=plugins=grpc:.

// User is a wrapper for IUser
type User struct {
	IUser
	isBindMobile null.Bool
	isExam       null.Bool
}

// GetUserFunc type, user should be nil if err != nil
type GetUserFunc func(c *gin.Context) (user *User, err error)

// IsRole 用户 u 是否是角色 r
func (u *User) IsRole(r ...role.Role) (bool, error) {
	if u == nil {
		return false, nil
	}
	return role.IsRole(u.ID, r...)
}

// IsExam 是否是答题用户
func (u *User) IsExam() (bool, error) {
	if u.isExam.Valid {
		return u.isExam.Bool, nil
	}
	if u.ID == 0 {
		return false, nil
	}
	count := 0
	// confirm 已答题用户进行位运算的标识为 1
	// "WHERE id = ? AND (confirm & 1)" 的形式 MySQL driver 会返回 parse SQL error
	err := service.DB.Model(&user.MowangskUser{}).Where("id = ? AND (confirm & 1 <> 0)", u.ID).Count(&count).Error
	if err != nil {
		return false, err
	}
	result := false
	if count > 0 {
		result = true
	}
	u.isExam.SetValid(result)
	return result, nil
}

// IsBindMobile checks if the user has added mobile number to his/her account
func (u *User) IsBindMobile() (bool, error) {
	if u.isBindMobile.Valid {
		return u.isBindMobile.Bool, nil
	}
	if u.ID == 0 {
		u.isBindMobile.SetValid(false)
		return false, nil
	}
	acc, err := service.SSO.UserInfo(u.ID)
	if err != nil {
		return false, err
	}
	var isBind bool
	if acc.Mobile != "" {
		isBind = true
	}
	u.isBindMobile.SetValid(isBind)
	return isBind, nil
}

func getUserFromSSOResponse(resp *sso.ClientResponse) User {
	return User{
		IUser: IUser{
			ID:       resp.User.UserID,
			Username: resp.User.Username,
			IconURL:  resp.User.IconURL,
			Email:    resp.User.Email,
			Mobile:   resp.User.Mobile,
			Region:   int32(resp.User.Region),
			Confirm:  int32(resp.User.Confirm),
			ExpireAt: resp.ExpireAt,
			LoginAt:  resp.LoginAt,
		},
	}
}

// InBlacklist checks if User.UserID is in black list
func (u *User) InBlacklist(blackType int) (bool, error) {
	return user.InBlacklist(u.ID, blackType)
}

// Middleware provide gin router middleware for user state.
func Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer c.Next()
		c.Set("user", GetUserFunc(GetUser))
	}
}

// GetUser gets User by token
func GetUser(c *gin.Context) (*User, error) {
	token, _ := c.Cookie("token")
	return FromToken(token, c.ClientIP())
}

// FromToken 从 token 获取 user
func FromToken(token string, ip string) (*User, error) {
	if token == "" {
		return nil, nil
	}
	tokenKey := keys.KeyUserTokenGo1.Format(token)
	val, err := service.LRURedis.Get(tokenKey).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}

	var u User
	if val != "" {
		err = proto.Unmarshal([]byte(val), &u.IUser)
		if err != nil {
			val = ""
		}
	}
	if u.ID == 0 && val != "" {
		logger.WithField("buf", base64.StdEncoding.EncodeToString([]byte(val))).
			Warn("has user buf but no user id")
	}
	if val == "" {
		// (re)generate session
		resp, err := service.SSO.Session(token, ip)
		if err != nil {
			if e, ok := err.(*sso.ClientError); ok &&
				(e.Code == 5 || e.Code == 300030002) {
				// WARKAROUND: 使用 missevan-sso 项目定义的常量可能导致循环引用
				// 5 -- token 过期或不存在
				// 300030002 新的 Token 过期或不存在
				return nil, nil
			}
			return nil, err
		}
		u = getUserFromSSOResponse(resp)
		userSessionData, err := proto.Marshal(&u.IUser)
		if err != nil {
			logger.Errorf("proto marshal failed: %v", err)
			// PASS
		} else {
			err = service.LRURedis.Set(tokenKey, string(userSessionData), sessionTTL).Err()
			if err != nil {
				logger.Errorf("user session save failed: %v", err)
				// PASS
			}
		}
	}
	// TODO: check expireAt
	return &u, nil
}

// SetIsBindMobileForTest 设置用户是否绑定手机的状态，用于测试使用
func (u *User) SetIsBindMobileForTest(isBind bool) {
	u.isBindMobile.SetValid(isBind)
}

func checkRole(c *gin.Context, roleNames []role.Role) (bool, error) {
	u, err := GetUser(c)
	if err != nil {
		return false, err
	}
	if u == nil {
		return false, nil
	}

	pass, err := role.IsRole(u.ID, roleNames...)
	if err != nil {
		return false, err
	}
	return pass, nil
}

// IsRole 是否具有某角色之一的中间件
func IsRole(roleNames ...role.Role) func(c *gin.Context) {
	if len(roleNames) == 0 {
		panic("roleNames is empty")
	}
	return func(c *gin.Context) {
		pass, err := checkRole(c, roleNames)
		if err != nil {
			logger.Error(err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, map[string]interface{}{
				"code": 100010500,
				"info": "服务器内部错误",
			})
			return
		}

		if !pass {
			c.AbortWithStatusJSON(http.StatusForbidden, map[string]interface{}{
				"code": 200020003,
				"info": "用户没有权限",
			})
			return
		}

		c.Next()
	}
}

type errorResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// IsRoleV2 是否具有某角色之一的中间件
func IsRoleV2(roleNames ...role.Role) func(c *gin.Context) {
	if len(roleNames) == 0 {
		panic("roleNames is empty")
	}
	return func(c *gin.Context) {
		pass, err := checkRole(c, roleNames)
		if err != nil {
			logger.Error(err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, errorResponse{
				Code:    100010500,
				Message: "服务器内部错误",
			})
			return
		}

		if !pass {
			c.AbortWithStatusJSON(http.StatusForbidden, errorResponse{
				Code:    200020003,
				Message: "用户没有权限",
			})
			return
		}

		c.Next()
	}
}
