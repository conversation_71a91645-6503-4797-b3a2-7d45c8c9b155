package apisign

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

type item struct {
	Key   string
	Value string
}

func isErrSign(err error) bool {
	_, ok := err.(*ErrSign)
	return ok
}

// Middleware 验证请求的签名是否正确
// 签名规则参考文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/backend/application/api_sign.md
func Middleware(key []byte) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass, err := VerifySign(c, key)
		if err != nil && !isErrSign(err) {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code": v.Code,
					"info": v.Message,
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code": handler.CodeUnknownError,
				"info": err.Error(),
			})
			return
		}
		if !pass {
			if handler.Mode() == handler.ReleaseMode {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"code": handler.CodeErrSignature,
					"info": "非法请求",
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": handler.CodeErrSignature,
				"info": err,
			})
			return
		}
		c.Next()
	}
}

// MiddlewareV2 验证请求的签名是否正确
// 签名规则参考文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/backend/application/api_sign.md
func MiddlewareV2(key []byte) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass, err := VerifySign(c, key)
		if err != nil && !isErrSign(err) {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, handler.BasicResponseV2{
					Code:    v.Code,
					Message: v.Message,
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusInternalServerError, handler.BasicResponseV2{
				Code:    handler.CodeUnknownError,
				Message: err.Error(),
			})
			return
		}
		if !pass {
			if handler.Mode() == handler.ReleaseMode {
				c.AbortWithStatusJSON(http.StatusUnauthorized, handler.BasicResponseV2{
					Code:    handler.CodeErrSignature,
					Message: "非法请求",
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, handler.BasicResponseV2{
				Code:    handler.CodeErrSignature,
				Message: err.Error(),
			})
			return
		}
		c.Next()
	}
}

// MiddlewarePerRequestKey 验证请求的签名是否正确，不同的请求可能使用不同的 key
func MiddlewarePerRequestKey(key []byte, f func(*http.Request) (string, error)) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			pass bool
			err  error
			k    string
		)
		k, err = f(c.Request)
		if err == nil {
			var keyToUse []byte
			if k != "" {
				keyToUse = []byte(k)
			} else {
				keyToUse = key
			}
			pass, err = VerifySign(c, keyToUse)
		}
		if err != nil && !isErrSign(err) {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code": v.Code,
					"info": v.Message,
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"code": handler.CodeUnknownError,
				"info": err.Error(),
			})
			return
		}
		if !pass {
			if handler.Mode() == handler.ReleaseMode {
				err = nil
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"code": handler.CodeErrSignature,
				"info": err,
			})
			return
		}
		c.Next()
	}
}

type debugSign struct {
	StrToSign string
	Sign      string
}

// ErrSign implements the error interface
type ErrSign struct {
	debugSign
	Message string
}

func (e *ErrSign) Error() string {
	if e == nil {
		return ""
	}
	if e.Message != "" {
		return e.Message
	}
	return fmt.Sprintf("strToSign:%s,\nsign:%s", e.StrToSign, e.Sign)
}

// VerifySign 验证签名
func VerifySign(c *gin.Context, key []byte) (bool, error) {
	authorization := c.GetHeader("authorization")

	strToSign, sign, err := BuildSign(c, key)
	if err != nil {
		return false, err
	}

	// 请求头 authorization 格式为“MissEvan <sign>”，即 sign 为该字符串第 9 位之后的内容
	if len(authorization) <= 9 || !hmac.Equal([]byte(authorization[9:]), sign) {
		return false, &ErrSign{
			debugSign: debugSign{
				StrToSign: strToSign,
				Sign:      string(sign),
			},
		}
	}
	return true, nil
}

// BuildSign 生成签名
func BuildSign(c *gin.Context, key []byte) (string, []byte, error) {
	// 获得待签名字符串
	verb := c.Request.Method
	proto := c.Request.Header.Get("X-Forwarded-Proto")
	if proto == "" {
		proto = "http"
	}
	/*
		{VERB}\n
		{CanonicalURI}\n
		{CanonicalQueryString}\n
		{CanonicalHeaders}\n
	*/
	var strToSign bytes.Buffer
	strToSign.WriteString(verb)
	strToSign.WriteString("\n")
	// CanonicalURI
	strToSign.WriteString(URIEncode(proto+"://"+c.Request.Host+c.Request.URL.Path, false))
	strToSign.WriteString("\n")
	// CanonicalQueryString
	strToSign.Write(encodeValues(c.Request.URL.Query()))
	strToSign.WriteString("\n")
	// CanonicalHeaders
	strToSign.WriteString(getCanonicalHeaders(c))
	strToSign.WriteString("\n")
	if verb == http.MethodPost {
		canonicalBodyHash, err := getCanonicalBody(c)
		if err != nil {
			return "", nil, err
		}
		strToSign.Write(canonicalBodyHash)
		strToSign.WriteString("\n")
	}

	// 生成签名
	mac := hmac.New(sha256.New, key)
	_, _ = mac.Write(strToSign.Bytes())
	src := mac.Sum(nil)
	dst := make([]byte, base64.StdEncoding.EncodedLen(len(src)))
	base64.StdEncoding.Encode(dst, src)

	return strToSign.String(), dst, nil
}

var pattern = regexp.MustCompile("(?i)^(x-m-.*)$")

func getCanonicalHeaders(c *gin.Context) string {
	var headers []item
	for k, v := range c.Request.Header {
		if len(pattern.FindStringSubmatch(k)) > 0 {
			if len(v) > 0 {
				headers = append(headers, item{
					Key: strings.ToLower(k), Value: strings.ToLower(k) + ":" + strings.TrimSpace(v[len(v)-1]),
				})
			}
		}
	}
	equipID, _ := c.Cookie("equip_id")
	headers = append(headers, item{Key: "equip_id", Value: "equip_id:" + equipID})
	token, _ := c.Cookie("token")
	if token != "" {
		headers = append(headers, item{Key: "token", Value: "token:" + token})
	}

	sort.Slice(headers, func(i, j int) bool {
		return headers[i].Key < headers[j].Key
	})
	var needHeaders strings.Builder
	for i, v := range headers {
		if i > 0 {
			needHeaders.WriteString("\n")
		}
		needHeaders.WriteString(v.Value)
	}
	return needHeaders.String()
}

func getCanonicalBody(c *gin.Context) ([]byte, error) {
	var content []byte
	switch c.ContentType() {
	case gin.MIMEPOSTForm:
		err := c.Request.ParseForm()
		if err != nil {
			return nil, err
		}
		content = encodeValues(c.Request.PostForm)
	case gin.MIMEMultipartPOSTForm:
		f, err := c.MultipartForm()
		if err != nil {
			return nil, err
		}

		var buf bytes.Buffer
		buf.Write(encodeValues(f.Value))

		if len(f.File) > 0 {
			keys := make([]string, 0, len(f.File))
			for k := range f.File {
				keys = append(keys, k)
			}
			sort.Strings(keys)

			buf.WriteString("\nUNSIGNED-PARAMTERS:") // 用 unsigned 标记 file 参数
			for i := range keys {
				if i > 0 {
					buf.WriteByte('&')
				}
				buf.WriteString(URIEncode(keys[i], true))
			}
		}

		content = buf.Bytes()
	case gin.MIMEJSON, "application/octet-stream", "":
		b := bytes.NewBuffer(make([]byte, 0,
			util.MaxInt64(0, c.Request.ContentLength)))
		reader := io.TeeReader(c.Request.Body, b)
		var err error
		content, err = ioutil.ReadAll(reader)
		if err != nil {
			return nil, err
		}
		c.Request.Body.Close()
		c.Request.Body = ioutil.NopCloser(b)
		if c.ContentType() == "" && len(content) > 0 {
			return nil, handler.NewActionError(http.StatusBadRequest, handler.CodeUnknownError, "不支持的 MIME 类型")
		}
	default:
		return nil, handler.NewActionError(http.StatusBadRequest, handler.CodeUnknownError, "不支持的 MIME 类型")
	}
	h := sha256.New()
	_, _ = h.Write(content)
	src := h.Sum(nil)
	dst := make([]byte, base64.StdEncoding.EncodedLen(len(src)))
	base64.StdEncoding.Encode(dst, src)
	return dst, nil
}

// URIEncode 转义 URI 字符
func URIEncode(URI string, encodeSlash bool) string {
	var result strings.Builder
	for i := 0; i < len(URI); i++ {
		v := URI[i]
		if (v >= 'A' && v <= 'Z') || (v >= 'a' && v <= 'z') || (v >= '0' && v <= '9') ||
			v == '_' || v == '-' || v == '~' || v == '.' {
			result.WriteByte(v)
		} else if v == '/' {
			if encodeSlash {
				result.WriteString("%2F")
			} else {
				result.WriteByte(v)
			}
		} else {
			result.WriteString("%")
			result.WriteString(strings.ToUpper(hex.EncodeToString([]byte{v})))
		}
	}
	return result.String()
}

func encodeValues(v url.Values) []byte {
	if v == nil {
		return []byte{}
	}
	keys := make([]string, 0, len(v))
	for k := range v {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var buf bytes.Buffer
	for _, k := range keys {
		vs := v[k]
		keyEncoded := URIEncode(k, true)
		// 同名 key 都需要设置进去
		for i := range vs {
			if buf.Len() > 0 {
				buf.WriteByte('&')
			}
			buf.WriteString(keyEncoded)
			buf.WriteByte('=')
			if vs[i] != "" {
				buf.WriteString(URIEncode(vs[i], true))
			}
		}
	}
	return buf.Bytes()
}
