package apisign

import (
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const serverName = "https://app.uat.missevan.com"

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestBuildSign(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := []byte("testkey")

	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request = httptest.NewRequest(http.MethodGet, serverName+"/you-might-like/get-sounds?persona_id=3", nil)
	c.Request.AddCookie(&http.<PERSON><PERSON>{Name: "equip_id", Value: "5090be76-e533-9e35-e272-49a0016e85db"})
	c.Request.Header.Add("X-M-Date", "2019-04-10T15:45:47Z")
	c.Request.Header.Add("X-M-Nonce", "9e054d64-ae45-4cf9-87ab-57c1b1730287")
	c.Request.Header.Add("Authorization", "MissEvan k7RvK+QipWQsnnhzyPkQkTNpUZK0TZssOBsT40Ut/pQ=")
	c.Request.Header.Add("X-Forwarded-Proto", "https")

	pass, err := VerifySign(c, key)
	require.NoError(err)
	assert.True(pass)

	c, _ = gin.CreateTestContext(httptest.NewRecorder())
	body := strings.NewReader("region=CN&account=***********&password=888888")
	c.Request = httptest.NewRequest(http.MethodPost, serverName+"/member/login", body)
	c.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	c.Request.AddCookie(&http.Cookie{Name: "equip_id", Value: "5090be76-e533-9e35-e272-49a0016e85db"})
	c.Request.Header.Add("X-M-Date", "2019-04-10T15:43:49Z")
	c.Request.Header.Add("X-M-Nonce", "8811a0de-0a0c-4492-acfd-852b47d2ce9b")
	c.Request.Header.Add("Authorization", "MissEvan ewQemtCf6EtdDROz1wofquk3xTUpITU4wAYZ1rJ99Rk=")
	c.Request.Header.Add("X-Forwarded-Proto", "https")

	pass, err = VerifySign(c, key)
	require.NoError(err)
	assert.True(pass)

	c, _ = gin.CreateTestContext(httptest.NewRecorder())
	b := &bytes.Buffer{}
	w := multipart.NewWriter(b)
	assert.NoError(w.WriteField("images_num", "1"))
	assert.NoError(w.WriteField("equipment", "(版本:*******;missevan;nubia NX589J;7.1.1)"))
	_, err = w.CreateFormFile("image_file", "image_file")
	assert.NoError(err)
	assert.NoError(w.Close())
	c.Request = httptest.NewRequest(http.MethodPost, serverName+"/person/image-feed-back", b)
	c.Request.Header.Add("Content-Type", w.FormDataContentType())
	c.Request.AddCookie(&http.Cookie{Name: "equip_id", Value: "5090be76-e533-9e35-e272-49a0016e85db"})
	c.Request.Header.Add("X-M-Date", "2019-04-10T15:49:17Z")
	c.Request.Header.Add("X-M-Nonce", "bac479e2-1d67-4c7e-9436-92e816d4acd6")
	c.Request.Header.Add("Authorization", "MissEvan Rs+KQIVETwumxgqn+fh/jRtZlxeodYazZFhBdpA9a6I=")
	c.Request.Header.Add("X-Forwarded-Proto", "https")

	pass, err = VerifySign(c, key)
	require.NoError(err)
	assert.True(pass)
}

func TestMiddleware(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := []byte("testkey")

	recorder := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(recorder)
	b := &bytes.Buffer{}
	w := multipart.NewWriter(b)
	require.NoError(w.WriteField("images_num", "1"))
	require.NoError(w.WriteField("equipment", "(版本:*******;missevan;nubia NX589J;7.1.1)"))
	_, err := w.CreateFormFile("image_file", "image_file")
	require.NoError(err)
	require.NoError(w.Close())
	c.Request = httptest.NewRequest(http.MethodPost, serverName+"/person/image-feed-back", b)
	c.Request.Header.Add("Content-Type", w.FormDataContentType())
	c.Request.AddCookie(&http.Cookie{Name: "equip_id", Value: "5090be76-e533-9e35-e272-49a0016e85db"})
	c.Request.Header.Add("X-M-Date", "2019-04-10T15:49:17Z")
	c.Request.Header.Add("X-M-Nonce", "bac479e2-1d67-4c7e-9436-92e816d4acd6")
	c.Request.Header.Add("Authorization", "MissEvan Rs+KQIVETwumxgqn+fh/invalid")
	c.Request.Header.Add("X-Forwarded-Proto", "https")

	handlerMode := handler.Mode()
	handler.SetMode("release")
	defer func() {
		handler.SetMode(handlerMode)
	}()

	middleware := Middleware(key)
	middleware(c)

	type body struct {
		Code int    `json:"code"`
		Info string `json:"info"`
	}

	var recorderBody body

	err = json.Unmarshal(recorder.Body.Bytes(), &recorderBody)
	require.NoError(err)
	assert.Equal(http.StatusUnauthorized, recorder.Code)
	assert.Equal(handler.CodeErrSignature, recorderBody.Code)
	assert.Equal("非法请求", recorderBody.Info)
}

func TestEncodeValues(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u, err := url.Parse("http://test.com/get?a=1&a=2&b/2=1&c")
	require.NoError(err)
	s := encodeValues(u.Query())
	assert.Equal("a=1&a=2&b%2F2=1&c=", string(s))
}

func TestMiddlewareV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := []byte("testkey")

	recorder := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(recorder)
	b := &bytes.Buffer{}
	w := multipart.NewWriter(b)
	require.NoError(w.WriteField("images_num", "1"))
	require.NoError(w.WriteField("equipment", "(版本:*******;missevan;nubia NX589J;7.1.1)"))
	_, err := w.CreateFormFile("image_file", "image_file")
	require.NoError(err)
	require.NoError(w.Close())
	c.Request = httptest.NewRequest(http.MethodPost, serverName+"/person/image-feed-back", b)
	c.Request.Header.Add("Content-Type", w.FormDataContentType())
	c.Request.AddCookie(&http.Cookie{Name: "equip_id", Value: "5090be76-e533-9e35-e272-49a0016e85db"})
	c.Request.Header.Add("X-M-Date", "2019-04-10T15:49:17Z")
	c.Request.Header.Add("X-M-Nonce", "bac479e2-1d67-4c7e-9436-92e816d4acd6")
	c.Request.Header.Add("Authorization", "MissEvan Rs+KQIVETwumxgqn+fh/invalid")
	c.Request.Header.Add("X-Forwarded-Proto", "https")

	handlerMode := handler.Mode()
	handler.SetMode("release")
	defer func() {
		handler.SetMode(handlerMode)
	}()

	middleware := MiddlewareV2(key)
	middleware(c)

	var recorderBody handler.BasicResponseV2
	err = json.Unmarshal(recorder.Body.Bytes(), &recorderBody)
	require.NoError(err)
	assert.Equal(http.StatusUnauthorized, recorder.Code)
	assert.Equal(handler.CodeErrSignature, recorderBody.Code)
	assert.Equal("非法请求", recorderBody.Message)
	assert.Nil(recorderBody.Data)
}
