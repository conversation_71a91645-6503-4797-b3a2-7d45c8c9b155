package session

import (
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/service/session"
)

// Config for session
type Config struct {
	Name     string `yaml:"name"`
	Prefix   string `yaml:"prefix"`
	Key      string `yaml:"key"`
	MaxAge   int    `yaml:"max_age"`
	HTTPOnly bool   `yaml:"http_only"`
}

// DefaultConfig for default session config
var DefaultConfig = &Config{
	Name:     "gsession",
	Key:      "secret",
	MaxAge:   86400,
	HTTPOnly: true,
}

var conf Config

// Default shortcut to get session
func Default(c *gin.Context) sessions.Session {
	s := sessions.Default(c)
	s.Options(sessions.Options{
		MaxAge:   conf.MaxAge,
		HttpOnly: conf.HTTPOnly,
	})
	return s
}

// Middleware provide gin router middleware for session.
func Middleware(config *Config) gin.HandlerFunc {
	conf = *config
	s := session.NewStore([]byte(conf.Key))
	return sessions.Sessions(conf.Name, s)
}
