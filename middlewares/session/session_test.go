package session

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestMiddlewareSave(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	// test code
	recorder := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(recorder)
	ctx.Request, _ = http.NewRequest("GET", "/", nil)
	middleware := Middleware(DefaultConfig)
	middleware(ctx)

	s := Default(ctx)
	s.Set("foo", "bar")
	err := s.Save()
	require.NoError(err)

	cookie := recorder.Result().Header["Set-Cookie"]
	assert.Contains(cookie[0], DefaultConfig.Name+"=")
	assert.Contains(cookie[0], fmt.Sprintf("Max-Age=%d", DefaultConfig.MaxAge))
	if DefaultConfig.HTTPOnly {
		assert.Contains(cookie[0], "HttpOnly")
	}
}

func TestMiddlewareNotSave(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	// test code
	recorder := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(recorder)
	ctx.Request, _ = http.NewRequest("GET", "/", nil)
	middleware := Middleware(DefaultConfig)
	middleware(ctx)

	h, ok := recorder.Result().Header["Set-Cookie"]
	assert.False(ok)
	assert.Nil(h)
}
