package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"os"
	"path"

	_ "go.uber.org/automaxprocs"

	"github.com/MiaoSiLa/missevan-go/cmd"
	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/config/configsync"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	appName          = "missevan-go"
	defaultConfigDir = "/etc/missevan-go"
)

var (
	// Version control for missevan-go
	Version = "0.0.1"
	// Conf the main config
	// TODO: 待优化
	Conf config.Config

	commandMap map[string]cmd.Command
)

// TODO: sync config
func loadConfigInCluster() (string, error) {
	err := configsync.InitSync("", appName, Version, nil)
	if err != nil {
		return "", err
	}
	err = configsync.WriteTo(defaultConfigDir)
	if err != nil {
		return "", err
	}
	return defaultConfigDir, nil
}

func main() {
	rand.Seed(util.TimeNow().UnixNano())
	serviceutil.SetVersion(Version)

	flag := flag.NewFlagSet(os.Args[0]+" [mode]", flag.ExitOnError)
	configDir := flag.String("config-dir", "", "config directory")
	configInCluster := flag.Bool("config-in-cluster", false, "load config in cluster")
	showVerbose := flag.Bool("verbose", false, "show verbose debug log")
	showHelp := flag.Bool("help", false, "show help message")

	if len(os.Args) < 2 {
		fmt.Fprintln(os.Stderr, "Please specify run mode")
		flag.Usage()
		os.Exit(1)
	}

	commandMap = cmd.Register()
	cmd, ok := commandMap[os.Args[1]]
	if !ok {
		fmt.Fprintf(os.Stderr, "Unknown run mode: %s\n", os.Args[1])
		flag.Usage()
		os.Exit(1)
	}

	err := flag.Parse(os.Args[2:])
	if err != nil {
		log.Fatalf("parse flag error: %v", err)
	}

	if *showHelp {
		flag.Usage()
		return
	}
	if *configInCluster {
		savePath, err := loadConfigInCluster()
		if err != nil {
			fmt.Fprintf(os.Stderr, "load config in cluster error: %v", err)
			os.Exit(1)
		}
		*configDir = savePath
	}
	if *configDir == "" {
		fmt.Fprintln(os.Stderr, "Please specify a config dir")
		flag.Usage()
		os.Exit(1)
	}

	conf, err := config.LoadConfig(path.Join(*configDir, "config.yml"))
	if err != nil {
		log.Fatalf("config error: %v", err)
	}
	Conf = conf

	if *showVerbose {
		Conf.Log.AccessLevel = "debug"
		Conf.Log.ErrorLevel = "debug"
	}

	if Conf.Log.Agent.Enabled && Conf.Log.Agent.Category == "" {
		Conf.Log.Agent.Category = cmd.Name()
	}
	err = logger.Init(appName, &Conf.Log)
	if err != nil {
		log.Fatalf("logger error: %v", err)
	}
	configsync.SetClientLogger(logger.WithFields(logger.Fields{}))
	util.InitGoroutineLogger(logger.LogError)

	// TODO: 待优化
	config.Conf = Conf
	err = cmd.Run(&Conf)
	if err != nil {
		logger.Fatal(err)
	}
}
