package geetest

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)

	kc.Check(Config{}, "url", "geetest_id", "geetest_key", "salt")
}

func TestConfigRequestURL(t *testing.T) {
	assert := assert.New(t)

	conf := TestConfig()
	assert.Equal("http://127.0.0.1/register.php", conf.RequestURL(URIRegister))
	assert.Equal("http://127.0.0.1/validate.php", conf.RequestURL(URIValidate))
}

func TestNewConfig(t *testing.T) {
	assert := assert.New(t)

	c := NewClient(TestConfig())
	assert.NotNil(c.c)
}

func TestClientRegister(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	cancel := c.SetMock(URIRegister, map[string]string{
		"challenge": "",
	})
	defer cancel()
	_, err := c.Register("127.0.0.1", "12")
	assert.EqualError(err, "geetest: request gt error")
	c.SetMock(URIRegister, map[string]string{
		"challenge": "0",
	})
	_, err = c.Register("127.0.0.1", "12")
	assert.EqualError(err, "geetest: request gt error")

	c.SetMock(URIRegister, map[string]string{
		"challenge": "123",
	})
	challenge, err := c.Register("127.0.0.1", "12")
	require.NoError(err)
	assert.Equal(util.MD5("123"+c.Conf.GeetestKey), challenge)
}

func TestClientValidate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	cancel := c.SetMock(URIValidate, map[string]interface{}{
		"seccode": "",
	})
	defer cancel()
	geetestValidateResp, err := c.Validate(ValidateParam{}, "127.0.0.1", "12", "", "")
	require.NoError(err)
	assert.Empty(geetestValidateResp.Seccode)
	c.SetMock(URIValidate, map[string]interface{}{
		"seccode": "false",
	})
	geetestValidateResp, err = c.Validate(ValidateParam{}, "127.0.0.1", "12", "", "")
	require.NoError(err)
	assert.Equal("false", geetestValidateResp.Seccode)
	c.SetMock(URIValidate, map[string]interface{}{
		"seccode":           "7558a815ee13ddd48dabb58038041e1d",
		"duration":          3.241436243057251,
		"challenge_type":    "click",
		"model_probability": 1,
		"web_simulator":     0,
		"uniformity":        false,
		"origin_info": map[string]interface{}{
			"ua":  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4327.127 Safari/537.36",
			"ip":  "***************",
			"url": "https://www.missevan.com",
		},
	})
	geetestValidateResp, err = c.Validate(ValidateParam{}, "127.0.0.1", "12", "", "")
	require.NoError(err)
	assert.Equal("7558a815ee13ddd48dabb58038041e1d", geetestValidateResp.Seccode)
	assert.Equal(1, *geetestValidateResp.ModelProbability)
	assert.Equal(0, *geetestValidateResp.WebSimulator)
}

func TestClientDo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	req, err := http.NewRequest(http.MethodGet, "https://www.missevan.com/health", nil)
	require.NoError(err)

	resp := make(map[string]interface{})
	assert.NoError(c.do(URIRegister, req, &resp))
	assert.Equal(map[string]interface{}{
		"success": true,
		"info":    "success",
	}, resp)
}

func TestParseToken(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, ok := ParseToken("")
	assert.False(ok)
	_, ok = ParseToken("geetest|")
	assert.False(ok)
	param, ok := ParseToken("geetest|1|2|3")
	assert.True(ok)
	assert.Equal(ValidateParam{
		Challenge: "1",
		Validate:  "2",
		Seccode:   "3",
	}, param)
	param, ok = ParseToken("geetest|1|2|3|")
	require.True(ok)
	assert.Equal(ValidateParam{
		Challenge: "1",
		Validate:  "2",
		Seccode:   "3|",
	}, param)
}

func TestValidateResp_IsValid(t *testing.T) {
	assert := assert.New(t)

	v := ValidateResp{}
	assert.False(v.IsValid())

	v.Seccode = "123456"
	assert.True(v.IsValid())
}

func TestValidateResp_RiskLevel(t *testing.T) {
	assert := assert.New(t)

	modelProbability := 0
	webSimulator := 0

	v := ValidateResp{}
	assert.Equal(RiskLevelHigh, v.RiskLevel())

	v.Seccode = "123"
	assert.Equal(RiskLevelLow, v.RiskLevel())

	webSimulator = 1
	v.WebSimulator = &webSimulator
	assert.Equal(RiskLevelMedium, v.RiskLevel())

	modelProbability = 1
	v.ModelProbability = &modelProbability
	assert.Equal(RiskLevelHigh, v.RiskLevel())
}
