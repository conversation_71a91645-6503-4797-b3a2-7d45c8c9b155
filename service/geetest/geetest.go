package geetest

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/service/util/retryhttp"
	"github.com/MiaoSiLa/missevan-go/util"
)

// RiskLevel 风险等级
type RiskLevel = int

// 风险等级: 低风险，中风险（建议二次验证），高风险（建议直接拦截）
const (
	RiskLevelLow RiskLevel = iota + 1
	RiskLevelMedium
	RiskLevelHigh
)

// 请求常量
const (
	digestmodMD5   = "md5"
	jsonFormatFlag = "1"
	sdkFlag        = "golang-gin:3.1.1"
)

// uri
// 接口文档：https://docs.geetest.com/sensebot/apirefer/api/server
const (
	URIRegister = "/register.php"
	URIValidate = "/validate.php"
)

// Config config
type Config struct {
	URL        string `yaml:"url"`
	GeetestID  string `yaml:"geetest_id"`
	GeetestKey string `yaml:"geetest_key"`
	Salt       string `yaml:"salt"`
}

// RequestURL returns request url
func (c *Config) RequestURL(uri string) string {
	return c.URL + uri
}

// Client client
type Client struct {
	Conf Config

	c *http.Client
}

// NewClient new client
func NewClient(conf Config) *Client {
	return &Client{
		Conf: conf,
		c:    retryhttp.NewEOFRetryClient(nil),
	}
}

// GeetestID 极验账号 ID
func (c *Client) GeetestID() string {
	return c.Conf.GeetestID
}

// Register 请求 register
func (c *Client) Register(ip, uniqueID string) (string, error) {
	q := url.Values{}
	q.Set("digestmod", digestmodMD5)
	q.Set("gt", c.Conf.GeetestID)
	q.Set("json_format", jsonFormatFlag)
	q.Set("sdk", sdkFlag)
	if uniqueID != "" {
		q.Set("user_id", c.md5UniqueID(uniqueID))
	}
	if ip != "" {
		q.Set("ip_address", ip)
	}
	url := c.Conf.RequestURL(URIRegister) + "?" + q.Encode()
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return "", err
	}
	var resp struct {
		Challenge string `json:"challenge"`
	}
	err = c.do(URIRegister, req, &resp)
	if err != nil {
		return "", err
	}
	if resp.Challenge == "" || resp.Challenge == "0" {
		return "", errors.New("geetest: request gt error")
	}
	return util.MD5(resp.Challenge + c.Conf.GeetestKey), nil
}

// ValidateResp 极验 /validate.php 接口响应
// 示例：
// 验证通过
//
//	{
//	  "seccode": "7558a815ee13ddd48dabb58038041e1d",
//	  "duration": 3.241436243057251,
//	  "challenge_type": "click",
//	  "model_probability": 1,
//	  "web_simulator": 0,
//	  "uniformity": false,
//	  "origin_info": {
//	    "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4327.127 Safari/537.36",
//	    "ip": "***************",
//	    "url": "https://www.missevan.com"
//	  }
//	}
//
// 验证不通过：
//
//	{
//	  "seccode": "false",
//	}
//
// 文档：https://info.missevan.com/pages/viewpage.action?pageId=132106906
type ValidateResp struct {
	Seccode       string   `json:"seccode"`
	Duration      *float64 `json:"duration,omitempty"`
	ChallengeType *string  `json:"click,omitempty"`
	// model_probability 是协议检测，黑产使用固定的脚本破解协议，极验更新新的协议后，携带旧的协议参数的行为会命中
	ModelProbability *int `json:"model_probability,omitempty"`
	// web_simulator 是识别使用 web 自动化模拟器的环境，也就是类似 phantomJS 平台模拟器、nightmare 模拟器、驱动 chrome 的模拟器、selenium 驱动的模拟器等.
	WebSimulator *int                   `json:"web_simulator,omitempty"`
	Uniformity   *bool                  `json:"uniformity,omitempty"`
	OriginInfo   map[string]interface{} `json:"origin_info,omitempty"`
}

// IsValid 极验是否校验通过
func (v *ValidateResp) IsValid() bool {
	return v.Seccode != "" && v.Seccode != "false"
}

// RiskLevel 风险等级
func (v *ValidateResp) RiskLevel() RiskLevel {
	if !v.IsValid() {
		return RiskLevelHigh
	}

	riskLevel := RiskLevelLow

	isLaunchedByScript := v.ModelProbability != nil && *v.ModelProbability == 1
	isWebSimulator := v.WebSimulator != nil && *v.WebSimulator == 1

	if isWebSimulator {
		riskLevel = RiskLevelMedium
	}
	if isLaunchedByScript {
		riskLevel = RiskLevelHigh
	}
	return riskLevel
}

// Validate 请求 validate
func (c *Client) Validate(param ValidateParam, ip, uniqueID, userAgent, pageURL string) (*ValidateResp, error) {
	postForm := url.Values{}
	postForm.Set("seccode", param.Seccode)
	postForm.Set("challenge", param.Challenge)
	postForm.Set("json_format", jsonFormatFlag)
	postForm.Set("sdk", sdkFlag)
	postForm.Set("captchaid", c.Conf.GeetestID)
	if uniqueID != "" {
		postForm.Set("user_id", c.md5UniqueID(uniqueID))
	}
	if ip != "" {
		postForm.Set("ip_address", ip)
	}
	if userAgent != "" {
		postForm.Set("customer_ua", userAgent)
	}
	if pageURL != "" {
		postForm.Set("customer_url", pageURL)
	}
	req, err := http.NewRequest(http.MethodPost, c.Conf.RequestURL(URIValidate),
		strings.NewReader(postForm.Encode()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	var resp ValidateResp
	err = c.do(URIValidate, req, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

func (c *Client) do(uri string, req *http.Request, resp interface{}) error {
	ok, err := c.mockDo(uri, resp)
	if ok {
		return err
	}
	r, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer r.Body.Close()
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return err
	}
	if r.StatusCode != http.StatusOK {
		return &serviceutil.APIError{
			Status:  r.StatusCode,
			Message: fmt.Sprintf("geetest: %s", body),
		}
	}
	return json.Unmarshal(body, &resp)
}

func (c *Client) md5UniqueID(uniqueID string) string {
	return util.MD5(uniqueID + c.Conf.Salt)
}

// ValidateParam validate param
type ValidateParam struct {
	Challenge string
	Validate  string
	Seccode   string
}

const tokenPrefix = "geetest|"

// ParseToken 解析 token
func ParseToken(token string) (ValidateParam, bool) {
	if !strings.HasPrefix(token, tokenPrefix) {
		return ValidateParam{}, false
	}
	ss := strings.SplitN(token[len(tokenPrefix):], "|", 3)
	if len(ss) != 3 {
		logger.Debugf("geetest: wrong token: %s", token)
		return ValidateParam{}, false
	}
	return ValidateParam{
		Challenge: ss[0],
		Validate:  ss[1],
		Seccode:   ss[2],
	}, true
}
