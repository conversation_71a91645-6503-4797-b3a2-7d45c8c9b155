package session

import (
	"bytes"
	"encoding/base32"
	"encoding/gob"
	"errors"
	"net/http"
	"strings"
	"time"

	ginSessions "github.com/gin-contrib/sessions"
	"github.com/gorilla/securecookie"
	"github.com/gorilla/sessions"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// session 和 redis key 默认过期时间为一周
const sessionExpire = 86400 * 7

type redisStore struct {
	Codecs    []securecookie.Codec
	opt       *sessions.Options // default configuration
	maxLength int
	keyPrefix string
}

// Get should return a cached session.
func (s *redisStore) Get(r *http.Request, name string) (*sessions.Session, error) {
	return sessions.GetRegistry(r).Get(s, name)
}

// New should create and return a new session.
func (s *redisStore) New(r *http.Request, name string) (*sessions.Session, error) {
	var err error
	session := sessions.NewSession(s, name)
	*session.Options = *s.opt
	session.IsNew = true
	if c, errCookie := r.<PERSON>(name); errCookie == nil {
		err = securecookie.DecodeMulti(name, c.Value, &session.ID, s.Codecs...)
		if err == nil {
			ok, err := s.load(session)
			session.IsNew = !(err == nil && ok) // not new if no error and data available
		}
	}
	return session, err
}

func (s *redisStore) load(session *sessions.Session) (bool, error) {
	data, err := service.LRURedis.Get(s.keyPrefix + session.ID).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return false, nil
		}
		return false, err
	}
	if data == "" {
		return false, errors.New("redisStore: LRURedis get session is empty")
	}
	return true, deserialize([]byte(data), session)
}

// Save should persist session to the underlying store implementation.
func (s *redisStore) Save(_ *http.Request, w http.ResponseWriter, session *sessions.Session) error {
	// Marked for deletion.
	if session.Options.MaxAge <= 0 {
		if err := service.LRURedis.Del(s.keyPrefix + session.ID).Err(); err != nil {
			logger.Errorf("redisStore: %v", err)
			// PASS
		}
		http.SetCookie(w, sessions.NewCookie(session.Name(), "", session.Options))
		return nil
	}

	// Build an alphanumeric key for the redis store.
	if session.ID == "" {
		session.ID = strings.TrimRight(base32.StdEncoding.EncodeToString(securecookie.GenerateRandomKey(32)),
			"=")
	}
	if err := s.save(session); err != nil {
		logger.Errorf("redisStore: %v", err)
		return err
	}
	encoded, err := securecookie.EncodeMulti(session.Name(), session.ID, s.Codecs...)
	if err != nil {
		logger.Errorf("redisStore: %v", err)
		return err
	}
	http.SetCookie(w, sessions.NewCookie(session.Name(), encoded, session.Options))
	return nil
}

// save stores the session in redis.
func (s *redisStore) save(session *sessions.Session) error {
	b, err := serialize(session)
	if err != nil {
		logger.Errorf("redisStore: %v", err)
		return err
	}
	if s.maxLength != 0 && len(b) > s.maxLength {
		return errors.New("redisStore: the value to store is too big")
	}
	age := session.Options.MaxAge
	err = service.LRURedis.Set(s.keyPrefix+session.ID, b, time.Duration(age)*time.Second).Err()
	return err
}

// Serialize using gob
func serialize(ss *sessions.Session) ([]byte, error) {
	buf := new(bytes.Buffer)
	enc := gob.NewEncoder(buf)
	err := enc.Encode(ss)
	if err == nil {
		return buf.Bytes(), nil
	}
	return nil, err
}

// Deserialize back to map[interface{}]interface{}
func deserialize(d []byte, ss *sessions.Session) error {
	dec := gob.NewDecoder(bytes.NewBuffer(d))
	return dec.Decode(&ss)
}

// Options Session 设置
func (s *redisStore) Options(options ginSessions.Options) {
	s.opt = &sessions.Options{
		Path:     options.Path,
		Domain:   options.Domain,
		MaxAge:   options.MaxAge,
		Secure:   options.Secure,
		HttpOnly: options.HttpOnly,
	}
}

// 默认 session 配置
func defaultOptions() *sessions.Options {
	return &sessions.Options{
		Path:   "/",
		MaxAge: sessionExpire,
	}
}

// NewStore 创建新的 Store
func NewStore(keyPairs []byte) ginSessions.Store {
	return &redisStore{
		opt:       defaultOptions(),
		Codecs:    securecookie.CodecsFromPairs(keyPairs),
		maxLength: 4096,
		keyPrefix: "session_",
	}
}
