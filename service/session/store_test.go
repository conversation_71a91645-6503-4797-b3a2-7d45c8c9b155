package session

import (
	"net/http"
	"os"
	"testing"

	"github.com/gorilla/securecookie"
	"github.com/gorilla/sessions"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	service.InitTestService()
	os.Exit(m.Run())
}

func TestNew(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	req, _ := http.NewRequest("GET", "/", nil)
	store := new(redisStore)
	store.opt = defaultOptions()
	_, err := store.New(req, "111")
	assert.NoError(err)
}

func TestSave(t *testing.T) {
	assert := assert.New(t)
	s := new(sessions.Session)
	s.Options = defaultOptions()
	req, _ := http.NewRequest("GET", "/", nil)
	resp := new(http.ResponseWriter)
	store := new(redisStore)
	store.Codecs = securecookie.CodecsFromPairs([]byte("keyPairs"))
	err := store.Save(req, *resp, s)
	assert.NoError(err)
}

func TestGet(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	req, _ := http.NewRequest("GET", "/", nil)
	store := new(redisStore)

	store.Codecs = securecookie.CodecsFromPairs([]byte("keyPairs"))
	store.opt = defaultOptions()
	store.maxLength = 4096
	store.keyPrefix = "session_"
	_, err := store.Get(req, "111")
	assert.NoError(err)
}

func TestSaveLoad(t *testing.T) {
	assert := assert.New(t)
	store := new(redisStore)
	store.opt = defaultOptions()
	session := sessions.NewSession(store, "111")
	*session.Options = *store.opt
	session.IsNew = true
	err := store.save(session)
	assert.NoError(err)
	_, err = store.load(session)
	assert.NoError(err)
}

func TestSerializeDeserialize(t *testing.T) {
	assert := assert.New(t)
	store := new(redisStore)
	store.opt = defaultOptions()
	session := sessions.NewSession(store, "111")
	*session.Options = *store.opt
	b, err := serialize(session)
	assert.NoError(err)
	err = deserialize(b, session)
	assert.NoError(err)
}

func TestNewStore(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	store := NewStore([]byte("keyPairs"))
	assert.NotNil(store)
}
