package mrpc

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestSetMock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(TestConfig())
	require.NoError(err)

	testErr := errors.New("test")
	uri := "go://test"
	var output struct {
		OK int `json:"ok"`
	}
	logger.Debug("before mock")
	// mockedURIs nil 不会 panic
	require.Nil(mockedURIs)
	errBefore := c.Call(uri, "", 1, &output)
	require.Error(errBefore)
	assert.NotEqual(testErr, errBefore)

	logger.Debug("mocking")
	var ok bool
	cancel := SetMock("go://test", func(i interface{}) (interface{}, error) {
		ok = true
		assert.Equal(1, i)
		return map[string]int{"ok": 1}, testErr
	})

	require.NotNil(mockedURIs)
	require.NotNil(mockedURIs["go://test"])
	err = c.Call("go://test", "", 1, &output)
	require.Equal(testErr, err)
	assert.Equal(1, output.OK)
	assert.True(ok)
	// 删除后正常
	logger.Debug("after mock")
	cancel()
	_, ok = mockedURIs["go://test"]
	assert.False(ok)
	errAfter := c.Call(uri, "", 1, &output)
	assert.Equal(errBefore, errAfter)
}
