//go:build !release
// +build !release

package mrpc

import "encoding/json"

// MockFunc rpc action 解析函数，mock 使用
type MockFunc func(input interface{}) (output interface{}, err error)

// mockedURIs 被 mock 的 uri 集合
var mockedURIs map[string]MockFunc

// TestConfig 单元测试基础配置
func TestConfig() Config {
	return Config{
		"app":      {URL: "http://missevan-app.srv.maoer.co:8017/rpc/", Key: "testkey"},
		"drama":    {URL: "http://missevan-drama.srv.maoer.co:8080/rpc/", Key: "testkey"},
		"go":       {URL: "http://missevan-go.srv.maoer.co:3032/rpc/", Key: "testkey"},
		"sso":      {URL: "http://missevan-sso.srv.maoer.co:3002/rpc/", Key: "testkey"},
		"live":     {URL: "http://live-service.srv.maoer.co:3013/rpc/", Key: "testkey"},
		"im":       {URL: "http://live-service.srv.maoer.co:3011/rpc/", Key: "testkey"},
		"fm":       {URL: "http://audio-chatroom.srv.maoer.co:3012/rpc/", Key: "testkey"}, // 目前无法访问
		"minigame": {URL: "http://missevan-minigame.srv.maoer.co:3035/rpc/", Key: "testkey"},
		"mrpc":     {URL: "http://mrpc:3000/rpc/", Key: "testkey"},
		"test":     {URL: "http://127.0.0.1:18997/rpc/", Key: "testkey"},
	}
}

// mockResp mock 请求后的结果
// NOTICE: 外层仅需要判断第一个参数，第二个参数也可能是返回结果之一
func (c *Client) mockResp(uri string, input, output interface{}) (bool, error) {
	if mockedURIs == nil {
		return false, nil
	}
	f := mockedURIs[uri]
	if f == nil {
		return false, nil
	}
	oPre, mockedErr := f(input)
	s, err := json.Marshal(oPre)
	if err != nil {
		return true, err
	}
	err = json.Unmarshal(s, &output)
	if err != nil {
		return true, err
	}
	return true, mockedErr
}

// SetMock mock 某 url，不进行 rpc 请求，走 mockFunc 返回数据
func SetMock(uri string, mockFunc MockFunc) func() {
	if mockedURIs == nil {
		mockedURIs = make(map[string]MockFunc)
	}
	mockedURIs[uri] = mockFunc
	return func() {
		delete(mockedURIs, uri)
	}
}
