package mrpc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"time"

	"go.mercari.io/go-dnscache"
	"go.uber.org/zap"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/util"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	keySwimlane    = "X-M-Swimlane"
	envDeployColor = "DEPLOY_COLOR"
)

const (
	mrpcVersion = "mrpc/1.0.0"
)

var (
	dnsResolver *dnscache.Resolver
)

// ConfigEntry for missevan rpc
type ConfigEntry struct {
	URL string `yaml:"url"`
	Key string `yaml:"key"`

	AppKey string `yaml:"app_key"`
}

// Config for missevan rpc
type Config map[string]ConfigEntry

// Client structure for missevan rpc
type Client struct {
	Config

	httpClient *http.Client
}

// ClientResponse structure for missevan rpc
type ClientResponse struct {
	Code    int             `json:"code"`
	Info    json.RawMessage `json:"info"` // legacy
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

// ClientError for client error message
type ClientError struct {
	Scheme  string
	Status  int
	Code    int
	Message string
}

// Error return error message
func (e *ClientError) Error() string {
	return fmt.Sprintf("mrpc (%s): (%d) %s", e.Scheme, e.Code, e.Message)
}

// IsCode returns if err is a client error and error code is the given code
// Deprecated: use IsErrorCode
func IsCode(err error, code int) bool {
	if e, ok := err.(*ClientError); ok {
		if e.Code == code {
			return true
		}
	}
	return false
}

// IsErrorCode returns if err is a client error and error code in the given codes
func IsErrorCode(err error, code ...int) bool {
	rpcError, ok := err.(*ClientError)
	if !ok {
		return false
	}
	return goutil.HasElem(code, rpcError.Code)
}

// EntryNotFoundError error string
type EntryNotFoundError string

// Error return entry not found error
func (err *EntryNotFoundError) Error() string {
	return fmt.Sprintf("mrpc: uri config entry \"%s\" not found", *err)
}

// NewEntryNotFoundError return entry not found error
func NewEntryNotFoundError(scheme string) *EntryNotFoundError {
	err := EntryNotFoundError(scheme)
	return &err
}

// NewRPCClient return the rpc client
func NewRPCClient(conf Config) *Client {
	return &Client{
		Config:     conf,
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
}

// NewClient return the rpc client
func NewClient(conf Config) (*Client, error) {
	client := &Client{
		Config:     conf,
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}
	resolver, err := newDNSResolver()
	if err != nil {
		return nil, err
	}
	client.httpClient.Transport = &http.Transport{
		DialContext: dnscache.DialFunc(resolver, nil),
	}
	return client, nil
}

// Call rpc
// Deprecated: use Do
func (c *Client) Call(uri, ip string, input, output interface{}, cookies ...map[string]string) error {
	// 先判断是否被 mock (忽略 entry 未配置的情况)
	ok, err := c.mockResp(uri, input, output)
	if ok {
		return err
	}

	u, err := url.Parse(uri)
	if err != nil {
		return err
	}
	conf, ok := c.Config[u.Scheme]
	if !ok {
		return NewEntryNotFoundError(u.Scheme)
	}
	signedData := util.RPCSign(input, conf.Key)
	uri = conf.URL + u.Host + u.Path
	req, err := http.NewRequest(http.MethodPost, uri, bytes.NewReader(signedData))
	if err != nil {
		return err
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", mrpcVersion+" "+serviceutil.UserAgent)
	if conf.AppKey != "" {
		req.Header.Set("X-AppKey", conf.AppKey)
	}
	if swimline := os.Getenv(envDeployColor); swimline != "" {
		req.Header.Set(keySwimlane, swimline)
	}
	for i := 0; i < len(cookies); i++ {
		for k, v := range cookies[i] {
			if v != "" {
				req.AddCookie(&http.Cookie{Name: k, Value: v})
			}
		}
	}
	if ip != "" {
		req.Header.Set("X-Forwarded-For", ip) // XFF header
	}

	return c.doRequest(req, output, signedData, u)
}

func newDNSResolver() (*dnscache.Resolver, error) {
	if dnsResolver != nil {
		return dnsResolver, nil
	}
	var err error
	// New 的第一个参数是刷新缓存的周期，第二个参数是 DNS 查询的超时时间
	// 每次刷新缓存都会把地址池内的地址都重新查询一次，如果查询成功则会更新
	// DNS 查询失败时（比如超时）不会更新已有 IP
	// 已缓存的 DNS 不会被清理掉，只会被刷新
	dnsResolver, err = dnscache.New(20*time.Second, 5*time.Second, zap.NewNop())
	return dnsResolver, err
}

// UserContext user context
type UserContext struct {
	IP        string
	Token     string
	UserAgent string
	BUVID     string
	EquipID   string
	Swimlane  string
}

// NewUserContext new UserContext
func NewUserContext(req *http.Request, clientIP string) UserContext {
	ctx := UserContext{
		IP: clientIP,
	}
	ctx.UserAgent = req.Header.Get("User-Agent")
	cookie, _ := req.Cookie("token")
	if cookie != nil {
		ctx.Token = cookie.Value
	}
	cookie, _ = req.Cookie("buvid")
	if cookie != nil {
		ctx.BUVID = cookie.Value
	}
	cookie, _ = req.Cookie("equip_id")
	if cookie != nil {
		ctx.EquipID = cookie.Value
	}
	ctx.Swimlane = req.Header.Get(keySwimlane)
	return ctx
}

// NewUserContextFromEnv 通过 env 获取 UserContext
func NewUserContextFromEnv() UserContext {
	ctx := UserContext{
		Swimlane: os.Getenv(envDeployColor),
	}
	return ctx
}

func (ctx UserContext) populateRequest(req *http.Request, appKey string) {
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", mrpcVersion+" "+serviceutil.UserAgent)
	if appKey != "" {
		req.Header.Set("X-AppKey", appKey)
	}
	if ctx.IP != "" {
		req.Header.Set("X-Real-IP", ctx.IP)
		req.Header.Set("X-Forwarded-For", ctx.IP) // XFF header
	}
	if ctx.Token != "" {
		req.AddCookie(&http.Cookie{Name: "token", Value: ctx.Token})
	}
	if ctx.BUVID != "" {
		req.AddCookie(&http.Cookie{Name: "buvid", Value: ctx.BUVID})
	}
	if ctx.EquipID != "" {
		req.AddCookie(&http.Cookie{Name: "equip_id", Value: ctx.EquipID})
	}
	if ctx.Swimlane != "" {
		req.Header.Set(keySwimlane, ctx.Swimlane)
	}
}

// Do request
func (c *Client) Do(ctx UserContext, uri string, input, output interface{}) error {
	ok, err := c.mockResp(uri, input, output)
	if ok {
		return err
	}

	u, err := url.Parse(uri)
	if err != nil {
		return err
	}
	conf, ok := c.Config[u.Scheme]
	if !ok {
		return NewEntryNotFoundError(u.Scheme)
	}
	signedData := util.RPCSign(input, conf.Key)
	uri = conf.URL + u.Host + u.Path
	if u.RawQuery != "" {
		uri += "?" + u.RawQuery
	}
	req, err := http.NewRequest(http.MethodPost, uri, bytes.NewReader(signedData))
	if err != nil {
		return err
	}
	ctx.populateRequest(req, conf.AppKey)
	return c.doRequest(req, output, signedData, u)
}

func (c *Client) doRequest(req *http.Request, output interface{},
	signedData []byte, u *url.URL) error {
	logger.Debugf("POST %s\n%s", req.URL, signedData)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}

	// close response
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	logger.Debugf("HTTP %s\n%s", resp.Status, body)

	var result ClientResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		return err
	}

	if result.Code != util.CodeRPCSuccess {
		clientErr := &ClientError{
			Scheme: u.Scheme,
			Status: resp.StatusCode,
			Code:   result.Code,
		}
		if result.Info != nil { // legacy
			err := json.Unmarshal(result.Info, &clientErr.Message)
			if err != nil {
				clientErr.Message = string(result.Info)
			}
		} else {
			clientErr.Message = result.Message
		}
		return clientErr
	}

	if output != nil {
		if result.Info != nil { // legacy
			err = json.Unmarshal(result.Info, output)
		} else {
			err = json.Unmarshal(result.Data, output)
		}
		if err != nil {
			return err
		}
	}

	return nil
}
