package mrpc

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var c *Client

type serverHandler struct {
	srv *http.Server
}

func (sh *serverHandler) ServeHTTP(rw http.ResponseWriter, req *http.Request) {
	var data []byte
	body, err := io.ReadAll(req.Body)
	if err != nil {
		rw.WriteHeader(http.StatusInternalServerError)
		return
	}
	pos := bytes.IndexByte(body, ' ') // sign not check
	if pos < 0 {
		rw.WriteHeader(http.StatusBadRequest)
		return
	}
	body = body[:pos]
	plainBody, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		rw.WriteHeader(http.StatusBadRequest)
		return
	}
	response := map[string]interface{}{
		"code":    0,
		"message": "test",
		"data":    nil,
	}
	if req.URL.Path == "/rpc/echo" {
		response["data"] = json.RawMessage(plainBody)
	} else if req.URL.Path == "/rpc/echo-legacy" {
		response = map[string]interface{}{
			"code": 0,
			"info": json.RawMessage(plainBody),
		}
	} else if req.URL.Path == "/rpc/echo-params" {
		// 处理查询参数
		params := map[string]interface{}{
			"params": req.URL.Query(),
			"data":   json.RawMessage(plainBody),
		}
		paramData, _ := json.Marshal(params)
		response = map[string]interface{}{
			"code": 0,
			"info": json.RawMessage(paramData),
		}
	} else if req.URL.Path == "/rpc/logout" {
		response["code"] = 300030002
		response["message"] = "Token 过期或不存在"
	}
	data, _ = json.Marshal(response)
	rw.WriteHeader(http.StatusOK)
	_, _ = rw.Write(data)
}

func (sh *serverHandler) Run() error {
	var err error
	go func() {
		err = sh.srv.ListenAndServe()
	}()
	time.Sleep(200 * time.Millisecond)
	return err
}

func (sh *serverHandler) Shutdown() {
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := sh.srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown http server failed: %v", err)
		// PASS
	}
}

func TestMain(m *testing.M) {
	logger.InitTestLog()
	sh := &serverHandler{}
	sh.srv = &http.Server{
		Addr:    "127.0.0.1:18997",
		Handler: sh}
	err := sh.Run()
	if err != nil {
		logger.Fatal(err)
	}
	defer sh.Shutdown()
	client, err := NewClient(TestConfig())
	if err != nil {
		logger.Fatal(err)
	}
	c = client
	m.Run()
}

func TestRPCClientCall(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	require.NoError(os.Setenv(envDeployColor, "env_swimlane"))
	defer os.Unsetenv(envDeployColor)
	data := map[string]interface{}{
		"foo": "bar",
	}
	var result map[string]interface{}
	err := c.Call("test://echo", "127.0.0.1", data, &result)
	require.NoError(err)
	assert.Equal(data, result)

	err = c.Call("test://echo-legacy", "127.0.0.1", data, &result)
	require.NoError(err)
	assert.Equal(data, result)

	err = c.Call("test2://echo", "127.0.0.1", data, &result)
	assert.EqualError(err, `mrpc: uri config entry "test2" not found`)
}

func TestIsCode(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	code := 200360006
	assert.False(IsCode(nil, code))
	assert.True(IsCode(&ClientError{Code: code}, code))
	assert.False(IsCode(errors.New(""), code))
	assert.False(IsCode(&ClientError{}, code))
}

func TestCallWithHeader(t *testing.T) {
	assert := assert.New(t)

	err := c.Call("test://logout", "127.0.0.1",
		struct{}{},
		nil,
		map[string]string{"token": "123"})
	assert.EqualError(err, "mrpc (test): (300030002) Token 过期或不存在")
}

func TestNewEntryNotFoundError(t *testing.T) {
	assert := assert.New(t)

	err := NewEntryNotFoundError("go")
	assert.EqualError(err, `mrpc: uri config entry "go" not found`)
}

func TestNewUserContext(t *testing.T) {
	assert := assert.New(t)

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	req.Header.Set("User-Agent", "user-agent")
	req.Header.Set(keySwimlane, "swimlane")
	req.AddCookie(&http.Cookie{Name: "token", Value: "token"})
	req.AddCookie(&http.Cookie{Name: "buvid", Value: "buvid"})
	req.AddCookie(&http.Cookie{Name: "equip_id", Value: "equip_id"})

	c := NewUserContext(req, "127.0.0.1")
	assert.Equal("127.0.0.1", c.IP)
	assert.Equal("token", c.Token)
	assert.Equal("user-agent", c.UserAgent)
	assert.Equal("buvid", c.BUVID)
	assert.Equal("equip_id", c.EquipID)
	assert.Equal("swimlane", c.Swimlane)
}

func TestNewUserContextFromEnv(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(os.Setenv(envDeployColor, "env_swimlane"))
	defer os.Unsetenv(envDeployColor)
	c := NewUserContextFromEnv()
	assert.Equal("env_swimlane", c.Swimlane)

	require.NoError(os.Unsetenv(envDeployColor))
	c = NewUserContextFromEnv()
	assert.Equal("", c.Swimlane)
}

func TestUserContext_addCookies(t *testing.T) {
	assert := assert.New(t)

	ctx := UserContext{
		IP:       "127.0.0.1",
		Token:    "test-token",
		BUVID:    "test-buvid",
		EquipID:  "test-equip-id",
		Swimlane: "test-swimlane",
	}
	req := httptest.NewRequest(http.MethodPost, "/test", nil)
	ctx.populateRequest(req, "app_key")
	assert.Equal("app_key", req.Header.Get("X-AppKey"))
	assert.Equal(ctx.IP, req.Header.Get("X-Real-IP"))
	assert.Equal(ctx.IP, req.Header.Get("X-Forwarded-For"))
	assert.Equal(3, len(req.Cookies()))
}

func TestClient_Do(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var ctx UserContext
	data := map[string]interface{}{
		"foo": "bar",
	}
	var result map[string]interface{}
	err := c.Do(ctx, "test://echo", data, &result)
	require.NoError(err)
	assert.Equal(data, result)

	err = c.Do(ctx, "test2://echo", data, &result)
	assert.EqualError(err, `mrpc: uri config entry "test2" not found`)
}

func TestIsErrorCode(t *testing.T) {
	assert := assert.New(t)

	err := &ClientError{
		Code: 100010002,
	}

	// 测试不是对应的 code 错误码
	assert.False(IsErrorCode(err, 100010003))

	// 测试是对应的 code 错误码
	assert.True(IsErrorCode(err, 100010002, 100010003))
}

func TestClient_DoWithQueryParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var ctx UserContext
	data := map[string]interface{}{
		"foo": "bar",
	}
	var result map[string]interface{}

	// 测试带有查询参数的 URI
	err := c.Do(ctx, "test://echo-params?param1=value1&param2=value2", data, &result)
	require.NoError(err)

	// 验证查询参数是否正确传递
	require.NotNil(result["params"])
	params, ok := result["params"].(map[string]interface{})
	require.True(ok)

	paramValues, ok := params["param1"].([]interface{})
	require.True(ok)
	require.Equal(1, len(paramValues))
	assert.Equal("value1", paramValues[0])

	paramValues, ok = params["param2"].([]interface{})
	require.True(ok)
	require.Equal(1, len(paramValues))
	assert.Equal("value2", paramValues[0])

	// 验证原始数据是否正确传递
	require.NotNil(result["data"])
	originalData, ok := result["data"].(map[string]interface{})
	require.True(ok)
	assert.Equal(data["foo"], originalData["foo"])

	// 测试不带查询参数的 URI
	result = make(map[string]interface{})
	err = c.Do(ctx, "test://echo-params", data, &result)
	require.NoError(err)

	// 验证查询参数是空的
	require.NotNil(result["params"])
	params, ok = result["params"].(map[string]interface{})
	require.True(ok)
	assert.Equal(0, len(params))
}
