package util

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestRPCSign(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	apiKey := "testkey"
	data := map[string]interface{}{
		"foo": "bar",
	}
	signedData := RPCSign(data, apiKey)

	body, err := CheckRPCSign(signedData, apiKey)
	require.NoError(err)
	var checkData map[string]interface{}
	err = json.Unmarshal(body, &checkData)
	require.NoError(err)
	assert.Equal(data, checkData)

	testData := "<></>"
	util.SetTimeNow(func() time.Time {
		return time.Unix(100000, 0)
	})
	defer util.SetTimeNow(nil)
	signedData = RPCSign(json.RawMessage(testData), apiKey)
	body, err = CheckRPCSign(signedData, apiKey)
	require.NoError(err)
	assert.Equal(testData, string(body))

	signedData = RPCSign(testData, apiKey)
	body, err = CheckRPCSign(signedData, apiKey)
	require.NoError(err)
	// 会有 html escape
	assert.Equal(`"\u003c\u003e\u003c/\u003e"`, string(body))
}
