package goclient_test

import (
	"net/http"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	service.InitTestService()
	os.Exit(m.Run())
}

func TestCheckText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/path", false, nil)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")

	result, err := goclient.CheckText(c, "item.taobao.com", scan.SceneUserInfo, scan.AdFreeOption)

	require.NoError(err)
	assert.False(result.Pass)
	assert.Equal(result.Labels[0], "blacklist")
}

func TestCheckTexts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/path", true, nil)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	results, allPass, err := goclient.CheckTexts(c, []string{"item.taobao.com", "ok"}, scan.SceneUserInfo, scan.AdFreeOption)
	require.NoError(err)
	require.Len(results, 2)
	assert.False(allPass)
	assert.False(results[0].Pass)
	assert.Equal(results[0].Labels[0], "blacklist")
	assert.True(results[1].Pass)
}
