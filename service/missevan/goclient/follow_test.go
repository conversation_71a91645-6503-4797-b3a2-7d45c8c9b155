package goclient

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const (
	testUserID      int64 = 2333
	testAttentionID int64 = 2334
)

func TestFollowUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(uriFollow, func(input interface{}) (interface{}, error) {
		assert.Equal(map[string]interface{}{
			"user_id":        testUserID,
			"follow_user_id": testAttentionID,
			"follow_from":    person.FollowFromGameDownload,
		}, input)
		return nil, nil
	})
	defer cancel()

	var c mrpc.UserContext
	require.NoError(FollowUser(c, testUserID, testAttentionID, person.FollowFromGameDownload))
}

func TestIsRPCUserError(t *testing.T) {
	assert := assert.New(t)

	// 测试 err 为 nil 的情况
	assert.False(IsRPCUserError(nil))

	// 测试不是 rpc user code 错误码
	err := &mrpc.ClientError{
		Code: handler.CodeAlbumNotFound,
	}
	assert.False(IsRPCUserError(err))

	// 测试是 rpc user code 错误码
	err.Code = handler.CodeBlockedUser
	assert.True(IsRPCUserError(err))
}
