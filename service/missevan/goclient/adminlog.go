package goclient

import (
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// AdminLogBox for batching add admin log
type AdminLogBox struct {
	adminLogs []adminlogger.AdminLog
	clientIP  string
	token     string
	equipID   string
	userID    int64
	url       string
}

// NewAdminLogBox initiate AdminLogBox instance
func NewAdminLogBox(c util.UserContext) *AdminLogBox {
	return &AdminLogBox{
		adminLogs: []adminlogger.AdminLog{},
		clientIP:  c.ClientIP(),
		token:     c.Token(),
		equipID:   c.EquipID(),
		userID:    c.UserID(),
		url:       c.Request().URL.Path,
	}
}

// NewRPCAdminLogBox initiate rpc AdminLogBox instance
func NewRPCAdminLogBox(c util.UserContext) *AdminLogBox {
	return &AdminLogBox{
		adminLogs: []adminlogger.AdminLog{},
		clientIP:  c.ClientIP(),
		token:     c.<PERSON>(),
		equipID:   c.EquipID(),
		url:       c.Request().URL.Path,
	}
}

// AdminLogOptions admin log options
type AdminLogOptions struct {
	ChannelID *int64
	UserID    *int64
}

// Add 添加 adminlog
func (a *AdminLogBox) Add(catalog int, intro string, options ...AdminLogOptions) {
	log := adminlogger.AdminLog{
		UserID:     a.userID,
		Catalog:    catalog,
		URL:        a.url,
		Intro:      intro,
		IP:         a.clientIP,
		CreateTime: util.TimeNow().Unix(),
	}
	if len(options) > 0 {
		opt := options[0]
		if opt.ChannelID != nil {
			log.ChannelID = *opt.ChannelID
		}
		if opt.UserID != nil {
			log.UserID = *opt.UserID
		}
	}
	a.adminLogs = append(a.adminLogs, log)
}

// Send 发送管理员操作日志
func (a *AdminLogBox) Send() (err error) {
	defer func() { a.adminLogs = []adminlogger.AdminLog{} }()
	if len(a.adminLogs) > 0 {
		err = service.MRPC.Call(uriGoAddAdminlog, a.clientIP, &a.adminLogs, nil,
			map[string]string{"token": a.token, "equip_id": a.equipID})
	}
	return
}
