package goclient

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

// FollowUserParams 关注接口的可选参数
type FollowUserParams struct {
	EventID     *string        `json:"event_id,omitempty"`      // 事件 ID
	EventIDFrom *string        `json:"event_id_from,omitempty"` // 事件来源 ID
	TrackID     *string        `json:"track_id,omitempty"`      // 跟踪 ID
	OS          *util.Platform `json:"os,omitempty"`            // 设备操作系统，由上游接口通过 equipment 信息获取后传入
	AutoFollow  bool           `json:"auto_follow,omitempty"`   // 是否系统自动关注。true 表示自动关注，false 表示手动关注
}

// FollowUser 用户关注
// 函数参数 followFrom 必须使用已定义的常量，常量定义的位置在 missevan-go/models/person/personfollow.go 文件中
// more 参数为可选的额外参数
func FollowUser(c mrpc.UserContext, userID, followID int64, followFrom int, more ...*FollowUserParams) error {
	input := map[string]interface{}{
		"user_id":        userID,
		"follow_user_id": followID,
		"follow_from":    followFrom,
	}

	// 如果提供了 more 参数，将其字段添加到 input 中
	if len(more) > 0 && more[0] != nil {
		params := more[0]
		if params.EventID != nil {
			input["event_id"] = *params.EventID
		}
		if params.EventIDFrom != nil {
			input["event_id_from"] = *params.EventIDFrom
		}
		if params.TrackID != nil {
			input["track_id"] = *params.TrackID
		}
		if params.OS != nil {
			input["os"] = *params.OS
		}
		input["auto_follow"] = params.AutoFollow
	}

	return service.MRPC.Do(c, uriFollow, input, nil)
}

// IsRPCUserError 是否为 RPC user code 错误码
func IsRPCUserError(err error) bool {
	rpcError, ok := err.(*mrpc.ClientError)
	if !ok {
		return false
	}
	switch rpcError.Code {
	case handler.CodeUserFollowLimit, handler.CodeBlockedUser, handler.CodeBlockedUserByOthers:
		return true
	}
	return false
}
