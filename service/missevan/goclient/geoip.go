package goclient

import (
	"net"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 支持的语言
const (
	LangEN   = "en"
	LangZHCN = "zh-cn"
)

// GetIPOptions 获取 IP 的参数
type GetIPOptions struct {
	Lang string // 语言
}

// IPInfo type
type IPInfo struct {
	CityName    string `json:"city_name"`
	RegionName  string `json:"region_name"`
	CountryCode string `json:"country_code"`
	CountryName string `json:"country_name"`
	ISP         string `json:"isp"`
}

var emptyIPInfo = IPInfo{}

// IsEmpty returns true if this IPInfo is empty
func (i IPInfo) IsEmpty() bool {
	return i == emptyIPInfo
}

// GetIPInfo 获取指定 IP 地址的相关信息
func GetIPInfo(userInfo util.UserContext, ip string, getIPOptions ...GetIPOptions) (info IPInfo, err error) {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return emptyIPInfo, serviceutil.ErrInvalidIP
	}
	// 某些 CDN 节点可能会收到小运营商给用户分配的私有 IP。
	// 这种情况下不需要调用 RPC，本地构造一个 CN 的 IPInfo 即可。
	if serviceutil.IsPrivateIP(parsedIP) {
		info := IPInfo{
			CountryCode: serviceutil.CountryCodeChina,
			CountryName: serviceutil.CountryNameChinaEN,
		}
		if len(getIPOptions) > 0 && getIPOptions[0].Lang == LangZHCN {
			info.CountryName = serviceutil.CountryNameChina
		}
		return info, nil
	}

	data := map[string]string{
		"ip": ip,
	}
	if len(getIPOptions) > 0 {
		data["lang"] = getIPOptions[0].Lang
	}

	userIP := ""
	if userInfo != nil {
		userIP = userInfo.ClientIP()
	}
	err = service.MRPC.Call("go://util/geoip", userIP, data, &info)
	return info, err
}
