package goclient

import (
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// CheckText 检测文本违规情况，text 中不能是空字符串
func CheckText(userInfo util.UserContext, text string, scene string, opts ...scan.CheckTextOption) (*scan.CheckResult, error) {
	var adFree bool
	for _, v := range opts {
		switch v {
		case scan.AdFreeOption:
			adFree = true
		}
	}

	input := userapi.TextScanTask{
		Text:    []string{text},
		Scene:   scene,
		UserID:  userInfo.UserID(),
		EquipID: userInfo.EquipID(),
		IP:      userInfo.ClientIP(),
		AdFree:  adFree,
	}
	var results []scan.CheckResult
	err := service.MRPC.Call(uriGoScanText, userInfo.ClientIP(), input, &results,
		map[string]string{"token": userInfo.Token(), "equip_id": userInfo.EquipID()})
	if err != nil {
		return nil, err
	}

	return &results[0], nil
}

// CheckTextFunc function type
type CheckTextFunc func(userInfo util.UserContext, text string, scene string, opts ...scan.CheckTextOption) (*scan.CheckResult, error)

// CheckTexts 批量检测文本违规情况
func CheckTexts(userInfo util.UserContext, text []string, scene string, opts ...scan.CheckTextOption) ([]*scan.CheckResult, bool, error) {
	var adFree bool
	for _, v := range opts {
		switch v {
		case scan.AdFreeOption:
			adFree = true
		}
	}

	input := userapi.TextScanTask{
		Text:   text,
		Scene:  scene,
		UserID: userInfo.UserID(),
		AdFree: adFree,
	}
	var results []*scan.CheckResult

	err := service.MRPC.Call(uriGoScanText, userInfo.ClientIP(), input, &results,
		map[string]string{"token": userInfo.Token(), "equip_id": userInfo.EquipID()})
	if err != nil {
		return nil, false, err
	}

	allPass := true
	for i := range results {
		if !results[i].Pass {
			allPass = false
			break
		}
	}
	return results, allPass, nil
}
