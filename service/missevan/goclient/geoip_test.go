package goclient_test

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestGetIPInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	privateIPs := []string{
		"********",
		"**********",
		"fc00::1",
		"fe80::1",
	}

	cancel := mrpc.SetMock("go://util/geoip", func(input interface{}) (output interface{}, err error) {
		params, ok := input.(map[string]string)
		require.True(ok)
		require.NotEmpty(params)
		// 确保私有 IP 不会进行实际的 RPC 调用
		require.NotContains(privateIPs, params["ip"])

		if params["lang"] == goclient.LangZHCN {
			return handler.M{
				"isp":          "中国电信",
				"city_name":    "上海",
				"region_name":  "上海",
				"country_code": "CN",
				"country_name": "中国",
			}, nil
		}

		return handler.M{
			"isp":          "ChinaTelecom",
			"city_name":    "Shanghai",
			"region_name":  "Shanghai",
			"country_code": "CN",
			"country_name": "China",
		}, nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodGet, "/path", false, nil)
	c.SetClientIP("************")
	info, err := goclient.GetIPInfo(c, c.ClientIP())
	require.NoError(err)
	assert.Equal("Shanghai", info.CityName)
	assert.Equal("Shanghai", info.RegionName)
	assert.Equal("CN", info.CountryCode)
	assert.Equal("China", info.CountryName)

	info, err = goclient.GetIPInfo(nil, c.ClientIP())
	require.NoError(err)
	assert.Equal("Shanghai", info.CityName)
	assert.Equal("Shanghai", info.RegionName)
	assert.Equal("CN", info.CountryCode)
	assert.Equal("China", info.CountryName)

	// 测试获取 IP 中文信息
	info, err = goclient.GetIPInfo(nil, c.ClientIP(), goclient.GetIPOptions{Lang: goclient.LangZHCN})
	require.NoError(err)
	assert.Equal("上海", info.CityName)
	assert.Equal("上海", info.RegionName)
	assert.Equal("CN", info.CountryCode)
	assert.Equal("中国", info.CountryName)

	// 测试私有 IP
	for _, ip := range privateIPs {
		info, err = goclient.GetIPInfo(nil, ip)
		require.NoError(err)
		assert.Equal("CN", info.CountryCode)
		assert.Equal("China", info.CountryName)

		info, err = goclient.GetIPInfo(nil, ip, goclient.GetIPOptions{Lang: goclient.LangZHCN})
		require.NoError(err)
		assert.Equal("CN", info.CountryCode)
		assert.Equal("中国", info.CountryName)
	}
}
