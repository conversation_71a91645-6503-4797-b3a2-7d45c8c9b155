package goclient

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(uriGoAddAdminlog, func(input any) (output any, err error) {
		called = true
		return "success", nil
	})
	defer cancel()
	c := handler.NewTestContext(http.MethodPost, "/123", true, nil)
	a := NewAdminLogBox(c)
	nowUnix := util.TimeNow().Unix()
	a.Add(adminlogger.CatalogSetUploadLog, "test", AdminLogOptions{
		ChannelID: util.NewInt64(123),
	})
	require.Len(a.adminLogs, 1)
	l := a.adminLogs[0]
	assert.Equal(int64(12), l.UserID)
	assert.Equal(adminlogger.CatalogSetUploadLog, l.Catalog)
	assert.Equal(int64(123), l.ChannelID)
	assert.Equal("/123", l.URL)
	assert.Equal("test", l.Intro)
	assert.Equal("", l.IP)
	assert.GreaterOrEqual(l.CreateTime, nowUnix)
	assert.NoError(a.Send())
	assert.True(called)
	assert.Len(a.adminLogs, 0)

	called = false
	a.Add(adminlogger.CatalogSetUploadLog, "test")
	require.Len(a.adminLogs, 1)
	l = a.adminLogs[0]
	assert.Equal(int64(12), l.UserID)
	assert.Equal(adminlogger.CatalogSetUploadLog, l.Catalog)
	assert.Equal("/123", l.URL)
	assert.Equal("test", l.Intro)
	assert.Equal("", l.IP)
	assert.GreaterOrEqual(l.CreateTime, nowUnix)
	assert.NoError(a.Send())
	assert.True(called)
	assert.Len(a.adminLogs, 0)
}
