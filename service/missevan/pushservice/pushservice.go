package pushservice

import (
	"html"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// Client implementation for pushservice
// doc: https://github.com/MiaoSiLa/mpush

// 推送的类型
// 参考文档：https://info.missevan.com/pages/viewpage.action?pageId=84728003
const (
	PushTypeOthers int = iota
	// PushTypeLiveOpen 推送类型为主播开播
	PushTypeLiveOpen
	// PushTypeDramaNewRelease 推送类型为剧集更新
	PushTypeDramaNewRelease
	// PushTypePowerSoundRelease 推送类型为启动音上线
	PushTypePowerSoundRelease
	// PushTypeBF 推送类型为男友系列的聊天
	PushTypeBF
	// PushTypeEvent 推送类型为运营活动
	PushTypeEvent
	// PushTypeRadioHypnosis 推送类型为催眠专享
	PushTypeRadioHypnosis
	// PushTypeMessagePM 推送类型为私信消息
	PushTypeMessagePM
	// PushTypeMessageComment 推送类型为评论消息
	PushTypeMessageComment
	// PushTypeMessageAt 推送类型为 @ 我消息
	PushTypeMessageAt
	// PushTypeMessageLike 推送类型为点赞消息
	PushTypeMessageLike
)

// 推送目标类型
const (
	TargetUsers            = "users"
	TargetUserFollowers    = "user_followers"
	TargetDramaSubscribers = "drama_subscribers"
	TargetDramaBuyers      = "drama_buyers"
	TargetWorkBuyers       = "work_buyers"
	TargetDevices          = "devices" // 推送目标类型是 devices 时推送源 source_ids 为设备 equip_id
	TargetBuvids           = "buvids"
)

// 推送策略规则
const (
	RuleDaily = "daily"
	RuleTask  = "task"
)

// 推送类型
const (
	TypeCommentLike = "comment_like"
	TypeComment     = "comment"
	TypeAt          = "at"
)

// Config for pushserivce
type Config mrpc.ConfigEntry

// Client structure for pushservice
type Client struct {
	conf *Config
	rpc  *mrpc.Client
}

// PushPolicy structure
type PushPolicy struct {
	Rule       string `json:"rule,omitempty"`
	Type       string `json:"Type,omitempty"`
	MaxNum     int    `json:"max_num,omitempty"`
	StartTime  int64  `json:"start_time,omitempty"`
	ExpireTime int64  `json:"expire_time,omitempty"`
}

// Push notification structure
type Push struct {
	TargetType string                 `json:"target_type,omitempty"`
	SourceIDs  []interface{}          `json:"source_ids,omitempty"`
	Message    string                 `json:"message,omitempty"`
	Title      string                 `json:"title,omitempty"`
	OpenURL    string                 `json:"open_url,omitempty"`
	PushType   int                    `json:"push_type,omitempty"`
	Policy     *PushPolicy            `json:"policy,omitempty"`
	Payload    map[string]interface{} `json:"payload,omitempty"`
}

// SMS notification structure
type SMS struct {
	To         string                 `json:"to,omitempty"`
	RegionCode int                    `json:"region_code,omitempty"`
	Scene      string                 `json:"scene,omitempty"`
	Payload    map[string]interface{} `json:"payload,omitempty"`
}

// Email notification structure
type Email struct {
	To       string                 `json:"to,omitempty"`
	Cc       string                 `json:"cc,omitempty"`
	Subject  string                 `json:"subject,omitempty"`
	Body     string                 `json:"body,omitempty"`
	Template string                 `json:"template,omitempty"`
	Payload  map[string]interface{} `json:"payload,omitempty"`
}

// BotChannel is the Bot channel type
type BotChannel string

// BotMessage is the Bot notification structure
type BotMessage struct {
	Message string     `json:"-"` // Deprecated: use content field and SendBotMessage
	Content string     `json:"content"`
	Channel BotChannel `json:"channel"`
	MsgType string     `json:"msgtype,omitempty"` // 为空时默认类型为 text
}

// SystemMsg is the System message structure
type SystemMsg struct {
	UserID   int64  `json:"user_id"`
	Title    string `json:"title"`
	Content  string `json:"content"`
	SendTime int64  `json:"send_time,omitempty"` // 秒级时间戳，不传为当前时间
}

// SystemMsgOptions is the System Message Options structure
type SystemMsgOptions struct {
	DisableHTMLEscape bool
}

// consts
const (
	Scheme = "pushservice"

	pushBatchSize = 100
)

// 机器人消息频道
const (
	BotChannelPMNotify   BotChannel = "pm_notify"
	BotChannelFansNotify            = "fans_notify"
)

// 机器人消息类型
const (
	BotMsgTypeText     = "text"
	BotMsgTypeMarkdown = "markdown"
)

// NewPushServiceClient returns the push service client
func NewPushServiceClient(conf *Config) (*Client, error) {
	rpcConfig := map[string]mrpc.ConfigEntry{
		Scheme: {
			URL: conf.URL,
			Key: conf.Key,
		},
	}
	rpc, err := mrpc.NewClient(rpcConfig)
	if err != nil {
		return nil, err
	}
	return &Client{
		conf: conf,
		rpc:  rpc,
	}, nil
}

// SendPush sends push to push server queue
func (c *Client) SendPush(n Push) error {
	return c.sendNotification("/api/push", map[string]interface{}{
		"notifications": []Push{n},
	})
}

// SendSMS sends sms to push server queue
func (c *Client) SendSMS(n SMS) error {
	return c.sendNotification("/api/sms", map[string]interface{}{
		"smses": []SMS{n},
	})
}

// SendSMSBatch sends sms list to push server queue
func (c *Client) SendSMSBatch(smses []SMS) error {
	var (
		lastErr  error
		batchSMS []SMS
	)

	msgCount := len(smses)
	for offset := 0; offset < msgCount; offset += pushBatchSize {
		leftBorder, rightBorder := offset, offset+pushBatchSize
		if rightBorder >= msgCount {
			batchSMS = smses[leftBorder:]
		} else {
			batchSMS = smses[leftBorder:rightBorder]
		}
		err := c.sendNotification("/api/sms", map[string]interface{}{
			"smses": batchSMS,
		})
		if err != nil {
			lastErr = err
			logger.Error(err)
			// PASS
		}
	}

	return lastErr
}

// SendEmail sends email to push server queue
func (c *Client) SendEmail(n Email) error {
	return c.sendNotification("/api/email", map[string]interface{}{
		"emails": []Email{n},
	})
}

// SendBot sends bot message to
// Deprecated: use SendBotMessage
func (c *Client) SendBot(n BotMessage) error {
	// 兼容 mpush 的 bot 消息
	type BotMsg struct {
		Message string     `json:"message"`
		Channel BotChannel `json:"channel"`
	}
	return c.sendNotification("/api/bot", map[string]interface{}{
		"bot": []BotMsg{{Message: n.Message, Channel: n.Channel}},
	})
}

// SendBotMessage send bot message
func (c *Client) SendBotMessage(botMsg BotMessage) error {
	return c.sendNotification("/api/im/send-bot", botMsg)
}

// SendSystemMsg sends system message to
// TODO: 后续等业务改造完成后，这里改成 DisableHTMLEscape: false
func (c *Client) SendSystemMsg(msgList []SystemMsg) error {
	return c.SendSystemMsgWithOptions(msgList, &SystemMsgOptions{DisableHTMLEscape: true})
}

// SendSystemMsgWithOptions sends system message with options to
func (c *Client) SendSystemMsgWithOptions(msgList []SystemMsg, opts *SystemMsgOptions) error {
	// 当 opts 是 nil 时，默认 HTML 转义 SystemMsg[].content
	if opts == nil {
		opts = &SystemMsgOptions{
			DisableHTMLEscape: false,
		}
	}

	var (
		lastErr  error
		batchMsg []SystemMsg
	)

	msgCount := len(msgList)
	for offset := 0; offset < msgCount; offset += pushBatchSize {
		leftBorder, rightBorder := offset, offset+pushBatchSize
		if rightBorder >= msgCount {
			batchMsg = msgList[leftBorder:]
		} else {
			batchMsg = msgList[leftBorder:rightBorder]
		}
		if !opts.DisableHTMLEscape {
			for i := range batchMsg {
				batchMsg[i].Content = html.EscapeString(batchMsg[i].Content)
			}
		}
		err := c.sendNotification("/api/systemmsg", map[string]interface{}{
			"systemmsgs": batchMsg,
		})
		if err != nil {
			lastErr = err
			logger.Error(err)
			// PASS
		}
	}

	return lastErr
}

// sendNotification sends notification to push server queue
func (c *Client) sendNotification(api string, data any) error {
	return c.rpc.Call(Scheme+":/"+api, "", data, nil)
}
