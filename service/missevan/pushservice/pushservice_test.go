package pushservice

import (
	"fmt"
	"html"
	"net"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	os.Exit(m.Run())
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, PushTypeOthers)
	assert.Equal(1, PushTypeLiveOpen)
	assert.Equal(2, PushTypeDramaNewRelease)
	assert.Equal(3, PushTypePowerSoundRelease)
	assert.Equal(4, PushTypeBF)
	assert.Equal(5, PushTypeEvent)
	assert.Equal(6, PushTypeRadioHypnosis)
	assert.Equal(7, PushTypeMessagePM)
	assert.Equal(8, PushTypeMessageComment)
	assert.Equal(9, PushTypeMessageAt)
	assert.Equal(10, PushTypeMessageLike)
}

func TestPushServiceSendPush(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	srv, err := NewPushServiceClient(&Config{
		URL: "http://127.0.0.1:8098/",
		Key: "testkey",
	})
	assert.NoError(err)
	err = srv.SendPush(Push{
		TargetType: "users",
		SourceIDs:  []interface{}{10},
		Message:    "这是一条测试推送",
		Title:      "测试",
		OpenURL:    "missevan://foo",
	})
	if _, ok := err.(net.Error); ok {
		t.SkipNow()
	}
	assert.NoError(err)
}

func TestPushServiceSendSMSBatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	counter := 0
	delMockFunc := mrpc.SetMock("pushservice://api/sms", func(input interface{}) (interface{}, error) {
		params, ok := input.(map[string]interface{})
		require.True(ok)
		assert.NotEmpty(params)
		item, ok := params["smses"]
		require.True(ok)
		require.NotNil(item)
		smses, ok := item.([]SMS)
		require.True(ok)
		assert.NotEmpty(smses)
		assert.True(len(smses) <= pushBatchSize)
		counter++
		return "success", nil
	})
	defer delMockFunc()

	smsCount := 123
	smsMsgList := make([]SMS, smsCount)
	for i := 0; i < smsCount; i++ {
		smsMsgList[i] = SMS{
			To:         "+8617775214817",
			RegionCode: 86,
			Scene:      "test_scene",
			Payload: map[string]interface{}{
				"code": "123456",
			},
		}
	}
	srv, err := NewPushServiceClient(&Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	})
	require.NoError(err)
	err = srv.SendSMSBatch(smsMsgList)
	require.NoError(err)
	assert.Equal(2, counter)
}

func TestPushServiceSendBot(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	cancel := mrpc.SetMock("pushservice://api/bot", func(i interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()
	srv, err := NewPushServiceClient(&Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	})
	assert.NoError(err)
	err = srv.SendBot(BotMessage{
		Message: "test message",
		Channel: BotChannelPMNotify,
	})
	if _, ok := err.(net.Error); ok {
		t.SkipNow()
	}
	assert.NoError(err)
}

func TestSendBotMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("pushservice://api/im/send-bot", func(i interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()
	srv, err := NewPushServiceClient(&Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	})
	require.NoError(err)
	err = srv.SendBotMessage(BotMessage{
		Message: "test message",
		Channel: BotChannelFansNotify,
		MsgType: BotMsgTypeText,
	})
	if _, ok := err.(net.Error); ok {
		t.SkipNow()
	}
	assert.NoError(err)
}

func TestPushServiceSendSystemMsg(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	srv, err := NewPushServiceClient(&Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	})
	assert.NoError(err)
	err = srv.SendSystemMsg([]SystemMsg{{
		UserID:  12,
		Title:   "测试系统通知标题",
		Content: "测试系统通知内容",
	}})
	assert.NoError(err)

	counter := 0
	cleanup := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (interface{}, error) {
		params, ok := input.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("input type error: %v", input)
		}
		item, ok := params["systemmsgs"]
		if !ok {
			return nil, fmt.Errorf("systemmsgs not exist: %v", input)
		}
		msgs, ok := item.([]SystemMsg)
		if !ok {
			return nil, fmt.Errorf("input type error: %v", item)
		}
		if len(msgs) > 500 {
			return nil, fmt.Errorf("Number of notifications(%d) over limit(500)", len(msgs))
		}
		counter++

		return "success", nil
	})
	defer cleanup()

	sysMsgCount := 555
	sysMsgList := make([]SystemMsg, sysMsgCount)
	for i := 0; i < sysMsgCount; i++ {
		sysMsgList[i] = SystemMsg{
			UserID:  int64(i + 1),
			Title:   "测试系统通知标题",
			Content: "测试系统通知内容",
		}
	}
	err = srv.SendSystemMsg(sysMsgList)
	require.NoError(err)
	expectCounter := sysMsgCount / pushBatchSize
	if sysMsgCount%pushBatchSize > 0 {
		expectCounter++
	}
	require.Equal(expectCounter, counter)
}

func TestSendSystemMsgWithOptions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (interface{}, error) {
		tutil.PrintJSON(input)
		return "success", nil
	})
	defer cleanup()

	srv, err := NewPushServiceClient(&Config{
		URL: "http://mpush.srv.maoer.co:8098/",
		Key: "testkey",
	})

	sysMsgList := make([]SystemMsg, 1)
	sysMsgList[0] = SystemMsg{
		UserID:   1,
		Title:    "测试系统通知标题",
		Content:  "<h1>测试系统通知内容</h1>",
		SendTime: util.TimeNow().Unix(),
	}
	sysMsgContent1 := sysMsgList[0].Content
	assert.NoError(err)
	err = srv.SendSystemMsgWithOptions(sysMsgList, &SystemMsgOptions{DisableHTMLEscape: true})
	require.NoError(err)
	assert.Equal(sysMsgContent1, sysMsgList[0].Content)
	assert.NotZero(sysMsgList[0].SendTime)

	sysMsgList2 := make([]SystemMsg, 1)
	sysMsgList2[0] = SystemMsg{
		UserID:  1,
		Title:   "测试系统通知标题",
		Content: "<h1>这是一级标题</h1>",
	}
	sysMsgContent2 := sysMsgList2[0].Content
	err = srv.SendSystemMsgWithOptions(sysMsgList2, nil)
	require.NoError(err)
	assert.Equal(html.EscapeString(sysMsgContent2), sysMsgList2[0].Content)
}
