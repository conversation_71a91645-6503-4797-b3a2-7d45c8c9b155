package sso

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

var conf = mrpc.Config{
	"sso": mrpc.ConfigEntry{
		URL: "http://missevan-sso.srv.maoer.co:3002/rpc/",
		Key: "testkey",
	},
}
var (
	testIP = "127.0.0.1"
	testUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75 Safari/537.36"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	os.Exit(m.Run())
}

func TestNewClient(t *testing.T) {
	t.Run("NewClient", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		confEntry := mrpc.ConfigEntry{URL: "http://test.srv.maoer.co:3002/rpc/"}
		conf := make(mrpc.Config)
		client, err := NewClient(conf)
		assert.Nil(client)
		assert.Error(err)
		conf["sso"] = confEntry
		c, err := NewClient(conf)
		assert.NoError(err)
		require.NotNil(c)
		_, ok := c.Config["sso"]
		assert.True(ok)
		conf["empty"] = confEntry
		assert.NotEqual(conf, c.Config)
	})
}

func TestCall(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(conf)
	require.NoError(err)
	err = c.Call(uriAuthlogin, testIP, "", "")
	assert.EqualError(err, "sso: 参数不合法")
}

func TestSSOClient(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	c, err := NewClient(conf)
	assert.NoError(err)
	u, err := c.LoginByThirdOpenID(ThirdTypeWeibo, "testweibouid", AgeForTwoHours, testIP,
		testUA, "")
	require.NoError(err)
	assert.NotEmpty(u.Token)
	assert.NotEmpty(u.User)
	assert.NotEmpty(u.User.Username)

	resp, err := c.Session(u.Token, testIP)
	require.NoError(err)
	assert.Equal(resp.User.UserID, u.User.UserID)

	err = c.Logout(u.Token)
	assert.NoError(err)

	_, err = c.RegistByMobile(12345689012, 86, u.User.Username, "testtest", testIP, 0, testUA, "")
	e := err.(*ClientError)
	assert.Equal(300020003, e.Code)
}

func TestLoginByMobile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(conf)
	require.NoError(err)

	// success
	_, err = c.LoginByMobile("000012345689012", 86, "testtest", AgeForOneDay, testIP, testUA, "")
	require.NoError(err)
	_, err = c.LoginByMobile("12345689012", 86, "testtest", AgeForOneDay, testIP, testUA, "")
	require.NoError(err)

	_, err = c.LoginByMobile("string", 86, "testtest", AgeForOneDay, testIP, testUA, "")
	e := err.(*ClientError)
	assert.Equal(201010002, e.Code)
	_, err = c.LoginByMobile("0", 86, "testtest", AgeForOneDay, testIP, testUA, "")
	require.NotNil(err)
	e = err.(*ClientError)
	assert.Equal(300010001, e.Code)
}

func TestFastLogin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(conf)
	require.NoError(err)
	input := FastLoginParams{
		Type:       TypeFastLogin,
		AuthToken:  "",
		MaxAgeType: AgeForTwoHours,
		UserAgent:  testUA,
	}
	_, err = c.FastLogin(uriFastlogin, testIP, input)
	assert.EqualError(err, "sso: 一键登录失败")

	input = FastLoginParams{
		Type:      TypeSMSLogin,
		Mobile:    12345678901,
		Region:    86,
		UserAgent: testUA,
	}
	res, err := c.FastLogin(uriFastlogin, testIP, input)
	require.NoError(err)
	assert.Equal("123******01", res.User.Mobile)
	assert.Equal(86, res.User.Region)

	input = FastLoginParams{
		Type:         TypeFastAuthBind,
		UverifyToken: "",
		AuthToken:    "",
		UserAgent:    testUA,
	}
	_, err = c.FastLogin(uriFastlogin, testIP, input)
	assert.EqualError(err, "sso: 一键登录失败")

	input = FastLoginParams{
		Type:      TypeSMSAuthBind,
		Mobile:    12345678901,
		Region:    86,
		AuthToken: "asdsds",
		UserAgent: testUA,
	}
	_, err = c.FastLogin(uriFastlogin, testIP, input)
	assert.EqualError(err, "sso: 一键登录失败")
}

func TestAuthLogin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(conf)
	require.NoError(err)
	var input = AuthLoginParams{
		AuthType:   AuthTypeBilibili,
		UID:        "testbilibiliuid",
		UserAgent:  testUA,
		MaxAgeType: AgeForTwoHours,
		AuthCode:   "asdsads",
	}
	res, err := c.AuthLogin(testIP, input)
	require.NoError(err)
	assert.Equal(int64(12), res.User.UserID)
	assert.Equal("零月", res.User.Username)
}

func TestUserInfo(t *testing.T) {
	assert := assert.New(t)

	c, err := NewClient(conf)
	assert.NoError(err)
	err = c.AdminUpdateUsername(12, "127.0.0.1", "零月")
	if err != nil {
		assert.Equal("sso: 该用户名已存在", err.Error())
	}
	acc, err := c.UserInfo(12)
	assert.NoError(err)
	assert.Equal(int64(12), acc.ID)
	assert.Equal(acc.Username, "零月")

	_, err = c.UserInfo(-10)
	assert.EqualError(err, "sso: 参数不可为空")
}

func TestListUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var userList = []int64{346286, 346287, 349524}
	c, err := NewClient(conf)
	require.NoError(err)
	res, err := c.ListUserInfo(userList)
	require.NoError(err)
	require.Len(res, 3)

	for _, user := range res {
		assert.True(util.HasElem(userList, user.ID))
	}
}

func TestAdminUpdateUsername(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(conf)
	require.NoError(err)
	now := util.TimeNow()
	err = c.AdminUpdateUsername(36, "127.0.0.1", fmt.Sprintf("sso_test_%d", now.Second()))
	assert.NoError(err)
	time.Sleep(500 * time.Millisecond)
	err = c.AdminUpdateUsername(36, "127.0.0.1", fmt.Sprintf("sso_test_%d", 100+now.Second()))
	assert.NoError(err)
}
