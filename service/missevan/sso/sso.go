// Package sso Client implement for missevan sso
// doc: https://apidoc.maoer.co/missevan-sso
// TODO: 后面删掉 body 里面的 equip_id
package sso

import (
	"errors"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const (
	uriRegister    = "sso://register"
	uriLogin       = "sso://login"
	uriLogout      = "sso://logout"
	uriSession     = "sso://session"
	uriGet         = "sso://get"
	uriList        = "sso://sso/list"
	uriAdminUpdate = "sso://admin/update"
	uriFastlogin   = "sso://sso/fastlogin"
	uriAuthlogin   = "sso://sso/authlogin"
)

// ThirdType
const (
	ThirdTypeQQ       = "qquid"
	ThirdTypeWeibo    = "weibouid"
	ThirdTypeWechat   = "weichatuid"
	ThirdTypeBilibili = "bilibiliuid"
)

// AuthType 第三方登录类型
type AuthType int

// AuthType
const (
	AuthTypeQQ AuthType = iota + 3
	AuthTypeWeiBo
	AuthTypeWechat
	AuthTypeBilibili
	AuthTypeApple
)

// FastLoginType 一键登录类型
type FastLoginType int

// FastLoginType
const (
	TypeFastLogin FastLoginType = iota
	TypeSMSLogin
	TypeFastAuthBind
	TypeSMSAuthBind
)

// Client structure for sso
type Client struct {
	*mrpc.Client
}

// ClientResponse structure for sso
type ClientResponse struct {
	ExpireAt int64  `json:"expire_at,omitempty"`
	LoginAt  int64  `json:"login_at,omitempty"`
	UpdateAt int64  `json:"update_at,omitempty"`
	Token    string `json:"token,omitempty"`
	User     User   `json:"user,omitempty"`
	IsNew    bool   `json:"is_new,omitempty"` // 如果是新号，这里返回 true，不是新号不返回
	// 第三方绑定时（type: 2, 3）返回此字段，其他情况不返回
	// type 2 第三方账号一键绑定手机号
	// type 3 第三方账号手机号验证码绑定手机号
	AuthInfo *AuthInfo `json:"auth_info,omitempty"`
}

// User structure for sso
type User struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	Mobile   string `json:"mobile,omitempty"`
	Region   int    `json:"region,omitempty"`
	Email    string `json:"email,omitempty"`
	IconURL  string `json:"iconurl,omitempty"`
	Confirm  int    `json:"confirm"`
}

// AuthInfo 第三方绑定时返回的字段
type AuthInfo struct {
	AuthType int    `json:"auth_type"`
	Username string `json:"username"`
}

// Account 用户账号信息
// NOTICE: 注意和 sso 保持同步
type Account struct {
	ID          int64  `json:"id"`
	QQUID       string `json:"qquid,omitempty"`
	WeiboUID    string `json:"weibouid,omitempty"`
	BilibiliUID string `json:"bilibiliuid,omitempty"`
	WechatUID   string `json:"wechatuid,omitempty"`
	Confirm     uint   `json:"confirm"`
	Username    string `json:"username"`
	Email       string `json:"email,omitempty"`
	CIP         string `json:"cip"`
	UIP         string `json:"uip"`
	CTime       int64  `json:"ctime"`
	UTime       int64  `json:"utime"`
	IconID      int    `json:"iconid"`
	IconURL     string `json:"iconurl"`
	IconColor   string `json:"iconcolor"`
	TeamID      int64  `json:"teamid"`
	TeamName    string `json:"teamname"`
	Subtitle    string `json:"subtitle"`
	Mobile      string `json:"mobile,omitempty"`
	Region      int    `json:"region,omitempty"`
}

// FastLoginParams 一键登录参数
type FastLoginParams struct {
	Type         FastLoginType `json:"type"`
	UverifyToken string        `json:"uverify_token,omitempty"`
	MaxAgeType   MaxAgeType    `json:"MaxAgeType,omitempty"`
	UserAgent    string        `json:"user_agent"`
	Mobile       int           `json:"mobile,omitempty"`
	Region       int           `json:"region,omitempty"`
	AuthToken    string        `json:"auth_token,omitempty"`
}

// AuthLoginParams 第三方账号登录参数
type AuthLoginParams struct {
	AuthType    AuthType   `json:"auth_type"`    // 登录类型。3: QQ, 4: 微博, 5: 微信, 6: Bilibili, 7: Apple
	AuthCode    string     `json:"auth_code"`    // 第三方账号 OAuth 2.0 授权码，可传可不传
	AccessToken string     `json:"access_token"` // 第三方账号 access_token, auth_code 没有的情况下，必须传
	UID         string     `json:"uid"`          // 第三方账号 ID, 后续 sso 支持第三方验证后后不传
	Opendid     string     `json:"opendid"`      // 第三方账号 openid
	Username    string     `json:"username"`     // 第三方账号用户名，后续 sso 支持第三方验证后后不传
	Iconurl     string     `json:"iconurl"`      // 第三方账号头像，后续 sso 支持第三方验证后后不传
	MaxAgeType  MaxAgeType `json:"maxAgeType"`   // Token 有效期（0：两小时、1：一天、2：一月、3：一年）
	UserAgent   string     `json:"user_agent"`   // 登录用户的 user agent
	EquipID     string     `json:"equip_id"`
}

// MaxAgeType for login session validity period
type MaxAgeType int

// max age types
const (
	AgeForTwoHours MaxAgeType = iota
	AgeForOneDay
	AgeForOneMonth
	AgeForOneYear
)

// ClientError for client error message
type ClientError struct {
	Code    int
	Message string
}

// Error return error message
func (e *ClientError) Error() string {
	return "sso: " + e.Message
}

// NewClient new client by mrpc.Config
func NewClient(conf mrpc.Config) (*Client, error) {
	c, ok := conf["sso"]
	if !ok {
		return nil, errors.New("sso config not found")
	}
	client, err := mrpc.NewClient(
		mrpc.Config{"sso": c})
	if err != nil {
		return nil, err
	}
	return &Client{
		Client: client,
	}, nil
}

// Call mrpc.ClientError 转换成 ClientError 的封装方法
// 基于 mrpc.Client.Call 进行封装
func (c *Client) Call(uri, ip string, input, output interface{}, cookies ...map[string]string) error {
	err := c.Client.Call(uri, ip, input, output, cookies...)
	if err != nil {
		if e, ok := err.(*mrpc.ClientError); ok {
			return &ClientError{Code: e.Code, Message: e.Message}
		}
		return err
	}
	return nil
}

// RegistByMobile 手机号注册
func (c *Client) RegistByMobile(mobile int64, region int, username, password, ip string, age MaxAgeType, userAgent, equipID string) (*ClientResponse, error) {
	res := new(ClientResponse)
	cookies := map[string]string{"equip_id": equipID}
	input := map[string]interface{}{
		"mobile":     mobile,
		"region":     region,
		"username":   username,
		"password":   password,
		"maxAgeType": age,
		"equip_id":   equipID,
		"user_agent": userAgent,
	}
	err := c.Call(uriRegister, ip, input, res, cookies)
	return res, err
}

// LoginByEmail login by user email address, equipID may be "" if it's from web browser
func (c *Client) LoginByEmail(email, password string, age MaxAgeType, ip, userAgent, equipID string) (*ClientResponse, error) {
	res := new(ClientResponse)
	cookies := map[string]string{"equip_id": equipID}
	input := map[string]interface{}{
		"email":      email,
		"password":   password,
		"maxAgeType": age,
		"user_agent": userAgent,
		"equip_id":   equipID,
	}
	err := c.Call(uriLogin, ip, input, res, cookies)
	return res, err
}

// LoginByMobile login by user mobile, equipID may be "" if it's from web browser
func (c *Client) LoginByMobile(mobile string, region int, password string, age MaxAgeType, ip, userAgent, equipID string) (*ClientResponse, error) {
	res := new(ClientResponse)
	cookies := map[string]string{"equip_id": equipID}
	// 去除海外手机号开头的 0
	parsedMobile, err := strconv.ParseInt(mobile, 10, 64)
	if err != nil {
		return nil, &ClientError{Code: 201010002, Message: "参数不合法"}
	}

	input := map[string]interface{}{
		"mobile":     parsedMobile,
		"region":     region,
		"password":   password,
		"maxAgeType": age,
		"user_agent": userAgent,
		"equip_id":   equipID,
	}
	err = c.Call(uriLogin, ip, input, res, cookies)
	return res, err
}

// LoginByThirdOpenID login by user third open id, equipID may be "" if it's from web browser
func (c *Client) LoginByThirdOpenID(thirdType, openID string, age MaxAgeType, ip, userAgent, equipID string) (*ClientResponse, error) {
	res := new(ClientResponse)
	cookies := map[string]string{"equip_id": equipID}
	input := map[string]interface{}{
		thirdType:    openID,
		"maxAgeType": age,
		"user_agent": userAgent,
		"equip_id":   equipID,
	}
	err := c.Call(uriLogin, ip, input, res, cookies)
	return res, err
}

// FastLogin 一键登录
func (c *Client) FastLogin(uri, ip string, input FastLoginParams) (*ClientResponse, error) {
	res := new(ClientResponse)
	err := c.Call(uri, ip, input, res)
	return res, err
}

// AuthLogin 第三方账号登录
func (c *Client) AuthLogin(ip string, input AuthLoginParams) (*ClientResponse,
	error) {
	res := new(ClientResponse)
	err := c.Call(uriAuthlogin, ip, input, res)
	return res, err
}

// Session acquire user info from token
func (c *Client) Session(token, ip string) (*ClientResponse, error) {
	res := new(ClientResponse)
	err := c.Call(uriSession, ip, struct{}{}, res, map[string]string{"token": token})
	return res, err
}

// Logout logout user and destory token
func (c *Client) Logout(token string) error {
	return c.Call(uriLogout, "", map[string]string{"token": token}, nil,
		map[string]string{"token": token})
}

// UserInfo 通过 id 查询用户信息
func (c *Client) UserInfo(userID int64) (*Account, error) {
	res := new(Account)
	err := c.Call(uriGet, "", map[string]int64{"user_id": userID}, res)
	return res, err
}

// ListUserInfo 根据用户 IDs 数组获取用户记录
func (c *Client) ListUserInfo(userIDs []int64) ([]*Account, error) {
	var res struct {
		Data []*Account `json:"data"`
	}
	err := c.Call(uriList, "", map[string][]int64{"user_ids": userIDs},
		&res)
	return res.Data, err
}

// AdminUpdateUsername 管理员更新某一个用户的用户名
func (c *Client) AdminUpdateUsername(userID int64, ip, username string) error {
	return c.Call(uriAdminUpdate, "",
		map[string]interface{}{
			"user_id": userID,
			"ip":      ip,
			"update": map[string]interface{}{
				"username": username,
			}}, nil)
}
