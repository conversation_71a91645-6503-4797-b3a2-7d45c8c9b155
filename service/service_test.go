package service

import (
	"testing"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{},
		"db", "message_db", "log_db", "drama_db", "voice_db", "main_db", "pay_db",
		"redis", "lru_redis",
		"databus",
		"mrpc", "pushservice", "gaia", "govern", "short_url",
		"storage", "upload", "antispam", "antispamv2", "geetest", "opensearch",
		"ipip", "captcha", "lancer", "smartsheet",
	)
}
