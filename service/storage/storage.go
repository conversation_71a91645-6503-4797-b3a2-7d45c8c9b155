package storage

import (
	"errors"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
	"github.com/MiaoSiLa/missevan-go/service/storage/oss"
)

// storage type
const (
	TypeOSS = "oss"
)

// Config for storage config
type Config map[string]entryconfig.Config

// Storage interface
type Storage interface {
	// TODO: 支持 ...option 配置 Cache 及过期时间等参数
	PutObject(path string, reader io.Reader) error
	PutObjectFromFile(path, localFile string) error
	GetObject(objectKey string) (io.ReadCloser, error)
	GetObjectToFile(objectKey, filePath string) error
	DeleteObject(path string) error
	GetPublicURL(path string) string // CDN 签名
	SignatureURI(uri string, duration time.Duration) string
}

// Client structure for storage bucket
type Client struct {
	conf    Config
	buckets map[string]Storage
}

// errors
var (
	ErrConfigEntryNotFound = errors.New("storage config entry not found")
)

// NewClient return the storage client
func NewClient(conf Config) *Client {
	return &Client{
		conf:    conf,
		buckets: make(map[string]Storage, len(conf)),
	}
}

// NewBucket return the storage Bucket
func NewBucket(entryConf entryconfig.Config) (Storage, error) {
	switch entryConf.Type {
	case TypeOSS:
		return oss.NewBucket(entryConf)
	default:
		panic("invalid storage type")
	}
}

// Get returns the specified oss bucket
func (c *Client) Get(entry string) (Storage, error) {
	// TODO: rw lock
	bucket, ok := c.buckets[entry]
	if ok {
		return bucket, nil
	}
	entryConf, ok := c.conf[entry]
	if !ok {
		return nil, ErrConfigEntryNotFound
	}
	bucket, err := NewBucket(entryConf)
	if err != nil {
		return nil, err
	}
	c.buckets[entry] = bucket
	return bucket, nil
}

// Parse scheme URL into http URL.
// It will throw exception directly when schemeURL is wrong format, or entry is not configured.
func (c *Client) Parse(schemeURL string) (httpURL string) {
	items := strings.SplitN(schemeURL, "://", 2)
	if 2 != len(items) || "" == items[1] {
		panic("Wrong scheme url: " + schemeURL)
	}
	entry := items[0]
	filePath := items[1]

	bucket, err := c.Get(entry)
	if err != nil {
		panic(err)
	}
	// CDN 的 URL 签名处理
	return bucket.GetPublicURL(filePath)
}

// Format http URL into scheme URL.
// 没有找到对应的则返回原结果
func (c *Client) Format(httpURL string) (string, bool) {
	u, err := url.Parse(httpURL)
	if err != nil {
		logger.Debug(err)
		return httpURL, false
	}
	// REVIEW: 目前 query 都是业务层加上去的，直接置空
	u.RawQuery = ""
	// https -> http
	if u.Scheme == "https" {
		u.Scheme = "http"
	}
	schemeURL := u.String()
	for entry, conf := range c.conf {
		entryURLs := make([]string, 0, 1+len(conf.PublicURLs))
		if conf.PublicURL != "" {
			entryURLs = append(entryURLs, strings.Replace(conf.PublicURL, "https://", "http://", 1))
		}
		for _, pc := range conf.PublicURLs {
			entryURLs = append(entryURLs, strings.Replace(pc.URL, "https://", "http://", 1))
		}
		for _, entryURL := range entryURLs {
			if strings.HasPrefix(schemeURL, entryURL) {
				return strings.Replace(schemeURL, entryURL, entry+"://", 1), true
			}
		}
	}
	return httpURL, false
}

// UploadFile upload local file to remote path in specified bucket
func (c *Client) UploadFile(entry, path, localFile string) (schemeURL string, err error) {
	bucket, err := c.Get(entry)
	if nil != err {
		return "", err
	}
	err = bucket.PutObjectFromFile(path, localFile)
	if nil != err {
		return "", err
	}
	return entry + "://" + path, err
}

// Upload upload data reading from io.Reader
// eg. Upload("test", "test/201812/20/foo.txt", strings.NewReader("content string"))
//     Upload("test", "test/201812/20/foo.txt", bytes.NewReader([]byte("content string")))
//     fd, err := os.Open("./bar.txt")
//     Upload("test", "test/201812/20/foo.txt", fd)
func (c *Client) Upload(entry, path string, reader io.Reader) (schemeURL string, err error) {
	bucket, err := c.Get(entry)
	if nil != err {
		return "", err
	}

	err = bucket.PutObject(path, reader)
	if nil != err {
		return "", err
	}

	return entry + "://" + path, err
}

// Delete delete file from storage
func (c *Client) Delete(schemeURL string) (err error) {
	items := strings.SplitN(schemeURL, "://", 2)
	if 2 != len(items) {
		return errors.New("Wrong scheme url: " + schemeURL)
	}
	entry := items[0]
	path := items[1]

	bucket, err := c.Get(entry)
	if nil != err {
		return err
	}
	err = bucket.DeleteObject(path)
	if nil != err {
		return err
	}
	return nil
}
