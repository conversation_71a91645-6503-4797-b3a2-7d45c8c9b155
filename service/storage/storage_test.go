package storage_test

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
)

func TestMain(t *testing.M) {
	service.InitTestService()
	logger.InitTestLog()

	conf := storage.Config{
		"test": {
			Type:            "oss",
			AccessKeyID:     "LTAIsNW7Hxzgnxu2",
			AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
			Bucket:          "missevan-test",
			Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
			PublicURLs: []entryconfig.SectionPublicURL{
				{
					URL:    "http://static-test.missevan.com/",
					Weight: 1,
				},
			},
		},
	}
	stdStorageClient = storage.NewStdClient(conf)

	os.Exit(t.Run())
}

func TestUploadFile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	schemeURL, err := service.Storage.UploadFile("test", "test/201812/17/test_readme.md", "../../README.md")
	require.Nil(err)
	assert.Contains(schemeURL, "test://test/201812/17/test_readme.md")
}

func TestUpload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	schemeURL, err := service.Storage.Upload("test", "test/201812/17/foo.txt", strings.NewReader("test content"))
	require.Nil(err)
	assert.Contains(schemeURL, "test://test/201812/17/foo.txt")

	httpURL := service.Storage.Parse("test://test/201812/17/foo.txt")
	resp, err := http.Get(httpURL)
	assert.Nil(err)
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	assert.Nil(err)
	assert.Equal(resp.StatusCode, http.StatusOK)
	assert.Equal(string(body), "test content")
}

func TestParse(t *testing.T) {
	assert := assert.New(t)

	httpURL := service.Storage.Parse("test://test/201812/17/test_readme.md")

	assert.Contains(httpURL, "http")
	assert.NotContains(httpURL, "auth_key")

	testClient := storage.NewClient(storage.Config{
		"test2": entryconfig.Config{
			Type:            "oss",
			AccessKeyID:     "LTAIsNW7Hxzgnxu2",
			AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
			Bucket:          "missevan-test",
			Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
			PublicURL:       "http://static-test.missevan.com/",
			PrivateKey:      "testkey",
		},
	})
	httpURL = testClient.Parse("test2://test/201812/17/test_readme.md")
	assert.Contains(httpURL, "http")
	assert.Contains(httpURL, "auth_key")

	defer func() {
		err := recover()
		assert.Equal("Wrong scheme url: test2://", fmt.Sprint(err))
	}()
	testClient.Parse("test2://")
}

func TestFormat(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := storage.Config{
		"aaa": entryconfig.Config{
			PublicURL: "https://aaa/",
		},
		"bbb": entryconfig.Config{PublicURL: "http://bbb/"},
		"ddd": entryconfig.Config{PublicURL: ""},
		"ccc": entryconfig.Config{
			PublicURLs: []entryconfig.SectionPublicURL{
				{
					URL: "https://ccc/",
				},
				{
					URL: "https://test-ccc/",
				},
			},
		},
	}
	c := storage.NewClient(conf)

	schemeURL, ok := c.Format("http://aaa/123")
	require.True(ok)
	assert.Equal("aaa://123", schemeURL)

	schemeURL, ok = c.Format("https://testaaa/1234\n")
	require.False(ok)
	assert.Equal("https://testaaa/1234\n", schemeURL)

	schemeURL, ok = c.Format("https://bbb/123456?test=128561")
	require.True(ok)
	assert.Equal("bbb://123456", schemeURL)

	schemeURL, ok = c.Format("https://ddd")
	require.False(ok)
	assert.Equal("https://ddd", schemeURL)

	schemeURL, ok = c.Format("https://ccc/test")
	require.True(ok)
	assert.Equal("ccc://test", schemeURL)

	schemeURL, ok = c.Format("https://test-ccc/test")
	require.True(ok)
	assert.Equal("ccc://test", schemeURL)
}

func TestDelete(t *testing.T) {
	assert := assert.New(t)

	err := service.Storage.Delete("test://test/201812/17/test_readme.md")
	assert.Nil(err)
	err = service.Storage.Delete("test://test/201812/17/foo.txt")
	assert.Nil(err)
}
