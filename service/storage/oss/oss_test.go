package oss

import (
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
)

var testConfig = entryconfig.Config{
	Type:            "oss",
	AccessKeyID:     "LTAIsNW7Hxzgnxu2",
	AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
	Bucket:          "missevan-test",
	Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
	PublicURLs: []entryconfig.SectionPublicURL{
		{
			URL:    "http://static-test.missevan.com/",
			Weight: 1,
		},
	},
	PrivateKey: "testkey",
}

var testBucket, _ = NewBucket(testConfig)

func TestGetObjectToFile(t *testing.T) {
	require := require.New(t)
	_ = os.Remove("./README.md")
	err := testBucket.GetObjectToFile("test/201912/17/test_readme.md", "./README.md")
	require.NoError(err)
	// 文件成功下载到本地后删除
	err = os.Remove("./README.md")
	if err != nil {
		t.Logf("remove file error: %v", err)
	}
}

func TestPutObjectFromFile(t *testing.T) {
	require := require.New(t)

	err := testBucket.PutObjectFromFile("test/201912/17/test_readme.md", "../../README.md")
	require.Nil(err)
}

func TestPutObject(t *testing.T) {
	require := require.New(t)

	err := testBucket.PutObject("test/201912/17/foo.txt", strings.NewReader("test content"))
	require.Nil(err)
}

func TestSignatureURI(t *testing.T) {
	assert := assert.New(t)

	signatureURI := testBucket.SignatureURI("test/201912/17/foo.txt", time.Minute)
	assert.Contains(signatureURI, "OSSAccessKeyId=")
}

func TestDelete(t *testing.T) {
	assert := assert.New(t)

	err := testBucket.DeleteObject("test://test/201912/17/test_readme.md")
	assert.Nil(err)
	err = testBucket.DeleteObject("test://test/201912/17/foo.txt")
	assert.Nil(err)
}

func TestGetPublicURL(t *testing.T) {
	assert := assert.New(t)

	filePath := "test/201812/17/test_readme.md"
	httpURL := testBucket.GetPublicURL(filePath)
	assert.Contains(httpURL, testConfig.PublicURLs[0].URL)
	assert.Contains(httpURL, "auth_key")

	testConfig2 := testConfig
	testConfig2.PublicURL = "-"
	testConfig2.PublicURLs = nil
	testConfig2.PrivateKey = ""
	testBucket2, _ := NewBucket(testConfig2)
	assert.Equal("", testBucket2.GetPublicURL(filePath))

	testConfig2.PublicURL = ""
	testBucket2, _ = NewBucket(testConfig2)
	assert.Contains(testBucket2.GetPublicURL(filePath), "OSSAccessKeyId=")
}

func TestRandomPublicURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := entryconfig.Config{
		AccessKeyID:     testConfig.AccessKeyID,
		AccessKeySecret: testConfig.AccessKeySecret,
		Endpoint:        testConfig.Endpoint,
		PublicURL:       "test",
	}
	b, err := NewBucket(conf)
	require.NoError(err)
	assert.Equal("test", b.randomPublicURL())

	conf = entryconfig.Config{
		AccessKeyID:     testConfig.AccessKeyID,
		AccessKeySecret: testConfig.AccessKeySecret,
		Endpoint:        testConfig.Endpoint,
		PublicURLs: []entryconfig.SectionPublicURL{
			{
				URL: "test",
			},
		},
	}
	b, err = NewBucket(conf)
	require.NoError(err)
	assert.Empty(b.randomPublicURL())

	conf = entryconfig.Config{
		AccessKeyID:     testConfig.AccessKeyID,
		AccessKeySecret: testConfig.AccessKeySecret,
		Endpoint:        testConfig.Endpoint,
		PublicURLs: []entryconfig.SectionPublicURL{
			{
				URL: "test",
			},
		},
	}
	b, err = NewBucket(conf)
	require.NoError(err)
	assert.Empty(b.randomPublicURL())

	conf = entryconfig.Config{
		AccessKeyID:     testConfig.AccessKeyID,
		AccessKeySecret: testConfig.AccessKeySecret,
		Endpoint:        testConfig.Endpoint,
		PublicURLs: []entryconfig.SectionPublicURL{
			{
				URL:    "test1",
				Weight: 1,
			},
			{
				URL:    "test2",
				Weight: 0,
			},
		},
	}
	b, err = NewBucket(conf)
	require.NoError(err)
	assert.Equal("test1", b.randomPublicURL())

	// 测试随机
	conf = entryconfig.Config{
		AccessKeyID:     testConfig.AccessKeyID,
		AccessKeySecret: testConfig.AccessKeySecret,
		Endpoint:        testConfig.Endpoint,
		PublicURLs: []entryconfig.SectionPublicURL{
			{
				URL:    "test1",
				Weight: 1,
			},
			{
				URL:    "test2",
				Weight: 1,
			},
		},
	}
	b, err = NewBucket(conf)
	require.NoError(err)
	var flag int
	for i := 0; i < 100; i++ {
		v := b.randomPublicURL()
		if v == "test1" {
			flag |= 1
		}
		if v == "test2" {
			flag |= 2
		}
		if flag == 3 {
			break
		}
	}
	assert.Equal(3, flag)
}
