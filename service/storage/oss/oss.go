package oss

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"

	aliyunoss "github.com/aliyun/aliyun-oss-go-sdk/oss"

	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Bucket oss bucket
type Bucket struct {
	bucket *aliyunoss.Bucket
	entryconfig.Config

	publicURLDistribution util.Distribution
}

// PutObjectFromFile creates a new object from the local file.
func (b Bucket) PutObjectFromFile(path, localFile string) error {
	return b.bucket.PutObjectFromFile(path, localFile)
}

// GetObjectToFile downloads the data to a local file.
func (b Bucket) GetObjectToFile(objectKey, filePath string) error {
	return b.bucket.GetObjectToFile(objectKey, filePath)
}

// PutObject creates a new object and it will overwrite the original one if it exists already.
func (b Bucket) PutObject(path string, reader io.Reader) error {
	return b.bucket.PutObject(path, reader)
}

// GetObject downloads the object.
func (b Bucket) GetObject(objectKey string) (io.ReadCloser, error) {
	return b.bucket.GetObject(objectKey)
}

// DeleteObject delete the object
func (b Bucket) DeleteObject(path string) error {
	return b.bucket.DeleteObject(path)
}

// SignatureURI signs the URL
func (b Bucket) SignatureURI(uri string, duration time.Duration) string {
	t := util.TimeNow().Add(duration).Unix()
	strToSign := fmt.Sprintf("GET\n\n\n%d\n/%s/%s", t, b.Bucket, uri)

	mac := hmac.New(sha1.New, []byte(b.Config.AccessKeySecret))
	_, _ = mac.Write([]byte(strToSign))
	sign := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	query := url.Values{}
	query.Set("OSSAccessKeyId", b.Config.AccessKeyID)
	query.Set("Expires", strconv.FormatInt(t, 10))
	query.Set("Signature", sign)
	return "https://" + b.Config.Bucket + "." +
		strings.Replace(b.Config.Endpoint, "-internal.", ".", 1) +
		"/" + uri + "?" + query.Encode()
}

// GetPublicURL get signature url with auth key
// 阿里云 CDN 的 URL 签名规则
func (b Bucket) GetPublicURL(filePath string) string {
	url := b.randomPublicURL()
	if url == "-" {
		// 禁用 public url
		return ""
	}
	if b.PrivateKey == "" && url == "" {
		// 无 private key 且没有配置 public url 的情况下，默认使用 oss 的签名规则
		// TODO: 配置链接有效期
		return b.SignatureURI(filePath, time.Hour*2)
	}
	httpURL := url + filePath
	if b.PrivateKey == "" || strings.HasSuffix(filePath, "/") {
		return httpURL
	}
	uri := "/" + filePath
	timestamp := util.TimeNow().Unix()
	uid := 0
	randStr := util.GetRandomString(8)
	h := md5.New()
	_, _ = h.Write([]byte(fmt.Sprintf("%s-%d-%s-%d-%s", uri, timestamp, randStr, uid, b.PrivateKey)))
	md5Str := hex.EncodeToString(h.Sum(nil))
	return httpURL + fmt.Sprintf("?auth_key=%d-%s-%d-%s", timestamp, randStr, uid, md5Str)
}

// NewBucket new oss bucket
func NewBucket(conf entryconfig.Config) (*Bucket, error) {
	ossClient, err := aliyunoss.New(conf.Endpoint, conf.AccessKeyID, conf.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	bucket, err := ossClient.Bucket(conf.Bucket)
	if err != nil {
		return nil, err
	}
	b := &Bucket{bucket: bucket, Config: conf}
	if len(conf.PublicURLs) != 0 {
		hasPublicURL := false
		weights := make([]int, len(conf.PublicURLs))
		for i := range conf.PublicURLs {
			if conf.PublicURLs[i].Weight <= 0 {
				continue
			}
			hasPublicURL = true
			weights[i] = conf.PublicURLs[i].Weight
		}
		if hasPublicURL {
			b.publicURLDistribution, err = util.NewDiscreteDistribution(weights,
				util.NewLockedSource(util.TimeNow().UnixNano()), false)
			if err != nil {
				return nil, err
			}
		}
	}
	return b, nil
}

func (b *Bucket) randomPublicURL() string {
	if b.PublicURL != "" {
		// 优先使用 PublicURL
		return b.PublicURL
	}
	if b.publicURLDistribution != nil {
		return b.PublicURLs[b.publicURLDistribution.NextInt()].URL
	}
	return ""
}
