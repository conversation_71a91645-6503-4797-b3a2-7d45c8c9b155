package entryconfig

// Config is the entry of storage config
type Config struct {
	Type            string             `yaml:"type"`
	AccessKeyID     string             `yaml:"access_key_id"`
	AccessKeySecret string             `yaml:"access_key_secret"`
	Bucket          string             `yaml:"bucket"`
	Endpoint        string             `yaml:"endpoint"`
	PublicURL       string             `yaml:"public_url"`
	PublicURLs      []SectionPublicURL `yaml:"public_urls"`
	PrivateKey      string             `yaml:"private_key"`
}

// SectionPublicURL 公共 url 配置
type SectionPublicURL struct {
	URL    string `yaml:"url"`
	Weight int    `yaml:"weight"` // parse 权重
}
