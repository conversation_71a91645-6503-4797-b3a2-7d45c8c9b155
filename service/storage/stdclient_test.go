package storage_test

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/storage"
)

var stdStorageClient *storage.StdClient

func TestStdClient_Parse(t *testing.T) {
	assert := assert.New(t)

	httpURL := stdStorageClient.Parse("oss://missevan-test/test/201812/17/test_readme.md")

	assert.Contains(httpURL, "http")
	assert.NotContains(httpURL, "auth_key")

	assert.Panics(func() {
		stdStorageClient.Parse("abc")
	})
}

func TestStdClient_Upload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	schemeURL, err := stdStorageClient.Upload("test", "test/201812/17/foo.txt", strings.NewReader("test content"))
	require.Nil(err)
	assert.Equal(schemeURL, "oss://missevan-test/test/201812/17/foo.txt")

	httpURL := stdStorageClient.Parse(schemeURL)
	resp, err := http.Get(httpURL)
	assert.Nil(err)
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	assert.Nil(err)
	assert.Equal(resp.StatusCode, http.StatusOK)
	assert.Equal(string(body), "test content")
}

func TestStdClient_UploadFile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	schemeURL, err := stdStorageClient.UploadFile("test", "test/201812/17/test_readme.md", "../../README.md")
	require.Nil(err)
	assert.Equal(schemeURL, "oss://missevan-test/test/201812/17/test_readme.md")
}
