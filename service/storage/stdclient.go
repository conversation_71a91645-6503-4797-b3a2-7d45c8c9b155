package storage

import (
	"errors"
	"io"
	"net/url"
	"sync"

	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
)

// StdClient .
type StdClient struct {
	conf    Config
	lock    *sync.RWMutex
	entries map[string]Storage
}

// NewStdClient return the storage client
func NewStdClient(conf Config) *StdClient {
	return &StdClient{
		conf:    conf,
		lock:    new(sync.RWMutex),
		entries: make(map[string]Storage, len(conf)),
	}
}

// errors
var (
	ErrSchemeUnsupport = errors.New("storage scheme unsupport")
)

func (c *StdClient) getEntryConfig(entry string) (*entryconfig.Config, error) {
	if ec, ok := c.conf[entry]; ok {
		return &ec, nil
	}
	return nil, nil
}

func (c *StdClient) findEntryConfig(u *url.URL) (entry string, ec *entryconfig.Config, err error) {
	for entry, e := range c.conf {
		if e.Type == u.Scheme && e.Bucket == u.Host {
			ec2 := e
			return entry, &ec2, nil
		}
	}
	return "", nil, ErrConfigEntryNotFound
}

func (c *StdClient) getEntryStorage(entry string, ec *entryconfig.Config) (Storage, error) {
	c.lock.RLock()
	if s, ok := c.entries[entry]; ok {
		c.lock.RUnlock()
		return s, nil
	}
	c.lock.RUnlock()
	switch ec.Type {
	case TypeOSS:
		s, err := NewBucket(*ec)
		if err != nil {
			return nil, err
		}
		c.lock.Lock()
		c.entries[entry] = s
		c.lock.Unlock()
		return s, nil
	default:
		return nil, ErrSchemeUnsupport
	}
}

// Parse scheme URL into http URL.
// It will throw exception directly when schemeURL is wrong format, or entry is not configured.
func (c *StdClient) Parse(schemeURL string) (httpURL string) {
	u, err := url.Parse(schemeURL)
	if err != nil {
		panic(err)
	}

	entry, ec, err := c.findEntryConfig(u)
	if err != nil {
		panic(err)
	}
	s, err := c.getEntryStorage(entry, ec)
	if err != nil {
		panic(err)
	}

	// CDN 的 URL 签名处理
	return s.GetPublicURL(u.Path)
}

func (c *StdClient) getUploadURL(ec *entryconfig.Config, path string) string {
	return ec.Type + "://" + ec.Bucket + "/" + path
}

// Upload upload data reading from io.Reader
//
// eg. Upload("test", "201812/20/foo.txt", strings.NewReader("content string"))
//
//	returns "oss://missevan-test/201812/20/foo.txt"
func (c *StdClient) Upload(entry string, path string, reader io.Reader) (schemeURL string, err error) {
	ec, err := c.getEntryConfig(entry)
	if err != nil {
		return "", err
	}
	s, err := c.getEntryStorage(entry, ec)
	if nil != err {
		return "", err
	}

	err = s.PutObject(path, reader)
	if nil != err {
		return "", err
	}
	return c.getUploadURL(ec, path), nil
}

// UploadFile upload local file to remote path in specified bucket
func (c *StdClient) UploadFile(entry, path, localFile string) (schemeURL string, err error) {
	ec, err := c.getEntryConfig(entry)
	if err != nil {
		return "", err
	}
	s, err := c.getEntryStorage(entry, ec)
	if nil != err {
		return "", err
	}

	err = s.PutObjectFromFile(path, localFile)
	if nil != err {
		return "", err
	}
	return c.getUploadURL(ec, path), nil
}
