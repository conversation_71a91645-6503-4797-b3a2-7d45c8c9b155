package smartsheet

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// SuccessCode request smartsheet api success code
const SuccessCode = 0

// uri
const (
	// APISheetCreate create sheet
	APISheetCreate = "/api/smartsheet/create"
	// APISheetAddRecord add sheet record
	APISheetAddRecord = "/api/smartsheet/add-record"
)

// Config smartsheet config
type Config struct {
	URL string `yaml:"url"`
}

// Client api client
type Client struct {
	Config
	c *http.Client
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: &http.Client{
			Timeout: 5 * time.Second,
		},
	}
	return c
}

// Request smartsheet api request
func (c *Client) request(req *http.Request, response interface{}, raw bool) error {
	logger.Debugf("%s %s", req.Method, req.URL.String())
	resp, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\nHTTP %s\n%s", req.Method, req.URL.String(), resp.Status, body)
		}
		return &serviceutil.APIError{
			Status:  resp.StatusCode,
			Message: string(body),
		}
	}

	if raw {
		// 返回原始数据
		err = json.Unmarshal(body, response)
		if err != nil {
			return &serviceutil.APIError{
				Status:  http.StatusBadRequest,
				Message: fmt.Sprintf("smartsheet: raw data unmarshal failed: %v", err)}
		}
		return nil
	}

	var ssResp Resp
	err = json.Unmarshal(body, &ssResp)
	if err != nil {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("smartsheet: data unmarshal failed: %v", err)}
	}
	if ssResp.Code != SuccessCode {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("smartsheet: (%d) - %s", ssResp.Code, ssResp.Message)}
	}
	if response != nil {
		err = json.Unmarshal(ssResp.Data, response)
		if err != nil {
			return &serviceutil.APIError{
				Status:  http.StatusBadRequest,
				Message: fmt.Sprintf("smartsheet: (%d) - %v", ssResp.Code, err)}
		}
	}
	return nil
}

// Resp request smartsheet api response
type Resp struct {
	Code    int64           `json:"code"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// SheetAddRecordParam add sheet record param
type SheetAddRecordParam struct {
	Channel string   `json:"channel"`
	Fields  []string `json:"fields"`
	Values  []string `json:"values"`
}

// AddRecord add sheet record
func (c *Client) AddRecord(param *SheetAddRecordParam) error {
	data, err := json.Marshal(param)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPost, c.URL+APISheetAddRecord, bytes.NewReader(data))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", serviceutil.UserAgent)
	return c.request(req, nil, false)
}

// SheetCreateParam create sheet param
type SheetCreateParam struct {
	DocName    string   `json:"doc_name"`
	AdminUsers []string `json:"admin_users"`
}

// SheetCreateRespData create sheet response data
type SheetCreateRespData struct {
	DocID string `json:"doc_id"`
	URL   string `json:"url"`
}

// CreateSheet create sheet
func (c *Client) CreateSheet(param *SheetCreateParam) (*SheetCreateRespData, error) {
	data, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, c.URL+APISheetCreate, bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	respData := new(SheetCreateRespData)
	err = c.request(req, respData, false)
	if err != nil {
		return nil, err
	}
	return respData, nil
}
