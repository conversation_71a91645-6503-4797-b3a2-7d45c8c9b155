//go:build !release
// +build !release

package smartsheet

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	responseData interface{}
	responseCode int64
	once         sync.Once

	testConfig Config
)

func startMockSmartsheetServer() {
	r := gin.New()
	r.POST(APISheetAddRecord, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    responseCode,
			"message": "",
			"data":    responseData,
		})
	})
	r.POST(APISheetCreate, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    responseCode,
			"message": "",
			"data":    responseData,
		})
	})

	addr := tutil.RunMockServer(r, 0)
	testConfig = Config{
		URL: fmt.Sprintf("http://%s", addr),
	}
}

// TestConfig 测试配置
func TestConfig() Config {
	once.Do(startMockSmartsheetServer)
	return testConfig
}

// SetMockResult 设置 mock 后的结果
func SetMockResult(uri string, code int64, data interface{}) {
	switch uri {
	case APISheetAddRecord:
		responseCode = code
		responseData = data
	case APISheetCreate:
		responseCode = code
		responseData = data
	}
}
