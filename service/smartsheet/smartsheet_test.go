package smartsheet

import (
	"bytes"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "url")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Resp{}, "code", "message", "data")
	kc.Check(SheetAddRecordParam{}, "channel", "fields", "values")
	kc.Check(SheetCreateParam{}, "doc_name", "admin_users")
}

func TestClient_request(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(APISheetCreate, SuccessCode, map[string]string{
		"doc_id": "test",
		"url":    "https://doc.test.com/smartsheet/newdoc",
	})
	c := NewClient(TestConfig())
	req, err := http.NewRequest(http.MethodPost, c.URL+APISheetCreate,
		bytes.NewReader([]byte(`{"doc_name":"test","admin_users":["user1","user2"]}`)))
	require.NoError(err)
	respData := new(SheetCreateRespData)
	err = c.request(req, respData, false)
	require.NoError(err)
	require.NotNil(respData)
	assert.Equal("test", respData.DocID)
	assert.Equal("https://doc.test.com/smartsheet/newdoc", respData.URL)

	// 测试 raw 为 true 的情况
	resp := new(Resp)
	err = c.request(req, resp, true)
	require.NoError(err)
	require.NotNil(respData)
	assert.EqualValues(SuccessCode, resp.Code)
	require.NotNil(resp.Data)
	assert.Equal(`{"doc_id":"test","url":"https://doc.test.com/smartsheet/newdoc"}`, string(resp.Data))
}

func TestClient_AddRecord(t *testing.T) {
	assert := assert.New(t)

	SetMockResult(APISheetAddRecord, SuccessCode, nil)
	c := NewClient(TestConfig())
	err := c.AddRecord(&SheetAddRecordParam{
		Channel: "test",
		Fields:  []string{"a", "b"},
		Values:  []string{"1", "2"},
	})
	assert.NoError(err)
}

func TestClient_CreateSheet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(APISheetCreate, SuccessCode, map[string]string{
		"doc_id": "test",
		"url":    "https://doc.test.com/smartsheet/newdoc",
	})
	c := NewClient(TestConfig())
	data, err := c.CreateSheet(&SheetCreateParam{
		DocName:    "test",
		AdminUsers: []string{"user1", "user2"},
	})
	require.NoError(err)
	require.NotNil(data)
	assert.Equal("test", data.DocID)
	assert.Equal("https://doc.test.com/smartsheet/newdoc", data.URL)
}
