package antispamv2

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/constants"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestClient_CheckImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testClient, err := NewClient(TestConfig())
	require.NoError(err)

	res, err := testClient.CheckImage("https://12323m123421vatars/icon01.png", "")
	require.Error(err)
	assert.Nil(res)

	res, err = testClient.CheckImage("https://static-test.maoercdn.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg", "")
	require.NoError(err)
	require.NotNil(res)
	assert.False(res.Pass)

	testClient.Config.Provider = ""
	res, err = testClient.CheckImage("https://static.maoercdn.com/avatars/icon01.png", "")
	require.NoError(err)
	require.NotNil(res)
	assert.True(res.Pass)
}

func TestClient_ImageSyncScanV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	img := "https://img0.baidu.com/it/u=104045263,**********&fm=253&fmt=auto&app=120&f=JPEG?w=1080&h=656" // 血腥图片
	c, err := NewClient(TestConfig())
	require.NoError(err)
	respResult, err := c.ImageSyncScanV2(img, "")
	require.NoError(err)
	assert.NotEmpty(respResult)
	for _, v := range respResult.Data.Results {
		assert.Equal(constants.AliyunSuggestionV2High, v.RiskLevel)
	}

	userRespResult, err := c.ImageSyncScanV2(img, scan.SceneUserInfo)
	require.NoError(err)
	assert.NotEmpty(userRespResult)
	assert.Equal(constants.AliyunSuggestionV2High, userRespResult.Data.RiskLevel)
}
