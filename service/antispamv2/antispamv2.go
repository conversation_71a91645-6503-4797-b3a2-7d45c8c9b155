package antispamv2

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/constants"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 第三方（阿里云）图片检测场景分类
const (
	// 图片检测类型：用基线检测_专业版
	AliImageCheckBaselineCheckPro = "baselineCheck_pro"
	// 图片检测类型：内容治理检测
	AliImageCheckTonalityImprove = "tonalityImprove"
	// 图片检测类型：头像图片检测
	AliImageCheckProfilePhotoCheck = "profilePhotoCheck"
	// AliImageActionImageModeration 检测类型：图片检测
	AliImageActionImageModeration = "ImageModeration"
	// 检测类型：图片批量检测
	AliImageActionImageBatchModeration = "ImageBatchModeration"
	// 普通图片检测
	SceneNormalCheck = AliImageCheckBaselineCheckPro + "," + AliImageCheckTonalityImprove
	// 用户相关图片检测
	SceneUserInfoCheck = AliImageCheckProfilePhotoCheck
)

// Config contains configuration for Client
type Config struct {
	Provider        string `yaml:"provider"`
	URL             string `yaml:"url"`
	AccessKeyID     string `yaml:"access_key_id"`     //  用于标识访问者身份
	AccessKeySecret string `yaml:"access_key_secret"` //  加密签名字符串和服务器端验证签名字符串的密钥
}

// ImageCheckParam 不同类型的图片检测参数
type ImageCheckParam struct {
	Service           string
	ServiceParameters ServiceParameters
	Action            string
}

// ServiceParameters 阿里图片检测的参数 https://help.aliyun.com/document_detail/467829.html
type ServiceParameters struct {
	ImageURL string `json:"imageUrl"`
	DataID   string `json:"dataId"`
}

// Client antispamv2 api client
type Client struct {
	*Config
	*http.Client
}

// BuildDefaultConfig returns a default Config
func BuildDefaultConfig() *Config {
	return &Config{
		Provider: "aliyun",
		URL:      "https://green-cip.cn-shanghai.aliyuncs.com",
	}
}

// WithAccess sets the AccessKeyID and the AccessKeyID
func (c *Config) WithAccess(accessKeyID, accessKeySecret string) *Config {
	if c != nil {
		c.AccessKeyID = accessKeyID
		c.AccessKeySecret = accessKeySecret
	}
	return c
}

// NewClient new Client
func NewClient(conf *Config) (*Client, error) {
	c := &Client{
		Config: conf,
		Client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
	return c, nil
}

// ImageCheckV2 请求阿里图片审核
func (c Client) ImageCheckV2(imageCheckParam ImageCheckParam) (*ImageScanRespDataV2, error) {
	api := c.URL

	urlValuesStr, err := c.buildParam(imageCheckParam)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, api, strings.NewReader(urlValuesStr))
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("User-Agent", serviceutil.UserAgent)

	urlValuesUnescapeStr, _ := url.QueryUnescape(urlValuesStr)
	logger.Debugf("%s %s\n%s", req.Method, api, urlValuesUnescapeStr)

	resp, err := c.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("read image body error: %v", err)
		return nil, err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)

	imageScanRespDataV2 := ImageScanRespDataV2{}
	err = json.Unmarshal(body, &imageScanRespDataV2)
	if err != nil {
		return nil, err
	}
	if imageScanRespDataV2.Code != constants.RespCodeOk {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\n%s\n\nHTTP %s\n%s", http.MethodPost, api, urlValuesUnescapeStr, resp.Status, body)
		}
		return nil, &serviceutil.APIError{
			Status:  imageScanRespDataV2.Code,
			Message: fmt.Sprintf("(%d) - %s - %v", imageScanRespDataV2.Code, imageScanRespDataV2.RequestID, imageScanRespDataV2.Msg),
		}
	}
	if imageScanRespDataV2.Data == nil {
		return nil, errors.New("image scan response data is empty")
	}
	return &imageScanRespDataV2, nil
}

// buildParam 构建阿里图片审核参数
func (c Client) buildParam(imageCheckParam ImageCheckParam) (string, error) {
	parameters := make(map[string]string, 16)
	// 公共参数
	parameters["Action"] = imageCheckParam.Action
	parameters["Version"] = "2022-03-02"
	parameters["AccessKeyId"] = c.AccessKeyID
	parameters["Timestamp"] = goutil.TimeNow().UTC().Format("2006-01-02T15:04:05Z")
	parameters["SignatureMethod"] = "HMAC-SHA1"
	parameters["SignatureVersion"] = "1.0"
	parameters["SignatureNonce"] = uuid.NewV4().String()
	parameters["Format"] = "JSON"

	parameters["Service"] = imageCheckParam.Service
	serviceParametersBytes, err := json.Marshal(imageCheckParam.ServiceParameters)
	if err != nil {
		return "", err
	}
	parameters["ServiceParameters"] = string(serviceParametersBytes)

	stringToSign := createStringToSign("POST", parameters)
	parameters["Signature"] = c.createSignature(stringToSign)

	urlValues := make(url.Values, len(parameters))
	for k, v := range parameters {
		urlValues.Set(k, v)
	}
	urlValuesStr := urlValues.Encode()
	return urlValuesStr, nil
}

// createSignature 创建数据签名
func (c Client) createSignature(stringToSign string) string {
	secret := c.AccessKeySecret + "&"
	mac := hmac.New(sha1.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	signData := mac.Sum(nil)
	return base64.StdEncoding.EncodeToString(signData)
}

// createStringToSign 构建为签名准备的字符串
// 字符串构建实现，参考链接最下面 HTTPS 原生调用的接入方式 https://help.aliyun.com/document_detail/467828.html
func createStringToSign(method string, parameters map[string]string) string {
	// 排序请求参数
	keys := make([]string, 0, len(parameters))
	for key := range parameters {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	canonicalizedQueryStrings := make([]string, 0, len(parameters))
	for _, k := range keys {
		canonicalizedQueryStrings = append(canonicalizedQueryStrings, percentEncode(k)+"="+percentEncode(parameters[k]))
	}
	stringToSign := method + "&" + percentEncode("/") + "&" + percentEncode(strings.Join(canonicalizedQueryStrings, "&"))
	return stringToSign
}

func percentEncode(str string) string {
	str = url.QueryEscape(str)
	return urlEncodeReplacer.Replace(str)
}

var urlEncodeReplacer = strings.NewReplacer("+", "%20", "*", "%2A", "%7E", "~")
