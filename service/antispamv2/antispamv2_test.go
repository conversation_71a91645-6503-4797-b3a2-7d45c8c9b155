package antispamv2

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/constants"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	m.Run()
}

func TestConfigTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "provider", "url", "access_key_id", "access_key_secret")
}

func TestClient_ImageCheckV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	client, err := NewClient(TestConfig())
	require.NoError(err)

	imageURL := "https://static-test.maoercdn.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg"
	checkService := SceneNormalCheck
	action := AliImageActionImageBatchModeration

	imageCheckParam := ImageCheckParam{
		Action:  action,
		Service: checkService,
		ServiceParameters: ServiceParameters{
			ImageURL: imageURL,
			DataID:   "DataIdValue",
		},
	}
	res, err := client.ImageCheckV2(imageCheckParam)
	require.NoError(err)
	assert.NotNil(res)
	assert.Equal(res.Data.RiskLevel, constants.AliyunSuggestionV2High)
}

func TestClient_buildParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	client, err := NewClient(TestConfig())
	require.NoError(err)

	antiSpamV2Case1 := ImageCheckParam{
		Action:  "TestActionValue",
		Service: "TestServiceValue",
		ServiceParameters: ServiceParameters{
			ImageURL: "ImageUrlValue",
			DataID:   "DataIdValue",
		},
	}

	result1, err := client.buildParam(antiSpamV2Case1)
	if err != nil {
		t.Errorf("Test case 1 failed: %v", err)
		require.NoError(err)
		assert.NotNil(result1)
		assert.Contains(result1, "TestActionValue")
		assert.Contains(result1, "TestServiceValue")
	}
}

func TestClient_createSignature(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	client, err := NewClient(TestConfig())
	require.NoError(err)

	stringToSign := "testString"
	actualSignature := client.createSignature(stringToSign)
	exceptedSignature := "khv7z72Q9R4pUrQHswevW4yBUPI="
	assert.Equal(exceptedSignature, actualSignature)
}

func TestCreateStringToSign(t *testing.T) {
	testCases := []struct {
		name       string
		httpMethod string
		parameters map[string]string
		expected   string
	}{
		{
			name:       "Simple Case",
			httpMethod: "GET",
			parameters: map[string]string{
				"param1": "value1",
				"param2": "value2",
			},
			expected: "GET&%2F&param1%3Dvalue1%26param2%3Dvalue2",
		},
		{
			name:       "Empty Parameters",
			httpMethod: "POST",
			parameters: map[string]string{},
			expected:   "POST&%2F&",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := createStringToSign(tc.httpMethod, tc.parameters)
			if result != tc.expected {
				t.Errorf("Expected %s, but got %s", tc.expected, result)
			}
		})
	}
}

func TestPercentEncode(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Normal string",
			input:    "hello world",
			expected: "hello%20world",
		},
		{
			name:     "String with special characters",
			input:    "hello*world~",
			expected: "hello%2Aworld~",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := percentEncode(tt.input)
			if result != tt.expected {
				t.Errorf("percentEncode(%q) = %q; expected %q", tt.input, result, tt.expected)
			}
		})
	}
}
