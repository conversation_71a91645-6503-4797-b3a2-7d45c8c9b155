package antispamv2

import (
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/constants"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

// 参考文档：
// https://help.aliyun.com/document_detail/467828.html
// https://help.aliyun.com/document_detail/2862201.html

// ImageScanRespDataV2 阿里图片检测 Data 的返回值
type ImageScanRespDataV2 struct {
	RequestID string           `json:"RequestId"`
	Code      int              `json:"Code"`
	Msg       string           `json:"Msg"`
	Data      *ImageScanDataV2 `json:"Data"`
}

// ImageScanDataV2 阿里图片检测 Data 的返回值
type ImageScanDataV2 struct {
	DataID    string               `json:"DataId"`
	RiskLevel string               `json:"RiskLevel"`
	Result    []ImageScanResultV2  `json:"Result"`
	Results   []ImageScanResultsV2 `json:"Results"`
}

// ImageScanResultV2 阿里图片检测单个 label 的返回值
type ImageScanResultV2 struct {
	Label       string  `json:"Label"`
	Confidence  float64 `json:"Confidence"`
	Description string  `json:"Description"`
}

// ImageScanResultsV2 阿里图片检测单个 service 的返回值
type ImageScanResultsV2 struct {
	ImageScanResultV2 []ImageScanResultV2 `json:"Result"`
	RiskLevel         string              `json:"RiskLevel"`
	Service           string              `json:"Service"`
}

// CheckImage 检测单张图片
func (c *Client) CheckImage(url string, scene string) (*scan.CheckResult, error) {
	ret := &scan.CheckResult{
		Pass: true,
	}
	switch c.Provider {
	case "aliyun":
		result, err := c.ImageSyncScanV2(url, scene)
		if err != nil {
			logger.Errorf("ImageSyncScan error: %v", err)
			return nil, err
		}
		ret.RequestID = result.RequestID // 新接口使用 RequestID，包含了 TaskID 语义
		ret.AliyunMatch = append(ret.AliyunMatch, scan.AliyunMatch{
			RequestID: result.RequestID,
		})
		if result.Data.RiskLevel == constants.AliyunSuggestionV2High ||
			(scene == scan.SceneUserInfo && (result.Data.RiskLevel == constants.AliyunSuggestionV2Medium ||
				result.Data.RiskLevel == constants.AliyunSuggestionV2Low)) {
			ret.Pass = false
		}
		if !ret.Pass {
			for _, serviceResult := range result.Data.Result {
				ret.Labels = append(ret.Labels, serviceResult.Label)
			}
		}
		return ret, nil
	}
	return ret, nil
}

// ImageSyncScanV2 单张图片同步检测 https://help.aliyun.com/document_detail/467826.html
func (c *Client) ImageSyncScanV2(url string, bizType string) (*ImageScanRespDataV2, error) {
	checkService := SceneNormalCheck
	action := AliImageActionImageBatchModeration
	if bizType == scan.SceneUserInfo {
		// 用户信息相关的图片检测，需要额外加上更细粒度（模型）检测
		checkService = SceneUserInfoCheck
		action = AliImageActionImageModeration
	}
	imageCheckParam := ImageCheckParam{
		Service:           checkService,
		ServiceParameters: ServiceParameters{ImageURL: url},
		Action:            action,
	}
	imageScanRespDataV2, err := c.ImageCheckV2(imageCheckParam)
	if err != nil {
		return nil, err
	}
	return imageScanRespDataV2, nil
}
