package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

const (
	// LocalKeyEmojiAllowList emoji 白名单缓存 key
	LocalKeyEmojiAllowList cache.KeyFormat = "emoji_allow_list"

	// LocalKeyDrawPointConfig1 抽奖积分配置缓存
	// 参数：活动 ID
	LocalKeyDrawPointConfig1 cache.KeyFormat = "draw_point_config:%d"

	// LocalKeyEventAnPrizeByPoolID2 抽奖奖品按照奖池缓存
	LocalKeyEventAnPrizeByPoolID2 cache.KeyFormat = "an_prize:event_id:%d:pool_id:%d"

	// LocalKeyEventAnPrize1 某一个活动的奖品缓存
	LocalKeyEventAnPrize1 cache.KeyFormat = "an_prize:event_id:%d"

	// LocalKeyTheatreSoundIDs0 盲盒剧场所有音频 ID
	LocalKeyTheatreSoundIDs0 cache.KeyFormat = "theatre_sound_ids"

	// LocalKeyForbiddenWords2 元素评论屏蔽词
	LocalKeyForbiddenWords2 cache.KeyFormat = "forbidden_words:element_id:%d:check_type:%d"

	// LocalKeyDramaSoundIDs1 某个剧集的音频 ID 缓存
	// params: drama_id
	LocalKeyDramaSoundIDs1 cache.KeyFormat = "drama_sound_ids:%d"

	// LocalKeyForbiddenWords1 屏蔽词本地缓存 key
	// params: 屏蔽词 Type
	LocalKeyForbiddenWords1 cache.KeyFormat = "local_forbidden_words:%d"
)
