package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

// LRURedis 使用的 Keys
const (
	KeyUserTokenGo1         cache.KeyFormat = "gotoken_%s"           // STRING
	KeyGetDramaIDBySoundID1 cache.KeyFormat = "drama_id:sound_id:%s" // STRING
)

// KeyDramaCornerMarkStyle0 剧集角标样式信息
// 举例: {"1": {"text": "已购", "text_color": "#FFFFFF", "bg_start_color": "#E66465", "bg_end_color": "#E66465"}}
const KeyDramaCornerMarkStyle0 cache.KeyFormat = "drama:corner_mark_style" // STRING

// KeyDramaCornerMarkViewRanks1 剧集自然周新增播放量，%s 代表日期（比如 20060102，每周周一日期）
const KeyDramaCornerMarkViewRanks1 cache.KeyFormat = "drama:corner_mark_view_rank:%s" // STRING

// KeySoundPlayRecommendStrategySounds1 音频播放页的推荐音频 ID（按推荐策略分类），%d 代表当前播放音频 ID
const KeySoundPlayRecommendStrategySounds1 cache.KeyFormat = "sound_play_recommend_strategy_v2:id:%d" // STRING

// KeySpecialTopicCardSearchWord1 特殊搜索专题卡
// params: search_word 搜索词
const KeySpecialTopicCardSearchWord1 cache.KeyFormat = "special_topic_card:%s" // STRING
