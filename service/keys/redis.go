package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

const (
	// KeyDailyFreeDrawUsers2 存放已经用掉某天的免费抽奖机会的用户 ID，两个参数分别指 event_id 和日期（比如 20060102）
	KeyDailyFreeDrawUsers2 cache.KeyFormat = "daily_free_draw_users:event_id:%d:%s" // SET
	// KeyEventPrizes1 奖品被抽出的次数
	KeyEventPrizes1 cache.KeyFormat = "event:%d:prizes" // HASH
	// LockEventUserPoint2 活动积分抽奖/兑换锁
	// params: event_id; user_id
	LockEventUserPoint2 cache.KeyFormat = "lock:event:%d:user_id:%d" // STRING
	// KeyDrawPointUserQuests2 活动积分任务 key
	// params: 活动 ID; 用户 ID
	KeyDrawPointUserQuests2 cache.KeyFormat = "draw_point:quests:event_id:%d:user_id:%d" // HASH

	// KeyEventUserExchangeCount2 用户兑换商品的次数
	KeyEventUserExchangeCount2 cache.KeyFormat = "event:%d:user:%d:exchange_count" // HASH

	// KeyEventPointTaskInfo2 抽奖活动积分任务完成信息
	// params: 活动 ID; 用户 ID
	KeyEventPointTaskInfo2 cache.KeyFormat = "event_point:task_info:event_id:%d:user_id:%d" // HASH
)

// 22 年 712 活动 key
const (
	KeyMaoMaoPointUserID1 cache.KeyFormat = "maomao_point:user_id:%d" // HASH
)

const (
	// KeySubtitleTeamUsers0 存放字幕组用户 ID
	// WORKAROUND: 待《天官赐福》剧集上线后删除此 key
	KeySubtitleTeamUsers0 cache.KeyFormat = "subtitle_team_users" // SET

	// LockUserAddDramaDanmaku2 用户添加弹幕加锁
	// params: user_id 用户 ID; drama_id 剧集 ID
	LockUserAddDramaDanmaku2 cache.KeyFormat = "lock:add_dm:user_id:%d:drama_id:%d" // STRING

	// KeyTheatreDramaIDs0 盲盒剧场的剧集 ID set
	KeyTheatreDramaIDs0 cache.KeyFormat = "theatre_drama_ids" // SET

	// KeyTheatreNewDramaIDs0 盲盒剧场 - 新作速递星盒的剧集 ID set
	KeyTheatreNewDramaIDs0 cache.KeyFormat = "theatre_new_drama_ids" // SET

	// KeyTheatreSubscribeDramaIDs2 盲盒剧场活动订阅剧集 ID
	// params: 活动 ID; 用户 ID
	KeyTheatreSubscribeDramaIDs2 cache.KeyFormat = "theatre_subscribe_drama_ids:event_id:%d:user_id:%d" // SET

	// KeyCvfesFollowUserIDs2 声优纪活动用户已关注的参演用户 IDs
	// params: 活动 ID; 用户 ID
	KeyCvfesFollowUserIDs2 cache.KeyFormat = "cvfes_follow_user_ids:event_id:%d:user_id:%d" // SET
)

// 配置相关
const (
	// KeyAllowListOfficeIP0 办公室 IP 配置
	KeyAllowListOfficeIP0 cache.KeyFormat = "allow_list_office_ip" // HASH

	// KeyUsersIPLocation0 用户 IP 属地配置
	// NOTICE: 目前数据量较小，若后续业务需要改造导致数据量变大，HASH 存储类型将不再适用
	KeyUsersIPLocation0 cache.KeyFormat = "users_ip_location" // HASH
)

const (
	// LockPushSearchClicks0 推送 OpenSearch 的搜索点击数据锁
	LockPushSearchClicks0 cache.KeyFormat = "lock:push_search_clicks" // STRING

	// KeyPushSearchClicksList0 推送到 OpenSearch 的搜索点击数据队列
	KeyPushSearchClicksList0 cache.KeyFormat = "push_search_clicks_list" // LIST
)

// KeyMsgUserNotFollowingMeList2 发信人的每日收信人未关注发信人列表
// params: 发信人用户 ID; 格式化后的日期 e.g. 20231007
const KeyMsgUserNotFollowingMeList2 cache.KeyFormat = "msg:user:%d:not_following_me:%s" // SET

// KeyDramaRevenueUserAuth1 查看剧集收益后台身份认证
// params: 用户 ID
const KeyDramaRevenueUserAuth1 cache.KeyFormat = "drama_revenue_auth:user_id:%d" // STRING
