package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// DatabusKeySearchClicks1 search-clicks databus key
// 参数是 {user_id} 或 :{crc32(ip)}
var DatabusKeySearchClicks1 = cache.DatabusFormatter{
	"search-clicks:%s",
	"search-clicks:",
}

// DatabusKeyUserFollowLog1 user follow/unfollow databus key
// 参数是 {user_id} 发起关注的用户 ID
var DatabusKeyUserFollowLog1 = cache.DatabusFormatter{
	"user-follow-log:%d", // STRING
	"user-follow-log:",
}

// DatabusKeyShareDetailLog1 分享 databus 消息
// params: 分享时间戳（单位：秒）
var DatabusKeyShareDetailLog1 = cache.DatabusFormatter{
	"share_detail_log:%d", // STRING
	"share_detail_log:",
}

// DatabusKeyUserPointDetailLog1 添加小鱼干 databus 消息
// params: user_id
var DatabusKeyUserPointDetailLog1 = cache.DatabusFormatter{
	"user_point_detail_log:%d", // STRING
	"user_point_detail_log:",
}

// DatabusKeySubscribeDramaDetailLog1 追剧 databus 消息
// params: user_id
var DatabusKeySubscribeDramaDetailLog1 = cache.DatabusFormatter{
	"subscribe_drama_detail_log:%d",
	"subscribe_drama_detail_log:",
}

// DatabusKeyCollectUserIPLog1 获取用户评论/发弹幕/启动 App 时的用户 IP 消息
// params: user_id
var DatabusKeyCollectUserIPLog1 = cache.DatabusFormatter{
	"collect_user_ip_log:%d", // STRING
	"collect_user_ip_log:",
}

// DatabusKeySoundPlayLog2 音频播放事件上报
// params[0]: web 或 app; params[1]: {user_id} 或 :{crc32(ip)}
var DatabusKeySoundPlayLog2 = cache.DatabusFormatter{
	"sound_play_log:%s:%s", // STRING
	"sound_play_log:",
}

// DatabusKeySoundPlayLogHeartbeat2 音频播放心跳上报
// params[0]: web 或 app; params[1]: {user_id} 或 :{crc32(ip)}
var DatabusKeySoundPlayLogHeartbeat2 = cache.DatabusFormatter{
	"sound_play_log_heartbeat:%s:%s", // STRING
	"sound_play_log_heartbeat:",
}

// DatabusKeyBuyDramaDetailLog1 购买剧集 databus 消息
// params: user_id
var DatabusKeyBuyDramaDetailLog1 = cache.DatabusFormatter{
	"buy_drama_detail_log:%d",
	"buy_drama_detail_log:",
}

// DatabusKeySetBirthdayLog1 用户在 app 上设置生日的消息
// params: 用户 ID
var DatabusKeySetBirthdayLog1 = cache.DatabusFormatter{
	"set_birthday_log:%d",
	"set_birthday_log:",
}

// DatabusKeyRecommendExposureLog1 猜你喜欢推荐算法日志
// params: user_id 或 ip CRC-32 校验和
var DatabusKeyRecommendExposureLog1 = cache.DatabusFormatter{
	"recommend_exposure_log:%d",
	"recommend_exposure_log:",
}

// DatabusKeyCommentLog1 发布评论 databus 消息
// params: user_id
var DatabusKeyCommentLog1 = cache.DatabusFormatter{
	"comment_log:%d",
	"comment_log:",
}
