package mongodb

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"runtime/debug"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsoncodec"
	"go.mongodb.org/mongo-driver/bson/bsonoptions"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MongoDatabase 封装 mongo.Database
type MongoDatabase struct {
	*mongo.Database
	Client  *mongo.Client
	Conf    Config
	timeout time.Duration
}

// Config for database config
type Config struct {
	URL            string        `yaml:"url"`
	Host           string        `yaml:"host"`
	Port           int           `yaml:"port"`
	Name           string        `yaml:"name"`
	Username       string        `yaml:"username"`
	Password       string        `yaml:"password"`
	Prefix         string        `yaml:"prefix"`
	Timeout        time.Duration `yaml:"timeout"`
	UseUTCTimeZone bool          `yaml:"use_utc_time_zone"`
	// DisableSession 是否禁用事务
	DisableSession bool `yaml:"disable_session"`
}

// NewMongoClient init mongodb connection
func NewMongoClient(conf *Config) (*mongo.Client, *MongoDatabase, error) {
	mdb, err := NewMongoDB(conf)
	if err != nil {
		return nil, nil, err
	}
	return mdb.Client, mdb, nil
}

// NewMongoDB init mongodb connection
func NewMongoDB(conf *Config) (*MongoDatabase, error) {
	var dsn string
	if conf.URL != "" {
		dsn = conf.URL
	} else {
		dsn = "mongodb://"
		if conf.Username != "" || conf.Password != "" {
			dsn += fmt.Sprintf("%s:%s@", conf.Username, conf.Password)
			dsn += fmt.Sprintf("%s:%d/%s", conf.Host, conf.Port, conf.Name)
		} else {
			// 在连接到数据库的情况下，mongo-driver 和 mongodb 命令行的解析 uri 操作不一致。
			// 如果用户名密码为空并且指定了数据库的情况下：
			// mongodb 命令行不进行用户的验证
			// mongo-driver 仍然会进行用户验证。此时在 dsn 上加上连接的数据库会导致用户验证失败
			dsn += fmt.Sprintf("%s:%d", conf.Host, conf.Port)
		}
	}
	timeout := 10 * time.Second
	if conf.Timeout > 0 {
		timeout = conf.Timeout
	}
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	opts := options.Client().ApplyURI(dsn)

	if !conf.UseUTCTimeZone {
		// time.Time use local time zone
		rb := bson.NewRegistryBuilder().
			RegisterTypeDecoder(
				reflect.TypeOf(time.Time{}),
				bsoncodec.NewTimeCodec(bsonoptions.TimeCodec().SetUseLocalTimeZone(true)),
			)
		opts.SetRegistry(rb.Build())
	}

	client, err := mongo.Connect(ctx, opts)
	if err != nil {
		return nil, err
	}
	err = client.Ping(ctx, nil)
	if err != nil {
		_ = client.Disconnect(ctx)
		return nil, err
	}
	mDb := &MongoDatabase{
		Database: client.Database(conf.Name),
		Client:   client,
		Conf:     *conf,
		timeout:  timeout,
	}

	// connect success
	return mDb, nil
}

// Collection gets a handle for a given collection in the database.
func (mDb *MongoDatabase) Collection(name string) *mongo.Collection {
	return mDb.Database.Collection(mDb.Conf.Prefix + name)
}

// Context 返回 mongodb 使用的 context
func (mDb *MongoDatabase) Context() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), mDb.timeout)
}

// IsNoDocumentsError 是否是 ErrNoDocuments
func IsNoDocumentsError(err error) bool {
	return errors.Is(err, mongo.ErrNoDocuments) //nolint:forbidigo
}

// IsDuplicateKeyError returns true if err is a duplicate key error
func IsDuplicateKeyError(err error) bool {
	return mongo.IsDuplicateKeyError(err)
}

// NewProjection 生成 projection
func NewProjection(fields string) map[string]int {
	s := strings.Split(fields, ",")
	res := make(map[string]int, len(s))
	for i := range s {
		if field := strings.TrimSpace(s[i]); field != "" {
			res[field] = 1
		} else {
			panic("field must not empty")
		}
	}
	return res
}

// UseSession mongo 事务
func UseSession(mDb *MongoDatabase, fn func(ctx context.Context) error, opts ...*options.TransactionOptions) error {
	ctx, cancel := mDb.Context()
	defer cancel()
	if mDb.Conf.DisableSession {
		return fn(ctx)
	}
	return mDb.Client.UseSession(ctx, func(sCtx mongo.SessionContext) (err error) {
		defer func() {
			if r := recover(); r != nil {
				if err2 := sCtx.AbortTransaction(ctx); err2 != nil {
					logger.Errorf("AbortTransaction error: %v", err2)
					// PASS
				}

				stack := util.IgnoreUntil(string(debug.Stack()), 7, '\n')
				logger.Errorf("UseSession recovery: \n%v\n%s", r, stack)
				err = errors.New(fmt.Sprint(r))
			}
		}()
		// 事务中查询必须读主库
		txnOpts := options.Transaction().
			SetReadPreference(readpref.Primary())

		if len(opts) != 0 {
			txnOpts = options.MergeTransactionOptions(append([]*options.TransactionOptions{txnOpts}, opts...)...)
		}

		err = sCtx.StartTransaction(txnOpts)
		if err != nil {
			return err
		}
		err = fn(sCtx)
		if err != nil {
			// 不用显式执行 AbortTransaction, 外层处理了
			return err
		}
		err = sCtx.CommitTransaction(sCtx)
		return err
	})
}

// UseSession mongo 事务
// TODO: 兼容已有的写法，将来可以移除
func (mDb *MongoDatabase) UseSession(client *mongo.Client, fn func(ctx context.Context) error, opts ...*options.TransactionOptions) error {
	return UseSession(mDb, fn, opts...)
}
