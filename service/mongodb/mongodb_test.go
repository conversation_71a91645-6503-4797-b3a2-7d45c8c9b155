package mongodb

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	m.Run()
}

func TestNewMongoClient(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := context.Background()

	// Test URL
	confURL := Config{URL: "mongodb://mongo.srv.maoer.co:27017/admin", Name: "admin"}
	client, db, err := NewMongoClient(&confURL)
	require.NoError(err)
	assert.NotNil(client)
	assert.NotNil(db)

	_ = client.Disconnect(ctx)

	// Test host & port
	confHostPort := Config{Host: "mongo.srv.maoer.co", Port: 27017, Name: "admin"}
	client, db, err = NewMongoClient(&confHostPort)
	require.NoError(err)
	assert.NotNil(client)
	assert.NotNil(db)

	_ = client.Disconnect(ctx)
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := context.Background()

	// Test URL
	confURL := Config{URL: "mongodb://mongo.srv.maoer.co:27017/chatroom", Name: "chatroom", Prefix: "test_"}
	client, db, err := NewMongoClient(&confURL)
	require.NoError(err)
	assert.NotNil(client)
	assert.NotNil(db)

	defer func() {
		_ = client.Disconnect(ctx)
	}()

	gifts := db.Collection("gifts")
	assert.Equal("test_gifts", gifts.Name())
}

func TestContext(t *testing.T) {
	assert := assert.New(t)

	db := MongoDatabase{timeout: 100 * time.Millisecond}
	c, cancel := db.Context()
	defer cancel()
	expcetd := util.TimeNow().Add(100 * time.Millisecond)
	select {
	case <-c.Done():
	case <-time.NewTimer(200 * time.Millisecond).C:
	}
	assert.WithinDuration(expcetd, util.TimeNow(), 50*time.Millisecond)

	c, cancel = db.Context()
	expcetd = util.TimeNow()
	cancel()
	select {
	case <-c.Done():
	case <-time.NewTimer(200 * time.Millisecond).C:
	}
	assert.WithinDuration(expcetd, util.TimeNow(), 50*time.Millisecond)
}

func TestIsNoDocumentsError(t *testing.T) {
	assert := assert.New(t)
	assert.True(IsNoDocumentsError(mongo.ErrNoDocuments))
	assert.False(IsNoDocumentsError(errors.New("test error")), "test error")
}

func TestIsDuplicateKeyError(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config := TestConfig("chatroom")
	_, db, err := NewMongoClient(&config)
	require.NoError(err)
	col := db.Collection("test")
	ctx := context.Background()
	_, err = col.DeleteMany(ctx, bson.M{"test": 45345})
	require.NoError(err)

	testOID := primitive.NewObjectID()
	_, err = col.InsertOne(ctx, bson.M{
		"_id":  testOID,
		"test": 45345,
	})
	require.False(IsDuplicateKeyError(err))

	// 测试唯一键重复插入
	_, err = col.InsertOne(ctx, bson.M{
		"_id":  testOID,
		"test": 45345,
	})
	assert.True(IsDuplicateKeyError(err))

	assert.False(IsDuplicateKeyError(errors.New("test error")), "test error")
}

func TestNewProjection(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(map[string]int{"id": 1, "_id": 1}, NewProjection("id, _id"))
	assert.PanicsWithValue("field must not empty", func() { NewProjection(" ") })
}

func TestTimeUseUTCTimeZone(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config := TestConfig("chatroom")
	_, db, err := NewMongoClient(&config)
	require.NoError(err)

	type T struct {
		Time time.Time `bson:"time"`
	}

	p1 := T{
		Time: util.TimeNow(),
	}

	col := db.Collection("time")

	ctx := context.Background()
	_, err = col.InsertOne(ctx, p1)
	require.NoError(err)
	defer func() {
		_, err := col.DeleteMany(ctx, bson.M{})
		assert.NoError(err)
	}()

	var p2 T
	err = col.FindOne(ctx, bson.M{}).Decode(&p2)
	require.NoError(err)
	assert.Equal(time.Local, p2.Time.Location())

	// test UTC time
	config = TestConfig("chatroom")
	config.UseUTCTimeZone = true
	_, db, err = NewMongoClient(&config)
	require.NoError(err)

	col = db.Collection("time")
	err = col.FindOne(ctx, bson.M{}).Decode(&p2)
	require.NoError(err)
	assert.Equal(time.UTC, p2.Time.Location())
}

func TestUseSession(t *testing.T) {
	require := require.New(t)

	config := TestConfig("chatroom")
	_, db, err := NewMongoClient(&config)
	require.NoError(err)

	pass := false
	col := db.Collection("test")
	f := func(ctx context.Context) error {
		if pass {
			panic("test panic")
		}
		return col.FindOne(ctx, bson.M{"test": 1}).Err()
	}
	require.NoError(UseSession(db, f))

	db.Conf.DisableSession = false
	require.EqualError(UseSession(db, func(ctx context.Context) error {
		return col.FindOne(ctx, bson.M{"test": 1}).Err()
	}), "(IllegalOperation) Transaction numbers are only allowed on a replica set member or mongos")

	pass = true
	require.EqualError(UseSession(db, f), "test panic")
}
