//go:build !release
// +build !release

package service

import (
	"path"
	"runtime"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	"github.com/MiaoSiLa/missevan-go/service/antispamv2"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	servicegovern "github.com/MiaoSiLa/missevan-go/service/bilibili/govern"
	serviceshorturl "github.com/MiaoSiLa/missevan-go/service/bilibili/shorturl"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/storage/entryconfig"
	"github.com/MiaoSiLa/missevan-go/service/util"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// InitTestService init services for testing only
func InitTestService() {
	logger.InitTestLog()
	params.InitTestParams()
	conf := &Config{
		Redis: serviceredis.Config{
			Addr:     "redis.srv.maoer.co:6379",
			DB:       500,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		LRURedis: serviceredis.Config{
			Addr:     "redis.srv.maoer.co:6379",
			DB:       700,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		MRPC: mrpc.TestConfig(),
		PushService: pushservice.Config{
			URL: "http://mpush.srv.maoer.co:8098/",
			Key: "testkey",
		},
		Gaia:     servicegaia.TestConfig(),
		Govern:   servicegovern.TestConfig(),
		ShortURL: serviceshorturl.TestConfig(),
		Storage: storage.Config{
			"test": {
				Type:            "oss",
				AccessKeyID:     "LTAIsNW7Hxzgnxu2",
				AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
				Bucket:          "missevan-test",
				Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
				PublicURLs: []entryconfig.SectionPublicURL{
					{
						URL:    "http://static-test.missevan.com/",
						Weight: 1,
					},
				},
			},
		},
		// TODO: 将 WithAccess 和 Set 两个函数改成 WithAccessKey
		AntiSpam:       *(antispam.TestConfig()),
		AntiSpamV2:     *(antispamv2.TestConfig()),
		Geetest:        geetest.TestConfig(),
		GeetestLowRisk: geetest.TestLowRiskConfig(),
		OpenSearch:     *(search.TestConfig()),
		Captcha:        captcha.BuildDefaultConfig(),
	}
	conf.DB = servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Name:         "app_missevan",
		Port:         3306,
		User:         "root",
		Pass:         "rootmysql",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
	}
	conf.LogDB = conf.DB
	conf.DramaDB = conf.DB
	conf.VoiceDB = conf.DB
	conf.MessageDB = conf.DB
	conf.PayDB = conf.DB

	conf.LogDB.Name = "app_missevan_log"
	conf.DramaDB.Name = "app_missevan_radio_drama"
	conf.VoiceDB.Name = "app_missevan_voice"
	conf.MessageDB.Name = "missevan_message"
	conf.PayDB.Name = "missevan_pay"

	_, testdataDir, _, _ := runtime.Caller(0)
	testdataDir = path.Join(path.Dir(testdataDir), "../testdata/")
	logger.Debugf("testdataDir: %s", testdataDir)

	conf.IPIP = util.IPIPConfig{
		DBIPv4:   path.Join(testdataDir, "ipip", "city.free.ipdb"),
		DBIPv6:   "",
		Language: util.IPIPLanguageCN,
	}
	conf.UserAgentParser = util.UserAgentParserConfig{
		RegexesFile: path.Join(testdataDir, "parseuseragent", "regexes.yaml"),
	}

	err := Init(conf)
	if err != nil {
		logger.Fatal(err)
	}
	servicedb.ApplyTestHooks(DB)

	// TODO: read form config
	Databus.AppLogPub = databus.TestNew(&databus.Config{Action: "pub"})
	Databus.AppLogSub = databus.TestNew(&databus.Config{Action: "sub"})
	Databus.LiveLogPub = databus.TestNew(&databus.Config{Action: "pub"})

	Smartsheet = smartsheet.NewClient(smartsheet.Config{
		URL: "http://127.0.0.1:9003",
	})
}

// InitTest 初始化单元测试
func InitTest() {
	InitTestService()

	_, file, _, _ := runtime.Caller(1)
	dbFile := path.Dir(file) + "/test.db" // db 放在每个单元测试的包目录下
	dbMessageFile := path.Dir(file) + "/message.db"
	dbMainFile := path.Dir(file) + "/main.db"
	dbLogFile := path.Dir(file) + "/log.db"
	dbDramaFile := path.Dir(file) + "/drama.db"
	dbPayFile := path.Dir(file) + "/pay.db"

	_, file, _, _ = runtime.Caller(0)
	queryFile := path.Dir(file) + "/../testdata/test.sql" // query 共用同一个
	queryMessageFile := path.Dir(file) + "/../testdata/test_missevan_message.sql"
	queryMainFile := path.Dir(file) + "/../testdata/test_missevan_main.sql"
	queryLogFile := path.Dir(file) + "/../testdata/test_missevan_log.sql"
	queryDramaFile := path.Dir(file) + "/../testdata/test_missevan_radio_drama.sql"
	queryPayFile := path.Dir(file) + "/../testdata/test_missevan_pay.sql"

	DB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbFile,
		QueryFile: queryFile,
	}, DB)

	MessageDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbMessageFile,
		QueryFile: queryMessageFile,
	}, MessageDB)

	LogDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbLogFile,
		QueryFile: queryLogFile,
	}, LogDB)

	MainDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbMainFile,
		QueryFile: queryMainFile,
	}, MainDB)

	DramaDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbDramaFile,
		QueryFile: queryDramaFile,
	}, DramaDB)

	PayDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPayFile,
		QueryFile: queryPayFile,
	}, PayDB)

	servicedb.Driver = servicedb.DriverSqlite
}

// Init services
func Init(conf *Config) (err error) {
	dbDefaultConfig := servicedb.Config{}
	DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	if conf.MessageDB != dbDefaultConfig {
		MessageDB, err = servicedb.InitDatabase(&conf.MessageDB)
		if err != nil {
			return
		}
	}
	if conf.LogDB != dbDefaultConfig {
		LogDB, err = servicedb.InitDatabase(&conf.LogDB)
		if err != nil {
			return
		}
	}
	if conf.DramaDB != dbDefaultConfig {
		DramaDB, err = servicedb.InitDatabase(&conf.DramaDB)
		if err != nil {
			return
		}
	}
	if conf.VoiceDB != dbDefaultConfig {
		VoiceDB, err = servicedb.InitDatabase(&conf.VoiceDB)
		if err != nil {
			return
		}
	}
	if conf.PayDB != dbDefaultConfig {
		PayDB, err = servicedb.InitDatabase(&conf.PayDB)
		if err != nil {
			return
		}
	}
	Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}
	LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return
	}
	MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return err
	}
	PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return err
	}
	SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return err
	}
	Gaia, err = servicegaia.NewClient(conf.Gaia)
	if err != nil {
		return err
	}

	Govern, err = servicegovern.NewClient(conf.Govern)
	if err != nil {
		return err
	}
	ShortURL, err = serviceshorturl.NewClient(conf.ShortURL)
	if err != nil {
		return err
	}

	Storage = storage.NewClient(conf.Storage)
	AntiSpam, err = antispam.NewClient(&conf.AntiSpam)
	if err != nil {
		return
	}
	AntiSpamV2, err = antispamv2.NewClient(&conf.AntiSpamV2)
	if err != nil {
		return
	}
	Geetest = geetest.NewClient(conf.Geetest)
	GeetestLowRisk = geetest.NewClient(conf.GeetestLowRisk)
	OpenSearch, err = search.NewClient(&conf.OpenSearch)
	if err != nil {
		return
	}

	GeoIP, err = serviceutil.NewIPIPReader(&conf.IPIP)
	if err != nil {
		return
	}

	UserAgentParser, err = serviceutil.NewUserAgentParser(&conf.UserAgentParser)
	if err != nil {
		return
	}

	Captcha, err = captcha.NewClient(conf.Captcha)
	if err != nil {
		return
	}
	return nil
}
