package liveserviceredis

import (
	"fmt"
	"math"
	"strconv"
	"time"

	redis "github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// TODO: go:embed ratelimit.lua
var ratelimitLuaScript string

func init() {
	ratelimitLuaScript = `
-- valueKey | capacity quantum intervalMS amount
local capacity   = tonumber(ARGV[1])
local quantum    = tonumber(ARGV[2])
local intervalMS = tonumber(ARGV[3])
local amount     = tonumber(ARGV[4])

local availableTokens
local lastTick

-- Use effects replication, not script replication;; this allows us to call 'TIME' which is non-deterministic
redis.replicate_commands()

local time = redis.call('TIME')
local nowMS = math.floor((time[1] * 1000) + (time[2] / 1000))
local initialTokens = redis.call('HGET', KEYS[1], 'tokens')
local initialTick = false

if initialTokens == false then
	-- If we found no record, we temporarily rewind the clock to refill
	-- via addTokens below
	availableTokens = 0
	lastTick = math.floor(nowMS / intervalMS) - 1
else
	availableTokens = tonumber(initialTokens)
	initialTick = redis.call('HGET', KEYS[1], 'tick')

	if initialTick == false then -- this is a corruption
		-- we make up a time that would fill this limit via addTokens below
		availableTokens = 0
		lastTick = math.floor(nowMS / intervalMS) - 1
	else
		lastTick = tonumber(initialTick)
	end
end

if availableTokens < capacity then
	local tick = math.floor(nowMS / intervalMS)
	local addTokens = (tick - lastTick) * quantum
	lastTick = tick
	availableTokens = availableTokens + addTokens
	if availableTokens > capacity then
		availableTokens = capacity
	end
end

local waitTimeMS = 0
local avail = availableTokens - amount
if avail < 0 then
	-- Round up the missing tokens to the nearest multiple
	-- of quantum - the tokens won't be available until
	-- that tick.

	-- endTick holds the tick when all the requested tokens will
	-- become available.
	waitTimeMS = math.floor((-avail+quantum-1) * intervalMS / quantum)
end

availableTokens = avail

redis.call('HMSET', KEYS[1], 'tokens', availableTokens, 'tick', lastTick)
redis.call('PEXPIRE', KEYS[1], waitTimeMS + intervalMS)

return waitTimeMS
`
}

// Time is measured in equal measured ticks, a given interval
// (fillInterval) apart. On each tick a number of tokens (quantum) are
// added to the bucket.
//
// When any of the methods are called the bucket updates the number of
// tokens that are in the bucket, and it records the current tick
// number too. Note that it doesn't record the current time - by
// keeping things in units of whole ticks, it's easy to dish out tokens
// at exactly the right intervals as measured from the start time.
//
// This allows us to calculate the number of tokens that will be
// available at some time in the future with a few simple arithmetic
// operations.
//
// The main reason for being able to transfer multiple tokens on each tick
// is so that we can represent rates greater than 1e9 (the resolution of the Go
// time package) tokens per second, but it's also useful because
// it means we can easily represent situations like "a person gets
// five tokens an hour, replenished on the hour".
//
// Reference: https://github.com/juju/ratelimit

// RedisBucket represents a token bucket in redis that fills at a predetermined
// rate. Methods on Bucket may be called concurrently.
type RedisBucket struct {
	name string
	c    *redis.Client
	s    *redis.Script

	// capacity holds the overall capacity of the bucket.
	capacity int64

	// quantum holds how many tokens are added on
	// each tick.
	quantum int64

	// fillInterval holds the interval between each tick.
	fillInterval time.Duration
}

// rateMargin specifes the allowed variance of actual
// rate from specified rate. 1% seems reasonable.
const rateMargin = 0.01

// NewRedisBucket returns a new token bucket that fills at the
// rate of one token every fillInterval, up to the given
// maximum capacity. Both arguments must be
// positive. The bucket is initially empty.
func NewRedisBucket(c *redis.Client, name string, fillInterval time.Duration, capacity int64) *RedisBucket {
	return NewRedisBucketWithQuantum(c, name, fillInterval, capacity, 1)
}

// NewRedisBucketWithQuantum is similar to NewBucket, but allows
// the specification of the quantum size - quantum tokens
// are added every fillInterval.
func NewRedisBucketWithQuantum(c *redis.Client, name string, fillInterval time.Duration, capacity, quantum int64) *RedisBucket {
	if fillInterval <= 0 {
		panic("token bucket fill interval is not > 0")
	}
	if capacity <= 0 {
		panic("token bucket capacity is not > 0")
	}
	if quantum <= 0 {
		panic("token bucket quantum is not > 0")
	}
	return &RedisBucket{
		name:         name,
		c:            c,
		s:            redis.NewScript(ratelimitLuaScript),
		fillInterval: fillInterval,
		capacity:     capacity,
		quantum:      quantum,
	}
}

// NewRedisBucketWithRate returns a token bucket that fills the bucket
// at the rate of rate tokens per second up to the given
// maximum capacity. Because of limited clock resolution,
// at high rates, the actual rate may be up to 1% different from the
// specified rate.
func NewRedisBucketWithRate(c *redis.Client, name string, rate float64, capacity int64) *RedisBucket {
	// Use the same bucket each time through the loop
	// to save allocations.
	tb := NewRedisBucketWithQuantum(c, name, 1, capacity, 1)
	for quantum := int64(1); quantum < 1<<50; quantum = nextQuantum(quantum) {
		fillInterval := time.Duration(
			time.Duration(1e9*float64(quantum)/rate).
				Milliseconds()) * time.Millisecond
		if fillInterval <= 0 {
			continue
		}
		tb.fillInterval = fillInterval
		tb.quantum = quantum
		if diff := math.Abs(tb.Rate() - rate); diff/rate <= rateMargin {
			return tb
		}
	}
	panic("cannot find suitable quantum for " + strconv.FormatFloat(rate, 'g', -1, 64))
}

// Wait takes count tokens from the bucket, waiting until they are
// available.
func (tb *RedisBucket) Wait(count int64) {
	if d := tb.Take(count); d > 0 {
		time.Sleep(d)
	}
}

// Take takes count tokens from the bucket without blocking. It returns
// the time that the caller should wait until the tokens are actually
// available.
//
// Note that if the request is irrevocable - there is no way to return
// tokens to the bucket once this method commits us to taking them.
func (tb *RedisBucket) Take(count int64) time.Duration {
	d, err := tb.take(count)
	if err != nil {
		logger.Errorf("ratelimit bucket %q take error: %v", tb.name, err)
		// PASS
	}
	return d
}

// Capacity returns the capacity that the bucket was created with.
func (tb *RedisBucket) Capacity() int64 {
	return tb.capacity
}

// Rate returns the fill rate of the bucket, in tokens per second.
func (tb *RedisBucket) Rate() float64 {
	return 1e9 * float64(tb.quantum) / float64(tb.fillInterval)
}

// nextQuantum returns the next quantum to try after q.
// We grow the quantum exponentially, but slowly, so we
// get a good fit in the lower numbers.
func nextQuantum(q int64) int64 {
	q1 := q * 11 / 10
	if q1 == q {
		q1++
	}
	return q1
}

// take is the internal version of Take
func (tb *RedisBucket) take(count int64) (time.Duration, error) {
	if count <= 0 {
		return 0, nil
	}

	if tb.s == nil {
		return 0, nil
	}

	args := []interface{}{
		tb.capacity, tb.quantum, tb.fillInterval.Milliseconds(), count,
	}
	wait, err := tb.s.Run(tb.c, []string{fmt.Sprintf("ratelimit:%s", tb.name)}, args...).Int64()
	if err != nil {
		return 0, err
	}

	return time.Duration(wait) * time.Millisecond, nil
}
