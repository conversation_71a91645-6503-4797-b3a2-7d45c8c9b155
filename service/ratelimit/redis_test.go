package liveserviceredis

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	service.InitTestService()
	m.Run()
}

type testBucket struct {
	b *RedisBucket

	v       int64
	target  int64
	closeCh chan struct{}
	wg      *sync.WaitGroup
}

func (tb *testBucket) consume() {
	defer tb.wg.Done()
	for {
		select {
		case <-tb.closeCh:
			return
		default:
		}
		d := tb.b.Take(1)
		time.Sleep(d)
		newV := atomic.AddInt64(&tb.v, 1)
		if newV == tb.target {
			close(tb.closeCh)
		}
	}
}

func (tb *testBucket) add(delta int) {
	tb.wg.Add(delta)
}

func (tb *testBucket) wait() {
	tb.wg.Wait()
}

func TestRedisBucket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := NewRedisBucketWithRate(service.Redis, "test", 100, 100)
	require.NotNil(b)
	assert.EqualValues(1, b.quantum)
	assert.EqualValues(100, b.Capacity())
	assert.EqualValues(100, b.Rate())

	t0 := goutil.TimeNow()

	tb := testBucket{
		b: b,

		target:  100,
		closeCh: make(chan struct{}),
		wg:      &sync.WaitGroup{},
	}
	for i := 0; i < 4; i++ {
		tb.add(1)
		go tb.consume()
	}
	tb.wait()

	d := time.Since(t0)
	assert.GreaterOrEqual(d, time.Microsecond*1000)
	// may depends networks (+50%)
	assert.Less(d, time.Millisecond*1500)
}
