package servicedb

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSQLColumn_NewSQLColumn(t *testing.T) {
	assert := assert.New(t)

	sc := NewSQLColumn("test")
	assert.Equal("test", sc.column)
}

func TestSQLColumn_Alias(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.<PERSON>("test_alias")

	assert.Equal("test AS test_alias", sc.column)
}

func TestSQLColumn_Coalesce(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.Coalesce("0")

	assert.Equal("COALESCE(test, 0)", sc.column)
}

func TestSQLColumn_CountDistinct(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.CountDistinct()
	assert.Equal("COUNT(DISTINCT test)", sc.column)
}

func TestSQLColumn_Count(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.Count()
	assert.Equal("COUNT(test)", sc.column)
}

func TestSQLColumn_Sum(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.Sum()

	assert.Equal("SUM(test)", sc.column)
}

func TestSQLColumn_Floor(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.Floor()

	assert.Equal("FLOOR(test)", sc.column)
}

func TestSQLColumn_Round(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.Round()

	assert.Equal("ROUND(test)", sc.column)
}

func TestSQLColumn_DivideBy(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.DivideBy(100)

	assert.Equal("(test / 100)", sc.column)
}

func TestSQLColumn_MultiplyBy(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.MultiplyBy(1000)

	assert.Equal("(test * 1000)", sc.column)
}

func TestSQLColumn_If(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{column: "test"}
	sc.If("a > b", "888", "777")

	assert.Equal("IF(a > b, 888, 777)", sc.column)
}

func TestSQLColumn_String(t *testing.T) {
	assert := assert.New(t)

	sc := &SQLColumn{}
	sc.If("attr = 1", "income - tax", "0").
		MultiplyBy(1000).
		Round().
		DivideBy(10).
		Floor().
		DivideBy(100).
		Sum().
		Coalesce("0").
		Alias("noble_income")

	assert.Equal("COALESCE(SUM((FLOOR((ROUND((IF(attr = 1, income - tax, 0) * 1000)) / 10)) / 100)), 0) AS noble_income", sc.String())
}
