package servicedb

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var regSQLLike = strings.NewReplacer(`%`, `\%`, "_", `\_`, `\`, `\\`)

// ToLikeStr mysql 模糊查询用
// str 进行了如下转义:
// % => \%
// _ => \_
// \ => \\
func ToLikeStr(str string) string {
	return "%" + regSQLLike.Replace(str) + "%"
}

// ToRightLikeStr 模糊查询
func ToRightLikeStr(str string) string {
	return regSQLLike.Replace(str) + "%"
}

// ToLeftLikeStr 模糊查询
func ToLeftLikeStr(str string) string {
	return "%" + regSQLLike.Replace(str)
}

// ErrNotSupport is the error of unsupported data type being used in BatchInsert
var ErrNotSupport = errors.New("only support insert []T or []*T")

// SplitBatchInsert inserts records into db table.
// chunkSize = 65535 / average_num_of_columns_per_record. 1000 is recommended.
func SplitBatchInsert(db *gorm.DB, table string, records interface{}, chunkSize int, ignoreSQLErr bool) error {
	kind := reflect.TypeOf(records).Kind()
	if kind != reflect.Slice {
		return ErrNotSupport
	}
	rows := reflect.ValueOf(records)
	for i := 0; i < rows.Len(); {
		j := i + chunkSize
		if j > rows.Len() {
			j = rows.Len()
		}

		err := BatchInsert(db, table, rows.Slice(i, j).Interface())
		if err != nil {
			logger.Errorf("SplitBatchInsert(%s, %d)[%d:%d] failed with error: %v", table, rows.Len(), i, j, err)
			if !ignoreSQLErr {
				return err
			}
		}

		i = j
	}
	return nil
}

// BatchInsert inserts rows into table (rows should be []T or []*T).
// NOTICE: Prepared statement has a limit of maximum 65535 placeholders. len(rows) should not be too large.
func BatchInsert(db *gorm.DB, table string, rows interface{}) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("%v", r)
		}
	}()

	kind := reflect.TypeOf(rows).Kind()
	if kind != reflect.Slice {
		return ErrNotSupport
	}

	sqlStr, values := genInsertSQL(table, rows)
	return db.Exec(sqlStr, values...).Error
}

func genInsertSQL(table string, rows interface{}) (string, []interface{}) {
	var (
		column     string
		needColumn = true
		needID     bool
		ignoreID   = func(i int, val interface{}) bool {
			if i == 0 {
				// NOTICE: 假定 id 的零值为 int64(0)
				needID = val != int64(0)
			} else if (val != int64(0)) != needID {
				panic("id 存在自增和赋值两种情况")
			}
			return !needID
		}

		values []interface{}
		raw    = reflect.ValueOf(rows)
		sql    = "INSERT INTO %s (%s) VALUES "
	)

	for i := 0; i < raw.Len(); i++ {
		val := reflect.ValueOf(raw.Index(i).Interface())
		for val.Kind() == reflect.Ptr {
			val = val.Elem()
		}
		if i == 0 {
			values = make([]interface{}, 0, raw.Len()*val.NumField())
		}

		line := "("
		tp := reflect.Indirect(val).Type()
		for j := 0; j < val.NumField(); j++ {
			colname := parseTag(tp.Field(j).Tag)["COLUMN"]
			if colname != "" {
				val := val.Field(j).Interface()
				if colname == "id" && ignoreID(i, val) {
					continue
				}
				if needColumn {
					column += "`" + colname + "`, "
				}
				line += "?, "
				values = append(values, val)
			}
		}
		line = strings.TrimSuffix(line, ", ")
		line += "), "
		sql += line
		needColumn = false
	}

	sql = strings.TrimSuffix(sql, ", ")
	column = strings.TrimSuffix(column, ", ")
	return fmt.Sprintf(sql, table, column), values
}

func parseTag(tags reflect.StructTag) map[string]string {
	setting := map[string]string{}
	str := tags.Get("gorm")
	if str != "" {
		tags := strings.Split(str, ";")
		for _, value := range tags {
			v := strings.Split(value, ":")
			k := strings.TrimSpace(strings.ToUpper(v[0]))
			if len(v) >= 2 {
				setting[k] = strings.Join(v[1:], ":")
			} else {
				setting[k] = k
			}
		}
	}
	return setting
}

// IsUniqueError 异常是否是唯一索引保存
// 为 true 是唯一索引异常
func IsUniqueError(err error) bool {
	if Driver == DriverSqlite {
		return strings.HasPrefix(err.Error(), "UNIQUE constraint failed:")
	}
	return strings.HasPrefix(err.Error(), "Error 1062:") || // gorm v1
		strings.HasPrefix(err.Error(), "Error 1062 ") // gorm v2: `Error 1062 (23000): Duplicate entry...`
}

// ForUpdate MySQL 增加 FOR UPDATE 行锁
// FOR UPDATE 只有根据主键查询才是行锁，其余是表锁
// gorm Set 方法里面有 clone 不会影响其他查询语句
func ForUpdate(tx *gorm.DB) (txRes *gorm.DB) {
	switch Driver {
	case DriverSqlite:
		txRes = tx
	case DriverMysql:
		fallthrough
	default:
		txRes = tx.Set("gorm:query_option", "FOR UPDATE")
	}
	return
}
