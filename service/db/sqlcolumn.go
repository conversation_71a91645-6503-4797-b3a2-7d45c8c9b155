package servicedb

import (
	"fmt"
)

// SQLColumn represents a SQL column
type SQLColumn struct {
	column string
}

// NewSQLColumn creates a new SQLColumn instance
func NewSQLColumn(column string) *SQLColumn {
	return &SQLColumn{column: column}
}

// <PERSON><PERSON> sets an alias for the column
func (s *SQLColumn) Alias(alias string) *SQLColumn {
	s.column = fmt.Sprintf("%s AS %s", s.column, alias)
	return s
}

// Coalesce sets a default value for the column
func (s *SQLColumn) Coalesce(defaultValue string) *SQLColumn {
	s.column = fmt.Sprintf("COALESCE(%s, %s)", s.column, defaultValue)
	return s
}

// CountDistinct counts the column with distinct values
func (s *SQLColumn) CountDistinct() *SQLColumn {
	s.column = fmt.Sprintf("COUNT(DISTINCT %s)", s.column)
	return s
}

// Count counts the column
func (s *SQLColumn) Count() *SQLColumn {
	s.column = fmt.Sprintf("COUNT(%s)", s.column)
	return s
}

// Sum sums the column
func (s *SQLColumn) Sum() *SQLColumn {
	s.column = fmt.Sprintf("SUM(%s)", s.column)
	return s
}

// Floor rounds the column down
func (s *SQLColumn) Floor() *SQLColumn {
	s.column = fmt.Sprintf("FLOOR(%s)", s.column)
	return s
}

// Round rounds the column
func (s *SQLColumn) Round() *SQLColumn {
	s.column = fmt.Sprintf("ROUND(%s)", s.column)
	return s
}

// DivideBy divides the column by a value
func (s *SQLColumn) DivideBy(val int64) *SQLColumn {
	s.column = fmt.Sprintf("(%s / %d)", s.column, val)
	return s
}

// MultiplyBy multiplies the column by a value
func (s *SQLColumn) MultiplyBy(val int64) *SQLColumn {
	s.column = fmt.Sprintf("(%s * %d)", s.column, val)
	return s
}

// If sets a condition for the column
func (s *SQLColumn) If(condSQL, trueVal, falseVal string) *SQLColumn {
	s.column = fmt.Sprintf("IF(%s, %s, %s)", condSQL, trueVal, falseVal)
	return s
}

// String returns the column as a string
func (s SQLColumn) String() string {
	return s.column
}
