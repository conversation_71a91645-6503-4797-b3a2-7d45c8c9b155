package servicedb

import (
	"database/sql"
	"fmt"
	"runtime"
	"strconv"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	gorm2 "gorm.io/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
)

const userIDForMysql = 22330

func TestIsErrNoRows(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsErrNoRows(sql.ErrNoRows))
	assert.True(IsErrNoRows(gorm.ErrRecordNotFound))
	assert.True(IsErrNoRows(gorm2.ErrRecordNotFound))
	assert.False(IsErrNoRows(nil))
	assert.False(IsErrNoRows(ErrNoRowsAffected))
}

func TestConfig_InitDefaults(t *testing.T) {
	assert := assert.New(t)

	c := new(Config)
	c.InitDefaults()
	assert.Equal(defaultPort, c.Port)
	assert.Equal(DefaultMaxIdleConns(), c.<PERSON>on<PERSON>)
	assert.Equal(defaultMaxLifeTime, c.MaxLifeTime)
}

func TestInitDatabase(t *testing.T) {
	assert := assert.New(t)

	// 错误：配置为空
	_, err := InitDatabase(&Config{
		URL:         "",
		MaxLifeTime: "10s",
	})
	assert.EqualError(err, "db config should not be empty")

	// 正常情况：通过 url 方式
	db, err := InitDatabase(&Config{
		URL:         "mysql://root:rootmysql@tcp(mysql.srv.maoer.co:3306)/app_missevan?charset=utf8mb4,utf8",
		MaxLifeTime: "10s",
	})
	assert.NoError(err)
	assert.NotNil(db)
	db.Close()

	// 正常情况：host, port, user 等分别指定方式
	db, err = InitDatabase(&Config{
		Host:        "mysql.srv.maoer.co",
		Port:        3306,
		Name:        "app_missevan",
		User:        "root",
		Pass:        "rootmysql",
		MaxLifeTime: "10s",
	})
	assert.NoError(err)
	assert.NotNil(db)
	db.Close()
}

func TestInitDatabase2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 错误：配置为空
	_, err := InitDatabase2(&Config{})
	assert.EqualError(err, "db config should not be empty")

	// 正常情况：通过 url 方式
	db, err := InitDatabase2(&Config{
		URL: "mysql://root:rootmysql@tcp(mysql.srv.maoer.co:3306)/app_missevan?charset=utf8mb4,utf8",
	})
	assert.NoError(err)
	require.NotNil(db)
	sqlDB, err := db.DB()
	require.NoError(err)
	require.NotNil(sqlDB)
	assert.NoError(sqlDB.Close())

	// 正常情况：host, user 等分别指定方式
	db, err = InitDatabase2(&Config{
		Host: "mysql.srv.maoer.co",
		Name: "app_missevan",
		User: "root",
		Pass: "rootmysql",
	})
	assert.NoError(err)
	require.NotNil(db)
	sqlDB, err = db.DB()
	require.NoError(err)
	require.NotNil(sqlDB)
	assert.NoError(sqlDB.Close())
}

func TestGetDriverAndDSN(t *testing.T) {
	assert := assert.New(t)

	var (
		driver string
		dsn    string
		err    error
	)
	// 错误：配置为空
	conf := &Config{}
	_, _, err = conf.getDriverAndDSN()
	assert.EqualError(err, "db config should not be empty")
	conf.Host = "localhost"
	conf.Name = "test"
	conf.User = "root"
	conf.Pass = "123456"
	_, _, err = conf.getDriverAndDSN()
	assert.EqualError(err, "db config should not be empty")

	// 正常情况：host, port, user 等分别指定方式
	conf.Port = 3306
	driver, dsn, err = conf.getDriverAndDSN()
	assert.NoError(err)
	assert.Equal(defaultDriver, driver)
	assert.Equal("root:123456@tcp(localhost:3306)/test?charset="+defaultCharset+"&parseTime="+
		strconv.FormatBool(defaultParseTime)+"&loc="+defaultLoc, dsn)

	// 错误：url 格式不对
	conf2 := &Config{
		URL: "test",
	}
	_, _, err = conf2.getDriverAndDSN()
	assert.EqualError(err, "db url should be configured such as 'mysql://user:password@tcp(host:port)/dbname?param1=value1'")

	// 错误：仅支持 mysql
	conf2.URL = "sqlite3://test"
	_, _, err = conf2.getDriverAndDSN()
	assert.EqualError(err, "db driver only support mysql currently")

	// 错误：dsn 应不为空
	conf2.URL = "mysql://"
	_, _, err = conf2.getDriverAndDSN()
	assert.EqualError(err, "db dsn is empty")

	// 正常情况：通过 url 方式
	conf2.URL = "mysql://root:123456@tcp(localhost:3306)/test?charset=utf8"
	driver, dsn, err = conf2.getDriverAndDSN()
	assert.NoError(err)
	assert.Equal(defaultDriver, driver)
	assert.Equal("root:123456@tcp(localhost:3306)/test?charset=utf8", dsn)
}

func TestIFExpr(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	defer func() { Driver = defaultDriver }()

	Driver = DriverMysql
	expr := IFExpr("0", "1", "2")
	var r int
	queryString := fmt.Sprintf("SELECT %s AS r", expr)
	require.NoError(testDB.Raw(queryString).Row().Scan(&r))
	assert.Equal(2, r)

	Driver = DriverSqlite
	expr = IFExpr("1", "3", "4")
	queryString = fmt.Sprintf("SELECT %s AS r", expr)
	require.NoError(sqliteDB.Raw(queryString).Row().Scan(&r))
	assert.Equal(3, r)
}

func TestGreatestExpr(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	defer func() { Driver = defaultDriver }()
	assert.Panics(func() { GreatestExpr("0") }, errParams)
	Driver = DriverMysql
	var r int
	queryString := fmt.Sprintf("SELECT %s AS r", GreatestExpr("3", "4"))
	require.NoError(testDB.Raw(queryString).Row().Scan(&r))
	assert.Equal(4, r)

	Driver = DriverSqlite
	require.NoError(sqliteDB.Raw(fmt.Sprintf("SELECT %s AS r", GreatestExpr("5", "6"))).Row().Scan(&r))
	assert.Equal(6, r)
}

func TestLeastExpr(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	defer func() { Driver = defaultDriver }()
	assert.Panics(func() { LeastExpr("0") }, errParams)
	Driver = DriverMysql
	var r int
	queryString := fmt.Sprintf("SELECT %s AS r", LeastExpr("3", "4"))
	require.NoError(testDB.Raw(queryString).Row().Scan(&r))
	assert.Equal(3, r)

	Driver = DriverSqlite
	require.NoError(sqliteDB.Raw(fmt.Sprintf("SELECT %s AS r", LeastExpr("5", "6"))).Row().Scan(&r))
	assert.Equal(5, r)
}

func TestUpdateOnDuplicateKeyExpr(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	defer func() { Driver = defaultDriver }()
	now := util.TimeNow().Unix()
	updates := UpdateOnDuplicateKeyExpr(fmt.Sprintf("modified_time = %d", now), "last_name, first_name")
	assert.Equal(fmt.Sprintf("ON DUPLICATE KEY UPDATE modified_time = %d", now), updates)
	Driver = DriverMysql
	queryExpr := fmt.Sprintf(`INSERT INTO live_addendum (id, vitality, last_punished_time, is_agreed,
		agree_guild_agreement_time, agree_live_agreement_time, create_time, modified_time)
	VALUES (%d, 11, 0, 0, 0, 0, 1584935815, 1584935815) %s`, userIDForMysql, updates)
	require.NoError(testDB.Exec(queryExpr).Error)
	// mysql 有延迟，所以只看是否报错

	Driver = DriverSqlite
	expr := UpdateOnDuplicateKeyExpr(`iconurl="test1.png"`, "last_name, first_name")
	assert.Equal(`ON CONFLICT(last_name, first_name) DO UPDATE SET iconurl="test1.png"`, expr)
	execQuery := fmt.Sprintf(`INSERT INTO user (id, last_name, first_name, iconurl, age) VALUES (2, "A", "B", "test.png", 21) %s`, expr)
	require.NoError(sqliteDB.Exec(execQuery).Error)
	var r string
	require.NoError(sqliteDB.Raw(fmt.Sprintf(`SELECT iconurl FROM user WHERE last_name = "%s" and first_name = "%s"`, "A", "B")).Row().Scan(&r))
	assert.Equal("test1.png", r)
}

func TestSubSatExpr(t *testing.T) {
	assert := assert.New(t)

	defer func() { Driver = defaultDriver }()
	Driver = DriverMysql
	expr := SubSatExpr("point", 100)
	assert.Equal(gorm.Expr("GREATEST(point, 100) - 100"), expr)

	Driver = DriverSqlite
	expr = SubSatExpr("point", 100)
	assert.Equal(gorm.Expr("MAX(point, 100) - 100"), expr)
}

func TestExists(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	query := sqliteDB.Table("mowangskuser").Where("id = ?", 12).Debug()
	exists, err := Exists(query)
	require.NoError(err)
	assert.True(exists)

	query = sqliteDB.Table("mowangskuser").Where("id = ?", 88888888).Debug()
	exists, err = Exists(query)
	require.NoError(err)
	assert.False(exists)
}

func TestDefaultMaxIdleConns(t *testing.T) {
	assert := assert.New(t)

	assert.Greater(DefaultMaxIdleConns(), 2)

	runtime.GOMAXPROCS(1)
	assert.Equal(2, DefaultMaxIdleConns())
}
