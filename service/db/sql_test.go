package servicedb

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOrderByMapSupported_ParseParam(t *testing.T) {
	temp := OrderByMapSupported{
		"guild_id.desc": {
			FieldMapping: "id",
			OrderBy:      orderByDesc,
		},
		"id.asc": {
			FieldMapping: "id",
			OrderBy:      orderByAsc,
		},
	}

	tests := []struct {
		name     string
		input    string
		expected *OrderByItem
	}{
		{
			name:     "non-existent",
			input:    "non-existent",
			expected: nil,
		},
		{
			name:     "guild_id.desc",
			input:    "guild_id.desc",
			expected: &OrderByItem{"id", orderByDesc},
		},
		{
			name:     "id.asc",
			input:    "id.asc",
			expected: &OrderByItem{"id", orderByAsc},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			item := temp.ParseParam(tt.input)
			require.Equal(t, tt.expected, item)
		})
	}
}

func TestOrderByItem_String(t *testing.T) {
	tests := []struct {
		name     string
		input    OrderByItem
		expected string
	}{
		{
			name:     "asc",
			input:    OrderByItem{"id", orderByAsc},
			expected: "id ASC",
		},
		{
			name:     "desc",
			input:    OrderByItem{"guild_id", orderByDesc},
			expected: "guild_id DESC",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.input.String())
		})
	}
}

func TestMakeOrderByMapSupported(t *testing.T) {
	tests := []struct {
		name            string
		sortFields      []string
		fieldMappingMap map[string]string
		len             int
		expected        OrderByMapSupported
	}{
		{
			name:            "single field",
			sortFields:      []string{"guild_id"},
			fieldMappingMap: nil,
			len:             2,
			expected: OrderByMapSupported{
				"guild_id.asc": OrderByItem{
					FieldMapping: "guild_id",
					OrderBy:      orderByAsc,
				},
				"guild_id.desc": OrderByItem{
					FieldMapping: "guild_id",
					OrderBy:      orderByDesc,
				},
			},
		},
		{
			name:            "multiple fields",
			sortFields:      []string{"guild_id.desc", "creator_id.asc", "income"},
			fieldMappingMap: map[string]string{"guild_id": "id", "income": "inocme_v2"},
			len:             4,
			expected: OrderByMapSupported{
				"guild_id.desc": OrderByItem{
					FieldMapping: "id",
					OrderBy:      orderByDesc,
				},
				"creator_id.asc": OrderByItem{
					FieldMapping: "creator_id",
					OrderBy:      orderByAsc,
				},
				"income.asc": OrderByItem{
					FieldMapping: "inocme_v2",
					OrderBy:      orderByAsc,
				},
				"income.desc": OrderByItem{
					FieldMapping: "inocme_v2",
					OrderBy:      orderByDesc,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sortMap := MakeOrderByMapSupported(tt.sortFields, tt.fieldMappingMap)
			require.Len(t, sortMap, tt.len)
			assert.Equal(t, tt.expected, sortMap)
		})
	}
}
