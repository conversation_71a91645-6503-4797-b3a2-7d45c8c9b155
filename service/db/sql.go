package servicedb

import (
	"strings"
)

const (
	fieldByAsc  = ".asc"
	fieldByDesc = ".desc"

	orderByAsc  = "ASC"
	orderByDesc = "DESC"
)

// OrderByItem 排序字段
type OrderByItem struct {
	FieldMapping string // 数据库中的字段名
	OrderBy      string // 排序规则：ASC 或 DESC
}

// String OrderByItem 转换为排序字符串
func (oi OrderByItem) String() string {
	return oi.FieldMapping + " " + oi.OrderBy
}

// OrderByMapSupported 排序字段集合
type OrderByMapSupported map[string]OrderByItem

// ParseParam 解析传入的排序字符串，将其转换为数据库查询可识别的字段名和排序方式。
// 推荐使用 MakeOrderByMapSupported 生成 map
/*
OrderByMapSupported{
	"guild_id.desc": OrderByItem{
		FieldMapping: "id",
		OrderBy:      "DESC",
	},
}.ParseParam("guild_id.desc")
return &OrderByItem{
	FieldMapping: "id",
	OrderBy:      "DESC",
}
*/
func (o OrderByMapSupported) ParseParam(orderByParam string) *OrderByItem {
	if orderItem, exists := o[orderByParam]; exists {
		return &orderItem
	}
	return nil
}

// MakeOrderByMapSupported 生成一个排序字段的映射表。
// sortFields 是一个包含排序字段的字符串切片，每个字段可以带有 ".asc" 或 ".desc" 后缀来指定排序方式。
// fieldMappingMap 是一个可选的字段映射表，用于将输入的字段名映射到数据库中的实际字段名。
/*
MakeOrderByMapSupported([]string{"guild_id.desc", "creator_id.asc", "income"}, map[string]string{"guild_id": "id", "income": "inocme_v2"})
return OrderByMapSupported{
	"guild_id.desc": OrderByItem{
		FieldMapping: "id",
		OrderBy:      "DESC",
	},
	"creator_id.asc": OrderByItem{
		FieldMapping: "creator_id",
		OrderBy:      "ASC",
	},
	"income.asc": OrderByItem{
		FieldMapping: "inocme_v2",
		OrderBy:      "ASC",
	},
	"income.desc": OrderByItem{
		FieldMapping: "inocme_v2",
		OrderBy:      "DESC",
	},
}
*/
func MakeOrderByMapSupported(sortFields []string, fieldMappingMap map[string]string) OrderByMapSupported {
	m := make(OrderByMapSupported, len(sortFields)*2)
	for _, field := range sortFields {
		items := strings.SplitN(field, ".", 2)
		fieldMapping := items[0]

		if fieldMappingMap != nil {
			if f, ok := fieldMappingMap[items[0]]; ok {
				fieldMapping = f
			}
		}

		if !strings.HasSuffix(field, fieldByAsc) && !strings.HasSuffix(field, fieldByDesc) {
			m[field+fieldByAsc] = OrderByItem{
				FieldMapping: fieldMapping,
				OrderBy:      orderByAsc,
			}
			m[field+fieldByDesc] = OrderByItem{
				FieldMapping: fieldMapping,
				OrderBy:      orderByDesc,
			}
		} else {
			m[field] = OrderByItem{
				FieldMapping: fieldMapping,
				OrderBy:      strings.ToUpper(items[1]),
			}
		}
	}
	return m
}
