package servicedb

import (
	"errors"
	"fmt"
	"runtime/debug"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Tx wraps f with transactions. It will rollback if err is not nil, or if any panic occurs.
// Otherwise it will commit.
func Tx(db *gorm.DB, f func(tx *gorm.DB) error) (err error) {
	// Doc: http://gorm.io/docs/transactions.html#A-Specific-Example
	tx := db.Begin()

	defer func() {
		if r := recover(); r != nil {
			if erro := tx.Rollback().Error; erro != nil {
				logger.Errorf("Rollback error: %v", erro)
				// PASS
			}

			stack := util.IgnoreUntil(string(debug.Stack()), 7, '\n')
			logger.Errorf("Tx recovery: \n%v\n%s", r, stack)
			err = errors.New(fmt.Sprint(r))
		}
	}()
	if err := tx.Error; err != nil {
		return err
	}
	err = f(tx)

	if err != nil {
		if erro := tx.Rollback().Error; erro != nil {
			logger.Errorf("Rollback error: %v", erro)
			// PASS
		}
		return err
	}

	// commit 后调用自定的 after_commit 回调
	defer func() {
		callback := db.Callback().Create().Get("missevan-go:after_commit_transaction")
		if callback != nil {
			// TODO: 使用 tx 的 scope 作为参数
			callback(nil)
		}
	}()

	return tx.Commit().Error
}

// Tx2 封装 gorm2 的事务操作
func Tx2(db *gorm2.DB, f func(tx *gorm2.DB) error) (err error) {
	defer func() {
		if r := recover(); r != nil {
			stack := util.IgnoreUntil(string(debug.Stack()), 7, '\n')
			logger.Errorf("Tx2 recovery: \n%v\n%s", r, stack)
			err = errors.New(fmt.Sprint(r))
		}
	}()
	return db.Transaction(f)
}
