package servicedb

import (
	"time"

	"github.com/jinzhu/gorm"
)

var mockDB func(db *gorm.DB)
var hasDirtyData bool
var syncDelayTime = time.Millisecond * 1000

// ApplyTestHooks replaces the original test hooks for test use
func ApplyTestHooks(db *gorm.DB) {
	hasDirtyData = false

	// 替换写入数据的回调，用于标记数据库
	db.Callback().Create().Replace("gorm:commit_or_rollback_transaction", testCommitOrRollbackTransactionCallback)
	db.Callback().Delete().Replace("gorm:commit_or_rollback_transaction", testCommitOrRollbackTransactionCallback)
	db.Callback().Update().Replace("gorm:commit_or_rollback_transaction", testCommitOrRollbackTransactionCallback)

	// 替换读取数据的回调
	db.Callback().Query().Before("gorm:query").Register("gorm:before_query", testBeforeQueryCallback)
	db.Callback().RowQuery().Before("gorm:row_query").Register("gorm:before_row_query", testBeforeQueryCallback)

	// 注册新的回调，用于在 Tx 结尾 commit 时标记数据库为 hasDirtyData
	db.Callback().Create().Register("missevan-go:after_commit_transaction", testAfterCommitTransaction)

	if mockDB != nil {
		mockDB(db)
	}
}

func testCommitOrRollbackTransactionCallback(scope *gorm.Scope) {
	_, naturalTx := scope.InstanceGet("gorm:started_transaction")
	// 在 Tx 函数中 naturalTx 为 false，不进行标记
	if naturalTx && scope.DB().RowsAffected > 0 {
		hasDirtyData = true
	}
	scope.CommitOrRollback()
}

func testAfterCommitTransaction(scope *gorm.Scope) {
	// TODO: 根据 scope 的 commit 是否出错判断是否要进行标记
	hasDirtyData = true
}

// queryCallback used to query data from database
func testBeforeQueryCallback(scope *gorm.Scope) {
	// 若数据库被标记为 hasDirtyData 则在读取前进行停顿，并在此后取消标记
	if hasDirtyData {
		time.Sleep(syncDelayTime)
		hasDirtyData = false
	}
}
