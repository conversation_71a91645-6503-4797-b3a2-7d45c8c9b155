package servicedb

import (
	"errors"
	"fmt"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestToLikeStr(t *testing.T) {
	assert := assert.New(t)

	likeStr := ToLikeStr(`魔道%_\`)
	assert.Equal(`%魔道\%\_\\%`, likeStr)
}

func TestToRightLikeStr(t *testing.T) {
	assert := assert.New(t)

	likeStr := ToRightLikeStr(`魔道%_\`)
	assert.Equal(`魔道\%\_\\%`, likeStr)
}

func TestToLeftLikeStr(t *testing.T) {
	assert := assert.New(t)

	likeStr := ToLeftLikeStr(`魔道%_\`)
	assert.Equal(`%魔道\%\_\\`, likeStr)
}

type Record struct {
	ID         int64  `json:"id" gorm:"column:id"`
	Name       string `json:"name" gorm:"column:name"`
	CreateTime int64  `json:"create_time" gorm:"column:create_time"`
}

func TestGenInsertSql(t *testing.T) {
	assert := assert.New(t)

	row1 := Record{ID: 1, Name: "row1", CreateTime: 11}
	row2 := Record{ID: 2, Name: "row2", CreateTime: 12}

	expectedSQL := "INSERT INTO collection (`id`, `name`, `create_time`) VALUES (?, ?, ?), (?, ?, ?)"
	expectedValues := []interface{}{1, "row1", 11, 2, "row2", 12}
	length := 6
	sql := ""
	var values []interface{}

	check := func() {
		assert.Equal(expectedSQL, sql)
		assert.Len(values, length)
		assert.Equal(6, cap(values))
		for k, v := range values {
			assert.Equal(fmt.Sprintf("%v", expectedValues[k]), fmt.Sprintf("%v", v), "value error", k)
		}
	}

	rows := []Record{row1, row2}
	sql, values = genInsertSQL("collection", rows)
	check()

	ptrRows := []*Record{&row1, &row2}
	sql, values = genInsertSQL("collection", ptrRows)
	check()

	row1 = Record{Name: "row1", CreateTime: 11}
	row2 = Record{Name: "row2", CreateTime: 12}
	expectedSQL = "INSERT INTO collection (`name`, `create_time`) VALUES (?, ?), (?, ?)"
	expectedValues = []interface{}{"row1", 11, "row2", 12}
	length = 4
	rows = []Record{row1, row2}
	sql, values = genInsertSQL("collection", rows)
	check()

	ptrRows = []*Record{&row1, &row2}
	sql, values = genInsertSQL("collection", ptrRows)
	check()

	// check panic
	assert.PanicsWithValue("id 存在自增和赋值两种情况", func() {
		row1 = Record{Name: "row1", CreateTime: 11}
		row2 = Record{ID: 1, Name: "row2", CreateTime: 12}
		rows = []Record{row1, row2}
		genInsertSQL("collection", rows)
	})

	assert.PanicsWithValue("id 存在自增和赋值两种情况", func() {
		row1 = Record{ID: 1, Name: "row1", CreateTime: 11}
		row2 = Record{Name: "row2", CreateTime: 12}
		rows = []Record{row1, row2}
		genInsertSQL("collection", rows)
	})
}

func TestBatchInsert(t *testing.T) {
	require := require.New(t)
	row1 := Record{ID: 1, Name: "row1", CreateTime: 11}
	row2 := Record{ID: 2, Name: "row2", CreateTime: 12}

	testLogDB.Exec(`CREATE TABLE IF NOT EXISTS app_missevan_log.collection (
		id BIGINT(10) NOT NULL,
		name VARCHAR(45) NULL,
		create_time BIGINT(10) NULL)
	  ENGINE = InnoDB
	  DEFAULT CHARACTER SET = utf8mb4`)
	err := BatchInsert(testLogDB, "collection", []Record{row1, row2})
	require.NoError(err)
}

func TestSplitBatchInsert(t *testing.T) {
	require := require.New(t)
	row1 := Record{ID: 3, Name: "row1", CreateTime: 11}
	row2 := Record{ID: 4, Name: "row2", CreateTime: 12}
	records := []Record{row1, row2}

	err := SplitBatchInsert(testLogDB, "collection", records, 1, true)
	require.NoError(err)

	err = SplitBatchInsert(testLogDB, "collection", records, 3, false)
	require.NoError(err)
}

func TestIsEmail(t *testing.T) {
	assert := assert.New(t)
	// 测试正确邮箱
	assert.True(util.IsEmail("<EMAIL>"))
	// 测试错误邮箱
	assert.False(util.IsEmail("12345"))
	// 测试脱敏邮箱
	assert.True(util.IsEmail("12****@163.com"))
}

func TestIsUniqueError(t *testing.T) {
	assert := assert.New(t)

	defer func(driver string) { Driver = driver }(Driver)

	Driver = DriverMysql
	// gorm v1
	assert.True(IsUniqueError(errors.New(`Error 1062: Duplicate entry '9074736' for key 'uk_userid'`)))
	assert.True(IsUniqueError(errors.New(`Error 1062: Duplicate entry m_kjjnassd`)))
	assert.False(IsUniqueError(errors.New(`asadasdas Error 1062: Duplicate entry '9074736' for key 'uk_userid'`)))
	// gorm v2
	assert.True(IsUniqueError(errors.New(`Error 1062 (23000): Duplicate entry '2' for key 'users.PRIMARY'`)))
	assert.False(IsUniqueError(errors.New(`panic: Error 1062 (23000): Duplicate entry '2' for key 'users.PRIMARY'`)))
	assert.False(IsUniqueError(errors.New(`Error 1364 (HY000): Field 'writer' doesn't have a default value`)))

	Driver = DriverSqlite
	assert.True(IsUniqueError(errors.New(`UNIQUE constraint failed: m_game_zxbf_profile.user_id`)))
	assert.True(IsUniqueError(errors.New(`UNIQUE constraint failed: fafdsd`)))
	assert.False(IsUniqueError(errors.New(`asadasdas UNIQUE constraint failed: m_game_zxbf_profile.user_id`)))
}

func TestForUpdate(t *testing.T) {
	assert := assert.New(t)

	defaultDriver := Driver
	defer func() { Driver = defaultDriver }()
	Driver = DriverMysql
	var txTest *gorm.DB
	_ = Tx(testDB, func(tx *gorm.DB) error {
		txTest = ForUpdate(tx).Exec(`SELECT * FROM m_game_zxbf_profile WHERE id = 1`)
		return nil
	})
	value, ok := txTest.Get("gorm:query_option")
	assert.True(ok)
	assert.Equal("FOR UPDATE", value)

	Driver = DriverSqlite
	_ = Tx(testDB, func(tx *gorm.DB) error {
		txTest = ForUpdate(tx).Exec(`SELECT * FROM m_game_zxbf_profile WHERE id = 1`)
		return nil
	})
	value, ok = txTest.Get("gorm:query_option")
	assert.False(ok)
	assert.Empty(value)
}
