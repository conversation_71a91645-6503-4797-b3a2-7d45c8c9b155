package servicedb

// THIS FILE SHOULD BE ENABLED IN TESTING ENVIRONMENT ONLY

import (
	"reflect"
	"time"

	"bou.ke/monkey"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/jinzhu/gorm"
)

var mocked = false

func init() {
	syncDelayTime = time.Millisecond * 3000
	mockDB = func(db *gorm.DB) {
		// run once only
		if mocked {
			return
		}

		var s *gorm.Scope
		// var guard *monkey.PatchGuard
		monkey.PatchInstanceMethod(reflect.TypeOf(s), "Exec", func(scope *gorm.Scope) *gorm.Scope {
			// WE DO NOT UNPATCH AS THIS METHOD MAY RUN PARALLELLY
			// guard.Unpatch()
			// defer guard.Restore()
			// return scope.Exec()

			// NO TRACE HERE
			// defer scope.trace(NowFunc())

			if !scope.HasError() {
				if result, err := scope.SQLDB().Exec(scope.SQL, scope.SQLVars...); scope.Err(err) == nil {
					if count, err := result.RowsAffected(); scope.Err(err) == nil {
						scope.DB().RowsAffected = count
					}
				}
			}

			hasDirtyData = true
			return scope
		})

		var c *mrpc.Client
		var guard *monkey.PatchGuard
		guard = monkey.PatchInstanceMethod(reflect.TypeOf(c), "Call", func(client *mrpc.Client, uri, ip string, input, output interface{}, cookies ...map[string]string) error {
			guard.Unpatch()
			defer guard.Restore()
			if hasDirtyData {
				time.Sleep(syncDelayTime)
			}
			hasDirtyData = true
			return client.Call(uri, ip, input, output, cookies...)
		})

		mocked = true
	}
}
