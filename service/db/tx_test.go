package servicedb

import (
	"errors"
	"os"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	gorm2 "gorm.io/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var testDB, testLogDB, sqliteDB *gorm.DB
var sqliteDB2 *gorm2.DB

func TestMain(m *testing.M) {
	logger.InitTestLog()
	var err error
	conf := &Config{
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		User:         "root",
		Name:         "app_missevan",
		Pass:         "rootmysql",
		MaxIdleConns: DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
	}
	testDB, err = InitDatabase(conf)
	if err != nil {
		logger.Fatal(err)
	}
	ApplyTestHooks(testDB)

	conf.Name = "app_missevan_log"
	testLogDB, err = InitDatabase(conf)
	if err != nil {
		logger.Fatal(err)
	}
	ApplyTestHooks(testLogDB)

	sqliteDB = newSqliteDB()
	sqliteDB2 = newSqliteDB2()
	os.Exit(m.Run())
}

func TestTx(t *testing.T) {
	assert := assert.New(t)

	err := Tx(testDB, func(tx *gorm.DB) error {
		return nil
	})
	assert.NoError(err)

	sampleErr := errors.New("sample")
	err = Tx(testDB, func(tx *gorm.DB) error {
		return sampleErr
	})
	assert.Equal(sampleErr, err)

	assert.NotPanics(func() {
		err = Tx(testDB, func(tx *gorm.DB) error {
			panic("panic")
		})
	})
	assert.EqualError(err, "panic")
}

func TestTx2(t *testing.T) {
	assert := assert.New(t)

	err := Tx2(sqliteDB2, func(tx *gorm2.DB) error {
		return nil
	})
	assert.NoError(err)

	sampleErr := errors.New("sample")
	err = Tx2(sqliteDB2, func(tx *gorm2.DB) error {
		return sampleErr
	})
	assert.Equal(sampleErr, err)

	assert.NotPanics(func() {
		err = Tx2(sqliteDB2, func(tx *gorm2.DB) error {
			panic("panic")
		})
	})
	assert.EqualError(err, "panic")
}

func newSqliteDB() *gorm.DB {
	return tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    "test.db",
		QueryFile: "../../testdata/test.sql",
	}, nil)
}

func newSqliteDB2() *gorm2.DB {
	return tutil.NewSqlite2(&tutil.ConfigSqlite{
		DBFile:    "test_v2.db",
		QueryFile: "../../testdata/test.sql",
	})
}
