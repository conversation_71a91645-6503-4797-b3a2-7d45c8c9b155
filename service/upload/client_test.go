package upload

import (
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestNewClient(t *testing.T) {
	testCases := []struct {
		url       string
		http      bool
		expectErr bool
	}{
		{
			url:       "https://example.com",
			http:      false,
			expectErr: false,
		},
		{
			url:       "https://example.com",
			http:      true,
			expectErr: false,
		},
		{
			url:       ":",
			http:      false,
			expectErr: true,
		},
		{
			url:       "ftp://example.com",
			http:      false,
			expectErr: true,
		},
	}

	for _, testCase := range testCases {
		config := Config{
			URL:  testCase.url,
			HTTP: testCase.http,
		}
		client, err := NewClient(config, nil)

		if testCase.expectErr {
			assert.Error(t, err)
			assert.Nil(t, client)
		} else {
			assert.NoError(t, err)
			assert.NotNil(t, client)
		}
	}
}

func TestClient_ToResource(t *testing.T) {
	t.Run("ValidHTTPSourceURL", func(t *testing.T) {
		client, err := NewClient(Config{
			URL:          "https://example.com",
			HTTP:         true,
			HTTPHostAddr: "***********",
		}, nil)
		require.NoError(t, err)
		sourceURL := SourceURL("https://example.com/testdata/file.txt")
		resource, err := client.ToResource(sourceURL)

		assert.NoError(t, err)
		assert.True(t, resource.http)
		assert.Equal(t, "https://***********/testdata/file.txt", resource.url)
	})

	t.Run("ValidHTTPSourceURLWithPort", func(t *testing.T) {
		client, err := NewClient(Config{
			URL:          "https://example.com",
			HTTP:         true,
			HTTPHostAddr: "***********:8080",
		}, nil)
		require.NoError(t, err)
		sourceURL := SourceURL("https://example.com/testdata/file.txt")
		resource, err := client.ToResource(sourceURL)

		assert.NoError(t, err)
		assert.True(t, resource.http)
		assert.Equal(t, "http://***********:8080/testdata/file.txt", resource.url)
	})

	t.Run("InvalidHTTPSourceURL", func(t *testing.T) {
		client, err := NewClient(Config{
			URL:  "https://example.com",
			HTTP: true,
		}, nil)
		require.NoError(t, err)
		sourceURL := SourceURL("https://wrong.com/testdata/file.txt")
		resource, err := client.ToResource(sourceURL)

		assert.Error(t, err)
		assert.Nil(t, resource)
	})

	t.Run("ValidLocalSourceURL", func(t *testing.T) {
		client, err := NewClient(Config{
			URL:  "https://example.com/testdata/",
			Path: "/home/<USER>/",
			HTTP: false,
		}, nil)
		require.NoError(t, err)
		sourceURL := SourceURL("https://example.com/testdata/file.txt")
		resource, err := client.ToResource(sourceURL)

		assert.NoError(t, err)
		assert.Equal(t, "/home/<USER>/file.txt", resource.localFilePath)
	})

	t.Run("InvalidLocalSourceURL", func(t *testing.T) {
		client, err := NewClient(Config{
			URL:  "https://example.com/testdata/",
			Path: "/home/<USER>/",
			HTTP: false,
		}, nil)
		require.NoError(t, err)
		sourceURL := SourceURL("https://example.com/testdata/../root/conf")
		resource, err := client.ToResource(sourceURL)

		assert.Error(t, err)
		assert.Nil(t, resource)
	})
}

func TestClient_OpenResource(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		_, err := rw.Write([]byte("..."))
		require.NoError(err)
	}))
	defer server.Close()

	sourceURL := SourceURL(server.URL + "/testdata/file.txt")

	url, err := url.Parse(server.URL)
	require.NoError(err)
	client := &Client{
		config: Config{
			URL:  url.String(),
			HTTP: true,
		},
		url:        url,
		httpClient: server.Client(),
	}
	require.NoError(err)

	reader, err := client.OpenResource(sourceURL)
	assert.NoError(err)
	assert.NotNil(reader)
	data := make([]byte, 3)
	n, err := reader.Read(data)
	assert.EqualError(err, "EOF")
	assert.Equal(3, n)
	assert.Equal("...", string(data))
}

type MockedUploader struct {
	mock.Mock
}

func (m *MockedUploader) Upload(entry, path string, reader io.Reader) (schemeURL string, err error) {
	args := m.Called(entry, path, reader)
	return args.String(0), args.Error(1)
}

func TestClient_Upload(t *testing.T) {
	now := time.Now()
	util.SetTimeNow(func() time.Time {
		return now
	})
	defer util.SetTimeNow(nil)

	server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
		_, err := rw.Write([]byte("..."))
		require.NoError(t, err)
	}))
	defer server.Close()

	clientURL := server.URL
	scheme := "oss"
	pathPrefix := "test/"
	schemeURLPrefix := scheme + "://" + pathPrefix

	t.Run("SourceURLIsInvalid", func(t *testing.T) {
		sourceURL := SourceURL("https://wrong.com/testdata/file.txt")

		uploader := &MockedUploader{}

		client, err := NewClient(Config{URL: clientURL, HTTP: true}, uploader)
		require.NoError(t, err)

		schemeURL, err := client.Upload(sourceURL, schemeURLPrefix)

		assert.EqualError(t, err, "upload: wrong source url")
		assert.Empty(t, schemeURL)
		uploader.AssertNotCalled(t, "Upload")
	})

	t.Run("UploadFailed", func(t *testing.T) {
		sourceURL := SourceURL(server.URL + "/testdata/file.txt")

		uploader := &MockedUploader{}
		uploader.On("Upload", mock.Anything, mock.Anything, mock.Anything).Return("", errors.New("upload failed"))

		client, err := NewClient(Config{URL: clientURL, HTTP: true}, uploader)
		require.NoError(t, err)

		schemeURL, err := client.Upload(sourceURL, schemeURLPrefix)

		assert.EqualError(t, err, "upload failed")
		assert.Empty(t, schemeURL)
		uploader.AssertCalled(
			t, "Upload",
			scheme, sourceURL.NewTargetPath(pathPrefix).String(), mock.Anything,
		)
	})

	t.Run("UploadSuccess", func(t *testing.T) {
		sourceURL := SourceURL(server.URL + "/testdata/file.txt")

		uploader := &MockedUploader{}
		uploader.On("Upload", mock.Anything, mock.Anything, mock.Anything).Return("test://...", nil)

		client, err := NewClient(Config{
			URL:  server.URL,
			HTTP: true,
		}, uploader)
		require.NoError(t, err)

		schemeURL, err := client.Upload(sourceURL, schemeURLPrefix)

		assert.NoError(t, err)
		assert.NotEmpty(t, schemeURL)
		uploader.AssertCalled(
			t, "Upload",
			scheme, sourceURL.NewTargetPath(pathPrefix).String(), mock.Anything,
		)
	})
}
