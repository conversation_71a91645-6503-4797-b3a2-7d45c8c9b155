package upload

import (
	"crypto/tls"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"
)

// Config for Client.
type Config struct {
	URL string `yaml:"url"` // Source URL prefix, is used to remove the prefix from the source URL, in order to get the relative path.

	// Local mode.
	Path string `yaml:"path"` // Local path prefix, is used to join with the relative path to get the absolute path.

	// HTTP mode.
	HTTP         bool   `yaml:"http"`           // Enable HTTP mode.
	HTTPHostAddr string `yaml:"http_host_addr"` // HTTP host address, is used to replace host in source URL.
}

// Uploader upload file to storage.
type Uploader interface {
	Upload(entry, path string, reader io.Reader) (schemeURL string, err error)
}

// Client for upload.
type Client struct {
	config     Config
	uploader   Uploader
	url        *url.URL
	httpClient *http.Client
}

// NewClient create a new upload Client.
func NewClient(config Config, uploader Uploader) (*Client, error) {
	c := &Client{
		config:   config,
		uploader: uploader,
	}

	var err error
	c.url, err = url.Parse(config.URL)
	if err != nil || !isSchemeHTTP(c.url.Scheme) {
		return nil, ErrConfiguredURL
	}

	if c.config.HTTP {
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		c.httpClient = &http.Client{Timeout: 30 * time.Second, Transport: tr}
	}

	return c, nil
}

// ToResource check if SourceURL is valid and convert it to Resource.
func (c *Client) ToResource(sourceURL SourceURL) (*Resource, error) {
	u, err := url.Parse(sourceURL.String())
	if err != nil {
		return nil, ErrSourceURL
	}
	if !isSchemeHTTP(u.Scheme) || u.Host != c.url.Host || !strings.HasPrefix(u.Path, c.url.Path) {
		return nil, ErrSourceURL
	}
	if c.config.HTTP {
		// TODO: 合法性检查
		res := &Resource{
			http:       true,
			httpClient: c.httpClient,
		}
		if c.config.HTTPHostAddr != "" {
			res.httpHost = u.Host
			// Replace host in url with configured IP, force resource to be accessed by http.
			u.Host = c.config.HTTPHostAddr
			// FIXME: support IPv6
			if strings.Contains(c.config.HTTPHostAddr, ":") {
				// If configured host IP contains port, set scheme to http.
				u.Scheme = "http"
			}
		}
		res.url = u.String()
		return res, nil
	}
	trimmedURL := strings.TrimPrefix(u.Path, c.url.Path)
	filePath, err := filepath.Abs(filepath.Join(c.config.Path, trimmedURL))
	if err != nil {
		return nil, err
	}
	// Check if file is in configured path, to prevent relative path.
	// For example:
	//   If configured url is `https://example.com/testdata/` and configured path is `/home/<USER>/`. Passing
	//   `http://example.com/testdata/../root/conf` will get `/home/<USER>/conf`. Which is not in valid home path
	//   `/home/<USER>/`.
	if !strings.HasPrefix(filePath, c.config.Path) {
		return nil, ErrSourceURL
	}
	return &Resource{localFilePath: filePath}, nil
}

// OpenResource opens the SourceURL for reading.
func (c *Client) OpenResource(sourceURL SourceURL) (io.ReadCloser, error) {
	res, err := c.ToResource(sourceURL)
	if err != nil {
		return nil, err
	}
	return res.Open()
}

// Upload uploads file to storage from SourceURL.
// schemeURLPrefix is used to specify the scheme and path prefix for the upload destination, e.g. `oss://push/`.
func (c *Client) Upload(source SourceURL, schemeURLPrefix string) (string, error) {
	file, err := c.OpenResource(source)
	if err != nil {
		return "", err
	}
	defer file.Close()

	u, err := url.Parse(schemeURLPrefix)
	if err != nil {
		return "", err
	}
	pathPrefix := u.Host + u.Path
	schemeURL, err := c.uploader.Upload(u.Scheme, source.NewTargetPath(pathPrefix).String(), file)
	if err != nil {
		return "", err
	}

	return schemeURL, nil
}
