package upload

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsSchemeHTTP(t *testing.T) {
	testCases := []struct {
		input    string
		expected bool
	}{
		{
			input:    "http",
			expected: true,
		},
		{
			input:    "https",
			expected: true,
		},
		{
			input:    "ftp",
			expected: false,
		},
		{
			input:    "",
			expected: false,
		},
	}

	for _, tc := range testCases {
		assert.Equal(t, tc.expected, isSchemeHTTP(tc.input))
	}
}
