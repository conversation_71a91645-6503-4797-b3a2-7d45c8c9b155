package upload

import (
	"fmt"
	"io"
	"net/http"
	"os"

	"github.com/MiaoSiLa/missevan-go/service/util"
)

// Resource represents a resource that can be opened for reading. It can be a local file or a HTTP resource.
type Resource struct {
	// Fields for HTTP resource
	http       bool
	httpHost   string
	httpClient *http.Client
	url        string

	// Fields for local file
	localFilePath string
}

// Open opens Resource for reading. It determines the type of resource (local file or HTTP resource).
func (r *Resource) Open() (io.ReadCloser, error) {
	if r.http {
		return r.openHTTP()
	}
	return r.openLocal()
}

// openLocal opens Resource as a local file for reading.
func (r *Resource) openLocal() (io.ReadCloser, error) {
	return os.Open(r.localFilePath)
}

// openHTTP opens Resource as a HTTP resource for reading. It creates a new HTTP request and sets the host IP if
// provided.
func (r *Resource) openHTTP() (io.ReadCloser, error) {
	req, err := http.NewRequest(http.MethodGet, r.url, nil)
	if err != nil {
		return nil, err
	}

	if r.httpHost != "" {
		req.Host = r.httpHost
	}

	req.Header.Set("User-Agent", util.UserAgent)

	resp, err := r.httpClient.Do(req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		if resp.Body == nil {
			return nil, fmt.Errorf("resource http status %d", resp.StatusCode)
		}

		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("resource http status %d", resp.StatusCode)
		}

		return nil, fmt.Errorf("resource http status %d, %s", resp.StatusCode, string(body))
	}

	return resp.Body, nil
}
