package upload

import (
	"fmt"
	"path/filepath"

	"github.com/MiaoSiLa/missevan-go/util"
)

// SourceURL url of source file to upload.
type SourceURL string

// String convert SourceURL to string.
func (s SourceURL) String() string {
	return string(s)
}

// Ext get extension name of SourceURL.
func (s SourceURL) Ext() string {
	return filepath.Ext(s.String())
}

// NewTargetPath generate a new TargetPath from SourceURL with specified prefix path.
func (s SourceURL) NewTargetPath(prefix string, optExt ...string) TargetPath {
	now := util.TimeNow()
	ext := s.Ext()
	if len(optExt) > 0 {
		ext = optExt[0]
	}
	return TargetPath(fmt.Sprintf(
		"%s%s%s%s%s",
		prefix,
		now.Format("200601/02/"),
		util.MD5(s.String()),
		now.Format("150405"),
		ext,
	))
}
