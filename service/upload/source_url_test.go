package upload

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestSourceURL_String(t *testing.T) {
	s := SourceURL("https://example.com/123456.mp3")
	assert.Equal(t, "https://example.com/123456.mp3", s.String())
}

func TestSourceURL_Ext(t *testing.T) {
	s := SourceURL("https://example.com/123456.mp3")
	assert.Equal(t, ".mp3", s.Ext())
}

func TestSourceURL_NewTargetPath(t *testing.T) {
	util.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	})
	defer util.SetTimeNow(nil)

	testCases := []struct {
		s      SourceURL
		prefix string
		ext    []string
		want   string
	}{
		{
			s:      SourceURL("https://example.com/123456.mp3"),
			prefix: "prefix/",
			ext:    nil,
			want:   "prefix/202401/01/11c1ef84614aa21a4fda35bbb427c345000000.mp3",
		},
		{
			s:      SourceURL("https://example.com/123456.mp3"),
			prefix: "",
			ext:    []string{".mp4"},
			want:   "202401/01/11c1ef84614aa21a4fda35bbb427c345000000.mp4",
		},
	}

	for _, tc := range testCases {
		tp := tc.s.NewTargetPath(tc.prefix, tc.ext...)
		assert.Equal(t, tc.want, tp.String())
	}
}
