package upload

import (
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"path"
	"runtime"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestResource_Open(t *testing.T) {
	t.Run("OpenLocalFile", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		_, f, _, _ := runtime.Caller(0)
		tmpPath := path.Join(path.Dir(f), "../../testdata")
		tmpFile, err := os.CreateTemp(tmpPath, "example")
		require.NoError(err)
		defer func(name string) {
			err := os.Remove(name)
			assert.NoError(err)
		}(tmpFile.Name())

		r := Resource{
			http:          false,
			localFilePath: tmpFile.Name(),
		}

		reader, err := r.Open()
		require.NoError(err)
		assert.NotNil(reader)
		assert.NoError(reader.Close())
	})

	t.Run("OpenHTTPResource", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
			_, err := rw.Write([]byte("OK"))
			require.NoError(err)
		}))
		defer server.Close()

		r := &Resource{
			http:       true,
			httpClient: server.Client(),
			url:        server.URL,
		}

		reader, err := r.Open()
		require.NoError(err)
		require.NotNil(reader)

		bytes, err := io.ReadAll(reader)
		require.NoError(err)
		assert.Equal("OK", string(bytes))

		require.NoError(reader.Close())
	})

	t.Run("Open404HTTPResource", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		server := httptest.NewServer(http.HandlerFunc(func(rw http.ResponseWriter, req *http.Request) {
			rw.WriteHeader(http.StatusNotFound)
			_, err := rw.Write([]byte("Not Found."))
			require.NoError(err)
		}))
		defer server.Close()

		r := &Resource{
			http:       true,
			httpClient: server.Client(),
			url:        server.URL,
		}

		reader, err := r.Open()
		assert.EqualError(err, "resource http status 404, Not Found.")
		assert.Nil(reader)
	})
}
