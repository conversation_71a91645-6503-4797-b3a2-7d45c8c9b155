package antispam

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/saf"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	serviceCouponAbuse   = "coupon_abuse"
	serviceRegisterAbuse = "account_abuse"
)

type abuseParam struct {
	OperateTime int64  `json:"operateTime"`
	IP          string `json:"ip"`

	AccountID string `json:"accountid,omitempty"`
	Mobile    string `json:"mobile,omitempty"`
	MobileMd5 string `json:"mobileMd5,omitempty"`
	Email     string `json:"email,omitempty"`

	regionNum int
}

// CheckCouponAbuse 阿里云的营销风险封装，不检查入参是否正确
// 已记录错误日志
func (c *Client) CheckCouponAbuse(userID int64, email, mobile string, region int, ip string) (*scan.CheckResult, error) {
	caParam := abuseParam{
		AccountID:   strconv.FormatInt(userID, 10),
		IP:          ip,
		OperateTime: util.TimeNow().Unix(),
		Email:       email,
		Mobile:      mobile,
		regionNum:   region,
	}
	return caParam.sendAbuseRequest(c, serviceCouponAbuse)
}

// CheckRegisterAbuse 阿里云的注册风险封装，不检查入参是否正确
// 已记录错误日志
func (c *Client) CheckRegisterAbuse(email, mobile string, region int, ip string) (*scan.CheckResult, error) {
	caParam := abuseParam{
		IP:          ip,
		OperateTime: util.TimeNow().Unix(),
		Email:       email,
		Mobile:      mobile,
		regionNum:   region,
	}
	return caParam.sendAbuseRequest(c, serviceRegisterAbuse)
}

func (p *abuseParam) regionMobile() {
	if p.Mobile == "" {
		return
	}
	// TODO: 目前仅大陆手机号支持 mobileMd5 参数查询
	if p.regionNum == 0 || p.regionNum == 86 {
		p.MobileMd5 = util.MD5(p.Mobile)
		p.Mobile = ""
		return
	}
	p.Mobile = fmt.Sprintf("%d-%s", p.regionNum, p.Mobile)
}

func (p *abuseParam) sendAbuseRequest(c *Client, abuseService string) (*scan.CheckResult, error) {
	p.regionMobile()
	req := saf.CreateExecuteRequestRequest()
	req.Service = abuseService
	req.Scheme = "https"
	temp, err := json.Marshal(p)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	req.ServiceParameters = string(temp)
	res, err := c.safClient.ExecuteRequest(req)
	if err != nil {
		logger.Errorf("ExecuteRequest failed: %v", err)
		return nil, err
	}
	if res.Code != 200 {
		logger.WithFields(logger.Fields{
			"request_id": res.RequestId,
			"code":       res.Code,
		}).Errorf("check %s failed: %s", abuseService, res.Message)
		return nil, &serviceutil.APIError{Status: res.Code, Message: fmt.Sprintf("check %s failed.", abuseService)}
	}
	score, err := strconv.ParseFloat(res.Data.Score, 64)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	caRes := &scan.CheckResult{Score: score}
	caRes.Labels = make([]string, 0, 1)
	switch {
	case score < 35.0:
		caRes.Pass = true
	case score < 65.0:
		caRes.Pass = true
		caRes.Labels = append(caRes.Labels, "low")
	case score < 85.0:
		caRes.Pass = true
		caRes.Labels = append(caRes.Labels, "medium")
	default:
		caRes.Pass = false
		caRes.Labels = append(caRes.Labels, "high")
	}
	return caRes, nil
}
