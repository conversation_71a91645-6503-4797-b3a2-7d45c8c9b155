package antispam

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/saf"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

const (
	// StatusExceedQuota 被阿里云限流时阿里云返回的错误码：
	// https://help.aliyun.com/document_detail/53414.html#section-qyw-h54-w2b
	StatusExceedQuota = 588
)

const (
	// APIVersion x-acs-version 内容安全接口版本
	// 当前版本为：2018-05-09
	// https://help.aliyun.com/document_detail/53413.html
	APIVersion = "2018-05-09"
)

// ClientInfo 内容安全检测的公共参数：
// https://help.aliyun.com/document_detail/53413.html
type ClientInfo struct {
	UserID string `json:"userId,omitempty"`
}

// BizData 业务数据
type BizData struct {
	BizType string      `json:"bizType,omitempty"`
	Scenes  []string    `json:"scenes"`
	Tasks   interface{} `json:"tasks"`
}

// Response of CommonAPI
type Response struct {
	ErrorAPI
	RequestID string          `json:"requestId"`
	Data      json.RawMessage `json:"data"`
}

// ErrorAPI contains the code and msg of CommonAPI
type ErrorAPI struct {
	// Code 和 HTTP 状态码一致（但有扩展）
	// https://help.aliyun.com/document_detail/53414.html
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (e *ErrorAPI) Error() string {
	s, _ := json.Marshal(e)
	return string(s)
}

// Config contains configuration for Client
type Config struct {
	Provider        string `yaml:"provider"`
	RegionID        string `yaml:"region_id,omitempty"`
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`

	Endpoint string `yaml:"endpoint,omitempty"`

	// TODO: yaml 解析字段暂未改动
	Blacklist BlocklistConfig `yaml:"blacklist"`
}

// BlocklistConfig 黑名单配置
type BlocklistConfig struct {
	CSVDir     string `yaml:"csv_dir"`
	AllowEmpty bool   `yaml:"-"` // 允许为空，测试时候使用
}

// BuildDefaultConfig returns a default Config
func BuildDefaultConfig() *Config {
	return &Config{
		Provider:  "aliyun",
		RegionID:  "cn-shanghai",
		Endpoint:  "https://green.cn-shanghai.aliyuncs.com",
		Blacklist: BlocklistConfig{CSVDir: "/var/lib/antispam/"},
	}
}

// WithAccess sets the AccessKeyID and the AccessKeyID
func (c *Config) WithAccess(accessKeyID, accessKeySecret string) *Config {
	if c != nil {
		c.AccessKeyID = accessKeyID
		c.AccessKeySecret = accessKeySecret
	}
	return c
}

// Client wraps the CommonAPI client
type Client struct {
	client    *sdk.Client
	safClient *saf.Client
	domain    string

	*blocklist
	Config
}

// NewClient returns a new Client
func NewClient(conf *Config) (c *Client, err error) {
	credential := credentials.NewAccessKeyCredential(conf.AccessKeyID, conf.AccessKeySecret)
	endpoint, err := url.Parse(conf.Endpoint)
	if err != nil {
		return nil, err
	}
	if endpoint.Scheme == "" || endpoint.Host == "" {
		return nil, errors.New("antispam: wrong endpoint")
	}
	config := sdk.NewConfig().WithScheme(strings.ToUpper(endpoint.Scheme))
	client, err := sdk.NewClientWithOptions(conf.RegionID, config, credential)
	if err != nil {
		return nil, err
	}
	safClient, err := saf.NewClientWithOptions(conf.RegionID, config, credential)
	if err != nil {
		return nil, err
	}

	res := &Client{
		client:    client,
		safClient: safClient,
		domain:    endpoint.Host,

		Config: *conf,
	}
	res.blocklist, err = newBlocklist(&conf.Blacklist)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (c *Client) doRequest(apiPath string, reqData interface{}, cli *ClientInfo, v interface{}) (requestID string, err error) {
	request := requests.NewCommonRequest()
	request.Method = "POST"
	request.Domain = c.domain
	request.Version = APIVersion
	request.PathPattern = apiPath

	if cli != nil {
		clientInfo, _ := json.Marshal(cli)
		request.QueryParams["ClientInfo"] = string(clientInfo)
	}

	body, err := json.Marshal(reqData)
	if err != nil {
		return "", err
	}
	request.Content = body
	response, err := c.client.ProcessCommonRequest(request)
	if err != nil {
		return "", err
	}
	var resp Response
	err = json.Unmarshal(response.GetHttpContentBytes(), &resp)
	if err != nil {
		return "", err
	}
	if resp.Code != http.StatusOK {
		msg := fmt.Sprintf("(%s) - %s", resp.RequestID, resp.Msg)
		logger.Errorf("antispam.Client.doRequest: %s", msg)
		return resp.RequestID, &serviceutil.APIError{Status: resp.Code, Message: msg}
	}
	err = json.Unmarshal(resp.Data, v)
	if err != nil {
		return resp.RequestID, err
	}

	return resp.RequestID, nil
}
