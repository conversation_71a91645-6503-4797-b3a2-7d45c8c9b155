package antispam

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestRegionMobile(t *testing.T) {
	assert := assert.New(t)
	param := abuseParam{
		Mobile:    "",
		regionNum: 0,
	}
	param.regionMobile()
	assert.Empty(param)

	param = abuseParam{
		Mobile:    "13112341234",
		regionNum: 86,
	}
	param.regionMobile()
	assert.Empty(param.Mobile)
	assert.Equal(util.MD5("13112341234"), param.MobileMd5)

	param = abuseParam{
		Mobile:    "13112341234",
		regionNum: 1,
	}
	param.regionMobile()
	assert.Equal("1-13112341234", param.Mobile)
	assert.Equal("", param.MobileMd5)
}

func TestCheckAbuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c, err := NewClient(TestConfig())
	require.NoError(err)

	// CheckCouponAbuse
	res, err := c.<PERSON>pon<PERSON>buse(12, "", "***********", 1, "127.0.0.1")
	require.NoError(err)
	assert.Equal(true, res.Pass)
	require.Equal(1, len(res.Labels))
	require.Equal("low", res.Labels[0])

	// CheckRegisterAbuse
	res, err = c.CheckRegisterAbuse("", "***********", 0, "127.0.0.1")
	require.NoError(err)
	assert.Equal(true, res.Pass)
	require.Equal(0, len(res.Labels))
}

func TestSendAbuseRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c, err := NewClient(TestConfig())
	require.NoError(err)

	param := abuseParam{
		AccountID: "12",
		Mobile:    "***********",
		IP:        "127.0.0.1",
		Email:     "",
		regionNum: 0,
	}
	r, err := param.sendAbuseRequest(c, serviceRegisterAbuse)
	require.NoError(err)
	assert.False(r.Pass)
	assert.NotZero(r.Score)
	assert.Equal("high", r.Labels[0])

	r, err = param.sendAbuseRequest(c, serviceCouponAbuse)
	require.NoError(err)
	assert.False(r.Pass)
	assert.NotZero(r.Score)
	assert.Equal("high", r.Labels[0])

	param.Mobile = ""
	param.MobileMd5 = ""
	r, err = param.sendAbuseRequest(c, serviceCouponAbuse)
	require.NoError(err)
	assert.True(r.Pass)
}
