package antispam

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestCheckTexts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(TestConfig())
	require.NoError(err)
	results, _, err := c.CheckTexts([]string{"nmsl", "日赚 300", "测试", "QQ 群: 24682469", "nmsl 2233"}, 0, scan.SceneIM)
	assert.NoError(err)
	expectedResult := []*scan.CheckResult{
		{
			Pass:   false,
			Labels: []string{"abuse"},
			AliyunMatch: []scan.AliyunMatch{{
				Label: "abuse",
				Details: []*scan.AliyunDetail{
					{
						Label:   "abuse",
						Context: "nmsl",
					},
				}}},
		},
		{
			Pass:   false,
			Labels: []string{"blacklist", "base"},
			BlacklistMatch: scan.BlacklistMatch{
				ViolationContent: []string{"日赚"},
				Mode:             blocklistModeRegex,
				Regex:            "[日曰].{0,3}[赚賺算]",
			},
		},
		{
			Pass:        true,
			Labels:      []string{"normal"},
			AliyunMatch: []scan.AliyunMatch{{Label: "normal"}},
		},
		{
			Pass:   false,
			Labels: []string{"ad"},
			AliyunMatch: []scan.AliyunMatch{{
				Label: "ad",
				Details: []*scan.AliyunDetail{{
					Label:   "ad",
					Context: "qq 群",
				}, {
					Label:   "ad",
					Context: "24682469",
				}}}},
		},
		{
			Pass:   false,
			Labels: []string{"abuse"},
			AliyunMatch: []scan.AliyunMatch{{
				Label: "abuse",
				Details: []*scan.AliyunDetail{{
					Label:   "abuse",
					Context: "nmsl",
				}}}},
		},
	}
	require.Equal(len(expectedResult), len(results))
	for i := range expectedResult {
		for j := range results[i].AliyunMatch {
			results[i].AliyunMatch[j].RequestID = ""
		}
		assert.Equal(expectedResult[i], results[i], tutil.SprintJSON(results[i]))
	}

	results, _, err = c.CheckTexts([]string{"nmsl", "日赚 300", "测试", "QQ 群: 24682469", "nmsl 2233"}, 0, scan.SceneDanmaku, OptionIgnoreAd)
	require.NoError(err)
	expectedResult[3].Pass = true
	for i := range expectedResult {
		for j := range results[i].AliyunMatch {
			results[i].AliyunMatch[j].RequestID = ""
		}
		assert.Equal(expectedResult[i], results[i], i)
	}

	/* 反垃圾又能通过了
	// 这个文本如果删掉一些阿里云会检测为通过
	textWithNoDetails := `【猫耳新春游园会-积分转盘】中获得奖品：分区小礼品盲盒 × 1。` +
		`请于2月20日前提交个人中奖邮寄信息（姓名，电话，地址），活动奖励将于2月25日后7个工作日内邮寄` +
		`亲爱的用户，恭喜您在【猫耳新春游园会-积分转盘】中获得奖品：猫耳FM周边 × 1。`
	results, aliYunResponse, err = c.CheckTexts([]string{textWithNoDetails}, 0, "")
	require.NoError(err)
	noDetailResult := []*scan.CheckResult{
		{
			Pass:   false,
			Labels: []string{"spam"},
			AliyunMatch: []scan.AliyunMatch{{
				RequestID: aliYunResponse[0].RequestID,
				Label:     "spam",
				Details:   make([]*scan.AliyunDetail, 0),
			},
			},
		},
	}
	assert.Equal(noDetailResult, results)
	*/

	// provider 未设置的情况
	c.Config.Provider = ""
	results, _, err = c.CheckTexts([]string{"nmsl", "日赚 300"}, 0, scan.SceneComment, OptionIgnoreAd)
	require.NoError(err)
	expectedResult[0] = &scan.CheckResult{Pass: true, Labels: []string{"normal"}}
	for i := range expectedResult[:2] {
		assert.Equal(expectedResult[i], results[i], i)
	}
}

func TestCheckTextsCommentEvil(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c, err := NewClient(TestConfig())
	require.NoError(err)
	results, _, err := c.CheckTexts([]string{"evil", "QQ 群: 24682469", " 日赚 300"}, 0, scan.SceneComment)
	require.NoError(err)
	require.Len(results, 3)
	for i := 0; i < 2; i++ {
		assert.True(results[i].Pass, i)
		assert.Contains(results[i].Labels, scan.LabelEvil, i)
	}
	// 第三个不通过
	assert.False(results[2].Pass)
}
