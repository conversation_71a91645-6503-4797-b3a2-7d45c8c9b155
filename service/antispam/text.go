package antispam

import (
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// OptionIgnoreAd 不检测广告
	OptionIgnoreAd Option = 1
)

// Option 对应自定义的文本检测场景，比如不检测广告
type Option int

// CheckTexts 文字检查
// NOTICE: aliyunRes 仅对直播间消息有效（每次只检查一条），其他情况会出现下标不匹配的情况
func (c *Client) CheckTexts(texts []string, userID int64, scene string, opts ...Option) (results []*scan.CheckResult, aliyunRes []AliyunTextRespElem, err error) {
	defer func() {
		// 添加假发送 evil label
		switch scene {
		case scan.ScenePrivateMessage, scan.SceneDanmaku, scan.SceneIM:
			for i := range results {
				if results[i] != nil && results[i].Pass {
					if c.checkEvil(texts[i]) {
						results[i].Labels = append(results[i].Labels, scan.LabelEvil)
					}
				}
			}
		case scan.SceneComment:
			// 评论假发送规则，详见代码
			for i := range results {
				if results[i] == nil {
					continue
				}
				if results[i].Pass {
					// label 有 ad 假发送，即使 Pass 是 true
					// evil 词库假发送
					if util.HasElem(results[i].Labels, scan.LabelAd) ||
						c.checkEvil(texts[i]) {
						results[i].Labels = append(results[i].Labels, scan.LabelEvil)
					}
					continue
				}
				// base 词库不通过
				// aliyun 涉政、暴恐、违禁、自定义不通过
				evilPass := true
				for j := range results[i].Labels {
					switch results[i].Labels[j] {
					case scan.LabelBlacklist, scan.LabelPolitics,
						scan.LabelTerrorism, scan.LabelContraband,
						scan.LabelCustomized:
						evilPass = false
					}
					if !evilPass {
						// NOTICE: break 不放在 switch 中
						break
					}
				}
				if evilPass {
					results[i].Pass = true
					results[i].Labels = append(results[i].Labels, scan.LabelEvil)
				}
			}
		}
		// 防止返回 nil
		// TODO: 用其他办法优化
		for i := range results {
			if results[i] == nil {
				results[i] = scan.NewPassedCheckResult()
			}
			results[i].FillEmptyLabel()
		}
	}()
	results = c.CheckText(texts, scene)

	passedTexts := make([]string, 0, len(texts))
	idxMap := make(map[int]int, len(texts))
	for i := range results {
		if results[i] == nil {
			passedTexts = append(passedTexts, texts[i])
			idxMap[len(passedTexts)-1] = i
		}
	}
	if len(passedTexts) == 0 {
		return results, nil, nil
	}

	switch c.Config.Provider {
	case "aliyun":
		// 搜索不需要走阿里云文本检测
		if scene == scan.SceneSearch {
			return results, nil, nil
		}
		allowAd := false
		for _, v := range opts {
			if v == OptionIgnoreAd {
				allowAd = true
				break
			}
		}
		aliyunRes, err = c.aliyunCheckText(scene, passedTexts, userID)
		if err != nil {
			// 阿里云失败仅降级
			logger.Error(err)
			return results, nil, nil
		}
		for i := range aliyunRes {
			results[idxMap[i]] = aliyunRes[i].ToScanCheckResult(scene, allowAd)
		}
		return results, aliyunRes, nil
	default:
		return results, nil, nil
	}
}
