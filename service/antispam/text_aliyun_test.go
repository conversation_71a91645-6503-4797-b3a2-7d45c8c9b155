package antispam

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestAliyunCheckText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bannedText := "法轮功" + strings.Repeat("0", 20000) + "法轮功"
	res, err := testClient.aliyunCheckText(scan.SceneComment, []string{bannedText}, 0)
	require.NoError(err)
	require.Len(res, 1)
	assert.NotEmpty(res[0].RequestID)
	require.Len(res[0].Results, 3) // 错误，正确，错误
	r := res[0].Results[0]
	assert.Equal(aliyunSuggestionBlock, r.Suggestion)
	assert.NotPanics(func() {
		assert.Equal(AliyunTextPosition{
			StartPos: 0,
			EndPos:   3,
		}, r.Details[0].Context[0].Positions[0])
	})
	r = res[0].Results[1]
	assert.Equal(aliyunSuggestionPass, r.Suggestion)
	r = res[0].Results[2]
	assert.Equal(aliyunSuggestionBlock, r.Suggestion)
	assert.NotPanics(func() {
		assert.Equal(AliyunTextPosition{
			StartPos: 20003,
			EndPos:   20006,
		}, r.Details[0].Context[0].Positions[0])
	})

	res, err = testClient.aliyunCheckText(scan.SceneDanmaku, []string{"test"}, 0)
	require.NoError(err)
	require.Len(res, 1)
	assert.NotEmpty(res[0].RequestID)
	require.Len(res[0].Results, 1)
	r = res[0].Results[0]
	assert.Equal(aliyunSuggestionPass, r.Suggestion)
}

func TestDivide(t *testing.T) {
	assert := assert.New(t)

	assert.PanicsWithValue("unsupported max: -1", func() {
		divide("123", -1, 0)
	})
	assert.PanicsWithValue("unsupported overlap: -2", func() {
		divide("123", 10, -2)
	})
	assert.Equal("[012 123 234 345 456 567 678 789]", fmt.Sprint(divide("0123456789", 3, 2)))
	assert.Equal("[0 1 2 3 4 5 6 7 8 9]", fmt.Sprint(divide("0123456789", 1, 0)))
	assert.Equal("[012345678 89]", fmt.Sprint(divide("0123456789", 9, 1)))
}

func TestNewAliyunDivider(t *testing.T) {
	assert := assert.New(t)

	text := []string{
		strings.Repeat("a", 10001),
		"bb",
	}
	ad := newAliyunDivider(text)
	assert.Equal(ad.InTexts, text)
	assert.Equal([]int{2, 1}, ad.Lens)
	assert.Equal([]string{
		strings.Repeat("a", 10000),
		strings.Repeat("a", 9),
		"bb",
	}, ad.OutTexts)
}

func TestAliyunDividerCombineAliyunTextResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	text := []string{
		strings.Repeat("a", 10001),
		"bb",
	}
	res, err := testClient.aliyunCheckText(scan.SceneComment, text, 0)
	require.NoError(err)
	require.Len(res, 2)
	assert.Len(res[0].Results, 2, "被分割的结果")
	assert.Len(res[1].Results, 1, "未被分割的结果")
}

func TestAliyunTextRespElemToScanCheckResult(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	elem := AliyunTextRespElem{
		Code:    0,
		Msg:     "OK",
		DataID:  "0",
		Content: "text",
		Results: make([]AliyunTextResult, 0),
	}
	cr := elem.ToScanCheckResult(scan.SceneIM, false)
	assert.Equal(scan.NewPassedCheckResult(), cr)
	elem.Results = append(elem.Results, AliyunTextResult{
		Scene:      "antispam",
		Suggestion: aliyunSuggestionBlock,
		Label:      "ad",
		Details: []AliyunTextDetail{
			{
				Label: "ad",
				Context: []AliyunTextContent{{
					Context:  "t",
					LibName:  "a",
					RuleType: "b",
				}},
			},
			{
				Label: "ad",
				Context: []AliyunTextContent{{
					Context:  "e",
					LibName:  "c",
					RuleType: "d",
				}},
			},
		},
	})
	cr = elem.ToScanCheckResult(scan.SceneIM, false)
	assert.False(cr.Pass)
	require.Len(cr.AliyunMatch, 1)
	details := cr.AliyunMatch[0].Details
	for i := range details {
		assert.Equal(elem.Results[0].Details[i].Label, details[i].Label)
		ctx := elem.Results[0].Details[i].Context[0]
		assert.Equal(ctx.Context, details[i].Context)
		assert.Equal([]string{ctx.LibName}, details[i].LibName)
		assert.Equal([]string{ctx.RuleType}, details[i].RuleType)
	}
	cr = elem.ToScanCheckResult(scan.SceneIM, true)
	assert.True(cr.Pass)
}
