package antispam

import (
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 阿里云文本审核文档: https://help.aliyun.com/document_detail/70439.html

// 阿里云建议的后续操作
// 文本审核操作建议：https://help.aliyun.com/document_detail/70439.html
// 图片审核操作建议：https://help.aliyun.com/document_detail/70292.html
const (
	aliyunSuggestionPass   = "pass"   // pass：文本正常
	aliyunSuggestionReview = "review" // review：需要人工审核
	aliyunSuggestionBlock  = "block"  // block：直接删除或者做限制处理
)

// apiAliyunTextScan 阿里云文本审核 API
const apiAliyunTextScan = "/green/text/scan"

// aliyunTextTask 阿里云文本审核任务指定检测对象
type aliyunTextTask struct {
	DataID  string `json:"dataId,omitempty"`
	Content string `json:"content"`
}

// AliyunTextRespElem 阿里云文本审核返回数据
// 文档：https://help.aliyun.com/document_detail/70439.html
type AliyunTextRespElem struct {
	// Code 错误码，和 HTTP 状态码一致。
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	DataID string `json:"dataId"`
	TaskID string `json:"taskId"`
	// Content 被检测文本，和调用请求中的待检测文本对应。
	Content string `json:"content"`
	// FilteredContent 如果被检测文本命中了自定义关键词词库中的关键词，则会返回当前字段，并将命中的关键词替换为星号（*）
	FilteredContent string `json:"filteredContent"`
	// Results 返回结果。调用成功时（code=200），返回结果中包含一个或多个元素
	Results []AliyunTextResult `json:"results"`

	// RequestID 辅助单元测试使用，阿里云不会在该结构内返回该字段
	RequestID string `json:"requestId"`
}

// AliyunTextResult 阿里云文本审核返回结果
// 文档：https://help.aliyun.com/document_detail/70439.html
type AliyunTextResult struct {
	Scene      string `json:"scene"`
	Suggestion string `json:"suggestion"`
	Label      string `json:"label"`
	// Rate 置信度，阿里云强烈建议不使用此字段判断
	Rate    float64            `json:"rate"`
	Details []AliyunTextDetail `json:"details"`
	// extras JSONObject	{"userId":"xxx"} 附加信息，扩展字段。
}

// AliyunTextDetail 阿里云文本审核命中风险的详细信息
type AliyunTextDetail struct {
	Label   string              `json:"label"`
	Context []AliyunTextContent `json:"contexts"`
	// hintWords JSONArray 阿里云：默认不返回该字段。如果您有需要，请提交工单联系我们进行配置。
}

// AliyunTextContent 阿里云文本审核命中该风险的上下文信息
type AliyunTextContent struct {
	// Context 检测文本命中的风险关键词，如果命中了关键词会返回该内容，如果命中了算法模型，则不会返回该字段。
	Context string `json:"context"`
	// Positions 如果命中关键词，会返回该词在原始文本中的位置。
	// 但是某些场景不会返回命中词的下标
	Positions []AliyunTextPosition `json:"positions"`
	// LibName 命中自定义词库时，才会返回当前字段。取值为创建词库时设置的词库名称。
	LibName string `json:"libName"`
	// LibCode 命中您自定义文本库时，才会返回当前字段。取值为创建风险文本库后系统返回的文本库 code.
	LibCode string `json:"libCode"`
	// RuleType 命中行为规则时，才会返回当前字段
	// 取值：user_id; ip; umid; content; similar_content; imei; imsi
	RuleType string `json:"ruleType"`
}

// AliyunTextPosition 阿里云文本审核命中文本的开始下标和结束下标
type AliyunTextPosition struct {
	StartPos int `json:"startPos"`
	EndPos   int `json:"endPos"`
}

// aliyunCheckText 阿里云文本审核
func (c *Client) aliyunCheckText(scanScene string, inputTexts []string, userID int64) ([]AliyunTextRespElem, error) {
	// 阿里云 API 规定每个被检测的文本最多 10000 个 Unicode 字符
	// https://help.aliyun.com/document_detail/70439.html

	// 两段被分割的文本在分割处保留 8 个字符的重叠，防止一个词被分成两个
	d := newAliyunDivider(inputTexts)
	tasks := make([]aliyunTextTask, len(d.OutTexts))
	for i := range d.OutTexts {
		tasks[i] = aliyunTextTask{DataID: strconv.Itoa(i), Content: d.OutTexts[i]}
	}
	bizData := BizData{
		BizType: scanScene,
		Scenes:  []string{"antispam"},
		Tasks:   tasks,
	}
	ci := &ClientInfo{UserID: strconv.FormatInt(userID, 10)}
	resp := make([]AliyunTextRespElem, 0, len(tasks))
	requestID, err := c.doRequest(apiAliyunTextScan, bizData, ci, &resp)
	if err != nil {
		logger.WithFields(logger.Fields{
			"scene":      scanScene,
			"texts":      d.OutTexts,
			"request_id": requestID,
		}).Error(err)
		return nil, err
	}
	return d.combineAliyunTextResp(resp, requestID), nil
}

// divide 分割字符串分割后的字符串最长 max 字符，相邻的字符串有 overlap 个字符重叠
/* max = 7, overlap = 2 的情况下
org: 012345678901234567
[0]: 0123456
[1]:      5678901
[2]:           0123456
[3]:                567
*/
func divide(v string, max, overlap int) (vv []string) {
	if max < 1 {
		panic(fmt.Sprintf("unsupported max: %d", max))
	}
	if overlap < 0 || overlap >= max {
		panic(fmt.Sprintf("unsupported overlap: %d", overlap))
	}
	runes := []rune(v)
	vv = make([]string, 0, len(runes)/max+1)
	for begin := 0; begin < len(runes); begin += max - overlap {
		end := begin + max
		if end >= len(runes) {
			// 最后一个元素
			end = len(runes)
			vv = append(vv, string(runes[begin:end]))
			break
		}
		vv = append(vv, string(runes[begin:end]))
	}
	return vv
}

type aliyunDivider struct {
	InTexts []string

	max     int
	overlap int

	OutTexts []string
	Lens     []int
}

func newAliyunDivider(orgTexts []string) *aliyunDivider {
	d := &aliyunDivider{
		InTexts: orgTexts,
		max:     10000,
		overlap: 8, // 两段被分割的文本在分割处保留 8 个字符的重叠，防止一个词被分成两个

		OutTexts: make([]string, 0, len(orgTexts)+1), // 一般不会超过
		Lens:     make([]int, 0, len(orgTexts)),
	}
	for _, v := range orgTexts {
		t := divide(v, d.max, d.overlap)
		d.OutTexts = append(d.OutTexts, t...)
		d.Lens = append(d.Lens, len(t))
	}
	return d
}

// combineAliyunTextResp 把分割后的文本结果合并返回
// 如果分割后的文本检测异常，分割前的文本则认为也有异常，并将所有异常合并在同一个 AliyunTextRespElem 里返回
// 只有全部分割后的文本检测通过了，分割前的文本检测才认为是通过
func (d aliyunDivider) combineAliyunTextResp(resp []AliyunTextRespElem, requestID string) []AliyunTextRespElem {
	// 将阿里云返回的结果排序
	orderedResp := make([]*AliyunTextRespElem, len(resp))
	for i := range resp {
		idx, err := strconv.ParseInt(resp[i].DataID, 10, 64)
		if err != nil {
			logger.Error(err)
			continue
		}
		if idx > int64(len(d.OutTexts)) {
			logger.Error("dataId out of range")
			continue
		}
		orderedResp[idx] = &resp[i]
	}

	res := make([]AliyunTextRespElem, len(d.InTexts))
	for i, rangeStart := 0, 0; i < len(res); i, rangeStart = i+1, rangeStart+d.Lens[i] {
		if d.Lens[i] == 1 && orderedResp[rangeStart] != nil {
			// 文本没有被分割并且找到了请求结果的时候
			res[i] = *orderedResp[rangeStart]
			res[i].RequestID = requestID
			continue
		}
		res[i] = AliyunTextRespElem{
			Code:      0,
			Msg:       "OK",
			DataID:    strconv.Itoa(i),
			Content:   d.InTexts[i],
			Results:   make([]AliyunTextResult, 0),
			RequestID: requestID,
		}
		// 获取分割后的结束位置
		rangeEnd := rangeStart + d.Lens[i]
		for j := rangeStart; j < rangeEnd; j++ {
			if orderedResp[j] == nil || // 没有解析到请求
				len(orderedResp[j].Results) == 0 { // 返回结果异常，没有 Results
				continue
			}
			// 第一个没有偏移
			if j == rangeStart {
				res[i].Results = append(res[i].Results, orderedResp[j].Results...)
				continue
			}
			// 后续的检测结果需要对 pos 进行偏移
			// TODO: 封装成 aliyunDivider 的函数
			posAdd := (j - rangeStart) * (d.max - d.overlap)
			for _, result := range orderedResp[j].Results {
				// 对检测出的每个结果，根据位置进行偏移
				for k := range result.Details {
					for l := range result.Details[k].Context {
						for m := range result.Details[k].Context[l].Positions {
							result.Details[k].Context[l].Positions[m].StartPos += posAdd
							result.Details[k].Context[l].Positions[m].EndPos += posAdd
						}
					}
				}
				res[i].Results = append(res[i].Results, result)
			}
		}
	}
	return res
}

// ToScanCheckResult 转换成 *scan.CheckResult
func (elem AliyunTextRespElem) ToScanCheckResult(scene string, allowAd bool) *scan.CheckResult {
	res := scan.NewPassedCheckResult()
	resLen := len(elem.Results)
	if resLen == 0 {
		// 已经覆盖了 elem.Code != http.StatusOK 的情况
		return res
	}
	res.AliyunMatch = make([]scan.AliyunMatch, 0, resLen)
	resLabel := make(map[string]struct{})
	for _, v := range elem.Results {
		if v.Suggestion == aliyunSuggestionBlock {
			res.Pass = false
		}
		match := scan.AliyunMatch{
			RequestID: elem.RequestID,
			Label:     v.Label,
		}
		matchLabel := make([]string, 0, len(v.Details))
		for i := range v.Details {
			matchLabel = util.AppendStringOnce(matchLabel, v.Details[i].Label)
			resLabel[v.Details[i].Label] = struct{}{}
			for _, c := range v.Details[i].Context {
				sd := scan.AliyunDetail{
					Label:   v.Details[i].Label,
					Context: c.Context,
				}
				if c.LibName != "" {
					sd.LibName = []string{c.LibName}
				}
				if c.RuleType != "" {
					sd.RuleType = []string{c.RuleType}
				}
				match.Details = append(match.Details, &sd)
			}
		}
		res.AliyunMatch = append(res.AliyunMatch, match)
	}
	res.Labels = make([]string, 0, len(resLabel))
	for s := range resLabel {
		res.Labels = append(res.Labels, s)
	}
	if allowAd && len(res.Labels) == 1 && res.Labels[0] == scan.LabelAd {
		// 允许广告并且只有广告的 label 时，认为通过
		res.Pass = true
	}
	return res
}
