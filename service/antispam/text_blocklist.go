package antispam

import (
	"bytes"
	"errors"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/csv"
)

// 黑名单匹配模式
const (
	blocklistModeWholeStr = iota
	blocklistModeSubStr
	blocklistModeRegex
)

// 黑名单匹配 csv
const (
	blocklistNameBase     = "base_blacklist.csv"
	blocklistNameUserInfo = "user_info_blacklist.csv"
	blocklistNameSearch   = "search_blacklist.csv"
	blocklistNameEvil     = "evil_blacklist.csv"
)

// internalADBlocklist 内置广告黑名单
var internalADBlocklist = []*regexp.Regexp{
	// ad
	regexp.MustCompile("[招召].{0,3}(小.{0,3}[说說]|文.{0,3}字|打.{0,3}字)"),
	regexp.MustCompile("(小.{0,3}[说說]|文.{0,3}字).{0,3}([输录].{0,3}入|千字)"),
	regexp.MustCompile("[日曰].{0,3}[赚賺算]"),
}

// blocklist 黑名单
type blocklist struct {
	baseListMatch     *MatchList
	userInfoListMatch *MatchList
	searchListMatch   *MatchList
	evilListMatch     *MatchList

	adBlocklist []*regexp.Regexp
}

// MatchElem 黑名单 csv 文件单行匹配
type MatchElem struct {
	Mode    int
	Pattern string
	Regex   *regexp.Regexp
}

// MatchList 黑名单 csv 文件匹配
type MatchList []MatchElem

func newBlocklistCheckResult(violationContent []string, matchMode int, regex string) *scan.CheckResult {
	return &scan.CheckResult{
		Pass:   false,
		Labels: []string{scan.LabelBlacklist},
		BlacklistMatch: scan.BlacklistMatch{
			ViolationContent: violationContent,
			Mode:             matchMode,
			Regex:            regex,
		},
	}
}

// Check checks s with the rules in m.
// It returns nil if s didn't match any rules in m.
// s should be a lowercase string
func (m MatchElem) Check(s string) *scan.CheckResult {
	switch m.Mode {
	case blocklistModeWholeStr:
		if m.Pattern == s {
			return newBlocklistCheckResult([]string{m.Pattern}, blocklistModeWholeStr, "")
		}
	case blocklistModeSubStr:
		if strings.Contains(s, m.Pattern) {
			return newBlocklistCheckResult([]string{m.Pattern}, blocklistModeSubStr, "")
		}
	case blocklistModeRegex:
		if m.Regex != nil && m.Regex.MatchString(s) {
			allmatches := util.UniqString(m.Regex.FindAllString(s, -1))
			return newBlocklistCheckResult(allmatches, blocklistModeRegex, m.Regex.String())
		}
	}
	return nil
}

// Check checks s with each rule in the list
// It returns nil if s didn't match any rules in list.
func (list MatchList) Check(s string) *scan.CheckResult {
	s = strings.ToLower(s)
	for _, match := range list {
		if r := match.Check(s); r != nil {
			return r
		}
	}
	return nil
}

func newBlocklist(conf *BlocklistConfig) (*blocklist, error) {
	list := &blocklist{
		adBlocklist: internalADBlocklist,
	}
	var err error
	list.baseListMatch, err = NewMatchlistFromCSV(
		filepath.Join(conf.CSVDir, blocklistNameBase), conf.AllowEmpty)
	if err != nil {
		return nil, err
	}
	list.userInfoListMatch, err = NewMatchlistFromCSV(
		filepath.Join(conf.CSVDir, blocklistNameUserInfo), conf.AllowEmpty)
	if err != nil {
		return nil, err
	}
	list.searchListMatch, err = NewMatchlistFromCSV(
		filepath.Join(conf.CSVDir, blocklistNameSearch), conf.AllowEmpty)
	if err != nil {
		return nil, err
	}
	list.evilListMatch, err = NewMatchlistFromCSV(filepath.Join(conf.CSVDir, blocklistNameEvil), conf.AllowEmpty)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// NewMatchlistFromCSV 从 CSV 文件读取 Matchlist
func NewMatchlistFromCSV(file string, allowEmpty bool) (list *MatchList, err error) {
	defer func() {
		if allowEmpty {
			err = nil
			if list == nil {
				list = new(MatchList)
			}
		}
	}()
	var b []byte
	b, err = ioutil.ReadFile(file)
	if err != nil {
		return
	}
	list, err = newMatchListFromBytes(b, file)
	return
}

// newMatchListFromBytes 从 csv 数据读取参数，file 仅供记录日志
func newMatchListFromBytes(b []byte, file string) (*MatchList, error) {
	r, err := csv.NewReader(bytes.NewReader(b))
	if err != nil {
		return nil, err
	}

	blackListRecords, err := r.ReadAll()
	if err != nil {
		return nil, err
	}
	loggerEntry := func(file string, text []string, line int) *logger.Entry {
		e := logger.WithFields(logger.Fields{
			"file": file,
			"text": text,
			"line": line,
		})
		return e
	}
	modeErr := func(file string, text []string, line int, modeStr string) error {
		errStr := fmt.Sprintf("invalid mode: %s", modeStr)
		loggerEntry(file, text, line).Error(errStr)
		return errors.New(errStr)
	}
	list := make(MatchList, 0, len(blackListRecords))
loop:
	for i, text := range blackListRecords {
		if len(text) != 3 {
			loggerEntry(file, text, i).Error("line skipped")
			err = errors.New("line data error")
			break
		}
		pattern := strings.TrimSpace(text[0])
		// level := strings.TrimSpace(line[1]) // 暂时没使用
		var mode int
		mode, err = strconv.Atoi(strings.TrimSpace(text[2]))
		if err != nil {
			err = modeErr(file, text, i, text[2])
			break
		}
		var regex *regexp.Regexp
		switch mode {
		case blocklistModeWholeStr, blocklistModeSubStr:
			pattern = strings.ToLower(pattern)
		case blocklistModeRegex:
			regex, err = regexp.Compile(pattern)
			if err != nil {
				loggerEntry(file, text, i).Errorf("invalid regular expression: %v", err)
				break loop
			}
		default:
			err = modeErr(file, text, i, text[2])
			break loop
		}

		list = append(list, MatchElem{
			Mode:    mode,
			Pattern: pattern,
			Regex:   regex,
		})
	}
	return &list, err
}

// CheckBase base 检查，各种 scene 均需要检查
func (b blocklist) CheckBase(s string) (res *scan.CheckResult) {
	defer func() {
		if res != nil {
			res.Labels = append(res.Labels, scan.LabelBase)
		}
	}()
	for _, r := range b.adBlocklist {
		if r.MatchString(s) {
			violationContents := util.UniqString(r.FindAllString(s, -1))
			res = newBlocklistCheckResult(violationContents, blocklistModeRegex, r.String())
			return
		}
	}

	res = b.baseListMatch.Check(s)
	return
}

func (b blocklist) CheckText(texts []string, scene string) []*scan.CheckResult {
	res := make([]*scan.CheckResult, len(texts))
	switch scene {
	case scan.SceneUserInfo:
		for i := range texts {
			res[i] = b.CheckBase(texts[i])
			r := b.userInfoListMatch.Check(texts[i])
			if r == nil {
				continue
			}
			if res[i] == nil {
				res[i] = r
			}
			res[i].Labels = append(res[i].Labels, scan.LabelUserInfo)
		}
	case scan.SceneSearch:
		for i := range texts {
			res[i] = b.CheckBase(texts[i])
			r := b.searchListMatch.Check(texts[i])
			if r == nil {
				continue
			}
			if res[i] == nil {
				res[i] = r
			}
			res[i].Labels = append(res[i].Labels, scan.LabelSearch)
		}
	default:
		for i := range texts {
			res[i] = b.CheckBase(texts[i])
		}
	}
	return res
}

// checkEvil 判断 s 是否是 evil 违规词
func (b blocklist) checkEvil(s string) bool {
	return b.evilListMatch.Check(s) != nil
}

// CheckEvil 判断 s 是否是 evil 违规词
func (c *Client) CheckEvil(s string) bool {
	return c.blocklist.checkEvil(s)
}
