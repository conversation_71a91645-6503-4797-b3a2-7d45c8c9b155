package antispam

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCheckImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	testClient, err := NewClient(TestConfig())
	require.NoError(err)

	res, err := testClient.CheckImage("https://static.maoercdn.com/avatars/icon01.png", 0, "")
	require.NoError(err)
	assert.True(res.Pass)

	/* FIXME: 暂时没有合适的 case
	// 在 user_info 场景下，review 级别的也是 not pass
	res, err = testClient.CheckImage("https://static-test.missevan.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg", 0, scan.SceneUserInfo)
	require.NoError(err)
	assert.False(res.Pass)
	assert.Contains(res.Labels, "others")
	*/

	res, err = testClient.CheckImage("https://static-test.missevan.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg", 0, "")
	require.NoError(err)
	require.NotNil(res)
	assert.False(res.Pass)

	testClient.Config.Provider = ""
	res, err = testClient.CheckImage("https://static.maoercdn.com/avatars/icon01.png", 0, "")
	require.NoError(err)
	require.NotNil(res)
	assert.True(res.Pass)
}
