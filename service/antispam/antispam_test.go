package antispam

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	testClient *Client
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	var err error
	testClient, err = NewClient(TestConfig())
	if err != nil {
		logger.Fatal(err)
	}
	m.Run()
}

func TestConfigTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)

	kc.Check(Config{}, "provider", "region_id", "access_key_id", "access_key_secret",
		"endpoint", "blacklist")
	kc.Check(BlocklistConfig{}, "csv_dir")
}

func TestImage(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	img := "https://img.alicdn.com/tfs/TB1urBOQFXXXXbMXFXXXXXXXXXX-1442-257.png"
	c, err := NewClient(TestConfig())
	require.NoError(err)
	requestID, r, err := c.ImageSyncScan(img, 0, "")
	require.NoError(err)
	assert.NotEmpty(requestID)
	assert.NotEmpty(r)
	for _, v := range r.Results {
		assert.Equal("normal", v.Label)
		assert.Equal("pass", v.Suggestion)
	}

	imgList := []string{img}
	_, mapTaskIDs, err := c.ImageAsyncScan(imgList, 0)
	require.NoError(err)
	assert.Len(mapTaskIDs, len(imgList))
	var taskID []string
	for _, v := range mapTaskIDs {
		taskID = append(taskID, v)
	}
	time.Sleep(time.Second)
	_, mapResults, err := c.ImageAsyncScanResults(taskID)
	require.NoError(err)
	assert.Len(mapResults, len(imgList))
	for i := range imgList {
		dataID := strconv.Itoa(i)
		taskID := mapTaskIDs[dataID]
		assert.Equal(taskID, mapResults[dataID].TaskID)
		results := mapResults[dataID].Results
		assert.NotEmpty(results)
		for _, v := range results {
			assert.Equal("normal", v.Label)
			assert.Equal("pass", v.Suggestion)
		}
	}
}
