package antispam

import (
	"path"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestNewBlocklistCheckResult(t *testing.T) {
	assert := assert.New(t)

	cr := newBlocklistCheckResult([]string{"abc"}, blocklistModeWholeStr, "123")
	assert.Equal(&scan.CheckResult{
		Pass:   false,
		Labels: []string{scan.LabelBlacklist},
		BlacklistMatch: scan.BlacklistMatch{
			ViolationContent: []string{"abc"},
			Mode:             blocklistModeWholeStr,
			Regex:            "123",
		},
	}, cr)
}

func TestMatchElemCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	elem := MatchElem{
		Mode:    blocklistModeWholeStr,
		Pattern: "test",
	}
	res := elem.Check("test")
	require.NotNil(res)
	assert.Equal(elem.Mode, res.BlacklistMatch.Mode)
	assert.Equal([]string{"test"}, res.BlacklistMatch.ViolationContent)
	assert.Empty(res.BlacklistMatch.Regex)
	assert.Nil(elem.Check("test test"))

	elem.Mode = blocklistModeSubStr
	res = elem.Check("test test")
	require.NotNil(res)
	assert.Equal(elem.Mode, res.BlacklistMatch.Mode)
	assert.Equal([]string{"test"}, res.BlacklistMatch.ViolationContent)
	assert.Empty(res.BlacklistMatch.Regex)

	elem.Mode = blocklistModeRegex
	elem.Regex = internalADBlocklist[2]
	res = elem.Check("日赚998")
	require.NotNil(res)
	assert.Equal(elem.Mode, res.BlacklistMatch.Mode)
	assert.Equal([]string{"日赚"}, res.BlacklistMatch.ViolationContent)
	assert.Equal(elem.Regex.String(), res.BlacklistMatch.Regex)

	assert.Nil(elem.Check("日"))
}

func TestMatchListCheck(t *testing.T) {
	assert := assert.New(t)

	list := MatchList{
		MatchElem{Mode: blocklistModeRegex, Regex: internalADBlocklist[2]},
	}
	assert.NotNil(list.Check("日ABC赚"))
	assert.Nil(list.Check("日"))
}

func TestNewBlocklist(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := &BlocklistConfig{AllowEmpty: true}
	l, err := newBlocklist(conf)
	require.NoError(err)
	assert.NotNil(l)
}

func TestNewMatchlistFromCSV(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := NewMatchlistFromCSV("", true)
	assert.NoError(err)

	l, err := NewMatchlistFromCSV(path.Join("../../testdata/service/antispam/test_blacklist.csv"), false)
	require.NoError(err)
	assert.NotNil(l)
}

func TestNewMatchListFromBytes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 正确情况
	b := []byte(
		`完全匹配,30,0
部分匹配,40,1
"^.{0,2}正.?则.?表.{0,2}$",50,2
`)
	l, err := newMatchListFromBytes(b, "")
	require.NoError(err)
	assert.NotNil(l)

	b = []byte(`部分匹配,40
完全匹配,30,0`)
	_, err = newMatchListFromBytes(b, "")
	logger.Debug(err)
	assert.Error(err, "格式错误")

	b = []byte(`完全匹配,30,a
	完全匹配,30,0`)
	_, err = newMatchListFromBytes(b, "")
	logger.Debug(err)
	assert.Error(err, "模式解析错误")

	b = []byte(`完全匹配,30,-1
完全匹配,30,0`)
	_, err = newMatchListFromBytes(b, "")
	logger.Debug(err)
	assert.Error(err, "模式不匹配")

	b = []byte(`"??",50,2
完全匹配,30,0`)
	_, err = newMatchListFromBytes(b, "")
	logger.Debug(err)
	assert.Error(err, "正则表达式错误")
}

func TestBlocklistCheckBase(t *testing.T) {
	assert := assert.New(t)

	l := blocklist{
		adBlocklist:   internalADBlocklist,
		baseListMatch: &MatchList{MatchElem{Mode: blocklistModeWholeStr, Pattern: "test"}},
	}

	res := l.CheckBase("日赚")
	assert.NotNil(res)
	assert.Contains(res.Labels, "base")

	res = l.CheckBase("test")
	assert.Contains(res.Labels, "base")
}

func TestBlocklistCheckText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := blocklist{
		adBlocklist:       internalADBlocklist,
		baseListMatch:     &MatchList{MatchElem{Mode: blocklistModeWholeStr, Pattern: "base"}},
		searchListMatch:   &MatchList{MatchElem{Mode: blocklistModeWholeStr, Pattern: "search"}},
		userInfoListMatch: &MatchList{MatchElem{Mode: blocklistModeWholeStr, Pattern: "user"}},
	}

	check := func(text, scene string, pass bool, label string) {
		res := l.CheckText([]string{text}, scene)
		require.Len(res, 1)
		if pass {
			assert.Nil(res[0], text, scene)
		} else {
			require.NotNil(res[0], text, scene)
			assert.Contains(res[0].Labels, label)
		}
	}

	check("123", scan.SceneIM, true, "")
	check("base", scan.SceneIM, false, scan.LabelBase)
	check("search", scan.SceneSearch, false, scan.LabelSearch)
	check("user", scan.SceneUserInfo, false, scan.LabelUserInfo)
}

func TestBlocklistCheckEvil(t *testing.T) {
	assert := assert.New(t)

	l := blocklist{
		evilListMatch: &MatchList{MatchElem{Mode: blocklistModeWholeStr, Pattern: "evil"}},
	}
	assert.True(l.checkEvil("evil"))
	assert.False(l.checkEvil("123"))
}

// benchmark

func BenchmarkCheckInternalText(b *testing.B) {
	conf := TestConfig()
	list, _ := newBlocklist(&conf.Blacklist)
	line := "招打字员"
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			list.CheckBase(line)
		}
	})
}
