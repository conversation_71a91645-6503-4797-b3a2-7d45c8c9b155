// +build !release

package antispam

import (
	"path"
	"runtime"
)

// TestConfig 单元测试配置
func TestConfig() *Config {
	_, csvDir, _, _ := runtime.Caller(0)
	csvDir = path.Join(path.Dir(csvDir), "../../testdata/service/antispam")
	// tutil.Debugf("csvDir: %s", csvDir)
	conf := BuildDefaultConfig().WithAccess("LTAIsNW7Hxzgnxu2",
		"AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o")
	conf.Blacklist.CSVDir = csvDir
	return conf
}
