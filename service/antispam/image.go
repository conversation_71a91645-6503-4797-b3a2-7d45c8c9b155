package antispam

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// 第三方（阿里云）图片检测场景分类
const (
	imageScenePorn      = "porn"      // 涉黄
	imageSceneTerrorism = "terrorism" // 暴恐涉政
	imageSceneAd        = "ad"        // 图文违规
	imageSceneQrcode    = "qrcode"    // 图片二维码
	imageSceneLive      = "live"      // 图片不良场景
	imageSceneLogo      = "logo"      // 图片 logo
)

var (
	imageCommonScenes = []string{imageScenePorn, imageSceneTerrorism, imageSceneAd}
)

type imageTask struct {
	DataID string `json:"dataId,omitempty"`
	URL    string `json:"url"`
}

// ImageScanRespData 某张图片的检测结果
type ImageScanRespData struct {
	ErrorAPI
	DataID  string            `json:"dataId,omitempty"`
	TaskID  string            `json:"taskId"`
	URL     string            `json:"url"`
	Results []ImageScanResult `json:"results"`
}

// ImageScanResult 某张图片对应 scene 和 label 的检测结果
// https://help.aliyun.com/document_detail/70292.html
type ImageScanResult struct {
	Scene      string  `json:"scene"`
	Label      string  `json:"label"`
	Suggestion string  `json:"suggestion"`
	Rate       float64 `json:"rate"` // 置信度分数（该值仅作为参考），取值范围：0（表示置信度最低）~100（表示置信度最高）
}

// CheckImage 检测单张图片
// NOTICE: 返回的第一个参数可能会返回 nil
func (c *Client) CheckImage(url string, userID int64, scene string) (*scan.CheckResult, error) {
	ret := &scan.CheckResult{
		Pass: true,
	}
	switch c.Provider {
	case "aliyun":
		requestID, result, err := c.ImageSyncScan(url, userID, scene)
		if err != nil {
			// TODO: 图片异步检测
			logger.Errorf("ImageSyncScan error: %v", err)
			return nil, nil
		}
		ret.TaskID = result.TaskID
		ret.AliyunMatch = append(ret.AliyunMatch, scan.AliyunMatch{
			RequestID: requestID,
			TaskID:    result.TaskID,
		})
		for _, v := range result.Results {
			if v.Suggestion == aliyunSuggestionBlock || (scene == scan.SceneUserInfo && v.Suggestion == aliyunSuggestionReview) {
				ret.Pass = false
			}
			if !ret.Pass {
				ret.Labels = append(ret.Labels, v.Label)
			}
		}
		return ret, nil
	}
	return ret, nil
}

// ImageSyncScan 单张图片同步检测
func (c *Client) ImageSyncScan(url string, userID int64, bizType string) (string, *ImageScanRespData, error) {
	scenes := imageCommonScenes
	if bizType == scan.SceneUserInfo {
		// 用户信息相关的图片检测，需要额外加上广告引流相关场景（模型）检测
		scenes = append(scenes, imageSceneQrcode, imageSceneLive)
	}
	bizData := BizData{
		BizType: bizType,
		Scenes:  scenes,
		Tasks:   []imageTask{{URL: url}},
	}
	ci := &ClientInfo{UserID: strconv.FormatInt(userID, 10)}
	var data []ImageScanRespData
	requestID, err := c.doRequest("/green/image/scan", bizData, ci, &data)
	if err != nil {
		return "", nil, err
	}
	if len(data) < 1 {
		return "", nil, errors.New("aliyun returns empty data")
	}
	if data[0].Code != http.StatusOK {
		msg := fmt.Sprintf("(%s) %s - %s", requestID, data[0].TaskID, data[0].Msg)
		return "", nil, &serviceutil.APIError{Status: data[0].Code, Message: msg}
	}
	return requestID, &data[0], nil
}

// ImageAsyncRet 图片异步检测 API 的调用返回值
type ImageAsyncRet struct {
	ErrorAPI
	DataID string `json:"dataId,omitempty"`
	TaskID string `json:"taskId"`
}

// MapDataIDTaskID maps dataId to taskId
type MapDataIDTaskID map[string]string

// ImageAsyncScan 图片异步检测
func (c *Client) ImageAsyncScan(url []string, userID int64) (requestID string, mapTaskID MapDataIDTaskID, err error) {
	var tasks []imageTask
	for i, v := range url {
		tasks = append(tasks, imageTask{
			DataID: strconv.Itoa(i),
			URL:    v,
		})
	}
	// TODO: 用户信息相关的图片检测，需要额外加上广告引流相关场景（模型）检测
	bizData := BizData{
		Scenes: imageCommonScenes,
		Tasks:  tasks,
	}
	ci := &ClientInfo{UserID: strconv.FormatInt(userID, 10)}
	var data []ImageAsyncRet
	requestID, err = c.doRequest("/green/image/asyncscan", bizData, ci, &data)
	if err != nil {
		return "", nil, err
	}
	if len(data) < 1 {
		return "", nil, errors.New("aliyun returns empty data")
	}
	mapTaskID = make(MapDataIDTaskID)
	for _, v := range data {
		if v.Code == http.StatusOK {
			mapTaskID[v.DataID] = v.TaskID
		}
	}
	return requestID, mapTaskID, nil
}

// MapDataIDResultsImgScan maps dataId to ImageScanResults
type MapDataIDResultsImgScan map[string]ImageScanRespData

// ImageAsyncScanResults 查询图片异步检测结果
func (c *Client) ImageAsyncScanResults(TaskID []string) (requestID string, mapResult MapDataIDResultsImgScan, err error) {
	var data []ImageScanRespData
	requestID, err = c.doRequest("/green/image/results", TaskID, nil, &data)
	if err != nil {
		return "", nil, err
	}
	mapResult = make(MapDataIDResultsImgScan)
	for _, v := range data {
		mapResult[v.DataID] = v
	}
	return requestID, mapResult, nil
}
