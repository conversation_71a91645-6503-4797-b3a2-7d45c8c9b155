package servicegaia

import (
	"crypto/tls"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"golang.org/x/net/http2"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/service/util/retryhttp"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 场景
const (
	SceneLiveIM = "maoer_live_im"
)

// api
const (
	APIRuleCheck = "/x/service/api/gaia/rule/check"
)

// Config config for client
type Config struct {
	URL              string `yaml:"url"`
	Host             string `yaml:"host"`
	AppKey           string `yaml:"app_key"`
	AppSecret        string `yaml:"app_secret"`
	HTTP2            bool   `yaml:"http2"`
	DisableSSLVerify bool   `yaml:"disable_ssl_verify"`
}

// RequestParams 请求参数
type RequestParams interface {
	// BeforeDo 实际请求前执行，比如添加 cookies
	BeforeDo(*http.Request) error
}

// response 风控接口返回响应
type response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TTL     int64  `json:"ttl"`
	Data    struct {
		Decisions []string `json:"decisions"`
	} `json:"data"`
}

// Client gaia 客户端
type Client struct {
	Config
	*http.Client
}

// NewClient new Client
func NewClient(conf Config) (*Client, error) {
	c := &Client{
		Config: conf,
	}
	var tr http.RoundTripper
	if strings.HasPrefix(conf.URL, "https://") {
		var tlsCfg *tls.Config
		if conf.DisableSSLVerify {
			tlsCfg = &tls.Config{
				InsecureSkipVerify: true,
			}
		}
		if conf.HTTP2 {
			// 仅在可以使用 http2 的情况下使用 http2
			tr = &http2.Transport{TLSClientConfig: tlsCfg}
		} else if tlsCfg != nil {
			// 非 http2 的情况下，仅判断是否需要跳过 SSL 检查
			tr = &http.Transport{
				TLSClientConfig: tlsCfg,
			}
		}
	}
	c.Client = retryhttp.NewEOFRetryClient(tr)
	return c, nil
}

// Call 请求
func (c Client) Call(api string, params RequestParams) ([]string, error) {
	api = c.URL + api

	// 接入文档: https://info.bilibili.co/pages/viewpage.action?pageId=114145712
	form := make(url.Values, 6)
	form.Set("scene", SceneLiveIM)
	nowStr := strconv.FormatInt(util.TimeNow().Unix(), 10)
	form.Set("event_ts", nowStr)
	ctx, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	form.Set("event_ctx", string(ctx))

	formStr := blademaster.Sign(c, form)

	req, err := http.NewRequest(http.MethodPost, api, strings.NewReader(formStr))
	if err != nil {
		return nil, err
	}
	err = params.BeforeDo(req)
	if err != nil {
		return nil, err
	}
	req.Host = c.Host
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("User-Agent", serviceutil.UserAgent)

	formStr, _ = url.QueryUnescape(formStr)
	logger.Debugf("%s %s\n%s", req.Method, api, formStr)

	resp, err := c.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\n%s\nHTTP %s\n%s", req.Method, api, formStr, resp.Status, body)
		}
		return nil, &serviceutil.APIError{
			Status:  resp.StatusCode,
			Message: string(body),
		}
	}

	var r response
	err = json.Unmarshal(body, &r)
	if err != nil {
		return nil, err
	}
	if r.Code != 0 {
		return nil, &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: r.Message,
		}
	}
	if r.Data.Decisions == nil {
		return []string{}, nil
	}
	return r.Data.Decisions, nil
}

// SignParams sign params
func (c Config) SignParams() (appKey, appSecret string) {
	return c.AppKey, c.AppSecret
}
