package servicegaia

import (
	"encoding/json"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var (
	testConfig Config
	testClient *Client
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	testConfig = TestConfig()
	var err error
	testClient, err = NewClient(testConfig)
	if err != nil {
		logger.Fatal(err)
	}
	os.Exit(m.Run())
}

func TestClientCall(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := make(SmartParams)
	params["chan"] = make(chan int)
	_, err := testClient.Call(APIRuleCheck, params)
	assert.Error(err, "json 有问题")
	delete(params, "chan")

	_, err = testClient.Call("\n", params)
	assert.Error(err, "NewRequest 失败")
	assert.EqualError(err, `parse "https://uat-maoer.biliapi.net\n": net/url: invalid control character in URL`)

	conf := TestConfig()
	conf.URL = "https://localhost:12"
	c, err := NewClient(conf)
	require.NoError(err)
	_, err = c.Call(APIRuleCheck, params)
	assert.EqualError(err, `Post "https://localhost:12/x/service/api/gaia/rule/check": dial tcp 127.0.0.1:12: connect: connection refused`)

	str := `{
    "mid": 23333,
    "buvid": " ",
    "ip": "************",
    "platform": "web",
    "ctime": "2020-10-09 12:20:00",
    "action": "",
    "api": "/rpc/broadcast",
    "origin": "",
    "referer": "https://fm.uat.missevan.com/",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36",
    "build": "",
    "area": "maoer_live_im",
    "content": "没问题",
    "content_id": "1da576f8-02a6-4b60-bf3b-ec4678e85302",
    "content_type": "text",
    "room_id": 112422405,
    "room_catalog_id": 116,
    "room_creator_id": 3456835,
    "msg_id": "1da576f8-02a6-4b60-bf3b-ec4678e85302",
    "msg_sent_time": "2020-10-09 12:20:00",
    "pass": true,
    "allow_ad": false,
    "user_has_medal": false,
    "user_is_admin": false,
    "aliyun_labels": ""
	}`
	// var params SmartParams
	require.NoError(json.Unmarshal([]byte(str), &params))
	res, err := testClient.Call(APIRuleCheck, params)
	require.NoError(err)
	assert.NotNil(res)
}
