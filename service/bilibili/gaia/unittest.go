//go:build !release
// +build !release

package servicegaia

import "net/http"

// TestConfig 返回测试配置
func TestConfig() Config {
	return Config{
		URL:              "http://uat-maoer-api.bilibili.co",
		AppKey:           "af6bf012e19fa847",
		AppSecret:        "2d0c40a454d40bece96ba4e2e95c815f",
		DisableSSLVerify: true,
	}
}

// SmartParams smart RequestParams
type SmartParams map[string]interface{}

// BeforeDo 空的请求操作
func (params SmartParams) BeforeDo(*http.Request) error { return nil }
