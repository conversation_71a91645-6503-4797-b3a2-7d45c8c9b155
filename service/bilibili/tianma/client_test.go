package tianma

import (
	"net/http"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestClient_Request(t *testing.T) {
	client := NewClient()

	t.Run("Test Get", func(t *testing.T) {
		type Response struct {
			Code int    `json:"code"`
			Data string `json:"data"`
		}

		var resp Response
		err := client.request(http.MethodGet, "/recommand", nil, &resp)
		require.EqualError(t, err, "request tianma api failed, status code: 400")
		assert.Equal(t, 0, resp.Code)
	})
}

func TestClient_GetHost(t *testing.T) {
	client := NewClient()

	t.Run("Production Environment", func(t *testing.T) {
		originalEnv := os.Getenv(util.EnvDeploy)
		defer os.Setenv(util.EnvDeploy, originalEnv)

		os.Setenv(util.EnvDeploy, util.DeployEnvProd)

		host := client.getHost()
		assert.Equal(t, hostProd, host)
	})

	t.Run("Pre-production Environment", func(t *testing.T) {
		originalEnv := os.Getenv(util.EnvDeploy)
		defer os.Setenv(util.EnvDeploy, originalEnv)

		os.Setenv(util.EnvDeploy, "pre")

		host := client.getHost()
		assert.Equal(t, hostPre, host)
	})
}
