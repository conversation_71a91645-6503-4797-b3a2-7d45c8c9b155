package tianma

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestClient_Recommend(t *testing.T) {
	client := NewClient()

	params := RecommendParams{
		Cmd:        RecommendCmdHome,
		MID:        123,
		Buvid:      "test_buvid",
		RequestCnt: 10,
		Timeout:    1000,
		DisplayID:  1,
		FreshType:  FreshTypeNormal,
		Network:    NetworkWifi,
		Country:    "CN",
		Sex:        SexMale,
		Persona:    1,
		TS:         1615000000,
		Chid:       "test_chid",
		Model:      "test_model",
		Platform:   PlatformAndroid,
		Version:    "1.0.0",
	}
	operateForcedInsertions := []OperateForcedInsertionItem{
		{ID: 111, Position: 5}, {ID: 222, Position: 5},
	}
	operateForcedInsertionsBytes, err := json.Marshal(operateForcedInsertions)
	require.NoError(t, err)
	params.OperateForcedInsertions = string(operateForcedInsertionsBytes)
	result, err := client.Recommend(params)
	require.NoError(t, err)
	assert.NotNil(t, result)
}
