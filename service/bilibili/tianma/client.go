package tianma

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/google/go-querystring/query"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 默认值
const (
	UnknownIntValue    = -1
	UnknownStringValue = ""
)

// 状态码
const (
	CodeOK                  int32 = 0   // 正常响应
	CodeBSFallback          int32 = 600 // 触发天马 BS 服务兜底
	CodeRouterFallback      int32 = 601 // 触发天马 Router 服务兜底
	CodeItemReqCntNotEnough int32 = -3  // 返回 Item 数量不足
	CodeNoResult            int32 = -77 // 无返回结果
	CodeBadRequest          int32 = 400 // 错误请求
)

// Client 天马推荐客户端
type Client struct {
	httpClient *http.Client
}

// NewClient 创建天马推荐客户端
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: 15 * time.Second,
		},
	}
}

// Timeout 获取 HTTP 客户端的超时时间
func (c *Client) Timeout() time.Duration {
	return c.httpClient.Timeout
}

// Get 发送 GET 请求
func (c *Client) Get(path string, params any, v any) error {
	values, err := query.Values(params)
	if err != nil {
		return err
	}
	path = path + "?" + values.Encode()
	return c.request(http.MethodGet, path, nil, v)
}

// Post 发送 POST 请求
func (c *Client) Post(path string, data any, v any) error {
	return c.request(http.MethodPost, path, data, v)
}

func (c *Client) request(method, path string, data any, v any) error {
	requestBody, err := json.Marshal(data)
	if err != nil {
		return err
	}

	request, err := c.newRequest(method, path, requestBody)
	if err != nil {
		return err
	}

	return c.doRequest(request, v)
}

func (c *Client) newRequest(method, path string, body []byte) (*http.Request, error) {
	url := c.getHost() + path
	request, err := http.NewRequest(method, url, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "application/json")

	logger.Debugf("%s %s\n%s", method, request.URL.String(), body)

	return request, nil
}

func (c *Client) doRequest(request *http.Request, v any) error {
	response, err := c.httpClient.Do(request)
	if err != nil {
		return err
	}
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("request tianma api failed, status code: %d", response.StatusCode)
	}

	defer response.Body.Close()
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		return err
	}

	logger.Debugf("HTTP %s\n%s", response.Status, responseBody)

	err = json.Unmarshal(responseBody, v)
	if err != nil {
		return err
	}
	return nil
}

const (
	hostProd = "http://data.bilibili.co"
	hostPre  = "http://pre-data.bilibili.co"
)

func (c *Client) getHost() string {
	v := os.Getenv(util.EnvDeploy)
	if v == util.DeployEnvProd {
		return hostProd
	}
	return hostPre
}
