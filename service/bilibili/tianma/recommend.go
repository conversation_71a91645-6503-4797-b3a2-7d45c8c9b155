package tianma

// 天马推荐接口路径，对接文档：https://info.bilibili.co/pages/viewpage.action?pageId=936796623
// NOTICE: 算法侧的推荐接口由于历史原因，接口路径为 /recommand
const recommendPath = "/recommand"

// RecommendCmd 推荐业务请求标示
type RecommendCmd = string

// 推荐业务请求标示
const (
	RecommendCmdHome      RecommendCmd = "maoer_home"     // 首页猜你喜欢
	RecommendCmdLive      RecommendCmd = "maoer_live"     // 首页直播推荐
	RecommendCmdHomeFeeds RecommendCmd = "maoer_feeds"    // 首页 Feed 流
	RecommendCmdLiveTab   RecommendCmd = "maoer_live_tab" // 直播 Feed 流
)

// Network 网络类型
type Network = string

// 网络类型
const (
	NetworkWifi     Network = "WIFI"
	NetworkCellular Network = "CELLULAR"
	NetworkOtherNet Network = "OTHERNET"
)

// Sex 性别
type Sex = int32

// 性别
const (
	SexFemale Sex = 0
	SexMale   Sex = 1
)

// Platform 设备平台
type Platform = string

// 设备平台
const (
	PlatformAndroid   Platform = "android"
	PlatformIOS       Platform = "ios"
	PlatformWeb       Platform = "web"
	PlatformMobileWeb Platform = "mobile_web"
	PlatformWindows   Platform = "windows"
	PlatformHarmonyOS Platform = "harmony_os"
)

// Goto 卡片类型
type Goto string

// 卡片类型
const (
	GotoSound   Goto = "sound"    // 音频卡
	GotoDrama   Goto = "drama"    // 剧集卡
	GotoLive    Goto = "live"     // 直播卡
	GotoOpSound Goto = "op_sound" // 音频运营卡
	GotoOpDrama Goto = "op_drama" // 剧集运营卡
	GotoOpLive  Goto = "op_live"  // 直播运营卡
)

// IsSound 判断是否为音频卡
func (g Goto) IsSound() bool {
	return g == GotoSound || g == GotoOpSound
}

// IsDrama 判断是否为剧集卡
func (g Goto) IsDrama() bool {
	return g == GotoDrama || g == GotoOpDrama
}

// IsLive 判断是否为直播卡
func (g Goto) IsLive() bool {
	return g == GotoLive || g == GotoOpLive
}

// IsOP 判断是否为运营干预卡
func (g Goto) IsOP() bool {
	switch g {
	case GotoOpSound, GotoOpDrama, GotoOpLive:
		return true
	default:
		return false
	}
}

// FreshType 刷新类型
type FreshType = int32

// 刷新类型
const (
	FreshTypeNormal       FreshType = 0 // 默认值
	FreshTypeAuto         FreshType = 1 // 自动刷新
	FreshTypeTopPull      FreshType = 2 // 顶部下拉
	FreshTypeChange       FreshType = 3 // 点击换一批
	FreshTypeBottomScroll FreshType = 4 // 底部上滑
)

// 个性化推荐
const (
	PersonalizedOpen  = 0 // 开启个性化推荐
	PersonalizedClose = 1 // 关闭个性化推荐
)

// 强插卡是否需要频控
const (
	ForcedInsertionPvControlNo  = 0 // 不需要
	ForcedInsertionPvControlYes = 1 // 需要
)

// RecommendParams 定义推荐接口的请求参数
type RecommendParams struct {
	// 必填字段
	Cmd        RecommendCmd `url:"cmd,required"` // 业务请求标示
	MID        int64        `url:"mid"`          // 用户 ID
	Buvid      string       `url:"buvid"`        // 设备号
	RequestCnt int64        `url:"request_cnt"`  // 推荐 Item 个数
	Timeout    int64        `url:"timeout"`      // 请求超时，单位毫秒
	DisplayID  int64        `url:"display_id"`   // 当前 Session 内的请求 ID，从 1 开始
	FreshType  FreshType    `url:"fresh_type"`   // 刷新方式
	Network    Network      `url:"network"`      // 用户当前网络状况
	Country    string       `url:"country"`      // 用户所在国家
	Sex        Sex          `url:"sex"`          // 用户性别
	Persona    int64        `url:"persona"`      // 用户画像
	TS         int64        `url:"ts"`           // 当前时间戳，秒级
	Chid       string       `url:"chid"`         // 渠道标识
	Model      string       `url:"model"`        // 设备型号
	Platform   Platform     `url:"platform"`     // 设备平台
	Version    string       `url:"version"`      // 设备版本号

	// 可选字段
	ZoneID                     string `url:"zone_id,omitempty"`                      // 地区标示 ID
	Province                   string `url:"province,omitempty"`                     // 用户所在省份
	City                       string `url:"city,omitempty"`                         // 用户所在城市
	FirstLoginTime             int64  `url:"first_logintime,omitempty"`              // 首次登录时间戳，秒级
	ClosePersonalizedRecommend int32  `url:"close_personalized_recommend,omitempty"` // 是否关闭个性化推荐，0：正常推荐，1：关闭个性化推荐

	// 直播 Feed 流特有字段
	LiveTabScene            int64  `url:"live_tab_scene,omitempty"`            // 标识二级分区场景（新星：-1；热门：0；其他二级分区：分区 ID）
	OperateForcedInsertions string `url:"operate_forced_insertions,omitempty"` // 强插位置列表（json 格式）

	// 首页 Feed 流特有字段
	Page int64 `url:"page,omitempty"` // 页码
}

// OperateForcedInsertionItem 强插项
type OperateForcedInsertionItem struct {
	ID        int64 `json:"id"`         // 元素 ID
	Position  int64 `json:"pos"`        // 需要插入的位置（从 0 开始计算）
	PvControl int   `json:"pv_control"` // 是否需要频控（0：不需要，1：需要）
}

// RecommendResult 推荐接口的返回结果
type RecommendResult struct {
	Code        int32           `json:"code"`         // 状态码
	Data        []RecommendItem `json:"data"`         // 卡片列表
	UserFeature string          `json:"user_feature"` // 用户特征

	// 首页 Feed 流特有字段
	ExtraCards []ExtraCards `json:"extra_cards,omitempty"` // 异形卡信息
}

// ExtraCards 异形卡信息
type ExtraCards struct {
	Type     string `json:"type"`     // 卡片类型
	Position int64  `json:"position"` // 卡片位置
}

// RecommendItem 推荐元素
type RecommendItem struct {
	ID        int64  `json:"id"`         // 卡片 ID
	Goto      Goto   `json:"goto"`       // 卡片类型
	Source    string `json:"source"`     // 卡片来源
	AVFeature string `json:"av_feature"` // 卡片特征
	TrackID   string `json:"trackid"`    // 埋点 ID
}

// Recommend 推荐接口
func (c *Client) Recommend(params RecommendParams) (*RecommendResult, error) {
	var result RecommendResult
	err := c.Get(recommendPath, params, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
