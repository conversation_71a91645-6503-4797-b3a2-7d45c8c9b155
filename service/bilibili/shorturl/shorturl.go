package shorturl

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/service/util/retryhttp"
)

// Config config for shorturl client
type Config struct {
	URL       string `yaml:"url"`
	Host      string `yaml:"host"`
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
}

// Client shorturl client
type Client struct {
	Config
	*http.Client
}

// SignParams sign params
func (c Config) SignParams() (appKey, appSecret string) {
	return c.AppKey, c.AppSecret
}

// NewClient new Client
func NewClient(conf Config) (*Client, error) {
	return &Client{
		Config: conf,
		Client: retryhttp.NewEOFRetryClient(nil),
	}, nil
}

// RequestParams 请求参数
type RequestParams struct {
	Business  string `json:"business"`
	OriginURL string `json:"origin_url"`
}

// Call 请求通用函数
func (c *Client) Call(api string, params RequestParams, res interface{}) error {
	if params.OriginURL == "" {
		panic("参数不能为空")
	}
	form := make(url.Values, 2)
	formStr := blademaster.Sign(c, form)

	reqBody, err := json.Marshal(params)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s%s?%s", c.URL, api, formStr), bytes.NewReader(reqBody))
	if err != nil {
		return err
	}
	req.Host = c.Host
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("User-Agent", serviceutil.UserAgent)

	logger.Debugf("%s %s?%s\n%s", req.Method, api, formStr, reqBody)

	resp, err := c.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)

	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\n%s\nHTTP %s\n%s", req.Method, api, formStr, resp.Status, body)
		}
		return &serviceutil.APIError{
			Status:  resp.StatusCode,
			Message: string(body),
		}
	}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return err
	}
	return nil
}
