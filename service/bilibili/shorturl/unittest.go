// +build !release

package shorturl

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	createAddData interface{}
	responseCode  int64
	once          sync.Once

	testConfig Config
)

func startMockShortURLServer() {
	r := gin.New()
	r.POST(APIShortURLAdd, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    responseCode,
			"message": "0",
			"ttl":     1,
			"data":    createAddData,
		})
	})

	addr := tutil.RunMockServer(r, 0)
	testConfig = Config{
		URL:       fmt.Sprintf("http://%s", addr),
		Host:      "uat-maoer.biliapi.net",
		AppKey:    "test_key",
		AppSecret: "test_secret",
	}
}

// TestConfig 测试配置
func TestConfig() Config {
	once.Do(startMockShortURLServer)
	return testConfig
}

// SetMockResult 设置 mock 后的结果
func SetMockResult(uri string, code int64, data interface{}) {
	switch uri {
	case APIShortURLAdd:
		responseCode = code
		createAddData = data
	}
}
