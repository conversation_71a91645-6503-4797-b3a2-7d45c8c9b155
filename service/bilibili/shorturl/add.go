package shorturl

import (
	"encoding/json"
	"net/http"

	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// APIShortURLAdd 添加短链 API
const APIShortURLAdd = "/x/service/api/shorturl/add"

// BusinessMaoer 常量
const BusinessMaoer = "maoer"

// response 添加短链接口返回响应
type response struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	TTL     int64           `json:"ttl"`
	Data    json.RawMessage `json:"data"`
}

// AddResult 添加短链返回结果
type AddResult struct {
	ShortURL string `json:"short_url"`
}

// Add 添加短链接口
func (c *Client) Add(originURL string) (*AddResult, error) {
	var r response
	err := c.Call(APIShortURLAdd, RequestParams{Business: BusinessMaoer, OriginURL: originURL}, &r)
	if err != nil {
		return nil, err
	}
	if r.Code != 0 {
		return nil, &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: r.Message,
		}
	}
	addResult := new(AddResult)
	err = json.Unmarshal(r.Data, addResult)
	if err != nil {
		return nil, err
	}
	return addResult, nil
}
