package shorturl

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var (
	testClient *Client
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	testConfig = TestConfig()
	var err error
	testClient, err = NewClient(testConfig)
	if err != nil {
		logger.Fatal(err)
	}
	m.Run()
}

func TestClient_Add(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(APIShortURLAdd, 0, map[string]interface{}{
		"short_url": "https://uat.b23.tv/xpiPwLL",
	})

	c, _ := NewClient(TestConfig())
	originURL := "https://www.bilibili.com"
	resp, err := c.Add(originURL)
	require.NoError(err)
	assert.Equal("https://uat.b23.tv/xpiPwLL", resp.ShortURL)
}
