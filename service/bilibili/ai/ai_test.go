package ai

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "url", "api_key", "app_key", "app_secret")

	kc = tutil.<PERSON><PERSON>eyChecker(t, tutil.JSON)
	kc.Check(aiGatewayResp{}, "code", "data", "message")
}

func TestNewRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	req, err := c.newRequest(http.MethodGet, "/test?type=1", nil)
	require.NoError(err)
	assert.NotEmpty(req.Host)
}

func TestClientRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	httpReq, err := c.newRequest(http.MethodGet, "/test?type=1", nil)
	require.NoError(err)
	_ = c.request(httpReq, false, false)
	assert.True(strings.Contains(httpReq.URL.String(), "sign="))
	assert.True(strings.Contains(httpReq.URL.String(), "appkey="))
	assert.NotEmpty(httpReq.Header.Get("apikey"))
}
