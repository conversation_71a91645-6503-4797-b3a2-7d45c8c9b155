package ai

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// Config ai-gateway config
type Config struct {
	URL       string `yaml:"url"`
	APIKey    string `yaml:"api_key"`
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
}

// SignParams sign params
func (c Config) SignParams() (appKey, appSecret string) {
	return c.AppKey, c.AppSecret
}

// Client ai-gateway api client
type Client struct {
	Config
	c *http.Client
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: &http.Client{
			Timeout: 5 * time.Second,
		},
	}
	return c
}

type aiGatewayResp struct {
	Code    int64           `json:"code"`
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

func (c *Client) newRequest(method, path string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, c.URL+path, body)
	if err != nil {
		return nil, err
	}
	req.URL.RawQuery = blademaster.Sign(c, req.URL.Query())
	req.Header.Set("apikey", c.APIKey)
	req.Header.Set("User-Agent", serviceutil.UserAgent)
	return req, nil
}

// Request ai gateway request
func (c *Client) request(req *http.Request, response interface{}, raw bool) error {
	logger.Debugf("%s %s", req.Method, req.URL.String())
	resp, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode >= http.StatusBadRequest {
			logger.Warnf("%s %s\nHTTP %s\n%s", req.Method, req.URL.String(), resp.Status, body)
		}
		return &serviceutil.APIError{
			Status:  resp.StatusCode,
			Message: string(body),
		}
	}

	if raw {
		// 返回原始数据
		err = json.Unmarshal(body, response)
		if err != nil {
			return &serviceutil.APIError{
				Status:  http.StatusBadRequest,
				Message: fmt.Sprintf("ai-gateway: raw data unmarshal failed: %v", err)}
		}
		return nil
	}

	var aiResp aiGatewayResp
	err = json.Unmarshal(body, &aiResp)
	if err != nil {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("ai-gateway: data unmarshal failed: %v", err)}
	}
	if aiResp.Code != 0 {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("ai-gateway: (%d) - %s", aiResp.Code, aiResp.Message)}
	}
	err = json.Unmarshal(aiResp.Data, response)
	if err != nil {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("ai-gateway: (%d) - %v", aiResp.Code, err)}
	}
	return nil
}
