// +build !release

package ai

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	createTaskData interface{}
	taskResultData interface{}
	responseCode   int64
	once           sync.Once

	testConfig Config
)

func startMockASRServer() {
	r := gin.New()
	r.POST(APIASRCreateTask, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    responseCode,
			"data":    createTaskData,
			"message": "",
		})
	})
	r.GET(APIASRTaskResult, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    responseCode,
			"data":    taskResultData,
			"message": "",
		})
	})

	addr := tutil.RunMockServer(r, 0)
	testConfig = Config{
		URL:       fmt.Sprintf("http://%s", addr),
		APIKey:    "test_key",
		AppKey:    "test_key",
		AppSecret: "test_secret",
	}
}

// TestConfig 测试配置
func TestConfig() Config {
	once.Do(startMockASRServer)
	return testConfig
}

// SetMockResult 设置 mock 后的结果
func SetMockResult(uri string, code int64, data interface{}) {
	switch uri {
	case APIASRCreateTask:
		responseCode = code
		createTaskData = data
	case APIASRTaskResult:
		responseCode = code
		taskResultData = data
	}
}
