package ai

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestASRTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(ASRTaskCreateRequest{}, "resource")
	kc.Check(ASRTaskCreateResp{}, "task_id")
	kc.Check(ASRTaskResultInfo{}, "task_id", "boss_url", "remark", "state")
}

func TestCreateASRTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(APIASRCreateTask, 0, map[string]any{
		"task_id": "12345",
	})
	c := NewClient(TestConfig())
	resp, err := c.CreateASRTask(&ASRTaskCreateRequest{
		Resource: "http://upos.test.co/mefmxcodeboss/aod/9c9031500.m4a",
	})
	require.NoError(err)
	assert.Equal("12345", resp.TaskID)
}

func TestASRTaskResult(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(APIASRTaskResult, 0, map[string]any{
		"task_id":  "12345",
		"boss_url": "http://boss.test.co/result.json",
		"remark":   "test remark",
		"state":    ASRTaskResultStateSuccess,
	})
	c := NewClient(TestConfig())
	resp, err := c.ASRTaskResult("12345")
	require.NoError(err)
	assert.Equal("12345", resp.TaskID)
	assert.Equal("http://boss.test.co/result.json", resp.BossURL)
	assert.Equal("test remark", resp.Remark)
	assert.Equal(ASRTaskResultStateSuccess, resp.State)

	// Test error case
	SetMockResult(APIASRTaskResult, 0, map[string]any{
		"task_id":  "54321",
		"boss_url": "",
		"remark":   "failed",
		"state":    ASRTaskResultStateFailed,
	})
	resp, err = c.ASRTaskResult("54321")
	require.NoError(err)
	assert.Equal("54321", resp.TaskID)
	assert.Empty(resp.BossURL)
	assert.Equal("failed", resp.Remark)
	assert.Equal(ASRTaskResultStateFailed, resp.State)
}

func TestASRResultUtterances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock a successful response
	mockResult := map[string]any{
		"utterances": []map[string]any{
			{
				"text":       "测试文本1",
				"start_time": 0,
				"end_time":   1000,
			},
			{
				"text":       "测试文本2",
				"start_time": 1000,
				"end_time":   2000,
			},
		},
	}

	// Create a test server to mock the boss URL response
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		if err := json.NewEncoder(w).Encode(mockResult); err != nil {
			t.Fatal(err)
		}
	}))
	defer ts.Close()

	c := NewClient(TestConfig())
	result := &ASRTaskResultInfo{
		TaskID:  "12345",
		BossURL: ts.URL,
		State:   ASRTaskResultStateSuccess,
	}

	var response struct {
		Utterances []struct {
			Text      string `json:"text"`
			StartTime int    `json:"start_time"`
			EndTime   int    `json:"end_time"`
		} `json:"utterances"`
	}

	err := c.ASRResultUtterances(result, &response)
	require.NoError(err)
	assert.Len(response.Utterances, 2)
	assert.Equal("测试文本1", response.Utterances[0].Text)
	assert.Equal(0, response.Utterances[0].StartTime)
	assert.Equal(1000, response.Utterances[0].EndTime)
	assert.Equal("测试文本2", response.Utterances[1].Text)
	assert.Equal(1000, response.Utterances[1].StartTime)
	assert.Equal(2000, response.Utterances[1].EndTime)
}
