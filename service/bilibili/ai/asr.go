package ai

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/url"

	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// 长语音识别任务状态
const (
	ASRTaskResultStateDefault    = iota // 已提交
	ASRTaskResultStateProcessing        // 处理中
	ASRTaskResultStateNoResult          // 无结果
	ASRTaskResultStateFailed            // 错误
	ASRTaskResultStateSuccess           // 成功
)

// uri
const (
	APIASRCreateTask = "/v3/asr/task"
	APIASRTaskResult = "/v3/asr/task/result"
)

// ASRTaskCreateRequest create asr task param
// 接口文档: https://cloud.bilibili.co/akali/appsManage?appId=ai.model.apisix&level=2&itemId=317304&appVersion=undefined#sh/sh001/prod
type ASRTaskCreateRequest struct {
	Resource string `json:"resource"`
}

// ASRTaskCreateResp create asr task resp
type ASRTaskCreateResp struct {
	TaskID string `json:"task_id"`
}

// CreateASRTask create asr task request
func (c *Client) CreateASRTask(taskParam *ASRTaskCreateRequest) (*ASRTaskCreateResp, error) {
	data, err := json.Marshal(taskParam)
	if err != nil {
		return nil, err
	}
	req, err := c.newRequest(http.MethodPost, APIASRCreateTask, bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp := new(ASRTaskCreateResp)
	err = c.request(req, resp, false)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ASRTaskResultInfo asr task result info
// 接口文档: https://cloud.bilibili.co/akali/appsManage?appId=ai.model.apisix&level=2&itemId=317305&appVersion=undefined#sh/sh001/prod
type ASRTaskResultInfo struct {
	TaskID  string `json:"task_id"`
	BossURL string `json:"boss_url"`
	Remark  string `json:"remark"`
	State   int    `json:"state"`
}

// ASRTaskResult get asr task result
func (c *Client) ASRTaskResult(taskID string) (*ASRTaskResultInfo, error) {
	params := url.Values{}
	params.Set("task_id", taskID)
	path := APIASRTaskResult + "?" + params.Encode()
	req, err := c.newRequest(http.MethodGet, path, nil)
	if err != nil {
		return nil, err
	}
	response := new(ASRTaskResultInfo)
	err = c.request(req, response, false)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// ASRResultUtterances get asr task result utterances
func (c *Client) ASRResultUtterances(result *ASRTaskResultInfo, v any) error {
	req, err := http.NewRequest(http.MethodGet, result.BossURL, nil)
	if err != nil {
		return err
	}
	req.Header.Set("User-Agent", serviceutil.UserAgent)
	return c.request(req, v, true)
}
