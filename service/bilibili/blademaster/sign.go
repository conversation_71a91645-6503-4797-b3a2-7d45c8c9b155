package blademaster

import (
	"net/url"
	"sort"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/util"
)

// Signer 签字人
type Signer interface {
	// SignParams 获取 sign 参数
	SignParams() (appKey, appSecret string)
}

// encodeForm encodes the values into ``URL encoded'' form  and exclude sign param
// ("bar=baz&foo=quux") sorted by key.
func encodeForm(form url.Values) string {
	if form == nil {
		return ""
	}
	var buf strings.Builder
	keys := make([]string, 0, len(form))
	for k := range form {
		if k == "sign" {
			continue
		}
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		vs := form[k]
		keyEscaped := url.QueryEscape(k)
		for _, v := range vs {
			if buf.Len() > 0 {
				buf.WriteByte('&')
			}
			buf.WriteString(keyEscaped)
			buf.WriteByte('=')
			buf.WriteString(url.QueryEscape(v))
		}
	}
	return strings.ReplaceAll(buf.String(), "+", "%20")
}

// Sign 签名
func Sign(p Signer, form url.Values) string {
	appKey, appSecret := p.SignParams()
	form.Set("ts", strconv.FormatInt(util.TimeNow().Unix(), 10))
	form.Set("appkey", appKey)

	strToSign := encodeForm(form)
	sign := util.MD5(strToSign + appSecret)
	return strToSign + "&sign=" + sign
}

// IsValidSign 校验是否为合法签名
func IsValidSign(p Signer, form url.Values) bool {
	appKey, appSecret := p.SignParams()
	if form.Get("sign") == "" || form.Get("appkey") != appKey {
		return false
	}
	if form.Get("ts") == "" {
		// TODO: check time
		return false
	}
	sign := form.Get("sign")
	strToSign := encodeForm(form)
	hsign := util.MD5(strToSign + appSecret)
	return sign == hsign
}
