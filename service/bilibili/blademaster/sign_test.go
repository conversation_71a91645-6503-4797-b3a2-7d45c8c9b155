package blademaster

import (
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

type smartSigner struct {
	AppKey    string
	AppSecret string
}

func (s smartSigner) SignParams() (string, string) {
	return s.AppKey, s.AppSecret
}

func TestEncodeForm(t *testing.T) {
	assert := assert.New(t)

	q := url.Values{}
	q.Add("ts", "1234567890")
	q.Add("appkey", "test+key")

	res := encodeForm(q)
	assert.Equal("appkey=test%2Bkey&ts=1234567890", res)

	q.Add("sign", "123456789")
	assert.Equal(res, encodeForm(q))
}

func TestSign(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.MapString)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1234567890, 0)
	})
	defer util.SetTimeNow(nil)

	s := smartSigner{AppKey: "appkey", AppSecret: "testkey"}

	form := make(url.Values)
	assert.Equal("appkey=appkey&ts=1234567890&sign=59a3d0b97d67fe2843041379ca7a817f", Sign(s, form))
	kc.Check(form, "ts", "appkey")
	assert.Equal(form.Get("appkey"), s.AppKey)
}

func TestIsValidSign(t *testing.T) {
	assert := assert.New(t)

	s := smartSigner{AppKey: "appkey", AppSecret: "testkey"}

	q := url.Values{}
	q.Add("ts", "1234567890")
	q.Add("appkey", s.AppKey)
	assert.False(IsValidSign(s, q))

	q.Set("sign", "59a3d0b97d67fe2843041379ca7a817f")
	assert.True(IsValidSign(s, q))

	q.Set("sign", "xxxxx")
	assert.False(IsValidSign(s, q))
}
