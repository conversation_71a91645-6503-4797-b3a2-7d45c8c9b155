package govern

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestClientReportResult(t *testing.T) {
	require := require.New(t)

	// 用 model 下的 govern 会有循环引用问题
	paramStr := `{"business":"maoer","sub_type":"reply","scene":"maoerAddReply","results":{"4000505":"pass"},"final":false}`
	var param RequestParams
	err := json.Unmarshal([]byte(paramStr), &param)
	require.NoError(err)
	err = testClient.ReportResult(param)
	require.NoError(err)
}
