package govern

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var (
	testConfig Config
	testClient *Client
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	testConfig = TestConfig()
	var err error
	testClient, err = NewClient(testConfig)
	if err != nil {
		logger.Fatal(err)
	}
	m.Run()
}

func TestClientContent(t *testing.T) {
	require := require.New(t)

	// 用 model 下的 govern 会有循环引用问题
	paramStr := `{
    "business": "maoer",
    "sub_type": "dm",
    "scene": "maoerAddDM",
    "contents": {
        "12": {
            "content": "abcd",
            "metadata": {
                "mid": 99,
                "username": "姓名",
                "caller": "maoer.service.api",
                "api": "/rpc/message/add-dm",
                "timestamp": 1615887183,
                "up_mid": 100,
                "platform": 1,
                "mobi_app": "android_missevan",
                "origin": "aaa",
                "ip": "127.0.0.1",
                "user_agent": "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
                "build": "5.4.5"
            },
            "extra": {
                "subject": "{\"id\":123,\"type\":1,\"title\":\"标题\",\"url\":\"https://www.missevan.com/sound/123\"}",
                "drama_id": "30698",
                "dm_id": "12",
                "dm_stime": "10.13"
            }
        }
    }
  }`
	var param RequestParams
	err := json.Unmarshal([]byte(paramStr), &param)
	require.NoError(err)
	content, err := testClient.Content(param)
	require.NoError(err)
	require.Len(content, 1)
	require.NotNil(content["12"])
}
