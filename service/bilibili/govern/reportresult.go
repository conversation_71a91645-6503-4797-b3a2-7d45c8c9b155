package govern

import (
	"net/http"

	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// APIGovernReportResult 社区治理上报结果 API
const APIGovernReportResult = "/x/service/api/govern/reportresult"

// ReportResult 社区治理结果上报接口
// 接入文档: https://info.bilibili.co/pages/viewpage.action?pageId=199113379
func (c *Client) ReportResult(params RequestParams) error {
	var r response
	err := c.Call(APIGovernReportResult, params, &r)
	if err != nil {
		return err
	}
	if r.Code != 0 {
		return &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: r.Message,
		}
	}
	return nil
}
