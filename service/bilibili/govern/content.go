package govern

import (
	"net/http"

	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// APIGovernContent 社区治理上报 API
const APIGovernContent = "/x/service/api/govern/content"

// response 上报接口返回响应
type response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TTL     int64  `json:"ttl"`
	Data    struct {
		Results map[string]ContentResult `json:"results"`
	} `json:"data"`
}

// ContentResult 社区治理上报返回结果
type ContentResult struct {
	Extra          string  `json:"extra"`
	UniqueID       int64   `json:"unique_id"`
	PositiveWeight float64 `json:"positive_weight"`
}

// Content 社区治理上报接口
// 接入文档: https://info.bilibili.co/pages/viewpage.action?pageId=199113379
func (c *Client) Content(params RequestParams) (map[string]ContentResult, error) {
	var r response
	err := c.Call(APIGovernContent, params, &r)
	if err != nil {
		return nil, err
	}
	if r.Code != 0 {
		return nil, &serviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: r.Message,
		}
	}
	if len(r.Data.Results) == 0 {
		return map[string]ContentResult{}, nil
	}
	return r.Data.Results, nil
}
