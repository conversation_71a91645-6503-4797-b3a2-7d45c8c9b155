# missevan-go 服务

## captcha 人机校验

服务端通过请求[阿里云人机校验服务](https://help.aliyun.com/document_detail/66340.html)，对用户登录、获取短信验证码等操作进行人机识别。

接入人机校验服务配置:

```yaml
service:
    ...
    captcha:
      enabled: false
      access_key_id: 'test_key'
      access_key_secret: 'test_secret'
      app_key: 'xxxxx'
      region_id: 'cn-hangzhou'
      endpoint: 'http://afs.aliyuncs.com'
      slide_url: 'http://static.missevan.com/standalone/uat/403/slide.html'
```

- [服务配置项相关代码](https://github.com/MiaoSiLa/missevan-go/blob/master/service/captcha/captcha.go)
