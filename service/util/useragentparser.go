package util

import (
	"errors"

	"github.com/ua-parser/uap-go/uaparser"
)

// UserAgentParserConfig UserAgent Parse Config
type UserAgentParserConfig struct {
	RegexesFile string `yaml:"regexes_file"` // user_agent_parser 配置文件地址
}

// NewUserAgentParser returns a new user agent parser
func NewUserAgentParser(conf *UserAgentParserConfig) (*uaparser.Parser, error) {
	if conf.RegexesFile == "" {
		return nil, errors.New("UserAgent parser regexes file config is empty")
	}
	return uaparser.New(conf.RegexesFile)
}
