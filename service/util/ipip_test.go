package util

import (
	"path"
	"runtime"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestIPIP(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var (
		ipip *IPIPReader
		err  error
	)
	t.Run("NewIPDBReader", func(t *testing.T) {
		_, file, _, _ := runtime.Caller(0)
		conf := &IPIPConfig{
			DBIPv4: path.Join(path.Dir(file), "../../testdata/", "ipip", "city.free.ipdb"),
			DBIPv6: path.Join(path.Dir(file), "../../testdata/", "ipip", "city.free.ipdb"),
		}
		_, err = NewIPIPReader(conf)
		require.EqualError(err, "language not support")

		conf.Language = IPIPLanguageCN
		_, err = NewIPIPReader(conf)
		require.EqualError(err, "IPv6 not support")

		conf.DBIPv6 = ""
		ipip, err = NewIPIPReader(conf)
		require.NoError(err)
	})

	t.Run("isValidLanguage", func(t *testing.T) {
		configLanguage := ipip.language
		ipip.language = "test"
		isValid := ipip.isValidLanguage("test")
		assert.True(isValid)

		ipip.language = configLanguage
		isValid = ipip.isValidLanguage("test")
		assert.False(isValid)

		isValid = ipip.isValidLanguage(IPIPLanguageCN)
		assert.True(isValid)
	})

	var ipv4record *IPIPRecord
	t.Run("Get", func(t *testing.T) {
		_, err = ipip.Get("27.190.250164", "")
		assert.EqualError(err, "invalid IP address")

		ipv4record, err = ipip.Get("**************", "")
		assert.NoError(err)

		ipv4record, err = ipip.Get("**************", IPIPLanguageCN)
		assert.NoError(err)
	})

	t.Run("RecordInfo", func(t *testing.T) {
		assert.Equal("唐山", ipv4record.CityName())
		assert.Equal("河北", ipv4record.RegionName())
		assert.Equal("中国", ipv4record.CountryName())
		// 测试用的 testdata/ipip/city.free.ipdb 免费版没有对应的结果
		assert.Equal("", ipv4record.CountryIsoCode())
		// 测试用的 testdata/ipip/city.free.ipdb 免费版没有对应的结果
		assert.Equal("", ipv4record.ISPName())
	})
}
