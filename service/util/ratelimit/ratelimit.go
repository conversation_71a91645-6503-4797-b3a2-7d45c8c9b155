package ratelimit

import (
	"io"
	"sync"
	"time"
)

// BaseLimiter interface
type BaseLimiter interface {
	// Take takes token from the bucket without blocking
	Take(n int64) time.Duration
}

// RateLimiter interface
type RateLimiter interface {
	BaseLimiter
	// Return returns pre allocate token with corresponding cost time
	Return(n int64, d time.Duration)
}

// LimitReaderFactory interface
type LimitReaderFactory interface {
	NewReader(r io.Reader) io.Reader
}

type reader struct {
	r io.Reader

	limiter RateLimiter
}

type readerFactory struct {
	limiter RateLimiter
}

// NewLimitReaderFactory .
func NewLimitReaderFactory(l RateLimiter) LimitReaderFactory {
	return &readerFactory{
		limiter: l,
	}
}

func (f *readerFactory) NewReader(r io.Reader) io.Reader {
	return NewReader(r, f.limiter)
}

// NewReader returns a reader that is rate limited by
// the given rate limiter.
func NewReader(r io.Reader, limiter RateLimiter) io.Reader {
	return &reader{
		r:       r,
		limiter: limiter,
	}
}

func (r *reader) Read(buf []byte) (int, error) {
	d := r.limiter.Take(int64(len(buf)))
	time.Sleep(d)

	n, err := r.r.Read(buf)
	if n <= 0 {
		r.limiter.Return(int64(len(buf)), d)
		return n, err
	}
	if n < len(buf) {
		ret := int64(len(buf) - n)
		d = time.Duration(float64(ret) / float64(len(buf)) * float64(d))
		r.limiter.Return(ret, d)
	}

	return n, err
}

// ScaledLimiter .
type ScaledLimiter struct {
	bucket BaseLimiter

	scale      int64
	avaliable  int64
	remainTime time.Duration

	mu sync.Mutex
}

// Take takes count bytes from the bucket without blocking. It returns
// the time that the caller should wait until the tokens are actually
// available.
func (l *ScaledLimiter) Take(n int64) time.Duration {
	l.mu.Lock()
	cost := n
	remainSize := l.avaliable - cost
	for remainSize < 0 {
		l.mu.Unlock()
		// TODO: calc take tokens num
		d := l.bucket.Take(1)
		l.mu.Lock()
		l.avaliable += 1 * l.scale
		l.remainTime += d
		remainSize = l.avaliable - cost
	}
	costTime := time.Duration(float64(cost) / float64(l.avaliable) * float64(l.remainTime))
	l.avaliable -= cost
	l.remainTime -= costTime
	l.mu.Unlock()
	return costTime
}

// Return returns pre allocate bytes with corresponding cost time.
func (l *ScaledLimiter) Return(n int64, costTime time.Duration) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.avaliable += int64(n)
	l.remainTime += costTime
}

// NewScaledLimiter returns a new rate limiter rate limited
// by token bucket.
func NewScaledLimiter(bucket BaseLimiter, scale int64) RateLimiter {
	return &ScaledLimiter{bucket: bucket, scale: scale}
}

// NewIOLimiter returns a new io limiter rate limited by token bucket.
// Each token in the bucket represents 1 Mbps.
func NewIOLimiter(bucket BaseLimiter) RateLimiter {
	return NewScaledLimiter(bucket, 1024*1024/8)
}
