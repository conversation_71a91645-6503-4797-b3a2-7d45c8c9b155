package util

import (
	"fmt"
	"net"
	"strings"
	"sync"

	"github.com/MiaoSiLa/missevan-go/logger"
)

var (
	appName    = "missevan-go"
	appVersion = "0.0.1-dev"
	// UserAgent for http request
	UserAgent = appName + "/" + appVersion
)

// APIError as an generic error wrapper for API error
type APIError struct {
	Status  int
	Message string
}

// Error return error message
func (e *APIError) Error() string {
	return e.Message
}

// SetAppName sets current app name
func SetAppName(name string) {
	appName = name
	updateUserAgent()
}

// SetVersion sets current version
func SetVersion(version string) {
	appVersion = version
	updateUserAgent()
}

func updateUserAgent() {
	UserAgent = fmt.Sprintf("%s/%s", appName, appVersion)
}

// ParseUserAgent 解析 rpc 请求头中 User-Agent
func ParseUserAgent(ua string) (appName, appVersion string) {
	app := strings.Split(strings.Split(ua, " ")[0], "/")
	if len(app) != 2 {
		return "", ""
	}
	return app[0], app[1]
}

var privateIPNets []*net.IPNet
var privateIPNetsOnce sync.Once

func initPrivateIPNets() {
	if privateIPNets != nil {
		return
	}
	cidrs := []string{
		"10.0.0.0/8",
		"**********/10",
		"*********/8",
		"**********/12",
		"***********/16",
		"fc00::/7",
		"fe80::/10",
		"***********/16",
	}
	privateIPNets = make([]*net.IPNet, 0, len(cidrs))
	for _, cidr := range cidrs {
		_, privateIPNet, err := net.ParseCIDR(cidr)
		if err != nil {
			logger.Errorf("parse %s failed: %v", cidr, err)
			// PASS: 出错时忽略这条配置，继续解析下一条
			continue
		}
		privateIPNets = append(privateIPNets, privateIPNet)
	}
}

// IsPrivateIP 判断 ip 是否为私有 ip
func IsPrivateIP(ip net.IP) bool {
	privateIPNetsOnce.Do(initPrivateIPNets)
	for _, network := range privateIPNets {
		if network.Contains(ip) {
			return true
		}
	}
	return false
}
