package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPKCS7Unpadding(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	data := []byte{0x12, 0x5, 0x5, 0x5, 0x5, 0x5}
	origData, err := PKCS7Unpadding(data)
	require.NoError(err)
	assert.Equal([]byte{0x12}, origData)

	data = []byte{0x12, 0x5, 0x4, 0x5, 0x5, 0x5}
	_, err = PKCS7Unpadding(data)
	require.Error(err)

	data = []byte{0x12, 0x8, 0x8, 0x8, 0x8, 0x8}
	_, err = PKCS7Unpadding(data)
	require.Error(err)
}
