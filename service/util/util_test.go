package util

import (
	"net"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSetVersion(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	SetAppName("myapp")
	SetVersion("2.0.0")
	assert.Equal(UserAgent, "myapp/2.0.0")

	SetAppName("missevan-go")
	assert.Equal(UserAgent, "missevan-go/2.0.0")

	SetVersion("1.0.0")
	assert.Equal(UserAgent, "missevan-go/1.0.0")
}

func TestParseUserAgent(t *testing.T) {
	assert := assert.New(t)

	name, version := ParseUserAgent("")
	assert.Equal("", name)
	assert.Equal("", version)
	name, version = ParseUserAgent("live-service/2.0.0 XXX")
	assert.Equal("live-service", name)
	assert.Equal("2.0.0", version)
	name, version = ParseUserAgent("live-service/1.5.0-8")
	assert.Equal("live-service", name)
	assert.Equal("1.5.0-8", version)
}

func TestIsPrivateIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tests := []struct {
		ip     string
		expect bool
	}{
		{ip: "0.0.0.0", expect: false},
		{ip: "::", expect: false},

		{ip: "********", expect: true},
		{ip: "********", expect: false},
		{ip: "fc00::1", expect: true},
		{ip: "ff00::1", expect: false},
	}

	for _, v := range tests {
		ip := net.ParseIP(v.ip)
		require.NotNil(ip)
		assert.Equal(v.expect, IsPrivateIP(ip))
	}
}
