package util

import (
	"encoding/binary"
	"errors"
	"net"

	geoip2 "github.com/oschwald/geoip2-golang"
)

// Global variables
var (
	ErrInvalidIP = errors.New("invalid IP address")
)

// GeoIPConfig configs geoip database
type GeoIPConfig struct {
	City string `yaml:"city_file"`
}

// GeoIPReader wraps a geoip2.Reader
type GeoIPReader struct {
	db *geoip2.Reader
}

// Record wraps a geoip2.City
type Record struct {
	Record *geoip2.City
}

// CityName returns the city name of the ip record
func (c *Record) CityName() string {
	return c.Record.City.Names["en"]
}

// RegionName returns the region name of the ip record
func (c *Record) RegionName() string {
	for _, v := range c.Record.Subdivisions {
		return v.Names["en"]
	}
	return ""
}

// RegionIsoCode returns the region code of the ip record
func (c *Record) RegionIsoCode() string {
	for _, v := range c.Record.Subdivisions {
		return v.IsoCode
	}
	return ""
}

// CountryName returns the country name of the ip record
func (c *Record) CountryName() string {
	return c.Record.Country.Names["en"]
}

// CountryIsoCode returns the isocode of country of the ip record
func (c *Record) CountryIsoCode() string {
	return c.Record.Country.IsoCode
}

// LoadGeoIPConfig new GeoIPReader from config
func LoadGeoIPConfig(conf *GeoIPConfig) (*GeoIPReader, error) {
	return NewGeoIPReader(conf.City)
}

// NewGeoIPReader returns a new GeoIPReader
func NewGeoIPReader(mmdbFile string) (*GeoIPReader, error) {
	db, err := geoip2.Open(mmdbFile)
	if err != nil {
		return nil, err
	}
	return &GeoIPReader{db}, nil
}

// Close the GeoIPReader and returns the resources to the system.
func (r *GeoIPReader) Close() {
	r.db.Close()
}

// Get returns the geographical information of the specified ipAddr
func (r *GeoIPReader) Get(ipAddr string) (city *Record, err error) {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return nil, ErrInvalidIP
	}
	record, err := r.db.City(ip)
	if err != nil {
		return nil, err
	}
	return &Record{
		Record: record,
	}, nil
}

// IP2long ip2long() for IPv4
func IP2long(ipAddress string) uint32 {
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return 0
	}
	return binary.BigEndian.Uint32(ip.To4())
}

// Long2ip long2ip() for IPv4
func Long2ip(properAddress uint32) string {
	ipByte := make([]byte, 4)
	binary.BigEndian.PutUint32(ipByte, properAddress)
	ip := net.IP(ipByte)
	return ip.String()
}
