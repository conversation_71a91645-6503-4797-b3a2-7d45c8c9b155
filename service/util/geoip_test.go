package util_test

import (
	"os"
	"path"
	"runtime"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/util"
)

func TestGeoIP(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	conf := util.GeoIPConfig{City: "/usr/share/GeoIP/GeoLite2-City.mmdb"}
	if runtime.GOOS == "windows" || runtime.GOOS == "darwin" {
		conf.City = path.Join(os.Getenv("GOPATH"),
			"/src/github.com/MiaoSiLa/missevan-go/testdata/GeoLite2-City.mmdb")
	}
	r, err := util.LoadGeoIPConfig(&conf)
	require.NoError(err)
	defer r.Close()
	c, err := r.Get("**************")
	require.NoError(err)
	assert.Equal("Shanghai", c.<PERSON>())
	assert.Equal("Shanghai", c.<PERSON>())
	assert.Equal("China", c.<PERSON>())
	assert.Equal("SH", c.RegionIsoCode())
	assert.Equal("CN", c.CountryIsoCode())
}

func TestIP2Long(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(uint32(134744072), util.IP2long("*******"))
	assert.Equal("*******", util.Long2ip(134744072))
}
