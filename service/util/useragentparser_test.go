package util

import (
	"path"
	"runtime"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestUserAgentParser_Tag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(UserAgentParserConfig{}, "regexes_file")
}

func TestNewUserAgentParser(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试配置为空的情况
	conf := &UserAgentParserConfig{}
	_, err := NewUserAgentParser(conf)
	require.EqualError(err, "UserAgent parser regexes file config is empty")

	// 测试正常配置的情况
	_, file, _, _ := runtime.Caller(0)
	conf.RegexesFile = path.Join(path.Dir(file), "../../testdata/", "parseuseragent", "regexes.yaml")
	parser, err := NewUserAgentParser(conf)
	require.NoError(err)
	assert.NotNil(parser)
	// 断言可以正常解析 User-Agent 字符串
	userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
	ua := parser.Parse(userAgent)
	assert.Equal("Chrome", ua.UserAgent.Family)
	assert.Equal("137", ua.UserAgent.Major)
	assert.Equal("0", ua.UserAgent.Minor)
	assert.Equal("0", ua.UserAgent.Patch)
	assert.Equal("Mac OS X", ua.Os.Family)
	assert.Equal("10", ua.Os.Major)
	assert.Equal("15", ua.Os.Minor)
	assert.Equal("7", ua.Os.Patch)
	assert.Equal("Mac", ua.Device.Family)
	assert.Equal("Apple", ua.Device.Brand)
	assert.Equal("Mac", ua.Device.Model)
}
