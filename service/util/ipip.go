package util

import (
	"net"

	ipdb "github.com/ipipdotnet/ipdb-go"

	"github.com/MiaoSiLa/missevan-go/util"
)

// 支持的语言
const (
	IPIPLanguageCN = "CN"
	IPIPLanguageEN = "EN"
)

// CountryCodeChina 国家地区代码
const CountryCodeChina = "CN"

// 国家地区名称
const (
	CountryNameChina   = "中国"
	CountryNameChinaEN = "China"
)

// IPIPConfig configs ipip.net database
type IPIPConfig struct {
	DBIPv4   string `yaml:"db_ipv4"`
	DBIPv6   string `yaml:"db_ipv6"`
	Language string `yaml:"language"`
}

// IPIPReader wraps a ipdbgo.City
type IPIPReader struct {
	dbv4     *ipdb.City
	dbv6     *ipdb.City
	language string
}

// IPIPRecord wraps a ipdbgo.CityInfo
type IPIPRecord struct {
	Record *ipdb.CityInfo
}

// NewIPIPReader returns a new GeoIPReader
func NewIPIPReader(conf *IPIPConfig) (*IPIPReader, error) {
	dbv4, err := ipdb.NewCity(conf.DBIPv4)
	if err != nil {
		return nil, err
	}
	if !dbv4.IsIPv4() {
		return nil, ipdb.ErrNoSupportIPv4
	}
	if conf.Language == "" {
		conf.Language = IPIPLanguageEN
	}

	var isLanguageSupported bool
	for _, lang := range dbv4.Languages() {
		if lang == conf.Language {
			isLanguageSupported = true
			break
		}
	}
	if !isLanguageSupported {
		return nil, ipdb.ErrNoSupportLanguage
	}

	var dbv6 *ipdb.City
	if conf.DBIPv6 != "" {
		dbv6, err = ipdb.NewCity(conf.DBIPv6)
		if err != nil {
			return nil, err
		}
		if !dbv6.IsIPv6() {
			return nil, ipdb.ErrNoSupportIPv6
		}

		var isLanguageSupported bool
		for _, lang := range dbv6.Languages() {
			if lang == conf.Language {
				isLanguageSupported = true
				break
			}
		}
		if !isLanguageSupported {
			return nil, ipdb.ErrNoSupportLanguage
		}
	}

	return &IPIPReader{
		dbv4:     dbv4,
		dbv6:     dbv6,
		language: conf.Language,
	}, nil
}

func (r *IPIPReader) isValidLanguage(language string) bool {
	if language == r.language {
		return true
	}
	if !util.HasElem(r.dbv4.Languages(), language) {
		return false
	}
	if r.dbv6 != nil && !util.HasElem(r.dbv6.Languages(), language) {
		return false
	}
	return true
}

// Get returns the geographical information of the specified ipAddr
func (r *IPIPReader) Get(ipAddr, language string) (record *IPIPRecord, err error) {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return nil, ErrInvalidIP
	}

	// 如果 language 是无效语言则使用默认的语言（IPIPConfig.language）
	targetLanguage := r.language
	if language != "" && r.isValidLanguage(language) {
		targetLanguage = language
	}

	cityinfo := new(ipdb.CityInfo)
	if ip4 := ip.To4(); ip4 != nil {
		cityinfo, err = r.dbv4.FindInfo(ipAddr, targetLanguage)
		if err != nil {
			return nil, err
		}
	} else if r.dbv6 != nil {
		cityinfo, err = r.dbv6.FindInfo(ipAddr, targetLanguage)
		if err != nil {
			return nil, err
		}
	}

	return &IPIPRecord{
		Record: cityinfo,
	}, nil
}

// CityName returns the city name of the ip record
func (r *IPIPRecord) CityName() string {
	return r.Record.CityName
}

// RegionName returns the region name of the ip record
func (r *IPIPRecord) RegionName() string {
	return r.Record.RegionName
}

// CountryName returns the country name of the ip record
func (r *IPIPRecord) CountryName() string {
	return r.Record.CountryName
}

// CountryIsoCode returns the isocode of country of the ip record
func (r *IPIPRecord) CountryIsoCode() string {
	return r.Record.CountryCode
}

// ISPName returns the isp name of the ip record
func (r *IPIPRecord) ISPName() string {
	return r.Record.IspDomain
}
