package util

import (
	"errors"
)

// errors
var (
	ErrInvalidData = errors.New("invalid data")
)

// PKCS7Unpadding implements pkcs#7 unpadding algorithm safely
func PKCS7Unpadding(plantText []byte) ([]byte, error) {
	length := len(plantText)
	padchr := plantText[length-1]
	unpadding := int(padchr)

	if unpadding <= 0 || unpadding > length {
		return nil, ErrInvalidData
	}
	for i := length - unpadding; i < length; i++ {
		if plantText[i] != padchr {
			return nil, ErrInvalidData
		}
	}

	return plantText[:(length - unpadding)], nil
}
