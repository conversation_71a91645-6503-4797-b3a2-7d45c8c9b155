package retryhttp

import (
	"bytes"
	"errors"
	"io"
	"math"
	"net/http"
	"syscall"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
)

type eofRetryTransport struct {
	RetryCount int

	transport http.RoundTripper
}

func backoff(retries int) time.Duration {
	return time.Duration(math.Pow(2, float64(retries))) * time.Second
}

func drainBody(resp *http.Response) {
	if resp != nil && resp.Body != nil {
		_, _ = io.Copy(io.Discard, resp.Body)
		resp.Body.Close()
	}
}

func (t *eofRetryTransport) shouldRetry(err error) bool {
	return errors.Is(err, io.EOF) || errors.Is(err, syscall.ECONNRESET)
}

func (t *eofRetryTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// Clone the request body
	var bodyBytes []byte
	var err error
	if req.Body != nil {
		bodyBytes, err = io.ReadAll(req.Body)
		if err != nil {
			return nil, err
		}
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	// Send the request
	resp, err := t.transport.RoundTrip(req)

	// Retry logic
	retries := 0
	for t.shouldRetry(err) && retries < t.RetryCount {
		logger.WithField("url", req.URL).Warnf("request error: %v, do retry", err)

		// We're going to retry, consume any response to reuse the connection.
		drainBody(resp)

		// Wait for the specified backoff period
		time.Sleep(backoff(retries))

		// Clone the request body again
		if bodyBytes != nil {
			req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}

		// Retry the request
		resp, err = t.transport.RoundTrip(req)

		retries++
	}

	// Return the response
	return resp, err
}

// NewEOFRetryClient 初始化 io.EOF 自动重试 client
func NewEOFRetryClient(tr http.RoundTripper) *http.Client {
	transport := &eofRetryTransport{
		RetryCount: 1, // 重试一次

		transport: tr,
	}
	if tr == nil {
		transport.transport = http.DefaultTransport
	}

	return &http.Client{
		Transport: transport,
		Timeout:   5 * time.Second,
	}
}
