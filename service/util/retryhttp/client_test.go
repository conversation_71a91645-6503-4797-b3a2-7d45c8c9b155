package retryhttp

import (
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"strings"
	"syscall"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBackoff(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(time.Second, backoff(0))
	assert.Equal(2*time.Second, backoff(1))
	assert.Equal(8*time.Second, backoff(3))
}

func TestEofRetryTransport_shouldRetry(t *testing.T) {
	t.Run("EOF error", func(t *testing.T) {
		assert := assert.New(t)

		var retry eofRetryTransport
		assert.True(retry.shouldRetry(io.EOF))
		assert.False(retry.shouldRetry(fmt.Errorf("test error")))
	})

	t.Run("reset by peer error", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		var retry eofRetryTransport

		server := func() {
			listener, err := net.Listen("tcp", ":8080")
			require.NoError(err)
			defer listener.Close()

			conn, err := listener.Accept()
			require.NoError(err)

			conn.Close()
		}

		client := func() {
			conn, err := net.Dial("tcp", "localhost:8080")
			require.NoError(err)

			_, err = conn.Write([]byte("ab"))
			require.NoError(err)

			time.Sleep(1 * time.Second) // wait for the server-side closure

			data := make([]byte, 1)
			_, err = conn.Read(data)
			require.True(errors.Is(err, syscall.ECONNRESET))
			t.Logf("client: %v", err)
			assert.True(retry.shouldRetry(err))
		}

		go server()

		time.Sleep(3 * time.Second) // wait for the server to run

		client()
	})
}

func TestDrainBody(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.NotPanics(func() { drainBody(nil) })

	resp := new(http.Response)
	assert.NotPanics(func() { drainBody(resp) })

	resp.Body = io.NopCloser(strings.NewReader("test"))
	assert.NotPanics(func() { drainBody(resp) })
	bodyBytes, err := io.ReadAll(resp.Body)
	require.NoError(err)
	assert.Empty(bodyBytes)
}

func TestNewEOFRetryClient(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	svr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "ok")
	}))
	defer svr.Close()

	c := NewEOFRetryClient(nil)
	r, err := c.Get(svr.URL)
	require.NoError(err)
	require.NotNil(r.Body)
	body, err := io.ReadAll(r.Body)
	require.NoError(err)
	assert.Equal([]byte("ok"), body)
}
