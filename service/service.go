package service

import (
	"math/rand"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"
	"github.com/patrickmn/go-cache"

	"github.com/MiaoSiLa/missevan-go/service/antispam"
	"github.com/MiaoSiLa/missevan-go/service/antispamv2"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	servicegovern "github.com/MiaoSiLa/missevan-go/service/bilibili/govern"
	serviceshorturl "github.com/MiaoSiLa/missevan-go/service/bilibili/shorturl"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/lancer"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/service/util"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/ua-parser/uap-go/uaparser"
)

// Config is service config
type Config struct {
	DB        servicedb.Config `yaml:"db"`
	MessageDB servicedb.Config `yaml:"message_db"`
	LogDB     servicedb.Config `yaml:"log_db"`
	DramaDB   servicedb.Config `yaml:"drama_db"`
	VoiceDB   servicedb.Config `yaml:"voice_db"`
	MainDB    servicedb.Config `yaml:"main_db"`
	PayDB     servicedb.Config `yaml:"pay_db"`

	Redis    serviceredis.Config `yaml:"redis"`
	LRURedis serviceredis.Config `yaml:"lru_redis"`

	Databus map[string]databus.Config `yaml:"databus"`

	MRPC        mrpc.Config            `yaml:"mrpc"`
	PushService pushservice.Config     `yaml:"pushservice"`
	Gaia        servicegaia.Config     `yaml:"gaia"`
	Govern      servicegovern.Config   `yaml:"govern"`
	ShortURL    serviceshorturl.Config `yaml:"short_url"`

	Storage        storage.Config    `yaml:"storage"`
	Upload         upload.Config     `yaml:"upload"`
	AntiSpam       antispam.Config   `yaml:"antispam"`
	AntiSpamV2     antispamv2.Config `yaml:"antispamv2"`
	Geetest        geetest.Config    `yaml:"geetest"`
	GeetestLowRisk geetest.Config    `yaml:"geetest_low_risk"`
	OpenSearch     search.Config     `yaml:"opensearch"`

	IPIP            serviceutil.IPIPConfig            `yaml:"ipip"`
	UserAgentParser serviceutil.UserAgentParserConfig `yaml:"user_agent_parser"`

	Captcha *captcha.Config `yaml:"captcha"`

	Lancer *lancer.Config `yaml:"lancer"`

	Smartsheet smartsheet.Config `yaml:"smartsheet"`
}

// DatabusClient .
type DatabusClient struct {
	AppLogPub  *databus.Databus
	AppLogSub  *databus.Databus
	LiveLogPub *databus.Databus
}

// service vars
var (
	DefaultConfig *Config

	DB        *gorm.DB
	MessageDB *gorm.DB
	LogDB     *gorm.DB
	DramaDB   *gorm.DB
	VoiceDB   *gorm.DB
	MainDB    *gorm.DB
	PayDB     *gorm.DB

	Databus DatabusClient

	Redis    *redis.Client
	LRURedis *redis.Client

	MRPC        *mrpc.Client
	PushService *pushservice.Client
	SSO         *sso.Client
	Gaia        *servicegaia.Client
	Govern      *servicegovern.Client
	ShortURL    *serviceshorturl.Client

	Storage        *storage.Client
	Upload         *upload.Client
	AntiSpam       *antispam.Client
	AntiSpamV2     *antispamv2.Client
	Geetest        *geetest.Client
	GeetestLowRisk *geetest.Client
	OpenSearch     *search.Client

	GeoIP           *util.IPIPReader
	UserAgentParser *uaparser.Parser

	Captcha *captcha.Client

	Smartsheet *smartsheet.Client

	Cache5Min = cache.New(5*time.Minute, 10*time.Minute)
)

func init() {
	maxIdleConns := servicedb.DefaultMaxIdleConns()
	DefaultConfig = &Config{
		DB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		MessageDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		LogDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		DramaDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		VoiceDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		MainDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		PayDB: servicedb.Config{
			Host:         "127.0.0.1",
			Port:         3306,
			MaxIdleConns: maxIdleConns,
			MaxLifeTime:  "10s",
		},
		Redis: serviceredis.Config{
			Addr:     "127.0.0.1:6379",
			DB:       0,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		LRURedis: serviceredis.Config{
			Addr:     "127.0.0.1:6379",
			DB:       0,
			PoolSize: serviceredis.DefaultPoolSize(),
		},
		AntiSpam:   *antispam.BuildDefaultConfig(),
		AntiSpamV2: *antispamv2.BuildDefaultConfig(),
		IPIP: serviceutil.IPIPConfig{
			DBIPv4:   "/usr/share/GeoIP/stable_v4_flagship.ipdb",
			DBIPv6:   "/usr/share/GeoIP/stable_v6_flagship.ipdb",
			Language: util.IPIPLanguageEN,
		},
		OpenSearch: *search.BuildDefaultConfig(),
		Smartsheet: smartsheet.Config{
			URL: "http://127.0.0.1:9003",
		},
	}

	rand.Seed(goutil.TimeNow().UnixNano())
}
