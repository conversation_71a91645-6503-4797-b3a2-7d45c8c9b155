package cache

import (
	"fmt"
	"strings"
)

// Formatter formatter
type Formatter interface {
	Format(v ...interface{}) string
}

// KeyFormat key format
type KeyFormat string

// Format generates formatted key name
func (k KeyFormat) Format(a ...interface{}) string {
	return fmt.Sprintf(string(k), a...)
}

// DatabusFormatter databus formatter
type DatabusFormatter [2]string

// Format format
func (df DatabusFormatter) Format(params ...interface{}) string {
	return fmt.Sprintf(df[0], params...)
}

// MatchKey key 是否匹配
func (df DatabusFormatter) MatchKey(s string) bool {
	return strings.HasPrefix(s, df[1])
}
