package serviceredis

import (
	"errors"
	"runtime"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// formats - `Key${name}${argc}`
const (
	// Redis
	KeyBlackList0   cache.KeyFormat = "black_list"    // ZSET
	KeyBlackListIP0 cache.KeyFormat = "black_list_ip" // ZSET

	// KeyActivityUserDrawPointEvent1 存放活动中用户的抽奖积分
	KeyActivityUserDrawPointEvent1 cache.KeyFormat = "activity:user:draw_point:event:%d" // ZSET
	// KeyActivityDailyUserDrawPointEvent2  存放活动中每天用户的抽奖积分
	KeyActivityDailyUserDrawPointEvent2 cache.KeyFormat = "activity:user:draw_point:event:%d:%s" // ZSET

	// KeyFrequencyTimes3 用户评论稿件记录
	// user_id c_type element_id
	KeyFrequencyTimes3 cache.KeyFormat = "comment:frequency/%d/%d/%d" // ZSET

	KeyTextScanUserAdLimit1     cache.KeyFormat = "textscan:ad:user:%d"               // STRING
	KeyTextScanIPAdLimit1       cache.KeyFormat = "textscan:ad:ip:%s"                 // STRING
	KeyRequestSignUUID1         cache.KeyFormat = "request:sign:uuid:%s"              // STRING
	KeyOpenSearchConfig0        cache.KeyFormat = "opensearch_config"                 // HASH
	KeyForbiddenWords1          cache.KeyFormat = "forbidden_words:%d"                // SET
	KeyForbiddenCommentElement1 cache.KeyFormat = "forbidden_comment:element_type:%d" // SET
	KeyAppLogLaserUserID0       cache.KeyFormat = "app_log:laser:user_id"             // HASH
	KeyAppLogLaserBUVID0        cache.KeyFormat = "app_log:laser:buvid"               // HASH

	LockUserComment1       cache.KeyFormat = "lock:comment:user_id:%d"              // STRING
	LockUserDM1            cache.KeyFormat = "lock:dm:user_id:%d"                   // STRING
	LockUserPMNotify1      cache.KeyFormat = "lock:user:pm_notify:%d"               // STRING
	LockUserComment3       cache.KeyFormat = "lock:user_id:%d:comment_id:%d:sub:%d" // STRING
	LockScanIMExceedQuota0 cache.KeyFormat = "lock:scan:im:exceed_quota"            // STRING
	// LockUserLikeDanmaku2 点赞弹幕加锁
	LockUserLikeDanmaku2 cache.KeyFormat = "lock:user_id:%d:danmaku_id:%d" // STRING
	// LockUserDeleteDanmaku2 删除弹幕加锁
	// NOTICE: LockUserDeleteDanmaku2 与 LockUserDeleteDanmaku2 的锁名相同，不拆分
	LockUserDeleteDanmaku2 cache.KeyFormat = "lock:user_id:%d:danmaku_id:%d" // STRING
	// LockUserFollow1 关注和取消关注加锁
	LockUserFollow1 cache.KeyFormat = "lock:follow:user_id:%d" // STRING

	// LockOpenSearchKocKeywordTargetWord0 生成 koc 关键字对应的承接内容原名缓存加锁
	LockOpenSearchKocKeywordTargetWord0 cache.KeyFormat = "lock:opensearch:koc:keyword:target_word" // STRING

	KeyEventGameCode1       cache.KeyFormat = "event:%d:game_code"        // LIST
	KeyCarnivalPointUserID1 cache.KeyFormat = "carnival_point:user_id:%d" // HASH

	KeyMessageSendUserID1 cache.KeyFormat = "message:user:%d" // SET

	LockMessageSendIP2     cache.KeyFormat = "lock:msg:ip:%s:time:%02d"  // STRING
	LockMessageSendUserID2 cache.KeyFormat = "lock:msg:uid:%d:time:%02d" // STRING

	// LockVoteIPLimit2 投票 IP 的限制
	// params: IP 和年月日的日
	LockVoteIPLimit2 cache.KeyFormat = "lock:vote:ip:%s:time:%02d" // STRING
	// LockVoteBUVIDLimit2 投票设备的限制
	// params: buvid 和年月日的日
	LockVoteBUVIDLimit2 cache.KeyFormat = "lock:vote:buvid:%s:time:%02d" // STRING

	// KeyOpenSearchOfflineAppID1 开放搜索线下应用的 app_id
	// params: app_name 开放搜索应用名称
	KeyOpenSearchOfflineAppID1 cache.KeyFormat = "opensearch:offline:%s:app_id" // STRING

	// KeyOpenSearchKocKeywordTargetID0 koc 关键字对应的承接内容原名关联 ID
	// 格式：koc 关键字: 承接内容原名关联 ID
	KeyOpenSearchKocKeywordTargetID0 cache.KeyFormat = "opensearch:koc:keyword:target_id" // HASH
	// KeyOpenSearchSearchInterventionTypeKeywordMD5FixedParams2 搜索干预类型和关键词的固定参数配置
	// params: type 搜索类型，keyword 搜索干预关键词
	KeyOpenSearchSearchInterventionTypeKeywordMD5FixedParams2 cache.KeyFormat = "opensearch:search_intervention:type:%d:keyword_md5:%s:fixed_params" // STRING
	// KeyOpenSearchKeywordAllowList0 搜索关键词白名单
	KeyOpenSearchKeywordAllowList0 cache.KeyFormat = "opensearch:keyword:allow_list"

	// KeyCounterUserFollows2 用户关注计数
	// params: user_id; one_hour 或 one_day
	KeyCounterUserFollows2 cache.KeyFormat = "counter:user_id:%d:follow:%s" // STRING

	// KeyCommentDislikeProportion0 评论/子评论热度中扣除的点踩比重，文档：https://info.missevan.com/pages/viewpage.action?pageId=84730951
	KeyCommentDislikeProportion0 cache.KeyFormat = "comment:dislike:proportion" // STRING

	// KeyEquipPlay1 账号当前播放付费或会员音频的设备信息
	// params: user_id
	KeyEquipPlay1 cache.KeyFormat = "equip_play:user_id:%d" // ZSET
)

// 验证码相关
const (
	// KeyMobileVCode1 手机验证码
	KeyMobileVCode1 cache.KeyFormat = "mobile_%s" // HASH
	// LockMobileVCode1 发送手机验证码锁, key 暂和 missevan-web 项目保持一致
	LockMobileVCode1 cache.KeyFormat = "mobile_%s_lock" // STRING
	// KeyCounterVCodeIP1 发送验证码对 IP 的限制
	KeyCounterVCodeIP1 cache.KeyFormat = "validate:identifyCode:%s" // STRING
	// KeyCounterVCodeUID1 发送验证码对 UID 的限制
	KeyCounterVCodeUID1 cache.KeyFormat = "validate:confirm:%d" // STRING
)

// Config for redis config
type Config struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

// NewRedisClient return the redis client
func NewRedisClient(conf *Config) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     conf.Addr,
		Password: conf.Password,
		DB:       conf.DB,
		PoolSize: conf.PoolSize,
	})

	_, err := client.Ping().Result()
	if err != nil {
		return nil, err
	}

	return client, nil
}

// IsRedisNil 是否是 redis.Nil
func IsRedisNil(err error) bool {
	return errors.Is(err, redis.Nil) //nolint:forbidigo
}

// DefaultPoolSize 获取默认连接池大小
// 默认连接池大小是 CPU 每核对应 10 个连接
func DefaultPoolSize() int {
	// NOTICE: automaxprocs 会根据 CPU 硬限，将 GOMAXPROCS 设置成 max(1, floor(cpu_quota))
	return runtime.GOMAXPROCS(0) * 10
}
