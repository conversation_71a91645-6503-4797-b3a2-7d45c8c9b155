package serviceredis

import (
	"errors"
	"runtime"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGenerateKey(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	assert.Equal("textscan:ad:user:123", KeyTextScanUserAdLimit1.Format(int64(123)))
}

func TestIsRedisNil(t *testing.T) {
	assert := assert.New(t)
	assert.True(IsRedisNil(redis.Nil))
	assert.False(IsRedisNil(errors.New("test error")), "test error")
}

func TestDefaultPoolSize(t *testing.T) {
	assert := assert.New(t)

	runtime.GOMAXPROCS(8)
	assert.Equal(80, DefaultPoolSize())

	runtime.GOMAXPROCS(1)
	assert.Equal(10, DefaultPoolSize())
}
