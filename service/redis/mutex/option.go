package redismutex

import "time"

// Option .
type Option func(*option)

type option struct {
	tryLockCount    int
	tryLockDuration time.Duration
}

func defaultOption() *option {
	return &option{
		tryLockCount:    10,                     // 默认尝试获取锁的次数
		tryLockDuration: 100 * time.Millisecond, // 默认尝试获取锁的超时时间
	}
}

// WithTryLockCount 尝试获取锁的次数
func WithTryLockCount(count int) Option {
	return func(opt *option) {
		opt.tryLockCount = count
	}
}

// WithTryLockDuration 尝试获取锁的超时时间
func WithTryLockDuration(duration time.Duration) Option {
	return func(opt *option) {
		opt.tryLockDuration = duration
	}
}
