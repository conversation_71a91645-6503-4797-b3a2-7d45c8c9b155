package redismutex

import (
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/google/uuid"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

var delLockKeyScript = redis.NewScript(`if redis.call('GET', KEYS[1]) == ARGV[1] then return redis.call('DEL', KEYS[1]) end`)

// Mutex 互斥锁
type Mutex struct {
	client *redis.Client
	key    string
	ttl    time.Duration
	uuid   string

	opt *option
}

// New 创建一个 Mutex
func New(client *redis.Client, key string, ttl time.Duration, opts ...Option) *Mutex {
	mutex := &Mutex{
		client: client,
		key:    key,
		ttl:    ttl,
		uuid:   uuid.NewString(),
		opt:    defaultOption(),
	}
	for _, opt := range opts {
		opt(mutex.opt)
	}
	return mutex
}

// Lock 锁定
func (m *Mutex) Lock() {
	panic("unimplemented")
}

// TryLock 尝试获取锁
func (m *Mutex) TryLock() bool {
	for i := 0; i < m.opt.tryLockCount; i++ {
		ok, err := m.client.SetNX(m.key, m.uuid, m.ttl).Result()
		if err != nil {
			logger.WithField("key", m.key).Error(err)
			return false
		}
		if ok {
			return true
		}
		// 最后一次不需要等待
		if i < m.opt.tryLockCount-1 {
			<-time.NewTimer(m.opt.tryLockDuration).C // 等待后再次尝试
		}
	}
	return false
}

// Unlock 释放锁
func (m *Mutex) Unlock() {
	err := delLockKeyScript.Run(m.client, []string{m.key}, m.uuid).Err()
	// 锁不一致的情况下会返回 redis nil error，不需要处理
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithField("key", m.key).Error(err)
		// PASS
	}
}
