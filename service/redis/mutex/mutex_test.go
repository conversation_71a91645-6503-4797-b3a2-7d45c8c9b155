package redismutex

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestMutex(t *testing.T) {
	require := require.New(t)

	var (
		testKey = "testRedLock"
	)
	require.NoError(service.Redis.Del(testKey).Err())

	redismutex := New(service.Redis, testKey, 2*time.Second)
	// 首次获取锁成功
	ok := redismutex.TryLock()
	require.True(ok)

	// 尝试再次获取锁失败
	ok = redismutex.TryLock()
	require.False(ok)

	// 解锁后再次获取锁成功
	redismutex.Unlock()
	ok = redismutex.TryLock()
	require.True(ok)
}

func TestMutexWithOption(t *testing.T) {
	require := require.New(t)

	var (
		testKey = "testRedLock"
	)
	require.NoError(service.Redis.Del(testKey).Err())

	redismutex := New(service.Redis, testKey, 500*time.Millisecond,
		WithTryLockCount(2), WithTryLockDuration(600*time.Millisecond))
	require.Equal(2, redismutex.opt.tryLockCount)
	require.Equal(600*time.Millisecond, redismutex.opt.tryLockDuration)

	// 首次获取锁成功
	ok := redismutex.TryLock()
	require.True(ok)

	// 尝试再次获取锁成功（锁过期）
	ok = redismutex.TryLock()
	require.True(ok)
}
