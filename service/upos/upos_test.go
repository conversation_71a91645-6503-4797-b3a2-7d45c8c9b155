package upos

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	m.Run()
}

func TestNewClient(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{
		"mefmboss": &BucketConfig{
			Bucket: "mefmboss",

			PreuploadEndpoint: "http://uat-member.bilibili.com",
			Profile:           "mefm/upclone",
			Token:             "ba1bea1e70329dc72c53bc43424920f9",
		},
	}
	client := NewClient(config)
	require.NotNil(client)
	require.NotNil(client.config["mefmboss"])

	bucketConfig := client.config["mefmboss"]
	// 有默认重试次数
	assert.NotZero(bucketConfig.Retry)
	assert.Equal("mefmboss", bucketConfig.Bucket)

	bucketConfig.Retry = 5
	client = NewClient(Config{"mefmboss": bucketConfig})
	require.NotNil(client)
	require.NotNil(client.config["mefmboss"])
	assert.Equal(5, client.config["mefmboss"].Retry)
}

func TestBucketConfig_preupload(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := BucketConfig{
		Bucket: "mefmboss",

		PreuploadEndpoint: "http://uat-member.bilibili.com",
		Profile:           "mefm/upclone",
		Token:             "ba1bea1e70329dc72c53bc43424920f9",

		userAgent:  "maoer-upos-client",
		httpClient: &http.Client{Timeout: 5 * time.Second},
	}

	preupload, err := config.preupload("test.mp4", "upos://mefmboss/test/test.mp4", 0)
	require.NoError(err)
	assert.Equal(1, preupload.OK)

	endpoint, err := config.startUpload(preupload, nil)
	require.NoError(err)
	assert.NotEmpty(endpoint.UploadID)

	/*f, err := os.Open("test.mp4")
	require.NoError(err)
	defer f.Close()
	err = client.doUpload(preupload, endpoint, f)
	require.NoError(err)

	finish, err := client.finishUpload(preupload, endpoint)
	require.NoError(err)
	assert.Equal(1, finish.OK)*/
}
