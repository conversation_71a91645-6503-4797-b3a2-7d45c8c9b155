package upos

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
)

// 参考文档：https://info.bilibili.co/pages/viewpage.action?pageId=129820576

var (
	// ErrBucketConfigNotFound bucket config not found error
	ErrBucketConfigNotFound = errors.New("bucket config not found")

	// ErrEmptyResponse empty response error
	ErrEmptyResponse = errors.New("empty response")

	// ErrSignedURLPathNotFound signed url path not found error
	ErrSignedURLPathNotFound = errors.New("signed url path not found")
)

type playURLResp struct {
	Data map[string]struct {
		URL []string `json:"url"`
	} `json:"data"`
}

func getPath(uri string) (string, error) {
	if !strings.HasPrefix(uri, "upos://") {
		return "", errors.New("not an upos uri")
	}
	return uri[7:], nil
}

func (c *Client) findBucketConfigByUposURI(uposURI string) (*BucketConfig, error) {
	u, err := url.Parse(uposURI)
	if err != nil {
		return nil, err
	}
	config, exists := c.config[u.Host]
	if !exists {
		return nil, ErrBucketConfigNotFound
	}
	return config, nil
}

// SignPlayURL 获取 playurl 签名地址
func (c *Client) SignPlayURL(uposURI, uip, platform string) (string, error) {
	bucketConfig, err := c.findBucketConfigByUposURI(uposURI)
	if err != nil {
		return "", err
	}

	path, err := getPath(uposURI)
	if err != nil {
		return "", err
	}

	query := url.Values{}
	query.Set("upos_uri", uposURI)
	query.Set("bili_bucket", bucketConfig.Token)
	if uip != "" {
		query.Set("uip", uip)
	}
	if platform != "" {
		query.Set("platform", platform)
	}
	if bucketConfig.BusinessSign.ForceHost != 0 {
		query.Set("force_host", strconv.Itoa(bucketConfig.BusinessSign.ForceHost))
	}
	if bucketConfig.BusinessSign.CDN != "" {
		query.Set("cdn", bucketConfig.BusinessSign.CDN)
	}

	url := bucketConfig.BusinessSign.URL + "?" + query.Encode()
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return "", err
	}
	req.Header.Set("Accept", "application/json")
	// TODO: smaller timeout
	r, err := bucketConfig.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer r.Body.Close()
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return "", err
	}
	if r.StatusCode != http.StatusOK {
		return "", fmt.Errorf("http status %d", r.StatusCode)
	}
	if len(body) == 0 {
		return "", ErrEmptyResponse
	}
	var resp playURLResp
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return "", err
	}

	d, ok := resp.Data[path]
	if !ok || len(d.URL) <= 0 {
		return "", ErrSignedURLPathNotFound
	}

	return d.URL[0], nil
}

// NoSignPlayURL 获取 playurl 非签名地址
func (c *Client) NoSignPlayURL(uposURI string) (string, error) {
	bucketConfig, err := c.findBucketConfigByUposURI(uposURI)
	if err != nil {
		return "", err
	}

	// 配置检查，避免滥用获取非签名地址方法
	if !bucketConfig.NoSign {
		return "", fmt.Errorf("bucket %s 获取资源访问链接需要签名", bucketConfig.Bucket)
	}
	path, err := getPath(uposURI)
	if err != nil {
		return "", err
	}
	return bucketConfig.PublicURL + "/" + path, nil
}
