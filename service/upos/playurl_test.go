package upos

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetPath(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := getPath("aaaa")
	require.Error(err)

	_, err = getPath("oss://aaaa")
	require.Error(err)

	path, err := getPath("upos://mefmxcodeboss/test.m4a")
	require.NoError(err)
	assert.Equal("mefmxcodeboss/test.m4a", path)
}

func TestClient_findBucketConfigByUposURI(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{
		"mefmxcodeboss": &BucketConfig{
			Bucket: "mefmxcodeboss",

			Token: "f3f35390c4bfd0eaaa46adce1452d461",

			BusinessSign: BusinessSignConfig{
				URL: "http://uat-business-sign-playurl.bilibili.co/playurl",
				CDN: "office",
			},
		},
	}
	client := NewClient(config)
	c, err := client.findBucketConfigByUposURI("upos://mefmxcodeboss/test.m4a")
	require.NoError(err)
	assert.Equal(config["mefmxcodeboss"], c)

	_, err = client.findBucketConfigByUposURI("upos://mefmxcode/test.m4a")
	require.Equal(err, ErrBucketConfigNotFound)
}

func TestClient_SignPlayURL(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{
		"mefmxcodeboss": &BucketConfig{
			Bucket: "mefmxcodeboss",

			Token: "f3f35390c4bfd0eaaa46adce1452d461",

			BusinessSign: BusinessSignConfig{
				URL: "http://uat-business-sign-playurl.bilibili.co/playurl",
				CDN: "office",
			},
		},
	}
	client := NewClient(config)
	require.NotNil(client)

	url, err := client.SignPlayURL("upos://mefmxcodeboss/test.m4a", "", "")
	require.NoError(err)
	assert.NotEmpty(url)
}

func TestClient_NoSignPlayURL(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{
		"mefmxcodeboss": &BucketConfig{},
	}
	client := NewClient(config)
	require.NotNil(client)

	uri := "upos://mefmxcodeboss/test.m4a"
	_, err := client.NoSignPlayURL(uri)
	assert.EqualError(err, "bucket mefmxcodeboss 获取资源访问链接需要签名")

	bucketConfig := client.config["mefmxcodeboss"]
	bucketConfig.NoSign = true
	bucketConfig.PublicURL = "http://static.example.com"
	client.config["mefmxcodeboss"] = bucketConfig
	url, err := client.NoSignPlayURL(uri)
	require.NoError(err)
	assert.Equal("http://static.example.com/mefmxcodeboss/test.m4a", url)

	uri = "upo://mefmxcodeboss/test.m4a"
	_, err = client.NoSignPlayURL(uri)
	require.EqualError(err, "not an upos uri")
}
