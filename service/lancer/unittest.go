//go:build !release

package lancer

import (
	"net/http"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

// IsBufferEmpty is buffer empty
func (c *Client) IsBufferEmpty() bool {
	c.lock.Lock()
	defer c.lock.Unlock()
	return c.buffer.Len() == 0
}

// BufferContent get buffer content
func (c *Client) BufferContent() string {
	c.lock.Lock()
	defer c.lock.Unlock()
	return c.buffer.String()
}

// RunTestServer run for test
func RunTestServer() (string, func()) {
	server, cleanup := tutil.RunMockServerWithHTTPHandlerFunc(map[string]http.HandlerFunc{
		"/log/system": func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			_, err := w.Write([]byte("ok"))
			if err != nil {
				panic(err)
			}
		},
	})

	return server.URL + "/log/system", cleanup
}
