package lancer

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Client lancer client
type Client struct {
	httpClient *http.Client
	lock       *sync.Mutex
	buffer     bytes.Buffer

	endpoint string
	interval time.Duration
}

// NewClient new client
func NewClient(conf *Config) (*Client, error) {
	timeoutInDuration, err := time.ParseDuration(conf.Timeout)
	if err != nil {
		return nil, err
	}
	intervalInDuration, err := time.ParseDuration(conf.Interval)
	if err != nil {
		return nil, err
	}

	var client Client
	client.httpClient = &http.Client{Transport: &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   timeoutInDuration,
			KeepAlive: 30 * time.Second,
		}).Dial<PERSON>ontext,
		MaxIdleConns:          10,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   timeoutInDuration,
		ExpectContinueTimeout: timeoutInDuration,
	}}
	client.endpoint = conf.Endpoint
	client.interval = intervalInDuration
	client.lock = new(sync.Mutex)
	client.buffer = bytes.Buffer{}

	return &client, nil
}

const fieldSep = "\x01"
const rowSep = "\x03"

// Collect collect log
func (c *Client) Collect(logID string, fields ...interface{}) (err error) {
	c.lock.Lock()
	defer c.lock.Unlock()

	nowUnixMilli := util.TimeNow().UnixNano() / 1e6

	fieldNum := len(fields)
	args := make([]any, 0, fieldNum*2)

	for i, field := range fields {
		var format = "%s"
		switch field.(type) {
		case int, int64:
			format = "%d"
		case string:
			format = "%s"
		default:
			logger.Errorf("unknown field type: %v", field)
			// PASS
		}

		var sep = fieldSep
		if i == fieldNum-1 {
			sep = rowSep
		}
		args = append(args, fmt.Sprintf(format, field), sep)
	}

	// 报文格式：${logId1}${timestamp1}${filed1}\u0001${filed2}\u0001${filed3}\u0003
	// 参考：https://info.bilibili.co/pages/viewpage.action?pageId=94017407
	item := fmt.Sprintf("%s%d", logID, nowUnixMilli) +
		fmt.Sprintf(strings.Repeat("%s%s", fieldNum), args...)
	_, err = c.buffer.Write([]byte(
		item,
	))
	return err
}

// Send
// https://info.bilibili.co/pages/viewpage.action?pageId=94017407
func send(client *Client, data []byte) error {
	req, err := http.NewRequest(http.MethodPost, client.endpoint, bytes.NewReader(data))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Encoding", "gzip")
	req.Header.Set("Content-Length", strconv.Itoa(len(data)))

	resp, err := client.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("lancer request error: http_code: %d", resp.StatusCode)
	}

	return nil
}

// Run run lancer log consumer
// https://github.com/MiaoSiLa/contrib/blob/master/dockerfiles/gogstash/output/lancer/outputlancer.go
func Run(ctx context.Context, c *Client) (err error) {
	tick := time.NewTicker(c.interval)
	defer tick.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-tick.C:
			var data []byte
			c.lock.Lock()
			if c.buffer.Len() <= 0 {
				c.lock.Unlock()
				break
			}
			c.buffer.Truncate(c.buffer.Len() - len(rowSep))
			data = c.buffer.Bytes()
			c.buffer = bytes.Buffer{}
			c.lock.Unlock()

			var buf bytes.Buffer
			gw := gzip.NewWriter(&buf)
			_, err = gw.Write(data)
			if err != nil {
				logger.Errorf("lancer: gzip error: %v", err)
				break
			}

			err = gw.Close()
			if err != nil {
				logger.Errorf("lancer: gzip error: %v", err)
				break
			}

			err = send(c, buf.Bytes())
			if err != nil {
				logger.Errorf("lancer: send error: %v", err)
				// PASS
			}
		}
	}
}
