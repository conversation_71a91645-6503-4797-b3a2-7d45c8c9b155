package lancer

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	m.Run()
}

func TestNewClient(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	conf := &Config{
		Endpoint: "https://foo.com/bar",
		Timeout:  "5s",
		Interval: "3s",
	}
	client, err := NewClient(conf)
	require.NoError(err)

	assert.Equal(conf.Endpoint, client.endpoint)
	assert.Equal(conf.Interval, client.interval.String())
	assert.NotNil(client.lock)
}

func TestSend(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	endpoint, cleanup := RunTestServer()
	defer cleanup()

	client, err := NewClient(&Config{
		Endpoint: endpoint,
		Timeout:  "5s",
		Interval: "3s",
	})
	require.NoError(err)

	assert.NoError(send(client, make([]byte, 0)))
}

func TestCollect(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	client, err := NewClient(&Config{
		Endpoint: "https://foo.com/bar",
		Timeout:  "5s",
		Interval: "3s",
	})
	require.NoError(err)

	err = client.Collect("123456", "aaa", 111, "bbb", 222)
	require.NoError(err)

	err = client.Collect("567890", "ccc", 666, "ddd", 777)
	require.NoError(err)

	nowUnixMilli := util.TimeNow().UnixNano() / 1e6
	assert.Equal(client.buffer.String(),
		fmt.Sprintf("123456%daaa\u0001111\u0001bbb\u0001222\u0003", nowUnixMilli)+
			fmt.Sprintf("567890%dccc\u0001666\u0001ddd\u0001777\u0003", nowUnixMilli))
}

func TestRun(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	endpoint, cleanup := RunTestServer()
	defer cleanup()

	client, err := NewClient(&Config{
		Endpoint: endpoint,
		Timeout:  "3s",
		Interval: "1s",
	})
	require.NoError(err)

	ctx, cancel := context.WithCancel(context.Background())
	go func(ctx context.Context) {
		err = Run(ctx, client)
		assert.NoError(err)
	}(ctx)

	assert.NoError(client.Collect("123456", "aaa", 111, "bbb"))
	assert.NoError(client.Collect("123456", "ccc", 222, "ddd"))
	assert.NoError(client.Collect("123456", "eee", 333, "fff"))
	assert.NoError(client.Collect("123456", "ggg", 444, "hhh"))
	assert.NoError(client.Collect("123456", "iii", 555, "jjj"))
	assert.NoError(client.Collect("123456", "kkk", 666, "lll"))

	assert.False(client.IsBufferEmpty())

	time.Sleep(2 * time.Second)
	cancel()

	assert.True(client.IsBufferEmpty())
}
