package userapi

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

// URI
const (
	// TODO: 接口已迁移至 go 项目，待替换
	// https://www.tapd.bilibili.co/35612194/prong/stories/view/1135612194002761773
	URIGetDramaIDBySoundID = "drama://api/get-dramaid-by-soundid"
)

// minigame URI
const (
	URIActivityLotteryRecord   = "minigame://activity/lottery/record"
	URIActivityDrawPointUpdate = "minigame://activity/drawpoint/update"
	URIGamecenterNotifications = "minigame://gamecenter/notifications"
)

// live URI
const (
	URIUserBackpackAdd       = "live://user/backpack/add"
	URIUserAppearanceAdd     = "live://user/appearance/add"
	URIUserLiveViewLogDelete = "live://user/viewlog/delete"

	URILiveUserBirthdayPriv = "live://user/birthday/priv" // 为用户发放生日特权
	URILiveCheckPM          = "live://live/check-pm"      // 直播相关私信检查

	// TODO: 后续可以移除以下接口，之前用于直播相关私信检查
	URILiveUserLevel    = "live://user/level"         // 获得用户的直播等级和贵族等级
	URIGuildGetContract = "live://guild/get-contract" // 获取用户与公会生效中的合约
)

// main URI
const (
	URIListAvatarFrame = "mrpc://missevan-main/person/list-avatar-frame" // 批量获取用户佩戴中的头像框
)

// GetDramaIDBySoundID 获取剧集 ID
// Deprecated: 请使用 dramainfo.GetDramaIDsBySoundIDs 代替
func GetDramaIDBySoundID(data interface{}, clientIP string, v interface{}) error {
	return service.MRPC.Call(URIGetDramaIDBySoundID, clientIP, data, v)
}

// GetActivityLotteryRecord 获取用户追剧抽奖的历史记录
func GetActivityLotteryRecord(userID, eventID, p, pagesize int64, clientIP string) (json.RawMessage, error) {
	var data json.RawMessage
	err := service.MRPC.Call(URIActivityLotteryRecord, clientIP,
		map[string]interface{}{
			"user_id":  userID,
			"event_id": eventID,
			"p":        p,
			"pagesize": pagesize,
		}, &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// AddBackpackGift 给用户增加背包礼物
// https://ci.maoer.co/apidoc/live-service/#api-webrpc-PostRpcUserBackpackAdd
func AddBackpackGift(userID, giftID int64, num int, endTime time.Time) error {
	req := map[string]interface{}{
		"user_ids": []int64{userID},
		"gift_id":  giftID,
		"num":      num,
		"end_time": endTime.Unix(),
	}
	var res interface{}
	return service.MRPC.Call(URIUserBackpackAdd, "", req, &res)
}

// AddAppearance 给用户发放外观
// https://ci.maoer.co/apidoc/live-service/#api-imrpc-PostRpcUserAppearanceAdd
func AddAppearance(userID, appearanceID int64, appearanceType int, duration int64) error {
	// 入参 duration 是毫秒，需要转换成秒
	req := map[string]interface{}{
		"user_id":       userID,
		"type":          appearanceType,
		"duration":      duration / 1000,
		"appearance_id": appearanceID,
	}
	var res interface{}
	return service.MRPC.Call(URIUserAppearanceAdd, "", req, &res)
}

// SendUserBirthdayPriv 给用户发放生日特权
// https://ci.maoer.co/apidoc/live-service/#api-webrpc-PostRpcUserBirthdayPriv
func SendUserBirthdayPriv(userID int64, birthdateMMDD string) error {
	req := map[string]interface{}{
		"user_id":        userID,
		"birthdate_mmdd": birthdateMMDD,
	}
	var res interface{}
	return service.MRPC.Call(URILiveUserBirthdayPriv, "", req, &res)
}

// DeleteUserLiveViewLog 删除用户直播间访问记录
// https://ci.maoer.co/apidoc/live-service/#api-webrpc-PostRpcUserViewlogDelete
func DeleteUserLiveViewLog(userID int64, Type int, roomIDs ...int64) error {
	req := map[string]interface{}{
		"user_id": userID,
		"type":    Type,
	}
	if len(roomIDs) > 0 {
		req["room_ids"] = roomIDs
	}
	var res interface{}
	return service.MRPC.Call(URIUserLiveViewLogDelete, "", req, &res)
}

// UpdateDrawPointResp 更新用户抽奖积分结果
type UpdateDrawPointResp struct {
	Point int64
}

// UpdateEventDrawPoint 更新用户抽奖积分
// https://ci.maoer.co/apidoc/missevan-minigame/#api-rpc_activity-PostRpcActivityDrawpointUpdate
func UpdateEventDrawPoint(eventID, userID, point int64) (*UpdateDrawPointResp, error) {
	req := map[string]interface{}{
		"event_id": eventID,
		"user_id":  userID,
		"point":    point,
	}
	var res *UpdateDrawPointResp
	err := service.MRPC.Call(URIActivityDrawPointUpdate, "", req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GamecenterNotificationsResp gamecenter/notifications 接口响应
type GamecenterNotificationsResp struct {
	Data []interface{} `json:"data"`
}

// GamecenterNotifications 获取游戏自动下载游戏
func GamecenterNotifications(ctx mrpc.UserContext, userID int64, os util.Platform) (*GamecenterNotificationsResp, error) {
	req := map[string]interface{}{
		"user_id": userID,
		"os":      os,
	}
	var res *GamecenterNotificationsResp
	err := service.MRPC.Do(ctx, URIGamecenterNotifications, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// LiveUserLevelResp 用户的直播等级和贵族等级返回
type LiveUserLevelResp struct {
	LiveLevel     int        `json:"live_level"`
	NobelLevel    int        `json:"noble_level"`
	HighnessLevel int        `json:"highness_level"`
	RoomMedal     *RoomMedal `json:"room_medal"`
}

// RoomMedal 用户在当前房间下的粉丝勋章信息
type RoomMedal struct {
	Name      string    `json:"name"`
	Level     int       `json:"level"`
	FrameURL  string    `json:"frame_url"`
	NameColor string    `json:"name_color"`
	SuperFan  *SuperFan `json:"super_fan"`
}

// SuperFan 超粉相关信息
type SuperFan struct {
	ExpireTime   int64 `json:"expire_time"`   // 过期时间，秒级时间戳
	RegisterTime int64 `json:"register_time"` // 开通时间，秒级时间戳
	Days         int64 `json:"days"`          // 开通天数
}

// LiveUserLevel 获得用户的直播等级和贵族等级
func LiveUserLevel(ctx mrpc.UserContext, userID, roomID int64) (*LiveUserLevelResp, error) {
	req := map[string]interface{}{
		"user_id": userID,
		"room_id": roomID,
	}
	var res *LiveUserLevelResp
	err := service.MRPC.Do(ctx, URILiveUserLevel, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GuildGetContractResp 获取用户与公会生效中的合约返回
type GuildGetContractResp struct {
	ID               int64        `json:"id"`
	GuildID          int64        `json:"guild_id"`
	GuildOwner       int64        `json:"guild_owner"`
	GuildName        string       `json:"guild_name"`
	LiveID           int64        `json:"live_id"`
	RoomID           int64        `json:"room_id"`
	ContractDuration int64        `json:"contract_duration"`
	ContractStart    int64        `json:"contract_start"`
	ContractEnd      int64        `json:"contract_end"`
	Rate             int          `json:"rate"`
	KPI              string       `json:"kpi"`
	Type             int64        `json:"type"`
	Attr             util.BitMask `json:"attr"`
	Status           int64        `json:"status"`
	CreateTime       int64        `json:"create_time"`
}

// GuildGetContract 获取用户与公会生效中的合约
func GuildGetContract(ctx mrpc.UserContext, userID int64) (*GuildGetContractResp, error) {
	req := map[string]interface{}{
		"user_id": userID,
	}
	var res *GuildGetContractResp
	err := service.MRPC.Do(ctx, URIGuildGetContract, req, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// 直播相关私信检查发送状态
const (
	SendStatusShamAllow = iota // 假发送
	SendStatusAllow            // 允许发送
)

// LiveCheckPMResp 直播相关私信检查结果
type LiveCheckPMResp struct {
	Status      int  `json:"status"`
	HasLuckyBag bool `json:"has_lucky_bag"`
}

// LiveCheckPM 直播相关私信检查
func LiveCheckPM(uc mrpc.UserContext, fromUserID, toUserID int64) (*LiveCheckPMResp, error) {
	body := map[string]interface{}{
		"from_user_id": fromUserID,
		"to_user_id":   toUserID,
	}
	var resp *LiveCheckPMResp
	err := service.MRPC.Do(uc, URILiveCheckPM, body, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// 获取用户佩戴的头像框场景
const (
	SceneDefault int = iota // 默认场景
	SceneComment            // 评论区场景
)

// ListAvatarFrameParams 请求参数
type ListAvatarFrameParams struct {
	UserIDs     []int64 `json:"user_ids"`
	Scene       int     `json:"scene,omitempty"`
	ElementType *int    `json:"element_type,omitempty"`
	ElementID   *int64  `json:"element_id,omitempty"`
}

// AvatarFrameInfo 用户头像框信息
type AvatarFrameInfo struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	AvatarFrameURL string `json:"avatar_frame_url"`
	IconURL        string `json:"icon_url"`
}

// ListAvatarFrame 批量获取用户佩戴中的头像框
func ListAvatarFrame(c mrpc.UserContext, param *ListAvatarFrameParams) (map[int64]AvatarFrameInfo, error) {
	resp := make(map[int64]AvatarFrameInfo)
	err := service.MRPC.Do(c, URIListAvatarFrame, param, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
