package userapi

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestCheckUserAdLimitUser(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	oldLimitUserAd := LimitUserAd
	oldLimitUserAdThreshold := LimitUserAdThreshold
	oldLimitIPAd := LimitIPAd
	oldLimitIPAdThreshold := LimitIPAdThreshold

	// for testing only
	LimitUserAd = 2
	LimitUserAdThreshold = 3
	LimitIPAd = 4
	LimitIPAdThreshold = 5

	defer func() {
		LimitUserAd = oldLimitUserAd
		LimitUserAdThreshold = oldLimitUserAdThreshold
		LimitIPAd = oldLimitIPAd
		LimitIPAdThreshold = oldLimitIPAdThreshold
	}()

	testIP := "127.0.0.1"
	// u := handler.CreateTestUser()
	testUserID := int64(12)
	cleanUpUserAdLimit(testUserID)
	cleanUpIPAdLimit(testIP)

	// default User is "old user"
	for i := int64(0); i < LimitUserAd+1; i++ {
		ok, err := CheckUserIPAdLimit(testUserID, testIP)
		require.NoError(err)
		assert.True(ok)
	}

	cleanUpUserAdLimit(testUserID)
	cleanUpIPAdLimit(testIP)

	// redefine old user
	o := LimitUserAdRegistTime
	LimitUserAdRegistTime = time.Hour * 24 * 365 * 20 // 20 years
	defer func() { LimitUserAdRegistTime = o }()

	_, err := service.Redis.Del(serviceredis.KeyBlackListIP0.Format()).Result()
	require.NoError(err)

	for i := int64(0); i < LimitUserAd+1; i++ {
		ok, err := CheckUserIPAdLimit(testUserID, testIP)
		require.NoError(err)
		if i < LimitUserAd {
			assert.True(ok)
		} else {
			assert.False(ok)
		}
	}

	// check threshold
	key := serviceredis.KeyBlackListIP0.Format()
	_, err = service.Redis.ZScore(key, testIP).Result()
	require.Equal(redis.Nil, err)

	ok, err := CheckUserIPAdLimit(testUserID, testIP)
	require.NoError(err)
	assert.False(ok)

	f, err := service.Redis.ZScore(key, testIP).Result()
	require.NoError(err)
	assert.NotZero(f)

	// cleanup
	_, err = service.Redis.ZRem(key, testIP).Result()
	require.NoError(err)
}

func cleanUpUserAdLimit(userID int64) {
	_, err := service.Redis.Del(serviceredis.KeyTextScanUserAdLimit1.Format(userID)).Result()
	if err != nil {
		panic(err)
	}
}

func cleanUpIPAdLimit(ip string) {
	_, err := service.Redis.Del(serviceredis.KeyTextScanIPAdLimit1.Format(ip)).Result()
	if err != nil {
		panic(err)
	}
}

func TestUserTextsResultKey(t *testing.T) {
	param := TextScanTask{
		Text:  []string{"1", "2", "3"},
		Scene: scan.ScenePrivateMessage,
	}
	opt := antispam.OptionIgnoreAd
	key := userTextsResultKey(param.Text, param.Scene, opt)
	keyPre := fmt.Sprint(param.Text, param.Scene, opt)
	logger.Debugf("keyPre: %s\n key: %s", keyPre, key)
}

func TestCheckUserTexts(t *testing.T) {
	// TODO: 精细测试
	assert := assert.New(t)
	require := require.New(t)
	param := TextScanTask{
		Text:  []string{"1", "2", "3"},
		Scene: scan.ScenePrivateMessage,
	}
	r1, _, err := CheckUserTexts(param)
	assert.NoError(err)
	param = TextScanTask{
		Text:  []string{"1", "2", "3"},
		Scene: scan.ScenePrivateMessage,
	}
	// 这里使用了缓存
	r2, _, err := CheckUserTexts(param)
	assert.NoError(err)
	for i := range r1 {
		r1[i].AliyunMatch = nil
	}
	assert.Equal(r1, r2)

	require.NotEmpty(r2)
	require.NotNil(r2[0])
	r2[0].Pass = false
	saveUserTextsResult("testKey", r2)
	_, ok := tmpCache.Get("testKey")
	assert.False(ok)
}

func TestCopyScanTextResult(t *testing.T) {
	assert := assert.New(t)

	org := []*scan.CheckResult{{
		Pass:   true,
		Labels: []string{"1", "2"},
	}, {
		Pass:   false,
		Labels: []string{"3", "4"},
	},
	}
	copyed := copyScanTextResult(org)
	wrongCopy := make([]*scan.CheckResult, len(org))
	copy(wrongCopy, org)
	assert.Equal(org, copyed)
	assert.Equal(org, wrongCopy)
	org[0].Pass = false
	org[1].Labels[0] = "test"
	assert.True(copyed[0].Pass)
	assert.False(wrongCopy[0].Pass)
	assert.Equal("3", copyed[1].Labels[0])
	assert.Equal("test", wrongCopy[1].Labels[0])
}

func TestConvertScanResultToBase(t *testing.T) {
	assert := assert.New(t)
	baseResult := &scan.CheckResult{
		Score:  0.98,
		Pass:   true,
		Labels: []string{"ad"},
		BlacklistMatch: scan.BlacklistMatch{
			ViolationContent: []string{"QQ"}, Mode: 2, Regex: "QQ",
		},
		AliyunMatch: []scan.AliyunMatch{{RequestID: "xxxx"}},
	}
	result := ConvertScanResultToBase(baseResult)
	assert.Equal(result.Score, baseResult.Score)
	assert.Equal(result.Pass, baseResult.Pass)
	assert.Equal(result.Labels, baseResult.Labels)
}
