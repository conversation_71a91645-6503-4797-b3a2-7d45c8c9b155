package userapi

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, SceneDefault)
	assert.Equal(1, SceneComment)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(ListAvatarFrameParams{}, "user_ids", "scene", "element_type", "element_id")
	kc.CheckOmitEmpty(ListAvatarFrameParams{}, "scene", "element_type", "element_id")
	kc.Check(AvatarFrameInfo{}, "id", "name", "avatar_frame_url", "icon_url")
}

func TestGetActivityLotteryRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIActivityLotteryRecord, func(input interface{}) (output interface{}, err error) {
		require.EqualValues(input.(map[string]interface{})["event_id"], 201)
		return map[string]interface{}{
			"draw": map[string]interface{}{
				"free_times":     1,
				"purchase_times": 0,
				"support_free":   true,
				"point":          0,
			},
			"prizes": []map[string]interface{}{
				{
					"name":   "《烧不尽》广播剧明信片",
					"status": 1,
				},
				{
					"name":   "《跨界演员》广播剧明信片",
					"status": 2,
				},
			},
			"pagination": map[string]interface{}{
				"count":    22,
				"maxpage":  5,
				"p":        1,
				"pagesize": 5,
			},
		}, nil
	})
	defer cleanup()

	r, err := GetActivityLotteryRecord(10, 201, 1, 2, "127.0.0.1")
	require.NoError(err)
	require.NotNil(r)

	var lr struct {
		Draw *struct {
			FreeTimes     int64 `json:"free_times"`
			PurchaseTimes int64 `json:"purchase_times"`
			SupportFree   bool  `json:"support_free"`
			Point         int64 `json:"point"`
		} `json:"draw"`
		Prizes []*struct {
			Name   string `json:"name"`
			Status int    `json:"status"`
		} `json:"prizes"`
		Pagination util.Pagination `json:"pagination"`
	}
	err = json.Unmarshal(r, &lr)
	require.NoError(err)
	require.NotNil(lr.Draw)
	require.NotNil(lr.Pagination)
	assert.EqualValues(1, lr.Draw.FreeTimes)
	assert.EqualValues(2, len(lr.Prizes))
	assert.EqualValues(22, lr.Pagination.Count)
}

func TestAddBackpackGift(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(URIUserBackpackAdd, func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	assert.NoError(AddBackpackGift(12, 123, 1, util.TimeNow()))
}

func TestAddAppearance(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(URIUserAppearanceAdd, func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	assert.NoError(AddAppearance(12, 123, 1, 1000))
}

func TestSendUserBirthdayPriv(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(URILiveUserBirthdayPriv, func(input interface{}) (output interface{}, err error) {
		return "success", nil
	})
	defer cancel()

	assert.NoError(SendUserBirthdayPriv(12, "0126"))
}

func TestUpdateEventDrawPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	cancel := mrpc.SetMock(URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()

	res, err := UpdateEventDrawPoint(int64(1), int64(12), int64(1))
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(int64(1), res.Point)
}

func TestDeleteUserLiveViewLog(t *testing.T) {
	require := require.New(t)

	cancel := mrpc.SetMock(URIUserLiveViewLogDelete, func(input interface{}) (output interface{}, err error) {
		return "删除成功", nil
	})
	defer cancel()

	require.NoError(DeleteUserLiveViewLog(12, 0, 99, 100))
}

func TestGamecenterNotifications(t *testing.T) {
	assert := require.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URIGamecenterNotifications, func(input interface{}) (output interface{}, err error) {
		return GamecenterNotificationsResp{}, nil
	})
	defer cancel()

	ctx := mrpc.UserContext{}
	resp, err := GamecenterNotifications(ctx, 12, util.Unknown)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestLiveUserLevel(t *testing.T) {
	assert := require.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URILiveUserLevel, func(input interface{}) (output interface{}, err error) {
		return LiveUserLevelResp{
			LiveLevel:  1,
			NobelLevel: 1,
			RoomMedal: &RoomMedal{
				Name:  "test",
				Level: 1,
			},
		}, nil
	})
	defer cancel()

	ctx := mrpc.UserContext{}
	userLevel, err := LiveUserLevel(ctx, 123, 456)
	require.NoError(err)
	require.NotNil(userLevel)
	assert.Equal(1, userLevel.LiveLevel)
	assert.NotNil(userLevel.RoomMedal)
}

func TestGuildGetContract(t *testing.T) {
	assert := require.New(t)
	require := require.New(t)

	// 测试返回 nil
	cancel := mrpc.SetMock(URIGuildGetContract, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()

	ctx := mrpc.UserContext{}
	contract, err := GuildGetContract(ctx, 9074509)
	require.NoError(err)
	assert.Nil(contract)

	cancel = mrpc.SetMock(URIGuildGetContract, func(input interface{}) (output interface{}, err error) {
		return GuildGetContractResp{
			GuildID: 1,
			LiveID:  123,
			RoomID:  456,
		}, nil
	})
	defer cancel()
	contract, err = GuildGetContract(ctx, 9074509)
	require.NoError(err)
	assert.NotNil(contract)
}

func TestLiveCheckPM(t *testing.T) {
	assert := require.New(t)
	require := require.New(t)

	expected := &LiveCheckPMResp{
		Status:      SendStatusAllow,
		HasLuckyBag: true,
	}
	cancel := mrpc.SetMock(URILiveCheckPM, func(input interface{}) (interface{}, error) {
		body := input.(map[string]interface{})
		assert.EqualValues(9074509, body["from_user_id"])
		assert.EqualValues(9074510, body["to_user_id"])
		return expected, nil
	})
	defer cancel()

	resp, err := LiveCheckPM(mrpc.UserContext{}, 9074509, 9074510)
	require.NoError(err)
	assert.Equal(expected, resp)
}

func TestListAvatarFrame(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := ListAvatarFrameParams{
		UserIDs:     []int64{10},
		Scene:       SceneComment,
		ElementType: util.NewInt(1),
		ElementID:   util.NewInt64(233),
	}
	testResp := map[int64]AvatarFrameInfo{
		10: {
			ID:             10,
			Name:           "小耳朵",
			AvatarFrameURL: "https://static.maoercdn.com/test_4.webp",
			IconURL:        "https://static.maoercdn.com/test_5.webp",
		},
	}
	cancel := mrpc.SetMock(URIListAvatarFrame, func(i interface{}) (interface{}, error) {
		body, ok := i.(*ListAvatarFrameParams)
		require.True(ok)
		assert.Equal(param, *body)
		return testResp, nil
	})
	defer cancel()

	resp, err := ListAvatarFrame(mrpc.UserContext{}, &param)
	require.NoError(err)
	assert.Equal(testResp, resp)
}
