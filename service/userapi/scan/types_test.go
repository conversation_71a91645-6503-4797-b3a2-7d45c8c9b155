package scan

import (
	"testing"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	baseCheckResult := BaseCheckResult{}
	kc.Check(baseCheckResult, "score", "pass", "labels")

	checkResult := CheckResult{}
	kc.Check(checkResult, "score", "pass", "labels", "blacklist_match", "aliyun_match")

	blacklistMatch := BlacklistMatch{}
	kc.Check(blacklistMatch, "violation_content", "mode", "regex")

	aliyunMatch := AliyunMatch{}
	kc.Check(aliyunMatch, "requestId", "taskId", "label", "details")

	aliyunDetail := AliyunDetail{}
	kc.Check(aliyunDetail, "label", "context", "libName", "ruleType")
}
