package scan

// labels
const (
	LabelBlacklist = "blacklist"
	LabelBase      = "base"
	LabelUserInfo  = "user_info"
	LabelSearch    = "search"

	LabelEvil = "evil"

	// 阿里云文本的 label
	LabelNormal      = "normal"
	LabelSpam        = "spam"        // 含垃圾信息
	LabelAd          = "ad"          // 广告
	LabelPolitics    = "politics"    // 涉政
	LabelTerrorism   = "terrorism"   // 暴恐
	LabelAbuse       = "abuse"       // 辱骂
	LabelPorn        = "porn"        // 色情
	LabelFlood       = "flood"       // 灌水
	LabelContraband  = "contraband"  // 违禁
	LabelMeaningless = "meaningless" // 无意义
	LabelCustomized  = "customized"  // 自定义
)

// BaseCheckResult 文本/图片检测返回的结果
type BaseCheckResult struct {
	Score  float64  `json:"score"`
	Pass   bool     `json:"pass"`
	Labels []string `json:"labels"`
}

// CheckResult 文本/图片检测返回的详细结果
type CheckResult struct {
	Score          float64        `json:"score"`
	Pass           bool           `json:"pass"`
	Labels         []string       `json:"labels"`
	BlacklistMatch BlacklistMatch `json:"blacklist_match,omitempty"`
	AliyunMatch    []AliyunMatch  `json:"aliyun_match,omitempty"`

	TaskID    string `json:"-"`
	RequestID string `json:"-"`
}

// NewPassedCheckResult 通过的结果
func NewPassedCheckResult() *CheckResult {
	return &CheckResult{Pass: true}
}

// FillEmptyLabel 对于没有 label 的结果，添加 normal label
func (cr *CheckResult) FillEmptyLabel() {
	if len(cr.Labels) == 0 {
		cr.Labels = []string{LabelNormal}
	}
}

// BlacklistMatch 黑名单配置的命中结果
type BlacklistMatch struct {
	ViolationContent []string `json:"violation_content,omitempty"`
	Mode             int      `json:"mode,omitempty"`
	Regex            string   `json:"regex,omitempty"`
}

// AliyunMatch 阿里云命中显示结果
type AliyunMatch struct {
	RequestID string          `json:"requestId"`
	TaskID    string          `json:"taskId"`
	Label     string          `json:"label"`
	Details   []*AliyunDetail `json:"details"`
}

// AliyunDetail 阿里云命中详情
type AliyunDetail struct {
	Label string `json:"label"`
	// 检测文本命中的风险关键词，如果命中了关键词会返回该内容，如果命中了算法模型，则不会返回该字段。
	Context string `json:"context"`
	// 命中自定义词库时，才会返回当前字段。取值为创建词库时设置的词库名称。
	LibName []string `json:"libName"`
	// 命中行为规则时，才会返回当前字段
	RuleType []string `json:"ruleType"`
}

// CheckTextOption 文本检测的选项
type CheckTextOption int

// CheckTextOption constants
const (
	AdFreeOption CheckTextOption = iota
)

// 文本检测场景
const (
	ScenePrivateMessage = "private_message"
	SceneComment        = "comment"
	SceneIntro          = "intro"
	SceneUserInfo       = "user_info" // 图片检测也会用到
	SceneDanmaku        = "danmaku"
	SceneIM             = "im"
	SceneSearch         = "search"
)
