package userapi

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/patrickmn/go-cache"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 临时解决直播间刷屏的缓存，待更好的缓存替换
var tmpCache = cache.New(time.Minute, 2*time.Minute)

// TextScanTask 文本检测任务
type TextScanTask struct {
	Text    []string `json:"text"`
	Scene   string   `json:"scene,omitempty"`
	UserID  int64    `json:"user_id,omitempty"`
	EquipID string   `json:"equip_id,omitempty"`
	IP      string   `json:"ip,omitempty"`
	AdFree  bool     `json:"ad_free,omitempty"`
}

var textScene = map[string]antispam.Option{
	scan.ScenePrivateMessage: antispam.OptionIgnoreAd,
	scan.SceneComment:        antispam.OptionIgnoreAd,
	scan.SceneSearch:         antispam.OptionIgnoreAd,
}

// CheckUserTexts 用户文本检测
// NOTICE: 阿里云文本审核返回结果仅对 scan/im 接口使用
func CheckUserTexts(input TextScanTask) ([]*scan.CheckResult, []antispam.AliyunTextRespElem, error) {
	opt := textScene[input.Scene]
	if input.AdFree {
		// TODO: add opts list
		opt = antispam.OptionIgnoreAd
	}
	canCache := true
	var cacheKey string
	for i := range input.Text {
		if len([]rune(input.Text[i])) > 30 {
			canCache = false
			break
		}
	}
	var results []*scan.CheckResult
	var aliRes []antispam.AliyunTextRespElem
	var ok bool
	if canCache {
		cacheKey = userTextsResultKey(input.Text, input.Scene, opt)
		results, ok = getUserTextsResult(cacheKey)
	}
	if !ok {
		var err error
		results, aliRes, err = service.AntiSpam.CheckTexts(input.Text, input.UserID, input.Scene, opt)
		if err != nil {
			return nil, nil, err
		}
		if canCache {
			saveUserTextsResult(cacheKey, results)
		}
	}

	// WORKAROUND: search 场景不走阿里云
	if input.Scene == scan.SceneSearch {
		return results, aliRes, nil
	}

	if !input.AdFree && input.UserID != 0 {
		adLimit := false
		for _, r := range results {
			if (r.Pass && util.HasElem(r.Labels, "ad")) ||
				util.HasElem(r.Labels, "blacklist") {
				ok, err := CheckUserIPAdLimit(input.UserID, input.IP)
				if err != nil {
					return nil, nil, err
				}
				adLimit = !ok
				break
			}
		}
		if adLimit {
			for i := range results {
				if results[i].Pass && util.HasElem(results[i].Labels, "ad") {
					results[i].Pass = false
				}
			}
		}
	}
	return results, aliRes, nil
}

// userTextsResultKey 生成文本检测缓存的 key
func userTextsResultKey(text []string, scene string, opts ...antispam.Option) string {
	pre := fmt.Sprint(text, scene, opts)
	return util.MD5(pre)
}

func getUserTextsResult(key string) ([]*scan.CheckResult, bool) {
	v, ok := tmpCache.Get(key)
	if !ok {
		return nil, false
	}
	resp := v.([]*scan.CheckResult)
	return copyScanTextResult(resp), true
}

func saveUserTextsResult(key string, resp []*scan.CheckResult) {
	for i := range resp {
		if !resp[i].Pass {
			// 只有全部检测通过的响应才缓存
			return
		}
	}
	tmpCache.Set(key, copyScanTextResult(resp), 0)
}

// NOTICE: 使用 copy(dst, src []*scan.checkResult) copy 的是指针元素，并没有复制指针指向的对象
func copyScanTextResult(src []*scan.CheckResult) (dst []*scan.CheckResult) {
	dst = make([]*scan.CheckResult, len(src))
	for i := range src {
		if src[i] != nil {
			cr := scan.CheckResult{
				Pass: src[i].Pass,
			}
			cr.Labels = make([]string, len(src[i].Labels))
			copy(cr.Labels, src[i].Labels)
			dst[i] = &cr
		}
	}
	return
}

// limits
var (
	LimitUserAd           = int64(5)
	LimitUserAdThreshold  = int64(8)
	LimitIPAd             = int64(9)
	LimitIPAdThreshold    = int64(12)
	LimitUserAdRegistTime = time.Duration(time.Hour * 24 * 3)
	LimitExpireAd         = time.Duration(time.Hour * 12)
)

// CheckUserIPAdLimit 增加并检测是否达到次数限制，超过一定阈值会放入黑名单
func CheckUserIPAdLimit(userID int64, ip string) (bool, error) {
	ctime, err := user.MowangskUser{}.GetCTimeByID(userID)
	if err != nil {
		if err != user.ErrUserNotFound {
			return false, err
		}
		logger.WithField("user_id", userID).Error("黑名单检查未查询到用户")
		ctime = util.TimeNow().Unix()
		// PASS
	}
	// allow old user post directly
	if time.Unix(ctime, 0).Before(util.TimeNow().Add(-LimitUserAdRegistTime)) {
		return true, nil
	}
	// check ad limit for current user
	keyUser := serviceredis.KeyTextScanUserAdLimit1.Format(userID)
	pipe := service.Redis.Pipeline()
	userCmd := pipe.Incr(keyUser)
	pipe.Expire(keyUser, LimitExpireAd)
	var ipCmd *redis.IntCmd // 用于 IP 限制的检查
	if ip != "" {
		keyIP := serviceredis.KeyTextScanIPAdLimit1.Format(ip)
		ipCmd = pipe.Incr(keyIP)
		pipe.Expire(keyIP, LimitExpireAd)
	}
	_, err = pipe.Exec()
	if err != nil {
		return false, err
	}

	limited := false
	addIPToBlackList := false
	if n := userCmd.Val(); n > LimitUserAd {
		addIPToBlackList = n > LimitUserAdThreshold
		limited = true
	}
	// 检查 IP 的限制，IP 不为空时 ipCmd 不为 nil
	if ipCmd != nil {
		n := ipCmd.Val()
		limited = limited || n > LimitIPAd
		addIPToBlackList = addIPToBlackList || n > LimitIPAdThreshold
	}

	if addIPToBlackList && ip != "" {
		// add to blacklist
		key := serviceredis.KeyBlackListIP0.Format()
		t := util.TimeNow().Add(time.Hour * 3).Unix()
		_, err = service.Redis.ZAdd(key, &redis.Z{Score: float64(t), Member: ip}).Result()
		if err != nil {
			logger.WithFields(logger.Fields{
				"ip":      ip,
				"user_id": userID,
			}).Errorf("Add IP to black list failed: %v", err)
		} else {
			logger.WithFields(logger.Fields{
				"ip":      ip,
				"user_id": userID,
			}).Warnf("Add IP to black list")
		}
	}

	return !limited, nil
}

// ConvertScanResultToBase 调用方如果不需要详细的结果，这里可以做业务上需要的结果的转换
func ConvertScanResultToBase(resp *scan.CheckResult) (baseResp *scan.BaseCheckResult) {
	baseResp = &scan.BaseCheckResult{
		Score:  resp.Score,
		Pass:   resp.Pass,
		Labels: resp.Labels}
	return
}
