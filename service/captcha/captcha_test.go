package captcha

import (
	"os"
	"testing"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/afs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(t *testing.M) {
	logger.InitTestLog()

	os.Exit(t.Run())
}

func TestName(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "enabled", "access_key_id", "access_key_secret", "app_key", "region_id", "endpoint", "slide_url")
}

func TestCheckCaptcha(t *testing.T) {
	assert := assert.New(t)

	conf := Config{
		AccessKeyID:     "test",
		AccessKeySecret: "test",
		AppKey:          "test",
		RegionID:        "cn-hangzhou",
		Endpoint:        "https://afs.cn-hangzhou.aliyuncs.com",
	}
	client, err := NewClient(&conf)
	assert.NoError(err)
	sig := "test"
	success := client.Valid(
		"test",
		"test",
		sig, "nc_test", "127.0.0.1")
	assert.False(success)
}

func TestDoRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	client, err := NewClient(BuildDefaultConfig())
	require.NoError(err)
	request := afs.CreateAuthenticateSigRequest()
	request.AppKey = client.conf.AppKey
	request.SessionId = "sessionID"
	request.Token = "token"
	request.Sig = "sig"
	request.Scene = SceneOriginalMessage
	request.RemoteIp = "127.0.0.1"
	request.Domain = client.domain
	// 校验失败
	resp, err := client.doRequest(request)
	require.NoError(err)
	assert.Equal(statusFail, resp.Code)
	assert.Equal("sig decrypt error", resp.Msg)
	// WORKAROUND: 无法获取真实 sig 等参数，校验成功的单元测试不太好测
}
