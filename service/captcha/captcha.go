package captcha

import (
	"errors"
	"net/url"
	"strings"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/auth/credentials"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/afs"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// SceneOriginalMessage 使用场景
const SceneOriginalMessage = "nc_message"

// captcha api response code
const (
	statusSuccess = 100 // 人机验证通过
	statusFail    = 900 // 验证失败
)

// Config config for captcha
type Config struct {
	Enabled         bool   `yaml:"enabled"`           // 是否开启人机校验
	AccessKeyID     string `yaml:"access_key_id"`     // aliyun 服务密钥 ID
	AccessKeySecret string `yaml:"access_key_secret"` // aliyun 服务密钥
	AppKey          string `yaml:"app_key"`           // aliyun 人机校验 appKey
	RegionID        string `yaml:"region_id"`         // aliyun 地域
	Endpoint        string `yaml:"endpoint"`          // aliyun 服务地址
	SlideURL        string `yaml:"slide_url"`         // 滑动校验页面
}

// BuildDefaultConfig returns a Config
func BuildDefaultConfig() *Config {
	return &Config{
		Enabled:         false,
		AccessKeyID:     "LTAIsNW7Hxzgnxu2",
		AccessKeySecret: "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
		AppKey:          "FFFF0N0N0000000016A7",
		RegionID:        "cn-hangzhou",
		Endpoint:        "https://afs.cn-hangzhou.aliyuncs.com",
		SlideURL:        "http://static.missevan.com/standalone/uat/403/slide.html",
	}
}

// Client structure for oss bucket
type Client struct {
	client *afs.Client
	conf   *Config

	domain string
}

// NewClient new Client
func NewClient(conf *Config) (*Client, error) {
	credential := credentials.NewAccessKeyCredential(conf.AccessKeyID, conf.AccessKeySecret)
	endpoint, err := url.Parse(conf.Endpoint)
	if err != nil {
		return nil, err
	}
	if endpoint.Scheme == "" || endpoint.Host == "" {
		return nil, errors.New("captcha: wrong endpoint")
	}
	config := sdk.NewConfig().WithScheme(strings.ToUpper(endpoint.Scheme)).
		WithAutoRetry(false).WithTimeout(10 * time.Second)
	c, err := afs.NewClientWithOptions(conf.RegionID, config, credential)
	if err != nil {
		return nil, err
	}
	return &Client{
		client: c,
		conf:   conf,
		domain: endpoint.Host,
	}, err
}

// IsEnabled Is captcha enabled
func (c *Client) IsEnabled() bool {
	return c.conf.Enabled
}

// Valid 人机校验
// doc: https://help.aliyun.com/document_detail/66340.htm
func (c *Client) Valid(sessionID, token, sig, scene, ip string) bool {
	request := afs.CreateAuthenticateSigRequest()
	request.AppKey = c.conf.AppKey
	request.SessionId = sessionID
	request.Token = token
	request.Sig = sig
	request.Scene = scene
	request.RemoteIp = ip
	request.Domain = c.domain

	resp, err := c.doRequest(request)
	if err != nil {
		logger.WithField("ip", request.RemoteIp).Errorf("人机验证请求失败：%v", err)
		return false
	}
	if resp.Code != statusSuccess {
		logger.WithField("ip", request.RemoteIp).Debugf("人机验证未通过：%s", resp.Msg)
		return false
	}
	return true
}

func (c *Client) doRequest(request *afs.AuthenticateSigRequest) (*afs.AuthenticateSigResponse, error) {
	return c.client.AuthenticateSig(request)
}
