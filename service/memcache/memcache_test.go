package servicememcache

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var conf = Config{
	Servers:  "memcache.srv.maoer.co:11211",
	Username: "user",
	Password: "pass",
	PoolSize: 5,
}

func TestMemcacheClient(t *testing.T) {
	// WORKAROUND: 测试机上未部署 memcache 暂时跳过这个测试
	t.Skip()
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	mc := NewMemcacheClient(&conf)

	err := mc.Set("testkey", "foo", time.Second*5)
	assert.NoError(err)
	err = mc.Set("testkey2", "foo", time.Second*10)
	assert.NoError(err)

	val, err := mc.Get("testkey")
	assert.NoError(err)
	assert.Equal("foo", val)

	time.Sleep(time.Second * 5)

	val, err = mc.Get("testkey")
	assert.Equal(ErrNotFound, err)
	assert.Empty(val)

	err = mc.Del("testkey")
	assert.Equal(ErrNotFound, err)
	err = mc.Del("testkey2")
	assert.NoError(err)

	val, err = mc.Get("testkey2")
	assert.Equal(ErrNotFound, err)
	assert.Empty(val)
}
