package servicememcache

import (
	"time"

	"github.com/memcachier/mc"
)

// Config for memcache config
type Config struct {
	Servers  string `yaml:"servers"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	PoolSize int    `yaml:"pool_size"`
}

// Client for memcache, implements simple interface
type Client struct {
	mc *mc.Client
}

// errors
var (
	ErrNotFound  = mc.ErrNotFound
	ErrKeyExists = mc.ErrKeyExists
)

// NewMemcacheClient return the memcache client
func NewMemcacheClient(conf *Config) *Client {
	mconfig := *mc.DefaultConfig()
	mconfig.PoolSize = conf.PoolSize
	c := mc.NewMCwithConfig(conf.Servers, conf.Username, conf.Password, &mconfig)
	return &Client{mc: c}
}

// Get retrieves a value from the cache
func (c *Client) Get(key string) (val string, err error) {
	val, _, _, err = c.mc.Get(key)
	if err == mc.ErrNotFound {
		// reset result
		val = ""
	}
	return
}

// Set sets a key/value pair in the cache
func (c *Client) Set(key, value string, expire time.Duration) (err error) {
	_, err = c.mc.Set(key, value, 0, uint32(expire.Seconds()), 0)
	return
}

// Del deletes a key/value from the cache
func (c *Client) Del(key string) (err error) {
	return c.mc.Del(key)
}

// MC returns the internal mc client
func (c *Client) MC() *mc.Client {
	return c.mc
}
