package search

import (
	"encoding/json"
	"net/url"
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var c *Client

func TestMain(m *testing.M) {
	c, _ = NewClient(TestConfig())
	cleanup := MockOpenSearchManagementServer(c)
	defer cleanup()

	logger.InitTestLog()
	os.Exit(m.Run())
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "region_id", "endpoint", "access_key_id", "access_key_secret", "push_user_id_key", "apps", "management")
}

func TestCommitClickData(t *testing.T) {
	assert := assert.New(t)

	clicks := []map[string]interface{}{
		{
			"cmd": "ADD",
			"fields": map[string]interface{}{
				"event_type":   0,
				"search_type":  1,
				"input":        "魔道",
				"hint_count":   4,
				"result_count": 10,
				"ipv":          3,
				"item_origin":  1,
				"origin":       1,
				"user_id":      0,
			}},
		{
			"cmd": "ADD",
			"fields": map[string]interface{}{
				"event_type":   1,
				"search_type":  1,
				"input":        "魔道",
				"hint_count":   4,
				"result_count": 10,
				"item_type":    0,
				"item_title":   "魔道祖师",
				"item_rank":    2,
				"origin":       1,
				"user_id":      346286,
			}},
		{
			"cmd": "ADD",
			"fields": map[string]interface{}{
				"event_type":       2,
				"search_type":      1,
				"input":            "魔道",
				"hint_count":       4,
				"result_count":     10,
				"item_origin":      1,
				"item_type":        0,
				"item_id":          891,
				"item_title":       "魔道祖师第一季",
				"item_rank":        2,
				"item_duration":    3000,
				"item_isback":      0,
				"origin":           1,
				"user_id":          346286,
				"ops_request_misc": url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
			}},
		{
			"cmd": "ADD",
			"fields": map[string]interface{}{
				"event_type":   3,
				"search_type":  3,
				"input":        "魔道",
				"result_count": 4,
				"user_id":      346286,
				"origin":       2,
			}},
	}
	err := c.CommitClickData(clicks, c.Apps.Sound.Name, nil)
	assert.NoError(err)
}

func TestQuerySearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Search 测试
	params := Params{
		Keyword:        "阿&&",
		UserID:         346286,
		Page:           1,
		PageSize:       20,
		SearchType:     TypeSound,
		AppName:        c.Apps.Sound.Name,
		SecondRankName: "sound",
		Filter: []string{
			"checked = 1",
		},
		AppParam: c.Apps.Sound,
	}
	params.Keyword = searchQueryReplacer.Replace(params.Keyword)
	queryParams := map[string]string{
		"query":            buildSearchQueryWithOptions(newBuildOptions(&params)),
		"second_rank_name": params.SecondRankName,
		"user_id":          strconv.FormatInt(params.UserID, 10),
		"raw_query":        params.Keyword,
	}
	query := querySearchParam{
		queryParams: queryParams,
		uriType:     uriTypeSearch,
	}

	var seResp Response
	err := querySearch(c, &query, &params, &seResp)
	require.NoError(err)
	assert.Equal("OK", seResp.Status)
	assert.GreaterOrEqual(len(seResp.Result.Items), 1)

	var detail struct {
		SoundStr  string `json:"soundstr"`
		IndexName string `json:"index_name"`
	}
	err = json.Unmarshal(seResp.Result.Items[0], &detail)
	require.NoError(err)
	assert.Equal(c.Apps.Sound.Name, detail.IndexName)
	assert.Contains(detail.SoundStr, "阿")

	// Suggest 测试
	params = Params{
		Keyword:    "阿",
		UserID:     346286,
		PageSize:   20,
		SearchType: TypeSound,
		AppName:    c.Apps.Sound.Name,
		AppParam: AppParam{
			Name: c.Apps.Sound.Name,
		},
	}
	queryParams = map[string]string{
		"query":     params.Keyword,
		"hit":       strconv.Itoa(params.PageSize),
		"re_search": "homonym", // 在少结果的时候触发同音字重查
		"user_id":   strconv.FormatInt(params.UserID, 10),
	}
	query = querySearchParam{
		queryParams: queryParams,
		uriType:     uriTypeSuggest,
	}

	var suResp SuggestResponse
	err = querySearch(c, &query, &params, &suResp)
	require.NoError(err)
	assert.Nil(suResp.Errors)
	assert.GreaterOrEqual(len(suResp.Suggestions), 1)
	assert.Contains(suResp.Suggestions[0].Suggestion, "阿")
}

func TestSearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := Params{
		Keyword:        "阿&&",
		UserID:         346286,
		Page:           1,
		PageSize:       20,
		SearchType:     TypeSound,
		AppName:        c.Apps.Sound.Name,
		SecondRankName: "sound",
		Filter: []string{
			"checked = 1",
		},
		AppParam: c.Apps.Sound,
	}

	resp, err := c.Search(params)
	require.NoError(err)
	assert.Equal("OK", resp.Status)
	assert.GreaterOrEqual(len(resp.Result.Items), 1)

	var detail struct {
		SoundStr  string `json:"soundstr"`
		IndexName string `json:"index_name"`
	}

	err = json.Unmarshal(resp.Result.Items[0], &detail)
	assert.NoError(err)
	assert.Equal(c.Apps.Sound.Name, detail.IndexName)
	assert.Contains(detail.SoundStr, "阿")

	// 测试关键词为空字符串（关键词全为特殊字符被过滤掉）时，获取到空的搜索结果
	params.Keyword = ""
	resp, err = c.Search(params)
	assert.NoError(err)
	assert.Equal("OK", resp.Status)
	assert.Empty(resp.Result.Items)

	params.Keyword = "阿&&"
	params.Page = 15000
	resp, err = c.Search(params)
	assert.NoError(err)
	assert.Equal("OK", resp.Status)
	assert.Empty(resp.Result.Items)

	params.Keyword = "&&&&&"
	resp, err = c.Search(params)
	assert.NoError(err)
	assert.Equal("OK", resp.Status)
	assert.GreaterOrEqual(len(resp.Result.Items), 0)

	// 测试有剧集插入
	params = Params{
		Keyword:           "貌合神离",
		Page:              2,
		PageSize:          3,
		SearchType:        TypeDrama,
		AppName:           c.Apps.Drama.Name,
		SecondRankName:    "drama",
		Filter:            []string{"checked = 1", "police = 0", "notin(id, \"1|3\")"},
		AppParam:          c.Apps.Drama,
		AllFixedTargetIDs: []string{"1", "2"},
		CurrentActiveFixedParams: []FixedParam{
			{
				TargetIndex: 1,
				TargetID:    1,
			},
		},
		PreFixedNum: 1,
	}
	resp, err = c.Search(params)
	require.NoError(err)
	assert.Equal("OK", resp.Status)
	assert.Len(resp.Result.Items, 2)
	require.Greater(len(resp.Result.Items), 0)
	compare0 := resp.Result.Items[0]

	params = Params{
		Keyword:        "貌合神离",
		Page:           1,
		PageSize:       20,
		SearchType:     TypeDrama,
		AppName:        c.Apps.Drama.Name,
		SecondRankName: "drama",
		Filter:         []string{"checked = 1", "police = 0"},
		AppParam:       c.Apps.Drama,
	}
	resp, err = c.Search(params)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal("OK", resp.Status)
	require.Greater(len(resp.Result.Items), 3)
	// 3*(2-1)-(2-1) = 2
	assert.Equal(compare0, resp.Result.Items[2])
}

func TestSuggest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := Params{
		Keyword:    "阿",
		UserID:     346286,
		PageSize:   20,
		SearchType: TypeSound,
		AppName:    c.Apps.Sound.Name,
		AppParam: AppParam{
			Name: c.Apps.Sound.Name,
		},
	}

	resp, err := c.Suggest(params)
	require.NoError(err)
	assert.Nil(resp.Errors)
	assert.GreaterOrEqual(len(resp.Suggestions), 1)
	assert.Contains(resp.Suggestions[0].Suggestion, "阿")

	// 测试关键词为空字符串（关键词全为特殊字符被过滤掉）时，获取到空的搜索结果
	params.Keyword = ""
	resp, err = c.Suggest(params)
	assert.NoError(err)
	assert.Nil(resp.Errors)
	assert.GreaterOrEqual(len(resp.Suggestions), 0)
}

func TestResponseEmpty(t *testing.T) {
	assert := assert.New(t)
	result := Response{}.Empty()
	assert.Equal(result.Status, "OK")
	assert.GreaterOrEqual(len(result.Result.Items), 0)
}

func TestInsertResultItems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	response := Response{
		Status: StatusSuccess,
		Result: struct {
			Total     int               `json:"total"`
			Num       int               `json:"num"`
			ViewTotal int               `json:"viewtotal"`
			Items     []json.RawMessage `json:"items"`
		}{},
	}

	type result struct {
		SearchName string `json:"search_index"`
	}
	bytes1, err := json.Marshal(result{SearchName: "剧集 1"})
	require.NoError(err)
	response.Result.Items = append(response.Result.Items, bytes1)
	response.Result.Num = 1
	response.Result.Total = 1
	response.Result.ViewTotal = 1
	require.Len(response.Result.Items, 1)

	// 插到第二位 ["剧集 1", "剧集 2"]
	bytes2, err := json.Marshal(result{SearchName: "剧集 2"})
	require.NoError(err)
	response.InsertResultItems([]InsertDataItem{
		{
			Index: 1,
			Item:  bytes2,
		},
	}, 3)
	assert.Len(response.Result.Items, 2)
	var r result
	err = json.Unmarshal(response.Result.Items[1], &r)
	require.NoError(err)
	assert.Equal("剧集 2", r.SearchName)
	assert.Equal(2, response.Result.Num)
	assert.Equal(4, response.Result.Total)
	assert.Equal(4, response.Result.ViewTotal)

	// 插到第二位 ["剧集 1", "剧集 3", "剧集 2"]
	bytes3, err := json.Marshal(result{SearchName: "剧集 3"})
	require.NoError(err)
	response.InsertResultItems([]InsertDataItem{
		{
			Index: 1,
			Item:  bytes3,
		},
	}, 1)
	assert.Len(response.Result.Items, 3)
	err = json.Unmarshal(response.Result.Items[1], &r)
	require.NoError(err)
	assert.Equal("剧集 3", r.SearchName)

	// 插到第十九位 ["剧集 1", "剧集 3", "剧集 2", "剧集 19"]
	bytes4, err := json.Marshal(result{SearchName: "剧集 19"})
	require.NoError(err)
	response.InsertResultItems([]InsertDataItem{
		{
			Index: 19,
			Item:  bytes4,
		},
	}, 1)
	assert.Len(response.Result.Items, 4)
	err = json.Unmarshal(response.Result.Items[len(response.Result.Items)-1], &r)
	require.NoError(err)
	assert.Equal("剧集 19", r.SearchName)
}

func TestSuggestResponseEmpty(t *testing.T) {
	assert := assert.New(t)
	result := SuggestResponse{}.Empty()
	assert.Nil(result.Errors)
	assert.GreaterOrEqual(len(result.Suggestions), 0)
}

func TestClientPushItemToAll(t *testing.T) {
	t.Run("PushItemToAllSuggestDenyList", func(t *testing.T) {
		require := require.New(t)

		err := c.PushItemToAllSuggestDenyList(ManagementCmdDelete, "apple")
		require.NoError(err)
	})

	t.Run("PushItemToAllSynonym", func(t *testing.T) {
		require := require.New(t)

		err := c.PushItemToAllSynonym(ManagementCmdAdd, "apple", []string{"iphone"})
		require.NoError(err)
	})

	t.Run("PushItemToAllUserAnalyzer", func(t *testing.T) {
		require := require.New(t)

		err := c.PushItemToAllUserAnalyzer(ManagementCmdAdd, "apple", "apple")
		require.NoError(err)
	})
}

func TestApp(t *testing.T) {
	assert := assert.New(t)

	expectedOK := []bool{true, true, true, true, true, true, true, true, true, true, false, false}
	expectedName := []string{"uat_m_sound", "uat_mowangskuser", "uat_m_album", "uat_mowangsksoundseiy",
		"uat_drama", "uat_live", "uat_special_search_items", "uat_m_tag", "uat_message", "uat_special_search_items", "", ""}
	appTypes := []int{TypeSound, TypeUser, TypeAlbum, TypeSeiy, TypeDrama, TypeLive,
		TypeSpecial, TypeChannel, TypeDanmaku, TypeSpecialTopicCard, 999, -1}
	for i := range appTypes {
		param, ok := c.App(appTypes[i])
		assert.Equal(expectedOK[i], ok)
		assert.Equal(expectedName[i], param.Name)
	}
}

func TestCheckConfig(t *testing.T) {
	assert := assert.New(t)

	conf := TestConfig()
	conf.Apps.Sound.Name = ""
	err := checkConfig(conf)
	assert.Equal(ErrOpenSearchNameMisconfigured, err)

	conf = TestConfig()
	conf.Apps.Sound.Sort = []string{}
	err = checkConfig(conf)
	assert.Equal(ErrOpenSearchSortMisconfigured, err)

	conf = TestConfig()
	conf.Apps.Sound.Indexes = []IndexItem{}
	err = checkConfig(conf)
	assert.Equal(ErrOpenSearchIndexMisconfigured, err)

	conf = TestConfig()
	conf.Apps.Sound.Indexes = []IndexItem{{Key: "", Weight: 99}}
	err = checkConfig(conf)
	assert.Equal(ErrOpenSearchIndexMisconfigured, err)

	conf.Apps.Sound.Indexes = []IndexItem{{Key: "text_pinyin", Fuzzy: true, Extra: true}}
	err = checkConfig(conf)
	assert.Equal(ErrOpenSearchIndexAttributesMisconfigured, err)
}
