// +build !release

package search

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testDictionaryNameSuggestDenyList = "test_drama_denylist"
	testDictionaryNameSynonym         = "test_drama_synonym"
	testUserAnalyzerName              = "test_user_analyzer"
)

type testManagementAPIResp struct {
	Foo string `json:"foo"`
}

func (resp *testManagementAPIResp) GetRequestID() string {
	return "dev_request_id"
}

// TestConfig returns a opensearch config for test
func TestConfig() *Config {
	testConf := &Config{
		RegionID: "cn-hangzhou",
		Endpoint: "http://opensearch-cn-hangzhou.aliyuncs.com",
		Apps: Apps{
			Sound: AppParam{
				Name: "uat_m_sound",
				Sort: []string{"sound", "view", "time", "dm"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "tags", Weight: 99},
					{Key: "title", Weight: 99},
					{Key: "title_pinyin", Weight: 99, Fuzzy: true},
					{Key: "username", Weight: 99},
					{Key: "username_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			User: AppParam{
				Name: "uat_mowangskuser",
				Sort: []string{"user"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "default_pinyin", Weight: 99, Fuzzy: true},
					{Key: "title", Weight: 99},
					{Key: "title_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Album: AppParam{
				Name: "uat_m_album",
				Sort: []string{"album"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "title", Weight: 99},
					{Key: "title_pinyin", Weight: 99, Fuzzy: true},
					{Key: "tags", Weight: 99},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Seiy: AppParam{
				Name: "uat_mowangsksoundseiy",
				Sort: []string{"seiy"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "default_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Drama: AppParam{
				Name: "uat_drama",
				Sort: []string{"drama", "view", "time"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "context", Weight: 10},
					{Key: "title", Weight: 99},
					{Key: "title_pinyin", Weight: 99, Fuzzy: true},
					{Key: "user", Weight: 99},
					{Key: "user_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Live: AppParam{
				Name: "uat_live",
				Sort: []string{"live"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "default_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true},
					{Key: "creator_id", Weight: 90, Int: true},
				},
			},
			Special: AppParam{
				Name: "uat_special_search_items",
				Sort: []string{"id"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Channel: AppParam{
				Name: "uat_m_tag",
				Sort: []string{"channel"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99},
					{Key: "id", Weight: 99, Int: true},
				},
			},
			Danmaku: AppParam{
				Name: "uat_message",
				Sort: []string{"danmaku"},
				Indexes: []IndexItem{
					{Key: "default", Weight: 99, Extra: true},
					{Key: "text_pinyin", Weight: 99, Fuzzy: true},
					{Key: "id", Weight: 99, Int: true, Extra: true},
					{Key: "user_id", Extra: true},
					{Key: "sound_id", Extra: true},
				},
			},
			SpecialTopicCard: AppParam{
				Name: "uat_special_search_items",
				Sort: []string{"topic_card"},
				Indexes: []IndexItem{
					{Key: "topiccard", Weight: 99},
				},
			},
		},
		Management: managementAPIConfig{
			Endpoint: "https://opensearch.cn-hangzhou.aliyuncs.com",
			SuggestDenyList: []string{
				"uat_drama_denylist",
				"uat_mowangskuser_denylist",
				"uat_m_sound_denylist",
			},
			Synonym: []string{
				"uat_drama",
				"uat_sound",
			},
			UserAnalyzer: []string{
				"uat_drama_name",
			},
		},
	}
	testConf.SetKeys("LTAIsNW7Hxzgnxu2", "AmAfcg3kGLLLJvEZkXqltUMZl6UZ4o",
		"test_push_user_id_key")
	return testConf
}

// MockOpenSearchManagementServer mock open search 管控 API 响应
func MockOpenSearchManagementServer(c *Client) func() {
	respFunc := func(responseWriter http.ResponseWriter, req *http.Request) {
		var requestID string
		switch req.URL.Path {
		case fmt.Sprintf(apiPushInterventionDictionaryEntriesTpl, testDictionaryNameSuggestDenyList):
			requestID = "32E0AD4F-8B17-5E3E-BE28-11111111"
		case fmt.Sprintf(apiPushInterventionDictionaryEntriesTpl, testDictionaryNameSynonym):
			requestID = "32E0AD4F-8B17-5E3E-BE28-22222222"
		case fmt.Sprintf(apiPushUserAnalyzerEntriesTpl, testUserAnalyzerName):
			requestID = "32E0AD4F-8B17-5E3E-BE28-33333333"
		}

		responseWriter.Header().Add("Content-Type", "application/json")
		responseWriter.WriteHeader(http.StatusOK)
		result := responsePushInterventionDictionaryEntries{
			managementAPIResponse{
				RequestID: requestID,
			},
		}
		bytes, err := json.Marshal(result)
		if err != nil {
			panic(err)
		}

		_, err = responseWriter.Write(bytes)
		if err != nil {
			panic(err)
		}
	}

	server, cleanup := tutil.RunMockServerWithHTTPHandlerFunc(map[string]http.HandlerFunc{
		fmt.Sprintf(apiPushInterventionDictionaryEntriesTpl, testDictionaryNameSuggestDenyList): respFunc,
		fmt.Sprintf(apiPushInterventionDictionaryEntriesTpl, testDictionaryNameSynonym):         respFunc,
		fmt.Sprintf(apiPushUserAnalyzerEntriesTpl, testUserAnalyzerName):                        respFunc,
		"/dev": func(responseWriter http.ResponseWriter, req *http.Request) {
			responseWriter.Header().Add("Content-Type", "application/json")
			responseWriter.WriteHeader(http.StatusOK)
			result := testManagementAPIResp{
				Foo: "bar",
			}
			bytes, err := json.Marshal(result)
			if err != nil {
				panic(err)
			}

			_, err = responseWriter.Write(bytes)
			if err != nil {
				panic(err)
			}
		},
	})

	var err error
	if c.Management.Endpoint, err = url.Parse(server.URL); err != nil {
		panic(err)
	}
	c.Management.SuggestDenyList = []string{testDictionaryNameSuggestDenyList}
	c.Management.Synonym = []string{testDictionaryNameSynonym}
	c.Management.UserAnalyzer = []string{testUserAnalyzerName}
	return cleanup
}
