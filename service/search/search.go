package search

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/opensearch"

	"github.com/MiaoSiLa/missevan-go/logger"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

// openSearch 搜索超时错误码
// https://help.aliyun.com/document_detail/193826.html
const errorCodeTimeout = 1000

// 不同 uriType 标识
const (
	uriTypeSuggest = iota
	uriTypeSearch
)

// OpenSearch 配置错误
var (
	ErrOpenSearchNameMisconfigured            = errors.New("opensearch app misconfigured with empty name")
	ErrOpenSearchSortMisconfigured            = errors.New("opensearch app misconfigured with empty sort")
	ErrOpenSearchIndexMisconfigured           = errors.New("opensearch app misconfigured with empty index or empty index key")
	ErrOpenSearchIndexAttributesMisconfigured = errors.New("opensearch app misconfigured with index having both fuzzy and extra attributes")
)

// Config contains configuration for Client
type Config struct {
	RegionID string `yaml:"region_id"`
	Endpoint string `yaml:"endpoint"`

	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`

	PushUserIDKey string `yaml:"push_user_id_key"`

	Apps Apps `yaml:"apps"`

	Management managementAPIConfig `yaml:"management"`
}

// Client wraps a opensearch.Client
type Client struct {
	client          *opensearch.Client
	accessKeyID     string
	accessKeySecret []byte
	pushUserIDKey   string
	host            string
	Apps            Apps

	Management struct {
		Endpoint        *url.URL
		SuggestDenyList []string
		Synonym         []string
		UserAnalyzer    []string
	}
}

// BuildDefaultConfig returns a Config with default region and endpoint
func BuildDefaultConfig() *Config {
	return &Config{
		RegionID: "cn-hangzhou",
		Endpoint: "http://opensearch-cn-hangzhou.aliyuncs.com",
		Management: managementAPIConfig{
			Endpoint: "https://opensearch.cn-hangzhou.aliyuncs.com",
		},
	}
}

// SetKeys sets the accessKeyID, accessKeySecret and pushUserIDKey keys
func (c *Config) SetKeys(accessKeyID, accessKeySecret, pushUserIDKey string) *Config {
	c.AccessKeyID = accessKeyID
	c.AccessKeySecret = accessKeySecret
	c.PushUserIDKey = pushUserIDKey
	return c
}

func checkConfig(conf *Config) error {
	apps := reflect.ValueOf(conf.Apps)
	for i := 0; i < apps.NumField(); i++ {
		if apps.Field(i).FieldByName("Name").String() == "" {
			return ErrOpenSearchNameMisconfigured
		}
		if apps.Field(i).FieldByName("Sort").Len() == 0 {
			return ErrOpenSearchSortMisconfigured
		}

		indexes := apps.Field(i).FieldByName("Indexes").Interface().([]IndexItem)
		if len(indexes) == 0 {
			return ErrOpenSearchIndexMisconfigured
		}
		for _, v := range indexes {
			if v.Key == "" {
				return ErrOpenSearchIndexMisconfigured
			}
			if v.Fuzzy && v.Extra {
				return ErrOpenSearchIndexAttributesMisconfigured
			}
		}
	}

	return nil
}

// NewClient returns a opensearch client
func NewClient(conf *Config) (*Client, error) {
	c, err := opensearch.NewClientWithAccessKey(conf.RegionID, conf.AccessKeyID, conf.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	endpoint, err := url.Parse(conf.Management.Endpoint)
	if err != nil {
		return nil, err
	}
	err = checkConfig(conf)
	if err != nil {
		return nil, err
	}

	client := &Client{
		client:          c,
		accessKeyID:     conf.AccessKeyID,
		accessKeySecret: []byte(conf.AccessKeySecret),
		pushUserIDKey:   conf.PushUserIDKey,
		host:            conf.Endpoint,
		Apps:            conf.Apps,
	}
	client.Management.Endpoint = endpoint
	client.Management.SuggestDenyList = conf.Management.SuggestDenyList
	client.Management.Synonym = conf.Management.Synonym
	client.Management.UserAnalyzer = conf.Management.UserAnalyzer
	return client, nil
}

// AddClickRecord 添加点击采集记录到 recordBuffer
func AddClickRecord(recordBuffer []map[string]interface{}, basicFields map[string]interface{}) []map[string]interface{} {
	jsonFields := map[string]interface{}{
		"sdk_type":    sdkType,
		"sdk_version": sdkVersion,
	}
	for k, v := range basicFields {
		jsonFields[k] = v
	}
	jsonRecord := map[string]interface{}{
		"cmd":    "ADD",
		"fields": jsonFields,
	}
	recordBuffer = append(recordBuffer, jsonRecord)
	return recordBuffer
}

// CommitClickData 提交点击采集记录（推送至阿里云开放搜索）
func (c *Client) CommitClickData(recordBuffer []map[string]interface{}, appName string,
	collectionName *string) error {
	if len(recordBuffer) == 0 {
		return errors.New("Empty Click Data")
	}
	if collectionName == nil {
		collectionName = &appName
	}
	now := util.TimeNow()
	recordsJSON, _ := json.Marshal(recordBuffer)
	uri := "/v3/openapi" + createPushPath(appName, *collectionName)
	req, _ := http.NewRequest("POST", c.host+uri, bytes.NewReader(recordsJSON))
	md5sum := md5.Sum(recordsJSON)
	req.Header.Set("Content-MD5", hex.EncodeToString(md5sum[:]))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Date", now.UTC().Format("2006-01-02T15:04:05Z"))
	req.Header.Set("X-Opensearch-Nonce", strconv.FormatInt(now.Unix()*100, 10)+strconv.Itoa(1000+rand.Intn(9000)))
	var canonicalized bytes.Buffer
	canonicalized.WriteString("POST\n")
	canonicalized.WriteString(req.Header.Get("Content-MD5"))
	canonicalized.WriteRune('\n')
	canonicalized.WriteString(req.Header.Get("Content-Type"))
	canonicalized.WriteRune('\n')
	canonicalized.WriteString(req.Header.Get("Date"))
	canonicalized.WriteRune('\n')
	canonicalized.Write(canonicalizedHeaders(req.Header))
	canonicalized.WriteString(URIEncode(uri, false))

	h := hmac.New(sha1.New, c.accessKeySecret)
	_, _ = h.Write(canonicalized.Bytes())
	sig := base64.StdEncoding.EncodeToString(h.Sum(nil))
	auth := fmt.Sprintf("OPENSEARCH %s:%s", c.accessKeyID, sig)
	req.Header.Set("Authorization", auth)

	client := http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	var result CommitResult
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return err
	}
	if result.Status != StatusSuccess {
		return &serviceutil.APIError{
			Status:  500, // http status
			Message: fmt.Sprintf("(%s) - %v", result.RequestID, result.Errors),
		}
	}
	return nil
}

// querySearchParam 请求 openSearch 的参数
type querySearchParam struct {
	queryParams map[string]string
	uriType     int
}

// Empty 空的搜索结果
func (Response) Empty() *Response {
	resp := &Response{
		Status: StatusSuccess,
		Errors: nil,
	}
	resp.Result.Items = []json.RawMessage{}
	return resp
}

// InsertDataItem for InsertResultItems
type InsertDataItem struct {
	Index int
	Item  json.RawMessage
}

// InsertResultItems 向搜索结果中插入数据
// NOTICE: insertData 必须是有序的（InsertDataItem.Index 正序）
func (r *Response) InsertResultItems(insertDataItems []InsertDataItem, filterTotal int) {
	insertLength := len(insertDataItems)
	if insertLength == 0 || r.Status != StatusSuccess {
		return
	}

	itemsLength := len(r.Result.Items)
	newItems := make([]json.RawMessage, 0, itemsLength+insertLength)
	// 如果插入的位置有空缺，相关 item 会依次向前顺延
	// https://info.missevan.com/pages/viewpage.action?pageId=82576065
	for i, j := 0, 0; i < insertLength || j < itemsLength; {
		if i < insertLength && (insertDataItems[i].Index == len(newItems) || j == itemsLength) {
			newItems = append(newItems, insertDataItems[i].Item)
			i++
			continue
		}
		if j < itemsLength {
			newItems = append(newItems, r.Result.Items[j])
			j++
		}
	}
	r.Result.Items = newItems
	r.Result.Num += insertLength
	r.AddResultTotal(filterTotal)
}

// AddResultTotal 修改 r.Result.Total、r.Result.ViewTotal
func (r *Response) AddResultTotal(addTotal int) {
	r.Result.Total += addTotal
	r.Result.ViewTotal += addTotal
}

// Empty 空的搜索建议结果
func (SuggestResponse) Empty() *SuggestResponse {
	return &SuggestResponse{
		Errors:      nil,
		Suggestions: []SuggestItem{},
	}
}

// querySearch 根据不同的 uriType 获得不同类型的搜索结果
func querySearch(c *Client, query *querySearchParam, params *Params, resp interface{}) error {
	var uri string
	switch query.uriType {
	case uriTypeSearch:
		uri = fmt.Sprintf("/v3/openapi/apps/%s/search", params.AppName)
	case uriTypeSuggest:
		uri = fmt.Sprintf("/v3/openapi/apps/%s/suggest/%s/search", params.AppName, params.AppParam.Name)
	default:
		return fmt.Errorf("搜索请求类型有误：uriType = %d", query.uriType)
	}

	req, err := http.NewRequest(http.MethodGet, c.host+uri, nil)
	if err != nil {
		return err
	}

	rawQuery := c.getRawQuery(query.queryParams)
	logger.WithField("app", params.AppName).Debugf("aliyun opensearch query: %s", func(rawQuery string) string {
		decode, err := url.QueryUnescape(rawQuery)
		if err != nil {
			return rawQuery
		}
		return decode
	}(rawQuery))
	req.URL.RawQuery = rawQuery
	c.setHeader(req, uri)
	client := http.Client{
		Timeout: 10 * time.Second,
	}

	response, err := client.Do(req)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	data, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, resp)
	if err != nil {
		return err
	}

	return nil
}

// Search get data by keyword from opensearch
// 相关文档：https://help.aliyun.com/document_detail/57155.html
// https://github.com/MiaoSiLa/requirements-doc/tree/master/2018-12-18%20搜索的智能联想/README.md
func (c *Client) Search(params Params) (*Response, error) {
	var resp Response
	params.Keyword = strings.TrimSpace(searchQueryReplacer.Replace(params.Keyword))
	if params.Keyword == "" {
		// 关键词可能被过滤掉特殊字符导致为空，此时直接返回空的搜索结果
		return resp.Empty(), nil
	}
	// start+hit 不能超过 5000
	// https://help.aliyun.com/document_detail/29146.html
	if params.PageSize*params.Page > MaxSearchResultCount {
		return resp.Empty(), nil
	}

	queryParams := map[string]string{
		"query":            buildSearchQueryWithOptions(newBuildOptions(&params)),
		"second_rank_name": params.SecondRankName,
	}
	if params.UserID > 0 {
		queryParams["user_id"] = strconv.FormatInt(params.UserID, 10)
	}
	if params.ABTest != "" {
		queryParams["abtest"] = params.ABTest
	}
	// 用于下拉提示等算法训练使用
	queryParams["raw_query"] = params.Keyword
	// 下拉提示点击情况上报
	if params.SuggestRequestID != "" {
		queryParams["suggest_request_id"] = params.SuggestRequestID
	}
	query := querySearchParam{
		queryParams: queryParams,
		uriType:     uriTypeSearch,
	}

	err := querySearch(c, &query, &params, &resp)
	if err != nil {
		return nil, err
	}

	timeout := util.Includes(len(resp.Errors), func(i int) bool {
		return resp.Errors[i].Code == errorCodeTimeout
	})

	if timeout {
		resp = Response{}
		err = querySearch(c, &query, &params, &resp)
		if err != nil {
			return nil, err
		}
	}

	if resp.Result.ViewTotal > MaxSearchResultCount {
		resp.Result.ViewTotal = MaxSearchResultCount
	}

	if resp.Status != StatusSuccess {
		message := fmt.Sprintf("requestID: %s", resp.RequestID)
		for _, error := range resp.Errors {
			message += fmt.Sprintf(", code: %d, msg: %s", error.Code, error.Message)
		}
		err = &serviceutil.APIError{
			Status:  http.StatusInternalServerError,
			Message: message,
		}
		logger.WithField("app", params.AppName).Errorf("aliyun opensearch search error: %v", err)

		// 有些 error 只是提示性的错误（opensearch 内部进行分片查询，某些分片超时，其它分片可正常查询到，因此整个查询结果是有效的）
		if len(resp.Result.Items) > 0 {
			return &resp, nil
		}
		return nil, err
	}

	return &resp, nil
}

// Suggest provide suggest items for search
// https://help.aliyun.com/document_detail/57156.html
func (c *Client) Suggest(params Params) (*SuggestResponse, error) {
	var resp SuggestResponse
	if params.Keyword == "" {
		// 关键词可能被过滤掉特殊字符导致为空，此时直接返回空的搜索结果
		return resp.Empty(), nil
	}

	// 下拉提示不需要转义 & 和 '
	queryParams := map[string]string{
		"query":     params.Keyword,
		"hit":       strconv.Itoa(params.PageSize),
		"re_search": "homonym", // 在少结果的时候触发同音字重查
	}
	if params.UserID != 0 {
		queryParams["user_id"] = strconv.FormatInt(params.UserID, 10)
	}
	query := querySearchParam{
		queryParams: queryParams,
		uriType:     uriTypeSuggest,
	}

	err := querySearch(c, &query, &params, &resp)
	if err != nil {
		return nil, err
	}

	timeout := util.Includes(len(resp.Errors), func(i int) bool {
		return resp.Errors[i].Code == errorCodeTimeout
	})

	if timeout {
		resp = SuggestResponse{}
		err = querySearch(c, &query, &params, &resp)
		if err != nil {
			return nil, err
		}
	}

	if len(resp.Errors) > 0 {
		logMsg := fmt.Sprintf("requestID: %s, code: %d, msg: %s", resp.RequestID, resp.Errors[0].Code, resp.Errors[0].Message)
		// 文档建议：errors 不为空，不代表 suggestions 为空。因此，解析时结果时，都得通过 suggestions 是否为空来判断是否无数据展示
		// https://help.aliyun.com/document_detail/74272.html
		if len(resp.Suggestions) == 0 {
			err = &serviceutil.APIError{
				Status:  http.StatusInternalServerError,
				Message: logMsg,
			}
			logger.WithField("app", params.AppName).Errorf("aliyun opensearch suggest error: %v", err)
			return nil, err
		}
		logger.WithField("app", params.AppName).Warnf("aliyun opensearch suggest error: %s", logMsg)
	}

	return &resp, nil
}

// PushItemToAllSynonym 变更所有同义词典
func (c *Client) PushItemToAllSynonym(cmd string, word string, alias []string) error {
	var errMsg []string
	for _, dictionaryName := range c.Management.Synonym {
		if _, err := c.pushItemToSynonym(cmd, dictionaryName, word, alias); err != nil {
			logger.WithField("dictionary_name", dictionaryName).Errorf("aliyun opensearch pushItemToSynonym error: %v", err)
			errMsg = append(errMsg, fmt.Sprintf("dictionary_name: %s, error: %s;", dictionaryName, err.Error()))
			// PASS
			continue
		}
	}
	if len(errMsg) != 0 {
		return errors.New(strings.Join(errMsg, "; "))
	}

	return nil
}

// PushItemToAllSuggestDenyList 变更所有下拉提示黑名单
func (c *Client) PushItemToAllSuggestDenyList(cmd string, word string) error {
	var errMsg []string
	for _, dictionaryName := range c.Management.SuggestDenyList {
		if _, err := c.pushItemToSuggestDenyList(cmd, dictionaryName, word); err != nil {
			logger.WithField("dictionary_name", dictionaryName).Errorf("aliyun opensearch pushItemToSuggestDenyList error: %v", err)
			errMsg = append(errMsg, fmt.Sprintf("dictionary_name: %s, error: %s;", dictionaryName, err.Error()))
			// PASS
			continue
		}
	}
	if len(errMsg) != 0 {
		return errors.New(strings.Join(errMsg, "; "))
	}

	return nil
}

// PushItemToAllUserAnalyzer 变更所有自定义分析器中词条
func (c *Client) PushItemToAllUserAnalyzer(cmd, key, value string) error {
	var errMsg []string
	for _, analyzerName := range c.Management.UserAnalyzer {
		if _, err := c.pushToUserAnalyzer(cmd, analyzerName, key, value); err != nil {
			logger.WithField("analyzer_name", analyzerName).Errorf("aliyun opensearch pushToUserAnalyzer error: %v", err)
			errMsg = append(errMsg, fmt.Sprintf("analyzer_name: %s, error: %s;", analyzerName, err.Error()))
			// PASS
			continue
		}
	}
	if len(errMsg) != 0 {
		return errors.New(strings.Join(errMsg, "; "))
	}

	return nil
}

// App 获取 OpenSearch 应用
func (c *Client) App(appEnum int) (AppParam, bool) {
	switch appEnum {
	case TypeSound:
		return c.Apps.Sound, true
	case TypeUser:
		return c.Apps.User, true
	case TypeAlbum:
		return c.Apps.Album, true
	case TypeSeiy:
		return c.Apps.Seiy, true
	case TypeDrama:
		return c.Apps.Drama, true
	case TypeLive:
		return c.Apps.Live, true
	case TypeSpecial:
		return c.Apps.Special, true
	case TypeChannel:
		return c.Apps.Channel, true
	case TypeDanmaku:
		return c.Apps.Danmaku, true
	case TypeSpecialTopicCard:
		return c.Apps.SpecialTopicCard, true
	default:
		return AppParam{}, false
	}
}

// GeneratePushUserID generate push_user_id
func (c *Client) GeneratePushUserID(userID int64) string {
	return util.MD5(fmt.Sprintf("%s:%d", c.pushUserIDKey, userID))
}
