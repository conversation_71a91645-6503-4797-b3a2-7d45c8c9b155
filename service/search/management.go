package search

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"

	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

const managementAPIVersion = "2017-12-25"

const (
	apiPushInterventionDictionaryEntriesTpl = "/v4/openapi/intervention-dictionaries/%s/entries/actions/bulk"
	apiPushUserAnalyzerEntriesTpl           = "/v4/openapi/user-analyzers/%s/entries/actions/bulk"
)

// 变更干预词典命令参数
const (
	ManagementCmdAdd    = "add"
	ManagementCmdDelete = "delete"
)

type managementAPIConfig struct {
	Endpoint        string   `yaml:"endpoint"`
	SuggestDenyList []string `yaml:"suggest_denylist"`
	Synonym         []string `yaml:"synonym"`
	UserAnalyzer    []string `yaml:"user_analyzer"`
}

// pushToSynonymBodyItem 变更同义词词条的单个请求项
type pushToSynonymBodyItem struct {
	pushInterventionDictionaryEntryBodyItem
	Alias     []string `json:"alias"`
	AntiAlias []string `json:"antiAlias"`
}

// pushInterventionDictionaryEntryBodyItem 变更干预词典中词条的单个请求项
type pushInterventionDictionaryEntryBodyItem struct {
	Cmd  string `json:"cmd"`
	Word string `json:"word"`
}

type pushUserAnalyzerEntriesBody struct {
	Entries []pushUserAnalyzerEntryItem `json:"entries"`
}

type pushUserAnalyzerEntryItem struct {
	Cmd          string `json:"cmd"`
	Key          string `json:"key"`
	Value        string `json:"value"`
	SplitEnabled bool   `json:"splitEnabled"`
}

type managementAPIResponseInterface interface {
	GetRequestID() string
}

type managementAPIResponse struct {
	RequestID string `json:"requestId"`
}

func (resp *managementAPIResponse) GetRequestID() string {
	return resp.RequestID
}

// responsePushInterventionDictionaryEntries 变更干预词典中词条的响应体
type responsePushInterventionDictionaryEntries struct {
	managementAPIResponse
}

// responsePushUserAnalyzerEntries 变更自定义分析器中词条的响应体
type responsePushUserAnalyzerEntries struct {
	managementAPIResponse
}

// pushItemToSynonym 变更同义词中的单个词条
func (c *Client) pushItemToSynonym(cmd string, dictionaryName string, word string, alias []string) (*responsePushInterventionDictionaryEntries, error) {
	if cmd == ManagementCmdDelete {
		// delete 时 alias 没有作用，但接口要求有 alias: [] 的结构
		alias = make([]string, 0)
	}
	return c.pushToSynonym(dictionaryName, []pushToSynonymBodyItem{
		{
			pushInterventionDictionaryEntryBodyItem: pushInterventionDictionaryEntryBodyItem{
				Cmd:  cmd,
				Word: word,
			},
			Alias:     alias,
			AntiAlias: make([]string, 0),
		},
	})
}

// pushItemToSuggestDenyList 变更下拉提示黑名单中的单个词条
func (c *Client) pushItemToSuggestDenyList(cmd string, dictionaryName string, word string) (*responsePushInterventionDictionaryEntries, error) {
	return c.pushToSuggestDenyList(dictionaryName, []pushInterventionDictionaryEntryBodyItem{
		{
			Cmd:  cmd,
			Word: word,
		},
	})
}

// pushToSynonym 变更同义词词条
// 参考：https://help.aliyun.com/document_detail/173606.html
func (c *Client) pushToSynonym(dictionaryName string, body []pushToSynonymBodyItem) (*responsePushInterventionDictionaryEntries, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	return c.pushInterventionDictionaryEntries(dictionaryName, bodyBytes)
}

// pushToSuggestDenyList 变更下拉提示（搜索建议）联想词黑名单词条
func (c *Client) pushToSuggestDenyList(dictionaryName string, body []pushInterventionDictionaryEntryBodyItem) (*responsePushInterventionDictionaryEntries, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	return c.pushInterventionDictionaryEntries(dictionaryName, bodyBytes)
}

// 变更干预词典（同义词、下拉提示、停用词、纠错词等）中的词条
// 参考：
// https://help.aliyun.com/document_detail/175962.html
// https://next.api.aliyun.com/api/OpenSearch/2017-12-25/PushInterventionDictionaryEntries
func (c *Client) pushInterventionDictionaryEntries(dictionaryName string, body []byte) (*responsePushInterventionDictionaryEntries, error) {
	result := new(responsePushInterventionDictionaryEntries)
	err := c.requestManagementAPI(requests.POST,
		fmt.Sprintf(apiPushInterventionDictionaryEntriesTpl, dictionaryName),
		nil, body,
		result)

	return result, err
}

// pushToUserAnalyzer 变更自定义分析器中的单个词条
func (c *Client) pushToUserAnalyzer(cmd, analyzerName, key, value string) (*responsePushUserAnalyzerEntries, error) {
	return c.pushUserAnalyzerEntries(analyzerName, pushUserAnalyzerEntriesBody{
		Entries: []pushUserAnalyzerEntryItem{
			{Cmd: cmd, Key: key, Value: value},
		},
	})
}

// 变更自定义分析器词典中的词条
// 参考：https://next.api.aliyun.com/document/OpenSearch/2017-12-25/PushUserAnalyzerEntries
func (c *Client) pushUserAnalyzerEntries(analyzerName string, body pushUserAnalyzerEntriesBody) (*responsePushUserAnalyzerEntries, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	result := new(responsePushUserAnalyzerEntries)
	err = c.requestManagementAPI(requests.POST,
		fmt.Sprintf(apiPushUserAnalyzerEntriesTpl, analyzerName),
		nil, bodyBytes,
		result)

	return result, err
}

func (c *Client) requestManagementAPI(method string, api string, queryParams map[string]string, body []byte, result managementAPIResponseInterface) error {
	req := requests.NewCommonRequest()
	req.Version = managementAPIVersion
	req.Scheme = c.Management.Endpoint.Scheme
	req.Domain = c.Management.Endpoint.Host
	req.Method = method
	req.PathPattern = api
	req.Headers["Content-Type"] = "application/json"

	req.QueryParams = queryParams
	req.Content = body

	resp, err := c.client.ProcessCommonRequest(req)
	if err != nil {
		return err
	}

	err = json.Unmarshal(resp.GetHttpContentBytes(), &result)
	if err != nil {
		return err
	}

	if resp.GetHttpStatus() != http.StatusOK {
		return &serviceutil.APIError{
			Status:  http.StatusInternalServerError,
			Message: fmt.Sprintf("management api request error: http_code[%d], request_id[%s], content[%s]", resp.GetHttpStatus(), result.GetRequestID(), resp.GetHttpContentString()),
		}
	}

	return nil
}
