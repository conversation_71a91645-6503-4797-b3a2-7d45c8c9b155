package search

import (
	"testing"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestManagementTagKey(t *testing.T) {
	t.Run("managementAPIConfig", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.YAML)
		kc.Check(managementAPIConfig{}, "endpoint", "suggest_denylist", "synonym", "user_analyzer")
	})

	t.Run("pushToSynonymBodyItem", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.JSON)
		kc.Check(pushToSynonymBodyItem{}, "alias", "antiAlias")
	})

	t.Run("pushInterventionDictionaryEntryBodyItem", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.JSON)
		kc.Check(pushInterventionDictionaryEntryBodyItem{}, "cmd", "word")
	})

	t.Run("pushUserAnalyzerEntriesBody", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.JSON)
		kc.Check(pushUserAnalyzerEntriesBody{}, "entries")
	})

	t.Run("pushUserAnalyzerEntryItem", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.JSON)
		kc.Check(pushUserAnalyzerEntryItem{}, "cmd", "key", "value", "splitEnabled")
	})

	t.Run("managementAPIResponse", func(t *testing.T) {
		kc := tutil.NewKeyChecker(t, tutil.JSON)
		kc.Check(managementAPIResponse{}, "requestId")
	})
}

func TestClientRequestManagementAPI(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	result := new(testManagementAPIResp)
	err := c.requestManagementAPI(requests.POST, "/dev", nil, nil, result)
	require.NoError(err)
	assert.Equal("bar", result.Foo)
}

func TestClientPushInterventionDictionaryEntriesOrUserAnalyzer(t *testing.T) {
	t.Run("pushInterventionDictionaryEntries", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushInterventionDictionaryEntries(testDictionaryNameSuggestDenyList, []byte(`{"cmd":"add","word":"abc"}`))
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-11111111", resp.RequestID)
	})

	t.Run("pushToSuggestDenyList", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushToSuggestDenyList(testDictionaryNameSuggestDenyList, []pushInterventionDictionaryEntryBodyItem{
			{
				Cmd:  ManagementCmdAdd,
				Word: "asmr",
			},
		})
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-11111111", resp.RequestID)
	})

	t.Run("pushToSynonym", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushToSynonym(testDictionaryNameSynonym, []pushToSynonymBodyItem{
			{
				pushInterventionDictionaryEntryBodyItem: pushInterventionDictionaryEntryBodyItem{
					Cmd:  ManagementCmdDelete,
					Word: "apple",
				},
				Alias:     []string{"iphone"},
				AntiAlias: make([]string, 0),
			},
		})
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-22222222", resp.RequestID)
	})

	t.Run("pushItemToSuggestDenyList", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushItemToSuggestDenyList(ManagementCmdAdd, testDictionaryNameSuggestDenyList, "asmr")
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-11111111", resp.RequestID)
	})

	t.Run("pushItemToSynonym", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushItemToSynonym(ManagementCmdDelete, testDictionaryNameSynonym, "apple", []string{"iphone"})
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-22222222", resp.RequestID)
	})

	t.Run("pushToUserAnalyzer", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushToUserAnalyzer(ManagementCmdAdd, testUserAnalyzerName, "asmr", "asmr")
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-33333333", resp.RequestID)
	})

	t.Run("pushUserAnalyzerEntries", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		resp, err := c.pushUserAnalyzerEntries(testUserAnalyzerName, pushUserAnalyzerEntriesBody{
			Entries: []pushUserAnalyzerEntryItem{
				{Cmd: ManagementCmdDelete, Key: "asmr", Value: "asmr"},
			},
		})
		require.NoError(err)
		require.NotNil(resp)
		assert.Equal("32E0AD4F-8B17-5E3E-BE28-33333333", resp.RequestID)
	})
}
