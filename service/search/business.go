package search

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"

	uuid "github.com/satori/go.uuid"

	"github.com/MiaoSiLa/missevan-go/util"
)

// Apps Open Search Apps
type Apps struct {
	Sound            AppParam `yaml:"sound"`
	User             AppParam `yaml:"user"`
	Album            AppParam `yaml:"album"`
	Seiy             AppParam `yaml:"seiy"`
	Drama            AppParam `yaml:"drama"`
	Live             AppParam `yaml:"live"`
	Special          AppParam `yaml:"special"`
	Channel          AppParam `yaml:"channel"`
	Danmaku          AppParam `yaml:"danmaku"`
	SpecialTopicCard AppParam `yaml:"special_topic_card"`
}

// AppParam app 参数
type AppParam struct {
	Name    string      `yaml:"name"`
	Sort    []string    `yaml:"sort"`
	Indexes []IndexItem `yaml:"indexes"`
}

// SortName 获取对应 index 的排序
func (appParam AppParam) SortName(index int) (string, bool) {
	if index > len(appParam.Sort)-1 || index < 0 {
		return "", false
	}
	return appParam.Sort[index], true
}

// 搜索类型
const (
	TypeSound = iota
	TypeUser
	TypeAlbum
	_ // 综合
	TypeSeiy
	TypeDrama
	TypeLive
	TypeSpecial
	TypeChannel
	TypeDanmaku
	TypeSpecialTopicCard
)

// 搜索场景
const (
	ScenarioMainSearch         = iota // 主搜（首页、发现页）
	ScenarioPersonSearch              // 个人搜索（个人主页音频）
	ScenarioBackendSearch             // 后台搜索（评论搜索后台）
	ScenarioOrganizationSearch        // 社团搜索（社团剧集搜索页）
)

// 排序类型
const (
	SortDefault    = iota // 默认排序
	SortView              // 按播放量排序
	SortUpdateTime        // 按更新时间排序
)

// SpecialFirstAndAfterPageSizeDiff 特型搜索第一页和后续页的 pageSize 差值（第一页 3 个，第二页 5 个）
const SpecialFirstAndAfterPageSizeDiff = 2

// special_search_items 搜索类型
const (
	SpecialDefault = iota
	SpecialTopicCard
	SpecialUpCard
	SpecialGameCard
)

// StatusSuccess 搜索成功状态值
const StatusSuccess = "OK"

// MaxSearchResultCount 搜索结果的最大数量
const MaxSearchResultCount = 5000

// IsInsert 搜索已被人工干预
const IsInsert = 1

// IndexItem 查询项配置
type IndexItem struct {
	Key    string `yaml:"key"`    // 索引名称
	Weight int    `yaml:"weight"` // 索引权重，当 weight 不等于 0 时需参与构建默认的 query 表达式
	Int    bool   `yaml:"int"`    // 索引是否只含有数字类型字段
	Fuzzy  bool   `yaml:"fuzzy"`  // 是否是模糊索引
	Extra  bool   `yaml:"extra"`  // 是否可用于构建额外的 query 表达式
}

// useBuildQuery 是否可用于构建 query 表达式
func (i IndexItem) useBuildQuery() bool {
	return i.Weight != 0
}

// useBuildExtraQuery 是否可用于构建额外的 query 表达式
func (i IndexItem) useBuildExtraQuery() bool {
	return i.Extra
}

const (
	sdkType    = "opensearch_sdk"
	sdkVersion = "3.1.0"
)

// CommitResult is the result of the commit api
type CommitResult struct {
	Errors []struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"errors"`
	RequestID string `json:"request_id"`
	Status    string `json:"status"`
}

func createPushPath(searchAppName, behaviorCollectionName string) string {
	return fmt.Sprintf("/app-groups/%s/data-collections/%s/actions/bulk", searchAppName, behaviorCollectionName)
}

type pair struct {
	key   string
	value string
}

func canonicalizedHeaders(header http.Header) []byte {
	var h []pair
	for key, value := range header {
		if len(key) == 0 || len(value) == 0 {
			continue
		}
		k := strings.Trim(key, " \t")
		v := strings.Trim(value[0], " \t")
		if strings.HasPrefix(k, "X-Opensearch-") && len(value[0]) > 0 {
			h = append(h, pair{
				key:   k,
				value: v,
			})
		}
	}
	var canonicalized bytes.Buffer
	if len(h) == 0 {
		return canonicalized.Bytes()
	}
	sort.Slice(h, func(i, j int) bool {
		return h[i].key < h[j].key
	})
	for _, p := range h {
		canonicalized.WriteString(strings.ToLower(p.key))
		canonicalized.WriteRune(':')
		canonicalized.WriteString(p.value)
		canonicalized.WriteRune('\n')
	}
	return canonicalized.Bytes()
}

// URIEncode 转义 URI 字符
func URIEncode(URI string, encodeSlash bool) string {
	var result strings.Builder
	for i := 0; i < len(URI); i++ {
		v := URI[i]
		if (v >= 'A' && v <= 'Z') || (v >= 'a' && v <= 'z') || (v >= '0' && v <= '9') ||
			v == '_' || v == '-' || v == '~' || v == '.' {
			result.WriteByte(v)
		} else if v == '/' {
			if encodeSlash {
				result.WriteString("%2F")
			} else {
				result.WriteByte(v)
			}
		} else {
			result.WriteString("%")
			result.WriteString(strings.ToUpper(hex.EncodeToString([]byte{v})))
		}
	}
	return result.String()
}

// Params struct for search api or suggest api
type Params struct {
	SearchType       int    `json:"search_type"`
	Scenario         int    `json:"scenario"`
	UserID           int64  `json:"user_id"`
	Sensitive        int    `json:"sensitive"`
	Keyword          string `json:"keyword"`
	Sort             int    `json:"sort"`
	Page             int    `json:"page"`
	PageSize         int    `json:"page_size"`
	Cid              int    `json:"cid"`
	OrgUserIDs       []int  `json:"org_user_ids"`
	ViewUserID       int64  `json:"view_user_id"`
	Self             bool   `json:"self"`
	PayType          []int  `json:"pay_type"`
	SuggestRequestID string `json:"suggest_request_id"`
	// TODO: 这里 filter 应该有解析和字段检查，需要重新实现一下
	ExtraFilters string `json:"extra_filters"`
	ExtraQueries string `json:"extra_queries"` // 额外的查询条件，JSON format

	IsKeywordNumber bool   `json:"-"`
	AppName         string `json:"-"`
	// TODO: 应该改为 Filter 的复数形式
	Filter             []string               `json:"-"`
	ExtraQueryIndexMap map[string]interface{} `json:"-"`
	SecondRankName     string                 `json:"-"`
	ABTest             string                 `json:"-"`
	AppParam           AppParam               `json:"-"`

	AdapterKeywordTargetID   *int64       `json:"-"`
	AllFixedTargetIDs        []string     `json:"-"` // 所有有效关联 IDs
	CurrentActiveFixedParams []FixedParam `json:"-"` // 当前页有效的配置
	PreFixedNum              int          `json:"-"` // 当前页之前需要过滤的数量
	SkipOffset               int          `json:"-"`
}

// FixedParam 固定搜索结果所处位置的必要参数
type FixedParam struct {
	TargetIndex int   `json:"target_index"`
	TargetID    int64 `json:"target_id"`
}

// ResponseError is the error structure in the response of search/suggest api
type ResponseError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Response response structure for search api
type Response struct {
	Status    string          `json:"status"`
	Errors    []ResponseError `json:"errors"`
	RequestID string          `json:"request_id"`
	Result    struct {
		// total、viewtotal、num 区别：https://help.aliyun.com/document_detail/57155.html
		// total 为一次查询（不考虑 config 子句）引擎中符合条件的结果数（在结果数较多情况下，该值会做优化），
		// 但考虑到性能及相关性，引擎最多会返回 viewtotal 个结果。
		// 如果需要翻页的话，要求 start+hit 必需要小于 viewtotal，total 一般用来做展示。
		// num 为本次查询请求（受限于 config 子句的 start 及 hit）实际返回的条目，不会超过 hit 值。
		Total     int               `json:"total"`
		Num       int               `json:"num"`
		ViewTotal int               `json:"viewtotal"`
		Items     []json.RawMessage `json:"items"`
	} `json:"result"`
	OpsRequestMisc string `json:"ops_request_misc"`
}

// SuggestItem is the structure of suggestion
type SuggestItem struct {
	Suggestion string `json:"suggestion"`
}

// SuggestResponse response structure for suggest api
type SuggestResponse struct {
	Errors      []ResponseError `json:"errors"`
	RequestID   string          `json:"request_id"`
	SearchTime  float32         `json:"searchtime"`
	Suggestions []SuggestItem   `json:"suggestions"`
}

// 若开启了查询分析，则需要过滤关键字中的 '\'，避免 query 子句报错
// 若关键字中有单引号则需要转义单引号，避免 query 子句报错
var searchQueryReplacer = strings.NewReplacer(`\`, "", "'", `\'`, `&`, `\&`)

type buildOptions struct {
	searchKeyword          string
	indexItems             []IndexItem
	extraQueryIndexMap     map[string]interface{}
	filters                []string
	pageOffset             int
	pageSize               int
	isKeywordNumber        bool
	adapterKeywordTargetID *int64
}

func newBuildOptions(params *Params) *buildOptions {
	// params.PreFixedNum 为当前页之前（不包括当前页）配置固定内容的内容数，params.SkipOffset 为搜索时额外的 offset
	pageOffset := params.PageSize*(params.Page-1) - params.PreFixedNum + params.SkipOffset
	pageSize := params.PageSize
	currentActiveFixedParamsLength := len(params.CurrentActiveFixedParams)
	if currentActiveFixedParamsLength != 0 {
		// 开放搜索数据数量等于业务端请求数量减掉当前页插入的数量
		if pageSize > currentActiveFixedParamsLength {
			pageSize -= currentActiveFixedParamsLength
		} else {
			// 即使不需要返回数据，也需要获取命中文档的总数
			pageSize = 0
		}
	}

	return &buildOptions{
		searchKeyword:          params.Keyword,
		indexItems:             params.AppParam.Indexes,
		extraQueryIndexMap:     params.ExtraQueryIndexMap,
		filters:                params.Filter,
		pageOffset:             pageOffset,
		pageSize:               pageSize,
		isKeywordNumber:        params.IsKeywordNumber,
		adapterKeywordTargetID: params.AdapterKeywordTargetID,
	}
}

// buildSearchQueryWithOptions 生成查询子句
// 各个查询子句之间必须要用 && 进行拼接
// https://help.aliyun.com/document_detail/54237.html
func buildSearchQueryWithOptions(opts *buildOptions) string {
	queryClauses := make([]string, 0, 4)

	queryClauses = append(queryClauses, opts.buildConfig())
	if opts.isKeywordNumber {
		queryClauses = append(queryClauses, opts.buildKeywordNumberQuery()...)
	} else {
		queryClauses = append(queryClauses, opts.buildQuery()...)
	}
	if len(opts.filters) > 0 {
		queryClauses = append(queryClauses, opts.buildFilter())
	}

	return strings.Join(queryClauses, "&&")
}

// buildConfig 生成配置语句
func (opts *buildOptions) buildConfig() string {
	return fmt.Sprintf("config=start:%d,hit:%d,format:json,rerank_size:200", opts.pageOffset, opts.pageSize)
}

// buildKeywordNumberQuery 拼接搜索内容数字类型的搜索语句
func (opts *buildOptions) buildKeywordNumberQuery() []string {
	var (
		queries = make([]string, 0, len(opts.indexItems))
		kvPairs string
	)
	for _, value := range opts.indexItems {
		if !value.useBuildQuery() {
			continue
		}
		queries = append(queries, value.query(opts.searchKeyword, opts.adapterKeywordTargetID))
		// kvparis 总是用 id 不需要根据具体的 Key 名一致，也不用和 IsInt 的 key 数量一致
		if value.Int && kvPairs == "" {
			kvPairs = fmt.Sprintf("kvpairs=id:%s", opts.searchKeyword)
			if opts.adapterKeywordTargetID != nil {
				kvPairs = kvPairs + fmt.Sprintf(",target_id:%d", *opts.adapterKeywordTargetID)
			}
		}
	}

	queryList := make([]string, 0, 2)
	queryExpr := strings.Join(queries, " OR ")
	extraQueryExpr := opts.buildExtraQuery()
	if extraQueryExpr != "" {
		queryExpr = fmt.Sprintf("(%s) AND (%s)", queryExpr, extraQueryExpr)
	}
	queryList = append(queryList, fmt.Sprintf("query=%s", queryExpr))

	if kvPairs != "" {
		queryList = append(queryList, kvPairs)
	}
	return queryList
}

// buildQuery 拼接搜索内容不是数字类型的搜索语句
func (opts *buildOptions) buildQuery() []string {
	var queries []string
	for _, value := range opts.indexItems {
		if !value.useBuildQuery() || value.Int {
			continue
		}
		queries = append(queries, value.query(opts.searchKeyword, opts.adapterKeywordTargetID))
	}
	if opts.adapterKeywordTargetID != nil && *opts.adapterKeywordTargetID != 0 {
		queries = append(queries, fmt.Sprintf("%s:'%d'^%d", "id", *opts.adapterKeywordTargetID, 99))
	}

	var kvPairs string
	if opts.adapterKeywordTargetID != nil {
		kvPairs = fmt.Sprintf("kvpairs=target_id:%d", *opts.adapterKeywordTargetID)
	}

	queryList := make([]string, 0, 2)
	queryExpr := strings.Join(queries, " OR ")
	extraQueryExpr := opts.buildExtraQuery()
	if extraQueryExpr != "" {
		queryExpr = fmt.Sprintf("(%s) AND (%s)", queryExpr, extraQueryExpr)
	}
	queryList = append(queryList, fmt.Sprintf("query=%s", queryExpr))
	if kvPairs != "" {
		queryList = append(queryList, kvPairs)
	}

	return queryList
}

// buildExtraQuery 构建额外的查询条件，索引之间使用 AND 逻辑运算符连接
func (opts *buildOptions) buildExtraQuery() string {
	var extraQueryExpr strings.Builder
	for k, v := range opts.extraQueryIndexMap {
		if extraQueryExpr.Len() > 0 {
			extraQueryExpr.WriteString(" AND ")
		}
		extraQueryExpr.WriteString(k)
		extraQueryExpr.WriteString(":'")
		switch vt := v.(type) {
		case float64: // float64, for JSON numbers
			extraQueryExpr.WriteString(strconv.FormatFloat(vt, 'f', -1, 64))
		case string: // string, for JSON strings
			extraQueryExpr.WriteString(searchQueryReplacer.Replace(vt))
		default:
			panic(fmt.Sprintf("error index value type: %T", vt))
		}
		extraQueryExpr.WriteByte('\'')
	}
	return extraQueryExpr.String()
}

// 构建 filter 子句，属性字段之间使用 AND 逻辑运算符连接
func (opts *buildOptions) buildFilter() string {
	return "filter=" + strings.Join(opts.filters, " AND ")
}

/*
 * 模糊索引使用的字段类型为 SHORT_TEXT，该类型会过滤掉所有的标点符号（不包含空格），参考文档：https://help.aliyun.com/document_detail/179439.html
 * 过滤标点符号的特性会导致用户搜索含有标点符号的关键字时无法召回期望的文档，此处需要先给模糊索引的关键词过滤标点符号（不影响模糊索引的搜索结果）
 * 该正则表达式参考阿里云的实现：String().replaceAll("[\\pP\\p{Punct}]","")，即 \pP 和 \p{Punct}
 * \pP 表示 Unicode 符号 P 类型，Unicode 通用类型文档：https://www.unicode.org/reports/tr44/#Property_Values
 * Unicode 完整列表可见：https://www.unicode.org/Public/14.0.0/ucd/UnicodeData.txt
 * \p{Punct} Posix 语法中表示 ASCII 符号，参考可见：https://www.ibm.com/docs/ro/db2/11.1?topic=editor-regular-expressions-syntax-reference
 */
var regPunct = regexp.MustCompile("\\p{P}|[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~]")

func filterPunctuation(keyword string) string {
	return strings.TrimSpace(regPunct.ReplaceAllString(keyword, ""))
}

// query 查询语句
func (i IndexItem) query(key string, adapterKeywordTargetID *int64) string {
	if i.Fuzzy {
		// 模糊分析的索引字段使用双引号包裹时可以告知分析器：期望搜索内容在检索文档中是相连的
		// 模糊搜索文档：https://help.aliyun.com/document_detail/179439.html
		return fmt.Sprintf(`%s:"%s"^%d`, i.Key, filterPunctuation(key), i.Weight)
	}
	if i.Int && adapterKeywordTargetID != nil && *adapterKeywordTargetID != 0 {
		return fmt.Sprintf("%s:'%s'|'%d'^%d", i.Key, key, *adapterKeywordTargetID, i.Weight)
	}

	return fmt.Sprintf("%s:'%s'^%d", i.Key, key, i.Weight)
}

// 对 query 中的特殊字符进行替换处理
// https://help.aliyun.com/document_detail/54237.html
// https://github.com/denverdino/aliyungo/blob/master/util/signature.go
var rawQueryReplacer = strings.NewReplacer("+", "%20", "&&", "%26%26", "*", "%2A", "%7E", "~")

// getRawQuery get raw query of GET request
func (c *Client) getRawQuery(queryParams map[string]string) string {
	q := url.Values{}
	for k, v := range queryParams {
		q.Add(k, v)
	}

	return rawQueryReplacer.Replace(q.Encode())
}

// setHeader set header of request
// https://help.aliyun.com/document_detail/54237.html
func (c *Client) setHeader(req *http.Request, uri string) {
	// if req.ContentLength > 0 {
	// 	TODO: 处理 body 的 MD5
	// 	var md5Str string
	// 	body, _ := ioutil.ReadAll(req.Body)
	// 	md5sum := md5.Sum(body)
	// 	md5Str = hex.EncodeToString(md5sum[:])
	// }

	// req.Header.Set("Content-MD5", "")
	req.Header.Set("Content-Type", "application/json")
	now := util.TimeNow()
	req.Header.Set("Date", now.UTC().Format("2006-01-02T15:04:05Z"))
	req.Header.Set("X-Opensearch-Nonce", uuid.NewV4().String())
	auth := c.generateAuthorization(req, uri)
	req.Header.Set("Authorization", auth)
}

// 生成签名字符串
// generateAuthorization generate authorization of header
// https://help.aliyun.com/document_detail/54237.html
func (c *Client) generateAuthorization(req *http.Request, uri string) string {
	var canonicalized bytes.Buffer
	canonicalized.WriteString(req.Method)
	canonicalized.WriteRune('\n')
	canonicalized.WriteString(req.Header.Get("Content-MD5"))
	canonicalized.WriteRune('\n')
	canonicalized.WriteString(req.Header.Get("Content-Type"))
	canonicalized.WriteRune('\n')
	canonicalized.WriteString(req.Header.Get("Date"))
	canonicalized.WriteRune('\n')
	canonicalized.Write(canonicalizedHeaders(req.Header))
	canonicalized.WriteString(uri + "?" + req.URL.RawQuery)

	h := hmac.New(sha1.New, c.accessKeySecret)
	_, _ = h.Write(canonicalized.Bytes())
	sig := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return fmt.Sprintf("OPENSEARCH %s:%s", c.accessKeyID, sig)
}

// hasExtraQueryKey 索引项配置中是否有索引 key 可以用于构额外查询语句
func hasExtraQueryKey(key string, indexItems []IndexItem) bool {
	for _, indexItem := range indexItems {
		if indexItem.useBuildExtraQuery() && key == indexItem.Key {
			return true
		}
	}
	return false
}

// SupportExtraIndexMap 获取 indexJSON 中支持的 index map
func SupportExtraIndexMap(indexJSON string, items []IndexItem) (map[string]interface{}, error) {
	var indexMap map[string]interface{}
	err := json.Unmarshal([]byte(indexJSON), &indexMap)
	if err != nil {
		return nil, err
	}
	supportIndexMap := make(map[string]interface{}, len(indexMap))
	for k, v := range indexMap {
		if !hasExtraQueryKey(k, items) {
			return nil, fmt.Errorf("unsupported index: %s", k)
		}
		// 目前仅支持数字和字符串类型的 value
		switch vt := v.(type) {
		case float64, string: // float64, for JSON numbers; string, for JSON strings
			supportIndexMap[k] = v
		default:
			return nil, fmt.Errorf("unsupported index value type: %T, index: %s", vt, k)
		}
	}
	return supportIndexMap, nil
}
