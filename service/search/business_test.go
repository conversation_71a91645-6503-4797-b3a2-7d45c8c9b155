package search

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestBusinessTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Apps{}, "sound", "user", "album", "seiy", "drama", "live", "special", "channel", "danmaku", "special_topic_card")
	kc.Check(IndexItem{}, "key", "weight", "int", "fuzzy", "extra")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Params{}, "search_type", "scenario", "user_id", "sensitive", "keyword", "sort", "page", "page_size", "cid",
		"org_user_ids", "view_user_id", "self", "pay_type", "suggest_request_id", "extra_filters", "extra_queries")
}

func TestSortName(t *testing.T) {
	assert := assert.New(t)

	var appParam AppParam
	assert.NotPanics(func() {
		sortConfig, ok := appParam.SortName(-1)
		assert.Empty(sortConfig)
		assert.False(ok)
	})

	sortConfig, ok := appParam.SortName(0)
	assert.Empty(sortConfig)
	assert.False(ok)

	sortConfig, ok = appParam.SortName(1)
	assert.Empty(sortConfig)
	assert.False(ok)

	appParam.Sort = []string{
		"drama",
	}
	sortConfig, ok = appParam.SortName(0)
	assert.Equal("drama", sortConfig)
	assert.True(ok)
}

func TestIndexItem_useBuildQuery(t *testing.T) {
	assert := assert.New(t)

	item := IndexItem{Weight: 99}
	assert.True(item.useBuildQuery())

	item = IndexItem{Weight: 0}
	assert.False(item.useBuildQuery())
}

func TestIndexItem_useBuildExtraQuery(t *testing.T) {
	assert := assert.New(t)

	item := IndexItem{Extra: true}
	assert.True(item.useBuildExtraQuery())

	item = IndexItem{}
	assert.False(item.useBuildExtraQuery())
}

func TestURIEncode(t *testing.T) {
	assert := assert.New(t)
	result := URIEncode("/Aa0.~-_ +", false)
	assert.Equal("/Aa0.~-_%20%2B", result)
}

func TestGenerateAuthorization(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	uri := fmt.Sprintf("/v3/openapi/apps/%s/suggest/%s/search", c.Apps.Sound.Name, c.Apps.Sound.Name)
	req, err := http.NewRequest(http.MethodGet, uri, nil)
	require.NoError(err)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Date", "2006-01-02T15:04:05Z")
	req.Header.Set("X-Opensearch-Nonce", "1234567890123456")

	auth := c.generateAuthorization(req, uri)

	assert.Equal(fmt.Sprintf("OPENSEARCH %s:%s", c.accessKeyID, "k6RFDW2m786jUt72QzhwZQotpyk="), auth)
}

func TestFilterPunctuation(t *testing.T) {
	assert := assert.New(t)

	// ASCII 标点符号
	assert.Equal("", filterPunctuation("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"))
	// CJK Unicode 标点符号
	/* 用例来源：
	 * https://www.compart.com/en/unicode/category
	 * https://en.wikipedia.org/wiki/CJK_Symbols_and_Punctuation
	 */
	assert.Equal("", filterPunctuation("_︳＿〜«»‘’“”‹›〈〉《》「」『』【】？！，。·"))
	// 阿里云测试用例
	assert.Equal("ss1如果自己知道什么是苦Z那么一定告诉别人什么是甜", filterPunctuation("ss&*(,.~1如果@&(^-自己!!知道`什`么#是$苦%……Z，&那*()么一-=定——+告诉::;\"'/?.,><[]{}\\||别人什么是甜。"))
}

func TestSetHeader(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	uri := fmt.Sprintf("/v3/openapi/apps/%s/suggest/%s/search", c.Apps.Sound.Name, c.Apps.Sound.Name)
	req, err := http.NewRequest(http.MethodGet, uri, nil)
	require.NoError(err)
	c.setHeader(req, uri)

	assert.Equal("application/json", req.Header.Get("Content-Type"))
	assert.NotEmpty(req.Header.Get("Date"))
	assert.NotEmpty(req.Header.Get("Authorization"))
	assert.NotEmpty(req.Header.Get("X-Opensearch-Nonce"))
}

func TestGetRawQuery(t *testing.T) {
	assert := assert.New(t)

	rawQuery := c.getRawQuery(map[string]string{
		"query": "阿",
		"hit":   "10",
	})

	assert.Equal("hit=10&query=%E9%98%BF", rawQuery)
}

func TestNewBuildOptions(t *testing.T) {
	assert := assert.New(t)

	params := &Params{
		Page:        1,
		PageSize:    2,
		SkipOffset:  1,
		PreFixedNum: 0,
		CurrentActiveFixedParams: []FixedParam{
			{
				TargetIndex: 0,
				TargetID:    2233,
			},
		},
	}
	options := newBuildOptions(params)
	assert.Equal(1, options.pageSize)
	assert.Equal(1, options.pageOffset)

	params.CurrentActiveFixedParams = append(params.CurrentActiveFixedParams, FixedParam{
		TargetIndex: 1,
		TargetID:    1111,
	})
	options = newBuildOptions(params)
	assert.Equal(0, options.pageSize)
	assert.Equal(1, options.pageOffset)
}

func TestBuildSearchQueryWithOptions(t *testing.T) {
	assert := assert.New(t)

	queryItems := []IndexItem{{Key: "context", Weight: 10}, {Key: "id", Weight: 99, Int: true}}
	opts := &buildOptions{
		searchKeyword:          "阿",
		indexItems:             queryItems,
		extraQueryIndexMap:     nil,
		filters:                []string{"checked = 1"},
		pageOffset:             0,
		pageSize:               20,
		isKeywordNumber:        false,
		adapterKeywordTargetID: nil,
	}

	searchQueryStr := buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=context:'阿'^10&&filter=checked = 1`, searchQueryStr)

	opts.indexItems = append(opts.indexItems, IndexItem{Key: "title", Weight: 100})
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=context:'阿'^10 OR title:'阿'^100&&filter=checked = 1`, searchQueryStr)

	opts.searchKeyword = "9888"
	opts.isKeywordNumber = true
	opts.indexItems = append(opts.indexItems, IndexItem{Key: "default", Weight: 99})
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=context:'9888'^10 OR id:'9888'^99 OR title:'9888'^100 OR default:'9888'^99&&kvpairs=id:9888&&filter=checked = 1`, searchQueryStr)

	opts.indexItems = append(opts.indexItems, IndexItem{Key: "room_id", Weight: 99, Int: true})
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=context:'9888'^10 OR id:'9888'^99 OR title:'9888'^100 OR default:'9888'^99 OR room_id:'9888'^99&&kvpairs=id:9888&&filter=checked = 1`, searchQueryStr)

	opts.adapterKeywordTargetID = util.NewInt64(1)
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=context:'9888'^10 OR id:'9888'|'1'^99 OR title:'9888'^100 OR default:'9888'^99 OR room_id:'9888'|'1'^99&&kvpairs=id:9888,target_id:1&&filter=checked = 1`, searchQueryStr)

	opts.searchKeyword = "ceshisousuo"
	opts.isKeywordNumber = false
	opts.indexItems = []IndexItem{{Key: "default", Weight: 10}, {Key: "default_pinyin", Weight: 99, Fuzzy: true}}
	opts.adapterKeywordTargetID = nil
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=default:'ceshisousuo'^10 OR default_pinyin:"ceshisousuo"^99&&filter=checked = 1`, searchQueryStr)

	opts.indexItems = []IndexItem{{Key: "default", Weight: 10}, {Key: "id", Weight: 99, Int: true}}
	opts.adapterKeywordTargetID = util.NewInt64(0)
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=default:'ceshisousuo'^10&&kvpairs=target_id:0&&filter=checked = 1`, searchQueryStr)

	opts.extraQueryIndexMap = map[string]interface{}{"sound_id": "2233"}
	searchQueryStr = buildSearchQueryWithOptions(opts)
	assert.Equal(`config=start:0,hit:20,format:json,rerank_size:200&&query=(default:'ceshisousuo'^10) AND (sound_id:'2233')&&kvpairs=target_id:0&&filter=checked = 1`, searchQueryStr)
}

func TestBuildOptions_buildKeywordNumberQuery(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	queryItems := []IndexItem{{Key: "context", Weight: 10}, {Key: "id", Weight: 99, Int: true}}
	opts := &buildOptions{
		searchKeyword:   "2233",
		indexItems:      queryItems,
		isKeywordNumber: true,
	}
	queryClauses := opts.buildKeywordNumberQuery()
	require.Len(queryClauses, 2)
	assert.Equal("query=context:'2233'^10 OR id:'2233'^99", queryClauses[0])
	assert.Equal("kvpairs=id:2233", queryClauses[1])

	opts.adapterKeywordTargetID = util.NewInt64(66)
	queryClauses = opts.buildKeywordNumberQuery()
	require.GreaterOrEqual(len(queryClauses), 2)
	assert.Equal("kvpairs=id:2233,target_id:66", queryClauses[1])

	opts.extraQueryIndexMap = map[string]interface{}{"sound_id": "99"}
	queryClauses = opts.buildKeywordNumberQuery()
	require.GreaterOrEqual(len(queryClauses), 1)
	assert.Equal("query=(context:'2233'^10 OR id:'2233'|'66'^99) AND (sound_id:'99')", queryClauses[0])
}

func TestBuildOptions_buildQuery(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	queryItems := []IndexItem{{Key: "context", Weight: 10}, {Key: "id", Weight: 99, Int: true}}
	opts := &buildOptions{
		searchKeyword: "啊",
		indexItems:    queryItems,
	}
	queryClauses := opts.buildQuery()
	require.Len(queryClauses, 1)
	assert.Equal("query=context:'啊'^10", queryClauses[0])

	opts.adapterKeywordTargetID = util.NewInt64(66)
	queryClauses = opts.buildQuery()
	require.GreaterOrEqual(len(queryClauses), 2)
	assert.Equal("kvpairs=target_id:66", queryClauses[1])

	opts.extraQueryIndexMap = map[string]interface{}{"sound_id": "99"}
	queryClauses = opts.buildQuery()
	require.GreaterOrEqual(len(queryClauses), 1)
	assert.Equal("query=(context:'啊'^10 OR id:'66'^99) AND (sound_id:'99')", queryClauses[0])
}

func TestBuildOptions_buildExtraQuery(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		extraQueries1 = `{"sound_id":"2233", "id": 45758424}`
		m1            map[string]interface{}
	)
	require.NoError(json.Unmarshal([]byte(extraQueries1), &m1))
	opts := buildOptions{
		extraQueryIndexMap: m1,
	}
	extraQueryExpr := opts.buildExtraQuery()
	assert.True(extraQueryExpr == `sound_id:'2233' AND id:'45758424'` ||
		extraQueryExpr == `id:'45758424' AND sound_id:'2233'`)

	// 测试 value 含有特殊字符被转义的情况
	var (
		extraQueries2 = `{"text":"'测试'"}`
		m2            map[string]interface{}
	)
	require.NoError(json.Unmarshal([]byte(extraQueries2), &m2))
	opts.extraQueryIndexMap = m2
	extraQueryExpr = opts.buildExtraQuery()
	assert.Equal(`text:'\'测试\''`, extraQueryExpr)

	m2["text"] = []string{"测试", "ce shi"}
	assert.Panics(func() { opts.buildExtraQuery() }, "error index value type: []interface {}")
}

func TestBuildOptions_buildFilter(t *testing.T) {
	assert := assert.New(t)

	opts := &buildOptions{
		filters: []string{"checked = 1", "police = 0"},
	}
	filterExpr := opts.buildFilter()
	assert.Equal("filter=checked = 1 AND police = 0", filterExpr)
}

func TestHasExtraQueryKey(t *testing.T) {
	assert := assert.New(t)

	items := []IndexItem{
		{Key: "sound_id", Extra: true},
		{Key: "id", Extra: true},
	}
	ok := hasExtraQueryKey("sound_id", items)
	assert.True(ok)

	ok = hasExtraQueryKey("dummy_key", items)
	assert.False(ok)
}

func TestSupportExtraIndexMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		indexJSON = `{"sound_id": 2233, "id": 2}`
		items     = []IndexItem{
			{Key: "sound_id", Extra: true},
			{Key: "id", Extra: true},
		}
	)
	supportExtraIndexMap, err := SupportExtraIndexMap(indexJSON, items)
	require.NoError(err)
	assert.Contains(supportExtraIndexMap, "sound_id")
	assert.Contains(supportExtraIndexMap, "id")

	indexJSON = `{"id_ccc": 2233}`
	_, err = SupportExtraIndexMap(indexJSON, items)
	assert.EqualError(err, "unsupported index: id_ccc")

	indexJSON = `{"id": [1,2,3]}`
	_, err = SupportExtraIndexMap(indexJSON, items)
	assert.EqualError(err, "unsupported index value type: []interface {}, index: id")
}
