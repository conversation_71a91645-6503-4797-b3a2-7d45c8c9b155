// +build !release

package databus

// TestNew new a databus for unit test
func TestNew(c *Config) *Databus {
	if c.Buffer == 0 {
		c.Buffer = 1024
	}
	if c.PoolSize == 0 {
		c.PoolSize = 10
	}

	d := &Databus{
		conf:   c,
		msgs:   make(chan *Message, c<PERSON>Buffer),
		marked: make(map[int32]int64),
		closed: _open,
	}

	// if c.Action == _actionSub || c.Action == _actionAll {
	// 	// consumer
	// }

	if c.Action == _actionPub || c.Action == _actionAll {
		// producer
		d.debugPubMsgs = make(chan *Message, c.Buffer)
	}

	return d
}

// TestPush push message for unit test
func (d *Databus) TestPush(m *Message) {
	d.msgs <- m
}

// DebugPubMsgs get pub messages for unit test
func (d *Databus) DebugPubMsgs() <-chan *Message {
	return d.debugPubMsgs
}

// ClearDebugPubMsgs clear pub messages for unit test
func (d *Databus) ClearDebugPubMsgs() {
	d.debugPubMsgs = make(chan *Message, d.conf.Buffer)
}
