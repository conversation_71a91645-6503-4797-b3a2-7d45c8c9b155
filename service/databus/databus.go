package databus

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	redis "github.com/go-redis/redis/v7"
	"github.com/gogo/protobuf/proto"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util/netutil"
)

const (
	// maxRetryCount databus 最大重试次数
	maxRetryCount = 2
)

var (
	_subTimeout = time.Second * 35
	bk          = netutil.DefaultBackoffConfig
)

// Config databus config.
type Config struct {
	Key      string `yaml:"key"`
	Secret   string `yaml:"secret"`
	Group    string `yaml:"group"`
	Topic    string `yaml:"topic"`
	Action   string `yaml:"action"` // shoule be "pub" or "sub" or "pubsub"
	Buffer   int    `yaml:"buffer"`
	Addr     string `yaml:"addr"`
	Auth     string `yaml:"auth"`
	PoolSize int    `yaml:"pool_size"` // pub pool size, default: 10
	// Degrade      *coin.Config
}

const (
	_actionSub    = "sub"
	_actionPub    = "pub"
	_actionAll    = "pubsub"
	_cmdPub       = "SET"
	_cmdPubHeader = "HSET"
	_cmdSub       = "MGET"
	_cmdSet       = "SET"
	_authFormat   = "%s:%s@%s/topic=%s&role=%s"
	_open         = int32(0)
	_closed       = int32(1)
)

// Message Data.
type Message struct {
	Key       string            `json:"key"`
	Value     json.RawMessage   `json:"value"`
	Topic     string            `json:"topic"`
	Partition int32             `json:"partition"`
	Offset    int64             `json:"offset"`
	Timestamp int64             `json:"timestamp"`
	Metadata  map[string]string `json:"metadata"`
	d         *Databus
}

// Commit ack message.
func (m *Message) Commit() {
	m.d.lock.Lock()
	if m.Offset >= m.d.marked[m.Partition] {
		m.d.marked[m.Partition] = m.Offset
	}
	m.d.lock.Unlock()
}

// Databus databus struct.
type Databus struct {
	conf *Config

	s *redis.Client
	p *redis.Client

	msgs   chan *Message
	lock   sync.RWMutex
	marked map[int32]int64

	closed int32

	// only for unit test
	debugPubMsgs chan *Message
}

// New new a databus.
func New(c *Config) *Databus {
	if c.Buffer == 0 {
		c.Buffer = 1024
	}
	if c.PoolSize == 0 {
		c.PoolSize = 10
	}
	d := &Databus{
		conf:   c,
		msgs:   make(chan *Message, c.Buffer),
		marked: make(map[int32]int64),
		closed: _open,
	}

	if c.Action == _actionSub || c.Action == _actionAll {
		pass := c.Auth
		if pass == "" {
			pass = fmt.Sprintf(_authFormat, c.Key, c.Secret, c.Group, c.Topic, _actionSub)
		}
		d.s = redis.NewClient(&redis.Options{
			Addr:         c.Addr,
			Password:     pass, // auth password set
			DB:           0,    // use default DB
			DialTimeout:  time.Second * 2,
			IdleTimeout:  time.Second * 10,
			ReadTimeout:  _subTimeout,
			WriteTimeout: time.Second * 1,
			PoolSize:     1,
		})
		go d.subproc()
	}

	if c.Action == _actionPub || c.Action == _actionAll {
		pass := c.Auth
		if pass == "" {
			pass = fmt.Sprintf(_authFormat, c.Key, c.Secret, c.Group, c.Topic, _actionPub)
		}
		// new pool
		d.p = redis.NewClient(&redis.Options{
			Addr:         c.Addr,
			Password:     pass, // auth password set
			DB:           0,    // use default DB
			DialTimeout:  time.Second * 2,
			IdleTimeout:  time.Second * 10,
			ReadTimeout:  time.Second * 5,
			WriteTimeout: time.Second * 1,
			PoolSize:     c.PoolSize,
		})
	}

	// TODO: degrade

	return d
}

func (d *Databus) subproc() {
	var (
		err      error
		res      []interface{}
		retry    int
		c        *redis.Conn
		commited = make(map[int32]int64)
		commit   = make(map[int32]int64)
	)
	for {
		if atomic.LoadInt32(&d.closed) == _closed {
			if c != nil {
				_ = c.Close()
				c = nil
			}
			close(d.msgs)
			return
		}
		if err != nil {
			time.Sleep(bk.Backoff(retry))
			retry++
		} else {
			retry = 0
		}

		if c == nil {
			d.lock.Lock()
			d.marked = make(map[int32]int64) // fix migrated offsets
			d.lock.Unlock()
			c = d.s.Conn()
			err = nil
		}
		d.lock.RLock()
		for k, v := range d.marked {
			if vv, ok := commited[k]; vv != v || !ok {
				commit[k] = v
			}
		}
		d.lock.RUnlock()
		// TODO: pipeline commit offset
		for k, v := range commit {
			if err = c.Set(strconv.FormatInt(int64(k), 10), v, 0).Err(); err != nil {
				_ = c.Close()
				c = nil
				logger.Errorf("group(%s) conn.Do(SET,%d,%d) commit error(%v)", d.conf.Group, k, v, err)
				break
			}
			delete(commit, k)
			commited[k] = v
		}
		if err != nil {
			continue
		}
		if res, err = c.MGet("pb").Result(); err != nil {
			_ = c.Close()
			c = nil
			logger.Errorf("conn.Do(MGET) error(%v)", err)
			continue
		}
		for _, r := range res {
			msg := &MessagePB{}
			if err = proto.Unmarshal([]byte(r.(string)), msg); err != nil {
				logger.Errorf("json.Unmarshal(%s) error(%v)", r, err)
				continue
			}
			d.msgs <- &Message{
				Key:       msg.Key,
				Value:     msg.Value,
				Topic:     msg.Topic,
				Partition: msg.Partition,
				Offset:    msg.Offset,
				Timestamp: msg.Timestamp,
				Metadata:  msg.Metadata,
				d:         d,
			}
		}
	}
}

// Messages get message chan.
func (d *Databus) Messages() <-chan *Message {
	return d.msgs
}

// Send send message to databus.
func (d *Databus) Send(c context.Context, k string, v interface{}) (err error) {
	if d.debugPubMsgs != nil {
		m := &Message{
			Key: k,
		}
		if m.Value, err = json.Marshal(v); err != nil {
			logger.Errorf("json.Marshal(%v) error(%v)", v, err)
			return
		}
		d.debugPubMsgs <- m
		return
	}
	return d.send(c, k, v, nil)
}

// SendDelay send delay message to databus.
func (d *Databus) SendDelay(c context.Context, k string, v interface{}, deliverAt time.Time) (err error) {
	if d.debugPubMsgs != nil {
		m := &Message{
			Key: k,
		}
		if m.Value, err = json.Marshal(v); err != nil {
			logger.Errorf("json.Marshal(%v) error(%v)", v, err)
			return
		}
		d.debugPubMsgs <- m
		return
	}
	header := make(map[string]string)
	header["deliver_at"] = strconv.FormatInt(deliverAt.UnixNano()/1e6, 10)
	return d.send(c, k, v, header)
}

func (d *Databus) send(c context.Context, k string, v interface{}, header map[string]string) (err error) {
	var b []byte
	// send message
	if b, err = json.Marshal(v); err != nil {
		logger.Errorf("json.Marshal(%v) error(%v)", v, err)
		return
	}

	var hs *Header
	var args []interface{}
	if len(header) == 0 {
		args = []interface{}{_cmdPub, k, b}
	} else {
		var h []byte
		hs = &Header{Metadata: header}
		h, err = proto.Marshal(hs)
		if err != nil {
			logger.Errorf("pb.Marshal(%v) error(%v)", hs, err)
			return
		}
		args = []interface{}{_cmdPubHeader, k, h, b}
	}
	var retryCount int
	retryInterval := 200 * time.Millisecond
	var doArgs string
	for {
		err = d.p.DoContext(c, args...).Err()
		if err == nil {
			break
		}
		// 出错，记录日志并重试
		if doArgs == "" {
			switch len(args) {
			case 3: // _cmdPub
				doArgs = fmt.Sprintf("%s,%s,%s", args...)
			case 4: // _cmdPubHeader
				hjson, _ := json.Marshal(hs)
				doArgs = fmt.Sprintf("%s,%s,%s,%s", args[0], args[1], hjson, args[3])
			}
		}
		logEntry := logger.WithField("retry_count", retryCount)
		if retryCount < maxRetryCount {
			logEntry.Warnf("conn.Do(%s) error(%v)", doArgs, err)
			retryCount++
			time.Sleep(retryInterval)
			retryInterval *= 2
		} else {
			logEntry.Errorf("conn.Do(%s) error(%v)", doArgs, err)
			break
		}
	}

	return
}

// Close close databus conn.
func (d *Databus) Close() (err error) {
	if !atomic.CompareAndSwapInt32(&d.closed, _open, _closed) {
		return
	}
	if d.s != nil {
		d.s.Close()
	}
	if d.p != nil {
		d.p.Close()
	}
	return nil
}
