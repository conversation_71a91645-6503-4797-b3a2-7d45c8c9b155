CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`last_name` varchar(20) NOT NULL
  ,`first_name` varchar(20) NOT NULL
  ,`iconurl` varchar(240) NOT NULL
  ,`age` uint NOT NULL
  -- ,UNIQUE(`last_name`,`first_name`)
)-- ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8
;

CREATE UNIQUE INDEX uk_lastname_firstname ON user(`last_name`,`first_name`);

INSERT INTO `user`
  (`id`, `last_name`, `first_name`, `iconurl`, `age`)
VALUES
  -- TestIFExpr/TestLeastExpr/TestGreatestExpr/TestUpdateOnDuplicateKeyExpr
  (1, 'A', 'B', '0.png', 20)
;

-- TODO: 有些字段没有用到，但是影响了插入
CREATE TABLE IF NOT EXISTS m_event (
  id int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,title varchar(256) NOT NULL COMMENT '活动名称'
  ,mobile_cover varchar(255) DEFAULT NULL COMMENT '手机端封面'
  --,main_cover varchar(256) NOT NULL COMMENT '活动封面'
  --,share_cover varchar(255) NOT NULL DEFAULT '' COMMENT '分享封面图'
  --,intro text NOT NULL COMMENT '活动介绍'
  ,short_intro varchar(255) DEFAULT NULL COMMENT '活动短介绍'
  ,bilibili_url_pc varchar(255) DEFAULT NULL COMMENT 'B站配置的 PC url'
  ,bilibili_url_h5 varchar(255) DEFAULT NULL COMMENT 'B站配置的 H5 url'
  ,tag_id int DEFAULT NULL
  --,tag varchar(64) NOT NULL COMMENT '活动标签'
  ,type tinyint unsigned NOT NULL COMMENT '活动上传类型0是音频，1是图片'
  --,vote_time int unsigned NOT NULL COMMENT '投票开始时间'
  ,vote_start_time bigint NOT NULL DEFAULT '0' COMMENT '投票开始时间'
  --,vote_end_time int unsigned NOT NULL COMMENT '投票结束时间'
  ,draw_start_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖开始时间'
  ,draw_end_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖结束时间'
  ,create_time int unsigned NOT NULL COMMENT '投稿开始时间'
  ,start_time int unsigned NOT NULL COMMENT '活动开始时间'
  ,end_time int unsigned NOT NULL COMMENT '投稿结束时间'
  ,head varchar(255) DEFAULT NULL COMMENT '音频头'
  ,tail varchar(255) DEFAULT NULL COMMENT '音频尾'
  ,extended_fields text COMMENT '额外数据，JSON format'
  ,status tinyint DEFAULT NULL COMMENT '手机端是否可见'
  ,`limit` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '活动每日投票上限'
  ,mini_cover varchar(255) DEFAULT NULL COMMENT '290图片'
  ,limit_work tinyint unsigned DEFAULT '0' COMMENT '每日可投票作品数量限制'
  ,limit_vote tinyint unsigned DEFAULT '0' COMMENT '每日每作品可投票数量限制'
  ,do_comment tinyint unsigned DEFAULT '0' COMMENT '活动是否支持评论 0：否；1：是'
  ,attr tinyint unsigned NOT NULL DEFAULT '3' COMMENT '活动属性'
)
;

INSERT INTO m_event
  (id, title, type, create_time, start_time, end_time, extended_fields, status, attr)
VALUES
  (110, 'shjh', 3, 100, 100, 1999999999, NULL, 31, 3)
  ,(116, '狂欢季转盘', 3, 100, 100, 1999999999, '{"return":{"point_task_start_time":1592928000,"lottery_start_time":1593532800},"draw_start_time":1593532800}', 31, 3)
  ,(133, '国庆学习挑战', 3, 100, 100, 1999999999, '{"return":{"point_task_start_time":1592928000,"lottery_start_time":1593532800},"draw_start_time":1593532800}', 31, 3)
  ,(148, '2021 春节游园活动', 5, 1999999998, 0, 1999999999,'{"draw_price":100,"daily_free_draw":1}', 15, 16)
  ,(151, '新岁迎佳音', 5, 1999999998, 0, 1999999999, '{"draw_price": 1, "draw_point_start_time": 0,"draw_point_end_time": 1622303999}', 15, 16)
  ,(400, '摇光录下载抽奖活动', 5, 1999999998, 0, 1999999999, '{"game_id": 25,"draw_point_start_time":0,"draw_point_end_time":1999999999,"draw_pools":[{"pool_id":0,"pool_type":1,"attr":1,"cost":1,"start_time":0,"end_time":1999999999}]}', 15, 16)
;

INSERT INTO m_event
  (id, title, type, create_time, start_time, end_time, draw_start_time, draw_end_time, extended_fields, status, attr)
VALUES
  (157, '超粉', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_price": 1}', 15, 16)
  ,(163, '光夜之歌', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{}', 15, 16)
  ,(175, '直播年度盛典', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{"draw_price": {"1": 10, "2": 20}}', 15, 16)
  ,(191, '春日集结（PK 活动）', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{}', 15, 16)
  ,(196, '风月宴知音', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{}', 15, 16)
  ,(205, 'M712 八周年庆 - 猫猫星球', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{"draw_price":{"1":0,"2":100},"daily_free_draw":1,"draw_point_start_time":1655784000,"draw_point_end_time":1657641600}', 15, 16)
  ,(204, '盲盒剧场抽奖', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{}', 15, 16)
  ,(214, '星座许愿', 5, 1999999998, 0, 1999999999, 1, 1999999999, '{}', 15, 16)
  ,(366, '摇光录分享抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(428, '时代新声力配音活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_id":1},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(446, '《放学等我》剧集抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[6,7]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(506, '《我在无限游戏里封神》剧集抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[6,7]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(553, '声优纪抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"follow_user_ids":[80,81],"drama_ids":[91,92,93,94,95],"sound_ids":[1,2]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(555, '声优纪分会场', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(590, '《我在无限游戏里封神》剧集抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[52347,71605,76557],"tasks":{"buy_one_drama":{"reward_point":3,"trigger":1,"limit":1},"subscribe_one_drama":{"reward_point":1,"trigger":1,"limit":1},"share_event":{"reward_point":1,"trigger":1,"limit":1}}},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(591, '《我在无限游戏里封神》剧集抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[52347,71605,76557],"tasks":{"buy_one_drama":{"reward_point":3,"trigger":1,"limit":1},"subscribe_one_drama":{"reward_point":1,"trigger":1,"limit":1},"play_drama_duration":{"reward_point":1,"trigger":600000,"daily_limit":1,"limit":5},"share_event":{"reward_point":1,"trigger":1,"limit":1}}},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(608, '猫耳夏日狂欢季抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"tasks":{"subscribe_one_drama":{"reward_point":1,"trigger":1,"daily_limit":1,"limit":7},"play_drama_duration":{"reward_point":1,"trigger":600000,"daily_limit":1,"limit":7},"share_event":{"reward_point":1,"trigger":1,"daily_limit":1,"limit":7}}},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(614, '暑期每日打卡抽奖活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"task_list":[{"task_type":"share","reward_point":1,"trigger":1,"daily_limit":1,"limit":14}]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(617, '吞海剧集活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[52347,71605,76557,76558],"tasks":{"subscribe_one_drama":{"reward_point":1,"trigger":1,"limit":1},"buy_one_drama":{"reward_point":2,"trigger":1,"limit":1},"play_drama_duration":{"reward_point":1,"trigger":600000,"daily_limit":1,"limit":5},"share_event":{"reward_point":1,"trigger":1,"limit":1}}},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(669, '再世权臣剧集活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[52347,71605,76557,76558],"task_list":[{"task_type":"play","reward_point":1,"trigger":600000,"daily_limit":1,"limit":1000},{"task_type":"subscribe_one_drama","reward_point":1,"trigger":1,"limit":1},{"task_type":"share","reward_point":1,"trigger":1,"limit":10},{"task_type":"buy_one_drama","reward_point":1,"trigger":1,"limit":1}]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
  ,(709, '盲盒剧场 V9 活动', 5, 1999999998, 0, 1999999999, 0, 1999999999, '{"draw_task_config":{"drama_ids":[52347,71605,76557,76558],"task_list":[{"task_type":"play","reward_point":1,"trigger":300000,"limit":1}]},"draw_point_start_time":0,"draw_point_end_time":1999999999}', 15, 16)
;

CREATE TABLE IF NOT EXISTS `mowangskuser` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`temp1` varchar(32) DEFAULT NULL
  ,`temp2` varchar(32) DEFAULT NULL
  ,`temp3` varchar(32) DEFAULT NULL
  ,`temp4` varchar(32) DEFAULT NULL
  ,`confirm` int NOT NULL DEFAULT '0' COMMENT '用户权限或身份标识'
  ,`username` varchar(20) NOT NULL
  ,`email` varchar(50) DEFAULT NULL
  ,`cip` varchar(15) NOT NULL DEFAULT ''
  ,`uip` varchar(15) NOT NULL
  ,`ctime` int NOT NULL
  ,`utime` int NOT NULL
  ,`quanxian` varchar(5) NOT NULL
  ,`teamid` int NOT NULL DEFAULT '1'
  ,`teamname` varchar(20) NOT NULL DEFAULT 'Drrr'
  ,`ban` tinyint NOT NULL DEFAULT '0'
  ,`ustr` int NOT NULL DEFAULT '0'
  ,`uint` int NOT NULL DEFAULT '0'
  ,`uagi` int NOT NULL DEFAULT '0'
  ,`point` int NOT NULL DEFAULT '50'
  ,`nowsound` int NOT NULL DEFAULT '0' COMMENT '记录用户当前音频时长'
  ,`iconid` int NOT NULL
  ,`iconurl` varchar(240) NOT NULL
  ,`iconcolor` varchar(50) NOT NULL
  ,`subtitle` varchar(10) NOT NULL
  ,`boardiconid` int NOT NULL DEFAULT '0'
  ,`boardiconurl` varchar(200) NOT NULL
  ,`boardiconcolor` varchar(50) NOT NULL DEFAULT '#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1'
  ,`coverid` int DEFAULT NULL COMMENT '封面图id'
  ,`coverurl` varchar(60) DEFAULT NULL COMMENT '封面图'
  ,`isnewmsg` int NOT NULL DEFAULT '0'
  ,`userintro` text
  ,`userintro_audio` int DEFAULT NULL
  ,`likenum` int DEFAULT '0' COMMENT '点赞数'
  ,`fansnum` int DEFAULT '0' COMMENT '粉丝数'
  ,`follownum` int DEFAULT '0' COMMENT '关注数'
  ,`soundnum` int DEFAULT '0' COMMENT '个人语音数'
  ,`albumnum` int DEFAULT '0' COMMENT '个人专辑数'
  ,`imagenum` int DEFAULT '0' COMMENT '个人图片数'
  ,`feednum` int DEFAULT '0' COMMENT 'feed 流未读信息数'
  ,`soundnumchecked` int DEFAULT '0' COMMENT '审核通过的声音'
  ,`imagenumchecked` int DEFAULT '0' COMMENT '审核通过的图片'
  ,`mlevel` int DEFAULT '1' COMMENT '用户当前等级'
  ,`avatar` varchar(100) DEFAULT NULL COMMENT '三次元头像'
  ,`icontype` int DEFAULT '1'
  ,`mobile` varchar(11) DEFAULT NULL
  ,`region` smallint DEFAULT NULL COMMENT '国际电话区号'
  ,`coverurl_new` varchar(60) NOT NULL DEFAULT '' COMMENT '用户上传的封面图链接'
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;
-- 新增测试用户
INSERT INTO `mowangskuser`
  (`id`, `temp1`, `temp2`, `temp3`, `temp4`, `confirm`, `username`, `email`, `cip`, `uip`, `ctime`, `utime`, `quanxian`, `teamid`, `teamname`, `ban`, `ustr`, `uint`, `uagi`, `point`, `nowsound`, `iconid`, `iconurl`, `iconcolor`, `subtitle`, `boardiconid`, `boardiconurl`, `boardiconcolor`, `coverid`, `coverurl`, `isnewmsg`, `userintro`, `userintro_audio`, `likenum`, `fansnum`, `follownum`, `soundnum`, `albumnum`, `imagenum`, `feednum`, `soundnumchecked`, `imagenumchecked`, `mlevel`, `avatar`, `icontype`, `mobile`, `region`, `coverurl_new`)
VALUES
  (346286, NULL, NULL, NULL, NULL, 0, 'InVinCiblezz', '<EMAIL>', '***************', '127.0.0.1', 1474172405, 0, 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, 13913903851, 86, '')
  ,(12, NULL, NULL, NULL, NULL, 0, '零月', '<EMAIL>', '***************', '127.0.0.1', 1474172405, 0, 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '', NULL, '')
  ,(13, NULL, NULL, NULL, NULL, 0, '操你妈', '<EMAIL>', '***************', '127.0.0.1', 1474172405, 0, 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '', NULL, '')
  ,(14, NULL, NULL, NULL, NULL, 0, '14', '<EMAIL>', '***************', '127.0.0.1', 1474172405, 0, 'A', 0, 'Drrr', 0, 0, 0, 0, 59, 0, 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 80, 79, 230, 0, 0, 0, 9697, 0, 0, 1, NULL, 0, '', NULL, '')
  -- test for add dm, scan/text
  ,(3013097, NULL, NULL, NULL, NULL, 0, 'test123s', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1616915657, 1617265389, 'B', 0, '', 0, 0, 0, 0, 50, 0, 0, 'http://static.missevan.com/avatars/icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, NULL, 0, 1890, 0, 1609, 136, 0, 0, 0, 0, 1, NULL, 1, '7365028549', 44, '')
  ,(3013620, NULL, NULL, NULL, NULL, 0,	'保驾护航233', '<EMAIL>', '127.0.0.1',	'127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 1, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  ,(3013621, NULL, NULL, NULL, NULL, 0,	'233', '<EMAIL>', '127.0.0.1',	'127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  ,(3013622, NULL, NULL, NULL, NULL, 0,	'保233', '<EMAIL>', '127.0.0.1',	'127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  -- 测试已注销用户
  ,(3013623, NULL, NULL, NULL, NULL, 4096, '测试已注销用户', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  -- 测试设置了隐私的用户
  ,(3013624, NULL, NULL, NULL, NULL, 256, '测试设置了隐私的用户', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  -- 测试未收藏剧单的用户
  ,(820591, NULL, NULL, NULL, NULL, 0, '测试用户 1', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  -- 测试已收藏剧单的用户
  ,(820592, NULL, NULL, NULL, NULL, 0, '测试用户 2', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  -- 测试提交评论和弹幕无权限收听的会员音的用户
  ,(10, NULL, NULL, NULL, NULL, 0, '测试用户 2', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  ,(11, NULL, NULL, NULL, NULL, 0, '测试用户 2', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  --TestAddDm
  ,(3457148, NULL, NULL, NULL, NULL, 0, '测试用户 2', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')
  ,(3457149, NULL, NULL, NULL, NULL, 0, '测试用户 2', '<EMAIL>', '127.0.0.1', '127.0.0.1', 1624358604, 1624420416, 'B', 1, 'Drrr', 0, 0, 0, 0, 1190568, 0, 0, 'http://static.missevan.com/avatars/201908/09/d15e08a904b2b403acc0d9a93e799b33171027.jpg', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', '', 0, 'icon01.png', '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9', NULL, NULL, 0, NULL, 0, 0, 30, 55, 16856, 1923, 0, 0, 0, 0, 1, '202106/23/c12607deb6274ff12a82f1d2c2399498114634.jpg', 1, 15611605860, 86, '')

;

CREATE TABLE IF NOT EXISTS `user_addendum` (
  `id` bigint NOT NULL COMMENT '用户 ID'
  ,`sex` tinyint DEFAULT NULL COMMENT '性别（0 未知，1 为男，2 为女）'
  ,`birthday` date DEFAULT NULL
  ,`qq` varchar(32) DEFAULT NULL COMMENT 'QQ 昵称'
  ,`weibo` varchar(32) DEFAULT NULL COMMENT '微博昵称'
  ,`wechat` varchar(32) DEFAULT NULL COMMENT '微信昵称'
  ,`bilibili` varchar(32) DEFAULT NULL COMMENT 'Bilibili 昵称'
  ,`apple` varchar(32) DEFAULT NULL COMMENT 'Apple 昵称'
  ,`message_config` varchar(255) DEFAULT NULL COMMENT '消息设置（JSON 字符串，例: {"receive":1,"fold":0}）'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` text NOT NULL COMMENT '用户 IP 详情'
  ,`birthdate_mmdd` char(4) DEFAULT NULL COMMENT '用于检索的用户生日月日（例如 12 月 26 日生日为 1226）'
  ,PRIMARY KEY (`id`)
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO `user_addendum`
  (`id`, `sex`, `birthday`, `qq`, `weibo`, `wechat`, `bilibili`, `apple`, `message_config`, `ip`, `ip_detail`, `birthdate_mmdd`)
VALUES
  -- TestFindUserIDsByBirthday、TestNeedLeapYearProcess
  (1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '127.0.0.1', '{}', '0126')
  ,(2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '127.0.0.1', '{}', '0228')
  ,(3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '127.0.0.1', '{}', '0229')
  ,(4, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '127.0.0.1', '{}', '0301')
;

CREATE TABLE IF NOT EXISTS `m_sound` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`catalog_id` int NOT NULL COMMENT '分类 ID'
  ,`create_time` int NOT NULL COMMENT '创建时间'
  ,`last_update_time` int NOT NULL
  ,`duration` int NOT NULL COMMENT '持续时间'
  ,`user_id` int NOT NULL
  ,`username` varchar(20) NOT NULL
  ,`cover_image` varchar(255) NOT NULL COMMENT '封面图片'
  ,`animationid` int NOT NULL
  ,`characterid` int NOT NULL
  ,`seiyid` int NOT NULL
  ,`soundstr` varchar(100) NOT NULL
  ,`intro` text NOT NULL COMMENT '简介'
  ,`soundurl` varchar(100) NOT NULL
  ,`soundurl_32` varchar(100) NOT NULL
  ,`soundurl_64` varchar(100) NOT NULL
  ,`soundurl_128` varchar(100) NOT NULL
  ,`downtimes` int NOT NULL DEFAULT '0'
  ,`uptimes` int NOT NULL DEFAULT '0' COMMENT '被赞次数'
  ,`checked` int NOT NULL DEFAULT '0' COMMENT '音频状态，-3：转码失败；-2：配音未转码；-1：未转码；0：审核中；1：已审核通过；2：报警音；3：下架音'
  ,`source` int NOT NULL DEFAULT '0' COMMENT '来源'
  ,`download` int NOT NULL DEFAULT '0' COMMENT '是否允许下载'
  ,`view_count` int NOT NULL DEFAULT '0' COMMENT '查看数'
  ,`comment_count` int NOT NULL DEFAULT '0' COMMENT '弹幕数'
  ,`favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏数'
  ,`point` int NOT NULL DEFAULT '0' COMMENT '猫耳数'
  ,`push` int NOT NULL DEFAULT '0' COMMENT '是否推送'
  ,`refined` int DEFAULT '0' COMMENT '是否加精'
  ,`comments_count` int NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`sub_comments_count` int NOT NULL DEFAULT '0' COMMENT '子评论数'
  ,`pay_type` int DEFAULT '0'
  ,`type` int DEFAULT '0' COMMENT '音频类型'
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

-- 新增测试音频
INSERT INTO `m_sound`
  (`id`, `catalog_id`, `create_time`, `last_update_time`, `duration`, `user_id`, `username`, `cover_image`, `animationid`, `characterid`, `seiyid`, `soundstr`, `intro`, `soundurl`, `soundurl_32`, `soundurl_64`, `soundurl_128`, `downtimes`, `uptimes`, `checked`, `source`, `download`, `view_count`, `comment_count`, `favorite_count`, `point`, `push`, `refined`, `comments_count`, `sub_comments_count`, `pay_type`, `type`)
VALUES
  (233, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(234, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试获取剧集信息（音频 1）', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 0, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(235, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试获取剧集信息（音频 2）', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(236, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试获取剧集信息（音频 3）', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 2, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(3674, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(3675, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, -3, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(88771, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(88772, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试下架音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 3, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  ,(88773, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试单集付费音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 1, 0)
  ,(44809, 19, 1435888828, 1565147366, 609209, 100913, '传奇火箭队', '201507/03/7ae84f02fe85afe9729cf4c7501f92ee100027.jpg', 0, 0, 0, '金牌助理之弯弯没想到 S02E00', '<p>【传奇火箭队出品】非天夜翔原著◎现代DM广播剧《金牌助理之弯弯没想到》第二季试播集（S02E00）</p><p><br /></p><p>工作组：</p><p>-传奇火箭队-</p><p>制作：临时攻【三好街民攻团】、瀛洲越客【颠覆声社】</p><p>海报：点八斤</p><p>宣传：传火营销部</p><p><br /></p><p>配音组：</p><p>报幕/旁白：雾一【绘音配音】</p><p>萧毅：<a href="http://www.missevan.com/seiy/1097">梁铎</a>【颠覆声社】</p><p>卢舟：楼生</p><p>于导演：<a href="http://www.missevan.com/seiy/1098">Mixtan</a></p><p>宁亚晴：芒果抽【颠覆声社】</p><p>小美：韶小韶【优思优铭】</p><p>杜马：燃【水岸聆音】</p><p>杜梅：KONG嘘【KA.U】</p><p>乌恒古：<a href="http://www.missevan.com/seiy/1118">柯暮卿</a>【优声由色】</p><p><br /></p><p>ED：《弯弯没想到》</p><p>原曲：《万万没想到》</p><p>填词：瀛洲越客【颠覆声社】</p><p>后期：茶雪</p><p>翻唱：<a href="http://www.missevan.com/seiy/1097">梁铎</a>【颠覆声社】ft.楼生</p><p><br /></p>', '201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', '32BIT/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', 'MP3/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', '128BIT/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', 54, 277, 1, 1, 0, 0, 1740, 8349, 282, 0, 0, 1265, 3, 0, 0)
  -- TestFindSoundByUserID
  ,(88775, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 3)
  ,(88776, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  -- TestGetUpCardWorks
  ,(88777, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  -- 转码失败情况
  ,(99999, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试限定音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, -3, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 0, 0)
  -- TestMSound_listSameUserSoundIDs
  -- TestMSound_listSameCatalogSoundIDs
  -- TestMSound_listSameTagSoundIDs
  -- TestMSound_getOrSetRecommendSoundMap
  -- TestListRecommendSoundIDs
  ,(100001, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100002, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100003, 4, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100004, 5, 1328281178, 1515393856, 2768, 1346281, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100005, 6, 1328281178, 1515393856, 2768, 1346282, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100006, 6, 1328281178, 1515393856, 2768, 1346283, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(100007, 7, 1328281178, 1515393856, 2768, 1346284, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  ,(1217690, 8, 1328281179, 1515393856, 2769, 1346285, 'test_user', '201701/24/test.png', 1, 1, 2, '测试推荐音频', '', '201202/03/test.mp3', '', 'sound://aod/202010/30/test.m4a', 'sound://aod/202010/30/test-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 0, 133, 107, 0, 0)
  -- TestAddDm
  ,(3678, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 2, 0)
  ,(3679, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 1, 0)
  ,(3680, 23, 1328281178, 1515393856, 2768, 346286, 'InVinCiblezz', '201701/24/92610e061e2c05937f8c823f93857c57091415.png', 1, 1, 2, '测试音频', '', '201202/03/f3688a4d3bf0129b665da1218eb92f42.mp3', '', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42.m4a', 'sound://aod/202010/30/f3688a4d3bf0129b665da1218eb92f42-128k.m4a', 3232, 795, 1, 0, 0, 67939, 103, 303, 263, 2, 14, 133, 107, 2, 0)
  ,(8814073, 75, 1702549708, 1702549708, 230527, 3457148, '太阳落下去了', '202312/14/f5f43bc0bcc71ad28e55250cff448e5f182829.jpg', 0, 0, 0, '16 New Romantics', '<p>	modified:   app/build.gradle.kts</p>', 'upos://mefmboss/1231214wndgbdyis1oaw63tp5zjbkrdg.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb-192k.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb-128k.m4a', 20, 2, 1, 1, 0, 693, 0, 6, 2, 2, 0, 4, 5, 2, 0)
  ,(8814074, 75, 1702549708, 1702549708, 230527, 3457149, '太阳落下去了', '202312/14/f5f43bc0bcc71ad28e55250cff448e5f182829.jpg', 0, 0, 0, '16 New Romantics', '<p>	modified:   app/build.gradle.kts</p>', 'upos://mefmboss/1231214wndgbdyis1oaw63tp5zjbkrdg.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb-192k.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb.m4a', 'sound://aod/202501/09/_00003k8ws55tk990u2nin85yczm9cwb-128k.m4a', 20, 2, 1, 1, 0, 693, 0, 6, 2, 2, 0, 4, 5, 2, 0)
;

CREATE TABLE IF NOT EXISTS `commentnotice` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`c_user_id` int NOT NULL COMMENT '评论用户'
  ,`c_user_name` varchar(20) NOT NULL COMMENT '评论用户名'
  ,`a_user_id` int NOT NULL COMMENT '被 @ 用户的用户 ID'
  ,`a_user_name` varchar(20) NOT NULL COMMENT '被 at 用户的用户名'
  ,`type` int NOT NULL COMMENT '1 是音频，2是专辑，3 是新闻'
  ,`eId` int NOT NULL COMMENT '对应 ID'
  ,`title` varchar(100) NOT NULL COMMENT '被评论的 title'
  ,`comment_id` int NOT NULL COMMENT '评论 ID'
  ,`sub` int NOT NULL COMMENT '是否为子评论，1 为是，0 为否'
  ,`isread` int NOT NULL COMMENT '是否已读'
  ,`notice_type` int NOT NULL DEFAULT '0' COMMENT '是否是被 @ 消息（是为 1 即 @ 我的提醒，否则为 0 即为评论提醒）'
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

CREATE TABLE IF NOT EXISTS `comment_like` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`cid` int NOT NULL
  ,`sub` int NOT NULL DEFAULT '0' COMMENT '1 为子评论，0 为评论'
  ,`userid` int NOT NULL COMMENT '用户 ID'
  ,`type` int NOT NULL DEFAULT '1' COMMENT '类型，1：点赞；2：点踩'
  ,`create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int NOT NULL DEFAULT '0' COMMENT '更新时间'
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

CREATE TABLE IF NOT EXISTS `like_notice` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`c_user_id` int NOT NULL COMMENT '点赞用户'
  ,`a_user_id` int NOT NULL COMMENT '被点赞用户'
  ,`type` int NOT NULL DEFAULT '1' COMMENT '1 是音频，2 是专辑，3 是新闻，4 是频道，6 是专题，7 是活动'
  ,`comment_id` int NOT NULL COMMENT '评论 ID'
  ,`title` text NOT NULL COMMENT '消息标题'
  ,`isread` int NOT NULL DEFAULT '0' COMMENT '是否已读'
  ,`time` int NOT NULL COMMENT '点赞时间'
  ,`eId` int NOT NULL COMMENT '资源 ID'
  ,`sub` int NOT NULL DEFAULT '0' COMMENT '0 是父评论点赞，1 是子评论点赞'
  ,`content` varchar(255) NOT NULL COMMENT '消息内容'
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

CREATE TABLE `authassignment`
(
  `itemname` varchar(64) NOT NULL
  ,`userid`   varchar(64) NOT NULL
  ,`bizrule`  varchar
  ,`data`     varchar
)
;

CREATE TABLE IF NOT EXISTS `m_appupdate`
(
  `id`                int(10) AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT 'id'
  ,`title`            varchar(32)                         NOT NULL DEFAULT ''   COMMENT 'app名称'
  ,`version`          varchar(32)                         NOT NULL DEFAULT ''   COMMENT '版本号'
  ,`intro`            text                                NOT NULL DEFAULT ''   COMMENT '介绍'
  ,`changelog`        text                                NOT NULL DEFAULT ''   COMMENT '变更日志'
  ,`status`           tinyint(3) unsigned                 NOT NULL DEFAULT '0'  COMMENT '发布状态'
  ,`appurl`           varchar(100)                        NOT NULL DEFAULT ''   COMMENT 'app下载地址'
  ,`update_time`      int(10) unsigned                    NOT NULL DEFAULT '0'  COMMENT '更新时间'
  ,`device`           tinyint(4)                                   DEFAULT '0'  COMMENT '设备类型，0：Android，1：iOS，2：Windows'
  ,`size`             float(5)                            NOT NULL DEFAULT '0'  COMMENT 'app大小(M)'
  ,`force_download`   int(11) unsigned                    NOT NULL DEFAULT '0'  COMMENT '是否强制更新'
  ,`developer`        varchar(255)                        NOT NULL DEFAULT 'Maoer Co.' COMMENT '开发者名称'
  ,`privacy_url`      varchar(255)                        NOT NULL DEFAULT 'https://link.missevan.com/rule/privacy' COMMENT '隐私政策地址'
  ,`permission_url`   varchar(255)                        NOT NULL DEFAULT 'https://link.missevan.com/rule/permission' COMMENT '权限用途地址'
  ,`appurl2`          varchar(100)                        NOT NULL DEFAULT '' COMMENT 'app 下载地址（Android: 64 位包，Windows: zip 包）'
)
;

CREATE TABLE IF NOT EXISTS `m_album`
(
  `id`               int(10) AUTO_INCREMENT PRIMARY KEY NOT NULL
  ,`title`            varchar(255)                       NOT NULL COMMENT '专辑标题'
  ,`intro`            text COMMENT '专辑介绍'
  ,`catalog_id`       int(5) unsigned                    NOT NULL DEFAULT '0' COMMENT '分类id'
  ,`create_time`      int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`last_update_time` int(10) unsigned                            DEFAULT NULL COMMENT '最后更新'
  ,`user_id`          int(10) unsigned                   NOT NULL COMMENT '作者id'
  ,`username`         varchar(20)                        NOT NULL COMMENT '作者名称'
  ,`cover_image`      varchar(255)                       NOT NULL DEFAULT '' COMMENT '封面图片'
  ,`uptimes`          int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '被赞次数'
  ,`refined`          tinyint(3) unsigned                NOT NULL DEFAULT '0' COMMENT '音单属性。比特位第 1 位为 1 表示加精；第 2 位为 1 表示音单未设置自定义封面图；比特位第 5 位为 1 表示私有音单；'
  ,`checked`          tinyint(4)                         NOT NULL DEFAULT '1' COMMENT '审核状态，0：未审核；1：已审核通过；2：报警音单；3：下架'
  ,`source`           tinyint(1)                         NOT NULL DEFAULT '0' COMMENT '来源'
  ,`view_count`       int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '查看数'
  ,`comment_count`    int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`favorite_count`   int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '收藏数'
  ,`music_count`      int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '音乐数'
);

CREATE TABLE IF NOT EXISTS `m_tag`
(
  `id`                int(10) AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT '标签id'
  ,`name`             varchar(64)                        NOT NULL COMMENT '标签名称'
  ,`icon`             varchar(100)                       NOT NULL DEFAULT '' COMMENT '标签图标'
  ,`cover`            varchar(120)                                DEFAULT '' COMMENT '频道背景图'
  ,`sintro`           varchar(200)                       NOT NULL DEFAULT '' COMMENT '一句话简介'
  ,`intro`            text COMMENT '标签简介'
  ,`sound_num`        mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '声音引用数'
  ,`image_num`        mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '图片引用数'
  ,`album_num`        mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '专辑引用数'
  ,`follow_num`       mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '标签订阅人数'
  ,`userid`           int(11) unsigned                   NOT NULL DEFAULT '0' COMMENT '绑定用户id'
  ,`seiyid`           mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '绑定声优id'
  ,`characterid`      mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '绑定角色id'
  ,`animationid`      mediumint(8) unsigned              NOT NULL DEFAULT '0' COMMENT '绑定作品id'
  ,`recommended`      tinyint(1) unsigned                NOT NULL DEFAULT '0' COMMENT '是否上首页'
  ,`sort_type`        tinyint(1) unsigned                NOT NULL DEFAULT '0' COMMENT '排序方式'
  ,`sort_channel`     tinyint(2)                         NOT NULL DEFAULT '0' COMMENT '频道排序'
  ,`catalogid`        smallint(5) unsigned               NOT NULL DEFAULT '0' COMMENT '分类id'
  ,`last_upload_time` int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '最后上传时间'
);

CREATE TABLE IF NOT EXISTS `topic`
(
  `id`               int(10) AUTO_INCREMENT PRIMARY KEY NOT NULL COMMENT '专题id'
  ,`type`            tinyint(1)                                  DEFAULT '0' COMMENT '专题类型，0：默认，1：新专题，2：弹窗'
  ,`title`           varchar(255)                                DEFAULT NULL COMMENT '标题地址'
  ,`pic_url`         varchar(255)                                DEFAULT NULL COMMENT '图片地址'
  ,`mobile_pic_url`  varchar(255)                                DEFAULT NULL COMMENT '手机图片地址'
  ,`share_pic_url`   varchar(255)                       NOT NULL DEFAULT '' COMMENT '分享封面图'
  ,`html_url`        varchar(255)                                DEFAULT NULL COMMENT '页面地址'
  ,`bilibili_url_pc` varchar(255)                       NOT NULL DEFAULT '' COMMENT 'B站配置的 PC url'
  ,`bilibili_url_h5` varchar(255)                       NOT NULL DEFAULT '' COMMENT 'B站配置的 H5 url'
  ,`comment_num`     int(10) unsigned                   NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`status`          tinyint(4) unsigned                NOT NULL DEFAULT '0' COMMENT '状态 0：未发布；1：已发布'
  ,`create_time`     bigint(20) unsigned                NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time`   bigint(20) unsigned                NOT NULL DEFAULT '0' COMMENT '更新时间'
)
;

CREATE TABLE IF NOT EXISTS `an_prize` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`name` varchar(255) DEFAULT NULL COMMENT '奖品名'
  ,`pic` varchar(255) DEFAULT NULL COMMENT '奖品图片'
  ,`event_id` int DEFAULT NULL
  ,`probability` int NOT NULL DEFAULT '0' COMMENT '中奖概率'
  ,`num` int NOT NULL DEFAULT '0' COMMENT '奖品数量'
)
;

INSERT INTO an_prize
  (name, pic, event_id, probability, num)
VALUES
  ('游戏兑换码', "", 110, 5, -1)
  ,('谢谢参与', 'https://a.png?0', 116, 100, 100)
  ,('谢谢参与', 'https://a.png?1', 116, 100, 100)
  ,('谢谢参与', "", 131, 1, -1)
  ,('谢谢参与', "", 133, 1, -1)
  ,('谢谢参与', "", 148, 1, -1)
  ,('谢谢参与', "", 151, 1, -1)
  ,('谢谢参与', "", 157, 1, -1)
  ,('谢谢参与', "", 161, 1, -1)
  ,('煦光颂歌 7d', "", 163, 1, -1)
  ,('永夜回音 7d', "", 163, 1, -1)
  ,('晨曦百合 7d', "", 163, 1, -1)
  ,('夜雾玫瑰 7d', "", 163, 1, -1)
  ,('光夜颂歌×1', "", 163, 1, -1)
  ,('白色礼赞×1', "", 163, 1, -1)
  ,('凤鸣天下×1', "", 163, 1, -1)
  ,("#1 小鱼干×20", "test://prize/202106/25/1.png", 175, 90, -1)
  ,("#1 小鱼干×50", "test://prize/202106/25/2.png", 175, 50, -1)
  ,("#1 剧集 1 季", "test://prize/202106/25/3.png", 175, 1, 4)
  ,("#1 小鱼干×100", "test://prize/202106/25/4.png", 175, 5, -1)
  ,("#2 钻石×10", "test://prize/202106/25/5.png", 175, 60, 342)
  ,("#2 钻石×100", "test://prize/202106/25/6.png", 175, 3, 16)
  ,("#2 钻石×200", "test://prize/202106/25/7.png", 175, 1, 4)
  ,("#2 再接再厉", "test://prize/202106/25/8.png", 175, 790, -1)
  ,('小闪电 × 10', 'test://prize/202106/25/9.png', 191, 90, -1)
  ,('#0,1 1k 热度卡 × 1', 'test://prize/202106/25/9.png', 196, 90, -1)
  ,('#1,1 谢谢参与', 'test://prize/202206/10/a.png', 205, 100, 100)
  ,('#2,1 谢谢参与', 'test://prize/202206/10/b.png', 205, 100, 100)
  ,('#1,1 小鱼干×20', 'test://prize/202206/10/a.png', 204, 100, -1)
  ,('#2,1 谢谢参与', 'test://prize/202206/10/b.png', 204, 100, -1)
;

CREATE TABLE IF NOT EXISTS `live` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID'
  ,`room_id` bigint NOT NULL COMMENT '直播间房间 ID'
  ,`catalog_id` int unsigned NOT NULL DEFAULT '0'
  ,`title` varchar(30) NOT NULL DEFAULT '' COMMENT '直播间名称'
  ,`intro` varchar(160) NOT NULL DEFAULT '' COMMENT '直播间简介'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '直播间封面'
  ,`status` tinyint(1) DEFAULT '0' COMMENT '直播间状态，0：没有开启房间；1：房间开启'
  ,`live_start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '开播时间'
  ,`contract_id` int unsigned NOT NULL DEFAULT '1' COMMENT '主播合约 ID'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '修改时间'
  ,`user_id` bigint DEFAULT NULL COMMENT '主播 ID'
)
;

INSERT INTO `live`
  (`id`, `room_id`, `catalog_id`, `title`, `intro`, `cover`, `status`, `live_start_time`, `contract_id`, `create_time`, `modified_time`, `user_id`)
VALUES
  (1, 999997, 0, '测试直播间 1', '', '', 0, 0, 1, 1622545264, 1622545264, 346286)
  ,(2, 999998, 0, '测试直播间 2', '', '', 0, 0, 1, 1622545264, 1622545264, 10)
  ,(3, 999999, 1, '测试直播间 3', '', '', 0, 0, 1, 1622545264, 1622545264, 12)
  -- TestFindOpenLiveByUserID
  ,(5, 999989, 1, '测试直播间 4', '', '', 1, 0, 1, 1622545264, 1622545264, 1)
;

CREATE TABLE IF NOT EXISTS `m_game_subscribe` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`game_id` int unsigned NOT NULL DEFAULT '0' COMMENT '游戏 ID'
  ,`user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`ip` varchar(15) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8;
;

INSERT INTO `m_game_subscribe`
  (`id`, `game_id`, `user_id`, `ip`, `create_time`, `modified_time`, `delete_time`)
VALUES
  -- TestMGameCenter_GetCardStatus
  (1, 1, 12, '*************', 1679500800, 1679500800, 0)
  -- TestMGameSubscribe_Exists
  ,(2, 2, 12, '*************', 1679500800, 1679500800, 1680080339)
;

CREATE TABLE IF NOT EXISTS `m_message_assign` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`recuid` int unsigned NOT NULL COMMENT '收到人M号'
  ,`send_uid` int unsigned NOT NULL COMMENT '发送人M号'
  ,`title` varchar(255) DEFAULT NULL COMMENT '标题'
  ,`content` varchar(5000) DEFAULT NULL COMMENT '正文'
  ,`status` tinyint NOT NULL DEFAULT '0' COMMENT '0未读1已读'
  ,`time` int unsigned NOT NULL
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
;

CREATE TABLE IF NOT EXISTS `topic` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '专题id'
  ,`type` tinyint(1) DEFAULT '0' COMMENT '专题类型'
  ,`title` varchar(255) DEFAULT NULL COMMENT '标题地址'
  ,`pic_url` varchar(255) DEFAULT NULL COMMENT '图片地址'
  ,`mobile_pic_url` varchar(255) DEFAULT NULL COMMENT '手机图片地址'
  ,`share_pic_url` varchar(255) NOT NULL DEFAULT '' COMMENT '分享封面图'
  ,`html_url` varchar(255) DEFAULT NULL COMMENT '页面地址'
  ,`bilibili_url_pc` varchar(255) DEFAULT NULL COMMENT 'B站配置的 PC url'
  ,`bilibili_url_h5` varchar(255) DEFAULT NULL COMMENT 'B站配置的 H5 url'
  ,`comment_num` int unsigned NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态 0：未发布；1：已发布'
  ,`create_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '更新时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8;
;

CREATE TABLE IF NOT EXISTS `user_oa` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`oa_name` varchar(20) NOT NULL COMMENT 'OA 账号'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 -1：取消绑定；1：已绑定'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OA 账号和用户 ID 关联表'
;

INSERT INTO `user_oa`
  (`id`, `oa_name`, `user_id`, `status`, `create_time`, `modified_time`, `delete_time`)
VALUES
  (1, '测试 OA 名称', 2, 1, 1624530554, 1624530554, 0);

CREATE TABLE IF NOT EXISTS `m_event_user_prize` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `user_id` bigint NOT NULL
  ,`prize_id` bigint NOT NULL
  ,`event_id` bigint NOT NULL
  ,`game_code` varchar(50) DEFAULT NULL
  ,`create_time` bigint NOT NULL
  ,`modified_time` bigint NOT NULL
  ,PRIMARY KEY (`id`)
  ,KEY `idx_gift` (`prize_id`)
  ,KEY `idx_userid` (`user_id`)
) -- ENGINE=InnoDB AUTO_INCREMENT=1041429 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

CREATE TABLE IF NOT EXISTS `m_sound_node` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`sound_id` bigint NOT NULL COMMENT '音频 ID'
  ,`node_type` tinyint(4) unsigned NOT NULL COMMENT '节点类型，1 为根节点；2 为子节点；3 为子叶节点'
  ,`next_ids` varchar(255) NOT NULL COMMENT '子节点 ID，值为包含多个子节点 ID 的 json 字符串'
  ,`attr` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '节点属性。比特位第一位为 1 表示默认选项'
  ,`stay_duration` int(11) NOT NULL COMMENT '选择页面停留时长，单位毫秒， -1 为未做选择前一直停留'
  ,`title` varchar(30) NOT NULL COMMENT '节点标题'
  ,`question` varchar(60) NOT NULL DEFAULT '' COMMENT '问题'
  ,`option` varchar(30) NOT NULL DEFAULT '' COMMENT '选项描述'
  ,`soundurl_user` varchar(125) NOT NULL COMMENT '原音频地址'
  ,`soundurl` varchar(125) NOT NULL DEFAULT '' COMMENT '原音质音频地址'
  ,`soundurl_128` varchar(125) NOT NULL DEFAULT '' COMMENT '128K 比特率音质音频地址'
  ,`soundurl_192` varchar(125) NOT NULL DEFAULT '' COMMENT '192K 比特率音质音频地址'
  ,`cover` varchar(125) NOT NULL DEFAULT '' COMMENT '封面地址'
  ,`duration` int(11) unsigned NOT NULL COMMENT '音频时长，单位毫秒'
  ,`pay_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型，0：免费；1：单集付费；2：整剧付费'
  ,`score_threshold` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '可选择时需要达到的分数阈值'
  ,`button_color` int(8) unsigned zerofill NOT NULL DEFAULT '16777215' COMMENT '按钮颜色'
  ,`button_image` varchar(125) NOT NULL DEFAULT '' COMMENT '按钮背景图'
  ,`checked` tinyint(3) NOT NULL DEFAULT '-1' COMMENT '状态，-1：待转码；0：待审核；1：审核通过；2：报警；3：下架'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,INDEX `idx_soundid_nodetype` (`sound_id`,`node_type`)
)
;

INSERT INTO `m_sound_node`
  (`sound_id`, `node_type`, `next_ids`, `attr`, `stay_duration`, `title`, `question`, `option`, `soundurl_user`, `soundurl`, `soundurl_128`, `soundurl_192`, `cover`, `duration`, `pay_type`, `score_threshold`, `button_color`, `button_image`, `checked`, `create_time`, `modified_time`)
VALUES
  (3674, 1, '[ {\"id\": 3, \"option\": \"晚上酒馆\"},{\"id\": 2, \"option\": \"华丽歌剧场\"}]', 0, -1, '烬 · 第一章 - 华丽开场', '何处演奏', '', '202004/26/e19d79dd8d785d8aa1f0fc700f2403e7135925.m4a', 'sound://aod/202004/26/e19d79dd8d785d8aa1f0fc700f2403e7135925.m4a', 'sound://aod/202004/26/e19d79dd8d785d8aa1f0fc700f2403e7135925-128k.m4a', '', 'oss://image/202005/25/test.png', 4579, 0, 0, 16777215, '', 1, 0, 0)
;

CREATE TABLE IF NOT EXISTS `catalog` (
  `id` int(10) unsigned AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类'
  ,`catalog_name` varchar(100) NOT NULL COMMENT '名称'
  ,`catalog_name_second` varchar(100) DEFAULT '' COMMENT '副名称'
  ,`catalog_name_alias` varchar(100) NOT NULL DEFAULT '' COMMENT '别名'
  ,`content` text COMMENT '详细介绍'
  ,`seo_title` varchar(100) NOT NULL DEFAULT '' COMMENT 'seo标题'
  ,`seo_keywords` varchar(255) NOT NULL DEFAULT '' COMMENT 'seo关键字'
  ,`seo_description` text COMMENT 'seo描述'
  ,`attach_file` varchar(100) DEFAULT '' COMMENT '附件'
  ,`attach_thumb` varchar(100) DEFAULT '' COMMENT '缩略图'
  ,`sort_order` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序'
  ,`data_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据量'
  ,`page_size` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '每页显示数量'
  ,`status_is` NOT NULL DEFAULT 'Y' COMMENT '状态'
  ,`menu_is` DEFAULT 'N' COMMENT '是否导航显示'
  ,`redirect_url` varchar(255) NOT NULL DEFAULT '' COMMENT '跳转地址'
  ,`display_type` NOT NULL DEFAULT 'list' COMMENT '显示方式'
  ,`template_list` varchar(100) NOT NULL DEFAULT '' COMMENT '列表模板'
  ,`template_page` varchar(100) NOT NULL DEFAULT '' COMMENT '单页模板'
  ,`template_show` varchar(100) NOT NULL DEFAULT '' COMMENT '内容页模板'
  ,`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '录入时间'
  ,`last_update_time` int(10) unsigned NOT NULL DEFAULT '0'
)
;

INSERT INTO `catalog`
  (`parent_id`, `catalog_name`, `catalog_name_second`, `catalog_name_alias`, `content`, `seo_title`, `seo_keywords`, `seo_description`, `attach_file`, `attach_thumb`, `sort_order`, `data_count`, `page_size`, `status_is`, `menu_is`, `redirect_url`, `display_type`, `template_list`, `template_page`, `template_show`, `create_time`, `last_update_time`)
VALUES
  (0, '音频', '', 'sound', '', '', '', '', NULL, NULL, 0, 0, 0, 'Y', 'N', '', 'list', 'list_text', 'list_page', 'show_post', 1405405394, 1617353762)
  ,(0, '图片', NULL, 'image', NULL, '', '', NULL, NULL, NULL, 0, 0, 0, 'Y', 'N', '', 'list', 'list_text', 'list_page', 'show_post', 1405405493, 0)
  ,(0, '音单', NULL, 'album', NULL, '', '', NULL, NULL, NULL, 0, 0, 0, 'Y', 'N', '', 'list', 'list_text', 'list_page', 'show_post', 1405405514, 1511163995)
  ,(26, '情感', '', 'entertainment_radio', '', '', '', '', NULL, NULL, 2, 0, 0, 'Y', 'N', '', 'list', 'list_text', 'list_page', 'show_post', 1406194677, 1625462871)
;

CREATE TABLE IF NOT EXISTS `m_attention_user` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`user_active` bigint NOT NULL COMMENT '关注者'
  ,`user_passtive` bigint NOT NULL COMMENT '被关注者'
  ,`time` bigint NOT NULL COMMENT '关注时间'
  ,`more` json DEFAULT NULL COMMENT '额外信息'
) COMMENT='关注'
;

CREATE TABLE IF NOT EXISTS `mowangsksoundseiy` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`name` varchar(40) NOT NULL COMMENT '声优名称'
  ,`icon` varchar(100) NOT NULL
  ,`profile` text NOT NULL
  ,`gender` tinyint unsigned NOT NULL
  ,`initial` tinyint unsigned NOT NULL COMMENT '首字母 0 代表其他, 1 到 26 代表 26 个字母'
  ,`birthyear` smallint unsigned NOT NULL
  ,`birthmonth` smallint NOT NULL
  ,`birthday` smallint NOT NULL
  ,`birthmonthday` smallint DEFAULT '0'
  ,`bloodtype` tinyint(1) NOT NULL COMMENT '血型'
  ,`career` tinyint(1) NOT NULL COMMENT '职业'
  ,`group` varchar(32) NOT NULL COMMENT '社团'
  ,`weibo` varchar(64) NOT NULL COMMENT '微博'
  ,`weiboname` varchar(64) NOT NULL COMMENT '微博名称'
  ,`baike` varchar(64) NOT NULL COMMENT '百科'
  ,`baikename` varchar(64) NOT NULL COMMENT '百科名称'
  ,`mid` bigint DEFAULT '0' COMMENT '用户 ID'
  ,`checked` smallint DEFAULT '0'
  ,`soundline1` int DEFAULT NULL COMMENT '声线1'
  ,`soundline2` int DEFAULT NULL COMMENT '声线2'
  ,`soundline3` int DEFAULT NULL COMMENT '声线3'
  ,`seiyalias` varchar(60) DEFAULT NULL
)
;

INSERT INTO `mowangsksoundseiy`
  (`id`, `name`, `icon`, `profile`, `gender`, `initial`, `birthyear`, `birthmonth`, `birthday`, `birthmonthday`, `bloodtype`, `career`, `group`, `weibo`, `weiboname`, `baike`, `baikename`, `mid`, `checked`, `soundline1`, `soundline2`, `soundline3`, `seiyalias`)
VALUES
  -- TestListSeiyIDsByUserID
  (1, '小野大辅', '201505/bcfd11fccce2fb449478af07819b705c120956.png', '测试', 1, 24, 0, 0, 0, 504, 0, 0, '', '', '', '', '', 346286, 1, 0, 0, 0, '')
;

CREATE TABLE IF NOT EXISTS `sound_video` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`sid` int NOT NULL COMMENT '音频 ID'
  ,`video_url` varchar(255) NOT NULL COMMENT '视频 URL 地址'
  ,`create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`videourl_360` varchar(125) NOT NULL DEFAULT '' COMMENT '360P 视频地址'
  ,`videourl_480` varchar(125) NOT NULL DEFAULT '' COMMENT '480P 视频地址'
  ,`videourl_720` varchar(125) NOT NULL DEFAULT '' COMMENT '720P 视频地址'
  ,`videourl_1080` varchar(125) NOT NULL DEFAULT '' COMMENT '1080P 视频地址'
  ,`attr` int NOT NULL DEFAULT '0' COMMENT '视频属性，比特位第一位为 1 时表示优先播放'
  ,`source` tinyint NOT NULL DEFAULT '0' COMMENT '视频来源，1：后台绑定；2：配音秀'
  ,`checked` tinyint NOT NULL DEFAULT '-1' COMMENT '视频审核状态，-3：转码失败；-1：待转码；0：待审核；1：审核通过'
  ,`more` text NOT NULL COMMENT '视频额外信息，格式为 json 字符串，size 字段存放不同视频大小，单位 Bytes'
)
;

INSERT INTO `sound_video`
  (`id`, `sid`, `video_url`, `create_time`, `modified_time`, `videourl_360`, `videourl_480`, `videourl_720`, `videourl_1080`, `attr`, `source`, `checked`, `more`)
VALUES
  (1, 44809, 'oss://video/test.mp4', 0, 0, '', '', '', '', 0, 1, 1, '')
;

INSERT INTO `authassignment`
  (`itemname`, `userid`, `bizrule`, `data`)
VALUES
  ("liveadmin", 9074509, NULL, "")
  ,("livefinance", 9074509, NULL, "")
  ,("livejudgement", 9074509, NULL, "")
;

CREATE TABLE IF NOT EXISTS `m_game_center` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT
  ,`url` VARCHAR(255) DEFAULT NULL COMMENT '跳转链接'
  ,`cover` VARCHAR(255) NOT NULL COMMENT '封面图'
  ,`icon` VARCHAR(255) NOT NULL COMMENT '图标'
  ,`name` VARCHAR(50) NOT NULL COMMENT '游戏名'
  ,`tag` VARCHAR(100) NOT NULL COMMENT '标签名，用半角逗号分隔'
  ,`intro` TEXT NOT NULL COMMENT '简介'
  ,`extended_fields` TEXT COMMENT '额外数据，JSON format'
  ,`sort` INTEGER NOT NULL DEFAULT '0' COMMENT '排序'
  ,`cooperative_mode` TINYINT NOT NULL DEFAULT '1' COMMENT '合作模式 1：SDK；2：CPS'
  ,`package_version` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '游戏包版本号'
  ,`create_time` INTEGER NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` INTEGER NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`publish_time` BIGINT NOT NULL DEFAULT '0' COMMENT '发布时间'
  ,`shutdown_time` BIGINT NOT NULL DEFAULT '0' COMMENT '下架时间（单位：秒），0：未下架'
)
;

INSERT INTO `m_game_center`
  (`id`, `url`, `cover`, `icon`, `name`, `tag`, `intro`, `extended_fields`, `shutdown_time`)
VALUES
  -- TestSearchCardParams_searchGameCard
  (1, 'http://test.com/mevent/102718', 'test_cover', 'test://test.jpg', 'test_name1', 'test_tag', 'test_intro', '{"download_open_time": 1999999999, "download_url": "test://app/MissEvan.apk", "package_name": "com.rastar.sndwz.maoer", "package_version_code": 3017, "card": {"cover": "test://game/202111/02/cover.jpg", "dark_cover": "test://game/202111/02/dark_cover.jpg", "btn_color": "#000000", "dark_btn_color": "#FFFFFF"}}', 0)
  ,(2, 'http://test.com/mevent/102718', 'test_cover', 'test://test.jpg', 'test_name1', 'test_tag', 'test_intro', '{"download_open_time": 1999999999, "download_url": "test://app/MissEvan.apk2", "package_name": "com.rastar.sndwz.maoer", "package_version_code": 3017, "card": {"cover": "test://game/202111/02/cover.jpg", "dark_cover": "test://game/202111/02/dark_cover.jpg", "btn_color": "#000000", "dark_btn_color": "#FFFFFF"}}', 1679500800)
;

CREATE TABLE IF NOT EXISTS `persona` (
  `id` int unsigned AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`equip_id` varchar(36) NOT NULL COMMENT '设备号'
  ,`persona` int unsigned DEFAULT '1' COMMENT '用户画像：1 ~ 8 位存模块画像（1. 大众 2. 普通男 3. 普通女 4. 腐女）；9 ~ 16 位猜你喜欢音推荐策略'
  ,`user_id` bigint DEFAULT NULL COMMENT '用户 ID'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid'
  ,`points` varchar(255) NOT NULL DEFAULT '' COMMENT 'JSON 字符串，存储画像分数，如：{"7":100,"9":1}'
)
;

CREATE TABLE IF NOT EXISTS `m_tag_sound_map`
(
  `tag_id` int NOT NULL COMMENT '标签 ID'
  ,`sound_id` int NOT NULL COMMENT '音频 ID'
)
;

INSERT INTO `m_tag_sound_map`
  (`tag_id`, `sound_id`)
VALUES
  -- TestMSound_listSameTagSoundIDs
  -- TestMSound_getOrSetRecommendSoundMap
  -- TestListRecommendSoundIDs
  (1, 100001)
  ,(2, 100001)
  ,(1, 100002)
  ,(2, 100002)
  ,(1, 100006)
  ,(3, 100003)
;

-- GRANT SELECT ON `m_user_vip` TO 'missevan_go'@'%'
CREATE TABLE IF NOT EXISTS `m_user_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）',
  `modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）',
  `vip_id` bigint NOT NULL COMMENT 'vip id',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `type` tinyint unsigned NOT NULL COMMENT 'vip 类型，4：点播会员',
  `start_time` bigint NOT NULL COMMENT '开始时间（秒级时间戳）',
  `end_time` bigint NOT NULL COMMENT '过期时间（秒级时间戳）'
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户的会员有效期';

INSERT INTO m_user_vip
  (id, create_time, modified_time, vip_id, user_id, type, start_time, end_time)
VALUES
  -- TestIsVip
  -- 由于创建测试数据和单测中使用当前时间可能有时间差，为了测试准确性，会员有效期起止时间设置为固定值
  (1, UNIX_TIMESTAMP() - 200, UNIX_TIMESTAMP() - 200, 1, 10, 4, 1723694194, 1726372594)
  ,(2, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 10, 4, 1726372594, 1728964594)
  ,(3, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 11, 4, 1726372594, 2999999999)
  -- TestAddDm
  ,(4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3457148, 4, 1726372594, 0)
  ,(5, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 100, 2, 3457149, 5, 1726372594, 2999999999)
 
;
