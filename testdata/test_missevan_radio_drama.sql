CREATE TABLE IF NOT EXISTS `radio_drama_dramainfo` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '剧集 ID'
  ,`name` varchar(60) DEFAULT NULL COMMENT '剧集名称'
  ,`cover` varchar(255) DEFAULT NULL COMMENT '剧集海报'
  ,`cover_color` int unsigned NOT NULL DEFAULT '12434877' COMMENT 'RGB 颜色值'
  ,`abstract` text COMMENT '剧集简介'
  ,`integrity` tinyint(1) NOT NULL COMMENT '完结度'
  ,`age` tinyint NOT NULL COMMENT '年代'
  ,`origin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '创作类型'
  ,`author` varchar(30) DEFAULT NULL COMMENT '原作者'
  ,`birthday` tinyint(1) NOT NULL DEFAULT '0' COMMENT '生日剧'
  ,`cv` varchar(30) DEFAULT NULL COMMENT '生日 CV'
  ,`ip` tinyint(1) NOT NULL DEFAULT '0' COMMENT '同人剧'
  ,`ipname` varchar(30) DEFAULT NULL COMMENT '原作标签'
  ,`type` int NOT NULL COMMENT '分类'
  ,`newest` varchar(30) DEFAULT '' COMMENT '更新至'
  ,`organization_id` int unsigned NOT NULL DEFAULT '0' COMMENT '社团 ID'
  ,`user_id` int NOT NULL COMMENT '所属用户 ID'
  ,`username` varchar(20) NOT NULL COMMENT '所属用户名'
  ,`checked` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态'
  ,`create_time` int NOT NULL COMMENT '发布时间'
  ,`lastupdate_time` int NOT NULL DEFAULT '0' COMMENT '最后编辑时间'
  ,`view_count` bigint NOT NULL DEFAULT '0' COMMENT '查看次数'
  ,`comment_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`catalog` int NOT NULL DEFAULT '0' COMMENT '分类 ID'
  ,`alias` varchar(60) DEFAULT NULL COMMENT '别名'
  ,`pay_type` int unsigned NOT NULL DEFAULT '0' COMMENT '付费类型，0: 免费；1: 单音付费；2: 剧集付费'
  ,`push` tinyint unsigned DEFAULT '0' COMMENT '新增单集是否过审推送'
  ,`refined` int unsigned NOT NULL DEFAULT '0' COMMENT '属性，比特位第一位为 1 标识擦边球，比特位第二位为 1 标识日本地区禁听，比特位第三位为 1 标识互动广播剧，比特位第四位为 1 标识无损音质广播剧'
  ,`police` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否报警'
  ,`ip_id` int unsigned NOT NULL DEFAULT '0' COMMENT '剧集所属 IP 的 ID'
  ,`subscription_num` int unsigned DEFAULT '0' COMMENT '订阅（追剧）人数'
  ,`show_revenue` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'UP 主是否能否查看该剧集的的收益（1 为可查看，0 为不可查看）'
  ,`name_letters` varchar(60) NOT NULL DEFAULT '' COMMENT '剧集名称每个字的首字母'
  ,`vip` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否为会员剧，0：否；1：是'
  ,KEY `IDX_SHOW_REVENUE` (`show_revenue`)
  ,KEY `IDX_CHECKED_USER_ID` (`checked`,`user_id`)
  ,KEY `IDX_TYPE` (`type`)
  ,KEY `IDX_CATALOG` (`catalog`)
  ,KEY `idx_checked_catalog_subscription_num` (`checked`,`catalog`,`subscription_num`)
  ,KEY `idx_checked_catalog_lastupdate_time` (`checked`,`catalog`,`lastupdate_time`)
  ,KEY `idx_nameletters` (`name_letters`)
  ,KEY `idx_vip` (`vip`)
) COMMENT='剧集信息表'
;

INSERT INTO `radio_drama_dramainfo`
  (`id`, `name`, `cover`, `cover_color`, `abstract`, `integrity`, `age`, `origin`, `author`, `birthday`, `cv`, `ip`, `ipname`, `type`, `newest`, `organization_id`, `user_id`, `username`, `checked`, `create_time`, `lastupdate_time`, `view_count`, `comment_count`, `catalog`, `alias`, `pay_type`, `push`, `refined`, `police`, `ip_id`, `subscription_num`, `show_revenue`, `name_letters`, `vip`)
VALUES
  (1, '测试更改修改查看次数（勿删）', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 1)
  ,(2, '测试获取剧集 1', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 12, 'test_user_name', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 3, 0, 0, 0, 1, 'ACS', 0)
  ,(3, '测试获取剧集 2', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 1234, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 3, 0, 0, 0, 1, 'ACS', 0)
  ,(4, '测试剧集状态为审核中', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 1234, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 3, 0, 0, 0, 1, 'ACS', 0)
  ,(5, '测试剧集状态为再审核', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 1234, 'test_user_name', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 7, 0, 0, 0, 1, 'ACS', 0)
  ,(6, '单元测试剧集我的王妃', '201807/11/ba854fe2e8496fa91eb66d180d36cc8d114247.png', 12434877, '<p>大来！</p>', 1, 0, 1, '十世2', 0, NULL, 0, NULL, 4, '风起', 1, 346286, 'InVinCiblezz', 1, 1460359592, 1547459576, 453026, 0, 89, '男人', 0, 0, 15, 0, 0, 3, 1, '', 0)
  ,(7, '冲撞', '201807/02/1cb96a5be3657e3647db40b8eb1e4d6b115143.png', 12434877, '<p>身边怎样的？</p>', 1, 1, 1, '晓春', 0, NULL, 0, NULL, 4, '第三期00', 0, 349524, '1231233334433', 1, 1460702276, 1543593600, 37559, 0, 89, NULL, 1, 0, 16, 0, 0, 2, 1, 'CZ', 1)
  ,(8, '辉子-单元测试用-勿动！!!', '201604/15/640fbc61fb1529e1cce932ba6dcce023065146.jpg', 12434877, '<p>原。</p>', 2, 1, 1, '筱禾', 0, NULL, 0, NULL, 4, '第五期', 1, 3462861, 'InVinCiblezzz', 1, 1460702561, 1530504799, 21927, 0, 89, NULL, 1, 0, 32, 0, 0, 4, 1, '', 0)
  ,(9, '望星辰之草原情殇', '201807/02/0eb8d2e59ef06c7b24bfeeb33cfd5b9f112930.jpg', 12434877, '<p>为命的主意……</p>', 2, 2, 1, '十世', 0, NULL, 0, NULL, 4, '第四期', 0, 346286, 'InVinCiblezzz', 1, 1460707133, 1530502200, 43632, 0, 89, NULL, 2, 0, 1, 0, 0, 5, 1, '', 0)
  ,(10, '测试获取剧集 3', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 12, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 3, 0, 1, 0, 1, 'ACS', 0)
  ,(11, '测试获取剧集 4', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者 2', 0, NULL, 0, NULL, 4, '', 0, 12, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 256, 0, 1, 0, 1, 'ACS', 0)
  -- TestGetDramaIDsBySoundIDs
  ,(12, '测试未过审剧集', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者', 0, NULL, 0, NULL, 4, '', 0, 12, 'test_user_name', 0, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 256, 0, 1, 0, 1, 'ACS', 0)
  ,(13, '测试过审剧集', 'test://201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, '测试剧集简介', 1, 1, 0, '测试原作者', 0, NULL, 0, NULL, 4, '', 0, 12, 'test_user_name', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 256, 0, 1, 0, 1, 'ACS', 0)
  -- TestSearchDrama
  ,(10623, '固定剧集测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 0)
  -- TestActionEmoteExclusive
  ,(52347, '专属表情包测试', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346286, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 0)
  -- TestListDramaInfoByIDs
  -- TestActionDramaListCollectList
  -- TestCollectListParams_collectList
  -- TestCollectListParams_fillDramalistInfo
  ,(52348, '测试剧单 1', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346287, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 0)
  ,(52349, '测试剧单 2', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346287, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 0)
  ,(52350, '测试剧单 3', '201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg', 12434877, NULL, 1, 1, 0, NULL, 0, NULL, 0, NULL, 4, '', 0, 346287, 'InVinCiblezzz', 1, 1629269616, 0, 4, 0, 89, NULL, 1, 0, 1, 0, 0, 0, 1, 'ACS', 0)
  --TestAddDm
  ,(70831, '花钱的剧', '202312/19/1515d57773e3603682334303906ef4fd112053.png', 2430985, '<p>------</p>', 3, 1, 0, '', 0, null, 0, null, 6, 'missevan.你离开了南京，从此没有人和我说话', 0, 3457148, '太阳落下去了', 1, 1702956052, 1702956289, 2074, 0, 89, null, 2, 1, 0, 0, 2928, 30, 1, 'HQDJ', 1)
  ,(70832, '花钱的剧', '202312/19/1515d57773e3603682334303906ef4fd112053.png', 2430985, '<p>------</p>', 3, 1, 0, '', 0, null, 0, null, 6, 'missevan.你离开了南京，从此没有人和我说话', 0, 3457149, '太阳落下去了', 1, 1702956052, 1702956289, 2074, 0, 89, null, 2, 1, 0, 0, 2928, 30, 1, 'HQDJ', 1)
;

CREATE TABLE IF NOT EXISTS `radio_drama_episode` (
  `id` int(10) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '单集 ID'
  ,`name` varchar(30) NOT NULL COMMENT '单集名称'
  ,`drama_id` int(10) NOT NULL COMMENT '剧集 ID'
  ,`sound_id` int(10) NOT NULL COMMENT '音频 ID'
  ,`date` int(10) NOT NULL DEFAULT '0' COMMENT '发表日期'
  ,`order` int(11) NOT NULL COMMENT '序号'
  ,`type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型, 0: 正片; 1: 访谈; 2: 音乐; 3: 更多资源'
  ,`pay_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费'
  ,`create_time` int(10) NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) NOT NULL COMMENT '更新时间'
  ,`subtitle` varchar(30) NOT NULL DEFAULT '' COMMENT '单集副标题'
  ,`vip` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '会员单集状态：0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集'
  ,UNIQUE KEY `sound_id` (`sound_id`)
  ,KEY `drama_id` (`drama_id`)
  ,KEY `idx_date` (`date`)
) COMMENT='单集信息表'
;

INSERT INTO `radio_drama_episode`
  (`id`, `name`, `drama_id`, `sound_id`, `date`, `order`, `type`, `pay_type`, `create_time`, `modified_time`, `subtitle`, `vip`)
VALUES
  -- TestFindEpisodeBySoundID
  (1, '测试单集名称（勿删）', 1, 1, UNIX_TIMESTAMP(), 1, 0, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 2)
  ,(2, '测试获取剧集信息（单集 1）', 2, 234, UNIX_TIMESTAMP(), 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(3, '测试获取剧集信息（单集 2）', 2, 235, UNIX_TIMESTAMP(), 2, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(4, '测试获取剧集信息（单集 3）', 2, 236, UNIX_TIMESTAMP(), 3, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(5, '测试获取剧集信息（单集 4）', 3, 234, UNIX_TIMESTAMP(), 3, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(6, '测试获取剧集信息（单集 5）', 3, 235, UNIX_TIMESTAMP(), 1, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(7, '测试获取剧集信息（单集 6）', 3, 236, UNIX_TIMESTAMP(), 2, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(8, '测试获取剧集信息（单集 7）', 3, 3674, UNIX_TIMESTAMP(), 2, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  -- TestDramaFeedNoticeParam_getNotice
  ,(9, '测试', 8, 3675, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  -- TestGetFeedDramas
  ,(10, '测试', 9, 3676, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  -- TestGetDramaIDsBySoundIDs
  ,(12, '测试', 12, 3677, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(13, '测试', 13, 3678, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 2)
  -- TestGetSoundLimitType
  ,(14, '测试', 13, 3679, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 2)
  ,(15, '测试', 13, 3680, UNIX_TIMESTAMP(), 0, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  -- TestAddDm
  ,(594386, 'missevan.16 New Romantics', 70831, 8814073,UNIX_TIMESTAMP(), 393216, 0, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
  ,(594387, 'missevan.16 New Romantics', 70832, 8814074,UNIX_TIMESTAMP(), 393216, 0, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '', 0)
;

CREATE TABLE IF NOT EXISTS `radio_drama_subscription` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id` int unsigned NOT NULL COMMENT '用户 ID'
  ,`drama_id` int unsigned NOT NULL COMMENT '剧集 ID'
  ,`create_time` int unsigned NOT NULL COMMENT '追剧时间'
  ,`is_top` tinyint NOT NULL DEFAULT '0' COMMENT '是否置顶'
  ,`update_time` int NOT NULL COMMENT '剧集更新时间'
  ,`saw_episode` varchar(30) NOT NULL DEFAULT '' COMMENT '上次观看的哪一期'
  ,`is_saw` tinyint NOT NULL DEFAULT '1' COMMENT '剧集的更新是否已查看（1 为已查看，0 为未查看）'
  ,`saw_episode_id` int NOT NULL DEFAULT '0' COMMENT '上次观看的哪一期对应的 episode ID'
  ,UNIQUE KEY `UK_DRAMA_ID_USER_ID` (`drama_id`,`user_id`)
  ,KEY `IDX_IS_TOP` (`is_top`)
  ,KEY `IDX_UPDATE_TIME` (`update_time`)
  ,KEY `IDX_USER_ID_UPDATE_TIME` (`user_id`,`update_time`)
  ,KEY `IDX_IS_SAW` (`is_saw`)
) COMMENT='剧集订阅表'
;

INSERT INTO `radio_drama_subscription`
  (`id`, `user_id`, `drama_id`, `create_time`, `is_top`, `update_time`, `saw_episode`, `is_saw`, `saw_episode_id`)
VALUES
  (1, 100, 10, 1481702748, 0, 1561531130, '', 0, 0)
  ,(2, 3013621, 10, 1481702748, 0, 1561531130, '', 0, 0)
  ,(83, 3010224, 712, 1481702748, 0, 1561531130, '', 0, 0)
  ,(84, 3010224, 6, 1481702748, 0, 1681833600, '', 0, 0)
  ,(85, 3010224, 7, 1481702748, 0, 1681747200, '', 0, 0)
  ,(86, 3010224, 8, 1481702748, 0, 1681833600, '', 0, 0)
  -- TestActionDramaListCollectList
  -- TestCollectListParams_collectList
  -- TestCollectListParams_fillDramalistInfo
  ,(87, 12, 52348, 1481702748, 0, 1681833600, '', 0, 0)
  ,(88, 12, 52349, 1481702748, 0, 1681833600, '', 0, 0)
  -- TestGetFeedDramas
  ,(89, 3010224, 9, 1481702748, 0, 1681920000, '测试', 1, 10)
;

CREATE TABLE IF NOT EXISTS `drama_saw_history` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id` int NOT NULL COMMENT '用户 ID'
  ,`drama_id` int NOT NULL COMMENT '剧集 ID'
  ,`episode_id` int NOT NULL COMMENT '上次观看的第几期 对应的 episode ID'
  ,`episode_name` varchar(30) NOT NULL DEFAULT '' COMMENT '上次观看的第几期'
  ,`create_time` int NOT NULL COMMENT '创建时间'
  ,`update_time` int NOT NULL COMMENT '更新时间'
  ,UNIQUE KEY `IDX_USER_ID_DRAMA_ID` (`user_id`,`drama_id`)
  ,KEY `IDX_USER_ID` (`user_id`)
  ,KEY `IDX_DRAMA_ID` (`drama_id`)
) COMMENT='剧集观看历史表'
;

CREATE TABLE IF NOT EXISTS `radio_drama_tag` (
  `id` int(6) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '标签 ID'
  ,`name` varchar(30) NOT NULL COMMENT '标签名称'
  ,`drama_num` mediumint(8) NOT NULL DEFAULT '0' COMMENT '广播剧数目'
  ,`visible_on_create` tinyint(4) NOT NULL DEFAULT '0' COMMENT '创建时可见'
  ,`manga_num` mediumint(8) NOT NULL DEFAULT '0'
  ,UNIQUE KEY `name` (`name`)
) COMMENT='剧集标签表'
;

CREATE TABLE IF NOT EXISTS `radio_drama_tag_drama` (
  `drama_id` int(10) NOT NULL COMMENT '剧集 ID'
  ,`tag_id` int(6) NOT NULL COMMENT '标签 ID'
  ,PRIMARY KEY (drama_id, tag_id)
) COMMENT='剧集标签和剧集 ID 关联表'
;

CREATE TABLE IF NOT EXISTS `radio_drama_price` (
  `drama_id` int(10) unsigned NOT NULL PRIMARY KEY COMMENT '剧集 ID'
  ,`price` int(10) unsigned NOT NULL COMMENT '价格'
  ,`rate` float unsigned NOT NULL COMMENT '分成比例'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`name` varchar(255) NOT NULL COMMENT '剧集名'
  ,`type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '1：单集价格，2：整剧价格'
  ,`rewardable` int(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否可进行打赏（0 为否，1 为是）'
) COMMENT='剧集价格表'
;

CREATE TABLE IF NOT EXISTS `checked_drama_review` (
  `id` int(10) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`drama_id` int(10) NOT NULL COMMENT '剧集 ID'
  ,`name` varchar(60) DEFAULT NULL COMMENT '剧集名称'
  ,`cover` varchar(255) DEFAULT NULL COMMENT '剧集海报'
  ,`cover_color` int(10) unsigned DEFAULT NULL COMMENT 'RGB 颜色值'
  ,`abstract` text COMMENT '剧集简介'
  ,`integrity` tinyint(1) DEFAULT NULL COMMENT '完结度'
  ,`age` tinyint(4) DEFAULT NULL COMMENT '年代'
  ,`origin` tinyint(1) DEFAULT NULL COMMENT '创作类型'
  ,`author` varchar(30) DEFAULT NULL COMMENT '原作者'
  ,`birthday` tinyint(1) DEFAULT NULL COMMENT '生日剧'
  ,`cv` varchar(30) DEFAULT NULL COMMENT '生日 CV'
  ,`ip` tinyint(1) DEFAULT NULL COMMENT '同人剧'
  ,`ipname` varchar(30) DEFAULT NULL COMMENT '原作标签'
  ,`type` int(10) DEFAULT NULL COMMENT '分类'
  ,`newest` varchar(30) DEFAULT NULL COMMENT '更新至'
  ,`organization_id` int(10) unsigned DEFAULT NULL COMMENT '社团 ID'
  ,`user_id` bigint(20) DEFAULT NULL COMMENT '所属用户 ID'
  ,`username` varchar(20) DEFAULT NULL COMMENT '所属用户名'
  ,`catalog` int(11) DEFAULT NULL COMMENT '分类 ID'
  ,`alias` varchar(60) DEFAULT NULL COMMENT '别名'
  ,`episodes` text COMMENT '添加或删除的单集'
  ,`tags` text COMMENT '标签'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '记录修改时间戳，单位：秒'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '记录删除时间戳（审核完成时间戳），单位：秒'
) COMMENT='剧集审核表'
;

INSERT INTO `checked_drama_review`
  (`id`, `drama_id`, `name`, `cover`, `cover_color`, `abstract`, `integrity`, `age`, `origin`, `author`, `birthday`, `cv`, `ip`, `ipname`, `type`, `newest`, `organization_id`, `user_id`, `username`, `catalog`, `alias`, `episodes`, `tags`)
VALUES
  -- TestActionDramaCheckStatus
  (1, 5, '测试再审剧集', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 12, 'InVinCiblezzz', NULL, NULL, '', '')
  ,(2, 2, '测试修改剧集名称 1', 'test://20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg', 1481702748, '测试修改剧集简介 1', 1, 2, 0, '测试修改原作者 1', 0, NULL, 0, NULL, 4, '', 0, 12, 'InVinCiblezzz', NULL, NULL, '{"create":[],"update":[{"episode_id":2,"sound_id":234,"name":"测试修改单集信息 1","type":0,"order":0,"date":1594252800},{"episode_id":3,"sound_id":235,"name":"测试修改单集信息 2","type":0,"order":1,"date":1594252800}],"delete":[{"episode_id":4,"sound_id":234,"name":"测试获取剧集信息（单集 3）","order":100000,"type":0,"date":1507607822}]}', '')
;

CREATE TABLE IF NOT EXISTS `radio_drama_derivative` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`ip_id` int unsigned NOT NULL COMMENT '推荐元素所属剧集 IP 的 ID'
  ,`user_id` bigint unsigned NOT NULL COMMENT '创建者用户 ID'
  ,`element_id` int unsigned NOT NULL DEFAULT '0' COMMENT '推荐元素 ID'
  ,`type` tinyint NOT NULL COMMENT '元素分类 1：商品；2：求签包；3：语音包；4：通用'
  ,`title` varchar(20) NOT NULL COMMENT '标题'
  ,`intro` varchar(15) DEFAULT '' COMMENT '一句话简介'
  ,`cover` varchar(255) NOT NULL COMMENT '封面图'
  ,`tag` varchar(4) DEFAULT '' COMMENT '标签'
  ,`url` varchar(255) DEFAULT '' COMMENT '链接'
  ,`sort` int NOT NULL DEFAULT '0' COMMENT '排序'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '最后修改时间'
) COMMENT='周边信息表'
;

INSERT INTO `radio_drama_derivative`
  (`id`, `ip_id`, `user_id`, `element_id`, `type`, `title`, `intro`, `cover`, `tag`, `url`, `sort`, `create_time`, `modified_time`)
VALUES
-- TestActionGetDervativesByIP
  (1, 1, 349524, 1, 1, '火锅火锅火锅火锅火锅', NULL, 'test://maoershop/images/201907/08/9ae2cef75c672bf3f6a813fcbfa5a90a124828.jpg', NULL, NULL, 3, 1562570856, 1562570856)
;

CREATE TABLE IF NOT EXISTS `radio_drama_ip` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `name` varchar(60) NOT NULL COMMENT '剧集所属 IPR 名',
  `seasons` varchar(2000) NOT NULL COMMENT 'JSON 季度信息',
  `user_id` bigint NOT NULL COMMENT '创建者 ID',
  `create_time` int unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int unsigned NOT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='剧集 IPR 信息表'
;

INSERT INTO `radio_drama_ip`
  (`id`, `name`, `seasons`, `user_id`, `create_time`, `modified_time`)
VALUES
  (1, 'test_ipr1', '', 10, 1562570856, 1562570856)
  ,(2, 'test_ipr2', '', 10, 1562570856, 1562570856)
;

CREATE TABLE IF NOT EXISTS `radio_drama_dramacopyright` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `drama_id` bigint NOT NULL DEFAULT '0' COMMENT '剧集 ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '剧集名称',
  `copyright_attribution` tinyint NOT NULL DEFAULT '0' COMMENT '版本归属，0：非自有版权；1：自有版权',
  `copyright_owner` tinyint NOT NULL DEFAULT '0' COMMENT '出品方，0：外部出品；1：自出品；2：联合出品',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '分发方式，0：非独播；1：独播',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
) COMMENT='版权信息'
;

INSERT INTO `radio_drama_dramacopyright`
  (`id`, `create_time`, `modified_time`, `drama_id`, `name`, `copyright_attribution`, `copyright_owner`, `type`, `delete_time`)
VALUES
-- TestFindCopyrightByDramaIDs
  (1, 1650424411, 1650424411, 1, '魔道祖师 第一季', 1, 1, 1, 0)
;

CREATE TABLE IF NOT EXISTS `radio_drama_corner_mark` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间',
  `drama_id` bigint NOT NULL DEFAULT 0 COMMENT '剧集 ID',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '角标类型，0：其他，详见 more 字段；1：原创；2：精选',
  `more` text NULL COMMENT '特殊角标配置信息'
) COMMENT='剧集角标信息'
;

INSERT INTO `radio_drama_corner_mark`
  (`id`, `create_time`, `modified_time`, `delete_time`, `drama_id`, `type`, `more`)
VALUES
-- TestFindCornerMarkByDramaIDs
  (1, 1650424411, 1650424411, 0, 1, 1, '')
-- TestDramaFeedNoticeParam_getNotice
  ,(2, 1650424411, 1650424411, 0, 8, 1, '')
;

CREATE TABLE IF NOT EXISTS `drama_corner_mark_style` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '1：已购；2：热播；3：原创；4：独播；5：首发；6：付费；7：精选',
  `text` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本',
  `text_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本背景色',
  `text_start_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本渐变起始颜色',
  `text_end_color` varchar(50) NOT NULL DEFAULT '' COMMENT '角标文本渐变结束颜色',
  `bg_start_color` varchar(50) NOT NULL DEFAULT '' COMMENT '渐变填充色色值',
  `bg_end_color` varchar(50) NOT NULL DEFAULT '' COMMENT '渐变填充色色值',
  `left_icon_url` varchar(255) NOT NULL DEFAULT '' COMMENT '左侧图标'
) COMMENT='剧集角标样式信息'
;

INSERT INTO `drama_corner_mark_style`
  (`id`, `create_time`, `modified_time`, `type`, `text`, `text_color`, `text_start_color`, `text_end_color`, `bg_start_color`, `bg_end_color`, `left_icon_url`)
VALUES
-- TestListAllMap
  (1, 1650424411, 1650424411, 1, '已购', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (2, 1650424411, 1650424411, 2, '热播', '#FFFFFF', '', '', '#E66465', '#E66465', 'test://cornermark/corner_mark.png'),
  (3, 1650424411, 1650424411, 3, '原创', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (4, 1650424411, 1650424411, 4, '独播', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (5, 1650424411, 1650424411, 5, '首发', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (6, 1650424411, 1650424411, 6, '付费', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (7, 1650424411, 1650424411, 7, '精选', '#FFFFFF', '', '', '#E66465', '#E66465', ''),
  (8, 1650424411, 1650424411, 8, '会员', '#FFFFFF', '#AAAAAA', '#AAAAAA', '#E66465', '#E66465', 'test://cornermark/corner_mark.png')
;

CREATE TABLE IF NOT EXISTS `radio_drama_weekly_hot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `drama_ids` text NOT NULL COMMENT '剧集 IDs，上周热播剧集按热度从高到低，用半角逗号分隔，最多有 40 个剧集 ID',
  `date` varchar(12) NOT NULL DEFAULT '' COMMENT '每周周一日期，举例：2006-01-02'
) COMMENT='每周热播剧'
;

INSERT INTO `radio_drama_weekly_hot`
  (`id`, `create_time`, `modified_time`, `drama_ids`, `date`)
VALUES
-- TestFindDramaIDsByDate
  (1, 1650424411, 1650424411, '1,2', '2022-10-03')
;

CREATE TABLE IF NOT EXISTS `radio_drama_episode_cv` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `episode_id` int NOT NULL COMMENT '单集ID',
  `cv_id` int NOT NULL COMMENT 'CVID',
  `character` varchar(30) NOT NULL COMMENT '出演角色',
  `main` tinyint NOT NULL DEFAULT '3' COMMENT '角色类型 1：主役；2：协役；3：龙套',
  `drama_id` int NOT NULL
)
;

INSERT INTO `radio_drama_episode_cv`
  (`id`, `episode_id`, `cv_id`, `character`, `main`, `drama_id`)
VALUES
-- TestFindHotDrama
  (1, 58, 1, '东方昊晔', 1, 1)
  ,(2, 58, 1, '晏明修', 2, 2)
  ,(3, 58, 1, '言子星', 3, 3)
-- TestListDramaCVByDramaIDs
  ,(4, 58, 1, '言子星', 1, 1)
  ,(5, 58, 1, '言子星', 1, 2)
  ,(6, 58, 4, '武熊悠山', 1, 4)
  ,(7, 58, 3, '倒吊男', 1, 4)
;

CREATE TABLE IF NOT EXISTS `radio_drama_subscribe_play_log_30day` (
  `id` bigint NOT NULL COMMENT 'ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`drama_id` bigint NOT NULL COMMENT '用户 30 天内收听过的订阅剧集 ID'
  ,`view_count` bigint NOT NULL COMMENT '30 天内播放次数'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '更新时间'
  ,PRIMARY KEY (`id`,`create_time`)
)
;

INSERT INTO `radio_drama_subscribe_play_log_30day`
  (`id`, `user_id`, `drama_id`, `view_count`, `create_time`, `modified_time`)
 VALUES
  (1, 3010224, 6, 2, 1681747200, 1681747200)
  ,(2, 3010224, 7, 2, 1681747200, 1681747200)
  ,(3, 3010224, 8, 20, 1681747200, 1681747200)
  ,(4, 3010224, 9, 30, 1681747200, 1681747200)
;
