CREATE TABLE IF NOT EXISTS `m_forbidden_words` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`create_time` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`element_id` BIGINT(20) NOT NULL COMMENT '元素 ID'
  ,`check_type` TINYINT(3) COMMENT '检查类型，1：剧集下评论，2：剧集下弹幕'
  ,`words` JSON NOT NULL COMMENT '屏蔽内容'
) COMMENT='元素屏蔽词表';

INSERT INTO `m_forbidden_words`
  (`id`, `element_id`, `check_type`, `words`)
VALUES
  (1, 39588, 1, '["公费追星","刘也","LY","瑾梨","JL","R1SE","你好tEsT"]')
  ,(2, 39588, 2, '["test","hello","fine","嘻嘻.*?哈哈"]')
;

CREATE TABLE IF NOT EXISTS `m_koc_keyword` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT  PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`keyword` varchar(100) NOT NULL DEFAULT '' COMMENT '关键词'
  ,`target_word` varchar(100) NOT NULL DEFAULT '' COMMENT '承接内容原名'
  ,`target_id` bigint(20) NOT NULL COMMENT '承接内容原名关联 ID'
  ,`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0：已停止；1：生效中'
  ,`attr` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'KOC 关键词属性：1 位是否计入 koc 报表（1 为是）'
  ,UNIQUE KEY `uk_keyword_status` (`keyword`,`status`)
  ,KEY `idx_modifieditime` (`modified_time`)
) COMMENT='KOC 关键词'
;

INSERT INTO `m_koc_keyword`
  (`id`, `create_time`, `modified_time`, `keyword`, `target_word`, `target_id`, `status`, `attr`)
VALUES
  (1, 1640751764, 1640751764, '傻狗男友', '漫画大佬和他的奶狗', 1, 1, 1)
  ,(2, 1640751764, 1640751764, '糖醋小排骨', '郁渔和他的偏执狂老公', 2, 0, 1)
  ,(3, 1640751764, 1640751764, '楚白之恋', '一啪即合', 3, 1, 1)
  ,(4, 1649819827, 1649819827, '貌合神离', '现世异闻1-3', 1386, 1, 1)
  ,(5, 1649819827, 1649819827, '1978', '不测的恋情', 1239, 1, 1)
;

CREATE TABLE IF NOT EXISTS `m_search_intervention_keyword` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`keyword` varchar(100) NOT NULL DEFAULT '' COMMENT '关键词'
  ,`target_id` bigint(20) NOT NULL COMMENT '被固定内容的关联 ID'
  ,`target_index` int(4) NOT NULL COMMENT '在搜索结果中的位置'
  ,`target_type` tinyint(4) NOT NULL COMMENT '干预搜索类型 0 音频, 1 UP 主, 2 音单, 4 声优, 5 剧集, 6 直播间, 7 特型, 8 频道, 9 弹幕, 10 专题卡'
  ,`status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态 0：已停止；1：生效中'
  ,`start_time` bigint(20) NOT NULL COMMENT '开始时间'
  ,`end_time` bigint(20) NOT NULL COMMENT '结束时间'
  ,KEY `idx_targettype_keyword_status_starttime` (`target_type`, `keyword`, `status`, `start_time`)
) COMMENT='搜索干预关键词'
;

INSERT INTO `m_search_intervention_keyword`
  (`id`, `create_time`, `modified_time`, `keyword`, `target_id`, `target_index`, `target_type`, `status`, `start_time`, `end_time`)
VALUES
  (1, 1657679280, 1657679280, '貌合神离', 1, 1, 5, 1, 1657679280, 4086556140)
  ,(2, 1657679280, 1657679280, '貌合神离', 2, 19, 5, 1, 1657679280, 4086556140)
  ,(3, 1657679280, 1657679280, '貌合神离', 3, 2, 5, 1, 1657679280, 0)
  ,(4, 1657679280, 1657679280, '貌合神离', 5, 4, 5, 1, 1657679280, 1658831747)
  ,(5, 1657679280, 1657679280, '貌合神离', 5, 4, 5, 1, 1658831747, 4086556140)
  ,(6, 1657679280, 1657679280, '貌合神离', 5, 4, 5, 0, 1657679280, 4086556140)
  ,(7, 1657679280, 1657679280, '测试', 10623, 2, 5, 1, 1657679280, 4086556140)
  ,(8, 1657679280, 1657679280, '测试干预清理', 3, 2, 5, 1, 1657679280, 4086556140)
  ,(9, 1657679280, 1657679280, '测试干预清理', 999999999, 3, 5, 1, 1657679280, 4086556140)
;

CREATE TABLE IF NOT EXISTS `m_user_history` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` BIGINT(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` BIGINT(20) NOT NULL COMMENT '修改时间'
  ,`delete_time` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '删除时间（-1 归档，0 未删除，大于 0 为具体的删除时间戳）'
  ,`user_id` BIGINT(20) NOT NULL COMMENT '用户 ID'
  ,`element_id` BIGINT(20) NOT NULL COMMENT '元素 ID'
  ,`element_type` TINYINT(3) NOT NULL COMMENT '元素类型（1 音频，2 剧集，3 直播间）'
  ,`access_time` BIGINT(20) NOT NULL COMMENT '最新访问时间（毫秒）'
  ,`more` JSON DEFAULT NULL COMMENT '更多详情'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户历史记录';

INSERT INTO `m_user_history`
  (`create_time`, `modified_time`, `delete_time`, `user_id`, `element_id`, `element_type`, `access_time`, `more`)
VALUES
  (1617797705, 1641814244, 0, 998855, 5, 2, 1641814260717, '{\"node\": {\"id\": 6564, \"completion\": 0.25}, \"last_play_sound\": {\"id\": 50}}')
  ,(1618974403, 1618974403, 1617186252, 3013091, 1200, 2, 1618974404397, '{\"node\": {\"id\": 1, \"completion\": 0.79}, \"last_play_sound\": {\"id\": 3674}}')
;

CREATE TABLE IF NOT EXISTS `m_radio_history` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` BIGINT(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` BIGINT(20) NOT NULL COMMENT '修改时间'
  ,`delete_time` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '删除时间（-1 归档，0 未删除，大于 0 为具体的删除时间戳）'
  ,`user_id` BIGINT(20) NOT NULL COMMENT '用户 ID'
  ,`sound_id` BIGINT(20) NOT NULL COMMENT '音频 ID'
  ,`access_time` BIGINT(20) NOT NULL COMMENT '最新访问时间（毫秒）'
  ,`more` JSON DEFAULT NULL COMMENT '更多详情'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户催眠专享播放历史记录';

CREATE TABLE IF NOT EXISTS `m_radio_sound` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间'
  ,`catalog_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '分类 ID'
  ,`sound_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '音频 ID'
  ,`title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题'
  ,`cover` varchar(125) NOT NULL DEFAULT '' COMMENT '封面图'
  ,`background_cover` varchar(125) NOT NULL DEFAULT '' COMMENT '播放页背景图'
  ,`background_video` varchar(125) NOT NULL DEFAULT '' COMMENT '播放页背景视频'
  ,`sort` bigint(20) NOT NULL DEFAULT '0' COMMENT '排序'
  ,`more` json DEFAULT NULL COMMENT '额外的信息，json 类型。其中 show_play_info 为 true 时表示播放页需要展示播放相关信息（UP 主信息及进度条）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='催眠专享音频表';

CREATE TABLE IF NOT EXISTS `user_ip_location` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`ip_location` varchar(50) NOT NULL COMMENT 'IP 属地'
  ,KEY `idx_deletetime` (`delete_time`)
  ,UNIQUE KEY `uk_userid_deletetime` (`user_id`, `delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户属地表';

INSERT INTO `user_ip_location`
  (`id`, `user_id`, `create_time`, `modified_time`, `delete_time`, `ip_location`)
VALUES
  (1, 12, 1676563200, 1676563200, 0, '山东')
  ,(2, 13, 1676563200, 1676563200, 0, '美国')
  ,(3, 14, 1676563200, 1676563200, 1676563200, '上海')
;

CREATE TABLE IF NOT EXISTS `m_emote_exclusive_element` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`element_type` tinyint NOT NULL COMMENT '元素类型。1：剧集'
  ,`package_id` bigint NOT NULL COMMENT '表情包 ID'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '解锁生效单位时间戳，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '解锁截止时间戳，单位：秒'
  ,`more` json DEFAULT NULL COMMENT '更多信息，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=97888451'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='表情包专属元素表';

INSERT INTO `m_emote_exclusive_element`
  (`id`, `create_time`, `modified_time`, `element_id`, `element_type`, `package_id`, `start_time`,`end_time`,`more`)
VALUES
  (1, 1676563200, 1676563200, 52347, 1, 2333, 0, 0, '{"unlock":false, "unlock_score": 233, "tip": "测试 tip"}')
;

CREATE TABLE IF NOT EXISTS `m_dramalist` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间，不为 0 时表示已删除。单位：秒'
  ,`title` varchar(50) NOT NULL COMMENT '剧单标题'
  ,`intro` varchar(100) NOT NULL DEFAULT '' COMMENT '剧单简介'
  ,`user_id` bigint NOT NULL COMMENT '剧单所属用户 ID'
  ,`collect_count` bigint NOT NULL DEFAULT '0' COMMENT '剧单被收藏数量'
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='剧单表';

INSERT INTO `m_dramalist`
  (`id`, `create_time`, `modified_time`, `delete_time`, `title`, `intro`, `user_id`, `collect_count`)
VALUES
  -- TestFindOne
  (1, 1676563200, 1676563200, 0, '测试剧单标题 1', '测试剧单简介 1', 12, 1)
  -- TestActionDramaListCollect
  ,(2, 1676563200, 1676563200, 0, '测试剧单标题 2', '测试剧单简介 2', 10, 0)
  -- TestNewCollectParams
  ,(3, 1676563200, 1676563200, 0, '测试剧单标题 3', '测试剧单简介 3', 10, 1)
  -- TestCollectParams_collect
  ,(4, 1676563200, 1676563200, 0, '测试剧单标题 4', '测试剧单简介 4', 10, 0)
  -- TestActionDramaListCancelCollect
  ,(5, 1676563200, 1676563200, 0, '测试剧单标题 5', '测试剧单简介 5', 10, 1)
  -- TestCollectParams_cancelCollect
  ,(6, 1676563200, 1676563200, 0, '测试剧单标题 6', '测试剧单简介 6', 10, 1)
  -- TestActionDramaListDetail
  ,(7, 1676563200, 1676563200, 0, '测试剧单标题 7', '测试剧单简介 7', 11, 1)
  ,(8, 1676563200, 1676563200, 0, '测试剧单标题 8', '测试剧单简介 8', 12, 1)
  -- TestActionDramaListCollectList
  -- TestCollectListParams_collectList
  -- TestCollectListParams_fillDramalistInfo
  -- TestCollectLister_List
  ,(10, 1676563200, 1676563200, 0, '测试剧单标题 10', '测试剧单简介 10', 10, 1)
  ,(11, 1676563200, 1676563200, 0, '测试剧单标题 11', '测试剧单简介 11', 10, 2)
  ,(12, 1676563200, 1676563200, 0, '测试剧单标题 12', '测试剧单简介 12', 10, 2)
  ,(13, 1676563200, 1676563200, 1676563200, '测试剧单标题 13', '测试剧单简介 13', 10, 2)
;

CREATE TABLE IF NOT EXISTS `m_dramalist_drama_map` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`dramalist_id` bigint NOT NULL COMMENT '剧单 ID'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`intro` varchar(100) NOT NULL DEFAULT '' COMMENT '剧集介绍（推荐）语'
  ,`sort` bigint NOT NULL DEFAULT '0' COMMENT '排序，值越小排在越前面'
  ,UNIQUE KEY `uk_dramalistid_dramaid` (`dramalist_id`, `drama_id`)
  ,KEY `idx_dramalistid_sort` (`dramalist_id`, `sort`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='剧单收录剧集表';

INSERT INTO `m_dramalist_drama_map`
  (`id`, `create_time`, `modified_time`, `dramalist_id`, `drama_id`, `intro`, `sort`)
VALUES
  -- TestActionDramaListDetail
  (1, 1676563200, 1676563200, 8, 1, '测试剧集介绍 1', 11)
  ,(2, 1676563200, 1676563200, 8, 2, '测试剧集介绍 2', 12)
  ,(3, 1676563200, 1676563200, 8, 3, '测试剧集介绍 3', 13)
  -- TestActionDramaListCollectList
  -- TestCollectListParams_collectList
  -- TestCollectListParams_fillDramalistInfo
  ,(10, 1676563200, 1676563200, 10, 52348, '测试剧集介绍 10', 1)
  ,(11, 1676563200, 1676563200, 10, 52349, '测试剧集介绍 11', 2)
  ,(12, 1676563200, 1676563200, 11, 52349, '测试剧集介绍 12', 1)
  ,(13, 1676563200, 1676563200, 12, 52350, '测试剧集介绍 13', 1)
;

CREATE TABLE IF NOT EXISTS `m_collect_dramalist` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`dramalist_id` bigint NOT NULL COMMENT '剧单 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,UNIQUE KEY `uk_userid_dramalistid` (`user_id`, `dramalist_id`)
  ,KEY `idx_dramalistid` (`dramalist_id`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户收藏剧单表';

INSERT INTO `m_collect_dramalist`
  (`id`, `create_time`, `modified_time`, `dramalist_id`, `user_id`)
VALUES
  -- TestIsCollected
  (1, 1676563200, 1676563200, 1, 1)
  -- TestNewCollectParams
  ,(2, 1676563200, 1676563200, 3, 2)
  -- TestActionDramaListCancelCollect
  ,(3, 1676563200, 1676563200, 5, 1)
  -- TestCollectParams_cancelCollect
  ,(4, 1676563200, 1676563200, 6, 1)
  -- TestDramalistDetailParams_getUserRelationDramalist
  ,(5, 1676563200, 1676563200, 8, 3010224)
  -- TestActionDramaListCollectList
  -- TestCollectListParams_collectList
  -- TestCollectListParams_fillDramalistInfo
  -- TestCollectLister_List
  ,(6, 1676563200, 1676563200, 10, 820592)
  ,(7, 1676563201, 1676563201, 11, 820592)
  ,(8, 1676563202, 1676563202, 12, 820592)
  ,(9, 1676563203, 1676563203, 13, 820592)
;

CREATE TABLE IF NOT EXISTS `m_avatar_frame` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`name` varchar(20) NOT NULL COMMENT '头像框名称'
  ,`intro` varchar(255) NOT NULL DEFAULT '' COMMENT '头像框简介，支持 HTML'
  ,`frame` varchar(125) NOT NULL COMMENT '头像框地址'
  ,`start_time` bigint DEFAULT '0' COMMENT '头像框可用开始时间。单位：秒'
  ,`end_time` bigint DEFAULT '0' COMMENT '头像框可用截止时间，为 0 时表示永久有效。单位：秒'
  ,`expire_duration` bigint DEFAULT '0' COMMENT '头像框获得后过期时长，为 0 时表示不会过期。单位：秒'
  ,`icon` varchar(125) DEFAULT NULL COMMENT '头像框 icon'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户（主站）头像框';

INSERT INTO `m_avatar_frame`
  (`id`, `create_time`, `modified_time`, `name`, `intro`, `frame`, `start_time`, `end_time`, `expire_duration`, `icon`)
VALUES
  -- FindValidOne
  (1, 1676563200, 1676563200, "测试头像框", "简介", "test://test.webp", 0, 0, 0, "test://icon/test.webp")
  ,(2, 1676563200, 1676563200, "测试头像框", "简介", "test://test.webp", 0, 100, 0, "test://icon/test.webp")
  -- TestActionSendAvatarFrame
  ,(3, 1676563200, 1676563200, "测试头像框", "简介", "test://test.webp", 0, 0, 86400000, "test://icon/test.webp")
  -- TestListWearingByUserIDs
  ,(4, 1676563200, 1676563200, "测试头像框 4", "简介 4", "test://test_4.webp", 0, 0, 0, "test://icon/test_4.webp")
  ,(5, 1676563200, 1676563200, "测试头像框 5", "简介 5", "test://test_5.webp", 0, 0, 0, "test://icon/test_5.webp")
;

CREATE TABLE IF NOT EXISTS `m_user_avatar_frame_map` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`avatar_frame_id` bigint NOT NULL COMMENT '头像框 ID，m_avatar_frame 表主键'
  ,`expire_time` bigint DEFAULT '0' COMMENT '头像框过期时间，为 0 时表示永久有效。单位：秒'
  ,`status` tinyint DEFAULT '0' COMMENT '使用状态。0: 未佩戴；1: 佩戴中'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与头像框关系表';

INSERT INTO `m_user_avatar_frame_map`
  (`id`, `create_time`, `modified_time`, `user_id`, `avatar_frame_id`, `expire_time`, `status`)
VALUES
  -- TestListWearingByUserIDs
  (1, 1676563200, 1676563200, 1, 4, 0, 1)
  ,(2, 1676563200, 1676563200, 2, 5, 0, 1)
;

CREATE TABLE IF NOT EXISTS `drama_revenue_reviewer_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`mobile_encrypt` varchar(255) NOT NULL COMMENT '手机号码（加密）'
  ,`region` smallint unsigned NOT NULL COMMENT '国际电话区号'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,UNIQUE KEY `uk_userid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集收益后台用户信息表'
;

-- GRANT SELECT ON m_user_config TO 'missevan_go'@'%'
CREATE TABLE IF NOT EXISTS `m_user_config` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间。单位：秒'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT '设备号'
  ,`app_config` json NOT NULL COMMENT '用户 APP 选项设置，personalized_recommend 表示个性化推荐开关，0 为关闭，1 为开启'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置项信息表'
;

-- GRANT SELECT ON special_search_items TO 'missevan_go'@'%'
CREATE TABLE `special_search_items` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
  ,`title` varchar(100) NOT NULL DEFAULT '' COMMENT '主标题'
  ,`search_words` varchar(255) NOT NULL DEFAULT '' COMMENT '搜索词，多个搜索词用半角逗号分隔'
  ,`drama_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '关联剧集，多个剧集 ID 用半角逗号分隔'
  ,`url` varchar(255) NOT NULL DEFAULT '' COMMENT '链接'
  ,`ip_id` bigint NOT NULL DEFAULT '0' COMMENT '关联周边 IP ID'
  ,`background` varchar(255) NOT NULL DEFAULT '' COMMENT '背景图'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '上线时间'
  ,`color` varchar(50) NOT NULL DEFAULT '' COMMENT '主题色'
  ,`status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 0：已停用；1：启用'
  ,`type` tinyint NOT NULL DEFAULT '0' COMMENT '类型 0：搜索特型库；1：搜索专题卡；2：UP 主卡片'
  ,`more` text COMMENT '额外数据，opensearch 暂不支持 json 字段同步，暂时先使用 text 类型'
  ,KEY `idx_modifieditime` (`modified_time`)
  ,KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊搜索结果表'
;
