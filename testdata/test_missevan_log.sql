-- 仅单测使用，不需要赋权
CREATE TABLE `collection` (
  `id` bigint NOT NULL
  ,`name` varchar(45) DEFAULT NULL
  ,`create_time` bigint DEFAULT NULL
)
;

-- GRANT INSERT ON m_admin_logger TO 'missevan_go'@'%'
CREATE TABLE `m_admin_logger` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`catalog` smallint unsigned DEFAULT NULL
  ,`channel_id` int unsigned NOT NULL DEFAULT '0' COMMENT '频道id'
  ,`url` varchar(255) NOT NULL DEFAULT '' COMMENT 'url'
  ,`intro` text COMMENT '操作'
  ,`ip` varchar(50) NOT NULL DEFAULT '127.0.0.1' COMMENT '操作ip'
  ,`create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '操作时间'
)
;

-- GRANT INSERT ON listen_log TO 'missevan_go'@'%'
CREATE TABLE `listen_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`sound_id` int unsigned NOT NULL COMMENT '音频 ID'
  ,`user_id` bigint DEFAULT NULL COMMENT '用户 ID'
  ,`equip_id` varchar(50) NOT NULL COMMENT '设备号'
  ,`duration` int unsigned NOT NULL COMMENT '音频时长'
  ,`play_duration` int unsigned NOT NULL COMMENT '播放时长（单位：毫秒）'
  ,`stay_duration` int unsigned NOT NULL COMMENT '播放页停留时长（单位：毫秒）'
  ,`drama_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属剧集 ID'
  ,`catalog_id` int unsigned NOT NULL COMMENT '音频分类 ID'
  ,`play_time` int unsigned NOT NULL COMMENT '进行播放时的时间戳（单位：秒）'
  ,`ip` varchar(50) NOT NULL COMMENT 'IP'
  ,`address` json NOT NULL COMMENT '地址'
  ,`ua` varchar(256) DEFAULT NULL COMMENT '设备信息'
  ,`os` tinyint unsigned DEFAULT NULL COMMENT '设备平台类型。1：Android；2：iOS'
  ,`is_payer` tinyint unsigned DEFAULT NULL COMMENT '是否有过充值（消费）'
  ,`loop_times` smallint unsigned DEFAULT '1' COMMENT '重复播放时播放次数'
  ,`online` tinyint unsigned DEFAULT NULL COMMENT '开始播放时是否设备是否联网。1：是；0：否'
  ,`attribute` tinyint NOT NULL DEFAULT '0' COMMENT '属性，比特位第一位为 1 时表示已被软删除'
  ,`referer` json NOT NULL COMMENT '进入音频播放页的来源'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '最后修改时间'
  ,`country` varchar(2) COMMENT '国家'
  ,`province` varchar(120) COMMENT '省'
  ,`city` varchar(120) COMMENT '市'
  ,`referer_name` varchar(50) COMMENT '页面名称'
  ,`referer_source` varchar(50) COMMENT '来源'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid'
  ,`channel` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道标识'
)
;

-- GRANT INSERT ON m_app_log TO 'missevan_go'@'%'
CREATE TABLE `m_app_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint DEFAULT '0' COMMENT '更新时间'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`equip_id` varchar(255) DEFAULT '' COMMENT '设备 ID'
  ,`upload_type` tinyint DEFAULT '0' COMMENT '上报类型 1：反馈时发送日志，2：主动拉取上报'
  ,`log_url` varchar(255) DEFAULT '' COMMENT '日志的下载链接'
  ,`buvid` varchar(255) NOT NULL DEFAULT '' COMMENT '设备号（buvid）'
  ,`os` tinyint NOT NULL DEFAULT '0' COMMENT '设备类型'
  ,`version` varchar(50) NOT NULL DEFAULT '' COMMENT '客户端版本号'
  ,`user_agent` varchar(100) NOT NULL DEFAULT '' COMMENT '用户代理 user-agent'
  ,`task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务 ID'
)
;

-- GRANT INSERT ON events_tracking_log TO 'missevan_go'@'%'
CREATE TABLE `events_tracking_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`user_id` bigint NOT NULL COMMENT '登录用户 ID'
  ,`event_id` varchar(100) NOT NULL COMMENT '事件 ID'
  ,`event_category` tinyint NOT NULL COMMENT '事件类型：1，PAGEVIEW; 2，CLICK; 3，EXPOSURE'
  ,`event_id_from` varchar(100) NOT NULL COMMENT '来源页面'
  ,`load_type` tinyint NOT NULL COMMENT '浏览类型：0，正常浏览；1，回退浏览'
  ,`page_type` tinyint NOT NULL COMMENT '页面类型：1，native；2，手机网页版'
  ,`pv_start` bigint NOT NULL COMMENT '页面进入时间'
  ,`pv_end` bigint NOT NULL COMMENT '页面离开时间'
  ,`duration` bigint NOT NULL COMMENT '页面展现的时长'
  ,`app_id` tinyint DEFAULT '0' COMMENT 'app_id'
  ,`fts` bigint DEFAULT '0' COMMENT '客户端首次启动的时间戳，为毫秒级时间戳'
  ,`channel` varchar(100) NOT NULL COMMENT '渠道标识'
  ,`version` varchar(50) NOT NULL COMMENT '客户端版本号'
  ,`os` tinyint NOT NULL COMMENT '设备类型'
  ,`equip_id` varchar(100) NOT NULL COMMENT '设备码'
  ,`network` tinyint NOT NULL COMMENT '网络状态'
  ,`ip` varchar(50) NOT NULL COMMENT 'IP'
  ,`extended_fields` text COMMENT '私有字段，JSON 字符串'
  ,`ctime` bigint NOT NULL COMMENT '事件发生时间，为毫秒级时间戳'
  ,`upload_time` bigint NOT NULL COMMENT '客户端上传时间，为毫秒级时间戳'
  ,`create_time` bigint NOT NULL COMMENT '事件创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '写入数据库的时间'
  ,`device_id` json DEFAULT NULL COMMENT '设备信息，为 JSON 字符串'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid'
  ,`uuid` varchar(100) NOT NULL DEFAULT '' COMMENT 'UUID'
  ,`ua` varchar(500) NOT NULL DEFAULT '' COMMENT '客户端原始 UA'
)
;
