CREATE TABLE IF NOT EXISTS `m_user_like_danmaku` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` int NOT NULL COMMENT '创建时间',
  `modified_time` int NOT NULL COMMENT '修改时间',
  `delete_time` int NOT NULL DEFAULT '0' COMMENT '删除时间，若不为 0，则表示已被软删除',
  `user_id` int NOT NULL COMMENT '点赞用户 ID',
  `danmaku_id` int NOT NULL COMMENT '弹幕 ID',
  `element_id` int NOT NULL COMMENT '弹幕所属音频 ID',
  `element_type` tinyint NOT NULL COMMENT '弹幕所属元素类型，1：音频；2：互动剧节点'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

CREATE UNIQUE INDEX uk_userid_danmakuid_deletetime
on `m_user_like_danmaku` (user_id, danmaku_id, delete_time);

CREATE TABLE IF NOT EXISTS `m_comment_cooldown` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint(20) NOT NULL DEFAULT '0'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  -- 防止以后可能会有不同冷静期级别的情况
  ,`start_time` bigint(20) NOT NULL DEFAULT '0'
  ,`end_time` bigint(20) NOT NULL DEFAULT '0'
);

CREATE TABLE IF NOT EXISTS `m_sound_comment` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '弹幕的主键，自增',
  `sound_id` int NOT NULL COMMENT '音频 ID',
  `user_id` int NOT NULL COMMENT '用户 ID',
  `text` text NOT NULL COMMENT '弹幕内容',
  `stime` varchar(11) NOT NULL COMMENT '弹幕出现的时间点',
  `size` int NOT NULL COMMENT '弹幕字体大小',
  `color` int NOT NULL COMMENT '弹幕颜色',
  `mode` int NOT NULL COMMENT '弹幕显示方式，1：滑动；4：底部；5：顶部',
  `date` int NOT NULL COMMENT '发送时间',
  `pool` int NOT NULL COMMENT '弹幕等级，50：劣质弹幕；80：灌水弹幕；160：常规弹幕；195：高质弹幕，198：优质弹幕；200：官方弹幕；240：字幕',
  `like_num` int NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP',
  `ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
);

INSERT INTO `m_sound_comment`
(`id`, `sound_id`, `user_id`, `text`, `stime`, `size`, `color`,`mode`, `date`, `pool`, `like_num`)
VALUES
(44809, 20232, 67130, '啦啦啦 啦啦啦啦啦……', '149.51', 25, 16776960, 4, 1458545816, 195, 0)
;

CREATE TABLE IF NOT EXISTS `sound_comment` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '评论的主键，自增'
  ,`comment_content` text NOT NULL COMMENT '评论的具体内容'
  ,`c_type` int NOT NULL COMMENT '评论的类型，1：单音评论，2：音单评论，3：新闻评论，4：频道评论，6：专题评论，7：活动评论，8：语音卡评论，9：运势卡评论'
  ,`element_id` int NOT NULL COMMENT '对象对应的 ID'
  ,`ctime` int NOT NULL COMMENT '创建评论时间'
  ,`userid` int NOT NULL COMMENT '上传者 ID'
  ,`username` varchar(20) NOT NULL COMMENT '上传者用户名'
  ,`sub_comment_num` int NOT NULL DEFAULT '0' COMMENT '子评论数'
  ,`like_num` int NOT NULL DEFAULT '0' COMMENT '点赞数（该字段为保留字段）'
  ,`dislike_num` int NOT NULL DEFAULT '0' COMMENT '点踩数'
  ,`control_num` int DEFAULT '0' COMMENT '热力数'
  ,`floor` int NOT NULL
  ,`checked` int NOT NULL DEFAULT '1' COMMENT '评论属性，-1：软删除；1：正常评论；2：评论违规'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
  ,`score_ratio` bigint NOT NULL DEFAULT 130 COMMENT '评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新评论基础分系数」更新）'
  ,`score_dislike_proportion_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分点踩系数，默认 1e2，生成评论时 Redis.Get(comment:dislike:proportion)'
  ,`score_blacklist_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2'
  ,`score` bigint COMMENT '评论得分（SQLite 不支持虚拟列，这里把公式删除），计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540'
  ) --ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='该表为评论表（暂时不包括新闻站评论）'
;

CREATE TABLE IF NOT EXISTS `delete_sound_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增'
  ,`comment_id` bigint NOT NULL DEFAULT '0' COMMENT '评论 ID'
  ,`comment_content` text NOT NULL COMMENT '评论的具体内容'
  ,`c_type` int NOT NULL COMMENT '评论的类型，1：单音评论，2：音单评论，3：新闻评论，4：频道评论，6：专题评论，7：活动评论，8：语音卡评论，9：运势卡评论'
  ,`element_id` int NOT NULL COMMENT '对象对应的 ID'
  ,`ctime` int NOT NULL COMMENT '创建评论时间'
  ,`userid` int NOT NULL COMMENT '上传者 ID'
  ,`username` varchar(20) NOT NULL COMMENT '上传者用户名'
  ,`sub_comment_num` int NOT NULL DEFAULT '0' COMMENT '子评论数'
  ,`like_num` int NOT NULL DEFAULT '0' COMMENT '点赞数（该字段为保留字段）'
  ,`dislike_num` int NOT NULL DEFAULT '0' COMMENT '点踩数'
  ,`control_num` int DEFAULT '0' COMMENT '热力数'
  ,`floor` int NOT NULL
  ,`checked` int NOT NULL DEFAULT '1' COMMENT '评论属性，-1：软删除；1：正常评论；2：评论违规'
  ,`delete_type` tinyint NOT NULL  COMMENT '删除类型，0：用户删除，包含 UP 主删除自己的评论；1：UP 主删除；2：管理员删除；3：违禁及涉政删除'
  ,`delete_time` bigint NOT NULL COMMENT '删除时间'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
  ,`score_ratio` bigint NOT NULL DEFAULT 130 COMMENT '评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新评论基础分系数」更新）'
  ,`score_dislike_proportion_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分点踩系数，默认 1e2，生成评论时 Redis.Get(comment:dislike:proportion)'
  ,`score_blacklist_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2'
  ,`score` bigint COMMENT '评论得分（SQLite 不支持虚拟列，这里把公式删除），计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540'
) COMMENT='该表为评论归档表（暂时不包括新闻站评论）'
;

INSERT INTO `sound_comment`
  (`id`, `comment_content`, `c_type`, `element_id`, `ctime`, `userid`, `username`, `floor`)
VALUES
  (1, '测试父评论', 1, 233, 1624530554, 12, '零月', 0);

  INSERT INTO `sound_comment`
  (`id`, `comment_content`, `c_type`, `element_id`, `ctime`, `userid`, `username`, `floor`)
VALUES
  (2, '测试评论', 1, 3678, 1624530554, 12, '零月', 0);

CREATE TABLE IF NOT EXISTS `sound_sub_comment` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '子评论的主键，自增'
  ,`comment_content` text NOT NULL COMMENT '评论的具体内容'
  ,`comment_id` int NOT NULL COMMENT '对应的评论 ID'
  ,`ctime` int NOT NULL COMMENT '创建评论时间'
  ,`userid` int NOT NULL COMMENT '上传者 ID'
  ,`username` varchar(20) NOT NULL COMMENT '上传者用户名'
  ,`like_num` int NOT NULL DEFAULT '0' COMMENT '点赞数（该字段为保留字段）'
  ,`dislike_num` int NOT NULL DEFAULT '0' COMMENT '点踩数'
  ,`floor` int NOT NULL DEFAULT '1'
  ,`checked` int NOT NULL DEFAULT '1' COMMENT '评论属性，-1：软删除；1：正常评论；2：评论违规'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
  ,`score_ratio` bigint NOT NULL DEFAULT 130 COMMENT '评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新子评论基础分系数」更新）'
  ,`score_dislike_proportion_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分点踩系数，默认 1e2，生成评论时 Redis.Get(comment:dislike:proportion)'
  ,`score_blacklist_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2'
  ,`score` bigint COMMENT '评论得分（SQLite 不支持虚拟列，这里把公式删除），计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540'
  ) --ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='该表为子评论表'
;

CREATE TABLE `delete_sound_sub_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增'
  ,`comment_id` bigint NOT NULL COMMENT '评论 ID'
  ,`comment_content` text NOT NULL COMMENT '评论的具体内容'
  ,`sub_comment_id` bigint  NOT NULL COMMENT '子评论 ID'
  ,`ctime` int(10) unsigned NOT NULL COMMENT '创建评论时间'
  ,`userid` bigint(20) NOT NULL COMMENT '发表子评论用户 ID'
  ,`username` varchar(20) NOT NULL COMMENT '上传者用户名，mowangskuser 中的 username'
  ,`like_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点赞数（该字段为保留字段）'
  ,`dislike_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点踩数'
  ,`floor` int(11) NOT NULL DEFAULT '1' COMMENT '楼层'
  ,`checked` tinyint(3) NOT NULL DEFAULT '1' COMMENT '评论属性，-1：软删除；1：正常评论；2：评论违规'
  ,`delete_type` tinyint unsigned NOT NULL COMMENT '删除类型，0：用户删除，包含 UP 主删除自己的子评论；1：UP 主删除；2：管理员删除；3：违禁及涉政删除'
  ,`delete_time` bigint NOT NULL COMMENT '删除时间'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
  ,`score_ratio` bigint NOT NULL DEFAULT 130 COMMENT '评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新子评论基础分系数」更新）'
  ,`score_dislike_proportion_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分点踩系数，默认 1e2，生成评论时 Redis.Get(comment:dislike:proportion)'
  ,`score_blacklist_ratio` bigint NOT NULL DEFAULT 100 COMMENT '评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2'
  ,`score` bigint COMMENT '评论得分（SQLite 不支持虚拟列，这里把公式删除），计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540'
) COMMENT='该表为子评论归档表'
;

CREATE TABLE IF NOT EXISTS `delete_m_sound_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`comment_id` bigint NOT NULL DEFAULT 0 COMMENT '弹幕 ID'
  ,`sound_id` bigint(20) NOT NULL COMMENT '音频 ID'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`text` text NOT NULL COMMENT '弹幕内容'
  ,`stime` varchar(11) NOT NULL COMMENT '弹幕出现的时间点'
  ,`size` int NOT NULL COMMENT '弹幕字体大小'
  ,`color` int NOT NULL COMMENT '弹幕颜色'
  ,`mode` int NOT NULL COMMENT '弹幕显示方式，1：滑动；4：底部；5：顶部'
  ,`date` bigint(20) NOT NULL COMMENT '发送时间'
  ,`pool` int NOT NULL COMMENT '弹幕等级，50：劣质弹幕；80：灌水弹幕；90：低质弹幕；160：常规弹幕；195：高质弹幕，198：优质弹幕；200：官方弹幕；240：字幕'
  ,`checked` int DEFAULT '1' COMMENT '弹幕属性 1：正常弹幕，2 违规弹幕（仅发送者可以看到）'
  ,`like_num` int NOT NULL DEFAULT 0 COMMENT '点赞数量'
  ,`delete_type` tinyint NOT NULL  COMMENT '删除类型，0：用户删除，包含 UP 主删除自己的弹幕；1：UP 主删除；2：管理员删除；3：违禁及涉政删除'
  ,`delete_time` bigint(20) DEFAULT 0 COMMENT '删除时间'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 IP'
  ,`ip_detail` json DEFAULT NULL COMMENT '用户 IP 详情'
) COMMENT='音频弹幕归档表'
;

CREATE TABLE IF NOT EXISTS `black_user` (
  `big_id` bigint NOT NULL COMMENT '数值更大的用户 ID'
  ,`small_id` bigint NOT NULL COMMENT '数值更小的用户 ID'
  ,`status` tinyint NOT NULL COMMENT 'status 二进制运算：1 位：small_id 拉黑 big_id；2 位：big_id 拉黑 small_id'
  ,`create_time` bigint DEFAULT NULL COMMENT '创建时间'
  ,`modified_time` bigint DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY(`big_id`, `small_id`)
) --ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO `black_user`
  (`small_id`, `big_id`, `status`, `create_time`, `modified_time`)
VALUES
  (99, 199, 2, 1633622400, 1633622400)
  ,(99, 200, 1, 1633622400, 1633622400)
  ,(20, 99, 2, 1633622400, 1633622400)
  ,(21, 99, 2, 1633622400, 1633622400)
  ,(22, 99, 3, 1633622400, 1633622400)
  -- TestActionBlockStatusList
  -- TestFindBlockUserIDs
  ,(19001, 20240480, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(19001, 20240481, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(19001, 20240482, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(18001, 19001, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(18002, 19001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(18003, 19001, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `m_comment_black_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
    ,`username` varchar(20) NOT NULL COMMENT '用户名'
    ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
    ,`modified_time` int unsigned NOT NULL COMMENT '修改时间'
)
;
INSERT INTO `m_comment_black_user`
  (`id`, `user_id`, `username`, `create_time`, `modified_time`)
VALUES
  (1, 999999999, '测试用户', 1633622400, 1633622400)
  -- TestActionAddSubComment
  ,(2, 12, '测试用户2', 1633622400, 1633622400)
;

CREATE TABLE IF NOT EXISTS `m_danmaku` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`element_id` bigint NOT NULL COMMENT '元素 ID'
  ,`element_type` tinyint unsigned NOT NULL COMMENT '元素类型'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`text` text NOT NULL COMMENT '内容'
  ,`stime` varchar(11) NOT NULL COMMENT '出现时间'
  ,`size` tinyint unsigned NOT NULL COMMENT '字号'
  ,`color` int unsigned NOT NULL COMMENT '颜色'
  ,`mode` tinyint unsigned NOT NULL COMMENT '模式：1 滚动、2 顶部、3 底部'
  ,`pool` tinyint unsigned NOT NULL DEFAULT '160' COMMENT '弹幕等级'
  ,`create_time` bigint NOT NULL COMMENT '添加时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`like_num` bigint NOT NULL DEFAULT '0' COMMENT '点赞数量'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='弹幕表';
;

CREATE TABLE IF NOT EXISTS `m_allowlist_emoji` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '添加时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`emoji` varchar(10) NOT NULL COMMENT 'emoji 表情'
  ,`unicode_emoji` varchar(125) NOT NULL COMMENT 'emoji unicode 编码'
  ,`description` varchar(255) NOT NULL COMMENT 'emoji 描述'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='emoji 白名单表'
;

INSERT INTO `m_allowlist_emoji`
  (`id`, `emoji`, `unicode_emoji`, `description`, `create_time`, `modified_time`)
VALUES
  (1, '🤩', '[1F929]', 'Star-Struck', 1633622400, 1633622400)
;

CREATE TABLE IF NOT EXISTS `m_drama_message_block_user` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间'
  ,`drama_id` bigint NOT NULL COMMENT '剧集 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`block_type` tinyint NOT NULL COMMENT '剧集拉黑评论类型，1：拉黑剧集弹幕；2：拉黑剧集评论'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='剧集评论用户黑名单表';

INSERT INTO `m_drama_message_block_user`
  (`id`, `create_time`, `modified_time`, `drama_id`, `user_id`, `block_type`)
VALUES
  (1, 1633622400, 1633622400, 1024, 66233, 1)
;

-- GRANT SELECT ON an_msg TO 'missevan_go'@'%'
CREATE TABLE IF NOT EXISTS `an_msg` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`small_id` bigint DEFAULT NULL COMMENT 'status 比特位第 3 位为 0 时为发送者 ID，为 1 时为接收者 ID'
  ,`big_id` bigint DEFAULT NULL COMMENT 'status 比特位第 3 位为 0 时为接收者 ID，为 1 时为发送者 ID'
  ,`post_name` varchar(20) DEFAULT NULL
  ,`post_icon` varchar(150) DEFAULT NULL
  ,`post_color` varchar(50) DEFAULT NULL
  ,`status` smallint DEFAULT NULL COMMENT '二进制运算：1 位是否已读（0：已读，1：未读），2 位是否是客服（0：非客服，1：客服），3 位发信方（0：small_id，1：big_id），4、5 位关闭会话方（4 位 small_id 关闭房间，5 位 big_id 关闭房间），6、7 位是否显示这条消息（0：显示；1：不显示）（6 位 small_id 显示，7 位 big_id 显示），8、9 位是否是拉黑不显示的消息（0：显示；1：拉黑不显示）（8 位 small_id 拉黑 big_id ，9 位 big_id 拉黑 small_id）'
  ,`msg` varchar(8000) NOT NULL
  ,`type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '私信内容的类型，0：纯文本，1：HTML'
  ,`ctime` int unsigned DEFAULT NULL
)
;
