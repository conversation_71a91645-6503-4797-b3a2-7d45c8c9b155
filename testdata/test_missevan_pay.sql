CREATE TABLE IF NOT EXISTS `balance` (
  `id` int NOT NULL PRIMARY KEY
  ,`ios` int unsigned DEFAULT '0' COMMENT 'iOS 充值余额'
  ,`android` int unsigned DEFAULT '0' COMMENT 'Android 充值余额'
  ,`paypal` int unsigned DEFAULT '0' COMMENT 'PayPal 余额'
  ,`tmallios` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 充值余额（单位：钻）'
  ,`googlepay` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 充值余额（单位：钻）'
  ,`in_ios` int unsigned DEFAULT '0' COMMENT 'iOS 收益余额'
  ,`in_android` int unsigned DEFAULT '0' COMMENT 'Android 收益余额'
  ,`in_paypal` int unsigned DEFAULT '0' COMMENT 'PayPal 收入'
  ,`in_tmallios` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收益余额（单位：钻）'
  ,`in_googlepay` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 收益余额（单位：钻）'
  ,`new_all_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日后）单位 ：分'
  ,`new_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日后）单位 ：分'
  ,`all_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日前）单位 ：分'
  ,`live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日前）单位 ：分'
  ,`profit` double unsigned DEFAULT '0' COMMENT '总收益余额（可提现）'
  ,`drama_reward_profit` int unsigned DEFAULT '0' COMMENT '剧集打赏总收益余 \n额'
  ,`drama_buy_profit` int unsigned DEFAULT '0' COMMENT '剧集购买总收益余额'
  ,`other_profit` int unsigned DEFAULT '0' COMMENT '其他总收益余额'
  ,`all_drama_buy_profit` int DEFAULT '0' COMMENT '剧集累计购买收益'
  ,`all_drama_reward_profit` int unsigned DEFAULT '0' COMMENT '剧集累计打赏收益'
  ,`all_other_profit` int unsigned DEFAULT '0' COMMENT '其他累计收益'
  ,`all_consumption` bigint DEFAULT '0' COMMENT '用户总消费（单位：钻），不包含贵族钻石消费'
  ,`all_topup` bigint NOT NULL DEFAULT '0' COMMENT '总充值额（单位：普通钻石）'
  ,`all_coin` int unsigned NOT NULL DEFAULT '0' COMMENT '普通钻石余额（单位：钻）'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8;
;

INSERT INTO `balance`
(`id`, `ios`, `android`, `paypal`, `tmallios`, `googlepay`, `in_ios`,`in_android`, `in_paypal`, `in_tmallios`, `in_googlepay`, `new_all_live_profit`, `new_live_profit`, `all_live_profit`, `live_profit`, `profit`, `drama_reward_profit`, `drama_buy_profit`, `other_profit`, `all_drama_buy_profit`, `all_drama_reward_profit`, `all_other_profit`, `all_consumption`, `all_topup`, `all_coin`)
VALUES
  (6666666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 233, 0, 0)
  ,(13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 233, 0, 0)
  ,(14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 233, 0, 0)
;

CREATE TABLE IF NOT EXISTS `transaction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`from_id` int NOT NULL
  ,`to_id` int NOT NULL
  ,`c_time` int NOT NULL
  ,`gift_id` int NOT NULL COMMENT '0 为知识问答 正整数为正常礼物'
  ,`title` varchar(255) DEFAULT NULL
  ,`ios_coin` int DEFAULT '0'
  ,`android_coin` int DEFAULT '0'
  ,`paypal_coin` int DEFAULT '0'
  ,`tmallios_coin` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）'
  ,`googlepay_coin` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）'
  ,`all_coin` int NOT NULL DEFAULT '0' COMMENT '钻石总和（花费的普通钻石与贵族钻石的总和）'
  ,`revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益'
  ,`income` double DEFAULT NULL
  ,`tax` double DEFAULT NULL
  ,`rate` double DEFAULT NULL
  ,`num` int NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度'
  ,`status` tinyint DEFAULT '0' COMMENT '-5 代充取消交易记录\n-4 已退款（钻石）\n-3 已退款（现金）\n-2 是直播问答取消\n-1 未完成（目前仅用作“直播问答提问中”）\n1 交易成功'
  ,`type` tinyint NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；'
  ,`suborders_num` int unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID'
  ,`attr` int unsigned NOT NULL DEFAULT '0' COMMENT '12 位及其之前为具体的商品属性：type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物，为 4 表示幸运签开箱礼物。type 为 2 或 3 时，attr 1 表示特殊途径购买，2 表示剧集兑换；type 为 4 时，attr 0 表示微信男友，1 表示掌心男友；13 位及其之后按位处理'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后修改时间'
)
;

INSERT INTO `transaction_log`
(`id` ,`from_id` ,`to_id` ,`c_time` ,`gift_id` ,`title` ,`ios_coin` ,`android_coin` ,`paypal_coin` ,`tmallios_coin` ,`googlepay_coin` ,`all_coin` ,`revenue` ,`income` ,`tax` ,`rate` ,`num` ,`status` ,`type` ,`suborders_num` ,`attr` ,`create_time` ,`modified_time`)
VALUES
  (1, 12, 0, 1522128358, 2, '测试用户是否已购买过该剧集', 0, 100, 0, 10, 0, 10, 5, 5, NULL, NULL, 1, 1, 3, 1, 2, 1522128358, 1522128358)
;

CREATE TABLE IF NOT EXISTS `transaction_sound_log` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`sound_id` int UNSIGNED NOT NULL COMMENT '单音 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`drama_id` int UNSIGNED NOT NULL COMMENT '剧集 ID'
  ,`tid` int NOT NULL COMMENT 'transaction_log 关联表 ID'
  ,`status` int NOT NULL DEFAULT 0 COMMENT '订单状态：-5 代充取消交易记录，-4 已退钻，-3 已退款（人民币），-2 取消（付费问答），-1 未完成（付费问答） ，1 成功'
  ,`attr` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单属性'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
) -- ENGINE=InnoDB CHARACTER SET=utf8 COLLATE=utf8_general_ci COMMENT = '单集购买订单表（字段说明：https://github.com/MiaoSiLa/missevan-doc/product/交易表字段说明.md）'
;

INSERT INTO `transaction_sound_log`
(`id`, `sound_id`, `user_id`, `drama_id`, `tid`, `status`, `attr`, `create_time`, `modified_time`)
VALUES
  -- TestFindUserPaidSoundIDs
  (2352, 91769, 3013091, 28, 10007889, 1, 0, 0, 0)
;
