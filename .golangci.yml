linters-settings:
  goimports:
    local-prefixes: github.com/MiaoSiLa
  forbidigo:
    analyze-types: true
    forbid:
      - p: ^print(|f|ln)$
        msg: '不能使用内置 print 函数'
      - p: ^sql\.ErrNoRows$
        pkg: ^database/sql$
        msg: '请使用 (missevan-go/service/servicedb).IsErrNoRows'
      - p: ^fmt\.Print(|f|ln)$
        pkg: ^fmt$
        msg: '不能使用 fmt Print 函数'
      - p: ^time\.Now$
        pkg: ^time$
        msg: '请使用 (missevan-go/util).TimeNow'
      - p: ^time\.(Until|Since)$
        pkg: ^time$
        msg: '请使用 time.Time.Sub 函数'
      - p: ^csv\.Reader\.Read$
        pkg: encoding/csv
        msg: '请使用 (missevan-go/util/csv).Read'
      - p: ^redis\.Nil$
        pkg: github.com/go-redis/redis/v7
        msg: '请使用 (missevan-go/service/serviceredis).IsRedisNil'
      - p: ^gorm\.(ErrRecordNotFound|IsRecordNotFoundError)$
        pkg: github.com/jinzhu/gorm
        msg: '请使用 (missevan-go/service/servicedb).IsErrNoRows'
      - p: ^gorm\.ErrRecordNotFound$
        pkg: gorm.io/gorm
        msg: '请使用 (missevan-go/service/servicedb).IsErrNoRows'
      - p: ^gorm\.DB\.Debug$
        pkg: github.com/jinzhu/gorm
        msg: '禁止使用 (github.com/jinzhu/gorm) DB 的 Debug 方法'
      - p: ^gorm\.DB\.Debug$
        pkg: gorm.io/gorm
        msg: '禁止使用 (gorm.io/gorm) DB 的 Debug 方法'
      - p: ^mongo\.ErrNoDocuments$
        pkg: go.mongodb.org/mongo-driver/mongo
        msg: '使用 (missevan-go/service/mongodb).IsNoDocumentsError 代替'
      - p: ^handler\.CreateTestContext$
        pkg: github.com/MiaoSiLa/missevan-go/controllers/handler
        msg: '使用 handler.NewTestContext 或 handler.NewRPCTestContext 代替'
      - p: ^messageassign\.(SystemMessageAssign|NewMessageBox)$
        pkg: github.com/MiaoSiLa/missevan-go/models/messageassign
        msg: '使用 service.PushService.SendSystemMsgWithOptions 代替'
      - p: ^pushservice\.Client\.SendSystemMsg$
        pkg: github.com/MiaoSiLa/missevan-go/service/missevan/pushservice
        msg: '使用 service.PushService.SendSystemMsgWithOptions 代替'
      - p: ^util\.ToMapWithSize$
        pkg: github.com/MiaoSiLa/missevan-go/util
        msg: '使用 (missevan-go/util).ToMap 代替'
  misspell:
    locale: US

linters:
  enable:
    - forbidigo
    - goimports
    - whitespace
    - misspell

issues:
  max-same-issues: 0
  max-issues-per-linter: 0
  exclude-rules:
    - path: _test\.go
      text: '^SA3000:'
      linters:
        - staticcheck

run:
  timeout: 3m
  skip-dirs:
    - library
