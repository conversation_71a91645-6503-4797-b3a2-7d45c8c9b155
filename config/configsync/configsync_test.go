package configsync

import (
	"errors"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tengattack/dandelion/app"
)

type emptyLogger struct{}

func (*emptyLogger) Debugf(format string, args ...interface{}) {
}

func (*emptyLogger) Infof(format string, args ...interface{}) {
}

func (*emptyLogger) Errorf(format string, args ...interface{}) {
}

var upgrader = websocket.Upgrader{} // use default options

var testServerURL string

func emptyWebSocket(w http.ResponseWriter, r *http.Request) {
	c, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Print("upgrade:", err)
		return
	}
	defer c.Close()
	for {
		_, message, err := c.ReadMessage()
		if err != nil {
			log.Println("read:", err)
			break
		}
		log.Printf("recv: %s", message)
	}
}

func TestMain(m *testing.M) {
	// fake dandelion server
	g := gin.New()
	v1 := g.Group("/api/v1")
	v1.GET("/match/:appID", func(c *gin.Context) {
		appID := c.Param("appID")
		c.JSON(http.StatusOK, gin.H{
			"code": 0,
			"info": gin.H{
				"app_id": appID,
				"config": app.AppConfig{ID: 1, AppID: appID, CommitID: "testcommitid"},
			},
		})
	})
	v1.GET("/archive/:appID/:commitID", func(c *gin.Context) {
		c.File("../../testdata/config/configsync.zip")
	})
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/connect/push" {
			// websocket connection
			emptyWebSocket(w, r)
			return
		}
		g.ServeHTTP(w, r)
	}))
	defer ts.Close()
	testServerURL = ts.URL
	os.Exit(m.Run())
}

func TestObtainDandelionURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dandelionURL := os.Getenv(dandelionEnvName)
	assert.Empty(dandelionURL)

	assert.Equal(defaultDandelionURL, obtainDandelionURL())

	setDandelionURL := "testurl:3000"
	err := os.Setenv(dandelionEnvName, setDandelionURL)
	defer func() {
		assert.NoError(os.Setenv(dandelionEnvName, ""))
	}()
	require.NoError(err)

	assert.Equal(setDandelionURL, obtainDandelionURL())
}

func TestConfigSync(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := InitSync(testServerURL, "test", "0.0.1", nil)
	require.NoError(err)

	var config struct {
		AccessKey string `yaml:"access_key"`
	}
	err = Unmarshal(&config)
	require.NoError(err)
	assert.NotEmpty(config.AccessKey)

	assert.NotPanics(func() {
		SetStatus(errors.New("test"))
		SetStatus(nil)
	})

	SetClientLogger(&emptyLogger{})

	err = WriteTo("testconfig")
	require.NoError(err)
	assert.FileExists("testconfig/config.yml")
	assert.FileExists("testconfig/params.yml")
	_ = os.RemoveAll("testconfig")

	err = Close()
	require.NoError(err)
}
