package configsync

import (
	"archive/zip"
	"errors"
	"io"
	"io/ioutil"
	"log"
	"os"
	"path"

	"github.com/tengattack/dandelion/app"
	"github.com/tengattack/dandelion/client"
	yaml "gopkg.in/yaml.v2"
)

// FilePath is the file path in configsync archive
// TODO: read file in configsync archive
type FilePath string

// UpdatedCallback the callback function for config updated
// TODO: implement it
type UpdatedCallback func(appID, version string, conf interface{}, err error)

// Client for configsync
type Client struct {
	configFile string

	c    *client.DandelionClient
	cfg  *app.ClientConfig
	app  *app.AppConfig
	meta map[string]interface{}
	z    *zip.Reader
}

const (
	dandelionEnvName    = "DANDELION_URL"
	defaultDandelionURL = "http://dandelion.infra:9012"
	defaultConfigFile   = "config.yml"
)

var (
	// ErrNoMatchConfig no suitable config for current app
	ErrNoMatchConfig = errors.New("no match config")
	// ErrNoConfigFile no config file in configsync archive
	ErrNoConfigFile = errors.New("no config file")

	defaultClient *Client
)

func obtainDandelionURL() string {
	// get envirnment variable DANDELION_URL
	dandelionURL := os.Getenv(dandelionEnvName)
	if dandelionURL != "" {
		return dandelionURL
	}

	// use default if not set
	return defaultDandelionURL
}

// InitSync inits config sync. It will fetch in cluster when url is empty.
func InitSync(url, appID, version string, updated UpdatedCallback) error {
	if url == "" {
		url = obtainDandelionURL()
	}

	c := &Client{configFile: defaultConfigFile}
	var err error
	c.c, err = client.NewDandelionClient(url, false)
	if err != nil {
		return err
	}

	hostname, _ := os.Hostname()
	host := os.Getenv("NODE_NAME")
	if host == "" {
		host = os.Getenv("HOST")
		if host == "" {
			host = hostname
		}
	}
	instanceID := os.Getenv("INSTANCE_ID")
	if instanceID == "" {
		instanceID = hostname
	}

	c.cfg = &app.ClientConfig{
		AppID:      appID,
		Version:    version,
		Host:       host,
		InstanceID: instanceID,
	}

	c.app, err = c.c.Match(c.cfg)
	if err != nil {
		return err
	}
	if c.app == nil {
		return ErrNoMatchConfig
	}

	c.meta = map[string]interface{}{
		"config_id": c.app.ID,
		"commit_id": c.app.CommitID,
	}
	err = c.c.SetStatus(c.cfg, client.StatusSyncing, c.meta)
	if err != nil {
		log.Print(err)
		// PASS
	}
	defer func() {
		if err != nil {
			_ = c.c.SetStatus(c.cfg, client.StatusError, c.meta)
		}
	}()

	c.z, err = c.c.GetZipArchive(appID, c.app.CommitID)
	if err != nil {
		return err
	}

	defaultClient = c
	return nil
}

// Close configsync
func Close() error {
	if defaultClient == nil {
		return nil
	}
	err := defaultClient.c.Close()
	defaultClient = nil
	return err
}

// SetClientLogger for config sync provider client
func SetClientLogger(lg client.Logger) {
	client.SetLogger(lg)
}

// Unmarshal default config struct
func Unmarshal(out interface{}) error {
	d, err := GetFile(defaultClient.configFile)
	defer func() {
		defaultClient.setStatus(err)
	}()
	if err != nil {
		return err
	}
	err = yaml.Unmarshal(d, out)
	return err
}

// WriteTo writes config files to dir
// TODO: sync instead of create new files
func WriteTo(dir string) error {
	var err error
	defer func() {
		defaultClient.setStatus(err)
	}()
	for _, f := range defaultClient.z.File {
		filePath := path.Join(dir, f.Name)
		err = os.MkdirAll(path.Dir(filePath), os.ModePerm)
		if err != nil && !os.IsExist(err) {
			return err
		}
		var fr io.ReadCloser
		fr, err = f.Open()
		if err != nil {
			return err
		}
		err = writeFileTo(fr, filePath)
		fr.Close()
		if err != nil {
			return err
		}
	}
	return nil
}

// GetFile gets file data in configsync archive
func GetFile(fileName string) ([]byte, error) {
	var found *zip.File
	for _, f := range defaultClient.z.File {
		if f.Name == fileName {
			found = f
			break
		}
	}
	if found == nil {
		return nil, ErrNoConfigFile
	}
	f, err := found.Open()
	if err != nil {
		return nil, err
	}
	defer f.Close()

	d, err := ioutil.ReadAll(f)
	if err != nil {
		return nil, err
	}

	return d, nil
}

// SetStatus set codesync error status
func SetStatus(err error) {
	defaultClient.setStatus(err)
}

func writeFileTo(r io.Reader, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = io.Copy(file, r)
	return err
}

func (c *Client) setStatus(err error) {
	if err != nil {
		_ = c.c.SetStatus(c.cfg, client.StatusError, c.meta)
		return
	}
	_ = c.c.SetStatus(c.cfg, client.StatusSuccess, c.meta)
}
