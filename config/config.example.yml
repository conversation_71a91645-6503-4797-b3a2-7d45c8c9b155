# missevan-go configuration file
ab:
  scan_im_antispam: 0.8 # 直播间聊天消息走 antispam 的几率

http:
  address: '' # default: '', aka. any
  port: 3032 # default: 3032
  mode: 'release' # default: 'release'
  rpc_key: 'testkey'
  rpc_app_key:
    app_key: 'testkey2'
  app_api_sign_key: 'testkey'
  csrf_allow_top_domains:
  - 'missevan.com'
  - 'bilibili.com'

log:
  format: "string" # string or json
  access_log: "stdout" # stdout: output to console, or define log path like "log/access_log"
  access_level: "info"
  error_log: "stderr" # stderr: output to console, or define log path like "log/error_log"
  error_level: "error"
  rotate: "" # or "daily"
  agent:
    enabled: false
    dsn: 'udp://logstash.example.com:8911'
    app_id: missevan-go
    # host: '' # log host, defaults to os.Getenv("HOST") or os.Hostname()
    # instance_id: '' # log instance_id, defaults to os.Getenv("INSTANCE_ID") or os.Hostname()
    # channel_size: 0 # default 1024

params:
  url:
    main: 'https://www.missevan.com/'
    cdn: 'test://'
    avatar_url: 'test://avatars/'
    profile_url: 'test://profile/'
    cover_url: 'test://coversmini/'
    default_icon_url: 'icon://avatars/icon01.png'
    drama_cover_url: 'test://dramacoversmini/'
    default_cover_url: 'test://coversmini/nocover.png'
  dh:
    prime: "FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A\
    431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE6\
    49286651ECE45B3DC2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F83655D23DCA3AD961C62F356208552BB9ED529077096966D670C\
    354E4ABC9804F1746C08CA18217C32905E462E36CE3BE39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9DE2BCBF6955817183995497C\
    EA956AE515D2261898FA051015728E5A8AACAA68FFFFFFFFFFFFFFFF" # 16 进制
    generator: '2' # 16 进制
  security:
    sensitive_information_key: 'openssl_aes_256_cbc_testpassword'
    # 加密 IV 初始化向量值，在需要数据库进行查询的地方使用到这个固定常量
    sensitive_fixed_iv_key: 'testiv'
  # 特殊音频 IDs（给用户做提示用的音频，不需要显示评论弹幕数等）
  notice_sound_ids: [75854, 115193, 223692, 518008, 1217690, 1616367, 519417, 246597, 884495, 1028077]
  # 客户端“收到的赞”页面链接
  message_like_open_url: 'missevan://message/like'
  # 客户端“我的评论”页面链接
  message_comment_open_url: 'missevan://message/comment'
  # 客户端“@我的”页面链接
  message_at_open_url: 'missevan://message/at'
  # 账号下同时播放付费或会员音设备的限制
  user_equip_play_limit:
    # 限制开关。true：开启；false：关闭。默认关闭
    enable: true
    # 设置到正在播放中设备的有效时长，单位：秒。一般和播放日志上报周期时长相同，考虑到网络延迟等问题，可增加 5s 冗余
    effective_duration: 300
    # 最大同时播放付费或会员音设备数量
    equip_max_num: 1

service:
  db:
    host: '************'
    port: 3306
    name: 'app_missevan'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  message_db:
    host: '************'
    port: 3306
    name: 'missevan_message'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/missevan_main?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  log_db:
    host: '************'
    port: 3306
    name: 'app_missevan_log'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan_log?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  drama_db:
    host: '************'
    port: 3306
    name: 'app_missevan_radio_drama'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan_radio_drama?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  voice_db:
    host: '************'
    port: 3306
    name: 'app_missevan_voice'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan_voice?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  main_db:
    host: '************'
    port: 3306
    name: 'missevan_main'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan_voice?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  pay_db:
    host: '************'
    port: 3306
    name: 'missevan_pay'
    user: 'user'
    pass: 'password'
    # url: 'mysql://user:password@tcp(************:3306)/app_missevan_voice?charset=utf8mb4,utf8'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
    # debug: false # default is false
  memcache:
    servers: '************:11211'
    username: 'user'
    password: 'pass'
    # pool_size: 10 # default: 10
  redis:
    addr: '************:6379'
    # password: '' # default: ''
    # db: 0 # default: 0
    # pool_size: 10 # default: serviceredis.DefaultPoolSize()
  lru_redis:
    addr: '************:6379'
    db: 0
  databus:
    app_log_pub:
      key: 'test1'
      secret: 'test1'
      group: 'AppLog-S'
      topic: 'AppLog-T'
      action: 'pub'
      # pool_size: 10 # pub pool size, default: 10
    app_log_sub:
      key: 'test1'
      secret: 'test1'
      group: 'AppLog-S'
      topic: 'AppLog-T'
      action: 'sub'
    live_log_pub:
      key: 'test1'
      secret: 'test1'
      group: 'LiveLog-S'
      topic: 'LiveLog-T'
      action: 'pub'
      # pool_size: 10 # pub pool size, default: 10
  storage:
    icon:  # related with bucket (eg. bucket-icon)
      type: 'oss'
      access_key_id: 'accesskey'
      access_key_secret: 'xxxxxx'
      bucket: 'bucket-icon'
      endpoint: 'oss-cn-hangzhou.aliyuncs.com'
      # public_url 配置为 - 时，将禁止获取 public url
      # public_url: 'http://static.example.com/' # storage 地址转公共地址优先级: public_url > public_urls
      public_urls:
      - url: 'http://static.example.com'
        weight: 1
      - url: 'http://foramt.example.com/'
        # weight: 0
      private_key: '' # cdn private key
  upload:
    url: 'https://www.uat.missevan.com/files/'
    path: '/data/files/'
    # http: false
    # http_host_addr: '127.0.0.1:80'
  upos:
    mefmexboss:
      preupload_endpoint: 'http://uat-member.bilibili.com'
      profile: 'mefm/upclone'
      token: 'xxx'
      no_sign: true
      public_url: 'http://static.example.com'
    mefmboss:
      preupload_endpoint: 'http://uat-member.bilibili.com'
      profile: 'mefm/upclone'
      token: 'xxx'
      business_sign:
        url: 'http://upos.test.com/maoerplayurl'
        force_host: 1
  mrpc:
    # mrpc url should have `/` suffix
    app:
      url: 'http://127.0.0.1:8017/rpc/'
      key: 'xxxxxx'
    drama:
      url: 'http://127.0.0.1:8080/rpc/'
      key: 'xxxxxx'
    go:
      url: 'http://127.0.0.1:3032/rpc/'
      key: 'xxxxxx'
    game:
      url: 'http://127.0.0.1:8999/rpc/'
      key: 'xxxxxx'
    sso:
      url: 'http://127.0.0.1:3002/rpc/'
      key: 'xxxxxx'
    live:
      url: 'http://127.0.0.1:3013/rpc/'
      key: 'xxxxxx'
    minigame:
      url: 'http://rpc-services:3035/rpc/'
      key: 'xxxxxx'
    mrpc:
      url: 'http://rpc-services:3000/rpc/'
      key: 'xxxxxx'
  pushservice:
    # using mrpc
    url: 'http://127.0.0.1:8098/'
    key: 'xxxxxx'
  antispam:
    blacklist:
      csv_dir: ''
    provider: 'aliyun'
    region_id: 'cn-shanghai'
    endpoint: 'https://green.cn-shanghai.aliyuncs.com'
    access_key_id: 'accesskey'
    access_key_secret: 'xxxxxx'
  antispamv2:
    provider: 'aliyun'
    url: 'https://green-cip.cn-shanghai.aliyuncs.com'
    access_key_id: 'accesskey'
    access_key_secret: 'xxxxxx'
  # TODO: 后续需要将 geetest 挪到 captcha 下
  geetest:
    url: 'http://api.geetest.com'
    geetest_id: 'testid'
    geetest_key: 'testkey'
    salt: 'salt'
  geetest_low_risk:
    url: 'http://api.geetest.com'
    geetest_id: 'testid'
    geetest_key: 'testkey'
    salt: 'salt'
  opensearch:
    region_id: 'cn-hangzhou'
    endpoint: 'http://opensearch-cn-hangzhou.aliyuncs.com'
    access_key_id: 'accesskey'
    access_key_secret: 'xxxxxx'
    push_user_id_key: 'push_user_id_key'
    apps:
      sound:
        name: 'uat_m_sound'
        sort: ['sound', 'view', 'time', 'dm']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'tags'
          weight: 99
        - key: 'title'
          weight: 99
        - key: 'title_pinyin'
          weight: 99
          fuzzy: true
        - key: 'username'
          weight: 99
        - key: 'username_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
      user:
        name: 'uat_mowangskuser'
        sort: ['user']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'default_pinyin'
          weight: 99
          fuzzy: true
        - key: 'title'
          weight: 99
        - key: 'title_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
      album:
        name: 'uat_m_album'
        sort: ['album']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'title'
          weight: 99
        - key: 'title_pinyin'
          weight: 99
          fuzzy: true
        - key: 'tags'
          weight: 99
        - key: 'id'
          weight: 99
          int: true
      seiy:
        name: 'uat_mowangsksoundseiy'
        sort: ['seiy']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'default_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
      drama:
        name: 'uat_drama'
        sort: ['drama', 'view', 'time']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'context'
          weight: 10
        - key: 'title'
          weight: 99
        - key: 'title_pinyin'
          weight: 99
          fuzzy: true
        - key: 'user'
          weight: 99
        - key: 'user_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
      live:
        name: 'uat_live'
        sort: ['live']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'default_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
        - key: 'creator_id'
          weight: 90
          int: true
      special:
        name: 'uat_special_search_items'
        sort: ['id']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'id'
          weight: 99
          int: true
      channel:
        name: 'uat_m_tag'
        sort: ['channel']
        indexes:
        - key: 'default'
          weight: 99
        - key: 'id'
          weight: 99
          int: true
      danmaku:
        name: 'uat_message'
        sort: ['danmaku']
        indexes:
        - key: 'default'
          weight: 99
          extra: true
        - key: 'text_pinyin'
          weight: 99
          fuzzy: true
        - key: 'id'
          weight: 99
          int: true
          extra: true
        - key: 'user_id'
          extra: true
        - key: 'sound_id'
          extra: true
      special_topic_card:
        name: 'uat_special_search_items'
        sort: ['topic_card']
        indexes:
        - key: 'topiccard'
          weight: 99
    management:
      endpoint: 'https://opensearch.cn-hangzhou.aliyuncs.com'
      # 下拉提示（联想词）黑名单词典名称
      suggest_denylist:
      - uat_drama_denylist
      - uat_m_tag_denylist
      - uat_mowangskuser_denylist
      - uat_m_sound_denylist
      # 搜索同义词词典名称
      synonym:
      - uat_drama
      - uat_sound
      # 自定义分析器词典名称
      user_analyzer:
      - uat_drama_name
  gaia:
    url: 'http://*************'
    # host: 'uat-maoer.biliapi.net' # default ''
    app_key: 'testkey'
    app_secret: 'testkey'
    http2: true # default: false
  govern:
    url: 'http://*************'
    # host: 'uat-maoer.biliapi.net' # default ''
    app_key: 'testkey'
    app_secret: 'testkey'
  short_url:
    url: 'http://*************'
    # host: 'uat-maoer.biliapi.net' # default ''
    app_key: 'testkey'
    app_secret: 'testkey'
  ipip:
    db_ipv4: '/path/to/stable_v4_flagship.ipdb'
    db_ipv6: '/path/to/stable_v6_flagship.ipdb' # empty string means ipv6 record will return empty
    language: 'EN' # 'EN' or 'CN', default 'EN'
  user_agent_parser: # user-agent 解析配置
    regexes_file: '/path/to/regexes.yaml'
  captcha:
    enabled: true
    access_key_id: 'xxxxx'
    access_key_secret: 'xxxxx'
    app_key: 'xxxxx'
    region_id: 'cn-hangzhou'
    endpoint: 'http://afs.aliyuncs.com'
    slide_url: 'https://www.uat.missevan.com/standalone/403/slide.html'
  lancer:
    # 接入文档：https://info.bilibili.co/pages/viewpage.action?pageId=94017407
    # 由于部署的机器不在 B 站 idc，无法使用 log agent 协议上报，使用 http 协议上报（http 协议为待废弃的协议）
    endpoint: 'http://dataflow.biliapi.com/log/system'
    timeout: '1s'
    interval: '3s'
    log_id:
      # lancer 集成任务：https://berserker.bilibili.co/#/lancer/list/job/173286393420201
      recommend_log: '026928'
  smartsheet:
    url: 'http://127.0.0.1:9003'
