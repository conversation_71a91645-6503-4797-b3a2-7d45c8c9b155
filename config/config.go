package config

import (
	"os"

	"github.com/gin-gonic/gin"
	"gopkg.in/yaml.v2"

	"github.com/MiaoSiLa/missevan-go/config/ab"
	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-go/service"
)

// Conf 默认的配置
// TODO: 目前是在 main.go 进行注册的，后续需要优化成单例模式
var Conf Config

// Config is config structure.
type Config struct {
	AB      ab.Config      `yaml:"ab"`
	HTTP    SectionHTTP    `yaml:"http"`
	Log     log.Config     `yaml:"log"`
	Params  params.Params  `yaml:"params"`
	Service service.Config `yaml:"service"`
}

// SectionHTTP is sub section of config.
type SectionHTTP struct {
	Address string `yaml:"address"`
	Port    int    `yaml:"port"`
	Mode    string `yaml:"mode"`

	RPCKey    string            `yaml:"rpc_key"`
	RPCAPPKey map[string]string `yaml:"rpc_app_key"`

	APPAPISignKey       string   `yaml:"app_api_sign_key"`
	CSRFAllowTopDomains []string `yaml:"csrf_allow_top_domains"`
}

// BuildDefaultConf is default config setting.
func BuildDefaultConf() Config {
	conf := Config{AB: make(ab.Config)}

	// HTTP
	conf.HTTP.Address = ""
	conf.HTTP.Port = 3032
	conf.HTTP.Mode = gin.ReleaseMode

	conf.HTTP.CSRFAllowTopDomains = []string{"missevan.com", "bilibili.com"}

	// Log
	conf.Log = *log.DefaultConfig

	// Service
	conf.Service = *service.DefaultConfig

	return conf
}

// LoadConfig load config from file
func LoadConfig(confPath string) (Config, error) {
	conf := BuildDefaultConf()

	configFile, err := os.ReadFile(confPath)
	if err != nil {
		return conf, err
	}

	err = yaml.Unmarshal(configFile, &conf)
	if err != nil {
		return conf, err
	}

	// init params
	params.InitParams(&conf.Params)
	return conf, nil
}
