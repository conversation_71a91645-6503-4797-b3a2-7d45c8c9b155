package config

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger/log"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestBuildDefaultConf(t *testing.T) {
	assert := assert.New(t)
	conf := BuildDefaultConf()

	assert.Equal("", conf.HTTP.Address)
	assert.Equal(3032, conf.HTTP.Port)
	assert.Equal("release", conf.HTTP.Mode)
	assert.Equal(*log.DefaultConfig, conf.Log)
	assert.Equal(*service.DefaultConfig, conf.Service)
}

func TestLoadConfig(t *testing.T) {
	assert := assert.New(t)

	_, err := LoadConfig("")
	assert.Error(err)

	_, err = LoadConfig("./config.go")
	assert.Error(err)

	_, err = LoadConfig(`./config.example.yml`)
	assert.NoError(err)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "ab", "http", "log", "params", "service")
	kc.Check(SectionHTTP{}, "address", "port", "mode", "rpc_key", "rpc_app_key", "app_api_sign_key", "csrf_allow_top_domains")
}
