package ab

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v2"
)

func TestABGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := "bool: true\n" +
		"float64: 0.5\n" +
		"int: 1\n" +
		"string: string\n"

	ab := new(Config)
	err := yaml.Unmarshal([]byte(b), ab)
	require.NoError(err)
	var numInt64 int64
	var numInt int
	var double float64
	var ok bool
	var s string
	var ch chan int
	ab.Get("int", &numInt64)
	ab.Get("int", &numInt)
	ab.Get("empty", &double)
	ab.Get("float64", &double)
	ab.Get("bool", &ok)
	ab.Get("string", &s)
	assert.Equal(int64(1), numInt64)
	assert.Equal(1, numInt)
	assert.Equal(0.5, double)
	assert.True(ok)
	assert.Equal("string", s)
	assert.NotPanics(func() { ab.Get("not_exists", ch) })
}
