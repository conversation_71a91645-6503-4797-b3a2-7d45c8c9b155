package params

// Params for static files
type Params struct {
	URL      SectionURL      `yaml:"url"`
	DH       dh              `yaml:"dh"`
	Key      sectionKey      `yaml:"key"`
	Security SectionSecurity `yaml:"security"`

	NoticeSoundIDs        []int64            `yaml:"notice_sound_ids"`
	MessageLikeOpenURL    string             `yaml:"message_like_open_url"`
	MessageCommentOpenURL string             `yaml:"message_comment_open_url"`
	MessageAtOpenURL      string             `yaml:"message_at_open_url"`
	UserEquipPlayLimit    userEquipPlayLimit `yaml:"user_equip_play_limit"`
}

// SectionURL items
type SectionURL struct {
	Main            string `yaml:"main"`
	CDN             string `yaml:"cdn"`
	AvatarURL       string `yaml:"avatar_url"`
	ProfileURL      string `yaml:"profile_url"`
	CoverURL        string `yaml:"cover_url"`
	DefaultIconURL  string `yaml:"default_icon_url"`
	DramaCoverURL   string `yaml:"drama_cover_url"`
	DefaultCoverURL string `yaml:"default_cover_url"`
}

type dh struct {
	Prime     string `yaml:"prime"`     // 16 进制
	Generator string `yaml:"generator"` // 16 进制
}

type sectionKey struct {
	// EventShareKey string `yaml:"event_share_key"`
}

// SectionSecurity 敏感信息加密相关参数
type SectionSecurity struct {
	SensitiveInformationKey string `yaml:"sensitive_information_key"`
	SensitiveFixedIVKey     string `yaml:"sensitive_fixed_iv_key"`
}

type userEquipPlayLimit struct {
	Enable            bool `yaml:"enable"`
	EffectiveDuration int  `yaml:"effective_duration"`
	EquipMaxNum       int  `yaml:"equip_max_num"`
}

// variables for external use
var (
	URL SectionURL
	DH  dh
	Key sectionKey

	NoticeSoundIDs        []int64
	MessageLikeOpenURL    string
	MessageCommentOpenURL string
	MessageAtOpenURL      string
	UserEquipPlayLimit    userEquipPlayLimit
)

// InitParams init params
func InitParams(paramsConf *Params) {
	URL = paramsConf.URL
	DH = paramsConf.DH
	Key = paramsConf.Key
	NoticeSoundIDs = paramsConf.NoticeSoundIDs
	MessageLikeOpenURL = paramsConf.MessageLikeOpenURL
	MessageCommentOpenURL = paramsConf.MessageCommentOpenURL
	MessageAtOpenURL = paramsConf.MessageAtOpenURL
	UserEquipPlayLimit = paramsConf.UserEquipPlayLimit
}

// InitTestParams init params for testing only
func InitTestParams() {
	URL = SectionURL{
		Main:            "https://www.missevan.com/",
		CDN:             "test://",
		AvatarURL:       "test://avatars/",
		ProfileURL:      "test://profile/",
		CoverURL:        "test://coversmini/",
		DefaultIconURL:  "test://avatars/icon01.png",
		DramaCoverURL:   "test://dramacoversmini/",
		DefaultCoverURL: "test://coversmini/nocover.png",
	}
	DH = dh{
		Prime: "FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A" +
			"431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE6" +
			"49286651ECE45B3DC2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F83655D23DCA3AD961C62F356208552BB9ED529077096966D670C" +
			"354E4ABC9804F1746C08CA18217C32905E462E36CE3BE39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9DE2BCBF6955817183995497C" +
			"EA956AE515D2261898FA051015728E5A8AACAA68FFFFFFFFFFFFFFFF",
		Generator: "2",
	}
	Key = sectionKey{}
	NoticeSoundIDs = []int64{}
	MessageLikeOpenURL = ""
	MessageCommentOpenURL = ""
	MessageAtOpenURL = ""
	UserEquipPlayLimit = userEquipPlayLimit{
		Enable:            true,
		EffectiveDuration: 300,
		EquipMaxNum:       1,
	}
}
