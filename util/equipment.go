package util

import (
	"net/http"
	"regexp"
	"strconv"
	"strings"
)

// Platform 设备类型
type Platform int

// constants
const (
	// 设备类型
	Unknown Platform = iota
	Android
	IOS
	Web
	MobileWeb
	Windows // 直播助手
	HarmonyOS
)

const (
	osAndroid   = "Android"
	osIOS       = "iOS"
	osWindows   = "Windows"
	osHarmonyOS = "HarmonyOS"

	// 手机移动端
	osMobileWebIPhone  = "iPhone"
	osMobileWebAndroid = "Android"
)

// 渠道标识
const (
	ChannelGoogle      = "missevan_google"
	ChannelGoogle64Bit = "missevan64_google"
)

// Equipment 设备
type Equipment struct {
	EquipID     string
	BUVID       string
	AppVersion  string
	OS          Platform
	OSVersion   string
	DeviceModel string

	FromApp bool
}

var matchUserAgent = regexp.MustCompile(`^MissEvanApp\/([^ ]+) \(([^\)]+)\)`)

// ParsePlatform parse platform
func ParsePlatform(os string) Platform {
	switch os {
	case osAndroid:
		return Android
	case osIOS:
		return IOS
	case osWindows:
		return Windows
	case osHarmonyOS:
		return HarmonyOS
	default:
		return Unknown
	}
}

// NewEquipment new Equipment
func NewEquipment(userAgent string) (e *Equipment) {
	e = new(Equipment)
	matches := matchUserAgent.FindStringSubmatch(userAgent)
	if len(matches) != 3 {
		if strings.Contains(userAgent, osMobileWebAndroid) ||
			strings.Contains(userAgent, osMobileWebIPhone) ||
			IsFromHarmonyOSMobileWeb(userAgent) {
			e.OS = MobileWeb
		} else {
			e.OS = Web
		}
		return
	}
	e.AppVersion = matches[1]
	osInfo := strings.Split(matches[2], ";")
	if len(osInfo) != 3 {
		e.AppVersion = ""
		return
	}
	e.OS = ParsePlatform(osInfo[0])
	if e.OS == Unknown {
		e.AppVersion = ""
		e.OS = Web
		return
	}
	e.OSVersion = osInfo[1]
	e.DeviceModel = osInfo[2]
	e.FromApp = true
	return e
}

// ParseEquipment parses the http request and returns a new Equipment
func ParseEquipment(r *http.Request) (*Equipment, error) {
	ret := NewEquipment(r.UserAgent())
	// If multiple cookies match the given name, only the 1st cookie will be returned.
	equipID, _ := r.Cookie("equip_id")
	if equipID != nil {
		ret.EquipID = equipID.Value
	}
	buvid, _ := ParseBuvid(r)
	if buvid != "" {
		ret.BUVID = buvid
	}
	// TODO: check
	return ret, nil
}

// ParseBuvid parses the http request and returns the buvid
func ParseBuvid(r *http.Request) (string, error) {
	// If multiple cookies match the given name, only the 1st cookie will be returned.
	buvid, _ := r.Cookie("buvid")
	if buvid != nil {
		return buvid.Value, nil
	}
	return "", nil
}

// IsAppOlderThan compares e.AppVersion with either versioniOS or versionAndroid, of which depends on whether e.OS is iOS or Android.
// Passing an empty versioniOS means to skip the iOS version comparison. This also applies to versionAndroid.
// If e.AppVersion is older than versioniOS (or maybe versionAndroid), it returns true. Otherwise it returns false.
// If e.OS is neither iOS nor Android, or if the version comparison is skipped, it returns false.
// Deprecated: 使用 IsOldApp 代替此函数
func (e Equipment) IsAppOlderThan(versioniOS, versionAndroid string) bool {
	v := AppVersions{
		IOS:     versioniOS,
		Android: versionAndroid,
	}
	return e.IsOldApp(v)
}

// AppVersions 版本号
type AppVersions struct {
	IOS       string
	Android   string
	Windows   string
	HarmonyOS string
}

func (a AppVersions) version(os Platform) string {
	switch os {
	case IOS:
		return a.IOS
	case Android:
		return a.Android
	case HarmonyOS:
		return a.HarmonyOS
	case Windows:
		return a.Windows
	}
	return ""
}

// IsOldApp 返回 e 是否是老版本客户端（版本号比 v 的版本号老）
func (e Equipment) IsOldApp(v AppVersions) bool {
	if !e.FromApp {
		return false
	}
	ver := v.version(e.OS)
	if ver == "" {
		return false
	}
	return VersionCompare(e.AppVersion, ver, OperatorLt)
}

// OperatorCompare type
type OperatorCompare string

// OperatorCompare enum
var (
	OperatorLt OperatorCompare = "<"
	OperatorGt OperatorCompare = ">"
	OperatorEq OperatorCompare = "="
	OperatorLe OperatorCompare = "<="
	OperatorGe OperatorCompare = ">="
	OperatorNe OperatorCompare = "!="
)

// VersionCompare implements php version_compare(), https://github.com/syyongx/php2go
// special version strings these are handled in the following order,
// (any string not found) < dev < alpha = a < beta = b < RC = rc < # < pl = p
// Usage:
// VersionCompare("1.2.3-alpha", "1.2.3RC7", OperatorGe)
// VersionCompare("1.2.3-beta", "1.2.3pl", OperatorLt)
// VersionCompare("1.1_dev", "1.2any", OperatorEq)
func VersionCompare(version1, version2 string, operator OperatorCompare) bool {
	var vcompare func(string, string) int
	var canonicalize func(string) string
	var special func(string, string) int

	// version compare
	vcompare = func(origV1, origV2 string) int {
		if origV1 == "" || origV2 == "" {
			if origV1 == "" && origV2 == "" {
				return 0
			}
			if origV1 == "" {
				return -1
			}
			return 1
		}

		ver1, ver2, compare := "", "", 0
		if origV1[0] == '#' {
			ver1 = origV1
		} else {
			ver1 = canonicalize(origV1)
		}
		if origV2[0] == '#' {
			ver2 = origV2
		} else {
			ver2 = canonicalize(origV2)
		}
		n1, n2 := 0, 0
		for {
			p1, p2 := "", ""
			n1 = strings.IndexByte(ver1, '.')
			if n1 == -1 {
				p1, ver1 = ver1[:], ""
			} else {
				p1, ver1 = ver1[:n1], ver1[n1+1:]
			}
			n2 = strings.IndexByte(ver2, '.')
			if n2 == -1 {
				p2, ver2 = ver2, ""
			} else {
				p2, ver2 = ver2[:n2], ver2[n2+1:]
			}

			if (p1[0] >= '0' && p1[0] <= '9') && (p2[0] >= '0' && p2[0] <= '9') { // all is digit
				l1, _ := strconv.Atoi(p1)
				l2, _ := strconv.Atoi(p2)
				if l1 > l2 {
					compare = 1
				} else if l1 == l2 {
					compare = 0
				} else {
					compare = -1
				}
			} else if !(p1[0] >= '0' && p1[0] <= '9') && !(p2[0] >= '0' && p2[0] <= '9') { // all digit
				compare = special(p1, p2)
			} else { // part is digit
				if p1[0] >= '0' && p1[0] <= '9' { // is digit
					compare = special("#N#", p2)
				} else {
					compare = special(p1, "#N#")
				}
			}

			if compare != 0 || n1 == -1 || n2 == -1 {
				break
			}
		}

		if compare == 0 {
			if ver1 != "" {
				if ver1[0] >= '0' && ver1[0] <= '9' {
					compare = 1
				} else {
					compare = vcompare(ver1, "#N#")
				}
			} else if ver2 != "" {
				if ver2[0] >= '0' && ver2[0] <= '9' {
					compare = -1
				} else {
					compare = vcompare("#N#", ver2)
				}
			}
		}

		return compare
	}

	// canonicalize
	canonicalize = func(version string) string {
		ver := []byte(version)
		l := len(ver)
		if l == 0 {
			return ""
		}
		var buf = make([]byte, l*2)
		j := 0
		for i, v := range ver {
			next := uint8(0)
			if i+1 < l { // Have the next one
				next = ver[i+1]
			}
			if v == '-' || v == '_' || v == '+' { // replace '-', '_', '+' to '.'
				if j > 0 && buf[j-1] != '.' {
					buf[j] = '.'
					j++
				}
			} else if (next > 0) &&
				(!(next >= '0' && next <= '9') && (v >= '0' && v <= '9')) ||
				(!(v >= '0' && v <= '9') && (next >= '0' && next <= '9')) { // Insert '.' before and after a non-digit
				buf[j] = v
				j++
				if v != '.' && next != '.' {
					buf[j] = '.'
					j++
				}
				continue
			} else if !((v >= '0' && v <= '9') ||
				(v >= 'a' && v <= 'z') || (v >= 'A' && v <= 'Z')) { // Non-letters and numbers
				if j > 0 && buf[j-1] != '.' {
					buf[j] = '.'
					j++
				}
			} else {
				buf[j] = v
				j++
			}
		}

		return string(buf[:j])
	}

	// compare special version forms
	special = func(form1, form2 string) int {
		found1, found2, len1, len2 := -1, -1, len(form1), len(form2)
		// (Any string not found) < dev < alpha = a < beta = b < RC = rc < # < pl = p
		forms := map[string]int{
			"dev":   0,
			"alpha": 1,
			"a":     1,
			"beta":  2,
			"b":     2,
			"RC":    3,
			"rc":    3,
			"#":     4,
			"pl":    5,
			"p":     5,
		}

		for name, order := range forms {
			if len1 < len(name) {
				continue
			}
			if strings.Compare(form1[:len(name)], name) == 0 {
				found1 = order
				break
			}
		}
		for name, order := range forms {
			if len2 < len(name) {
				continue
			}
			if strings.Compare(form2[:len(name)], name) == 0 {
				found2 = order
				break
			}
		}

		if found1 == found2 {
			return 0
		} else if found1 > found2 {
			return 1
		} else {
			return -1
		}
	}

	compare := vcompare(version1, version2)

	switch operator {
	case OperatorLt:
		return compare == -1
	case OperatorLe:
		return compare != 1
	case OperatorGt:
		return compare == 1
	case OperatorGe:
		return compare != -1
	case OperatorEq:
		return compare == 0
	case OperatorNe:
		return compare != 0
	default:
		panic("operator: invalid")
	}
}

// IsFromGoogleChannel 是否来自谷歌渠道
func IsFromGoogleChannel(req *http.Request) bool {
	channel := req.Header.Get("channel")
	return channel == ChannelGoogle || channel == ChannelGoogle64Bit
}

// IsFromHarmonyOSMobileWeb 是否来自鸿蒙版手机网页端
// 格式：Mozilla/5.0 ({deviceType}; {OSName} {OSVersion}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 ArkWeb/{ArkWeb VersionCode} {Mobile}
// 参考：https://developer.huawei.com/consumer/cn/doc/harmonyos-guides-V5/web-default-useragent-V5
func IsFromHarmonyOSMobileWeb(userAgent string) bool {
	// 旧版本鸿蒙为 HarmonyOS，示例：Mozilla/5.0 (Phone; HarmonyOS 4.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 ArkWeb/4.1.6.1 Mobile
	// 新版鸿蒙（纯血鸿蒙）为 OpenHarmony，示例：Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 ArkWeb/4.1.6.1 Mobile
	return (strings.Contains(userAgent, "HarmonyOS") || strings.Contains(userAgent, "OpenHarmony")) &&
		(strings.Contains(userAgent, "Phone") || strings.Contains(userAgent, "Mobile"))
}
