package util

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestReplaceAllStringSubmatchFunc(t *testing.T) {
	assert := assert.New(t)

	input := `abc foo:bar def baz:qux ghi`
	r := regexp.MustCompile(`(\w+):(\w+)`)
	assert.Equal(`abc foo^bar def baz^qux ghi`, ReplaceAllStringSubmatchFunc(r, input, func(group []string) string {
		return group[1] + "^" + group[2]
	}))
}

func TestSubStr(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("一二三", Substr("一二三四五", 0, 3))
	assert.Equal("一二三", Substr("一二三", 0, 6))
	assert.Equal("", Substr("123", 6, 0))
	assert.Equal("3", Substr("123", 2, 2))
	assert.Equal("", Substr("一二三", 6, 9))

	assert.Equal("⊙)⛔", Substr("(⊙﹏⊙)⛔e", 3, 3))

	assert.PanicsWithValue("length must be greater than or equal to zero", func() {
		Substr("abc", 0, -1)
	})
}

func TestSplitToInt64Array(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := SplitToInt64Array("1,2, 3 ,  4", ",")
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3, 4}, r)

	r, err = SplitToInt64Array("", ",")
	require.NoError(err)
	assert.Empty(r)

	_, err = SplitToInt64Array("test", ",")
	assert.Error(err)
}

func TestFormatMessage(t *testing.T) {
	assert := assert.New(t)

	message := "<b>${user_name}</b> 给 <b>${user_name}</b> 送出 <b>${num} 个${gift_name}</b>，快来围观吧~"
	param := map[string]string{"user_name": "test", "num": "1", "gift_name": "药丸"}
	r := FormatMessage(message, param)
	assert.Equal("<b>test</b> 给 <b>test</b> 送出 <b>1 个药丸</b>，快来围观吧~", r)

	param = map[string]string{}
	r = FormatMessage(message, param)
	assert.Equal(message, r)
}

func TestFormatMessageBracketed(t *testing.T) {
	assert := assert.New(t)

	message := "<b>#[user_name]</b> 给 <b>#[user_name]</b> 送出 <b>#[num] 个#[gift_name]</b>，快来围观吧~"
	param := map[string]string{"user_name": "test", "num": "1", "gift_name": "药丸"}
	r := FormatMessageBracketed(message, param)
	assert.Equal("<b>test</b> 给 <b>test</b> 送出 <b>1 个药丸</b>，快来围观吧~", r)

	param = map[string]string{}
	r = FormatMessageBracketed(message, param)
	assert.Equal(message, r)
}

func TestUniqString(t *testing.T) {
	assert := assert.New(t)

	input := []string{"a1", "a2", "a1"}
	uniqueArray := UniqString(input)
	assert.Equal([]string{"a1", "a2"}, uniqueArray)
}

func TestContainsEmoji(t *testing.T) {
	assert := assert.New(t)

	assert.False(ContainsEmoji("测试字符串"))
	assert.True(ContainsEmoji("测试😂"))
	assert.True(ContainsEmoji("测试🇯🇵"))
}

func TestExtractHTMLPlainText(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("", ExtractHTMLPlainText(""))
	assert.Equal("【特别好 & 椰子】", ExtractHTMLPlainText("<div><p>【特别好 <b>&</b> 椰子】</p></div>"))
	assert.Equal("【特", ExtractHTMLPlainText("<div><p>【特别好 <b>&</b> 椰子】</p></div>", 2))
}
