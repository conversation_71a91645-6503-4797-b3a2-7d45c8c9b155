package xsingleflight

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	cancelCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xsingleflight",
			Name:      "cancel_counter",
			Help:      "singleflight  cancel counter",
		}, []string{"key"})

	panicCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xsingleflight",
			Name:      "panic_counter",
			Help:      "singleflight  panic counter",
		}, []string{"key"})

	visitCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "xsingleflight",
			Name:      "visit_counter",
			Help:      "singleflight  visit counter",
		}, []string{"key", "shared"})
)

// InitMetrics 初始化 metrics
func InitMetrics(promRegistry *prometheus.Registry) {
	promRegistry.MustRegister(cancelCounter)
	promRegistry.MustRegister(panicCounter)
	promRegistry.MustRegister(visitCounter)
}
