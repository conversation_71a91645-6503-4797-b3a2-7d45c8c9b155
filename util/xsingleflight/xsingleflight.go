package xsingleflight

import (
	"bytes"
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"golang.org/x/sync/singleflight"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Group single flight group
type Group struct {
	singleflight.Group
}

// NewGroup new group
func NewGroup() *Group {
	return &Group{}
}

// Do 支持超时与取消
// fix 异常 case: 解决单个请求超时影响后续其他请求的问题
func (g *Group) Do(ctx context.Context, key string, fn func() (interface{}, error)) (v interface{}, shared bool, err error) {
	return g.do(ctx, key, key, fn)
}

// DoWithLabel 支持超时与取消
// fix 异常 case: 解决单个请求超时影响后续其他请求的问题
func (g *Group) DoWithLabel(ctx context.Context, label, key string, fn func() (interface{}, error)) (v interface{}, shared bool, err error) {
	return g.do(ctx, label, key, fn)
}

// DoChan do chan
func (g *Group) DoChan(_ context.Context, key string, fn func() (interface{}, error)) <-chan singleflight.Result {
	return g.Group.DoChan(key, fn)
}

func (g *Group) do(ctx context.Context, label, key string, fn func() (interface{}, error)) (v interface{}, shared bool, err error) {
	v, err, shared = g.Group.Do(key, func() (interface{}, error) {
		start := util.TimeNow()
		ch := make(chan func() (interface{}, error), 1)
		go func() {
			var (
				v interface{}
				e error
			)
			wrapFn := func() (interface{}, error) {
				return v, e
			}
			defer func() {
				if r := recover(); r != nil { // 出现异常情况，返回错误
					v, e = nil, newPanicError(r)
					// Panic 统计
					panicCounter.With(prometheus.Labels{"key": label}).Inc()
					logger.Errorf("singleflight Do panic, key is (%+v) err is (%+v)", key, e)
				}
				select {
				case ch <- wrapFn:
				default: // 防止堵塞
				}
			}()
			v, e = fn()
		}()
		select {
		case getResultFn := <-ch:
			return getResultFn()
		case <-ctx.Done():
			// cancel 统计
			cancelCounter.With(prometheus.Labels{"key": label}).Inc()
			now := util.TimeNow()
			logger.Errorf("singleflight Do cancel, key is (%+v) cost is (%+v/ms)  err is (%+v)", key, float64(now.Sub(start))/float64(time.Millisecond), ctx.Err())
		}
		return nil, ctx.Err()
	})

	sharedStr := "not shared"
	if shared {
		sharedStr = "shared"
	}
	visitCounter.With(prometheus.Labels{"key": label, "shared": sharedStr}).Inc()

	return
}

// Forget forget
func (g *Group) Forget(_ context.Context, key string) {
	g.Group.Forget(key)
}

type panicError struct {
	value interface{}
	stack []byte
}

// Error return error
func (p *panicError) Error() string {
	return fmt.Sprintf("%v\n\n%s", p.value, p.stack)
}

func newPanicError(v interface{}) error {
	stack := debug.Stack()

	if line := bytes.IndexByte(stack[:], '\n'); line >= 0 {
		stack = stack[line+1:]
	}
	return &panicError{value: v, stack: stack}
}
