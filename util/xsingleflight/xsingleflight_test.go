package xsingleflight

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"golang.org/x/sync/singleflight"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestGroup_Do(t *testing.T) {
	t.Run("cancel", func(t *testing.T) {
		g := &Group{
			Group: singleflight.Group{},
		}
		fn := func() (interface{}, error) {
			time.Sleep(time.Second * 2)
			return "ok", nil
		}

		ctx, cancel := context.WithCancel(context.Background())
		go func() {
			time.Sleep(time.Second)
			cancel()
		}()

		r, s, e := g.Do(ctx, "1", fn)
		assert.Equal(t, nil, r)
		assert.Equal(t, context.Canceled, e)
		assert.Equal(t, false, s)
	})

	t.Run("timeout", func(t *testing.T) {
		g := &Group{
			Group: singleflight.Group{},
		}
		fn := func() (interface{}, error) {
			time.Sleep(time.Second * 2)
			return "ok", nil
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()

		r, s, e := g.Do(ctx, "1", fn)
		assert.Equal(t, nil, r)
		assert.Equal(t, context.DeadlineExceeded, e)
		assert.Equal(t, false, s)
	})

	t.Run("redo", func(t *testing.T) {
		g := &Group{
			Group: singleflight.Group{},
		}
		seconds := 10
		fn := func() (interface{}, error) {
			s := seconds
			time.Sleep(time.Second * time.Duration(s))
			return fmt.Sprintf("ok: %d", s), nil
		}
		wg := &sync.WaitGroup{}
		for i := 0; i < 2; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
				defer cancel()
				r, s, e := g.Do(ctx, "1", fn)
				assert.Equal(t, nil, r)
				assert.Equal(t, context.DeadlineExceeded, e)
				assert.Equal(t, true, s)
			}()
		}
		time.Sleep(time.Second * 3)
		seconds = 1
		// 可以正常使用
		r, s, e := g.Do(context.Background(), "1", fn)
		assert.Equal(t, "ok: 1", r)
		assert.Equal(t, nil, e)
		assert.Equal(t, false, s)
		wg.Wait()
	})
}
