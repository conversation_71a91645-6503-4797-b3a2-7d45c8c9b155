package util

import (
	"math/rand"
	"runtime"
	"sync"
)

// Logger interface
type Logger interface {
	Errorf(format string, v ...interface{})
}

var goroutineLogger Logger

// InitGoroutineLogger sets the logger used by Go function
func InitGoroutineLogger(lg Logger) {
	goroutineLogger = lg
}

var concurrentWaitGroup sync.WaitGroup

// WaitGo 等待结束
func WaitGo() {
	// 此处会阻塞
	concurrentWaitGroup.Wait()
}

func recoveryLog(p any) {
	// From net/http/server.go
	// https://github.com/golang/go/blob/release-branch.go1.13/src/net/http/server.go#L1765
	const size = 64 << 10
	buf := make([]byte, size)
	// if the size of stack tracing message is larger than 64k, the message will be truncated.
	buf = buf[:runtime.Stack(buf, false)]
	goroutineLogger.Errorf("Panic recovered, %v\n%s", p, buf)
}

// Go is a simple wrapper of `go f()`. It starts a new goroutine to call f.
// It recovers the panic from the same goroutine where f is called.
func Go(f func()) {
	if goroutineLogger == nil {
		panic("goroutineLogger is not configured")
	}

	concurrentWaitGroup.Add(1)
	go func() {
		defer func() {
			if p := recover(); p != nil {
				recoveryLog(p)
			}
			concurrentWaitGroup.Done()
		}()

		f()
	}()
}

// GoArg1 wraps a function with a goroutine with one argument.
func GoArg1[T any](f func(T), arg1 T) {
	if goroutineLogger == nil {
		panic("goroutineLogger is not configured")
	}

	concurrentWaitGroup.Add(1)
	go func() {
		defer func() {
			if p := recover(); p != nil {
				recoveryLog(p)
			}
			concurrentWaitGroup.Done()
		}()

		f(arg1)
	}()
}

// LockedSource makes the Rand.Intn method itself safe to use in concurrent goroutines if Rand is using the LockedSource.
// Copied from https://github.com/golang/go/blob/af09ff1981063b28705726a86b913dfa98d70942/src/math/rand/rand.go#L382
type LockedSource struct {
	lk  sync.Mutex
	src rand.Source
}

// NewLockedSource returns a new LockedSource
func NewLockedSource(seed int64) *LockedSource {
	return &LockedSource{src: rand.NewSource(seed)}
}

// Int63 implements the rand.Source interface
func (r *LockedSource) Int63() (n int64) {
	r.lk.Lock()
	n = r.src.Int63()
	r.lk.Unlock()
	return
}

// Seed implements the rand.Source interface
func (r *LockedSource) Seed(seed int64) {
	r.lk.Lock()
	r.src.Seed(seed)
	r.lk.Unlock()
}
