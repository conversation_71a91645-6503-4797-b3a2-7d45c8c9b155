package util

import "time"

// time second
// TODO: 之后逐步迁移 live-service 相关引用
const (
	SecondOneMinute int64 = 60                   // 一分钟时长（单位：秒）
	SecondOneHour         = 60 * SecondOneMinute // 一小时时长（单位：秒）
	SecondOneDay          = 24 * SecondOneHour   // 一天时长（单位：秒）
)

// TimeFormats
const (
	TimeFormatYMD  = "2006-01-02"
	TimeFormatHMS  = "2006-01-02 15:04:05"
	TimeFormatHHMM = "15:04"

	TimeFormatYMDWithNoSpace = "20060102"
)

// TimeUnixMilli 毫秒级时间戳
type TimeUnixMilli int64

// NewTimeUnixMilli returns t as a Unix time, the number of millseconds elapsed
// since January 1, 1970 UTC. The result does not depend on the
// location associated with t.
func NewTimeUnixMilli(t time.Time) TimeUnixMilli {
	return TimeUnixMilli(t.UnixNano() / 1e6)
}

// ToTime 毫秒时间戳转换成 time.Time
func (u TimeUnixMilli) ToTime() time.Time {
	return time.Unix(0, int64(u)*1e6)
}

// Milliseconds converts time.Duration d to milliseconds
func Milliseconds(d time.Duration) int64 {
	return int64(d / time.Millisecond)
}

// WeekdayToInt 将 time.Weekday 转换成星期一作为起始的 int 值
func WeekdayToInt(weekDay time.Weekday) int {
	return (int(weekDay) + 6) % 7
}

// BeginningOfDay 一天的开始
func BeginningOfDay(t time.Time) time.Time {
	y, m, d := t.Date()
	return time.Date(y, m, d, 0, 0, 0, 0, t.Location())
}

// BeginningOfWeek 一周的开始（按照周一为第一天算）
func BeginningOfWeek(t time.Time) time.Time {
	t = BeginningOfDay(t)
	weekday := WeekdayToInt(t.Weekday())
	if weekday == 0 {
		// 周一
		return t
	}
	return t.Add(-24 * time.Hour * time.Duration(weekday))
}

var timeNow func() time.Time

// TimeNow 获取当前时间
func TimeNow() time.Time {
	if timeNow == nil {
		return time.Now() //nolint:forbidigo
	}
	return timeNow()
}

// TimeLte l <= r
func TimeLte(l, r time.Time) bool {
	return !l.After(r)
}

// TimeGte l >= r
func TimeGte(l, r time.Time) bool {
	return !l.Before(r)
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time
	EndTime   time.Time
}

// NewTimeRange new TimeRange
func NewTimeRange(st, et time.Time) TimeRange {
	return TimeRange{
		StartTime: st,
		EndTime:   et,
	}
}

// NewTimeRangeUnix 从时间戳生成 TimeRange
func NewTimeRangeUnix(st, et int64) TimeRange {
	return TimeRange{
		StartTime: time.Unix(st, 0),
		EndTime:   time.Unix(et, 0),
	}
}

// Between 返回某时刻是否在时间范围内，左闭右开 st <= when < et
func (tr TimeRange) Between(when time.Time) bool {
	return TimeGte(when, tr.StartTime) && when.Before(tr.EndTime)
}

// Overlap 返回两个时间范围是否有重叠部分
/* 只有两种情况不重叠：
-----*****
*****-----
不重叠的情况：a.et <= b.st || b.et <= a.st
取反则是重叠的情况 !(a.et <= b.st || b.et <= a.st)
整合取反和括号 a.et > b.st && b.et > a.st
调整逻辑顺序 a.st < b.et && a.et > b.st
*/
// 代码实现范例
func (tr TimeRange) Overlap(tr2 TimeRange) bool {
	return tr.StartTime.Before(tr2.EndTime) &&
		tr.EndTime.After(tr2.StartTime)
}
