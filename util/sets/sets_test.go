package sets

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDiff(t *testing.T) {
	assert := assert.New(t)
	a := []int64{0, 1, 2, 2, 0, 0}
	var b []int64
	assert.ElementsMatch([]int64{0, 1, 2}, Diff(a, b))
	diffEmpty := Diff(b, a)
	assert.Empty(diffEmpty)
	assert.NotNil(diffEmpty)

	b = []int64{1, 2, 4, 5, 5}
	assert.ElementsMatch([]int64{0}, Diff(a, b))
	diffEmpty = Diff(b, b)
	assert.Empty(diffEmpty)
	assert.NotNil(diffEmpty)

	c := []string{"1", "1", "2", "3", "2"}
	d := []string{"2", "3", "4", "2", "5", "4"}
	assert.ElementsMatch([]string{"1"}, Diff(c, d))
	assert.ElementsMatch([]string{"4", "5"}, Diff(d, c))
}

func TestUniq(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(Uniq([]int64{}))
	assert.ElementsMatch([]int64{0, 1, 2, 3, 4}, Uniq([]int64{0, 4, 1, 2, 2, 3, 1, 4}))
	assert.ElementsMatch([]int64{1, 2, 4, 5}, Uniq([]int64{4, 4, 1, 2, 4, 5}))
	assert.ElementsMatch([]string{"1", "2", "3"}, Uniq([]string{"3", "1", "1", "2", "3"}))
	assert.ElementsMatch([]string{"2", "3", "4"}, Uniq([]string{"2", "3", "4"}))
}
