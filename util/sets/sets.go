package sets

// Diff returns elem in s1 but not in s2，差集，传入 [1, 3, 2, 3, 1, 1], [2, 3, 4, 4, 2] 返回 [1]
// 参考 github 上同类型方法在求差集时实现去重：https://github.com/thoas/go-funk/blob/main/intersection.go#L75
func Diff[T comparable](s1, s2 []T) []T {
	s2Map := make(map[T]struct{}, len(s2))
	for _, v := range s2 {
		s2Map[v] = struct{}{}
	}

	res := make([]T, 0, len(s1))
	resMap := make(map[T]struct{}, len(s1))
	var ok bool
	for _, v := range s1 {
		if _, ok = s2Map[v]; ok {
			// 跳过在 s2 中也出现的元素
			continue
		}
		if _, ok = resMap[v]; !ok {
			resMap[v] = struct{}{}
			res = append(res, v)
		}
	}
	return res
}

// Uniq removes duplicates from a slice，重复数据消除，传入 [1, 6, 1, 8, 6] 返回 [1, 6, 8]
func Uniq[T comparable](slice []T) []T {
	uniqueMap := make(map[T]struct{}, len(slice))
	uniqueSlice := make([]T, 0, len(slice))
	for _, v := range slice {
		if _, ok := uniqueMap[v]; !ok {
			uniqueMap[v] = struct{}{}
			uniqueSlice = append(uniqueSlice, v)
		}
	}
	return uniqueSlice
}
