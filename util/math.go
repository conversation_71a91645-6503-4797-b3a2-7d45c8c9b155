package util

import (
	"errors"
	"math/rand"
	"sort"
)

// BitMask bit 位，最大支持 16 位
type BitMask int16

// Set 第 i 位置 1
func (b *BitMask) Set(i int) {
	*b |= (1 << (i - 1))
}

// IsSet 判断是否第 i 位为 1
func (b BitMask) IsSet(i int) bool {
	return b&(1<<(i-1)) != 0
}

// Unset 第 i 位置 0
func (b *BitMask) Unset(i int) {
	*b &^= (1 << (i - 1))
}

// Distribution 随机变量的分布
type Distribution interface {
	Next() float64
	NextInt() int
}

// DiscreteDistribution 离散型随机变量的分布
type DiscreteDistribution struct {
	c      []int // cumulative sum of weight
	sum    int
	r      *rand.Rand
	search func([]int, int) int
	index  []int
}

// linearSearch searches the slice for x. The slice must be sorted in ascending order.
func linearSearch(a []int, x int) int {
	for i, v := range a {
		if x <= v {
			return i
		}
	}
	return len(a) - 1
}

// binarySearch searches the slice for x. The slice must be sorted in ascending order.
func binarySearch(a []int, x int) int {
	i := sort.SearchInts(a, x)
	if i < len(a) {
		return i
	}
	return len(a) - 1
}

// NextInt returns a random integer i in the range of [0, len(weights)). The possibility of i is weights[i]/total_weights.
func (d *DiscreteDistribution) NextInt() int {
	return d.index[d.search(d.c, d.r.Intn(d.sum)+1)]
}

// Next implements the Distribution interface
func (d *DiscreteDistribution) Next() float64 {
	return float64(d.NextInt())
}

// NewDiscreteDistribution creates a new DiscreteDistribution
// weights 权重
// source 如果 source 是线程安全的，Next() 就是线程安全的
// useBinarySearch 是否采用二分搜索，当 weights 数量很多，没有 dominant 的权重的情况下使用
func NewDiscreteDistribution(weights []int, source rand.Source, useBinarySearch bool) (Distribution, error) {
	if len(weights) <= 0 {
		return nil, errors.New("weights can not be empty")
	}

	slice := make([]struct {
		Index  int
		Weight int
	}, len(weights))
	for i := range slice {
		slice[i].Index = i
		slice[i].Weight = weights[i]
	}

	// sort slice by weights in descending order, item with the largest weight will be the first. (the most efficient way to use linear search)
	sort.Slice(slice, func(i, j int) bool {
		return slice[i].Weight > slice[j].Weight
	})

	mapIndex := make([]int, len(slice))
	for i, v := range slice {
		mapIndex[i] = v.Index
	}

	w := make([]int, len(slice))
	for i := range w {
		if slice[i].Weight < 0 {
			return nil, errors.New("weights can not be negative")
		}
		if i == 0 {
			w[i] = slice[i].Weight
		} else {
			w[i] = w[i-1] + slice[i].Weight
		}
	}
	sum := w[len(w)-1]
	if sum <= 0 {
		return nil, errors.New("sum of weights is 0")
	}

	d := &DiscreteDistribution{
		c:     w,
		sum:   sum,
		r:     rand.New(source),
		index: mapIndex,
	}
	if useBinarySearch {
		d.search = binarySearch
	} else {
		d.search = linearSearch
	}
	return d, nil
}

// SumInt calculates sum of int
func SumInt(in ...int) int {
	total := 0
	for _, v := range in {
		total += v
	}
	return total
}

// MaxInt64 最大值
func MaxInt64(a int64, b ...int64) int64 {
	for _, num := range b {
		if num > a {
			a = num
		}
	}
	return a
}

// MinInt64 最小值
func MinInt64(a int64, b ...int64) int64 {
	for _, num := range b {
		if num < a {
			a = num
		}
	}
	return a
}
