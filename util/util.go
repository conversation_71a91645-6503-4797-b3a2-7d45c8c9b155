package util

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var emailRegex = regexp.MustCompile(`^([^@]+)@(.+)$`) // 邮箱匹配正则

// Dict is a short name for map[string]interface{}
type Dict map[string]interface{}

// HasElem checks whether slice/array `s` contains element `elem`
func HasElem(s interface{}, elem interface{}) bool {
	arrV := reflect.ValueOf(s)

	if arrV.Kind() == reflect.Slice || arrV.Kind() == reflect.Array {
		for i := 0; i < arrV.Len(); i++ {
			// XXX - panics if slice element points to an unexported struct field
			// see https://golang.org/pkg/reflect/#Value.Interface
			if arrV.Index(i).Interface() == elem {
				return true
			}
		}
	}

	return false
}

var specialReg = regexp.MustCompile(`[\x00-\x08\x0B-\x0C\x0E-\x1F\r]`)

// FilterSpecialCodes 过滤字符串 str 中的特殊字符
// REVIEW: 现在过滤的字符可能不完全，需要确认后完善
func FilterSpecialCodes(str string, filterNewLine bool) string {
	if str == "" {
		return ""
	}
	ret := specialReg.ReplaceAllLiteralString(str, "")
	if filterNewLine {
		return strings.Replace(ret, "\n", "", -1)
	}
	return ret
}

// GroupBy is a handy function. It groups records by value of the specified field. The field should be a string.
// See https://github.com/MiaoSiLa/missevan-app/blob/master/components/util/MUtils.php (MUtils::groupArray)
func GroupBy(records []Dict, fieldName string) map[string][]Dict {
	m := make(map[string][]Dict)
	for _, record := range records {
		if field, ok := record[fieldName]; ok {
			if k, ok := field.(string); ok {
				m[k] = append(m[k], record)
			}
		}
	}
	return m
}

// AppendStringOnce appends b to a if b is not in a
func AppendStringOnce(a []string, b ...string) []string {
	for _, bb := range b {
		isNew := true
		for _, aa := range a {
			if bb == aa {
				isNew = false
			}
		}
		if isNew {
			a = append(a, bb)
		}
	}
	return a
}

// IgnoreUntil ignores characters from s until met the rune 'r' n times.
// if no such rune, the original s is returned.
// e.g. IgnoreUntil("1,2,3", 2, ',') == "3"
func IgnoreUntil(s string, n int, r rune) string {
	if n <= 0 {
		return s
	}
	runes := []rune(s)
	for i, v := range runes {
		if v == r {
			n--
			if n <= 0 {
				return string(runes[i+1:])
			}
		}
	}
	return s
}

// MosaicType 打码类型
type MosaicType int

// 打码类型
const (
	MosaicRealName MosaicType = iota
	MosaicPhoneNumber
	MosaicEmail
	MosaicIDNumber
	MosaicBankCardNumber
)

// MosaicString 字符串脱敏函数
// https://github.com/MiaoSiLa/requirements-doc/issues/209
func MosaicString(s string, m MosaicType) string {
	switch m {
	case MosaicRealName:
		// 刘**
		runes := []rune(s)
		if len(runes) > 0 {
			n := len(runes) - 1
			if n <= 0 {
				n = 1
			}
			return string(runes[0]) + strings.Repeat("*", n)
		}
	case MosaicPhoneNumber:
		// 186******88
		// +86186******88
		prefix := ""
		if len(s) > 3 && s[0] == '+' {
			// TODO: 其它的区号
			prefix = s[:3]
			s = s[3:]
		}
		if len(s) > 3+2 {
			return prefix + string(s[:3]) + strings.Repeat("*", len(s)-3-2) + string(s[len(s)-2:])
		}
	case MosaicEmail:
		// <EMAIL> -> 1******@gmail.com
		// <EMAIL> -> tes****@163.com
		// <EMAIL> -> nih****@163.com
		at := ""

		i := strings.LastIndex(s, "@")
		if i >= 0 {
			at = s[i:]
			s = s[:i]
		}

		if len(s) >= 3 {
			return string(s[:3]) + "****" + at
		}
		return s + strings.Repeat("*", 3-len(s)+4) + at
	case MosaicIDNumber:
		// 23***************4
		if len(s) > 2+1 {
			return string(s[:2]) + strings.Repeat("*", len(s)-2-1) + string(s[len(s)-1:])
		}
	case MosaicBankCardNumber:
		// 62************34
		if len(s) > 2+2 {
			return string(s[:2]) + strings.Repeat("*", len(s)-2-2) + string(s[len(s)-2:])
		}
	}
	return s
}

// JoinIntArray join int array by specific string
func JoinIntArray(pieces []int, glue string) string {
	if len(pieces) == 0 {
		return ""
	}
	stringPieces := make([]string, len(pieces))
	for i, v := range pieces {
		stringPieces[i] = strconv.Itoa(v)
	}
	return strings.Join(stringPieces, glue)
}

// JoinInt64Array join int64 array by specific string
func JoinInt64Array(pieces []int64, glue string) string {
	if len(pieces) == 0 {
		return ""
	}
	stringPieces := make([]string, len(pieces))
	for i, v := range pieces {
		stringPieces[i] = strconv.FormatInt(v, 10)
	}
	return strings.Join(stringPieces, glue)
}

// NextDayTime 获得 when 的第二天零点时间戳
func NextDayTime(when time.Time) time.Time {
	y, m, d := when.Date()
	return time.Date(y, m, d+1, 0, 0, 0, 0, when.Location())
}

// Uniq 去重
// TODO: 用接口重写以支持更多
// Deprecated: 使用 sets.Uniq 代替此 func
func Uniq(arrs []int64) []int64 {
	set := make(map[int64]struct{}, len(arrs))
	arrUniq := make([]int64, 0, len(arrs))
	for _, v := range arrs {
		if _, ok := set[v]; !ok {
			set[v] = struct{}{}
			arrUniq = append(arrUniq, v)
		}
	}

	return arrUniq
}

const letters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

// GetRandomString get random string
func GetRandomString(length int) string {
	bytes := []byte(letters)
	bytesLen := len(bytes)
	var result []byte
	for i := 0; i < length; i++ {
		result = append(result, bytes[rand.Intn(bytesLen)])
	}
	return string(result)
}

// RandomCode 获取由数字组成的固定位数字符串
func RandomCode(length int) string {
	maxNum := int(math.Pow10(length))
	return fmt.Sprintf("%0*d", length, rand.Intn(maxNum))
}

// IsEmail 验证邮箱是否合法
func IsEmail(email string) bool {
	return emailRegex.MatchString(email)
}

// BeginningOfMonth 返回月份开始时间（返回示例：2020-01-01 00:00:00）
func BeginningOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// EndOfMonth 返回传入月份的下个月 0 点，注意使用开区间（使用示例：传入 2020-01-01 00:00:00，返回 2020-02-01 00:00:00）
func EndOfMonth(t time.Time) time.Time {
	return BeginningOfMonth(t).AddDate(0, 1, 0)
}

// ToMap transforms a slice or array of structs to a map based on a pivot field: []*Foo => Map<int, *Foo>
// Code from https://github.com/thoas/go-funk, with slight modification.
// 使用 in 的长度来作为输出 map 的初始化容量
func ToMap(in interface{}, pivot string) interface{} {
	value := reflect.ValueOf(in)

	// input value must be a slice or array
	if value.Kind() != reflect.Slice && value.Kind() != reflect.Array {
		panic("input must be a slice or array")
	}
	return toMapWithSize(value, pivot, value.Len())
}

// ToMapWithSize transforms a slice or array of structs to a map based on a pivot field: []*Foo => Map<int, *Foo>
// Code from https://github.com/thoas/go-funk, with slight modification.
// n is the pre-allocated size of the map.
// NOTICE: This function should be used in the same package where the struct type of in is defined, to avoid typed coupling.
// 从 benchmark 的结果看，运行速度相比原生写法，在数量级上差不太多
func ToMapWithSize(in interface{}, pivot string, n int) interface{} {
	value := reflect.ValueOf(in)

	// input value must be a slice or array
	if value.Kind() != reflect.Slice && value.Kind() != reflect.Array {
		panic("input must be a slice or array")
	}
	return toMapWithSize(value, pivot, n)
}

func toMapWithSize(value reflect.Value, pivot string, n int) interface{} {
	sliceType := value.Type()

	// retrieve the struct in the slice to deduce key type
	structType := sliceType.Elem()
	if structType.Kind() == reflect.Ptr {
		structType = structType.Elem()
	}

	keyField, ok := structType.FieldByName(pivot)
	if !ok {
		return nil
	}

	// value of the map will be the input type
	collectionType := reflect.MapOf(keyField.Type, sliceType.Elem())

	// create a map from scratch
	collection := reflect.MakeMapWithSize(collectionType, n)

	for i := 0; i < value.Len(); i++ {
		element := value.Index(i)
		key := reflect.Indirect(element).FieldByName(pivot)
		collection.SetMapIndex(key, element)
	}

	return collection.Interface()
}

// BoolToInt bool 转 int
func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// IntToBool int 转 bool
func IntToBool(i int) bool {
	return i != 0
}

// NewBool bool 转指针类型
func NewBool(b bool) *bool {
	return &b
}

// NewInt creates new int pointer
func NewInt(i int) *int {
	return &i
}

// NewInt64 creates new int64 pointer
func NewInt64(i int64) *int64 {
	return &i
}

// NewFloat64 creates new float64 pointer
func NewFloat64(i float64) *float64 {
	return &i
}

// NewString creates new string pointer
func NewString(s string) *string {
	return &s
}

// Float2DP is a type of float with 2 decimal places when encoded to json
type Float2DP float64

// MarshalJSON implements the Marshaler interface
func (f Float2DP) MarshalJSON() ([]byte, error) {
	s := strconv.FormatFloat(float64(f), 'f', 2, 64)
	return []byte(s), nil
}

// UnmarshalJSON implements the Unmarshaler interface
func (f *Float2DP) UnmarshalJSON(bytes []byte) error {
	var v interface{}
	err := json.Unmarshal(bytes, &v)
	if err != nil {
		return err
	}

	if v == nil {
		// JSON null
		return nil
	}

	switch t := v.(type) {
	case float64:
		*f = Float2DP(t)
	case string:
		n, err := strconv.ParseFloat(t, 64)
		if err != nil {
			return err
		}
		*f = Float2DP(n)
	default:
		return fmt.Errorf("json: cannot unmarshal %q into Go value of type Float2DP", bytes)
	}
	return nil
}

// Includes The method determines whether an array includes a certain value among its entries, returning true or false as appropriate
func Includes(length int, fn func(i int) bool) bool {
	for i := 0; i < length; i++ {
		if fn(i) {
			return true
		}
	}
	return false
}

// FindIndex The method returns the index of the first element in the array
// that satisfies the provided testing function. Otherwise,
// it returns -1, indicating that no element passed the test.
func FindIndex(length int, fn func(i int) bool) int {
	for i := 0; i < length; i++ {
		if fn(i) {
			return i
		}
	}
	return -1
}

// IsLeapYear checks whether a year is a leap year
func IsLeapYear(year int) bool {
	return year%4 == 0 && (year%100 != 0 || year%400 == 0)
}

// TruncateString 截断字符串
// NOTICE: 不会处理类似 emoji 之类的由多个字符组成的符号
func TruncateString(s string, length int, suffix string) string {
	runes := []rune(s)
	if len(runes) <= length {
		return s
	}
	return string(runes[:length]) + suffix
}

// TruncateResponseBodyWithNotice 截断 response body，并添加截断提示
func TruncateResponseBodyWithNotice(body string) string {
	return TruncateString(body, 128, " (truncated...)")
}

// SliceMap 对切片中的元素进行映射，返回新的切片，新切片中的元素为 f(v) 的返回值
// 示例：
// [1,2,3].map(i => i + 1) -> [2,3,4]
// [1,2,3].map(i => i * 2) -> [2,4,6]
func SliceMap[V any, F any](s []V, f func(V) F) []F {
	result := make([]F, 0, len(s))
	for _, v := range s {
		result = append(result, f(v))
	}
	return result
}

// ChunkSlice 将切片按指定大小分组
// 如果不指定 size 或 size <= 0，则使用默认大小 500
// 返回二维切片，每个子切片的长度最大为 size
func ChunkSlice[T any](slice []T, size ...int) [][]T {
	chunkSize := 500
	if len(size) > 0 && size[0] > 0 {
		chunkSize = size[0]
	}
	length := len(slice)
	chunks := make([][]T, 0, (length+chunkSize-1)/chunkSize)
	for i := 0; i < length; i += chunkSize {
		end := i + chunkSize
		if end > length {
			end = length
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}
