package security

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/google/uuid"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// IsValidSecurityUUID 检查 UUID 中的信息是否合法
// validTimeSinceUnix 一般配置为 HTTPS 证书的开始时间，在保证接口成功的前提下，新生成的 UUID 中解析出的时间不能比这个早
func IsValidSecurityUUID(uuidStr string, hmacKey string, validTimeSinceUnix int64) bool {
	if uuidStr == "" {
		return false
	}
	// 将字符串解析为 uuid.UUID 对象
	uuidObj, err := uuid.Parse(uuidStr)
	if err != nil {
		// PASS
		return false
	}
	timeUnix, _ := uuidObj.Time().UnixTime()
	if timeUnix <= validTimeSinceUnix {
		// 校验时间戳是否过期
		return false
	}
	uuidHash := uuidStr[len(uuidStr)-8:]
	uuidStr = strings.ReplaceAll(uuidStr, "-", "")
	b, err := hex.DecodeString(uuidStr)
	if err != nil {
		logger.WithField("uuid", uuidStr).Error(err)
		return false
	}
	// 去掉最后 4 个字节并补上两个常量位
	result := make([]byte, len(b)-2)
	at := copy(result, b[:8])
	at += copy(result[at:], []byte{3, 0})
	copy(result[at:], b[8:len(b)-4])
	// 使用 HMAC-SHA1 生成签名
	hmacSHA1 := hmac.New(sha1.New, []byte(hmacKey))
	_, _ = hmacSHA1.Write(result)
	sign := base64.StdEncoding.EncodeToString(hmacSHA1.Sum(nil))
	// 转换为 8 位的十六进制字符串
	signHashStr := fmt.Sprintf("%08x", uint32(hashCode(sign)))
	return uuidHash == signHashStr
}

// 计算字符串哈希
func hashCode(str string) int32 {
	var hash int32
	for i := 0; i < len(str); i++ {
		hash = (31 * hash) + int32(str[i])
	}
	return hash
}
