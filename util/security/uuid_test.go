package security

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsValidSecurityUUID(t *testing.T) {
	assert := assert.New(t)

	ok := IsValidSecurityUUID("ea90a4d0-0f46-11ef-a014-afbe04ef3d01", "testkey", 0)
	assert.False(ok)

	// 时间无效
	ok = IsValidSecurityUUID("ea90a4d0-0f46-11ef-a014-afbe04ef3d01", "testkey", 9999999999999)
	assert.False(ok)

	ok = IsValidSecurityUUID("ea90a4d0-0f46-11ef-a014-afbe7c141ae1", "testkey", 0)
	assert.True(ok)
}

func TestHashCode(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int32(-432559497), hashCode("xfoLM/sbJhZfLDUX8WABJUolZLA="))
	assert.Equal(int32(82787585), hashCode("wnt47intsQdiXPR0g+8AZ8hKXWQ="))
	assert.Equal(int32(3556498), hashCode("test"))
}
