package util

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTimeUnixMilli(t *testing.T) {
	assert := assert.New(t)
	testTime := time.Unix(1234567890, 123456789)
	assert.Equal(TimeUnixMilli(1234567890123), NewTimeUnixMilli(testTime))

	testTime = time.Unix(123456789, 123000000)
	assert.Equal(testTime, TimeUnixMilli(123456789123).ToTime())
}

func TestMilliSeconds(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(int64(1000), Milliseconds(time.Second))
}

func TestWeekday(t *testing.T) {
	assert := assert.New(t)
	weekdays := [7]time.Weekday{
		time.Sunday,
		time.Monday,
		time.Tuesday,
		time.Wednesday,
		time.Thursday,
		time.Friday,
		time.Saturday}
	testInt := [7]int{6, 0, 1, 2, 3, 4, 5}
	for i := 0; i < 7; i++ {
		assert.Equal(testInt[i], WeekdayToInt(weekdays[i]))
	}
}

func TestBeginningOfDay(t *testing.T) {
	assert := assert.New(t)
	tm := time.Date(2020, 5, 20, 6, 30, 0, 0, time.Local)
	assert.Equal(time.Date(2020, 5, 20, 0, 0, 0, 0, time.Local), BeginningOfDay(tm))
}

func TestBeginningOfWeek(t *testing.T) {
	assert := assert.New(t)

	for i := 1; i <= 7; i++ {
		t := time.Date(2018, 1, i, 15, 4, 5, 0, time.Local)
		assert.Equal("2018-01-01 00:00:00", BeginningOfWeek(t).Format(TimeFormatHMS))
		t = time.Date(2021, 5, i+2, 15, 4, 5, 0, time.Local)
		assert.Equal("2021-05-03 00:00:00", BeginningOfWeek(t).Format(TimeFormatHMS))
	}
}

func TestTimeNow(t *testing.T) {
	assert := assert.New(t)

	defer SetTimeNow(nil)
	now := time.Now() //nolint:forbidigo
	timeNow := TimeNow()
	assert.True(now.Before(timeNow))
	SetTimeNow(func() time.Time {
		return time.Unix(10000000000, 0)
	})
	now = TimeNow()
	assert.Equal(int64(10000000000), now.Unix())
}

func TestTimeGteLte(t *testing.T) {
	assert := assert.New(t)

	l := time.Unix(1, 0)
	r := time.Unix(2, 0)
	assert.True(TimeLte(l, l))  // 相等
	assert.True(TimeLte(l, r))  // 小于
	assert.False(TimeLte(r, l)) // 大于

	assert.True(TimeGte(l, l))  // 相等
	assert.False(TimeGte(l, r)) // 小于
	assert.True(TimeGte(r, l))  // 大于
}

func TestNewTimeRange(t *testing.T) {
	assert := assert.New(t)

	st := time.Unix(1, 0)
	et := time.Unix(2, 0)
	expect := TimeRange{
		StartTime: st,
		EndTime:   et,
	}
	assert.Equal(expect, NewTimeRange(st, et))
	assert.Equal(expect, NewTimeRangeUnix(1, 2))
}

func TestTimeRangeBetween(t *testing.T) {
	assert := assert.New(t)

	tp := NewTimeRange(time.Unix(1, 0), time.Unix(3, 0))
	expected := []bool{false, true, true, false, false}
	for i := int64(0); i < int64(len(expected)); i++ {
		assert.Equal(expected[i], tp.Between(time.Unix(i, 0)), i)
	}
}

func TestTimeRangeOverlap(t *testing.T) {
	assert := assert.New(t)

	tr1 := NewTimeRange(time.Unix(3, 0), time.Unix(5, 0))
	tr2 := NewTimeRange(time.Unix(6, 0), time.Unix(8, 0))

	// 挪动 tp2 的 st
	expected := []bool{false, false, true, true, true, true, true}
	ts := []int64{6, 5, 4, 3, 2, 1, 0}
	for i := 0; i < len(ts); i++ {
		tr2.StartTime = time.Unix(ts[i], 0)
		assert.Equal(expected[i], tr1.Overlap(tr2), i)
	}
	// 挪动 tp2 的 et
	expected = []bool{true, true, true, true, true, false, false}
	ts = []int64{8, 7, 6, 5, 4, 3, 2}
	for i := 0; i < len(ts); i++ {
		tr2.EndTime = time.Unix(ts[i], 0)
		assert.Equal(expected[i], tr1.Overlap(tr2), i)
	}
	assert.Equal(NewTimeRange(time.Unix(0, 0), time.Unix(2, 0)), tr2)
}
