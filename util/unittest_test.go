package util

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSetTimeNow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer func() { timeNow = nil }()
	cleanup := SetTimeNow(func() time.Time {
		return time.Unix(100000, 0)
	})
	defer cleanup()
	require.NotNil(timeNow)
	assert.Equal(int64(100000), timeNow().Unix())

	// 测试 cleanup
	cleanup()
	assert.Nil(timeNow)

	SetTimeNow(func() time.Time { return time.Unix(1, 0) })
	SetTimeNow(nil)
	assert.Nil(timeNow)
}
