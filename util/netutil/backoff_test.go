package netutil

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestBackoff(t *testing.T) {
	assert := assert.New(t)

	bk := BackoffConfig{
		MaxDelay:  120 * time.Second,
		BaseDelay: 1.0 * time.Second,
		Factor:    1.6,
		Jitter:    0, // no random delay
	}
	assert.Equal("1s", bk.Backoff(0).String())
	assert.Equal("1.6s", bk.Backoff(1).String())
	assert.Equal("2m0s", bk.Backoff(1000).String())
}
