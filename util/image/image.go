package image

import (
	"errors"
	"image"
	"io"

	"github.com/disintegration/imaging"
)

// Format is an image file format.
type Format int

// Image file formats.
const (
	INVALID Format = -1
	JPEG    Format = Format(imaging.JPEG)
	PNG     Format = Format(imaging.PNG)
	GIF     Format = Format(imaging.GIF)
)

var formatNames = map[Format]string{
	JPEG: "JPEG",
	PNG:  "PNG",
	GIF:  "GIF",
}

func (f Format) String() string {
	return formatNames[f]
}

// ErrImageTooSmall 图片的长/宽小于目标区域的长/宽
var ErrImageTooSmall error = errors.New("Image: dimension too small")

// ErrUnsupportedFormat 不支持的图片格式
var ErrUnsupportedFormat error = imaging.ErrUnsupportedFormat

// Convert 把图片 r 缩放至恰好填充以图片中心为中心的 width * height 的矩形区域，把矩形区域内的图像输出到 w
// 可以但不允许 width, height 超出图片 r
// 输入和输出都支持 jpeg, gif, png 格式
// format 指定输出格式
// 如果 width 为 0，则使用原图片的宽度；height 同样
func Convert(r io.Reader, w io.Writer, width, height int, format Format) (outW int, outH int, err error) {
	img, err := imaging.Decode(r, imaging.AutoOrientation(true))
	if err != nil {
		return 0, 0, err
	}

	imgW, imgH := img.Bounds().Dx(), img.Bounds().Dy()
	if imgW < width || imgH < height {
		return 0, 0, ErrImageTooSmall
	}

	if width == 0 {
		width = imgW
	}

	if height == 0 {
		height = imgH
	}

	var dstimg *image.NRGBA
	if width == imgW && height == imgH {
		dstimg = imaging.Clone(img)
	} else {
		dstimg = imaging.Fill(img, width, height, imaging.Center, imaging.Box)
	}

	err = imaging.Encode(w, dstimg, imaging.Format(format))
	if err != nil {
		return 0, 0, err
	}
	return dstimg.Bounds().Dx(), dstimg.Bounds().Dy(), nil
}

// FormatFromFilename 从文件名中获取图像格式
func FormatFromFilename(filename string) (Format, error) {
	format, err := imaging.FormatFromFilename(filename)
	if err != nil {
		return INVALID, err
	}
	f := Format(format)
	if _, ok := formatNames[f]; !ok {
		return INVALID, ErrUnsupportedFormat
	}
	return f, nil
}
