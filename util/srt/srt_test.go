package srt

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestToSRTBytes(t *testing.T) {
	require := require.New(t)

	testContent := []Elem{
		{
			StartTime:  1,
			EndTime:    15,
			Transcript: "语音内容1",
		},
		{
			StartTime:  17,
			EndTime:    65100,
			Transcript: "语音内容2",
		},
	}

	result, err := ToSRTBytes(testContent)
	require.NoError(err)
	require.NotEmpty(result)
	/*
		1
		00:00:00,010 --> 00:00:00,015
		语音内容1

		2
		00:00:00,017 --> 00:01:05,100
		语音内容2
	*/
}
