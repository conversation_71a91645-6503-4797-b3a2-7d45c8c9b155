package srt

import (
	"bytes"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 参考项目：https://github.com/asticode/go-astisub

// Constants
const (
	srtTimeBoundariesSeparator = " --> "
)

// Vars
var (
	bytesSRTTimeBoundariesSeparator = []byte(srtTimeBoundariesSeparator)
)

// Bytes
var (
	bytesBOM           = []byte{0xEF, 0xBB, 0xBF}
	bytesLineSeparator = []byte("\n")
)

// Errors
var (
	ErrNoSubtitlesToWrite = errors.New("srt: no subtitles to write")
)

// Subtitles represents an ordered list of items with formatting
type Subtitles struct {
	Items []*Item
}

// Item represents a text to show between 2 time boundaries with formatting
type Item struct {
	StartAt int64
	EndAt   int64
	Lines   []Line
}

// LineItem represents a formatted line item
type LineItem struct {
	Text string
}

// Line represents a set of formatted line items
type Line struct {
	Items []LineItem
}

// String implements the Stringer interface
func (l Line) String() string {
	var texts []string
	for _, i := range l.Items {
		texts = append(texts, i.Text)
	}
	return strings.Join(texts, " ")
}

// FormatPlayTime formats play time
func FormatPlayTime(t int64) (s string) {
	playTime := time.Duration(t) * time.Millisecond
	// parse hours
	hours := playTime / time.Hour
	// parse minutes
	n := playTime % time.Hour
	minutes := int(n / time.Minute)
	// parse seconds
	n = playTime % time.Minute
	seconds := int(n / time.Second)
	// parse milliseconds
	n = playTime % time.Second
	millisecond := n / time.Millisecond
	return fmt.Sprintf("%02d:%02d:%02d,%03d", hours, minutes, seconds, millisecond)
}

// BuildToSRT writes subtitles in .srt format
func (s Subtitles) BuildToSRT() ([]byte, error) {
	// Do not write anything if no subtitles
	if len(s.Items) == 0 {
		return nil, ErrNoSubtitlesToWrite
	}

	// Add BOM header
	var c bytes.Buffer
	c.Write(bytesBOM)

	// Loop through subtitles
	for k, v := range s.Items {
		// Add time boundaries
		c.Write([]byte(strconv.Itoa(k + 1)))
		c.Write(bytesLineSeparator)
		c.Write([]byte(FormatPlayTime(v.StartAt)))
		c.Write(bytesSRTTimeBoundariesSeparator)
		c.Write([]byte(FormatPlayTime(v.EndAt)))
		c.Write(bytesLineSeparator)

		// Loop through lines
		for _, l := range v.Lines {
			c.Write([]byte(l.String()))
			c.Write(bytesLineSeparator)
		}

		// Add new line
		c.Write(bytesLineSeparator)
	}
	b := c.Bytes()
	// Remove last new line
	return b[:len(b)-len(bytesLineSeparator)], nil
}
