package srt

// Elem srt elem
type Elem struct {
	StartTime  int64  `json:"start_time"` // start time (ms)
	EndTime    int64  `json:"end_time"`   // end time (ms)
	Transcript string `json:"transcript"` // subtitle content
}

// ToSRTBytes converts json subtitle into srt subtitle
func ToSRTBytes(subtitles []Elem) ([]byte, error) {
	newSub := new(Subtitles)

	items := make([]*Item, 0, len(subtitles))
	for i := range subtitles {
		line := []Line{
			{
				Items: []LineItem{{Text: subtitles[i].Transcript}},
			},
		}

		item := &Item{
			StartAt: subtitles[i].StartTime,
			EndAt:   subtitles[i].EndTime,
			Lines:   line,
		}
		items = append(items, item)
	}

	newSub.Items = items

	return newSub.BuildToSRT()
}
