package util

import "net/http"

// UserContext 用户上下文
type UserContext interface {
	Token() string
	ClientIP() string
	EquipID() string
	BUVID() string
	UserID() int64
	UserAgent() string
	Request() *http.Request
}

// SmartUserContext smart UserContext
type SmartUserContext struct {
	UID         int64
	IP          string
	UserToken   string
	UserEquipID string
	UserBUVID   string
	UA          string
	Req         *http.Request
}

// UserID 用户 ID
func (c SmartUserContext) UserID() int64 {
	return c.UID
}

// ClientIP 客户端 ip
func (c SmartUserContext) ClientIP() string {
	return c.IP
}

// Token 用户 token
func (c SmartUserContext) Token() string {
	return c.UserToken
}

// EquipID 用户 equip_id
func (c SmartUserContext) EquipID() string {
	return c.UserEquipID
}

// BUVID 返回 buvid
func (c SmartUserContext) BUVID() string {
	return c.UserBUVID
}

// UserAgent User-Agent
func (c SmartUserContext) UserAgent() string {
	return c.UA
}

// Request 返回 http 请求
// NOTICE: 可能是 nil
func (c SmartUserContext) Request() *http.Request {
	return c.Req
}
