package util

import (
	"math"
	"math/rand"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestBitMask(t *testing.T) {
	assert := assert.New(t)

	var b BitMask
	b.Set(1)
	assert.Equal(1, int(b))
	b.Set(2)
	assert.Equal(1+2, int(b))
	b.Set(3)
	assert.Equal(1+2+4, int(b))
	assert.True(b.IsSet(1))
	assert.True(b.IsSet(2))
	assert.True(b.IsSet(3))
	assert.False(b.IsSet(4))
	b.Unset(1)
	assert.Equal(2+4, int(b))
	b.Unset(2)
	assert.Equal(4, int(b))
	b.Unset(3)
	assert.Equal(0, int(b))

	assert.False(b.IsSet(16))
	b.Set(16)
	assert.Equal(uint16(1<<15), uint16(b))
	assert.True(b.IsSet(16))
	b.Unset(16)
	assert.Equal(0, int(b))
	assert.False(b.IsSet(16))
}

func BenchmarkDiscreteDistribution(b *testing.B) {
	data := make([]int, 0, 1000)
	for i := 1; i <= 1000; i++ {
		data = append(data, i)
	}
	randSource := NewLockedSource(TimeNow().Unix())
	b.Run("linearSearch", func(b *testing.B) {
		d, _ := NewDiscreteDistribution(data, randSource, false)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			d.NextInt()
		}
	})
	b.Run("binarySearch", func(b *testing.B) {
		d, _ := NewDiscreteDistribution(data, randSource, true)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			d.NextInt()
		}
	})
}

func TestDiscreteDistribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 非零最小权值
	minWeight := 1
	rate := 0.125
	epsilon := rate * float64(minWeight)

	weights := []int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, minWeight, 4, 5, 40, 50, 100, 9800}
	rand.Seed(TimeNow().Unix())
	rand.Shuffle(len(weights), func(i, j int) { weights[i], weights[j] = weights[j], weights[i] })
	totalWeights := SumInt(weights...)
	tutil.Debug(totalWeights)

	// 根据 epsilon 计算 scale 和 delta，使单元测试出错的概率至少 < 1/1000
	// 4*sigma <= scale*epsilon
	// sigma^2 = N*p*(1-p) = N*p*q
	// N = scale*totalWeights
	// p = minWeight / totalWeights
	q := 1 - float64(minWeight)/float64(totalWeights)
	scale := 16 * minWeight * int(math.Ceil(q/epsilon/epsilon))
	if scale == 0 {
		scale = 1
	}
	tutil.Debug(scale)

	E := make([]float64, len(weights))
	for i, v := range weights {
		E[i] = float64(scale) * float64(v)
	}
	tutil.Debug(E)

	delta := make([]float64, len(weights))
	for i, v := range weights {
		q := 1 - float64(v)/float64(totalWeights)
		delta[i] = 4 * math.Sqrt(E[i]*q)
	}
	tutil.Debug(delta)

	N := totalWeights * scale
	now := TimeNow().UnixNano()

	weightsCopy := make([]int, len(weights))
	copy(weightsCopy, weights)

	d, err := NewDiscreteDistribution(weights, NewLockedSource(now), false)
	require.NoError(err)
	X := make([]int, len(weights))
	for i := 0; i < N; i++ {
		X[d.NextInt()]++
	}
	tutil.Debug(X)

	d, err = NewDiscreteDistribution(weights, NewLockedSource(now), true)
	require.NoError(err)
	Y := make([]int, len(weights))
	for i := 0; i < N; i++ {
		Y[d.NextInt()]++
	}

	assert.Equal(X, Y)
	assert.Equal(weightsCopy, weights)

	for i, x := range X {
		if weights[i] == 0 {
			assert.Zero(delta[i])
			assert.Zero(x)
		} else {
			assert.Less(math.Abs(float64(x)-E[i]), delta[i])
		}
	}
}

func TestSumInt(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(10000, SumInt(1, 4, 5, 40, 50, 100, 9800))
}

func TestSearch(t *testing.T) {
	assert := assert.New(t)

	a := []int{0, 0, 1, 1, 1, 2, 2}
	assert.Equal(2, binarySearch(a, 1))
	assert.Equal(2, linearSearch(a, 1))
	assert.Equal(len(a)-1, binarySearch(a, 10))
	assert.Equal(len(a)-1, linearSearch(a, 10))
}

func TestMaxMin(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(int64(100), MaxInt64(0, 1, 100))
	assert.Equal(int64(-100), MinInt64(0, 1, -100))
}
