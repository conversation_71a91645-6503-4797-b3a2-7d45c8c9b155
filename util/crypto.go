package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/des"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"hash/crc32"
	"strings"
)

// ErrPaddingSize padding size error
var ErrPaddingSize = errors.New("padding size error")

// AES 加密方案
const (
	AESType128 = 16
	AESType192 = 24
	AESType256 = 32
)

// MD5 md5
func MD5(str string) string {
	h := md5.New()
	_, _ = h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// Base64 base64 for string
func Base64(src string) string {
	return base64.StdEncoding.EncodeToString([]byte(src))
}

// HmacSha1 hmac sha1
func HmacSha1(secretKey string, str string) string {
	h := hmac.New(sha1.New, []byte(secretKey))
	_, _ = h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// HmacSha256 hmac sha256
func HmacSha256(secretKey string, str string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	_, _ = h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// DesEncryption des-cbc encryption
// 使用 skey 和 siv 前 8 个字符
func DesEncryption(skey, siv, plainText string) (string, error) {
	iv, block, err := desPretreatment(skey, siv)
	if err != nil {
		return "", err
	}

	blockMode := cipher.NewCBCEncrypter(block, iv)

	data := PKCS5Padding([]byte(plainText), block.BlockSize())

	cryted := make([]byte, len(data))
	blockMode.CryptBlocks(cryted, data)
	return hex.EncodeToString(cryted), nil
}

// DesDecryption des-cbc decryption
// 使用 skey 和 siv 前 8 个字符
func DesDecryption(skey, siv, cipherText string) (string, error) {
	data, err := hex.DecodeString(cipherText)
	if err != nil {
		return "", err
	}
	iv, block, err := desPretreatment(skey, siv)
	if err != nil {
		return "", err
	}

	blockMode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(data))
	blockMode.CryptBlocks(plaintext, data)
	origData, err := PKCS5Unpadding(plaintext, block.BlockSize())
	if err != nil {
		return "", err
	}
	return string(origData), nil
}

func desPretreatment(skey, siv string) ([]byte, cipher.Block, error) {
	key, iv := []byte(skey), []byte(siv)
	if len(key) < 8 {
		return nil, nil, des.KeySizeError(len(key))
	}
	if len(iv) < 8 {
		return nil, nil, des.KeySizeError(len(iv))
	}
	key, iv = key[:8], iv[:8]
	block, err := des.NewCipher(key)
	return iv, block, err
}

// PKCS5Padding PKCS5 padding
func PKCS5Padding(src []byte, blockSize int) []byte {
	srcLen := len(src)
	padLen := blockSize - (srcLen % blockSize)
	padText := bytes.Repeat([]byte{byte(padLen)}, padLen)
	return append(src, padText...)
}

// PKCS5Unpadding PKCS5 unpadding
func PKCS5Unpadding(src []byte, blockSize int) ([]byte, error) {
	srcLen := len(src)
	paddingLen := int(src[srcLen-1])
	if paddingLen > blockSize || paddingLen >= srcLen || paddingLen <= 0 {
		return nil, ErrPaddingSize
	}
	for i := srcLen - paddingLen; i < srcLen; i++ {
		if int(src[i]) != paddingLen {
			return nil, ErrPaddingSize
		}
	}
	return src[:srcLen-paddingLen], nil
}

func aesEncryptPad(origin string, standard int) []byte {
	lenOrigin := len(origin)

	if lenOrigin < standard {
		// 未达到特定长度则补 \x00
		padLen := standard - lenOrigin%standard
		origin = origin + strings.Repeat("\x00", padLen)
	} else if lenOrigin > standard {
		// 超长则截断到特定长度
		origin = origin[:standard]
	}
	return []byte(origin)
}

func aesPretreatment(skey, siv string, aesType int) ([]byte, cipher.Block) {
	if aesType != AESType128 && aesType != AESType192 && aesType != AESType256 {
		panic("invalid aesType")
	}
	key := aesEncryptPad(skey, aesType)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	return aesEncryptPad(siv, aes.BlockSize), block
}

// Encrypt 加密
// NOTICE: 当前固定使用 AES-256-CBC 模式
func Encrypt(skey, siv, plainText string) string {
	iv, block := aesPretreatment(skey, siv, AESType256)

	blockMode := cipher.NewCBCEncrypter(block, iv)
	data := PKCS5Padding([]byte(plainText), block.BlockSize())
	crypted := make([]byte, len(data))
	blockMode.CryptBlocks(crypted, data)

	return base64.StdEncoding.EncodeToString(crypted)
}

// Decrypt 解密
// NOTICE: 当前固定使用 AES-256-CBC 模式
func Decrypt(skey, siv, crypted string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(crypted)
	if err != nil {
		return "", err
	}

	iv, block := aesPretreatment(skey, siv, AESType256)
	blockSize := block.BlockSize()
	dataLen := len(data)
	if dataLen%blockSize != 0 {
		return "", errors.New("ciphertext is not a multiple of the block size")
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, dataLen)
	blockMode.CryptBlocks(plaintext, data)
	plaintext, err = PKCS5Unpadding(plaintext, blockSize)
	if err != nil {
		return "", err
	}
	return string(plaintext), nil
}

// CRC32 returns the CRC-32 checksum of data using the IEEE polynomial
func CRC32(s string) int64 {
	return int64(crc32.ChecksumIEEE([]byte(s)))
}
