package retry

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/netutil"
)

// ErrNoRetry 表示不应该重试的错误
var ErrNoRetry = errors.New("no retry")

// IsRetryable 判断错误是否可重试的函数类型
type IsRetryable func(error) bool

// Callback 失败后的回调函数类型，attempt 为当前尝试次数（从 0 开始），err 为这次尝试的错误
type Callback func(attempt int, err error)

// Option 定义重试选项的函数类型，用于配置 Options
type Option func(*Options)

// Options 重试操作的配置选项
type Options struct {
	// MaxAttempts 最大尝试次数
	MaxAttempts int
	// Backoff 退避策略，用于计算重试间隔
	Backoff netutil.Backoff
	// RetryableCheck 用于判断错误是否可重试的函数
	RetryableCheck IsRetryable
	// FailedCallback 每次失败后的回调函数
	FailedCallback Callback
	// Operation 操作名称，用于指标统计
	Operation string
}

// WithMaxAttempts 设置最大尝试次数的选项
func WithMaxAttempts(maxAttempts int) Option {
	return func(o *Options) {
		o.MaxAttempts = maxAttempts
	}
}

// WithBackoff 设置退避策略的选项
func WithBackoff(backoff netutil.Backoff) Option {
	return func(o *Options) {
		o.Backoff = backoff
	}
}

// WithRetryableCheck 设置错误检查函数的选项，用于判断错误是否可重试
func WithRetryableCheck(fn IsRetryable) Option {
	return func(o *Options) {
		o.RetryableCheck = fn
	}
}

// WithFailedCallback 设置每次失败后的回调函数的选项
func WithFailedCallback(fn Callback) Option {
	return func(o *Options) {
		o.FailedCallback = fn
	}
}

// WithOperation 设置操作名称，用于指标统计
func WithOperation(operation string) Option {
	return func(o *Options) {
		o.Operation = operation
	}
}

// getCallerFunctionName 获取调用者的函数名
// skip 表示调用栈向上跳过的层数
func getCallerFunctionName(skip int) string {
	pc, _, _, ok := runtime.Caller(skip)
	if !ok {
		return "unknown"
	}

	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return "unknown"
	}

	// 完整函数名，如 github.com/MiaoSiLa/missevan-go/controllers/handler.someFunc
	fullName := fn.Name()

	// 提取短函数名，例如 handler.someFunc
	parts := strings.Split(fullName, "/")
	shortName := parts[len(parts)-1]

	return shortName
}

// doAttempt 执行重试逻辑的通用函数
// ctx: 上下文，用于控制取消
// withContext: 是否使用上下文
// fn: 无上下文的执行函数，当 withContext 为 false 时使用
// fnWithCtx: 带上下文的执行函数，当 withContext 为 true 时使用
func (o *Options) doAttempt(ctx context.Context, withContext bool, fn func() error, fnWithCtx func(context.Context) error) error {
	var err error
	startTime := util.TimeNow()

	for i := 0; i < o.MaxAttempts; i++ {
		// 检查上下文是否已取消
		if ctx.Err() != nil {
			// 统计取消
			failureCounter.WithLabelValues(o.Operation).Inc()
			durationHistogram.WithLabelValues(o.Operation, "canceled").Observe(util.TimeNow().Sub(startTime).Seconds())
			return ctx.Err()
		}

		// 统计尝试次数
		retryCounter.WithLabelValues(o.Operation, strconv.Itoa(i)).Inc()

		// 根据是否有上下文执行相应的函数
		if withContext {
			err = fnWithCtx(ctx)
		} else {
			err = fn()
		}

		if err == nil {
			// 统计成功
			successCounter.WithLabelValues(o.Operation).Inc()
			durationHistogram.WithLabelValues(o.Operation, "success").Observe(util.TimeNow().Sub(startTime).Seconds())
			return nil
		}

		// 检查错误是否可重试
		retryable := !errors.Is(err, ErrNoRetry) && (o.RetryableCheck == nil || o.RetryableCheck(err))
		if !retryable {
			// 统计不可重试的失败
			failureCounter.WithLabelValues(o.Operation).Inc()
			durationHistogram.WithLabelValues(o.Operation, "not_retryable").Observe(util.TimeNow().Sub(startTime).Seconds())
			return err
		}

		// 记录日志并执行回调
		if i == 0 {
			logger.Warnf("execute func %s error, will retry, error: %v", o.Operation, err)
		} else {
			logger.Warnf("execute func %s error, retry count: %d, error: %v", o.Operation, i, err)
		}

		if o.FailedCallback != nil {
			o.FailedCallback(i, err)
		}

		// 最后一次尝试后不需要等待
		if i < o.MaxAttempts-1 {
			select {
			case <-ctx.Done():
				// 统计取消
				failureCounter.WithLabelValues(o.Operation).Inc()
				durationHistogram.WithLabelValues(o.Operation, "canceled").Observe(util.TimeNow().Sub(startTime).Seconds())
				return ctx.Err()
			case <-time.After(o.Backoff.Backoff(i)):
			}
		}
	}

	// 统计失败
	failureCounter.WithLabelValues(o.Operation).Inc()
	durationHistogram.WithLabelValues(o.Operation, "failure").Observe(util.TimeNow().Sub(startTime).Seconds())

	logger.Errorf("execute func %s failed after %d retries, error: %v", o.Operation, o.MaxAttempts-1, err)
	return fmt.Errorf("operation %s failed after %d retries: %w", o.Operation, o.MaxAttempts-1, err)
}

// Do 执行带重试逻辑的函数
// fn: 需要执行的函数
// 返回: 如果执行成功返回 nil，否则返回最后一次执行的错误
func (o *Options) Do(fn func() error) error {
	return o.doAttempt(context.Background(), false, fn, nil)
}

// DoWithContext 执行带上下文和重试逻辑的函数
// ctx: 上下文，用于控制取消
// fn: 需要执行的函数，接收上下文参数
// 返回: 如果执行成功返回 nil，上下文取消返回上下文错误，否则返回最后一次执行的错误
func (o *Options) DoWithContext(ctx context.Context, fn func(ctx context.Context) error) error {
	return o.doAttempt(ctx, true, nil, fn)
}

// newDefaultAttemptOptions 创建默认的重试选项
// 默认最大尝试 3 次，指数退避策略，基础延迟 100ms，因子 2，抖动 0.1，最大延迟 1s
func newDefaultAttemptOptions() *Options {
	return &Options{
		MaxAttempts: 3,
		Backoff: &netutil.BackoffConfig{
			BaseDelay: 100 * time.Millisecond,
			Factor:    2,
			Jitter:    0.1,
			MaxDelay:  time.Second,
		},
		RetryableCheck: nil,
		FailedCallback: nil,
		// 获取调用方函数名作为默认操作名称，跳过 3 层调用栈
		// 调用栈: Do/DoWithContext -> newDefaultAttemptOptions -> getCallerFunctionName
		Operation: getCallerFunctionName(3),
	}
}

// Do 使用默认或自定义选项执行带重试逻辑的函数
// fn: 需要执行的函数
// opts: 自定义重试选项，可选
// 返回: 如果执行成功返回 nil，否则返回最后一次执行的错误
func Do(fn func() error, opts ...Option) error {
	options := newDefaultAttemptOptions()
	for _, opt := range opts {
		opt(options)
	}
	return options.Do(fn)
}

// DoWithContext 使用默认或自定义选项执行带上下文和重试逻辑的函数
// ctx: 上下文，用于控制取消
// fn: 需要执行的函数，接收上下文参数
// opts: 自定义重试选项，可选
// 返回: 如果执行成功返回 nil，上下文取消返回上下文错误，否则返回最后一次执行的错误
func DoWithContext(ctx context.Context, fn func(ctx context.Context) error, opts ...Option) error {
	options := newDefaultAttemptOptions()
	for _, opt := range opts {
		opt(options)
	}
	return options.DoWithContext(ctx, fn)
}
