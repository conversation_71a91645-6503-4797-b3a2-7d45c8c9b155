package retry

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	// 重试计数器，记录重试次数
	retryCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "retry",
			Name:      "attempt_counter",
			Help:      "重试次数统计",
		}, []string{"operation", "attempt"})

	// 重试成功计数器，记录最终成功的操作
	successCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "retry",
			Name:      "success_counter",
			Help:      "重试成功次数统计",
		}, []string{"operation"})

	// 重试失败计数器，记录最终失败的操作
	failureCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "retry",
			Name:      "failure_counter",
			Help:      "重试失败次数统计",
		}, []string{"operation"})

	// 操作耗时直方图，记录操作耗时分布
	durationHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "retry",
			Name:      "duration_seconds",
			Help:      "重试操作耗时（秒）",
			Buckets:   []float64{0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10},
		}, []string{"operation", "result"})
)

// InitMetrics 初始化 metrics
func InitMetrics(promRegistry *prometheus.Registry) {
	promRegistry.MustRegister(retryCounter)
	promRegistry.MustRegister(successCounter)
	promRegistry.MustRegister(failureCounter)
	promRegistry.MustRegister(durationHistogram)
}
