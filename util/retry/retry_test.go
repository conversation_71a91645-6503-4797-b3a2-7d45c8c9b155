package retry

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

// 测试用错误
var (
	errTest         = errors.New("test error")
	errNotRetryable = errors.New("not retryable error")
)

// 创建一个测试用的 Registry
func newTestRegistry() *prometheus.Registry {
	registry := prometheus.NewRegistry()
	InitMetrics(registry)
	return registry
}

// 获取指标值的辅助函数
func getCounterValue(reg *prometheus.Registry, name, operation string, labels ...string) (float64, error) {
	metricFamilies, err := reg.Gather()
	if err != nil {
		return 0, fmt.Errorf("gather metrics: %w", err)
	}

	for _, family := range metricFamilies {
		if family.GetName() == name {
			for _, metric := range family.GetMetric() {
				matches := true
				labelMap := make(map[string]string)

				// 先获取所有标签对
				for _, label := range metric.GetLabel() {
					labelMap[label.GetName()] = label.GetValue()
				}

				// 检查 operation 标签
				if operation != "" && labelMap["operation"] != operation {
					matches = false
				}

				// 检查其他标签
				for i := 0; i < len(labels); i += 2 {
					if i+1 < len(labels) {
						key := labels[i]
						val := labels[i+1]
						if labelMap[key] != val {
							matches = false
							break
						}
					}
				}

				if matches {
					if metric.Counter != nil {
						return metric.Counter.GetValue(), nil
					}
					if metric.Histogram != nil {
						return float64(metric.Histogram.GetSampleCount()), nil
					}
				}
			}
		}
	}

	return 0, fmt.Errorf("metric %s with labels %v not found", name, labels)
}

// 测试基本重试功能
func TestDo(t *testing.T) {
	t.Run("成功场景_第二次重试成功", func(t *testing.T) {
		registry := newTestRegistry()

		var count int
		err := Do(func() error {
			if count < 1 {
				count++
				return errTest
			}
			return nil
		}, WithOperation("test_success"))

		assert.NoError(t, err, "应该没有错误返回")
		assert.Equal(t, 1, count, "应该只重试一次")

		// 校验指标
		value, err := getCounterValue(registry, "retry_attempt_counter", "test_success", "attempt", "0")
		require.NoError(t, err, "获取指标不应该失败")
		assert.Equal(t, float64(1), value, "尝试计数器应该为 1")
	})

	t.Run("失败场景_超过重试次数", func(t *testing.T) {
		var count int
		err := Do(func() error {
			count++
			return errTest
		}, WithOperation("test_failure"), WithMaxAttempts(2))

		assert.Error(t, err, "应该返回错误")
		assert.Equal(t, 2, count, "应该尝试两次")
		assert.Contains(t, err.Error(), "operation test_failure failed after 1 retries", "错误消息应该包含重试次数")
	})
}

// 测试带上下文的重试功能
func TestDoWithContext(t *testing.T) {
	t.Run("成功场景", func(t *testing.T) {
		var count int
		ctx := context.Background()
		err := DoWithContext(ctx, func(ctx context.Context) error {
			if count < 1 {
				count++
				return errTest
			}
			return nil
		}, WithOperation("ctx_success"))

		assert.NoError(t, err, "应该没有错误返回")
		assert.Equal(t, 1, count, "应该只重试一次")
	})

	t.Run("上下文取消", func(t *testing.T) {
		registry := newTestRegistry()

		var count int
		ctx, cancel := context.WithCancel(context.Background())

		go func() {
			time.Sleep(50 * time.Millisecond)
			cancel()
		}()

		err := DoWithContext(ctx, func(ctx context.Context) error {
			count++
			time.Sleep(100 * time.Millisecond)
			return errTest
		}, WithOperation("ctx_cancel"), WithMaxAttempts(5))

		assert.ErrorIs(t, err, context.Canceled, "应该返回上下文取消错误")
		assert.LessOrEqual(t, count, 2, "计数应该最多为 2")

		// 验证取消指标
		value, err := getCounterValue(registry, "retry_failure_counter", "ctx_cancel")
		require.NoError(t, err, "获取指标不应该失败")
		assert.Greater(t, value, float64(0), "失败计数器应该大于 0")
	})
}

// 测试可重试条件
func TestRetryableCheck(t *testing.T) {
	// 自定义可重试检查函数
	isRetryable := func(err error) bool {
		return err != errNotRetryable
	}

	t.Run("可重试错误", func(t *testing.T) {
		var count int
		err := Do(func() error {
			if count < 1 {
				count++
				return errTest // 可重试错误
			}
			return nil
		}, WithRetryableCheck(isRetryable))

		assert.NoError(t, err, "应该没有错误返回")
		assert.Equal(t, 1, count, "应该只重试一次")
	})

	t.Run("不可重试错误", func(t *testing.T) {
		var count1 int
		err := Do(func() error {
			count1++
			return errNotRetryable // 不可重试错误
		}, WithRetryableCheck(isRetryable), WithMaxAttempts(5))

		assert.ErrorIs(t, err, errNotRetryable, "应该返回不可重试错误")
		assert.Equal(t, 1, count1, "应该只尝试一次")

		var count2 int
		err = Do(func() error {
			count2++
			return ErrNoRetry
		}, WithRetryableCheck(isRetryable), WithMaxAttempts(5))

		assert.ErrorIs(t, err, ErrNoRetry, "应该返回不应该重试错误")
		assert.Equal(t, 1, count2, "应该只尝试一次")
	})
}

// 测试回调函数
func TestFailedCallback(t *testing.T) {
	t.Run("回调功能测试", func(t *testing.T) {
		var callbackCount int
		var lastAttempt int
		var lastError error

		callback := func(attempt int, err error) {
			callbackCount++
			lastAttempt = attempt
			lastError = err
		}

		var executionCount int
		err := Do(func() error {
			if executionCount < 2 {
				executionCount++
				return errTest
			}
			return nil
		}, WithFailedCallback(callback))

		assert.NoError(t, err, "应该没有错误返回")
		assert.Equal(t, 2, callbackCount, "回调应该被调用两次")
		assert.Equal(t, 1, lastAttempt, "最后一次尝试应该是 1")
		assert.Equal(t, errTest, lastError, "最后一次错误应该是测试错误")
	})
}

// 测试指标收集
func TestMetrics(t *testing.T) {
	t.Run("指标收集", func(t *testing.T) {
		registry := newTestRegistry()

		// 执行重试操作
		_ = Do(func() error {
			return errTest
		}, WithOperation("metrics_test"), WithMaxAttempts(1))

		// 校验尝试次数指标
		value, err := getCounterValue(registry, "retry_attempt_counter", "metrics_test", "attempt", "0")
		require.NoError(t, err, "获取指标不应该失败")
		assert.Equal(t, float64(1), value, "尝试计数器应该为 1")

		// 校验失败指标
		value, err = getCounterValue(registry, "retry_failure_counter", "metrics_test")
		require.NoError(t, err, "获取指标不应该失败")
		assert.Equal(t, float64(1), value, "失败计数器应该为 1")

		// 校验存在耗时直方图
		value, err = getCounterValue(registry, "retry_duration_seconds", "metrics_test", "result", "failure")
		require.NoError(t, err, "获取指标不应该失败")
		assert.Greater(t, value, float64(0), "耗时直方图应该有样本")
	})
}

// 测试获取调用者函数名
func TestGetCallerFunctionName(t *testing.T) {
	t.Run("直接获取调用者", func(t *testing.T) {
		callerName := getCallerFunctionName(0)
		expectedName := "retry.getCallerFunctionName" // 子测试函数名

		assert.Equal(t, expectedName, callerName, "调用者名称应该匹配")
	})

	t.Run("通过包装函数", func(t *testing.T) {
		wrapper := func() string {
			return getCallerFunctionName(1)
		}

		callerName := wrapper()
		expectedName := "retry.TestGetCallerFunctionName.func2.1" // 子测试函数名

		assert.Equal(t, expectedName, callerName, "调用者名称应该匹配")
	})

	t.Run("默认重试选项", func(t *testing.T) {
		testFunction := func() *Options {
			return newDefaultAttemptOptions()
		}

		options := testFunction()
		expectedName := "retry.TestGetCallerFunctionName.func3" // 嵌套函数名

		assert.Equal(t, expectedName, options.Operation, "操作名称应该匹配")
	})
}

// 测试自定义退避策略
func TestCustomBackoff(t *testing.T) {
	t.Run("固定延迟退避", func(t *testing.T) {
		// 自定义固定延迟的退避策略
		fixedDelay := 10 * time.Millisecond
		customBackoff := &FixedBackoff{Delay: fixedDelay}

		var count int
		start := util.TimeNow()

		err := Do(func() error {
			if count < 1 {
				count++
				return errTest
			}
			return nil
		}, WithBackoff(customBackoff))

		duration := util.TimeNow().Sub(start)

		assert.NoError(t, err, "应该没有错误返回")
		assert.GreaterOrEqual(t, duration, fixedDelay, "持续时间应该至少是固定延迟")
	})
}

// 用于测试的固定时间退避策略
type FixedBackoff struct {
	Delay time.Duration
}

func (b *FixedBackoff) Backoff(_ int) time.Duration {
	return b.Delay
}

// 测试处理边界情况
func TestEdgeCases(t *testing.T) {
	t.Run("零次重试", func(t *testing.T) {
		err := Do(func() error {
			return errTest
		}, WithMaxAttempts(0))

		assert.Error(t, err, "零次重试应该返回错误")
	})

	t.Run("大量重试", func(t *testing.T) {
		var count int
		start := util.TimeNow()

		// 使用非常短的退避时间，避免测试时间过长
		err := Do(func() error {
			count++
			return errTest
		}, WithMaxAttempts(20), WithBackoff(&FixedBackoff{Delay: time.Microsecond}))

		duration := util.TimeNow().Sub(start)

		assert.Error(t, err, "应该返回错误")
		assert.Equal(t, 20, count, "应该重试 20 次")
		t.Logf("20 次重试耗时: %v", duration)
	})
}
