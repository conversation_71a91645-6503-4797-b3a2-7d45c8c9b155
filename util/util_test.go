package util

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHasElem(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	assert.True(<PERSON><PERSON><PERSON>([]string{"foo", "bar"}, "bar"))
	assert.<PERSON><PERSON><PERSON>(<PERSON><PERSON><PERSON>([]string{"foo", "bar"}, "baz"))

	assert.<PERSON>(<PERSON><PERSON><PERSON>([]int{114, 514}, 114))
	assert.<PERSON><PERSON><PERSON>(<PERSON><PERSON><PERSON>([]int{114, 514}, 233))

	assert.True(<PERSON><PERSON><PERSON>([2]int{114, 514}, 114))
	assert.False(<PERSON><PERSON><PERSON>([2]int{114, 514}, 233))
}

func TestFilterSpecialCodes(t *testing.T) {
	assert.Equal(t, "_(:з)∠)_", FilterSpecialCodes("_(:з)∠)_\x00\n\r", true))
}

func TestGroupBy(t *testing.T) {
	assert := assert.New(t)
	records := []Dict{
		{"id": "1", "value": 1},
		{"id": "1", "value": 2},
		{"id": "2", "value": 3},
	}
	m := GroupBy(records, "id")
	assert.Equal(1, m["1"][0]["value"])
	assert.Equal(2, m["1"][1]["value"])
	assert.Equal(3, m["2"][0]["value"])
}

func TestAppendStringOnce(t *testing.T) {
	assert := assert.New(t)
	a := []string{"1", "2", "3"}
	b := AppendStringOnce(a, "3", "4")
	assert.Equal(append(a, "4"), b)
}

func TestIgnoreUntil(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	str := "abc一二\n三def\nExpect"
	assert.Equal("Expect", IgnoreUntil(str, 2, '\n'))
	assert.Equal(str, IgnoreUntil(str, 3, '\n'))
}

func TestMosaic(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("张*", MosaicString("张三", MosaicRealName))
	assert.Equal("133******39", MosaicString("13333333339", MosaicPhoneNumber))
	assert.Equal("+86133******39", MosaicString("+*************", MosaicPhoneNumber))
	assert.Equal("12345", MosaicString("12345", MosaicPhoneNumber))
	assert.Equal("tes****@163.com", MosaicString("<EMAIL>", MosaicEmail))
	assert.Equal("1******@gmail.com", MosaicString("<EMAIL>", MosaicEmail))
	assert.Equal("nih****@163.com", MosaicString("<EMAIL>", MosaicEmail))
	assert.Equal("30***************3", MosaicString("301012199510162233", MosaicIDNumber))
	assert.Equal("62*************34", MosaicString("*****************", MosaicBankCardNumber))
}

func TestJoinIntArray(t *testing.T) {
	assert := assert.New(t)

	arr := []int{
		2,
		0,
		1,
		9,
		0,
		1,
		0,
		1,
	}

	result := JoinIntArray(arr, "~")
	assert.Equal("2~0~1~9~0~1~0~1", result)
}

func TestJoinInt64Array(t *testing.T) {
	assert := assert.New(t)

	arr := []int64{
		123,
		456,
		789,
	}

	result := JoinInt64Array(arr, "|")
	assert.Equal("123|456|789", result)
}

func TestNextDayTime(t *testing.T) {
	assert := assert.New(t)

	now := time.Date(2006, 2, 31, 15, 30, 25, 0, time.Local)
	next := NextDayTime(now)
	y, m, d := next.Date()
	assert.Equal(2006, y)
	assert.Equal(time.March, m)
	assert.Equal(4, d)

	assert.Zero(next.Hour())
	assert.Zero(next.Minute())
	assert.Zero(next.Second())
	assert.Zero(next.Nanosecond())
}

func TestUniq(t *testing.T) {
	assert := assert.New(t)
	arr := []int64{1, 1}
	assert.Equal([]int64{1}, Uniq(arr))
}

func TestGetRandomString(t *testing.T) {
	assert := assert.New(t)

	randStr := GetRandomString(8)
	assert.Equal(8, len(randStr))

	for i := 0; i < len(randStr); i++ {
		assert.Contains(letters, string(randStr[i]))
	}
}

func TestRandomCode(t *testing.T) {
	assert := assert.New(t)
	assert.Len(RandomCode(5), 5)
	assert.Len(RandomCode(6), 6)
}

func TestBeginningOfMonth(t *testing.T) {
	assert := assert.New(t)

	t1 := time.Date(2006, 2, 31, 3, 4, 5, 6, time.Local)
	t2 := time.Date(2006, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(t2, BeginningOfMonth(t1))
}

func TestEndOfMonth(t *testing.T) {
	assert := assert.New(t)

	t1 := time.Date(2006, 1, 31, 3, 4, 5, 6, time.Local)
	t2 := time.Date(2006, 2, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(t2, EndOfMonth(t1))
}

func TestToMap(t *testing.T) {
	assert := assert.New(t)

	assert.PanicsWithValue("input must be a slice or array", func() { ToMap(1, "a") })
	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 2)
	for i := 0; i < 2; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	assert.Nil(ToMap(inputs, "empty"))
	result, ok := ToMap(inputs, "ID").(map[int]*foo)
	assert.True(ok)

	for _, elem := range inputs {
		v, ok := result[elem.ID]
		assert.True(ok)
		assert.Equal(elem, v)
	}

	in := [2]*foo{{0, "A"}, {1, "A"}}
	_, ok = ToMap(in, "ID").(map[int]*foo)
	assert.True(ok)
}

func TestToMapWithSize(t *testing.T) {
	assert := assert.New(t)

	assert.PanicsWithValue("input must be a slice or array", func() { ToMapWithSize(1, "a", 1) })
	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 2)
	for i := 0; i < 2; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	assert.Nil(ToMapWithSize(inputs, "empty", 2))
	result, ok := ToMapWithSize(inputs, "ID", 2).(map[int]*foo)
	assert.True(ok)

	for _, elem := range inputs {
		v, ok := result[elem.ID]
		assert.True(ok)
		assert.Equal(elem, v)
	}

	in := [2]*foo{{0, "A"}, {1, "A"}}
	_, ok = ToMapWithSize(in, "ID", 2).(map[int]*foo)
	assert.True(ok)
}

func BenchmarkToMapWithSize(b *testing.B) {
	/*
		goos: windows
		goarch: amd64
		pkg: github.com/MiaoSiLa/live-service/util
		BenchmarkToMap-8   	   52993	     19912 ns/op	    3735 B/op	     105 allocs/op
		PASS
		ok  	github.com/MiaoSiLa/live-service/util	1.374s
		Success: Benchmarks passed.
	*/
	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 100)
	for i := 0; i < 100; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ToMapWithSize(inputs, "ID", 100)
	}
}

func TestBoolToInt(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, BoolToInt(false))
	assert.Equal(1, BoolToInt(true))
}

func TestIntToBool(t *testing.T) {
	assert := assert.New(t)

	assert.True(IntToBool(1))
	assert.True(IntToBool(-1))
	assert.True(IntToBool(-2))
	assert.False(IntToBool(0))
}

func TestNewBool(t *testing.T) {
	assert := assert.New(t)

	b := NewBool(true)
	assert.Equal(true, *b)

	b = NewBool(false)
	assert.Equal(false, *b)
}

func TestNewInt(t *testing.T) {
	assert := assert.New(t)

	a := 10
	b := NewInt(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(10, a)
}

func TestNewInt64(t *testing.T) {
	assert := assert.New(t)

	a := int64(10)
	b := NewInt64(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(int64(10), a)
}

func TestNewFloat64(t *testing.T) {
	assert := assert.New(t)

	a := 90.3
	b := NewFloat64(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(90.3, a)
}

func TestNewString(t *testing.T) {
	assert := assert.New(t)

	a := "a"
	b := NewString(a)

	assert.Equal(a, *b)

	*b = "b"
	assert.Equal("a", a)
}

func TestFloat2DP(t *testing.T) {
	assert := assert.New(t)

	a := Float2DP(0.167)
	b, _ := json.Marshal(a)
	assert.Equal(b, []byte(`0.17`))

	aa := a
	err := json.Unmarshal([]byte(`null`), &a)
	assert.NoError(err)
	assert.Equal(aa, a)

	err = json.Unmarshal([]byte(`1`), &a)
	assert.NoError(err)
	assert.EqualValues(1, a)

	err = json.Unmarshal([]byte(`"1"`), &a)
	assert.NoError(err)
	assert.EqualValues(1, a)

	assert.Error(json.Unmarshal([]byte(`"test"`), &a))
	assert.Error(json.Unmarshal([]byte(`true`), &a))
}

func TestIncludes(t *testing.T) {
	assert := assert.New(t)

	list := []int64{1, 2, 3, 4, 5, 6}
	assert.True(Includes(len(list), func(i int) bool {
		return list[i] == 2
	}))

	assert.False(Includes(len(list), func(i int) bool {
		return list[i] == 7
	}))

	list = []int64{}
	assert.False(Includes(len(list), func(i int) bool {
		return list[i] == 2
	}))
}

func TestFindIndex(t *testing.T) {
	assert := assert.New(t)

	list := []int64{1, 2, 3, 4, 5, 6, 1, 2}
	assert.Equal(1, FindIndex(len(list), func(i int) bool {
		return list[i] == 2
	}), "测试总是返回第一个符合的元素")

	assert.Equal(-1, FindIndex(len(list), func(i int) bool {
		return list[i] == 7
	}), "测试找不到返回 -1")

	assert.Equal(-1, FindIndex(3, func(i int) bool {
		return list[i] == 6
	}), "测试查询不会超过 length")

	list = []int64{}
	assert.Equal(-1, FindIndex(len(list), func(i int) bool {
		return list[i] == 2
	}))
}

func TestIsLeapYear(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsLeapYear(2000))
	assert.False(IsLeapYear(1900))
	assert.True(IsLeapYear(2024))
	assert.False(IsLeapYear(2025))
}

func TestTruncateString(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("12345", TruncateString("12345", 5, "..."))
	assert.Equal("12345", TruncateString("12345", 10, "..."))
	assert.Equal("123 (truncated...)", TruncateString("12345", 3, " (truncated...)"))
	assert.Equal("♣♧⓪①一二㋀㋁¹⁴₁₃﹢﹣×÷，。°′″%℃↑↓ぁあ (truncated...)", TruncateString("♣♧⓪①一二㋀㋁¹⁴₁₃﹢﹣×÷，。°′″%℃↑↓ぁあa", 27, " (truncated...)"))
	assert.Equal("<html><div></div></h (truncated...)", TruncateString("<html><div></div></html>", 20, " (truncated...)"))

	// 两个 unicode 字符的 emoji 表情会有问题
	assert.Equal("😀🚜🇵...", TruncateString("😀🚜🇵🇸", 3, "..."))
}

func TestTruncateResponseBodyWithNotice(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("<html><div></div></html>", TruncateResponseBodyWithNotice("<html><div></div></html>"))
	assert.Equal(`
	<html>
		<div>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
		</div>
		<div>
			<p>12345</p (truncated...)`,
		TruncateResponseBodyWithNotice(`
	<html>
		<div>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
		</div>
		<div>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
			<p>12345</p>
		</div>
	</html>
	`))
}

func TestSliceMap(t *testing.T) {
	assert := assert.New(t)

	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 2)
	for i := 0; i < 2; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	result := SliceMap(inputs, func(f *foo) int { return f.ID })
	assert.Equal([]int{0, 1}, result)
}

func TestChunkSlice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	slice := make([]int, 1001)
	for i := 0; i < 1001; i++ {
		slice[i] = i + 1
	}

	chunks := ChunkSlice(slice)
	// 默认大小 500
	require.Equal(3, len(chunks))
	assert.Equal(500, len(chunks[0]))
	assert.Equal(500, len(chunks[1]))
	assert.Equal(1, len(chunks[2]))
	assert.Equal(1, chunks[0][0])
	assert.Equal(500, chunks[0][499])
	assert.Equal(501, chunks[1][0])
	assert.Equal(1000, chunks[1][499])
	assert.Equal(1001, chunks[2][0])

	// 自定义大小 300
	customChunks := ChunkSlice(slice, 300)
	require.Equal(4, len(customChunks))
	assert.Equal(300, len(customChunks[0]))
	assert.Equal(300, len(customChunks[1]))
	assert.Equal(300, len(customChunks[2]))
	assert.Equal(101, len(customChunks[3]))
}
