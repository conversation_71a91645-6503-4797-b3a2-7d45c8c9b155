package util

import (
	"crypto/des"
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMD5(t *testing.T) {
	res := MD5("a")
	assert.Equal(t, "0cc175b9c0f1b6a831c399e269772661", res)
}

func TestBase64(t *testing.T) {
	assert.Equal(t, "YQ==", Base64("a"))
}

func TestHmacSha1(t *testing.T) {
	res := HmacSha1("secret_key", "a")
	assert.Equal(t, "e1235c6e4fb1e406fc75ae717afbd1d5d380ca3f", res)
}

func TestHmacSha256(t *testing.T) {
	res := HmacSha256("secret_key", "a")
	assert.Equal(t,
		"c6412067d8c5805a6f7e9e8a14677cb465c3664b92d0518c02ccafc8fe988254", res)
}

func TestRetrunErrors(t *testing.T) {
	assert := assert.New(t)
	// test desPretreatmemt error
	_, err := DesEncryption("12", "12345678", "test")
	assert.Equal(des.KeySizeError(2), err)
	_, err = DesDecryption("12345678", "12", hex.EncodeToString([]byte("test")))
	assert.Equal(des.KeySizeError(2), err)

	// test hex.DecodeString error
	_, err = DesDecryption("12345678", "12345678", "test")
	_, ok := err.(hex.InvalidByteError)
	assert.True(ok)
}

func TestEncryptAndDecrypt(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 加密
	crypted := Encrypt("1234567", "123", "this 1 机密")
	assert.Equal("M5gmNgLh1wOWXvZywpbseg==", crypted)

	// 解密
	data, err := Decrypt("1234567", "123", crypted)
	require.NoError(err)
	assert.Equal("this 1 机密", data)

	// 测试密文长度不是块大小倍数的情况
	_, err = Decrypt("1234567", "123", "MhcLMHmQBN9TiP8eFii4P6Kj")
	assert.EqualError(err, "ciphertext is not a multiple of the block size")
}

func TestAESEncryptPad(t *testing.T) {
	assert := assert.New(t)
	assert.Equal([]byte{97, 98, 99, 0, 0, 0}, aesEncryptPad("abc", 6))
	assert.Equal([]byte("abc123"), aesEncryptPad("abc123", 6))
	assert.Equal([]byte("abc1"), aesEncryptPad("abc123", 4))
}

func TestCRC32(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(2346098258), CRC32("Hello world"))
}
