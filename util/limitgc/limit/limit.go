//go:build go1.19
// +build go1.19

package limit

import (
	"errors"
	"fmt"
	"math"
	"os"
	"runtime/debug"
)

const (
	_envGoMemLimit  = "GOMEMLIMIT"
	_defaultPercent = 0.7
)

var errNoCgroup = errors.New("not in cgroup")
var errNotSupport = errors.New("cgroups is not supported")
var errNoLimit = errors.New("memory is not limited")

// Option is the interface to set limit options.
type Option interface {
	apply(*config)
}

type optionFunc func(*config)

// MaxMemoryPercent sets the max memory percent to limit.
func MaxMemoryPercent(n float64) Option {
	if n <= 0 || n >= 1 {
		panic("invalid memory percent")
	}
	return optionFunc(func(cfg *config) {
		cfg.maxMemoryPercent = n
	})
}

// DisableAutoGC disables the automatic GC.
func DisableAutoGC() Option {
	return optionFunc(func(cfg *config) {
		cfg.gcPercent = new(int)
		*cfg.gcPercent = -1
	})
}

// SetGCPercent sets the GC percent.
func SetGCPercent(n int) Option {
	return optionFunc(func(cfg *config) {
		cfg.gcPercent = new(int)
		*cfg.gcPercent = n
	})
}

type config struct {
	maxMemoryPercent float64
	gcPercent        *int
}

func (of optionFunc) apply(cfg *config) { of(cfg) }

// Set sets the memory limit.
func Set(opts ...Option) {
	cfg := &config{
		maxMemoryPercent: _defaultPercent,
	}
	for _, o := range opts {
		o.apply(cfg)
	}
	envLimit := os.Getenv(_envGoMemLimit)
	if envLimit != "" {
		fmt.Printf("limitgc: env memory limit: %v\n", envLimit) //nolint:forbidigo
		return
	}
	cgroupLimit, err := fromCgroup()
	if err != nil {
		fmt.Printf("limitgc: %v\n", err) //nolint:forbidigo
		return
	}

	if cfg.gcPercent != nil {
		debug.SetGCPercent(*cfg.gcPercent)
	}

	limit := float2Int(float64(cgroupLimit) * cfg.maxMemoryPercent)
	fmt.Printf("limitgc: set memory limit: %v approach to: %s GB\n", limit, limitToGB(limit)) //nolint:forbidigo
	debug.SetMemoryLimit(limit)
}

func limitToGB(limit int64) string {
	return fmt.Sprintf("%.2f", float64(limit)/(1024*1024*1024))
}

func float2Int(f float64) int64 {
	if f > math.MaxInt64 {
		return math.MaxInt64
	}
	return int64(f)
}
