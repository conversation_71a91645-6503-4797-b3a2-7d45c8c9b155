package limit

import (
	"github.com/containerd/cgroups"
	v2 "github.com/containerd/cgroups/v2"
)

const (
	cgroupMountPoint = "/sys/fs/cgroup"
)

func fromCgroup() (uint64, error) {
	switch cgroups.Mode() {
	case cgroups.Legacy:
		return cgroupV1()
	case cgroups.Hybrid, cgroups.Unified:
		return cgroupV2()
	}
	return 0, errNoCgroup
}

func cgroupV1() (uint64, error) {
	cg, err := cgroups.Load(cgroups.SingleSubsystem(cgroups.V1, cgroups.Memory), cgroups.RootPath)
	if err != nil {
		return 0, err
	}

	metrics, err := cg.Stat(cgroups.IgnoreNotExist)
	if err != nil {
		return 0, err
	} else if metrics.Memory == nil {
		return 0, errNoLimit
	}

	return metrics.Memory.HierarchicalMemoryLimit, nil
}

func cgroupV2() (uint64, error) {
	path, err := v2.NestedGroupPath("")
	if err != nil {
		return 0, err
	}

	m, err := v2.<PERSON>ad<PERSON>anager(cgroupMountPoint, path)
	if err != nil {
		return 0, err
	}

	stats, err := m.Stat()
	if err != nil {
		return 0, err
	} else if stats.Memory == nil {
		return 0, errNoLimit
	}

	return stats.Memory.UsageLimit, nil
}
