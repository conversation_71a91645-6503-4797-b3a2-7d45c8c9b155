package util

import (
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSmartUserContext(t *testing.T) {
	assert := assert.New(t)

	smartC := SmartUserContext{
		UID:         10,
		UserToken:   "testtoken",
		UserEquipID: "test-equip-id",
		UserBUVID:   "test_buvid",
		IP:          "127.0.0.1",
		Req:         httptest.NewRequest("GET", "/health", nil),
		UA:          "live-service/0.0.1",
	}
	var c UserContext
	convertToUserContext := func() {
		c = interface{}(smartC).(UserContext)
	}
	assert.NotPanics(convertToUserContext)
	assert.Equal(int64(10), c.UserID())
	assert.Equal("testtoken", c.<PERSON>())
	assert.Equal("test-equip-id", c.EquipID())
	assert.Equal("test_buvid", c.BUVID())
	assert.Equal("127.0.0.1", c.<PERSON>lient<PERSON>())
	assert.Equal("/health", c.Request().URL.Path)
	assert.Equal("live-service/0.0.1", c.User<PERSON>gent())
}
