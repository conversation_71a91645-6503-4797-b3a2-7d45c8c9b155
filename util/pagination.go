package util

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/mongo/options"
	gormv2 "gorm.io/gorm"
)

const (
	// DefaultPageSize defines default size of per page
	DefaultPageSize = 20
)

// Pagination pagination，查询前要保证 Pagination.Valid() 返回 true
type Pagination struct {
	Count    int64 `json:"count"`
	MaxPage  int64 `json:"maxpage"`
	P        int64 `json:"p"`
	PageSize int64 `json:"pagesize"`
}

// MarkerPagination 无页数信息进行分页，maker 内容由各业务自行定义
type MarkerPagination struct {
	HasMore bool   `json:"has_more"`
	Marker  string `json:"marker"`
}

// Valid 指定页数 pa.P 是否存在
func (pa Pagination) Valid() bool {
	return 1 <= pa.P && pa.P <= pa.MaxPage
}

// Offset 偏移量
// NOTICE: 确保 pa 有效
func (pa Pagination) Offset() int64 {
	return pa.PageSize * (pa.P - 1)
}

// Limit 查询数目限制
// NOTICE: 确保 pa 有效
func (pa Pagination) Limit() int64 {
	remain := pa.Count - pa.Offset()
	if pa.PageSize < remain {
		return pa.PageSize
	}
	return remain
}

// calcMaxPage 通过其他参数计算 pa.MaxPage
func (pa *Pagination) calcMaxPage() {
	pa.MaxPage = pa.Count / pa.PageSize
	if pa.Count%pa.PageSize != 0 {
		pa.MaxPage++
	}
}

// MakePagination make Pagination
func MakePagination(count, p, pageSize int64) Pagination {
	if pageSize <= 0 {
		pageSize = DefaultPageSize
	}
	pa := Pagination{Count: count, P: p, PageSize: pageSize}
	pa.calcMaxPage()
	return pa
}

// ApplyTo 应用 Pagination 到 db 的 Qurey
// NOTICE: 确保 pa 有效
func (pa Pagination) ApplyTo(db *gorm.DB) *gorm.DB {
	return db.Offset(pa.Offset()).Limit(pa.Limit())
}

// SetFindOptions 设置 mongodb 的 FindOptions
// f 是 nil 则使用 options.Find()
// NOTICE: 确保 pa 有效
func (pa Pagination) SetFindOptions(f *options.FindOptions) *options.FindOptions {
	if f == nil {
		f = options.Find()
	}
	return f.SetSkip(pa.Offset()).SetLimit(pa.Limit())
}

// SetSpecialFindOptions 设置 mongodb 的 FindOptions
// f 是 nil 则使用 options.Find()
// NOTICE: 确保 pa 有效，offset: 偏移量，第一页只查 pagesize - offset 条，后续页查询条数正常
// TODO: 当前只适用于第一页发生偏移，偏移量 offset 小于 pagesize 的情况
func (pa Pagination) SetSpecialFindOptions(f *options.FindOptions, offset int64) *options.FindOptions {
	if offset >= pa.PageSize {
		panic(fmt.Sprintf("offset: %d must be smaller than page_size: %d", offset, pa.PageSize))
	}
	if f == nil {
		f = options.Find()
	}
	if pa.P == int64(1) {
		return f.SetSkip(pa.Offset()).SetLimit(pa.PageSize - offset)
	}
	return f.SetSkip(pa.Offset() - offset).SetLimit(pa.Limit())
}

// MarkerParam 基础 marker 参数，用于单字段筛选（同时该字段倒序排序的情况）
type MarkerParam struct {
	FieldName string

	FieldValue         int64
	ID                 int64
	HasSameValueAtLast bool

	isEmpty bool
}

// DecodeFieldMarkerParam decode MarkerParam
func DecodeFieldMarkerParam(marker string, fieldName string) (MarkerParam, bool) {
	param := MarkerParam{
		FieldName: fieldName,
		isEmpty:   len(marker) == 0,
	}
	// 第一次请求 marker 为空
	if param.isEmpty {
		return param, true
	}

	// TODO: marker 后续需要加签
	s := strings.Split(marker, ",")
	if len(s) != 3 {
		return param, false
	}
	var err error
	param.FieldValue, err = strconv.ParseInt(s[0], 10, 64)
	if err != nil {
		return param, false
	}
	param.ID, err = strconv.ParseInt(s[1], 10, 64)
	if err != nil {
		return param, false
	}
	if s[2] != "0" && s[2] != "1" {
		return param, false
	}
	if s[2] == "1" {
		param.HasSameValueAtLast = true
	}
	return param, true
}

// Encode 构造 marker 字符串
func (param MarkerParam) Encode() string {
	return fmt.Sprintf("%d,%d,%d", param.FieldValue, param.ID, BoolToInt(param.HasSameValueAtLast))
}

func (param *MarkerParam) buildTimeRangeQuery() (string, []any) {
	if param.HasSameValueAtLast {
		return fmt.Sprintf("%s < ? OR (%s = ? AND id < ?)", param.FieldName, param.FieldName), []any{param.FieldValue, param.FieldValue, param.ID}
	}
	return fmt.Sprintf("%s < ?", param.FieldName), []any{param.FieldValue}
}

func (param *MarkerParam) buildOrder() string {
	return fmt.Sprintf("%s DESC, id DESC", param.FieldName)
}

// ApplyTo 将 marker 应用到 gorm v1 db 上
func (param MarkerParam) ApplyTo(db *gorm.DB, pageSize int64) *gorm.DB {
	if !param.isEmpty {
		query, args := param.buildTimeRangeQuery()
		db = db.Where(query, args...)
	}
	return db.Order(param.buildOrder()).Limit(pageSize + 1)
}

// ApplyToV2 将 marker 应用到 gorm v2 db 上
func (param MarkerParam) ApplyToV2(db *gormv2.DB, pageSize int64) *gormv2.DB {
	if !param.isEmpty {
		query, args := param.buildTimeRangeQuery()
		db = db.Where(query, args...)
	}
	return db.Order(param.buildOrder()).Limit(int(pageSize + 1))
}

// ProcessMarkerResult 处理查询结果并生成 marker
func ProcessMarkerResult[T any](pageSize int64, fieldName string, records []T) ([]T, MarkerPagination) {
	var pa MarkerPagination
	if int64(len(records)) <= pageSize {
		// 没有更多数据，返回空 pa
		return records, pa
	}
	pa.HasMore = true
	nextRecord := records[pageSize]
	records = records[:pageSize]
	last := records[len(records)-1]
	param := MarkerParam{
		FieldValue: getGormFieldInt64Value(last, fieldName),
		ID:         getGormFieldInt64Value(last, "id"),
	}
	param.HasSameValueAtLast = param.FieldValue == getGormFieldInt64Value(nextRecord, fieldName)
	pa.Marker = param.Encode()
	return records, pa
}

func getGormFieldInt64Value(v any, fieldName string) int64 {
	t := reflect.TypeOf(v)
	val := reflect.ValueOf(v)
	if t.Kind() == reflect.Pointer {
		t = t.Elem()
		val = val.Elem()
	}
	for i := 0; i < t.NumField(); i++ {
		if isGormColumn(t.Field(i).Tag, fieldName) {
			return val.Field(i).Int()
		}
	}
	panic("cannot find gorm tag " + fieldName)
}

func isGormColumn(tag reflect.StructTag, fieldName string) bool {
	t := tag.Get("gorm")
	t = strings.SplitN(t, ";", 2)[0]
	return t == "column:"+fieldName
}
