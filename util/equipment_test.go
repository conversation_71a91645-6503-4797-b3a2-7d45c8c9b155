package util

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOs(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, Unknown)
	assert.Equal(1, Android)
	assert.Equal(2, I<PERSON>)
	assert.Equal(3, Web)
	assert.Equal(4, MobileWeb)
	assert.Equal(5, Windows)
	assert.Equal(6, <PERSON><PERSON>)
}

func TestParsePlatform(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(Unknown, ParsePlatform("123"))
	assert.Equal(IOS, ParsePlatform("iOS"))
	assert.Equal(Android, ParsePlatform("Android"))
	assert.Equal(Windows, ParsePlatform("Windows"))
	assert.Equal(HarmonyOS, ParsePlatform("HarmonyOS"))
}

func TestNewEquipment(t *testing.T) {
	assert := assert.New(t)

	e := NewEquipment("MissEvanApp/4.2.4 (iOS;12.0.1;iPhone7,2)")
	assert.Equal(IOS, e.OS)
	assert.Equal("12.0.1", e.OSVersion)
	assert.Equal("iPhone7,2", e.<PERSON>ce<PERSON>)
	assert.Equal(true, e.FromApp)

	e = NewEquipment("Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36")
	assert.Equal(Web, e.OS)

	e = NewEquipment("MissEvanApp/4.2.4 (iOS)")
	assert.Equal(Unknown, e.OS)

	e = NewEquipment("Mozilla/5.0 (Linux; Android 4.4.4; N5209 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36 MissEvanApp/5.2.0")
	assert.Equal(MobileWeb, e.OS)

	e = NewEquipment("Mozilla/5.0 (Linux; Android 10; Pixel 4 XL Build/QQ1B.191205.012.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Mobile Safari/537.36 MissEvanApp/5.3.1 (Theme Light; NetType 4G; FreeFlow)")
	assert.Equal(MobileWeb, e.OS)

	e = NewEquipment("Mozilla/5.0 (Phone; HarmonyOS 4.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 ArkWeb/******* Mobile")
	assert.Equal(MobileWeb, e.OS)

	e = NewEquipment("Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  ArkWeb/******* Mobile HuaweiBrowser/5.0.4.300")
	assert.Equal(MobileWeb, e.OS)
}

func TestParseEquipment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	want := &Equipment{
		EquipID:     "5b29bec2-cfd8-b345-6b66-ece42b359995",
		BUVID:       "Z342EB37EED43E82411D878802220C5F6952",
		AppVersion:  "4.2.2",
		OS:          IOS,
		OSVersion:   "12.0",
		DeviceModel: "iPhone9,2",
		FromApp:     true,
	}

	r := httptest.NewRequest(http.MethodGet, "/path", nil)
	ua := "MissEvanApp/" + want.AppVersion + " (iOS;" + want.OSVersion + ";" + want.DeviceModel + ")"
	r.Header.Add("User-Agent", ua)
	r.AddCookie(&http.Cookie{Name: "equip_id", Value: want.EquipID})
	r.AddCookie(&http.Cookie{Name: "buvid", Value: want.BUVID})
	equip, err := ParseEquipment(r)
	require.NoError(err)
	assert.Equal(want, equip)
}

func TestParseBuvid(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := httptest.NewRequest(http.MethodGet, "/path", nil)
	buvid, err := ParseBuvid(r)
	require.NoError(err)
	assert.Empty(buvid)

	value := "Z342EB37EED43E82411D878802220C5F6952"
	r.AddCookie(&http.Cookie{Name: "buvid", Value: value})
	buvid, err = ParseBuvid(r)
	require.NoError(err)
	assert.Equal(value, buvid)
}

func TestIsAppOlderThan(t *testing.T) {
	assert := assert.New(t)

	e := NewEquipment("MissEvanApp/4.2.4 (iOS;12.0.1;iPhone7,2)")
	assert.True(e.IsAppOlderThan("4.3.4", "5.2.5"))
	assert.False(e.IsAppOlderThan("", "5.2.5"))

	e = NewEquipment("MissEvanApp/4.3.4 (iOS;12.0.1;iPhone7,2)")
	assert.False(e.IsAppOlderThan("4.3.4", "5.2.5"))

	e = NewEquipment("MissEvanApp/5.1.8 (Android;6.0.1;OPPO OPPO R9sk R9sk)")
	assert.True(e.IsAppOlderThan("4.3.4", "5.2.5"))
	assert.False(e.IsAppOlderThan("4.3.4", ""))

	e = NewEquipment("MissEvanApp/5.2.5 (Android;6.0.1;OPPO OPPO R9sk R9sk)")
	assert.False(e.IsAppOlderThan("4.3.4", "5.2.5"))

	e = NewEquipment("MissEvanApp/4.2.4")
	assert.False(e.IsAppOlderThan("4.3.4", "5.2.5"))
}

func TestAppVersionsVersion(t *testing.T) {
	assert := assert.New(t)

	v := AppVersions{
		IOS:       "1.2.3",
		Android:   "4.5.6",
		Windows:   "7.8.9",
		HarmonyOS: "6.2.3",
	}
	assert.Equal(v.IOS, v.version(IOS))
	assert.Equal(v.Android, v.version(Android))
	assert.Equal(v.Windows, v.version(Windows))
	assert.Equal(v.HarmonyOS, v.version(HarmonyOS))
	assert.Equal("", v.version(-1))
}

func TestEquipIsOldApp(t *testing.T) {
	assert := assert.New(t)

	v := AppVersions{
		IOS:       "1.0.0",
		Android:   "1.0.0",
		HarmonyOS: "1.0.0",
	}

	e := Equipment{
		FromApp:    true,
		OS:         -1,
		AppVersion: "1.2.3",
	}
	assert.False(e.IsOldApp(v))

	e = Equipment{
		FromApp:    true,
		OS:         IOS,
		AppVersion: "1.2.3",
	}
	assert.False(e.IsOldApp(v))
	e.AppVersion = "0.0.1"
	assert.True(e.IsOldApp(v))

	e = Equipment{
		FromApp:    true,
		OS:         Android,
		AppVersion: "1.2.3",
	}
	assert.False(e.IsOldApp(v))
	e.AppVersion = "0.0.1"
	assert.True(e.IsOldApp(v))

	e = Equipment{
		FromApp:    true,
		OS:         HarmonyOS,
		AppVersion: "1.2.3",
	}
	assert.False(e.IsOldApp(v))
	e.AppVersion = "0.0.1"
	assert.True(e.IsOldApp(v))

	e = Equipment{
		FromApp: false,
		OS:      Web,
	}
	assert.False(e.IsOldApp(v))
}

func TestVersionCompare(t *testing.T) {
	assert := assert.New(t)

	assert.False(VersionCompare("1.2.3-alpha", "1.2.3RC7", OperatorGe))
	assert.True(VersionCompare("1.2.3-beta", "1.2.3pl", OperatorLt))
	assert.False(VersionCompare("1.1_dev", "1.2any", OperatorEq))
}

func TestIsFromGoogleChannel(t *testing.T) {
	assert := assert.New(t)

	req := httptest.NewRequest(http.MethodGet, "/", nil)
	assert.False(IsFromGoogleChannel(req))

	req.Header.Set("channel", ChannelGoogle)
	assert.True(IsFromGoogleChannel(req))

	req.Header.Set("channel", ChannelGoogle64Bit)
	assert.True(IsFromGoogleChannel(req))
}

func TestIsFromHarmonyOSMobileWeb(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsFromHarmonyOSMobileWeb("Mozilla/5.0 (Phone; HarmonyOS 4.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 ArkWeb/******* Mobile"))
	assert.True(IsFromHarmonyOSMobileWeb("Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  ArkWeb/******* Mobile HuaweiBrowser/5.0.4.300"))
	assert.False(IsFromHarmonyOSMobileWeb("Mozilla/5.0 (Linux; Android 10; Pixel 4 XL Build/QQ1B.191205.012.A1; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/79.0.3945.93 Mobile Safari/537.36 MissEvanApp/5.3.1 (Theme Light; NetType 4G; FreeFlow)"))
}
