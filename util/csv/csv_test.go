package csv

import (
	"bufio"
	"bytes"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// newTestCSVColumnData 新建用于测试的 csv 字符串数据
// rows 为字段名，可以是多个，最终数据以 ${字段名}_${行数} 的形式进行生成
// rowsNum 为需要生成的行数
func newTestCSVColumnData(rows []string, rowsNum int64) string {
	if len(rows) == 0 || rowsNum == 0 {
		return ""
	}

	content := make([]string, rowsNum+1)
	content[0] = strings.Join(rows, ",")
	for i := range content {
		if i == len(content)-1 {
			break
		}

		columnLine := make([]string, len(rows))
		for j := range columnLine {
			columnLine[j] = fmt.Sprintf("%s_%d", rows[j], i+1)
		}
		content[i+1] = strings.Join(columnLine, ",")
	}

	return strings.Join(content, "\n")
}

// newTestReader 新建一个用于测试的 io.Reader
// content 为传入的 bytes
func newTestReader(content []byte) (*bytes.Reader, error) {
	return bytes.NewReader(content), nil
}

func cleanTestFile(path string) {
	_, err := os.Stat(path)
	if err == nil {
		err2 := os.Remove(path)
		if err2 != nil {
			logger.Error(err2)
		}
	} else {
		logger.Error(err)
	}
}

func removeAndCreate(content []byte, path string) (*os.File, error) {
	_, err := os.Stat(path)
	if err == nil {
		err := os.Remove(path)
		if err != nil {
			return nil, err
		}
	}

	err = ioutil.WriteFile(path, content, 0644)
	if err != nil {
		return nil, err
	}

	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}

	return file, nil
}

func TestRead(t *testing.T) {
	t.Run("EmptyCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		csvFile, err := newTestReader(make([]byte, 0))
		require.NoError(err)

		err = Read(csvFile, func([][]string, int64) error { return nil })
		assert.EqualError(err, "CSV 不可为空文件，请检查后再试")
	})

	t.Run("CallbackError", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(2)
		csvContent := newTestCSVColumnData([]string{"test"}, length)
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		returnError := func(rows [][]string, startLine int64) error {
			runTimes++
			return errors.New("test error")
		}
		err = Read(csvFile, returnError)
		assert.EqualError(err, "test error")
		assert.Equal(1, runTimes)
	})

	t.Run("OneLineEmptyCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(7)
		csvContent := newTestCSVColumnData([]string{"user_id", "num"}, length)
		csvContent = strings.ReplaceAll(csvContent, "user_id_1,num_1", "")
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			runTimes++
			require.NotEmpty(rows)
			require.NoError(err)
			assert.Len(rows, int(length)-1)
			assert.Equal(int64(2), startLine)
			for i := range rows {
				assert.Len(rows[i], 2)
				assert.NotEmpty(rows[i])
			}
			assert.Equal("user_id_2", rows[0][0])
			return nil
		})
		require.NoError(err)
		assert.Equal(1, runTimes)
	})

	t.Run("OneLineEmptyFieldsCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(7)
		csvContent := newTestCSVColumnData([]string{"user_id", "num"}, length)
		csvContent = strings.ReplaceAll(csvContent, "user_id_1", "")
		csvContent = strings.ReplaceAll(csvContent, "num_1", " ")
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			runTimes++
			require.NotEmpty(rows)
			require.NoError(err)
			assert.Len(rows, int(length)-1)
			for i := range rows {
				assert.Len(rows[i], 2)
				assert.NotEmpty(rows[i])
			}
			assert.Equal("user_id_2", rows[0][0])
			return nil
		})
		require.NoError(err)
		assert.Equal(1, runTimes)
	})

	t.Run("OneLineError", func(t *testing.T) {
		require := require.New(t)

		csvContent := newTestCSVColumnData([]string{"user_id", "num"}, 7)
		csvContent = strings.ReplaceAll(csvContent, ",num_1", "")
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		err = Read(csvFile, func([][]string, int64) error { return nil })
		require.EqualError(err, "record on line 2: wrong number of fields", "第二行有字段缺失")
	})

	t.Run("OneColumnCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(7)
		csvContent := newTestCSVColumnData([]string{"user_id"}, length)
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			runTimes++
			require.NotEmpty(rows)
			require.NoError(err)
			assert.Len(rows, int(length))
			for i := range rows {
				assert.Len(rows[i], 1)
				assert.NotEmpty(rows[i])
			}
			return nil
		})
		require.NoError(err)
		assert.Equal(1, runTimes)
	})

	t.Run("MultiColumnCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(7)
		csvContent := newTestCSVColumnData([]string{"user_id", "num"}, length)
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			runTimes++
			require.NotEmpty(rows)
			require.NoError(err)
			assert.Len(rows, int(length))
			for i := range rows {
				assert.Len(rows[i], 2)
				assert.NotEmpty(rows[i])
			}
			return nil
		})
		require.NoError(err)
		assert.Equal(1, runTimes)
	})

	t.Run("MaxBatchLinesParameter", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		batchSize := int64(20)
		length := batchSize + 10
		csvContent := newTestCSVColumnData([]string{"user_id"}, length)
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		totalRows := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			require.NotEmpty(rows)
			require.NoError(err)
			totalRows += len(rows)
			if runTimes == 0 {
				assert.Len(rows, int(batchSize))
				assert.Equal(int64(2), startLine)
			}
			if runTimes == 1 {
				assert.Len(rows, 10)
				assert.Equal(int64(22), startLine)
			}
			for i := range rows {
				assert.Len(rows[i], 1)
				assert.NotEmpty(rows[i])
			}

			runTimes++
			return nil
		}, batchSize)
		require.NoError(err)
		assert.Equal(2, runTimes)
		assert.Equal(length, int64(totalRows))
	})

	t.Run("BatchRead10kLinesCSV", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := defaultMaxBatchLines + 10
		csvContent := newTestCSVColumnData([]string{"user_id"}, length)
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(err)

		runTimes := 0
		totalRows := 0
		err = Read(csvFile, func(rows [][]string, startLine int64) error {
			require.NotEmpty(rows)
			require.NoError(err)
			totalRows += len(rows)
			if runTimes == 0 {
				assert.Len(rows, int(defaultMaxBatchLines))
				assert.Equal(int64(2), startLine)
			}
			if runTimes == 1 {
				assert.Len(rows, 10)
				assert.Equal(defaultMaxBatchLines+2, startLine)
			}
			for i := range rows {
				assert.Len(rows[i], 1)
				assert.NotEmpty(rows[i])
			}

			runTimes++
			return nil
		})
		require.NoError(err)
		assert.Equal(2, runTimes)
		assert.Equal(length, int64(totalRows))
	})
}

func TestReadFromFile(t *testing.T) {
	t.Run("ReadFromFile", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		length := int64(7)
		csvContent := newTestCSVColumnData([]string{"user_id"}, length)
		_, err := removeAndCreate([]byte(csvContent), "test.csv")
		require.NoError(err)
		defer cleanTestFile("test.csv")

		runTimes := 0
		err = ReadFromFile("test.csv", func(rows [][]string, startLine int64) error {
			runTimes++
			require.NotEmpty(rows)
			require.NoError(err)
			assert.Len(rows, int(length))
			assert.Equal(int64(runTimes-1)*defaultMaxBatchLines+2, startLine)
			for i := range rows {
				assert.Len(rows[i], 1)
				assert.NotEmpty(rows[i])
			}
			return nil
		})
		require.NoError(err)
		assert.Equal(1, runTimes)
	})
}

func TestHasBOM(t *testing.T) {
	t.Run("TooShort", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		reader := bufio.NewReader(bytes.NewReader([]byte{0xEF}))
		has, err := hasBOM(reader)
		require.NoError(err)
		assert.False(has)
	})

	t.Run("HasBOM", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		reader := bufio.NewReader(bytes.NewReader([]byte{0xEF, 0xBB, 0xBF}))
		has, err := hasBOM(reader)
		require.NoError(err)
		assert.True(has)
	})

	t.Run("NotHaveBOM", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		reader := bufio.NewReader(bytes.NewReader([]byte{0xAF, 0xAB, 0xBA}))
		has, err := hasBOM(reader)
		require.NoError(err)
		assert.False(has)
	})
}

func TestSkipBOM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建基础 csv 数据
	rows := 7
	csvData := newTestCSVColumnData([]string{"user_id"}, int64(rows))

	t.Run("HasBOM", func(t *testing.T) {
		var b bytes.Buffer
		b.Write(utf8BOMBytes)
		b.Write([]byte(csvData))
		bytesReader := bytes.NewReader(b.Bytes())
		reader, err := SkipBOM(bytesReader)
		require.NoError(err)

		record, err := io.ReadAll(reader)
		require.NoError(err)
		assert.Equal(csvData, string(record))
	})

	t.Run("NotHaveBOM", func(t *testing.T) {
		var b bytes.Buffer
		b.Write([]byte(csvData))
		bytesReader := bytes.NewReader(b.Bytes())
		reader, err := SkipBOM(bytesReader)
		require.NoError(err)

		record, err := io.ReadAll(reader)
		require.NoError(err)
		assert.Equal(csvData, string(record))
	})
}

func TestNewReader(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建基础 csv 数据
	csvData := newTestCSVColumnData([]string{"user_id"}, int64(1))

	t.Run("HasBOM", func(t *testing.T) {
		var b bytes.Buffer
		b.Write(utf8BOMBytes)
		b.Write([]byte(csvData))
		bytesReader := bytes.NewReader(b.Bytes())
		csvReader, err := NewReader(bytesReader)
		require.NoError(err)

		records, err := csvReader.ReadAll()
		require.NoError(err)
		assert.Equal("user_id", records[0][0])
		assert.Equal("user_id_1", records[1][0])
	})

	t.Run("NotHaveBOM", func(t *testing.T) {
		var b bytes.Buffer
		b.Write([]byte(csvData))
		bytesReader := bytes.NewReader(b.Bytes())
		csvReader, err := NewReader(bytesReader)
		require.NoError(err)

		records, err := csvReader.ReadAll()
		require.NoError(err)
		assert.Equal("user_id", records[0][0])
		assert.Equal("user_id_1", records[1][0])
	})
}

func TestReadFirstColumnInt64(t *testing.T) {
	t.Run("ValidData", func(t *testing.T) {
		csvContent := "id\n1\n2\n3\n4\n5"
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		numbers, err := ReadFirstColumnInt64(csvFile)
		require.NoError(t, err)
		assert.Equal(t, []int64{1, 2, 3, 4, 5}, numbers)
	})

	t.Run("EmptyFile", func(t *testing.T) {
		csvFile, err := newTestReader([]byte(""))
		require.NoError(t, err)

		_, err = ReadFirstColumnInt64(csvFile)
		assert.EqualError(t, err, "CSV 不可为空文件，请检查后再试")
	})

	t.Run("InvalidData", func(t *testing.T) {
		csvContent := "id\n1\nabc\n3\n4\n5"
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		_, err = ReadFirstColumnInt64(csvFile)
		assert.EqualError(t, err, "CSV 文件内容错误, 行 3")
	})

	t.Run("ALotOfInvalidData", func(t *testing.T) {
		csvContent := "id\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\na\nb\nc\nd\ne\nf\ng\nh\ni\nj"
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		_, err = ReadFirstColumnInt64(csvFile, 10)
		assert.EqualError(t, err, "CSV 文件内容错误, 行 12,13,14,15,16,...")
	})

	t.Run("EmptyLines", func(t *testing.T) {
		csvContent := "id\n1\n\n3\n\n5"
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		numbers, err := ReadFirstColumnInt64(csvFile)
		require.NoError(t, err)
		assert.Equal(t, []int64{1, 3, 5}, numbers)
	})

	t.Run("NumbersWithSpaces", func(t *testing.T) {
		csvContent := "id\n 1\n2 \n 3 "
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		numbers, err := ReadFirstColumnInt64(csvFile)
		require.NoError(t, err)
		assert.Equal(t, []int64{1, 2, 3}, numbers)
	})

	t.Run("LessDataThanExpected", func(t *testing.T) {
		csvContent := "id\n1\n2\n3"
		csvFile, err := newTestReader([]byte(csvContent))
		require.NoError(t, err)

		numbers, err := ReadFirstColumnInt64(csvFile)
		require.NoError(t, err)
		assert.Equal(t, []int64{1, 2, 3}, numbers)
	})
}
