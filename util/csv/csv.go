package csv

import (
	"bufio"
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
)

const defaultMaxBatchLines = int64(1000)

var utf8BOMBytes = []byte{0xEF, 0xBB, 0xBF}

// Read 读取 csv 文件，通过回调默认每次批量最多返回 1000 行数据，
// 回调函数中 rows 为批量读取 csv 所对应的列表，startLine 表示当前批次的起始行数，行数从 1 开始计算
func Read(input io.Reader, callback func(rows [][]string, startLine int64) error, maxBatchLines ...int64) error {
	r, err := NewReader(input)
	if err != nil {
		return err
	}
	// 读取第一行，同时忽略表头
	_, err = r.Read()
	if err == io.EOF {
		return errors.New("CSV 不可为空文件，请检查后再试")
	}
	if err != nil {
		return err
	}

	// 初始化
	lastLine := int64(1)               // 不清零，总行数，行数从 1 开始计算，含表头
	startLine := lastLine + 1          // 当前批次的起始行数，行数从 1 开始计算，含表头
	readLines := int64(0)              // 清零，用于批量计算
	batchLines := defaultMaxBatchLines // 单批最多行数
	if len(maxBatchLines) != 0 && maxBatchLines[0] != 0 {
		batchLines = maxBatchLines[0]
	}

	rows := make([][]string, 0, batchLines)
	for {
		row, err := r.Read()
		if err != nil {
			// 已经读取到文件末尾
			if err == io.EOF {
				break
			}
			return err
		}
		trimmedRow := make([]string, 0, len(row))
		for _, v := range row {
			trimmedRow = append(trimmedRow, strings.TrimSpace(v))
		}

		// NOTICE: 如果读取到的行是完全的空字符串，则会被跳过
		// 如果读取到的行是空字段（比如：,,），则不会被加入到 rows 中
		lastLine++
		if !isEmpty(trimmedRow) {
			rows = append(rows, trimmedRow)
			readLines++

			// 已读取行数达到上限
			if readLines >= batchLines {
				// 调用回调，并且重置行数以及切片
				cbErr := callback(rows, startLine)
				if cbErr != nil {
					return cbErr
				}

				rows = rows[:0]
				readLines = 0
				startLine = lastLine + 1
			}
		}
	}

	var cbErr error
	if len(rows) > 0 {
		// 最终 EOF 调用回调
		cbErr = callback(rows, startLine)
	}

	return cbErr
}

func truncateElementsToString[T int | int64](elements []T) string {
	const limit = 5
	strList := make([]string, 0, limit+1)
	for i := 0; i < len(elements); i++ {
		if i >= limit {
			strList = append(strList, "...")
			break
		}
		strList = append(strList, fmt.Sprintf("%d", elements[i]))
	}
	return strings.Join(strList, ",")
}

// ReadFirstColumnInt64 读取 csv 文件的第一列，并返回 int64 类型的列表
// 如果读取到的行中包含非数字，则会被跳过，并返回错误
func ReadFirstColumnInt64(input io.Reader, maxBatchLines ...int64) ([]int64, error) {
	batchLines := defaultMaxBatchLines
	if len(maxBatchLines) != 0 && maxBatchLines[0] != 0 {
		batchLines = maxBatchLines[0]
	}
	allRows := make([]int64, 0, batchLines)
	err := Read(input, func(rows [][]string, startLine int64) error {
		badRows := make([]int64, 0, len(rows))
		for i, v := range rows {
			value, err := strconv.ParseInt(v[0], 10, 64)
			if err != nil {
				badRows = append(badRows, startLine+int64(i))
				// PASS
				continue
			}
			allRows = append(allRows, value)
		}
		if len(badRows) > 0 {
			return fmt.Errorf("CSV 文件内容错误, 行 %s", truncateElementsToString(badRows))
		}
		return nil
	}, batchLines)
	if err != nil {
		return nil, err
	}
	if len(allRows) == 0 {
		return nil, errors.New("CSV 文件中没有有效的数据")
	}
	return allRows, nil
}

// ReadFromFile 从文件路径读取文件，并通过回调默认每次批量最多返回 1000 行数据，
// 回调函数中 rows 为批量读取 csv 所对应的列表，lastLine 表示当前读取到的行数，行数从 1 开始计算
func ReadFromFile(filePath string, callback func(rows [][]string, lastLine int64) error, maxBatchLines ...int64) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	return Read(file, callback, maxBatchLines...)
}

// determine if the input csv row is empty
func isEmpty(row []string) bool {
	if len(row) == 0 {
		return true
	}
	for _, v := range row {
		if v != "" {
			return false
		}
	}
	return true
}

func hasBOM(reader *bufio.Reader) (bool, error) {
	prefixBytes, err := reader.Peek(len(utf8BOMBytes))
	if err != nil && err != io.EOF {
		return false, err
	}

	return bytes.Equal(prefixBytes, utf8BOMBytes), nil
}

// SkipBOM 跳过 UTF-8 BOM 头
func SkipBOM(reader io.Reader) (io.Reader, error) {
	bReader := bufio.NewReader(reader)
	has, err := hasBOM(bReader)
	if err != nil {
		return nil, err
	}
	if has {
		_, err = bReader.Discard(len(utf8BOMBytes))
		if err != nil {
			return nil, err
		}
	}
	return bReader, nil
}

// NewReader 返回新的 csv 读取器，如果 csv 文件格式为 utf-8 with BOM 则会自动跳过 BOM 开头
func NewReader(reader io.Reader) (*csv.Reader, error) {
	// check for BOM
	skippedReader, err := SkipBOM(reader)
	if err != nil {
		return nil, err
	}
	return csv.NewReader(skippedReader), nil
}
