package util

import (
	"fmt"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	gormv2 "gorm.io/gorm"
)

func TestPagination(t *testing.T) {
	assert := assert.New(t)

	pagination := MakePagination(101, 1, 20)
	assert.EqualValues(0, pagination.Offset())

	pagination = MakePagination(101, 6, 20)
	assert.EqualValues(6, pagination.MaxPage)
	assert.EqualValues(100, pagination.Offset())
	assert.True(pagination.Valid())

	pagination = MakePagination(101, 7, 20)
	assert.EqualValues(6, pagination.MaxPage)
	assert.False(pagination.Valid())

	pagination = MakePagination(0, 1, 20)
	assert.False(pagination.Valid())
}

func TestPaginationLimit(t *testing.T) {
	assert := assert.New(t)
	pa := MakePagination(10, 1, 20)
	assert.True(pa.Valid() && pa.Limit() == 10)
	pa = MakePagination(100, 4, 30)
	assert.Equal(int64(10), pa.Limit())
	assert.True(pa.Valid() && pa.Limit() == 10)
}

func TestPaginationApplyTo(t *testing.T) {
	assert := assert.New(t)

	db, _ := gorm.Open("testdb", "")
	pa := MakePagination(12, 3, 5)
	db = pa.ApplyTo(db)
	assert.Contains(fmt.Sprint(db.QueryExpr()), "LIMIT 2 OFFSET 10")
}

func TestDecodeFieldMarkerParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, ok := DecodeFieldMarkerParam("1", "time")
	require.False(ok)
	_, ok = DecodeFieldMarkerParam("a,2,1", "time")
	require.False(ok)
	_, ok = DecodeFieldMarkerParam("1,a,1", "time")
	require.False(ok)
	_, ok = DecodeFieldMarkerParam("1,2,2", "time")
	require.False(ok)

	expected := MarkerParam{
		FieldName:          "time",
		FieldValue:         1,
		ID:                 2,
		HasSameValueAtLast: true,
	}
	param, ok := DecodeFieldMarkerParam("1,2,1", "time")
	require.True(ok)
	assert.Equal(expected, param)

	expected.HasSameValueAtLast = false
	param, ok = DecodeFieldMarkerParam("1,2,0", "time")
	require.True(ok)
	assert.Equal(expected, param)
}

func TestMarkerParam_Encode(t *testing.T) {
	assert := assert.New(t)

	param := MarkerParam{
		FieldValue:         12,
		ID:                 1,
		HasSameValueAtLast: false,
	}
	assert.Equal("12,1,0", param.Encode())
}

func TestTestMarkerParam_ApplyTo(t *testing.T) {
	assert := assert.New(t)

	db, _ := gorm.Open("testdb", "")
	param := MarkerParam{
		FieldName:          "create_time",
		FieldValue:         1234567890,
		ID:                 10,
		HasSameValueAtLast: true,
	}
	db2 := param.ApplyTo(db, 2)
	assert.Contains(fmt.Sprint(db2.QueryExpr()),
		"WHERE (create_time < ? OR (create_time = ? AND id < ?)) ORDER BY create_time DESC, id DESC LIMIT 3")

	param.HasSameValueAtLast = false
	db2 = param.ApplyTo(db, 2)
	assert.Contains(fmt.Sprint(db2.QueryExpr()),
		"WHERE (create_time < ?) ORDER BY create_time DESC, id DESC LIMIT 3")

	param = MarkerParam{
		FieldName: "create_time",
		isEmpty:   true,
	}
	db2 = param.ApplyTo(db, 2)
	assert.Contains(fmt.Sprint(db2.QueryExpr()), "ORDER BY create_time DESC, id DESC LIMIT 3")
}

func TestMarkerParam_ApplyToV2(t *testing.T) {
	assert := assert.New(t)

	db, _ := gormv2.Open(sqlite.Open(""), nil)
	param := MarkerParam{
		FieldName:          "create_time",
		FieldValue:         1234567890,
		ID:                 10,
		HasSameValueAtLast: true,
	}

	type test struct {
	}
	sql := db.ToSQL(func(tx *gormv2.DB) *gormv2.DB {
		return param.ApplyToV2(tx, 2).Find(&test{})
	})
	assert.Contains(sql, "SELECT * FROM `tests` WHERE create_time < 1234567890 OR (create_time = 1234567890 AND id < 10) ORDER BY create_time DESC, id DESC LIMIT 3")

	param.HasSameValueAtLast = false
	sql = db.ToSQL(func(tx *gormv2.DB) *gormv2.DB {
		return param.ApplyToV2(tx, 2).Find(&test{})
	})
	assert.Contains(sql, "SELECT * FROM `tests` WHERE create_time < 1234567890 ORDER BY create_time DESC, id DESC LIMIT 3")

	param = MarkerParam{
		FieldName: "create_time",
		isEmpty:   true,
	}
	sql = db.ToSQL(func(tx *gormv2.DB) *gormv2.DB {
		return param.ApplyToV2(tx, 2).Find(&test{})
	})
	assert.Contains(sql, "SELECT * FROM `tests` ORDER BY create_time DESC, id DESC LIMIT 3")
}

type markerRecord struct {
	ID         int64 `gorm:"column:id;primary_key"`
	FieldValue int64 `gorm:"column:field_value"`
}

func TestProcessMarkerResult(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	records := []markerRecord{
		{ID: 1, FieldValue: 10},
		{ID: 2, FieldValue: 9},
		{ID: 3, FieldValue: 8},
	}
	r2, pa := ProcessMarkerResult(2, "field_value", records)
	require.Len(r2, 2)
	assert.True(pa.HasMore)
	assert.Equal("9,2,0", pa.Marker)

	records2 := []*markerRecord{
		{ID: 1, FieldValue: 10},
		{ID: 2, FieldValue: 9},
		{ID: 3, FieldValue: 8},
	}
	r3, pa := ProcessMarkerResult(2, "field_value", records2)
	require.Len(r3, 2)
	assert.True(pa.HasMore)
	assert.Equal("9,2,0", pa.Marker)

	r3, pa = ProcessMarkerResult(3, "field_value", records2)
	require.Len(r3, 3)
	assert.Zero(pa)
}
