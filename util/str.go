package util

import (
	"html"
	"regexp"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/rivo/uniseg"
	emoji "github.com/tmdvs/Go-Emoji-Utils"
)

// ReplaceAllStringSubmatchFunc is the 'Submatch' version of ReplaceAllStringFunc
func ReplaceAllStringSubmatchFunc(re *regexp.Regexp, str string, repl func([]string) string) string {
	result := ""
	lastIndex := 0

	for _, v := range re.FindAllSubmatchIndex([]byte(str), -1) {
		groups := []string{}
		for i := 0; i < len(v); i += 2 {
			groups = append(groups, str[v[i]:v[i+1]])
		}

		result += str[lastIndex:v[0]] + repl(groups)
		lastIndex = v[1]
	}

	return result + str[lastIndex:]
}

// Substr is the Unicode version of input[start:start+length]
func Substr(input string, start int, length int) string {
	if length < 0 {
		panic("length must be greater than or equal to zero")
	}
	asRunes := []rune(input)

	if start >= len(asRunes) {
		return ""
	}

	if start+length > len(asRunes) {
		length = len(asRunes) - start
	}

	return string(asRunes[start : start+length])
}

// SplitToInt64Array split str with sep, return int64 array
func SplitToInt64Array(s, sep string) ([]int64, error) {
	if s == "" {
		return []int64{}, nil
	}

	splitList := strings.Split(s, sep)
	result := make([]int64, len(splitList))
	for i, v := range splitList {
		id, err := strconv.ParseInt(strings.TrimSpace(v), 10, 64)
		if err != nil {
			return nil, err
		}
		result[i] = id
	}
	return result, nil
}

// FormatMessage format message
func FormatMessage(message string, params map[string]string) string {
	for key, value := range params {
		message = strings.ReplaceAll(message, "${"+key+"}", value)
	}
	return message
}

// FormatMessageBracketed format message with bracketed placeholder params like #[key]
func FormatMessageBracketed(message string, params map[string]string) string {
	for key, value := range params {
		message = strings.ReplaceAll(message, "#["+key+"]", value)
	}
	return message
}

// UniqString 合并重复字符串
// Deprecated: 使用 sets.Uniq 代替此 func
func UniqString(ss []string) []string {
	set := make(map[string]struct{}, len(ss))
	res := make([]string, 0, len(ss))
	for _, s := range ss {
		if _, ok := set[s]; !ok {
			set[s] = struct{}{}
			res = append(res, s)
		}
	}
	return res
}

// ContainsEmoji 查看字符串是否包含 emoji
func ContainsEmoji(str string) bool {
	// 分析并提取字符，细节解释请移步：https://github.com/rivo/uniseg
	gr := uniseg.NewGraphemes(str)
	for gr.Next() {
		// 判断字符是否为 emoji
		if _, err := emoji.LookupEmoji(gr.Str()); err == nil {
			return true
		}
	}
	return false
}

// ExtractHTMLPlainText 将文本中的 HTML 标签去除，传入 maxLength 可以截取指定长度
func ExtractHTMLPlainText(text string, maxLength ...int) string {
	// 去除 HTML 标签
	re := regexp.MustCompile(`<[^>]*>`)
	text = re.ReplaceAllString(text, "")
	// 解码 HTML 实体
	text = html.UnescapeString(text)

	// 判断是否需要截取
	if len(maxLength) > 0 && maxLength[0] > 0 && utf8.RuneCountInString(text) > maxLength[0] {
		// 使用 rune 切片来确保不会在字符中间截断
		return string([]rune(text)[:maxLength[0]])
	}
	return text
}
