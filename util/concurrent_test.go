package util

import (
	"fmt"
	"math/rand"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

type log struct {
	strings.Builder
}

func (s *log) Errorf(format string, v ...interface{}) {
	s.WriteString(fmt.Sprintf(format, v...))
}

func TestGo(t *testing.T) {
	var lg log
	InitGoroutineLogger(&lg)

	Go(func() {
		panic("⚠️")
	})

	time.Sleep(50 * time.Millisecond)
	assert.Contains(t, lg.String(), "⚠️")
}

func TestGoArg1(t *testing.T) {
	var lg log
	InitGoroutineLogger(&lg)

	GoArg1(func(str string) {
		panic("⚠️" + str)
	}, "1")

	time.Sleep(50 * time.Millisecond)
	assert.Contains(t, lg.String(), "⚠️1")
}

func TestLockedSource(t *testing.T) {
	assert := assert.New(t)

	now := TimeNow().Unix()
	src := rand.NewSource(now)
	lkSrc := NewLockedSource(now)

	assert.Equal(src.Int63(), lkSrc.Int63())
}

func TestWaitGo(t *testing.T) {
	assert := assert.New(t)

	went := true
	Go(func() {
		time.Sleep(1 * time.Second)
		went = false
	})

	assert.True(went)
	WaitGo()

	assert.False(went)
}
