tests:
  go:
    coverage: 'total:.*?[-+]?((?:\d*\.\d+|\d+)%)'
    delta_coverage: 'Diff Coverage[\s\S]*Coverage:.*?[-+]?((?:\d*\.\d+|\d+)%)'
    cmds:
      - 'go test -mod=mod -coverpkg=./... -coverprofile=cover.out ./...'
      - 'go tool cover -func=cover.out'
      - 'sh -c "gocov convert cover.out | gocov-xml > cover.xml"'
      - 'diff-cover cover.xml --compare-branch=$BASE_COMMIT'

# GitHub 账号参考：https://github.com/orgs/MiaoSiLa/teams/backend/members
# OA 账号参考：https://info.missevan.com/pages/viewpage.action?pageId=15371839
users: # GitHub 账号，OA 账号键值对
  tengattack: pengzhuoteng
  mowangsk: shaobo
  xiaoliwang: caojiepeng
  Jiie: jiangjie01
  liutian7y: wangchengcheng
  nekomeowww: zhang<PERSON><PERSON>
  luoluoluoxiaxiaixa: majinlong
  ZhengDexing: zhengdexing
  helloworldaha: wujun
  wms-github: wangmaosen
  icylight: wangsen
  fishcg: chengang
  xchenhao: chenhao03
  nichanglan: nichanglan
  Predestinations: maojinpeng
  SeigeC: chengyuhan
  xiaomifeng007: lusenqi
  skyandong: wangandong
  AeaZer: hewenchang
  YidaZhou: zhouyida01
