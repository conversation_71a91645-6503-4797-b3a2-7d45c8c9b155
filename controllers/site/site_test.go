package site

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler(new(config.Config))
	assert.Equal("site", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "/notifications", "/uploadlog", "/get-ip-detail")
}

func TestAppOnly(t *testing.T) {
	assert := assert.New(t)

	// web
	recorder := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(recorder)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	appOnly(c)
	req := recorder.Result()
	assert.Equal(http.StatusNotFound, req.StatusCode)
	// app
	recorder = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(recorder)
	c.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	c.Request.Header.Set("User-Agent", "MissEvanApp/6.0.7 (iOS;15.6.1;iPhone12,1)")
	appOnly(c)
	req = recorder.Result()
	assert.Equal(http.StatusOK, req.StatusCode)
}
