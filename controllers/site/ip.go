package site

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
)

type getIPDetailResp struct {
	IP         string `json:"ip"`
	IPLocation string `json:"ip_location"`
	ISP        string `json:"isp"`
}

// ActionGetIPDetail 获取用户 IP 信息接口
/**
 * @api {get} /x/site/get-ip-detail 获取用户 IP 信息接口
 *
 * @apiVersion 0.1.0
 * @apiName get-ip-detail
 * @apiGroup site
 *
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "ip": "**************",
 *         "ip_location": "北京",
 *         "isp": "电信"
 *       }
 *     }
 */
func ActionGetIPDetail(c *handler.Context) (handler.ActionResponse, error) {
	resp := &getIPDetailResp{
		IP:         c.ClientIP(),
		IPLocation: "未知",
		ISP:        "未知",
	}
	info, err := goclient.GetIPInfo(c, c.ClientIP(), goclient.GetIPOptions{Lang: goclient.LangZHCN})
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if info.IsEmpty() {
		return resp, nil
	}
	// country code 为空代表本地地址、保留地址、局域网等情况
	if info.CountryCode != "" {
		var ipLocation string
		if info.CountryName == "中国" {
			// 境内（包括港澳台）选择一级行政区名称
			ipLocation = info.RegionName
		} else {
			ipLocation = info.CountryName
		}
		if ipLocation != "" {
			resp.IPLocation = ipLocation
		}
	}
	if info.ISP != "" {
		resp.ISP = info.ISP
	}
	return resp, nil
}
