package site

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionGetIPDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var ipInfo goclient.IPInfo
	var rpcErr error
	cancel := mrpc.SetMock("go://util/geoip", func(i interface{}) (interface{}, error) {
		return ipInfo, rpcErr
	})
	defer cancel()

	testIP := "**************"
	newC := func() *handler.Context {
		c := handler.NewTestContext(http.MethodGet, "/get-ip-detail", false, nil)
		c.SetClientIP(testIP)
		return c
	}
	// rpc 报错情况
	rpcErr = errors.New("test")
	_, err := ActionGetIPDetail(newC())
	assert.Error(err)

	// 空 ip 的情况
	rpcErr = nil
	r, err := ActionGetIPDetail(newC())
	require.NoError(err)
	var resp *getIPDetailResp
	require.IsType(resp, r)
	resp = r.(*getIPDetailResp)
	assert.Equal(&getIPDetailResp{
		IP:         testIP,
		IPLocation: "未知",
		ISP:        "未知",
	}, resp)

	// 国内情况
	ipInfo = goclient.IPInfo{
		CityName:    "上海",
		RegionName:  "上海",
		CountryCode: "CN",
		CountryName: "中国",
		ISP:         "电信",
	}
	r, err = ActionGetIPDetail(newC())
	require.NoError(err)
	resp = r.(*getIPDetailResp)
	assert.Equal(&getIPDetailResp{
		IP:         testIP,
		IPLocation: "上海",
		ISP:        "电信",
	}, resp)

	// 国外情况
	ipInfo = goclient.IPInfo{
		CityName:    "华盛顿",
		RegionName:  "华盛顿",
		CountryCode: "US",
		CountryName: "美国",
	}
	r, err = ActionGetIPDetail(newC())
	require.NoError(err)
	resp = r.(*getIPDetailResp)
	assert.Equal(&getIPDetailResp{
		IP:         testIP,
		IPLocation: "美国",
		ISP:        "未知",
	}, resp)
}
