package site

import (
	"net/url"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/applog"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// UploadLogRequest 日志上报的请求
type UploadLogRequest struct {
	Type   int64  `form:"type"`
	TaskID int64  `form:"task_id"`
	LogURL string `form:"log_url"`
}

// ActionUploadLog 客户端上报日志
/**
 * @api {post} /x/site/uploadlog 客户端上报日志
 *
 * @apiVersion 0.1.0
 * @apiName uploadlog
 * @apiGroup site
 *
 * @apiParam {Number} type 上报类型，1：反馈时发送日志，2：主动拉取上报
 * @apiParam {Number} [task_id] 上传日志的任务 id, 上报类型不是主动拉取时此参数为空
 * @apiParam {String} log_url 日志文件的下载地址，日志文件是存储在B站的
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "type": 1,
 *       "task_id": 24,
 *       "log_url": "http://test.com/log"
 *     }
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "保存成功"
 *     }
 */
func ActionUploadLog(c *handler.Context) (handler.ActionResponse, error) {
	var req UploadLogRequest
	err := c.Bind(&req)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if (req.Type != applog.TypeFeedback && req.Type != applog.TypeReport) || req.LogURL == "" {
		return nil, actionerrors.ErrParams
	}

	// WORKAROUND: 新版本需要额外确认 TaskID 参数正确
	equip := c.Equip()
	v := util.AppVersions{
		IOS:     "4.8.1",
		Android: "5.6.9",
	}
	if !equip.IsOldApp(v) && req.TaskID == 0 && req.Type == applog.TypeReport {
		return nil, actionerrors.ErrParams
	}
	userID := c.UserID()
	buvid := c.BUVID()
	if userID == 0 && buvid == "" {
		return nil, actionerrors.ErrParams
	}

	parsedURL, err := url.Parse(req.LogURL)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	log := applog.AppLog{
		UserID:     userID,
		BUVID:      buvid,
		OS:         c.Equip().OS,
		Version:    c.Equip().AppVersion,
		UserAgent:  c.UserAgent(),
		UploadType: req.Type,
		LogURL:     parsedURL.String(),
		TaskID:     req.TaskID,
	}
	if err = service.LogDB.Create(&log).Error; err != nil {
		return err, actionerrors.ErrServerInternal(err, nil)
	}

	userIDLogKey := serviceredis.KeyAppLogLaserUserID0.Format()
	buvidLogKey := serviceredis.KeyAppLogLaserBUVID0.Format()
	pipe := service.Redis.Pipeline()
	if userID != 0 {
		pipe.HDel(userIDLogKey, strconv.FormatInt(userID, 10))
	}
	if buvid != "" {
		pipe.HDel(buvidLogKey, buvid)
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Errorf("redis hdel error: %v", err)
		// PASS
	}
	return "保存成功", nil
}
