package site

import (
	"encoding/json"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/applog"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestActionUploadLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var req UploadLogRequest

	// 请求数据为空
	c := handler.NewTestContext("POST", "/uploadlog", true, nil)
	_, err := ActionUploadLog(c)
	require.Equal(actionerrors.ErrParams, err)

	// 请求数据 Type 错误
	req.Type = 0
	c = handler.NewTestContext("POST", "/uploadlog", true, req)
	_, err = ActionUploadLog(c)
	require.Equal(actionerrors.ErrParams, err)

	// 请求数据 LogURL 错误
	req.Type = 2
	req.LogURL = ""
	c = handler.NewTestContext("POST", "/uploadlog", true, req)
	_, err = ActionUploadLog(c)
	require.Equal(actionerrors.ErrParams, err)

	// 新版本请求数据 TaskID 错误
	req.LogURL = "http://test/log"
	req.TaskID = 0
	c = handler.NewTestContext("POST", "/uploadlog", true, req)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.8.1 (iOS;12.0.1;iPhone7,2)")
	_, err = ActionUploadLog(c)
	require.Equal(actionerrors.ErrParams, err)
	req.Type = 1
	c = handler.NewTestContext("POST", "/uploadlog", true, req)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.8.1 (iOS;12.0.1;iPhone7,2)")
	_, err = ActionUploadLog(c)
	require.NoError(err)

	// 准备好参数无误的 POST 请求
	testBUVID := "test-buvid-id"
	req.TaskID = 24
	c = handler.NewTestContext("POST", "/uploadlog", true, req)
	c.C.Request.AddCookie(&http.Cookie{Name: "buvid", Value: testBUVID})
	userID := c.UserID()

	// 准备好 redis 上的数据
	var testTask uploadLogTask
	testTask.TaskID = req.TaskID
	testTask.BUVID = testBUVID
	testTask.UserID = userID
	val, err := json.Marshal(testTask)
	require.NoError(err)

	// 在 redis 上植入好数据
	userIDLogKey := serviceredis.KeyAppLogLaserUserID0.Format()
	buvidLogKey := serviceredis.KeyAppLogLaserBUVID0.Format()
	require.NoError(service.Redis.HSet(userIDLogKey, strconv.FormatInt(userID, 10), val).Err())
	require.NoError(service.Redis.HSet(buvidLogKey, testBUVID, val).Err())

	// 上传成功
	resp, err := ActionUploadLog(c)
	assert.Equal("保存成功", resp)
	assert.NoError(err)

	var appl applog.AppLog
	err = service.LogDB.Table(applog.AppLog{}.TableName()).
		Where("user_id = ? AND buvid = ? AND log_url = ?", userID, testBUVID, req.LogURL).Take(&appl).Error
	require.NoError(err)
	assert.Equal(req.TaskID, appl.TaskID)

	userIDIsMember, err := service.Redis.HExists(userIDLogKey, strconv.FormatInt(userID, 10)).Result()
	require.NoError(err)
	assert.False(userIDIsMember)

	buvidIsMember, err := service.Redis.HExists(buvidLogKey, testBUVID).Result()
	require.NoError(err)
	assert.False(buvidIsMember)
}
