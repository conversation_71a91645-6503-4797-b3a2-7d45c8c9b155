package site

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name:        "site",
		Middlewares: gin.HandlersChain{appOnly},
		Actions: map[string]*handler.Action{
			"/notifications": handler.NewAction(handler.GET, ActionNotifications, false),
			"/uploadlog":     handler.NewAction(handler.POST, ActionUploadLog, false),
			"/get-ip-detail": handler.NewAction(handler.GET, ActionGetIPDetail, false),
		},
	}
}

func appOnly(c *gin.Context) {
	e := util.NewEquipment(c.Request.UserAgent())
	if !e.FromApp {
		// 保持和 gin 返回值一致
		c.String(http.StatusNotFound, "404 page not found")
		c.Abort()
		return
	}
	c.Next()
}
