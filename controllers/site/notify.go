package site

import (
	"encoding/json"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	notificationsActiveOff   = iota // app 前台切换至后台
	notificationsActiveOn           // app 启动后台切换至前台
	notificationsActiveLogin        // 登录账号
)

// uploadLog 作为 ActionNotifications 里 uploadlog 的返回内容
type uploadLog struct {
	TaskID  int64  `json:"task_id"`
	LogDate string `json:"log_date"`
}

type notificationsResp struct {
	UploadLog *uploadLog                           `json:"uploadlog"`
	Game      *userapi.GamecenterNotificationsResp `json:"game,omitempty"`
}

// ActionNotifications 服务端告知客户端是否需要上报用户日志
/**
 * @api {get} /x/site/notifications 服务端告知客户端是否需要上报用户日志、自动下载游戏等操作
 *
 * @apiVersion 0.1.0
 * @apiName notifications
 * @apiGroup site
 *
 * @apiParam {Number=0,1,2} [active=0] 请求时机，0：前台切换至后台，1：app 启动、后台切换至前台，2：登录账号
 *
 * @apiSuccessExample {json} 需要客户端进行所有额外操作的情况:
 *     {
 *       "code": 0,
 *       "info": {
 *         "uploadlog": { // 需要上报日志，上传完成后请求 /v2/site/uploadlog 接口，字段返回了就需要客户端上传日志（与请求参数无关）
 *           "task_id": 24, // 上传日志的任务 id
 *           "log_date": "2020-07-19" // 筛选日志的日期，不筛选（上报所有日志）时是空字符串
 *         },
 *         "game": { // 需要自动下载的游戏，active 为 0 时总不会有此字段
 *           "data": [
 *             {
 *               "id": 1,
 *               "url": "https://www.uat.missevan.com/blackboard/activity-FRIHSJoNJa.html",
 *               "icon": "https://static-test.maoercdn.com/game/images/icon.jpg",
 *               "name": "开启自动下载的游戏",
 *               "tag": "二次元,养成",
 *               "intro": "简介",
 *               "download_url": "https://www.missevan.com/x/gamecenter/download?game_id=1",
 *               "package_name": "com.missevan.app"
 *             },
 *             {
 *               "id": 2,
 *               "url": "https://www.uat.missevan.com/blackboard/activity-FRIHSJoNJa.html",
 *               "icon": "https://static-test.maoercdn.com/game/images/icon.jpg",
 *               "name": "开启自动下载的游戏 2",
 *               "tag": "二次元,养成",
 *               "intro": "简介",
 *               "download_url": "https://www.missevan.com/x/gamecenter/download?game_id=2",
 *               "package_name": "com.missevan.app"
 *             }
 *           ]
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 不需要客户端进行额外操作的情况:
 *     {
 *       "code": 0,
 *       "info": { // 不需要游戏自动下载，不返回 game 字段
 *         "uploadlog": null // 不需要上报日志
 *       }
 *     }
 */
func ActionNotifications(c *handler.Context) (handler.ActionResponse, error) {
	active, _ := c.GetParamInt("active")
	switch active {
	case notificationsActiveOff, notificationsActiveOn, notificationsActiveLogin:
	default:
		active = notificationsActiveOff
	}

	var resp notificationsResp
	equip := c.Equip()
	var err error
	data, err := findUploadLogTask(c.UserID(), c.BUVID(), equip.OS)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if data != nil {
		resp.UploadLog = &uploadLog{
			TaskID:  data.TaskID,
			LogDate: data.LogDate,
		}
	}

	userID := c.UserID()
	// WORKAROUND: iOS 4.9.7, android 5.8.0 之后返回 game 字段
	v := util.AppVersions{IOS: "4.9.7", Android: "5.8.0"}
	if active == notificationsActiveOff || userID == 0 || equip.IsOldApp(v) {
		return resp, nil
	}
	resp.Game, err = userapi.GamecenterNotifications(c.UserContext(), userID, equip.OS)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if resp.Game != nil && len(resp.Game.Data) == 0 {
		// 没有自动下载的游戏，game 字段改成空
		resp.Game = nil
	}
	return resp, nil
}

// uploadLogTask 用 HGET 获取的 redis 数据类型
type uploadLogTask struct {
	TaskID  int64  `json:"task_id"`
	BUVID   string `json:"buvid,omitempty"`
	UserID  int64  `json:"user_id,omitempty"`
	MobiApp string `json:"mobi_app"`
	LogDate string `json:"log_date,omitempty"`
}

func findUploadLogTask(userID int64, buvid string, os util.Platform) (*uploadLogTask, error) {
	key := serviceredis.KeyAppLogLaserUserID0.Format()
	var jsonData []byte
	var err error
	if userID != 0 {
		jsonData, err = service.Redis.HGet(key, strconv.FormatInt(userID, 10)).Bytes()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				return nil, err
			}
		}
	}
	if len(jsonData) == 0 {
		key = serviceredis.KeyAppLogLaserBUVID0.Format()
		jsonData, err = service.Redis.HGet(key, buvid).Bytes()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				return nil, err
			}
		}
	}
	if len(jsonData) == 0 {
		return nil, nil
	}
	var data uploadLogTask
	err = json.Unmarshal(jsonData, &data)
	if err != nil {
		logger.Errorf("notification json unmarshal error: %v", err)
		return nil, err
	}
	bothiOS := os == util.IOS && data.MobiApp == common.MobiAppIOS
	bothAndroid := os == util.Android && data.MobiApp == common.MobiAppAndroid
	bothHarmonyOS := os == util.HarmonyOS && data.MobiApp == common.MobiAppHarmonyOS
	if bothAndroid || bothiOS || bothHarmonyOS {
		// PASS
		return &data, nil
	}
	return nil, nil
}
