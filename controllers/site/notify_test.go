package site

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionNotifications(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var called bool
	cancel := mrpc.SetMock(userapi.URIGamecenterNotifications, func(input interface{}) (output interface{}, err error) {
		called = true
		return userapi.GamecenterNotificationsResp{
			Data: []interface{}{},
		}, nil
	})
	defer cancel()

	data := uploadLogTask{
		TaskID:  20020,
		UserID:  10010,
		MobiApp: common.MobiAppIOS,
		LogDate: "2017-06-01",
	}
	val, err := json.Marshal(data)
	require.NoError(err)

	key := serviceredis.KeyAppLogLaserUserID0.Format()
	require.NoError(service.Redis.HSet(key, data.UserID, val).Err())

	c := handler.NewTestContext("GET", "/notifications", true, nil)
	runActionNotifications := func() interface{} {
		resp, err := ActionNotifications(c)
		require.NoError(err)
		return resp
	}

	// 测试 iOS
	called = false
	c = handler.NewTestContext("GET", "/notifications?active=1", true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.9.7 (iOS;12.0.1;iPhone7,2)")
	c.User().ID = data.UserID
	resp, ok := runActionNotifications().(notificationsResp)
	require.True(ok)
	assert.Equal(data.TaskID, resp.UploadLog.TaskID)
	assert.Equal(data.LogDate, resp.UploadLog.LogDate)
	assert.True(called)
	assert.Nil(resp.Game)

	// 设备不匹配
	c = handler.NewTestContext("GET", "/notifications", true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.6.9 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	c.User().ID = data.UserID
	resp = runActionNotifications().(notificationsResp)
	assert.Nil(resp.UploadLog)

	data.MobiApp = common.MobiAppAndroid
	val, err = json.Marshal(data)
	require.NoError(err)
	require.NoError(service.Redis.HSet(key, data.UserID, val).Err())

	// 测试安卓
	called = false
	c = handler.NewTestContext("GET", "/notifications?active=1", true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.8.0 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	c.User().ID = data.UserID
	resp = runActionNotifications().(notificationsResp)
	require.True(ok)
	assert.Equal(data.TaskID, resp.UploadLog.TaskID)
	assert.Equal(data.LogDate, resp.UploadLog.LogDate)
	assert.True(called)
	assert.Nil(resp.Game)

	// 测试在 redis 中未找到
	c = handler.NewTestContext("GET", "/notifications", true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.6.9 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	c.User().ID = -1
	resp = runActionNotifications().(notificationsResp)
	assert.Nil(resp.UploadLog)
}

func TestFindUploadLogTask(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	data := uploadLogTask{
		TaskID:  20020,
		UserID:  10010,
		BUVID:   "tempBUVID",
		MobiApp: common.MobiAppIOS,
		LogDate: "2017-06-01",
	}

	val, err := json.Marshal(data)
	require.NoError(err)

	// 设置 redis 内的数据
	ukey := serviceredis.KeyAppLogLaserUserID0.Format()
	require.NoError(service.Redis.HSet(ukey, data.UserID, val).Err())
	bkey := serviceredis.KeyAppLogLaserBUVID0.Format()
	require.NoError(service.Redis.HSet(bkey, data.BUVID, val).Err())

	// 通过 userID 查找
	os := util.IOS
	res, err := findUploadLogTask(data.UserID, "", os)
	require.NoError(err)
	assert.Equal(data.TaskID, res.TaskID)
	assert.Equal(data.LogDate, res.LogDate)

	// 通过 buvid 查找
	res, err = findUploadLogTask(-1, data.BUVID, os)
	require.NoError(err)
	assert.Equal(data.TaskID, res.TaskID)
	assert.Equal(data.LogDate, res.LogDate)

	// 失败的查找
	res, err = findUploadLogTask(-1, "", os)
	require.NoError(err)
	assert.Nil(res)

	// 测试 iOS 客户端与设备不匹配
	os = util.Android
	res, err = findUploadLogTask(-1, data.BUVID, os)
	require.NoError(err)
	assert.Nil(res)

	data.MobiApp = common.MobiAppAndroid
	val, err = json.Marshal(data)
	require.NoError(err)
	require.NoError(service.Redis.HSet(ukey, data.UserID, val).Err())

	// 测试安卓客户端与设备不匹配
	os = util.IOS
	res, err = findUploadLogTask(data.UserID, "", os)
	require.NoError(err)
	assert.Nil(res)
}
