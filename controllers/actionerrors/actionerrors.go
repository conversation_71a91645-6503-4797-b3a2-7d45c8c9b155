package actionerrors

import (
	"html"
	"net/http"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// common errors
var (
	ErrParams = ErrBadRequest(handler.CodeInvalidParam, "参数错误")

	ErrSoundNotFound        = ErrNotFound(handler.CodeSoundNotFound, "该音频不存在")
	ErrAlbumNotFound        = ErrNotFound(handler.CodeAlbumNotFound, "该音单不存在")
	ErrEventNotFound        = ErrNotFound(handler.CodeEventNotFound, "该活动不存在")
	ErrTopicNotFound        = ErrNotFound(handler.CodeUnknownError, "该专题不存在")
	ErrTagNotFound          = ErrNotFound(handler.CodeTagNotFound, "该频道不存在")
	ErrCardNotFound         = ErrNotFound(handler.CodeCardNotFound, "该语音不存在")
	ErrCommentNotFound      = ErrNotFound(handler.CodeCommentNotFound, "该评论不存在")
	ErrReplyCommentNotFound = ErrNotFound(handler.CodeCommentNotFound, "回复的评论不存在")
	ErrDanmakuNotFound      = ErrNotFound(handler.CodeDanmakuNotFound, "该弹幕不存在")
	ErrDramaNotFound        = ErrNotFound(handler.CodeDramaNotFound, "该剧集不存在")
	ErrDramalistNotFound    = ErrNotFound(handler.CodeDramalistNotFound, "剧单不存在")
	ErrUserNotFound         = ErrNotFound(handler.CodeUserNotFound, "用户不存在")

	ErrAvatarFrameNotFound = ErrNotFound(handler.CodeAvatarFrameNotFound, "头像框不存在")
	ErrAvatarFrameNotOwned = ErrBadRequest(handler.CodeAvatarFrameNotOwned, "未获得该头像框")
	ErrAvatarFrameExpired  = ErrBadRequest(handler.CodeAvatarFrameExpired, "头像框已过期")
	ErrBadgeNotFound       = ErrNotFound(handler.CodeBadgeNotFound, "称号不存在")
	ErrThemeSkinNotFound   = ErrNotFound(handler.CodeThemeSkinNotFound, "主题皮肤不存在")

	ErrContentExpired         = ErrForbidden(handler.CodeUnknownError, "当前内容暂不能进行此操作")
	ErrCanNotSendDanmaku      = ErrForbidden(handler.CodeUnknownError, "暂不可发送弹幕")
	ErrNeedPurchaseToComment  = ErrForbidden(handler.CodeUnknownError, "您未购买暂时不能评论")
	ErrEmptyComment           = ErrBadRequest(handler.CodeFailedToComment, "评论不能为空~")
	ErrNeedGetCard            = ErrBadRequest(handler.CodeUnknownError, "未获得的语音不能评论哦")
	ErrNoPermissionDelDanmaku = ErrForbidden(handler.CodeUnknownError, "你不是弹幕或者音频拥有者，无权限操作")

	ErrSendMessageUserCountLimit = ErrForbidden(handler.CodeSendMessageUserCountLimit, "发送私信人数达到上限")
	ErrSendMsgNotReplyLimit      = ErrForbidden(handler.CodeSendMessageUserCountLimit, "对方关注或回复你之前，仅能发送 1 条消息")
	ErrSendMsgNotFollowingLimit  = ErrForbidden(handler.CodeSendMessageUserCountLimit, "一天最多可以和 10 个未关注自己的人打招呼哦~")
	ErrSendMessageLimit          = ErrForbidden(handler.CodeSendMessageLimit, "M娘来不及传递私信啦~ 回血中 >-<")
	ErrDownloadLimit             = ErrForbidden(handler.CodeUnknownError, "未开放下载")

	ErrCommentRepeat    = ErrBadRequest(handler.CodeInvalidParam, "重复评论，请勿刷屏")
	ErrCommentFrequency = ErrBadRequest(handler.CodeUserLimit, "发太快啦，休息一下嘛 (・∀・)")
	ErrCommentViolation = ErrForbidden(handler.CodeFailedToComment, "评论内容含有违规信息")

	ErrNotEnoughPoint = ErrForbidden(handler.CodeNotEnoughPoint, "没有小鱼干可以投食了 T_T")

	ErrCollectDramalistLimit = ErrBadRequest(handler.CodeCollectDramalistLimit, "不能收藏自己的剧单")

	ErrUserDeleted = ErrForbidden(handler.CodeUserDeleted, "该用户已注销")

	ErrCaptchaVerificationFailure = ErrForbidden(handler.CodeCaptchaVerificationFailure, "人机校验失败")

	ErrDramaReviewUserNoMobile = ErrForbidden(handler.CodeDramaReviewUserNoMobile, "请联系工作人员进行绑定")
	ErrDramaReviewUserNoAuth   = ErrForbidden(handler.CodeDramaReviewUserNoAuth, "用户访问剧集收益后台未认证")
	ErrDramaReviewForbidden    = ErrForbidden(handler.CodeDramaReviewForbidden, "很抱歉，您没有权限访问此页面")
)

var (
	errServerInternal = handler.NewLoggerErrorWithSkip(500, handler.CodeUnknownError, "服务器内部错误", 3)
)

// ErrServerInternal 服务器内部错误
func ErrServerInternal(err error, fields logger.Fields) error {
	return errServerInternal.New(err, fields)
}

// ErrBadRequest returns a new bad request error with customized description
func ErrBadRequest(code int, description string) error {
	return handler.NewActionError(
		http.StatusBadRequest, code,
		description,
	)
}

// ErrForbidden returns a new forbidden error with customized description
func ErrForbidden(code int, description string) error {
	return handler.NewActionError(
		http.StatusForbidden, code,
		description,
	)
}

// ErrNotFound returns a new 404 not found error with customized description
func ErrNotFound(code int, description string) error {
	return handler.NewActionError(
		http.StatusNotFound, code,
		description,
	)
}

// ErrGlobalPopupPrompt error msg of global popup error prompt
func ErrGlobalPopupPrompt(msg string) error {
	return handler.NewActionError(http.StatusBadRequest, handler.CodeGlobalPopupPrompt, msg)
}

// ErrParamMsg error param msg
func ErrParamMsg(msg string) error {
	return ErrBadRequest(handler.CodeInvalidParam, msg)
}

// ErrConfirmRequired 需要弹窗验证，msg 默认会自动 html.EscapeString
func ErrConfirmRequired(msg string, confirm interface{}, disableHTMLEscape ...bool) *handler.ActionError {
	if len(disableHTMLEscape) == 0 || !disableHTMLEscape[0] {
		msg = html.EscapeString(msg)
	}
	return handler.NewActionErrorWithInfo(428, 100010020, "", handler.M{"confirm": confirm, "msg": msg})
}
