package actionerrors

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestErrGlobalPopupPrompt(t *testing.T) {
	assert := assert.New(t)
	err := ErrGlobalPopupPrompt("测试 msg")
	assert.Error(err)
	actionError := err.(*handler.ActionError)
	assert.Equal(100010018, actionError.Code)
	assert.Equal(400, actionError.Status)
	assert.Equal("测试 msg", actionError.Message)
}

func TestLoggerError(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err1 := errors.New("test")
	err2 := ErrServerInternal(err1, nil)
	assert.Error(err2)
	assert.NotEqual(err1, err2)

	var e *handler.LoggerError
	require.IsType(e, err2)
	e = err2.(*handler.LoggerError)
	e.Log(logger.DebugLevel, "actionerrors")
}

func TestErrConfirmRequired(t *testing.T) {
	assert := assert.New(t)

	msg := "msg"
	confirm := 1
	excepted := handler.NewActionErrorWithInfo(428, 100010020, "", handler.M{"confirm": confirm, "msg": msg})
	assert.Equal(excepted, ErrConfirmRequired(msg, confirm))

	msg = "<>"
	excepted = handler.NewActionErrorWithInfo(428, 100010020, "", handler.M{"confirm": confirm, "msg": "&lt;&gt;"})
	assert.Equal(excepted, ErrConfirmRequired(msg, confirm))
	assert.Equal(excepted, ErrConfirmRequired(msg, confirm, false))
	excepted = handler.NewActionErrorWithInfo(428, 100010020, "", handler.M{"confirm": confirm, "msg": "<>"})
	assert.Equal(excepted, ErrConfirmRequired(msg, confirm, true))
}
