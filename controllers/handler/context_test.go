package handler

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestContextUserAgent(t *testing.T) {
	c := CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "", nil)
	assert.Equal(t, "", c.UserAgent())
	c.C.Request.Header.Set("User-Agent", "test-user-agent")
	assert.Equal(t, "test-user-agent", c.UserAgent())
}

func TestContextClientIP(t *testing.T) {
	assert := assert.New(t)

	c := NewTestContext("GET", "", false, nil)
	assert.Empty(c.ClientIP())

	c.SetClientIP("***********")
	assert.Equal("***********", c.ClientIP())
}

func TestContextRequest(t *testing.T) {
	assert := assert.New(t)
	c := NewTestContext(http.MethodGet, "/health", false, nil)
	req := c.Request()
	assert.NotNil(req)
	assert.Equal("/health", req.URL.Path)
	assert.Equal("GET", req.Method)
}

func TestContextEquip(t *testing.T) {
	assert := assert.New(t)

	// MissEvanApp/5.1.9 (Android;6.0.1;OPPO OPPO R9sk R9sk)
	expected := &util.Equipment{
		EquipID:     "5ae93485-454a-b665-9ada-a26e4dda48ae",
		BUVID:       "Z342EB37EED43E82411D878802220C5F6952",
		AppVersion:  "5.1.9",
		OS:          util.Android,
		OSVersion:   "6.0.1",
		DeviceModel: "OPPO OPPO R9sk R9sk",
		FromApp:     true,
	}

	r := httptest.NewRequest(http.MethodGet, "/path", nil)
	ua := "MissEvanApp/" + expected.AppVersion + " (Android;" + expected.OSVersion + ";" + expected.DeviceModel + ")"
	r.Header.Add("User-Agent", ua)
	r.AddCookie(&http.Cookie{Name: "equip_id", Value: expected.EquipID})
	r.AddCookie(&http.Cookie{Name: "buvid", Value: expected.BUVID})

	c := CreateTestContext(false)
	c.C.Request = r
	actual := c.Equip()
	assert.Equal(expected, actual)
	assert.NoError(c.GetEquipError())
}

func TestContextUser(t *testing.T) {
	assert := assert.New(t)

	enableSessionUser := true
	cancel := mrpc.SetMock("sso://session", func(i interface{}) (interface{}, error) {
		if !enableSessionUser {
			// Token 过期或不存在
			return nil, &sso.ClientError{Code: 300030002}
		}
		nowUnix := time.Now().Unix()
		return sso.ClientResponse{
			LoginAt:  nowUnix,
			ExpireAt: nowUnix + 3600,
			Token:    "token1",
			User: sso.User{
				UserID:   1,
				Username: "user1",
			},
		}, nil
	})
	defer cancel()

	tokenKey := keys.KeyUserTokenGo1.Format("token1")
	err := service.LRURedis.Del(tokenKey).Err()
	assert.NoError(err)

	c := CreateTestContext(false)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/path", nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "token",
		Value: "token1",
	})
	user.Middleware()(c.C)

	u := c.User()
	assert.NotNil(u)
	assert.Equal(int64(1), u.ID)
	assert.NoError(c.GetUserError())

	// 测试从缓存中读取
	c = CreateTestContext(false)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/path", nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "token",
		Value: "token1",
	})
	user.Middleware()(c.C)

	u = c.User()
	assert.NotNil(u)
	assert.Equal(int64(1), u.ID)
	assert.NoError(c.GetUserError())

	// 测试 token 过期或不存在
	enableSessionUser = false
	c = CreateTestContext(false)
	c.C.Request = httptest.NewRequest(http.MethodGet, "/path", nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "token",
		Value: "tokenNotExist",
	})
	user.Middleware()(c.C)

	u = c.User()
	assert.Nil(u)
	assert.NoError(c.GetUserError())
}

func TestContextBind(t *testing.T) {
	c := CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/adminpass",
		strings.NewReader(`{"test":true}`))
	c.C.Request.Header.Add("Content-Type", "application/json")
	var v M
	err := c.Bind(&v)
	assert.NoError(t, err)
	assert.True(t, v["test"].(bool))

	c = CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/api/v2/guild/adminpass",
		strings.NewReader(`{"test":true}`))
	err = c.BindJSON(&v)
	assert.NoError(t, err)
	assert.True(t, v["test"].(bool))
}

func TestQueryParam(t *testing.T) {
	assert := assert.New(t)

	ctx := CreateTestContext(true)
	ctx.C.Request, _ = http.NewRequest("POST", "?name=%20abc%20&push=1", nil)
	// 普通的 Param
	name, ok := ctx.GetParam("name")
	assert.True(ok)
	assert.Equal(" abc ", name)
	name = ctx.GetDefaultParam("empty", "empty")
	assert.Equal("empty", name)
	name = ctx.GetDefaultParam("name", "123")
	assert.Equal(" abc ", name)
	// string
	name, ok = ctx.GetParamString("name")
	assert.True(ok)
	assert.Equal("abc", name)
	_, ok = ctx.GetParamString("empty")
	assert.False(ok)
	name = ctx.GetDefaultParamString("empty", "empty")
	assert.Equal("empty", name)
	name = ctx.GetDefaultParamString("name", "123")
	assert.Equal("abc", name)
	// int
	push, err := ctx.GetParamInt("push")
	assert.NoError(err)
	assert.Equal(1, push)
	_, err = ctx.GetParamInt("empty")
	assert.Equal(ErrEmptyValue, err)
	push, err = ctx.GetDefaultParamInt("empty", -10)
	assert.NoError(err)
	assert.Equal(-10, push)
	push, err = ctx.GetDefaultParamInt("push", -10)
	assert.NoError(err)
	assert.Equal(1, push)
	// int64
	push64, err := ctx.GetParamInt64("push")
	assert.NoError(err)
	assert.Equal(int64(1), push64)
	push64, err = ctx.GetParamInt64("push")
	assert.NoError(err)
	assert.Equal(int64(1), push64)
	_, err = ctx.GetParamInt64("aaa")
	assert.Equal(ErrEmptyValue, err)
	push64, err = ctx.GetDefaultParamInt64("empty", -10)
	assert.NoError(err)
	assert.Equal(int64(-10), push64)
	push64, err = ctx.GetDefaultParamInt64("push", -10)
	assert.NoError(err)
	assert.Equal(int64(1), push64)
}

func TestGetParamInt64ArrayFromString(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewTestContext(http.MethodGet, "", false, nil)
	_, err := c.GetParamInt64ArrayFromString("key")
	assert.Equal(ErrEmptyParam, err)

	c = NewTestContext(http.MethodGet, "?key=", false, nil)
	_, err = c.GetParamInt64ArrayFromString("key", ",")
	assert.Equal(ErrInvalidParam, err)

	c = NewTestContext(http.MethodGet, "?key=1-2-3", false, nil)
	value, err := c.GetParamInt64ArrayFromString("key", "-")
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, value, "")

	c = NewTestContext(http.MethodGet, "?key=1,2,3", false, nil)
	value, err = c.GetParamInt64ArrayFromString("key")
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, value, "default sep")

	c = NewTestContext(http.MethodGet, "?key=1,2,3", false, nil)
	assert.Panics(func() { _, _ = c.GetParamInt64ArrayFromString("key", ",", "-") }, "invalid sep param")
}

func TestQueryDate(t *testing.T) {
	assert := assert.New(t)

	testCase := []struct {
		query            string
		defaultStartDate string
		defaultEndDate   string
		startDate        time.Time
		endDate          time.Time
		err              bool
	}{
		{
			query:            "start_date=2019-06-20",
			defaultStartDate: "2019-06-20",
			defaultEndDate:   "2019-07-01",
			startDate:        time.Date(2019, 6, 20, 0, 0, 0, 0, time.Local),
			endDate:          time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		},
		{
			query:            "end_date=2019-07-01",
			defaultStartDate: "2019-06-20",
			defaultEndDate:   "2019-07-01",
			startDate:        time.Date(2019, 6, 20, 0, 0, 0, 0, time.Local),
			endDate:          time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		},
		{
			query:            "start_date=2019-06-20&end_date=2019-07-01",
			defaultStartDate: "",
			defaultEndDate:   "",
			startDate:        time.Date(2019, 6, 20, 0, 0, 0, 0, time.Local),
			endDate:          time.Date(2019, 7, 1, 0, 0, 0, 0, time.Local),
		},
		{
			query:            "end_date=2019-06-01",
			defaultStartDate: "",
			defaultEndDate:   "2019-06-01",
			err:              true,
		},
		{
			query:            "start_date=2019-06-20",
			defaultStartDate: "2019-06-01",
			defaultEndDate:   "",
			err:              true,
		},
		{
			query:            "start_date=2019-06-20&end_date=2019-06-01",
			defaultStartDate: "",
			defaultEndDate:   "",
			err:              true,
		},
	}
	for i := 0; i < len(testCase); i++ {
		ctx := CreateTestContext(false)
		ctx.C.Request = httptest.NewRequest("GET", "/path?"+testCase[i].query, nil)
		startDate, endDate, err :=
			ctx.GetParamDateRange(testCase[i].defaultStartDate, testCase[i].defaultEndDate)
		if testCase[i].err {
			assert.Error(err, i)
		} else {
			assert.NoError(err, i)
			assert.Equal(startDate, testCase[i].startDate, i)
			assert.Equal(endDate, testCase[i].endDate, i)
		}
	}
}

func TestGetParamPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewTestContext(http.MethodGet, "?p=10&page_size=100", false, nil)
	p, pageSize, err := c.GetParamPage()
	require.NoError(err)
	assert.Equal(int64(10), p)
	assert.Equal(int64(100), pageSize)

	c = NewTestContext(http.MethodGet, "?p=10&pagesize=100", false, nil)
	p, pageSize, err = c.GetParamPage()
	require.NoError(err)
	assert.Equal(int64(10), p)
	assert.Equal(int64(100), pageSize)

	c = NewTestContext(http.MethodGet, "?", false, nil)
	p, pageSize, err = c.GetParamPage(&PageOption{DefaultPageSize: 10})
	require.NoError(err)
	assert.Equal(int64(1), p)
	assert.Equal(int64(10), pageSize, "传参 defaultPageSize")

	c = NewTestContext(http.MethodGet, "?", false, nil)
	_, pageSize, err = c.GetParamPage()
	require.NoError(err)
	assert.Equal(DefaultPageSize, pageSize, "未传参 defaultPageSize")

	c = NewTestContext(http.MethodGet, "?p=aaa&page_size=aaa", false, nil)
	_, _, err = c.GetParamPage()
	assert.Equal(ErrInvalidParam, err, "p 和 page_size 格式错误")

	c = NewTestContext(http.MethodGet, "?p=-10&page_size=1", false, nil)
	_, _, err = c.GetParamPage()
	assert.Equal(ErrInvalidParam, err, "p 小于 0")

	c = NewTestContext(http.MethodGet, "?p=1&page_size=-1", false, nil)
	_, _, err = c.GetParamPage()
	assert.Equal(ErrInvalidParam, err, "page_size 小于 0")

	c = NewTestContext(http.MethodGet, "?p=1&page_size=101", false, nil)
	_, _, err = c.GetParamPage()
	assert.Equal(ErrInvalidParam, err, "page_size 超过最大值")
}

func TestGetParamMarker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试传参格式错误
	c := NewTestContext(http.MethodGet, "?page_size=aaa", false, nil)
	_, _, err := c.GetParamMarker()
	assert.Equal(ErrInvalidParam, err)

	// 测试 page_size 小于 0
	c = NewTestContext(http.MethodGet, "?page_size=-1", false, nil)
	_, _, err = c.GetParamMarker()
	assert.Equal(ErrInvalidParam, err)

	// 测试 page_size 超过最大值
	c = NewTestContext(http.MethodGet, "?page_size=101", false, nil)
	_, _, err = c.GetParamMarker()
	assert.Equal(ErrInvalidParam, err)

	c = NewTestContext(http.MethodGet, "?page_size=31", false, nil)
	_, _, err = c.GetParamMarker(&PageOption{MaxPageSize: 30})
	assert.Equal(ErrInvalidParam, err)

	// 测试传参 defaultPageSize
	c = NewTestContext(http.MethodGet, "?", false, nil)
	_, pageSize, err := c.GetParamMarker(&PageOption{DefaultPageSize: 30})
	require.NoError(err)
	assert.EqualValues(30, pageSize)

	// 测试获取参数
	c = NewTestContext(http.MethodGet, "?marker=1&page_size=100", false, nil)
	marker, pageSize, err := c.GetParamMarker()
	require.NoError(err)
	assert.Equal("1", marker)
	assert.EqualValues(100, pageSize)
}

func TestGetPageSize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	option := &PageOption{DefaultPageSize: 20}
	c := NewTestContext(http.MethodGet, "?", false, nil)
	pageSize, err := getPageSize(c, option)
	require.NoError(err)
	assert.Equal(int64(20), pageSize)

	c = NewTestContext(http.MethodGet, "?page_size=5", false, nil)
	pageSize, err = getPageSize(c, option)
	require.NoError(err)
	assert.Equal(int64(5), pageSize)

	c = NewTestContext(http.MethodGet, "?pagesize=5", false, nil)
	pageSize, err = getPageSize(c, option)
	require.NoError(err)
	assert.Equal(int64(5), pageSize)

	c = NewTestContext(http.MethodGet, "?page_size=5&pagesize=10", false, nil)
	pageSize, err = getPageSize(c, option)
	require.NoError(err)
	assert.Equal(int64(10), pageSize)
}

func TestGetParamMonthRange(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试默认值
	defaultStartDate := "2020-01"
	defaultEndDate := "2020-01"
	c := NewTestContext(http.MethodGet, "?p=10&page_size=100", false, nil)
	startDate, endDate, err := c.GetParamMonthRange(defaultStartDate, defaultEndDate)
	require.NoError(err)
	assert.Equal(startDate.Unix(), int64(1577808000))
	assert.Equal(endDate.Unix(), int64(1577808000))

	// 测试正常返回
	c = NewTestContext(http.MethodGet, "?start_month=2020-02&end_month=2020-03", false, nil)
	startDate, endDate, _ = c.GetParamMonthRange(defaultStartDate, defaultEndDate)
	assert.Equal(startDate.Unix(), int64(1580486400))
	assert.Equal(endDate.Unix(), int64(1582992000))

	// 测试结束时间小于开始时间
	c = NewTestContext(http.MethodGet, "?start_month=2020-02&end_month=2020-01", false, nil)
	_, _, err = c.GetParamMonthRange(defaultStartDate, defaultEndDate)
	assert.Equal(err, ErrInvalidDateRange)
}

func TestGetPageOption(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(&PageOption{DefaultPageSize: DefaultPageSize, MaxPageSize: MaxPageSize},
		getPageOption(), "未传 option")

	assert.Equal(&PageOption{DefaultPageSize: 10, MaxPageSize: MaxPageSize},
		getPageOption(&PageOption{DefaultPageSize: 10}), "未传 MaxPageSize")

	assert.Equal(&PageOption{DefaultPageSize: DefaultPageSize, MaxPageSize: 30},
		getPageOption(&PageOption{MaxPageSize: 30}), "未传 DefaultPageSize")

	assert.Equal(&PageOption{DefaultPageSize: 10, MaxPageSize: 30},
		getPageOption(&PageOption{DefaultPageSize: 10, MaxPageSize: 30}), "传参 MaxPageSize, DefaultPageSize")

	assert.Panics(func() { getPageOption(&PageOption{DefaultPageSize: 10, MaxPageSize: 5}) }, "DefaultPageSize < MaxPageSize")
	assert.Panics(func() { getPageOption(&PageOption{DefaultPageSize: -1, MaxPageSize: 5}) }, "DefaultPageSize < 0")
}

func TestGetParamSearchWord(t *testing.T) {
	assert := assert.New(t)

	c := NewTestContext(http.MethodGet, "/?s=123123&name=大家好", false, nil)
	searchWord, err := c.GetParamSearchWord("s")
	assert.NoError(err)
	assert.True(searchWord.IsInteger)
	assert.Equal(int64(123123), searchWord.WordInteger)

	searchWord, err = c.GetParamSearchWord("empty_val")
	assert.Error(err, ErrEmptyValue.Error())

	searchWord, err = c.GetParamSearchWord("name")
	assert.NoError(err)
	assert.False(searchWord.IsInteger)
	assert.Equal("大家好", searchWord.Word)
}

func TestToken(t *testing.T) {
	assert := assert.New(t)

	c := CreateTestContext(false)
	c.C.Request, _ = http.NewRequest("GET", "", nil)
	assert.Empty(c.Token())
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: "testtoken"})
	assert.Equal("testtoken", c.Token())
}

func TestContext_UserContext(t *testing.T) {
	assert := assert.New(t)

	ctx := NewTestContext(http.MethodGet, "/echo", true, nil)
	ctx.Request().AddCookie(&http.Cookie{Name: "token", Value: "token"})

	uc := ctx.UserContext()
	assert.Equal("token", uc.Token)
}

func TestSendDownload(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := NewTestContext(http.MethodGet, "", false, nil)
	var b bytes.Buffer
	b.Write([]byte("test content"))
	c.SendDownload(&b, int64(b.Len()), "测试文件下载.csv")
	assert.Equal("application/octet-stream", c.C.Writer.Header().Get("Content-Type"))
	assert.Equal(`attachment; filename="%E6%B5%8B%E8%AF%95%E6%96%87%E4%BB%B6%E4%B8%8B%E8%BD%BD.csv"; filename*=UTF-8''%E6%B5%8B%E8%AF%95%E6%96%87%E4%BB%B6%E4%B8%8B%E8%BD%BD.csv`, c.C.Writer.Header().Get("Content-Disposition"))
	assert.Equal(http.StatusOK, c.C.Writer.Status())
	contentLengthStr := c.C.Writer.Header().Get("Content-Length")
	contentLength, err := strconv.Atoi(contentLengthStr)
	require.NoError(err)
	assert.True(contentLength > 0)
}
