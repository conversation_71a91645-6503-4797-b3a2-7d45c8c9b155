package handler

import (
	"errors"
	"net/http"
	"reflect"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// Error Codes
// doc: https://info.missevan.com/pages/viewpage.action?pageId=50727657
const (
	CodeSuccess                    = 0
	CodeErrSignature               = 100010002
	CodeLoginRequired              = 100010006
	CodeUnknownError               = 100010007
	CodeBindMobile                 = 100010008
	CodeCaptchaVerificationFailure = 100010014
	CodeOperateTooFrequently       = 100010016
	CodeGlobalPopupPrompt          = 100010018
	CodeGlobalToast                = 100010022 // 全局 toast 提示错误

	CodeUserNotFound              = 200020001
	CodeCannotOperateSelf         = 200020002
	CodeBannedUser                = 200020004
	CodeUserLimit                 = 200020005
	CodeSendMessageUserCountLimit = 200020006
	CodeSendMessageLimit          = 200020008
	CodeUserFollowLimit           = 200020009 // 用户关注限制错误码
	CodeBlockedUser               = 200020010 // 用户拉黑错误码
	CodeDramaReviewUserNoMobile   = 200020011 // 用户未绑定剧集收益后台认证手机号
	CodeDramaReviewUserNoAuth     = 200020012 // 用户访问剧集收益后台未认证
	CodeBlockedUserByOthers       = 200020013 // 用户被拉黑错误码
	CodeDramaReviewForbidden      = 200020014 // 无权限获取剧集收益信息

	CodeCollectDramalistLimit = 200140003 // 用户收藏剧单限制错误码

	CodeSoundNotFound     = 200110001
	CodeAlbumNotFound     = 200120001
	CodeEventNotFound     = 200410001
	CodeTagNotFound       = 200430001
	CodeCardNotFound      = 200120001
	CodeDanmakuNotFound   = 200710001
	CodeDramaNotFound     = 200130001
	CodeDramalistNotFound = 200140001

	CodeAvatarFrameNotFound = 200160001 // 头像框不存在错误码
	CodeAvatarFrameNotOwned = 200160002 // 用户未获得头像框错误码
	CodeAvatarFrameExpired  = 200160003 // 头像框已过期错误码
	CodeBadgeNotFound       = 200160004 // 称号不存在错误码
	CodeThemeSkinNotFound   = 200160005 // 主题皮肤不存在错误码

	CodeEmptyParam   = 201010001
	CodeInvalidParam = 201010002

	CodeCommentNotFound   = 200310001 // 评论不存在错误码
	CodeFailedToComment   = 200310100 // 评论添加失败错误码
	CodeCommentEmoteLimit = 200310102 // 评论中表情数量超过上限错误码

	CodeInsufficientBalance = 200360006 // 您的余额不足，请充值后购买
	CodeNotEnoughPoint      = 200360104 // 小鱼干不足

	CodeSearchHasForbiddenWord = 200510002

	CodeUserDeleted = 300010005 // 用户已注销错误码
)

// errors
var (
	ErrLoginRequired = NewActionError(http.StatusForbidden, CodeLoginRequired, "请先登录")
	ErrBadRequest    = NewActionError(http.StatusBadRequest, CodeUnknownError, "错误的请求")
	ErrEmptyParam    = NewActionError(http.StatusBadRequest, CodeEmptyParam, "参数不可为空")
	ErrInvalidParam  = NewActionError(http.StatusBadRequest, CodeInvalidParam, "参数不合法")

	ErrEmptyValue = errors.New("empty value")
)

// ErrInvalidDateRange is returned by GetParamDateRange when start_date > end_date
var ErrInvalidDateRange = errors.New("start_date > end_date")

// ErrRawResponse is the special error that should be returned if the result of a action handler is not in the format of basicResponse.
var ErrRawResponse = errors.New("response is managed by action handler")

// ResponseError response error interface
type ResponseError interface {
	// StatusCode 响应状态码
	StatusCode() int
	// ErrorCode 错误代码
	ErrorCode() int
	// ErrorInfo 响应错误信息
	ErrorInfo() interface{}
	// ErrorMessage 响应错误提示
	ErrorMessage() string
	// WithData 设置 data
	WithData(data ActionResponse) ResponseError
	// ErrorData 响应错误信息
	ErrorData() interface{}

	error
}

// ActionError for action error
type ActionError struct {
	Status  int
	Code    int
	Message string
	Info    M // TODO: 调整老接口响应格式使用 Data 字段来处理
	Data    ActionResponse
}

// NewActionError returns a new ActionError
func NewActionError(status int, code int, msg string) *ActionError {
	return &ActionError{
		Status:  status,
		Code:    code,
		Message: msg,
	}
}

// NewActionErrorWithInfo returns a new ActionError with extra info
func NewActionErrorWithInfo(status int, code int, msg string, info M) *ActionError {
	return &ActionError{
		Status:  status,
		Code:    code,
		Message: msg,
		Info:    info,
		Data:    info,
	}
}

// StatusCode status code
func (e *ActionError) StatusCode() int {
	return e.Status
}

// ErrorCode error code
func (e *ActionError) ErrorCode() int {
	return e.Code
}

// ErrorInfo response info
// returns {"msg":"test err", e.Info...}
func (e *ActionError) ErrorInfo() interface{} {
	if e.Info != nil {
		// 客户端固定使用 msg 字段获取提示信息
		if _, ok := e.Info["msg"]; !ok {
			info := make(M, len(e.Info)+1)
			for key, value := range e.Info {
				info[key] = value
			}
			// TODO: 后续相关调用方将 msg 调整至 e.Info 中后，可删除此兼容
			info["msg"] = e.Error()
			return info
		}
		return e.Info
	}
	return e.Error()
}

// ErrorMessage response message
func (e *ActionError) ErrorMessage() string {
	return e.Error()
}

// WithData set response data
func (e *ActionError) WithData(data ActionResponse) ResponseError {
	e.Data = data
	return e
}

// ErrorData response data
func (e *ActionError) ErrorData() interface{} {
	return e.Data
}

func (e *ActionError) Error() string {
	return e.Message
}

// LoggerError action error with logger
type LoggerError struct {
	Status int
	Code   int
	*logger.ContextError
}

// NewLoggerError new LoggerError
func NewLoggerError(status, code int, msg string) *LoggerError {
	return NewLoggerErrorWithSkip(status, code, msg, 2)
}

// NewLoggerErrorWithSkip new LoggerError with custom skip
// skip 是堆栈忽略的层数，直接使用返回的 error 时，skip 为 2 能记录到使用的地方
// 每嵌套一层使用的函数时（比如再次封装函数强制使用 .New 的成员函数），skip 总是需要 +1 才能正确记录到使用 error 的位置
func NewLoggerErrorWithSkip(status, code int, msg string, skip int) *LoggerError {
	err := &LoggerError{
		Status:       status,
		Code:         code,
		ContextError: logger.NewContextError(msg),
	}
	err.SetSkip(skip)
	return err
}

// StatusCode status code
func (e *LoggerError) StatusCode() int {
	return e.Status
}

// ErrorCode error code
func (e *LoggerError) ErrorCode() int {
	return e.Code
}

// ErrorInfo response info
func (e *LoggerError) ErrorInfo() interface{} {
	if Mode() == ReleaseMode {
		return e.Title()
	}
	return e.Error()
}

// ErrorMessage response message
func (e *LoggerError) ErrorMessage() string {
	if Mode() == ReleaseMode {
		return e.Title()
	}
	return e.Error()
}

// WithData set response data
func (e *LoggerError) WithData(data ActionResponse) ResponseError {
	return e
}

// ErrorData response data
func (e *LoggerError) ErrorData() interface{} {
	return nil
}

// New returns a new LoggerError which records the call frame.
func (e *LoggerError) New(err error, fields logger.Fields) *LoggerError {
	err2 := e.ContextError.New(err, fields)
	return &LoggerError{
		Status:       e.Status,
		Code:         e.Code,
		ContextError: err2,
	}
}

type basicResponse struct {
	Code int            `json:"code"`
	Info ActionResponse `json:"info"`
}

// BasicResponseV2 basic response V2
type BasicResponseV2 struct {
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    ActionResponse `json:"data"`
}

// Action structure
type Action struct {
	Method        Method
	Action        ActionFunc
	LoginRequired bool

	handler gin.HandlerFunc
}

// ActionV2 structure
type ActionV2 struct {
	Method            Method
	Action            ActionFunc
	ActionWithMessage ActionFuncWithMessage
	LoginRequired     bool

	handler gin.HandlerFunc
}

// GetHandler for request handler
func (a *Action) GetHandler() gin.HandlerFunc {
	return a.handler
}

// GetHandler for request handler v2
func (a *ActionV2) GetHandler() gin.HandlerFunc {
	return a.handler
}

// NewAction creates new action
func NewAction(method Method, handler ActionFunc, loginRequired bool) *Action {
	a := Action{Method: method, Action: handler, LoginRequired: loginRequired}
	a.handler = func(c *gin.Context) {
		// init context
		ctx := Context{C: c}

		if a.LoginRequired && ctx.User() == nil {
			err := ctx.GetUserError()
			if err == nil {
				err = ErrLoginRequired
			}
			abortError(c, err)
			return
		}

		r, err := a.Action(&ctx)
		if err != nil {
			if err != ErrRawResponse {
				abortError(c, err)
			}
			return
		}
		c.JSON(http.StatusOK, basicResponse{Code: CodeSuccess, Info: r})
	}
	return &a
}

// ActionOption action option
type ActionOption struct {
	LoginRequired bool
}

// NewActionV2 creates new action v2
func NewActionV2(method Method, handler interface{}, actionOption ...ActionOption) *ActionV2 {
	a := ActionV2{Method: method}
	if len(actionOption) > 0 {
		a.LoginRequired = actionOption[0].LoginRequired
	}

	if f, ok := handler.(func(c *Context) (ActionResponse, error)); ok {
		// 如果 handler 是一个符合 ActionFunc 签名的函数，将其转换为 ActionFunc 类型
		handlerFunc := ActionFunc(f)
		a.Action = handlerFunc
	} else if f, ok := handler.(func(c *Context) (ActionResponse, string, error)); ok {
		// 如果 handler 是一个符合 ActionFuncWithMessage 签名的函数，将其转换为 ActionFuncWithMessage 类型
		handlerFunc := ActionFuncWithMessage(f)
		a.ActionWithMessage = handlerFunc
	} else {
		panic("不支持的 handler 类型")
	}

	a.handler = func(c *gin.Context) {
		// init context
		ctx := Context{C: c}

		if a.LoginRequired && ctx.User() == nil {
			err := ctx.GetUserError()
			if err == nil {
				err = ErrLoginRequired
			}
			abortErrorV2(c, err, nil)
			return
		}
		var data ActionResponse
		var message string
		var err error
		if a.Action != nil {
			data, err = a.Action(&ctx)
		} else {
			data, message, err = a.ActionWithMessage(&ctx)
		}
		if data != nil && Mode() != ReleaseMode && isSimpleType(data) {
			// data 不能为数组、字符串、数字等简单类型，非 release mode 的时候会强制报错
			abortErrorV2(c, errors.New("data 字段不支持数组、字符串、数字等简单类型"), nil)
			return
		}
		if err != nil {
			if err != ErrRawResponse {
				abortErrorV2(c, err, data)
			}
			return
		}
		c.JSON(http.StatusOK, BasicResponseV2{Code: CodeSuccess, Message: message, Data: data})
	}

	return &a
}

func abortError(c *gin.Context, err error) {
	switch v := err.(type) {
	case *mrpc.ClientError:
		abortWithError(c, v.Status, v.Code, v.Message)
	case *serviceutil.APIError:
		// TODO: specified code
		abortWithError(c, v.Status, CodeUnknownError, v.Message)
	case ResponseError:
		if l, ok := v.(logger.ContextLogger); ok {
			l.Log(logger.ErrorLevel, c.Request.URL.String())
		}
		c.Status(v.StatusCode())
		abortWithError(c, v.StatusCode(), v.ErrorCode(), v.ErrorInfo())
	default:
		logger.Errorf("%s: %v", c.Request.URL.String(), err)
		c.Status(http.StatusInternalServerError)
		abortWithError(c, http.StatusInternalServerError, CodeUnknownError, err.Error())
	}
}

func abortWithError(c *gin.Context, status, code int, info interface{}) {
	c.AbortWithStatusJSON(status, basicResponse{Code: code, Info: info})
}

func abortErrorV2(c *gin.Context, err error, data ActionResponse) {
	switch v := err.(type) {
	case *mrpc.ClientError:
		abortWithErrorV2(c, v.Status, v.Code, v.Message, nil)
	case *serviceutil.APIError:
		// TODO: specified code
		abortWithErrorV2(c, v.Status, CodeUnknownError, v.Message, nil)
	case ResponseError:
		if l, ok := v.(logger.ContextLogger); ok {
			l.Log(logger.ErrorLevel, c.Request.URL.String())
		}
		c.Status(v.StatusCode())
		if data != nil {
			if Mode() != ReleaseMode && v.ErrorData() != nil {
				err = errors.New("action 返回的 data 和 err 自带的 data 冲突")
				abortErrorV2(c, err, nil)
				return
			}
			v = v.WithData(data)
		}
		abortWithErrorV2(c, v.StatusCode(), v.ErrorCode(), v.ErrorMessage(), v.ErrorData())
	default:
		logger.Errorf("%s: %v", c.Request.URL.String(), err)
		c.Status(http.StatusInternalServerError)
		abortWithErrorV2(c, http.StatusInternalServerError, CodeUnknownError, err.Error(), nil)
	}
}

func abortWithErrorV2(c *gin.Context, status, code int, message string, data interface{}) {
	c.AbortWithStatusJSON(status, BasicResponseV2{Code: code, Message: message, Data: data})
}

// isSimpleType 判断是否是数组、字符串、数字等简单类型
func isSimpleType(value interface{}) bool {
	switch reflect.TypeOf(value).Kind() {
	case reflect.Array, reflect.Slice,
		reflect.String,
		reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64:
		return true
	default:
		return false
	}
}
