package handler

import (
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/middlewares/session"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

// pageSize limit value and default value
const (
	DefaultPageSize int64 = 20
	MaxPageSize     int64 = 100
)

// split limit and default value
const (
	MaxParamSplitLength = 100

	DefaultSplitSep = ","
)

// Context request context
type Context struct {
	C *gin.Context

	equip     *util.Equipment
	equipOnce sync.Once
	equipErr  error

	user     *user.User
	userOnce sync.Once
	userErr  error
}

// UserID returns the user_id from Context
func (ctx *Context) UserID() int64 {
	if u := ctx.User(); u != nil {
		return u.ID
	}
	return 0
}

// EquipID returns the equipment id from Context
func (ctx *Context) EquipID() string {
	if eq := ctx.Equip(); eq != nil {
		return eq.EquipID
	}
	return ""
}

// UserAgent returns the "User-Agent" header
func (ctx *Context) UserAgent() string {
	return ctx.C.GetHeader("User-Agent")
}

// ClientIP returns the client IP
func (ctx *Context) ClientIP() string {
	return ctx.C.ClientIP()
}

// Request return the request
func (ctx *Context) Request() *http.Request {
	return ctx.C.Request
}

// Equip gets the equipment info from the Context
func (ctx *Context) Equip() *util.Equipment {
	if ctx.equip == nil {
		ctx.equipOnce.Do(func() {
			ctx.equip, ctx.equipErr = util.ParseEquipment(ctx.C.Request)
		})
	}
	return ctx.equip
}

// BUVID gets the buvid info from the Context
func (ctx *Context) BUVID() string {
	return ctx.Equip().BUVID
}

// GetEquipError returns the error associated with the util.Equipment in Context
func (ctx *Context) GetEquipError() error {
	return ctx.equipErr
}

// User gets user.User from the Context
func (ctx *Context) User() *user.User {
	if ctx.user == nil {
		ctx.userOnce.Do(func() {
			u, _ := ctx.C.Get("user")
			uu := u.(user.GetUserFunc)
			ctx.user, ctx.userErr = uu(ctx.C)
		})
	}
	return ctx.user
}

// GetUserError returns the error associated with the user.User in Context
func (ctx *Context) GetUserError() error {
	return ctx.userErr
}

// BindJSON decodes the body into v as json.Unmarshal does
func (ctx *Context) BindJSON(v interface{}) error {
	return ctx.C.BindJSON(v)
}

// Bind checks the Content-Type to select a binding engine automatically
func (ctx *Context) Bind(v interface{}) error {
	return ctx.C.Bind(v)
}

// Session returns session interface
func (ctx *Context) Session() sessions.Session {
	return session.Default(ctx.C)
}

// MRPC call missevan rpc
func (ctx *Context) MRPC(uri string, data, v interface{}) error {
	return service.MRPC.Do(ctx.UserContext(), uri, data, v)
}

// GetDefaultParam gets params from query if successful, otherwise post form, otherwise defaultValue
func (ctx *Context) GetDefaultParam(key string, defaultValue string) string {
	value, ok := ctx.C.GetQuery(key)
	if ok {
		return value
	}
	return ctx.C.DefaultPostForm(key, defaultValue)
}

// GetParam gets params from query if successful, otherwise post form
func (ctx *Context) GetParam(key string) (string, bool) {
	value, ok := ctx.C.GetQuery(key)
	if ok {
		return value, true
	}
	return ctx.C.GetPostForm(key)
}

// GetDefaultParamString returns string without leading and trailing white space if successful,
// otherwise post form, otherwise defaultValue
func (ctx *Context) GetDefaultParamString(key string, defaultValue string) string {
	value, ok := ctx.GetParamString(key)
	if ok {
		return value
	}
	return defaultValue
}

// GetParamString returns string without leading and trailing white space
func (ctx *Context) GetParamString(key string) (string, bool) {
	raw, ok := ctx.GetParam(key)
	if !ok {
		return "", false
	}
	return strings.TrimSpace(raw), true
}

// GetDefaultParamInt gets params from query if successful, otherwise post form, otherwise defaultValue
func (ctx *Context) GetDefaultParamInt(key string, defaultValue int) (int, error) {
	val, ok := ctx.GetParam(key)
	if !ok {
		return defaultValue, nil
	}
	value, err := strconv.Atoi(val)
	return value, err
}

// GetParamInt gets params from query if successful, otherwise post form
func (ctx *Context) GetParamInt(key string) (int, error) {
	val, ok := ctx.GetParam(key)
	if !ok {
		return 0, ErrEmptyValue
	}
	value, err := strconv.Atoi(val)
	return value, err
}

// GetDefaultParamInt64 gets params from query if successful, otherwise post form, otherwise defaultValue
func (ctx *Context) GetDefaultParamInt64(key string, defaultValue int64) (int64, error) {
	val, ok := ctx.GetParam(key)
	if !ok {
		return defaultValue, nil
	}
	value, err := strconv.ParseInt(val, 10, 64)
	return value, err
}

// GetParamInt64 gets params from query if successful, otherwise post form
func (ctx *Context) GetParamInt64(key string) (int64, error) {
	val, ok := ctx.GetParam(key)
	if !ok {
		return 0, ErrEmptyValue
	}
	value, err := strconv.ParseInt(val, 10, 64)
	return value, err
}

// GetParamInt64ArrayFromString gets params to int64 array from query if successful, otherwise post form
// NOTICE: sep 参数默认为半角逗号
func (ctx *Context) GetParamInt64ArrayFromString(key string, sep ...string) ([]int64, error) {
	val, ok := ctx.GetParam(key)
	if !ok {
		return nil, ErrEmptyParam
	}

	splitSep := DefaultSplitSep
	if len(sep) == 1 {
		splitSep = sep[0]
	} else if len(sep) > 1 {
		panic("invalid sep param")
	}
	value, err := util.SplitToInt64Array(val, splitSep)
	if err != nil {
		return nil, ErrInvalidParam
	}
	length := len(value)
	if length == 0 || length > MaxParamSplitLength {
		return nil, ErrInvalidParam
	}
	return value, nil
}

// GetParamDateRange gets the start_date and end_date parameters
func (ctx *Context) GetParamDateRange(defaultStartDate, defaultEndDate string) (startDate, endDate time.Time, err error) {
	startDateStr, ok := ctx.GetParam("start_date")
	if !ok && defaultStartDate != "" {
		startDateStr = defaultStartDate
	}
	endDateStr, ok := ctx.GetParam("end_date")
	if !ok && defaultEndDate != "" {
		endDateStr = defaultEndDate
	}

	startDate, err = time.ParseInLocation("2006-01-02", startDateStr, time.Local)
	if err != nil {
		return
	}
	endDate, err = time.ParseInLocation("2006-01-02", endDateStr, time.Local)
	if err != nil {
		return
	}
	if endDate.Before(startDate) {
		err = ErrInvalidDateRange
		return
	}
	return
}

// GetParamMonthRange gets the start_month and end_month parameters
func (ctx *Context) GetParamMonthRange(defaultStartDate, defaultEndDate string) (startTime, endTime time.Time, err error) {
	startTimeStr, ok := ctx.GetParam("start_month")
	if !ok && defaultStartDate != "" {
		startTimeStr = defaultStartDate
	}
	endTimeStr, ok := ctx.GetParam("end_month")
	if !ok && defaultEndDate != "" {
		endTimeStr = defaultEndDate
	}

	startTime, err = time.ParseInLocation("2006-01", startTimeStr, time.Local)
	if err != nil {
		return
	}
	endTime, err = time.ParseInLocation("2006-01", endTimeStr, time.Local)
	if err != nil {
		return
	}
	if endTime.Before(startTime) {
		err = ErrInvalidDateRange
		return
	}
	return
}

// PageOption param page option
type PageOption struct {
	DefaultPageSize int64 // pageSize, 默认 20
	MaxPageSize     int64 // 被允许使用的最大 pageSize, 默认 100
}

// GetParamPage gets the p and page_size parameters
// NOTICE: return: p 默认值: 1, pageSize 默认值: 20
func (ctx *Context) GetParamPage(opt ...*PageOption) (p, pageSize int64, err error) {
	p, err = ctx.GetDefaultParamInt64("p", 1)
	if err != nil || p <= 0 {
		return 0, 0, ErrInvalidParam
	}
	option := getPageOption(opt...)
	pageSize, err = getPageSize(ctx, option)
	if err != nil || pageSize <= 0 || pageSize > option.MaxPageSize {
		return 0, 0, ErrInvalidParam
	}
	return
}

// GetParamMarker gets the marker and page_size parameters
// NOTICE: return: pageSize 默认值: 20
func (ctx *Context) GetParamMarker(opt ...*PageOption) (string, int64, error) {
	marker, _ := ctx.GetParamString("marker")

	option := getPageOption(opt...)
	pageSize, err := ctx.GetDefaultParamInt64("page_size", option.DefaultPageSize)
	if err != nil || pageSize <= 0 || pageSize > option.MaxPageSize {
		return "", 0, ErrInvalidParam
	}
	return marker, pageSize, nil
}

// TODO: 待请求参数都修改为 pagesize 后，移除对 page_size 参数的支持
// NOTICE: 获取分页 size, 支持 pagesize, 并兼容 page_size
func getPageSize(ctx *Context, option *PageOption) (pageSize int64, err error) {
	size, exists := ctx.GetParam("pagesize")
	if !exists {
		size, exists = ctx.GetParam("page_size")
		if !exists {
			return option.DefaultPageSize, nil
		}
	}
	return strconv.ParseInt(size, 10, 64)
}

func getPageOption(opt ...*PageOption) *PageOption {
	length := len(opt)
	if length == 0 {
		return &PageOption{DefaultPageSize: DefaultPageSize, MaxPageSize: MaxPageSize}
	}
	if length > 1 {
		panic("invalid page option")
	}
	if opt[0].DefaultPageSize == 0 {
		opt[0].DefaultPageSize = DefaultPageSize
	}
	if opt[0].MaxPageSize == 0 {
		opt[0].MaxPageSize = MaxPageSize
	}
	if opt[0].DefaultPageSize < 0 || opt[0].MaxPageSize < opt[0].DefaultPageSize {
		panic("invalid page option")
	}
	return opt[0]
}

// SearchWord 搜索词
type SearchWord struct {
	Word        string
	IsInteger   bool
	WordInteger int64
}

// GetParamSearchWord 根据 key 返回搜索词
func (ctx *Context) GetParamSearchWord(key string) (SearchWord, error) {
	s := SearchWord{}
	var ok bool
	s.Word, ok = ctx.GetParamString(key)
	if !ok {
		return s, ErrEmptyValue
	}

	if s.Word != "" {
		var err error
		s.WordInteger, err = strconv.ParseInt(s.Word, 10, 64)
		if err == nil {
			s.IsInteger = true
		}
	}
	return s, nil
}

// Token 从 cookie 中获取 token, 没有返回空字符串
func (ctx *Context) Token() string {
	token, _ := ctx.C.Cookie("token")
	return token
}

// UserContext UserContext
func (ctx *Context) UserContext() mrpc.UserContext {
	return mrpc.NewUserContext(ctx.Request(), ctx.ClientIP())
}

// SendDownload 发送下载响应
// 使用这个操作之后 Action 方法需要返回 handler.ErrRawResponse
// rfc 文档：https://www.rfc-editor.org/rfc/rfc8187
func (ctx *Context) SendDownload(reader io.Reader, length int64, filename string) {
	escapedFilename := url.QueryEscape(filename)
	ctx.C.DataFromReader(http.StatusOK, length, "application/octet-stream", reader,
		map[string]string{
			"Content-Disposition": `attachment; filename=` + strconv.Quote(escapedFilename) + `; filename*=UTF-8''` + escapedFilename,
		})
}
