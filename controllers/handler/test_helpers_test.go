package handler

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestCreateTestCreateTestContexts(t *testing.T) {
	assert := assert.New(t)

	c := CreateTestContext(true)
	assert.NotNil(c.User())
	c = CreateTestContext(false)
	assert.Nil(c.User())

	req, _ := http.NewRequest("GET", "", nil)
	c = CreateTestContextWithMiddlewares(req)
	assert.Nil(c.User())
}

func TestIsLowercaseJSONKey(t *testing.T) {
	assert.False(t, IsLowercaseJSONKey([]byte(`{"A":1}`)))
	assert.True(t, IsLowercaseJSONKey([]byte(`{"a":1}`)))
}

func TestNewTestContext(t *testing.T) {
	assert := assert.New(t)
	c := NewTestContext(http.MethodGet, "test", false, tutil.ToRequestBody(map[string]int{"test": 1}))
	assert.Nil(c.User())
	assert.Equal(http.MethodGet, c.C.Request.Method)
	b, _ := ioutil.ReadAll(c.Request().Body)
	assert.Equal(`{"test":1}`, string(b))

	c = NewTestContext(http.MethodGet, "test", false, nil)
	assert.Nil(c.User())
	assert.Equal(http.MethodGet, c.C.Request.Method)

	c = NewTestContext(http.MethodPost, "test", true, nil)
	assert.NotNil(c.User())
	assert.Nil(c.C.Request.Body)
	assert.Equal(http.MethodPost, c.C.Request.Method)

	c = NewTestContext(http.MethodPost, "test", true, tutil.ToRequestBody(map[string]int{"test": 1}))
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
	assert.Equal(ContentTypeJSON, c.C.ContentType())

	params := map[string]int64{"duration": 1}
	data, _ := json.Marshal(params)
	c = NewTestContext(http.MethodPost, "test", true, bytes.NewBuffer(data))
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
	assert.Equal(ContentTypeJSON, c.C.ContentType())

	c = NewTestContext(http.MethodPost, "test", true, `duration=1`)
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
	assert.Equal(ContentTypeFormUrlencoded, c.C.ContentType())

	c = NewTestContext(http.MethodPost, "test", true, strings.NewReader(`"duration": 1`))
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
	assert.Equal(ContentTypeJSON, c.C.ContentType())

	form := url.Values{}
	form.Add("creator_id", "123456")
	c = NewTestContext(http.MethodPost, "test", true, form)
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
	assert.Equal(ContentTypeFormUrlencoded, c.C.ContentType())

	c = NewTestContext(http.MethodPost, "test", true, map[string]int{"test": 1})
	assert.NotNil(c.User())
	assert.NotNil(c.C.Request.Body)
}

func TestNewTestRPCContext(t *testing.T) {
	assert := assert.New(t)

	assert.PanicsWithValue("RPC middleware doesn't support GetUser function",
		func() { NewRPCTestContext("/rpc", nil).User() })
}

func TestContext_SetClientIP(t *testing.T) {
	assert := assert.New(t)

	c := NewTestContext(http.MethodPost, "test", false, nil)
	c.SetClientIP("***********")
	assert.Equal("***********", c.ClientIP())

	// 测试 IPv4-mapped IPv6
	c.SetClientIP("::ffff:*********")
	assert.Equal("*********", c.ClientIP())

	c.SetClientIP("2001:db8::1428:57ab")
	assert.Equal("2001:db8::1428:57ab", c.ClientIP())

	assert.Panicsf(func() {
		c.SetClientIP("255.255.255.256")
	}, "invalid ip address")
}
