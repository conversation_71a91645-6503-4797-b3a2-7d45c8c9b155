package handler

import (
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	os.Exit(m.Run())
}

func TestModeFuncs(t *testing.T) {
	orgMode := Mode()
	defer SetMode(orgMode)
	assert := assert.New(t)
	SetMode(ReleaseMode)
	assert.Equal(ReleaseMode, Mode())
	SetMode(DebugMode)
	SetMode("")
	assert.Equal(ReleaseMode, Mode())
}

func TestHandlerMount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	middleware := func(c *gin.Context) {
		c.Set("url", c.Request.URL.String())
		c.Next()
	}
	actionTest := func(c *Context) (ActionResponse, error) {
		url, _ := c.C.Get("url")
		return url, nil
	}

	r := gin.Default()
	h3 := Handler{
		Name:    "3",
		Actions: map[string]*Action{"/test": NewAction(GET, actionTest, false)},
	}
	h2 := Handler{
		Name:        "2",
		Actions:     map[string]*Action{"/test": NewAction(GET, actionTest, false)},
		SubHandlers: []Handler{h3},
	}
	h1 := Handler{
		Name:        "1",
		Middlewares: gin.HandlersChain{middleware},
		Actions:     map[string]*Action{"/test": NewAction(POST, actionTest, false)},
		SubHandlers: []Handler{h2},
	}
	h1.Mount(r)
	s := httptest.NewServer(r)
	defer s.Close()

	resp, err := http.Post(fmt.Sprintf("%s/1/test", s.URL), "", nil)
	require.NoError(err)
	body, _ := io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"info":"/1/test"}`, string(body))

	resp, err = http.Get(fmt.Sprintf("%s/1/2/test", s.URL))
	require.NoError(err)
	body, _ = io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"info":"/1/2/test"}`, string(body))

	resp, err = http.Get(fmt.Sprintf("%s/1/2/3/test", s.URL))
	require.NoError(err)
	body, _ = io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"info":"/1/2/3/test"}`, string(body))
}

func TestMergeMiddlewares(t *testing.T) {
	assert := assert.New(t)

	middleware := func(c *gin.Context) {
		c.Set("url", c.Request.URL.String())
		c.Next()
	}

	h1 := HandlerV2{
		Name:        "1",
		Middlewares: gin.HandlersChain{middleware},
	}
	assert.Len(h1.mergeMiddlewares(), 2)
}

func TestHandlerV2_Mount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	middleware := func(c *gin.Context) {
		c.Set("url", c.Request.URL.String())
		c.Next()
	}
	actionTest := func(c *Context) (ActionResponse, string, error) {
		url, _ := c.C.Get("url")
		actionVersion, _ := c.C.Get(ginKeyActionVersion)

		return map[string]interface{}{"a": url, "is_actionv2": IsActionV2(c.C), "version": actionVersion}, url.(string), nil
	}

	r := gin.Default()
	h3 := HandlerV2{
		Name:    "3",
		Actions: map[string]*ActionV2{"/test": NewActionV2(GET, actionTest)},
	}
	h2 := HandlerV2{
		Name:        "2",
		Actions:     map[string]*ActionV2{"/test": NewActionV2(GET, actionTest)},
		SubHandlers: []HandlerV2{h3},
	}
	h1 := HandlerV2{
		Name:        "1",
		Middlewares: gin.HandlersChain{middleware},
		Actions:     map[string]*ActionV2{"/test": NewActionV2(POST, actionTest)},
		SubHandlers: []HandlerV2{h2},
	}
	h1.Mount(r)
	assert.Len(h1.mergeMiddlewares(), 2)
	s := httptest.NewServer(r)
	defer s.Close()

	resp, err := http.Post(fmt.Sprintf("%s/1/test", s.URL), "", nil)
	require.NoError(err)
	body, _ := io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"message":"/1/test","data":{"a":"/1/test","is_actionv2":true,"version":"v2"}}`, string(body))

	resp, err = http.Get(fmt.Sprintf("%s/1/2/test", s.URL))
	require.NoError(err)
	body, _ = io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"message":"/1/2/test","data":{"a":"/1/2/test","is_actionv2":true,"version":"v2"}}`, string(body))

	resp, err = http.Get(fmt.Sprintf("%s/1/2/3/test", s.URL))
	require.NoError(err)
	body, _ = io.ReadAll(resp.Body)
	assert.JSONEq(`{"code":0,"message":"/1/2/3/test","data":{"a":"/1/2/3/test","is_actionv2":true,"version":"v2"}}`, string(body))
}

func TestIsActionV2(t *testing.T) {
	assert := assert.New(t)

	c := &gin.Context{}
	assert.False(IsActionV2(c))

	c.Set(ginKeyActionVersion, ginValueActionVersionV2)
	assert.True(IsActionV2(c))
}
