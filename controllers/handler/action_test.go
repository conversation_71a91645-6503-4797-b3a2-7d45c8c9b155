package handler

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(basicResponse{}, "code", "info")
	kc.Check(BasicResponseV2{}, "code", "message", "data")
}

func TestAbortError(t *testing.T) {
	t.Run("TestClientError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		abortError(c, &mrpc.ClientError{Status: 400, Code: 100000001, Message: "test client error"})
		assert.Equal(400, w.Code)
		assert.JSONEq(`{"code":100000001,"info":"test client error"}`, w.Body.String())
	})

	t.Run("TestAPIError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		abortError(c, &serviceutil.APIError{Status: 500, Message: "test api error"})
		assert.Equal(500, w.Code)
		assert.JSONEq(`{"code":100010007,"info":"test api error"}`, w.Body.String())
	})

	t.Run("TestActionError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		var err error = NewActionError(403, 100000000, "test action error")
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortError(c, err)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"info":"test action error"}`, w.Body.String())
	})

	t.Run("TestActionErrorWithInfo", func(t *testing.T) {
		assert := assert.New(t)

		// 测试有 msg 字段时，期望 msg 不被覆盖
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		var err error
		err = NewActionErrorWithInfo(403, 100000000, "test err", M{"msg": "not err", "confirm": 1})
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortError(c, err)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"info":{"msg":"not err","confirm":1}}`, w.Body.String())

		// 测试没有 msg 字段时，期望 msg 被赋值
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		err = NewActionErrorWithInfo(403, 100000000, "test err", M{"confirm": 1})
		_, ok = err.(ResponseError)
		assert.True(ok)
		abortError(c, err)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"info":{"msg":"test err","confirm":1}}`, w.Body.String())
	})

	t.Run("TestActionLoggerError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "", nil)
		l := NewLoggerError(403, 100000000, "logger error").New(errors.New("error"), nil)
		SetMode(ReleaseMode)
		assert.Equal("logger error", l.ErrorInfo())
		SetMode(TestMode)
		assert.Equal("logger error: error", l.ErrorInfo())
		err := error(l)
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortError(c, err)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"info":"logger error: error"}`, w.Body.String())
	})

	t.Run("TestUnknownError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "", nil)
		abortError(c, errors.New("test others error"))
		assert.Equal(http.StatusInternalServerError, w.Code)
		assert.JSONEq(`{"code":100010007,"info":"test others error"}`, w.Body.String())
	})
}

func TestNewAction(t *testing.T) {
	assert := assert.New(t)

	actionFunc := func(c *Context) (ActionResponse, error) {
		_, ok := c.C.Get("err")
		if ok {
			return nil, NewActionError(404, 100010000, "test error")
		}
		return "test", nil
	}
	// 因为登录问题被拦截
	action := NewAction(GET, actionFunc, true)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user", user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return nil, nil
	}))
	action.GetHandler()(c)
	assert.Equal(ErrLoginRequired.Status, w.Code)
	// 正常响应
	action = NewAction(POST, actionFunc, false)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	action.GetHandler()(c)
	assert.Equal(200, w.Code)
	assert.JSONEq(`{"code":0,"info":"test"}`, w.Body.String())
	// 错误响应
	action = NewAction(POST, actionFunc, false)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("err", true)
	action.GetHandler()(c)
	assert.Equal(404, w.Code)
	assert.JSONEq(`{"code":100010000,"info":"test error"}`, w.Body.String())
}

func TestNewActionV2(t *testing.T) {
	assert := assert.New(t)

	// 测试 handler 为符合 ActionFunc 签名的函数
	actionFunc := func(c *Context) (ActionResponse, error) {
		_, ok := c.C.Get("err")
		if ok {
			return nil, NewActionError(404, 100010000, "test error")
		}
		return map[string]int64{"a": 1, "b": 2}, nil
	}
	// 因为登录问题被拦截
	action := NewActionV2(GET, actionFunc, ActionOption{LoginRequired: true})
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user", user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return nil, nil
	}))
	action.GetHandler()(c)
	assert.Equal(ErrLoginRequired.Status, w.Code)
	// 正常响应
	action = NewActionV2(POST, actionFunc)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	action.GetHandler()(c)
	assert.Equal(200, w.Code)
	assert.JSONEq(`{"code":0,"message":"","data":{"a":1,"b":2}}`, w.Body.String())
	// 错误响应
	action = NewActionV2(POST, actionFunc)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("err", true)
	action.GetHandler()(c)
	assert.Equal(404, w.Code)
	assert.JSONEq(`{"code":100010000,"message":"test error","data":null}`, w.Body.String())

	// 测试 handler 为符合 ActionFuncWithMessage 签名的函数
	actionFuncWithMessage := func(c *Context) (ActionResponse, string, error) {
		_, ok := c.C.Get("err")
		if ok {
			return nil, "", NewActionError(404, 100010000, "test error")
		}
		return map[string]int64{"a": 1, "b": 2}, "test", nil
	}
	// 因为登录问题被拦截
	action = NewActionV2(GET, actionFuncWithMessage, ActionOption{LoginRequired: true})
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("user", user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return nil, nil
	}))
	action.GetHandler()(c)
	assert.Equal(ErrLoginRequired.Status, w.Code)
	// 正常响应
	action = NewActionV2(POST, actionFuncWithMessage)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	action.GetHandler()(c)
	assert.Equal(200, w.Code)
	assert.JSONEq(`{"code":0,"message":"test","data":{"a":1,"b":2}}`, w.Body.String())
	// 错误响应
	action = NewActionV2(POST, actionFuncWithMessage)
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("err", true)
	action.GetHandler()(c)
	assert.Equal(404, w.Code)
	assert.JSONEq(`{"code":100010000,"message":"test error","data":null}`, w.Body.String())

	// 测试 handler 为既不符合 ActionFunc 签名的函数，也不符合 ActionFuncWithMessage 签名的函数
	actionFunc2 := func(c *Context) error {
		_, ok := c.C.Get("err")
		if ok {
			return NewActionError(404, 100010000, "test error")
		}
		return nil
	}
	assert.PanicsWithValue("不支持的 handler 类型", func() {
		NewActionV2(POST, actionFunc2, ActionOption{LoginRequired: true})
	})
}

func TestAbortErrorV2(t *testing.T) {
	t.Run("TestClientError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		abortErrorV2(c, &mrpc.ClientError{Status: 400, Code: 100000001, Message: "test client error"}, nil)
		assert.Equal(400, w.Code)
		assert.JSONEq(`{"code":100000001,"message":"test client error","data":null}`, w.Body.String())
	})

	t.Run("TestAPIError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		abortErrorV2(c, &serviceutil.APIError{Status: 500, Message: "test api error"}, nil)
		assert.Equal(500, w.Code)
		assert.JSONEq(`{"code":100010007,"message":"test api error","data":null}`, w.Body.String())
	})

	t.Run("TestActionError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		var err error = NewActionError(403, 100000000, "test action error")
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortErrorV2(c, err, nil)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"message":"test action error","data":null}`, w.Body.String())
	})

	t.Run("TestActionErrorWithInfo", func(t *testing.T) {
		assert := assert.New(t)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		var err error = NewActionErrorWithInfo(403, 100000000, "test err", M{"msg": "not err", "confirm": 1})
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortErrorV2(c, err, nil)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"message":"test err","data":{"msg":"not err","confirm":1}}`, w.Body.String())

		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "", nil)
		err = NewActionErrorWithInfo(403, 100000000, "test err", M{"msg": "not err", "confirm": 1})
		abortErrorV2(c, err, "test")
		assert.Equal(500, w.Code)
		assert.JSONEq(`{"code":100010007,"message":"action 返回的 data 和 err 自带的 data 冲突","data":null}`,
			w.Body.String())

		defer SetMode(TestMode)
		SetMode(ReleaseMode)
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		err = NewActionErrorWithInfo(403, 100000000, "test err", M{"msg": "not err", "confirm": 1})
		abortErrorV2(c, err, "test")
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"message":"test err","data":"test"}`,
			w.Body.String())
	})

	t.Run("TestActionErrorWithData", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		var err error = NewActionError(403, 100000000, "test error")
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortErrorV2(c, err, M{"msg": "not err", "confirm": 1})
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"message":"test error","data":{"msg":"not err","confirm":1}}`, w.Body.String())
	})

	t.Run("TestActionLoggerError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "", nil)
		l := NewLoggerError(403, 100000000, "logger error").New(errors.New("error"), nil)
		SetMode(ReleaseMode)
		assert.Equal("logger error", l.ErrorMessage())
		SetMode(TestMode)
		assert.Equal("logger error: error", l.ErrorMessage())
		err := error(l)
		_, ok := err.(ResponseError)
		assert.True(ok)
		abortErrorV2(c, err, nil)
		assert.Equal(403, w.Code)
		assert.JSONEq(`{"code":100000000,"message":"logger error: error","data":null}`, w.Body.String())
	})

	t.Run("TestUnknownError", func(t *testing.T) {
		assert := assert.New(t)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest("GET", "", nil)
		abortErrorV2(c, errors.New("test others error"), nil)
		assert.Equal(http.StatusInternalServerError, w.Code)
		assert.JSONEq(`{"code":100010007,"message":"test others error","data":null}`, w.Body.String())
	})
}

func TestIsSimpleType(t *testing.T) {
	assert := assert.New(t)

	assert.True(isSimpleType("字符串"))

	assert.True(isSimpleType([5]int{1, 2, 3, 4, 5}))
	assert.True(isSimpleType([]float64{1.1, 2.2, 3.3}))

	assert.True(isSimpleType(1))
	assert.True(isSimpleType(int8(1)))
	assert.True(isSimpleType(int16(1)))
	assert.True(isSimpleType(int32(1)))
	assert.True(isSimpleType(int64(1)))

	assert.True(isSimpleType(uint(1)))
	assert.True(isSimpleType(uint8(1)))
	assert.True(isSimpleType(uint16(1)))
	assert.True(isSimpleType(uint32(1)))
	assert.True(isSimpleType(uint64(1)))
	assert.True(isSimpleType(uintptr(1)))

	assert.True(isSimpleType(float32(1.1)))
	assert.True(isSimpleType(float64(1.1)))

	assert.False(isSimpleType(map[string]int{"a": 111, "b": 85}))

	type resp struct {
		test1 string
		test2 int
	}
	assert.False(isSimpleType(resp{test1: "aaa", test2: 1}))
}
