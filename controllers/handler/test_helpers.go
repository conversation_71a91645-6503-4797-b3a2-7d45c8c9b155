//go:build !release
// +build !release

package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/session"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/util"
)

func init() {
	// 保证在非 release 下引入总是测试模式
	SetMode(TestMode)
}

// CreateTestContext returns a fresh context for testing purposes
func CreateTestContext(withUser bool) *Context {
	logger.Debug("CreateTestContext 已弃用，请使用 NewTestContext")
	return newTestContext(withUser)
}

// CreateTestContextWithMiddlewares returns a fresh context with middlewares for testing purposes
func CreateTestContextWithMiddlewares(req *http.Request) *Context {
	logger.Debug("CreateTestContextWithMiddlewares 已弃用，请使用 NewTestContext")
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request = req
	// run middlewares for testing, it should using same middlewares
	session.Middleware(session.DefaultConfig)(c)
	user.Middleware()(c)
	return &Context{C: c}
}

// CreateTestUser returns a user for testing purposes
func CreateTestUser() *user.User {
	t := util.TimeNow()
	return &user.User{
		IUser: user.IUser{
			ID:       12,
			Username: "零月",
			IconURL:  "http://static.missevan.com/avatars/icon01.png",
			Confirm:  0,
			LoginAt:  t.Unix(),
			ExpireAt: t.Add(time.Hour).Unix(),
		},
	}
}

// IsLowercaseJSONKey tests whether the keys in data (bytes of json) are lowercase.
func IsLowercaseJSONKey(data []byte) bool {
	var v interface{}
	err := json.Unmarshal(data, &v)
	if err != nil {
		return false
	}
	return isLowercaseJSONKey(v)
}

func isLowercaseJSONKey(vv interface{}) bool {
	switch v := vv.(type) {
	case []interface{}:
		for _, val := range v {
			if !isLowercaseJSONKey(val) {
				return false
			}
		}
	case map[string]interface{}:
		for key := range v {
			if key != strings.ToLower(key) {
				return false
			}
		}
		for _, val := range v {
			if !isLowercaseJSONKey(val) {
				return false
			}
		}
	}
	return true
}

func newTestContext(withUser bool) *Context {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	ctx := Context{C: c}
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		if withUser {
			return CreateTestUser(), nil
		}
		return nil, nil
	})
	// 防止 c.User() panic
	c.Set("user", userFunc)
	return &ctx
}

// NewTestContext 构建 application/json 和 application/x-www-form-urlencoded 类型 context
// NOTICE: body 类型对应的 content-type:
// nil 为无 content-type
// string 和 url.Values 类型对应 application/x-www-form-urlencoded
// io.reader 和其余类型默认对应 application/json
func NewTestContext(method, uri string, withUser bool, body interface{}) *Context {
	c := newTestContext(withUser)
	switch v := body.(type) {
	case nil:
		c.C.Request, _ = http.NewRequest(method, uri, nil)
		return c
	case io.Reader:
		c.C.Request, _ = http.NewRequest(method, uri, v)
		c.C.Request.Header.Set("Content-Type", ContentTypeJSON)
		return c
	case string:
		query, err := url.ParseQuery(v)
		if err != nil {
			panic(err)
		}
		c.C.Request, _ = http.NewRequest(method, uri, strings.NewReader(query.Encode()))
		c.C.Request.Header.Set("Content-Type", ContentTypeFormUrlencoded)
		return c
	case url.Values:
		c.C.Request, _ = http.NewRequest(method, uri, strings.NewReader(v.Encode()))
		c.C.Request.Header.Set("Content-Type", ContentTypeFormUrlencoded)
		return c
	default:
		b, err := json.Marshal(body)
		if err != nil {
			panic(err)
		}
		c.C.Request, _ = http.NewRequest(method, uri, bytes.NewBuffer(b))
		c.C.Request.Header.Set("Content-Type", ContentTypeJSON)
		return c
	}
}

// rpcGetUserPanic must panic
// 避免 rpc 包循环引用，这里复制一份
func rpcGetUserPanic(c *gin.Context) (*user.User, error) {
	panic("RPC middleware doesn't support GetUser function")
}

// NewRPCTestContext 构建测试 RPC 接口的 Context
func NewRPCTestContext(uri string, body interface{}) *Context {
	c := NewTestContext(http.MethodPost, uri, false, body)
	c.C.Set("user", user.GetUserFunc(rpcGetUserPanic))
	return c
}

// SetClientIP 设置当前请求的 client IP
func (ctx *Context) SetClientIP(ip string) {
	netIP := net.ParseIP(ip)
	if netIP == nil {
		panic("invalid ip address")
	}
	if netIP.To4() != nil {
		ctx.C.Request.RemoteAddr = fmt.Sprintf("%s:0", netIP.String())
	} else { // netIP is ipv6 address
		ctx.C.Request.RemoteAddr = fmt.Sprintf("[%s]:0", netIP.String())
	}
}
