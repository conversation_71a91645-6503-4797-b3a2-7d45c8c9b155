package captcha

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "captcha",
		Actions: map[string]*handler.Action{
			"challenge": handler.NewAction(handler.GET, ActionChallenge, false),
		},
	}
}

// RPCHandler rpc handler
func RPCHandler() handler.Handler {
	return handler.Handler{
		Name: "captcha",
		Actions: map[string]*handler.Action{
			"validate": handler.NewAction(handler.POST, ActionRPCValidate, false),
		},
	}
}
