package captcha

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
)

const (
	challengeTypeGeetest = "geetest"
	// captchaSceneOther 其他的验证场景
	captchaSceneOther = "other"
)

type challengeResp struct {
	Type   string        `json:"type"`
	Params geetestParams `json:"params"`
}

type geetestParams struct {
	Gt        string `json:"gt"`
	Challenge string `json:"challenge"`
	Offline   bool   `json:"offline"`
}

// ActionChallenge 获取校验参数
/**
 * @api {get} /x/captcha/challenge 获取校验参数
 *
 * @apiName challenge
 * @apiVersion 0.1.0
 * @apiGroup captcha
 *
 * @apiParam {String} [scene] 验证的场景
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "type": "geetest"
 *       "params": {
 *         "gt": "123456789",
 *         "challenge": "abcd",
 *         "offline": false,
 *       }
 *     }
 *   }
 */
func ActionChallenge(c *handler.Context) (handler.ActionResponse, error) {
	scene, _ := c.GetParamString("scene")
	var uniqueID string
	if userID := c.UserID(); userID != 0 {
		uniqueID = strconv.FormatInt(userID, 10)
	}

	var challenge string
	var geetestID string
	var err error

	if scene == captchaSceneOther {
		challenge, err = service.GeetestLowRisk.Register(c.ClientIP(), uniqueID)
		geetestID = service.GeetestLowRisk.GeetestID()
	} else {
		challenge, err = service.Geetest.Register(c.ClientIP(), uniqueID)
		geetestID = service.Geetest.GeetestID()
	}
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &challengeResp{
		Type: challengeTypeGeetest,
		Params: geetestParams{
			Gt:        geetestID,
			Challenge: challenge,
			// TODO: 支持 bypass 逻辑
			Offline: false,
		},
	}, nil
}
