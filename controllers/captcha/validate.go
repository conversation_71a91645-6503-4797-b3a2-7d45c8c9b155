package captcha

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
)

type validateParam struct {
	CaptchaToken string `json:"captcha_token"`
	IP           string `json:"ip"`
	UserAgent    string `json:"user_agent"`
	URL          string `json:"url"`
	UniqueID     string `json:"unique_id"`
	Scene        string `json:"scene"`
}

type validateResp struct {
	Success   bool              `json:"success"`
	RiskLevel geetest.RiskLevel `json:"risk_level"`
}

// ActionRPCValidate 二次校验
/**
 * @api {get} /rpc/captcha/validate 二次校验
 *
 * @apiName challenge
 * @apiVersion 0.1.0
 * @apiGroup rpc/captcha
 *
 * @apiParam {String} captcha_token 校验参数
 * @apiParam {String} [ip] 请求用户 IP
 * @apiParam {String} [user_agent] 请求用户 UserAgent
 * @apiParam {String} [url] 网页地址
 * @apiParam {String} [unique_id] 请求唯一 ID
 * @apiParam {String} [scene] 验证的场景
 *
 * @apiSuccessExample 请求结果
 *   {
 *     "code": 0
 *     "info": {
 *       "success": true, // 是否通过校验
 *       "risk_level": 2, // 风险等级，1 低风险，2 中风险（建议二次验证），3 高风险（建议直接拦截）
 *     }
 *   }
 */
func ActionRPCValidate(c *handler.Context) (handler.ActionResponse, error) {
	var param validateParam
	err := c.BindJSON(&param)
	if err != nil || param.CaptchaToken == "" {
		return nil, actionerrors.ErrParams
	}
	resp := new(validateResp)
	resp.RiskLevel = geetest.RiskLevelHigh
	gp, ok := geetest.ParseToken(param.CaptchaToken)
	if !ok {
		return resp, nil
	}

	var geetestValidateResp *geetest.ValidateResp
	if param.Scene == captchaSceneOther {
		geetestValidateResp, err = service.GeetestLowRisk.Validate(gp, param.IP, param.UniqueID, param.UserAgent, param.URL)
	} else {
		geetestValidateResp, err = service.Geetest.Validate(gp, param.IP, param.UniqueID, param.UserAgent, param.URL)
	}
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	resp.Success = geetestValidateResp.IsValid()
	resp.RiskLevel = geetestValidateResp.RiskLevel()

	return resp, nil
}
