package captcha

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	h := Handler()
	assert.Equal("captcha", h.Name)
	kc.Check(h, "challenge")
}

func TestRPCHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	h := RPCHandler()
	assert.Equal("captcha", h.Name)
	kc.Check(h, "validate")
}
