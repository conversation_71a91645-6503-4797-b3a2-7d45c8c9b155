package captcha

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestChallengeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(challengeResp{}, "type", "params")
	kc.Check(geetestParams{}, "gt", "challenge", "offline")
}

func TestActionChallenge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := service.Geetest.SetMock(geetest.URIRegister, handler.M{"challenge": "123"})
	defer cancel()

	cancel = service.GeetestLowRisk.SetMock(geetest.URIRegister, handler.M{"challenge": "123"})
	defer cancel()

	c := handler.NewTestContext(http.MethodGet, "challenge", true, nil)
	r, err := ActionChallenge(c)
	require.NoError(err)
	var resp *challengeResp
	require.IsType(resp, r)
	resp = r.(*challengeResp)
	assert.Equal(challengeTypeGeetest, resp.Type)
	assert.Equal(service.Geetest.GeetestID(), resp.Params.Gt)

	c = handler.NewTestContext(http.MethodGet, "challenge?scene=other", true, nil)
	r, err = ActionChallenge(c)
	require.NoError(err)
	require.IsType(resp, r)
	resp = r.(*challengeResp)
	assert.Equal(challengeTypeGeetest, resp.Type)
	assert.Equal(service.GeetestLowRisk.GeetestID(), resp.Params.Gt)
}
