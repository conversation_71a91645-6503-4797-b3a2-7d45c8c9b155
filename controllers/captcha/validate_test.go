package captcha

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestValidateTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(validateParam{}, "unique_id", "ip", "captcha_token")
	kc.Check(validateResp{}, "success")
}

func TestActionValidate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := service.Geetest.SetMock(geetest.URIValidate, handler.M{
		"seccode":           "123",
		"model_probability": 1,
	})
	defer cancel()

	c := handler.NewRPCTestContext("validate", nil)
	_, err := ActionRPCValidate(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewRPCTestContext("validate", handler.M{"captcha_token": "123"})
	r, err := ActionRPCValidate(c)
	require.NoError(err)
	var resp *validateResp
	require.IsType(resp, r)
	resp = r.(*validateResp)
	assert.False(resp.Success)
	assert.Equal(geetest.RiskLevelHigh, resp.RiskLevel)

	c = handler.NewRPCTestContext("validate", handler.M{"captcha_token": "geetest|1|2|3"})
	r, err = ActionRPCValidate(c)
	require.NoError(err)
	resp = r.(*validateResp)
	assert.True(resp.Success)
	assert.Equal(geetest.RiskLevelHigh, resp.RiskLevel)

	cancel = service.GeetestLowRisk.SetMock(geetest.URIValidate, handler.M{
		"seccode":       "123",
		"web_simulator": 1,
	})
	defer cancel()

	c = handler.NewRPCTestContext("validate", handler.M{"captcha_token": "geetest|1|2|3", "scene": "other"})
	r, err = ActionRPCValidate(c)
	require.NoError(err)
	resp = r.(*validateResp)
	assert.True(resp.Success)
	assert.Equal(geetest.RiskLevelMedium, resp.RiskLevel)
}
