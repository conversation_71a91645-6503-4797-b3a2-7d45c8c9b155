package dramareview

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramarevenuereviewerinfo"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCheckAuthTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(noAuthData{}, "region", "mobile")
}

func TestActionCheckAuth(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试未绑定手机号的情况
	testUserID := int64(12)
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	api := "/x/drama/review/check-auth"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)
	_, _, err := ActionCheckAuth(c)
	assert.Equal(actionerrors.ErrDramaReviewUserNoMobile, err)

	// 测试未认证的情况
	authKey := keys.KeyDramaRevenueUserAuth1.Format(testUserID)
	require.NoError(service.Redis.Del(authKey).Err())
	testMobile := "13333333333"
	require.NoError(dramarevenuereviewerinfo.DramaRevenueReviewerInfo{}.DB().Create(
		&dramarevenuereviewerinfo.DramaRevenueReviewerInfo{
			UserID: testUserID,
			Mobile: testMobile,
			Region: 86,
		}).Error)

	c = handler.NewTestContext(http.MethodPost, api, true, nil)
	result, _, err := ActionCheckAuth(c)
	require.Equal(actionerrors.ErrDramaReviewUserNoAuth, err)
	data, ok := result.(noAuthData)
	require.True(ok)
	assert.Equal(86, data.Region)
	assert.Equal("133******33", data.Mobile)

	// 测试认证通过的情况
	require.NoError(service.Redis.Set(authKey, goutil.MD5(c.Token()), time.Minute).Err())
	result, message, err := ActionCheckAuth(c)
	require.NoError(err)
	require.Nil(result)
	assert.Equal("已认证", message)
}
