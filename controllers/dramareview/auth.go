package dramareview

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramarevenuereviewerinfo"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type authParam struct {
	IdentifyCode string `form:"identify_code" json:"identify_code"` // 验证码

	mobileNumber *vcode.MobileNumber
	userID       int64
	userToken    string
}

// ActionAuth 查看剧集收益后台认证接口
/**
 * @api {post} /x/drama/review/auth 查看剧集收益后台认证接口
 *
 * @apiVersion 0.1.0
 * @apiName auth
 * @apiGroup x/drama/review
 *
 * @apiPermission user
 *
 * @apiParam {String} identify_code 验证码
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "认证成功",
 *       "data": null
 *     }
 * @apiErrorExample {json} Error-Response:
 *     {
 *       "code": 100010007
 *       "message": "认证失败",
 *       "data": null
 *     }
 */
func ActionAuth(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAuthParam(c)
	if err != nil {
		return nil, "", err
	}
	// 短信验证通过后，设置认证权限
	err = param.setAuth()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	// 删除验证码
	vcode.DelIdentifyCode(param.mobileNumber.RegionMobile)
	return nil, "认证成功", nil
}

// getUserMobileNumber 获取用户绑定的认证手机号信息
func getUserMobileNumber(userID int64) (*vcode.MobileNumber, error) {
	reviewerInfo, err := dramarevenuereviewerinfo.FindByUserID(userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if reviewerInfo == nil {
		return nil, actionerrors.ErrDramaReviewUserNoMobile
	}
	// 判断验证码是否正确
	// NOTICE: 目前仅支持中国大陆地区手机号
	mobileNumber, err := vcode.RegionMobile(reviewerInfo.Mobile, vcode.DefaultRegion)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, logger.Fields{"user_id": userID})
	}
	return &mobileNumber, nil
}

// newAuthParam 处理参数并进行短信验证码验证
func newAuthParam(c *handler.Context) (*authParam, error) {
	param := new(authParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.IdentifyCode == "" {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()
	// 获取用户绑定的认证手机号信息
	param.mobileNumber, err = getUserMobileNumber(param.userID)
	if err != nil {
		return nil, err
	}
	ok, err := vcode.CheckIdentifyCode(param.mobileNumber.RegionMobile, param.IdentifyCode, vcode.ObjectiveTypeDramaRevenue)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, handler.NewActionError(400, handler.CodeUnknownError, "验证码错误")
	}
	param.userToken = c.Token()
	return param, nil
}

// setAuth 设置查看剧集收益后台身份认证权限
func (param *authParam) setAuth() error {
	// 将登录用户 token 的 md5 值存入 redis，有效期 2h
	key := keys.KeyDramaRevenueUserAuth1.Format(param.userID)
	return service.Redis.Set(key, goutil.MD5(param.userToken), 2*time.Hour).Err()
}
