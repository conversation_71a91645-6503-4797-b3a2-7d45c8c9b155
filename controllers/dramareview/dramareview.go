package dramareview

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler 返回 dramareview handler
func Handler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "drama/review",
		Actions: map[string]*handler.ActionV2{
			"check-auth": handler.NewActionV2(handler.POST, ActionCheckAuth, handler.ActionOption{LoginRequired: true}),
			"auth":       handler.NewActionV2(handler.POST, ActionAuth, handler.ActionOption{LoginRequired: true}),
			"sendcode":   handler.NewActionV2(handler.POST, ActionSendcode, handler.ActionOption{LoginRequired: true}),
		},
	}
}
