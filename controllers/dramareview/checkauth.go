package dramareview

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// noAuthData 未认证时，返回的 data
type noAuthData struct {
	Region int    `json:"region"`
	Mobile string `json:"mobile"`
}

// ActionCheckAuth 检查进入剧集收益后台是否已认证
/**
 * @api {post} /x/drama/review/check-auth 检查进入剧集收益后台是否已认证
 *
 * @apiVersion 0.1.0
 * @apiName check-auth
 * @apiGroup x/drama/review
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "已认证",
 *       "data": null
 *     }
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020011
 *       "message": "请联系工作人员进行绑定",
 *       "data": null
 *     }
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020012
 *       "message": "未认证",
 *       "data": {
 *         "region": 86,
 *         "mobile": "135****8480"
 *       }
 *     }
 */
func ActionCheckAuth(c *handler.Context) (handler.ActionResponse, string, error) {
	userID := c.UserID()
	mobileNumber, err := getUserMobileNumber(userID)
	if err != nil {
		return nil, "", err
	}
	authKey := keys.KeyDramaRevenueUserAuth1.Format(userID)
	authValue, err := service.Redis.Get(authKey).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if serviceredis.IsRedisNil(err) || goutil.MD5(c.Token()) != authValue {
		// 若未认证，则返回相关错误
		return noAuthData{
			Region: mobileNumber.RegionCode,
			Mobile: mobileNumber.MosaicMobile(),
		}, "", actionerrors.ErrDramaReviewUserNoAuth
	}
	return nil, "已认证", nil
}
