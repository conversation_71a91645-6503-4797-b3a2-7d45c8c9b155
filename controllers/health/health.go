package health

import (
	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "health",
		Actions: map[string]*handler.Action{
			"": handler.NewAction(handler.GET, actionHealth, false),
		},
	}
}

func actionHealth(c *handler.Context) (handler.ActionResponse, error) {
	return "success", nil
}
