package dramalist

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler 返回 dramalist handler
func Hand<PERSON>() handler.Handler {
	return handler.Handler{
		Name: "dramalist",
		Actions: map[string]*handler.Action{
			"detail":         handler.NewAction(handler.GET, ActionDramaListDetail, false),
			"get-drama":      handler.NewAction(handler.GET, ActionDramaListGetDrama, false),
			"collect":        handler.NewAction(handler.POST, ActionDramaListCollect, true),
			"cancel-collect": handler.NewAction(handler.POST, ActionDramaListCancelCollect, true),
			"collect-list":   handler.NewAction(handler.GET, ActionDramaListCollectList, false),
		},
	}
}
