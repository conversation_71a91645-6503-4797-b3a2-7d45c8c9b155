package dramalist

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionDramaListCancelCollect 取消收藏剧单
/**
 * @api {post} /x/dramalist/cancel-collect 取消收藏剧单
 *
 * @apiVersion 0.1.0
 * @apiName cancel-collect
 * @apiGroup /x/dramalist/
 *
 * @apiParam {Number} dramalist_id 剧单 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "已取消收藏",
 *       "is_collected": 0 // 是否收藏。0：未被收藏；1：已收藏
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200140001
 * @apiError (404) {String} info 剧单不存在
 *
 * @apiError (400) {Number} code 200140003
 * @apiError (400) {String} info 不能收藏自己的剧单
 */
func ActionDramaListCancelCollect(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newCollectOrCancelParams(c)
	if err != nil {
		return nil, err
	}

	// 取消收藏剧单
	err = param.cancelCollect()
	if err != nil {
		return nil, err
	}

	return &collectResp{
		Msg:         "已取消收藏",
		IsCollected: mcollectdramalist.StatusNotCollected,
	}, nil
}

// cancelCollect 取消收藏剧单
func (param *collectParams) cancelCollect() error {
	// 判断是否收藏过该剧单
	collected, err := mcollectdramalist.IsCollected(param.userID, param.DramalistID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if !collected {
		// 如果未收藏剧单，则直接返回，保持结果幂等性
		return nil
	}
	err = servicedb.Tx(service.MainDB, func(tx *gorm.DB) error {
		// 删除收藏记录
		db := tx.Table(mcollectdramalist.MCollectDramalist{}.TableName()).
			Delete("", "user_id = ? AND dramalist_id = ?", param.userID, param.DramalistID)
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return nil
		}
		// 剧单收藏数量 -1
		err = tx.Table(mdramalist.MDramalist{}.TableName()).
			Where("id = ?", param.DramalistID).
			Update(map[string]interface{}{
				"collect_count": gorm.Expr("GREATEST(collect_count, ?) - ?", 1, 1),
				"modified_time": goutil.TimeNow().Unix(),
			}).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	return nil
}
