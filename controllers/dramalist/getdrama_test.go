package dramalist

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscription"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalistdramamap"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

const testDramalistID = 23333

func TestDramaListGetDramaRespTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(dramalistGetDramaResp{}, "data", "pagination")
	kc.Check(dramalistDramaInfo{}, "id", "name", "intro", "cover", "cover_color", "cvs", "view_count",
		"is_subscribed", "integrity", "catalog_name", "type_name", "corner_mark")
	kc.CheckOmitEmpty(dramalistDramaInfo{}, "corner_mark")
}

func createTestData() error {
	dramalistinfo := mdramalist.MDramalist{
		ID:           testDramalistID,
		Title:        "测试剧单标题",
		Intro:        "测试剧单简介",
		UserID:       233,
		CollectCount: 123,
	}
	if err := dramalistinfo.DB().Delete("", "id = ?", testDramalistID).Error; err != nil {
		return err
	}
	if err := dramalistinfo.DB().Create(dramalistinfo).Error; err != nil {
		return err
	}

	var dramalistDramas []mdramalistdramamap.MDramalistDramaMap
	for i := 1; i < 4; i++ {
		drama := mdramalistdramamap.MDramalistDramaMap{
			DramalistID: testDramalistID,
			DramaID:     int64(i),
			Intro:       fmt.Sprintf("测试剧集简介 %d", i),
			Sort:        int64(i),
		}
		dramalistDramas = append(dramalistDramas, drama)
	}
	if err := dramalistDramas[0].DB().Delete("", "dramalist_id = ?", testDramalistID).Error; err != nil {
		return err
	}
	if err := servicedb.BatchInsert(service.MainDB, dramalistDramas[0].TableName(), dramalistDramas); err != nil {
		return err
	}

	subscription := dramasubscription.RadioDramaSubscription{
		UserID:  12,
		DramaID: 1,
	}
	if err := subscription.DB().Delete("", "user_id = ? AND drama_id = ?",
		subscription.UserID, subscription.DramaID).Error; err != nil {
		return err
	}
	if err := subscription.DB().Create(&subscription).Error; err != nil {
		return err
	}
	return nil
}

func TestActionDramaListGetDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(createTestData())

	key := keys.KeyDramaCornerMarkStyle0.Format()
	require.NoError(service.LRURedis.Del(key).Err())
	// 测试获取剧单内剧集列表
	api := "/x/dramalist/get-drama"
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=1&pagesize=20", api, testDramalistID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.2.6 (iOS;18.1.1;iPhone17,1)")
	res, err := ActionDramaListGetDrama(c)
	require.NoError(err)
	require.NotNil(res)
	data, ok := res.(dramalistGetDramaResp)
	require.True(ok)
	assert.Equal(util.Pagination{Count: 3, MaxPage: 1, P: 1, PageSize: 20}, data.Pagination)

	require.Len(data.Data, 2)
	assert.EqualValues(1, data.Data[0].ID)
	assert.NotEmpty(data.Data[0].Name)
	assert.Equal("测试剧集简介 1", data.Data[0].Intro)
	assert.NotEmpty(data.Data[0].Cover)
	assert.Equal([]string{"小野大辅"}, data.Data[0].CVs)
	assert.EqualValues(4, data.Data[0].ViewCount)
	assert.Equal(1, data.Data[0].IsSubscribed)
	assert.EqualValues(dramainfo.IntegrityNameSerializing, data.Data[0].Integrity)
	assert.Equal("会员", data.Data[0].CornerMark.Text)
	assert.Equal("#AAAAAA", data.Data[0].CornerMark.TextStartColor)
	assert.Equal("#AAAAAA", data.Data[0].CornerMark.TextEndColor)
	assert.Equal("中文广播剧", data.Data[0].CatalogName)
	dramaInfo := new(dramainfo.RadioDramaDramainfo)
	require.NoError(dramaInfo.DB().Select("catalog, type").Where("id = ?", data.Data[0].ID).Find(dramaInfo).Error)
	assert.Equal(dramainfo.TypeBoysLove, dramaInfo.Type)
	assert.Equal("纯爱", dramaInfo.TypeName)
	assert.Empty(data.Data[0].TypeName)

	assert.EqualValues(2, data.Data[1].ID)
	assert.NotEmpty(data.Data[1].Name)
	assert.Equal("测试剧集简介 2", data.Data[1].Intro)
	assert.NotEmpty(data.Data[1].Cover)
	assert.Equal([]string{"小野大辅"}, data.Data[1].CVs)
	assert.EqualValues(4, data.Data[1].ViewCount)
	assert.Zero(data.Data[1].IsSubscribed)
	assert.EqualValues(dramainfo.IntegrityNameSerializing, data.Data[1].Integrity)
	assert.Equal("已购", data.Data[1].CornerMark.Text)
	assert.Equal(util.Pagination{Count: 3, MaxPage: 1, P: 1, PageSize: 20}, data.Pagination)

	// 测试 iOS < 6.2.5 不返回 text_start_color 和 text_end_color 角标颜色渐变字段
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=1&pagesize=20", api, testDramalistID), true, nil)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.2.4 (iOS;18.1.1;iPhone17,1)")
	res, err = ActionDramaListGetDrama(c)
	require.NoError(err)
	require.NotNil(res)
	data, ok = res.(dramalistGetDramaResp)
	require.True(ok)
	assert.Equal("", data.Data[0].CornerMark.TextStartColor)
	assert.Equal("", data.Data[0].CornerMark.TextEndColor)

	// 测试没有数据
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=2&pagesize=20", api, testDramalistID), true, nil)
	res, err = ActionDramaListGetDrama(c)
	require.NoError(err)
	require.NotNil(res)
	data, ok = res.(dramalistGetDramaResp)
	require.True(ok)
	assert.Empty(data.Data)
}

func TestNewDramalistGetDramaParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/x/dramalist/get-drama"
	c := handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=1&pagesize=20", api, -1), true, nil)
	_, err := newDramalistGetDramaParams(c)
	assert.EqualError(err, "参数错误")

	// 测试剧单不存在
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=1&pagesize=20", api, 324234), true, nil)
	_, err = newDramalistGetDramaParams(c)
	assert.EqualError(err, "剧单不存在")

	// 测试获取参数
	dramalistinfo := mdramalist.MDramalist{
		ID:           testDramalistID,
		Title:        "测试剧单标题",
		Intro:        "测试剧单简介",
		UserID:       233,
		CollectCount: 123,
	}
	require.NoError(dramalistinfo.DB().Delete("", "id = ?", testDramalistID).Error)
	require.NoError(dramalistinfo.DB().Create(dramalistinfo).Error)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("%s?dramalist_id=%d&p=1&pagesize=3", api, testDramalistID), true, nil)
	param, err := newDramalistGetDramaParams(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(testDramalistID, param.DramalistID)
	assert.EqualValues(12, param.userID)
}

func TestDramalistGetDramaParams_getDramaList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(createTestData())
	param := &dramalistGetDramaParams{
		DramalistDramasListParam: mdramalistdramamap.DramalistDramasListParam{
			DramalistID: testDramalistID,
			Page:        1,
			PageSize:    20,
		},
		userID: 12,
	}
	require.NoError(param.getDramaList())
	assert.Len(param.checkedPassDramaIDs, 2)
	assert.Len(param.dramalistDramasInfo, 3)
	assert.Len(param.cvNamesMap, 2)
	assert.Len(param.cornerMarkMap, 2)
	assert.Len(param.checkedPassDramaInfoMap, 2)
	assert.Len(param.subscribedDramaMap, 1)
}

func TestDramalistGetDramaParams_getDramaCVNamesMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有数据
	param := dramalistGetDramaParams{
		checkedPassDramaIDs: []int64{3},
	}
	cvMap, err := param.getDramaCVNamesMap()
	require.NoError(err)
	_, ok := cvMap[3]
	require.False(ok)

	// 测试获取剧集主役声优名
	param = dramalistGetDramaParams{
		checkedPassDramaIDs: []int64{1},
	}
	cvMap, err = param.getDramaCVNamesMap()
	require.NoError(err)
	names, ok := cvMap[1]
	require.True(ok)
	assert.Equal([]string{"小野大辅"}, names)
}
