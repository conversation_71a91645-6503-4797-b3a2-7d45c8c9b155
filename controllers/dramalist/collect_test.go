package dramalist

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestCollectTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(collectParams{}, "dramalist_id")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(collectParams{}, "dramalist_id")
	kc.Check(collectResp{}, "msg", "is_collected")
}

func findCollectDramalist(userID, dramalistID int64) (*mcollectdramalist.MCollectDramalist, error) {
	mCollectDramalist := new(mcollectdramalist.MCollectDramalist)
	err := mcollectdramalist.MCollectDramalist{}.DB().
		Where("user_id = ? AND dramalist_id = ?", userID, dramalistID).
		Take(mCollectDramalist).Error
	if err != nil {
		return nil, err
	}

	return mCollectDramalist, nil
}

func deleteCollectDramalist(userID, dramalistID int64) error {
	return mcollectdramalist.MCollectDramalist{}.DB().Delete("",
		"user_id = ? AND dramalist_id = ?", userID, dramalistID).Error
}

func getDramalistCollectCount(dramalistID int64) (int64, error) {
	var collectCount int64
	err := mdramalist.MDramalist{}.DB().Select("collect_count").
		Where("id = ? AND delete_time = 0", dramalistID).Row().Scan(&collectCount)
	if err != nil {
		return collectCount, err
	}

	return collectCount, nil
}

func TestActionDramaListCollect(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	param := &collectParams{
		DramalistID: 0,
	}
	api := "/x/dramalist/collect"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	result, err := ActionDramaListCollect(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试剧单不存在
	param = &collectParams{
		DramalistID: 10000,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionDramaListCollect(c)
	assert.Equal(actionerrors.ErrDramalistNotFound, err)
	assert.Nil(result)

	// 测试未收藏该剧单时收藏剧单
	param = &collectParams{
		DramalistID: 2,
	}
	// 删除收藏记录数据
	userID := c.UserID()
	require.NoError(deleteCollectDramalist(userID, param.DramalistID))
	// 查询收藏剧单前剧单的收藏数
	beforeCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	// 测试收藏成功（App 用户）
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.0.1 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	result, err = ActionDramaListCollect(c)
	require.NoError(err)
	resp := result.(*collectResp)
	assert.Equal("剧单收藏成功，可在「我听-收藏」中查看", resp.Msg)
	assert.Equal(mcollectdramalist.StatusCollected, resp.IsCollected)
	// 验证剧单收藏记录已生成
	mCollectDramalist, err := findCollectDramalist(userID, param.DramalistID)
	require.NoError(err)
	// 验证收藏数量已加 1
	afterCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(beforeCollectCount+1, afterCollectCount)

	// 测试已收藏过该剧单时收藏剧单（接口幂等性）（非 App 用户）
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionDramaListCollect(c)
	require.NoError(err)
	require.NotNil(result)
	resp = result.(*collectResp)
	assert.Equal("剧单收藏成功，可前往 App > 我听 > 收藏中查看", resp.Msg)
	assert.Equal(mcollectdramalist.StatusCollected, resp.IsCollected)
	// 验证收藏记录还是之前的收藏记录
	mCollectDramalist1, err := findCollectDramalist(userID, param.DramalistID)
	require.NoError(err)

	assert.Equal(mCollectDramalist.ID, mCollectDramalist1.ID)
	// 验证收藏数量不会增加
	afterCollectCount1, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(afterCollectCount, afterCollectCount1)
}

func TestNewCollectOrCancelParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	body := &collectParams{
		DramalistID: 0,
	}
	api := "/x/dramalist/collect"
	c := handler.NewTestContext(http.MethodPost, api, true, body)
	param, err := newCollectOrCancelParams(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试剧单不存在
	body = &collectParams{
		DramalistID: 10001,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newCollectOrCancelParams(c)
	assert.Equal(actionerrors.ErrDramalistNotFound, err)
	assert.Nil(param)

	// 测试收藏自己的剧单时
	body = &collectParams{
		DramalistID: 3,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 10
	param, err = newCollectOrCancelParams(c)
	assert.Equal(actionerrors.ErrCollectDramalistLimit, err)
	assert.Nil(param)

	// 测试未收藏剧单时
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	param, err = newCollectOrCancelParams(c)
	require.NoError(err)
	assert.EqualValues(3, param.DramalistID)
	assert.EqualValues(12, param.userID)
}

func TestCollectParams_collect(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := &collectParams{
		DramalistID: 4,
		userID:      12,
	}

	// 测试用户未收藏剧单时
	// 删除收藏记录数据
	require.NoError(deleteCollectDramalist(param.userID, param.DramalistID))
	// 查询收藏剧单前剧单的收藏数
	beforeCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	err = param.collect()
	require.NoError(err)
	// 验证收藏记录已生成
	mCollectDramalist, err := findCollectDramalist(param.userID, param.DramalistID)
	require.NoError(err)
	// 验证收藏数量 +1
	afterCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(beforeCollectCount+1, afterCollectCount)

	// 测试用户已收藏剧单时
	err = param.collect()
	require.NoError(err)
	// 验证收藏记录还是原来的记录
	mCollectDramalist1, err := findCollectDramalist(param.userID, param.DramalistID)
	require.NoError(err)
	assert.Equal(mCollectDramalist, mCollectDramalist1)
	// 验证收藏数量不增加
	afterCollectCount1, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(afterCollectCount, afterCollectCount1)
}
