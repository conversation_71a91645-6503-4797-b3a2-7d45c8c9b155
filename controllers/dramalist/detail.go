package dramalist

import (
	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscription"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalistdramamap"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

type dramalistDetailParams struct {
	DramalistID int64 `form:"dramalist_id"` // 剧单 ID

	userID                 int64                  // 用户 ID
	userSubscribedDramaNum int64                  // 用户已追（订阅）剧单内剧集数量
	userCollected          bool                   // 用户是否收藏剧单
	coverURL               string                 // 剧单封面
	coverColor             int                    // 封面 RGB 颜色值（10 进制表示）
	checkedPassDramaIDs    []int64                // 剧单内全部已过审剧集 ID
	dramalistCreator       *user.MowangskUser     // 剧单所属用户信息
	dramalistInfo          *mdramalist.MDramalist // 剧单信息
}

type dramalistDetailResp struct {
	ID                   int64  `json:"id"`                     // 剧单 ID
	Title                string `json:"title"`                  // 剧单标题
	Intro                string `json:"intro"`                  // 剧单简介
	Cover                string `json:"cover"`                  // 剧单封面
	CoverColor           int    `json:"cover_color"`            // 封面 RGB 颜色值（10 进制表示）
	CollectCount         int64  `json:"collect_count"`          // 剧单被收藏数量
	IsCollected          int    `json:"is_collected"`           // 用户是否收藏剧单
	DramaCount           int64  `json:"drama_count"`            // 剧单内剧集数量
	SubscribedDramaCount int64  `json:"subscribed_drama_count"` // 用户已追（订阅）剧单内剧集数量
	UserID               int64  `json:"user_id"`                // 剧单所属用户 ID
	UserName             string `json:"username"`               // 剧单所属用户昵称
	IconURL              string `json:"iconurl"`                // 剧单所属用户头像
}

// ActionDramaListDetail 获取剧单详情
/**
 * @api {get} /x/dramalist/detail{?dramalist_id} 获取剧单详情
 *
 * @apiVersion 0.1.0
 * @apiName detail
 * @apiGroup /x/dramalist/
 *
 * @apiParam {Number} dramalist_id 剧单 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "id": 1, // 剧单 ID
 *       "title": "剧单标题",
 *       "intro": "剧单简介", // 未设置简介时下发空字符串
 *       "cover": "https://www.test.com/cover.jpg", // 剧单封面，剧单封面为剧单收录剧集表中已过审的剧集中 sort 值最小的剧集封面
 *       "cover_color": 12434877, // 封面 RGB 颜色值（10 进制表示）
 *       "collect_count": 233, // 剧单被收藏数量
 *       "is_collected": 0, // 是否收藏。0：未被收藏；1：已收藏
 *       "drama_count": 20, // 剧单内剧集数量
 *       "subscribed_drama_count": 13, // 剧单内已追（订阅）剧集数量
 *       "user_id": 1, // 剧单所属用户 ID
 *       "username": "用户昵称",
 *       "iconurl": "https://www.test.com/avatar.jpg" // 用户头像
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200140001
 * @apiError (404) {String} info 剧单不存在
 */
func ActionDramaListDetail(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDramalistDetailParams(c)
	if err != nil {
		return nil, err
	}

	// 获取剧单封面
	err = param.getDramalistCover()
	if err != nil {
		return nil, err
	}

	// 获取用户和剧单之间的关系（用户订阅剧单中剧集数和是否收藏了该剧单）
	err = param.getUserRelationDramalist()
	if err != nil {
		return nil, err
	}

	return param.buildResp(), nil
}

func newDramalistDetailParams(c *handler.Context) (*dramalistDetailParams, error) {
	param := new(dramalistDetailParams)
	err := c.Bind(param)
	if err != nil || param.DramalistID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()

	// 查询剧单信息
	param.dramalistInfo, err = mdramalist.FindOne(param.DramalistID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.dramalistInfo == nil {
		return nil, actionerrors.ErrDramalistNotFound
	}

	// 查询剧单所属用户信息
	param.dramalistCreator, err = user.FindByUserID(param.dramalistInfo.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if param.dramalistCreator == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUserNotFound, "剧单所属用户不存在")
	}
	return param, nil
}

// getDramalistCover 获取剧单封面
// TODO: 优化获取剧单封面逻辑
func (param *dramalistDetailParams) getDramalistCover() error {
	param.coverURL = service.Storage.Parse(params.URL.DefaultCoverURL)

	// 获取剧单内所有剧集 ID
	dramaIDs, err := mdramalistdramamap.ListDramalistDramaIDs(param.DramalistID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramaIDs) == 0 {
		return nil
	}

	// 获取剧单内全部过审剧集信息
	dramaInfos, err := dramainfo.ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramaInfos) == 0 {
		return nil
	}
	dramaInfoMap := util.ToMap(dramaInfos, "ID").(map[int64]dramainfo.RadioDramaDramainfo)

	for _, dramaID := range dramaIDs {
		// 过滤非过审的剧集
		if _, ok := dramaInfoMap[dramaID]; ok {
			param.checkedPassDramaIDs = append(param.checkedPassDramaIDs, dramaID)
		}
	}
	if len(param.checkedPassDramaIDs) == 0 {
		return nil
	}

	// 获取剧单封面，剧单封面为剧单收录剧集表中已过审的剧集中 sort 值最小的剧集封面
	if drama, ok := dramaInfoMap[param.checkedPassDramaIDs[0]]; ok {
		param.coverURL = drama.CoverURL
		param.coverColor = drama.CoverColor
	}
	return nil
}

// getUserRelationDramalist 获取用户和剧单之间的关系（用户订阅剧单中剧集数和是否收藏了该剧单）
func (param *dramalistDetailParams) getUserRelationDramalist() (err error) {
	if param.userID == 0 {
		return nil
	}

	// 获取用户订阅剧单内剧集数量
	param.userSubscribedDramaNum, err = dramasubscription.CountUserSubscribedDramas(param.userID, param.checkedPassDramaIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 用户是否收藏了该剧单
	if param.userID != param.dramalistCreator.ID {
		param.userCollected, err = mcollectdramalist.IsCollected(param.userID, param.DramalistID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
	}
	return nil
}

// buildResp 构建返回数据
func (param *dramalistDetailParams) buildResp() dramalistDetailResp {
	return dramalistDetailResp{
		ID:                   param.DramalistID,
		Title:                param.dramalistInfo.Title,
		Intro:                param.dramalistInfo.Intro,
		Cover:                param.coverURL,
		CoverColor:           param.coverColor,
		CollectCount:         param.dramalistInfo.CollectCount,
		IsCollected:          util.BoolToInt(param.userCollected),
		DramaCount:           int64(len(param.checkedPassDramaIDs)),
		SubscribedDramaCount: param.userSubscribedDramaNum,
		UserID:               param.dramalistCreator.ID,
		UserName:             param.dramalistCreator.UserName,
		IconURL:              param.dramalistCreator.IconURL,
	}
}
