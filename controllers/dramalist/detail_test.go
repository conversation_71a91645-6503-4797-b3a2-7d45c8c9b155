package dramalist

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestDramalistDetailRespTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(dramalistDetailResp{}, "id", "title", "intro", "cover", "cover_color", "collect_count", "is_collected",
		"drama_count", "subscribed_drama_count", "user_id", "username", "iconurl")
}

func TestActionDramaListDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获剧单详情
	c := handler.NewTestContext(http.MethodGet, "/x/dramalist/detail?dramalist_id=8", true, nil)
	res, err := ActionDramaListDetail(c)
	require.NoError(err)
	require.NotNil(res)
	data, ok := res.(dramalistDetailResp)
	require.True(ok)
	assert.EqualValues(8, data.ID)
	assert.Equal("测试剧单标题 8", data.Title)
	assert.Equal("测试剧单简介 8", data.Intro)
	assert.NotEmpty(data.Cover)
	assert.EqualValues(1, data.CollectCount)
	assert.Equal(mcollectdramalist.StatusNotCollected, data.IsCollected)
	assert.EqualValues(2, data.DramaCount)
	assert.EqualValues(0, data.SubscribedDramaCount)
	assert.EqualValues(12, data.UserID)
	assert.Equal("零月", data.UserName)
	assert.NotEmpty(data.IconURL)
}

func TestNewDramalistDetailParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/dramalist/detail?dramalist_id=-1", true, nil)
	_, err := newDramalistDetailParams(c)
	assert.EqualError(err, "参数错误")

	// 测试剧单不存在
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/detail?dramalist_id=324234", true, nil)
	_, err = newDramalistDetailParams(c)
	assert.EqualError(err, "剧单不存在")

	// 测试剧单所属用户不存在
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/detail?dramalist_id=7", true, nil)
	_, err = newDramalistDetailParams(c)
	assert.EqualError(err, "剧单所属用户不存在")

	// 测试获取参数
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/detail?dramalist_id=8", true, nil)
	res, err := newDramalistDetailParams(c)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(8, res.dramalistInfo.ID)
	assert.EqualValues(12, res.dramalistCreator.ID)
}

func TestDramalistDetailParams_getDramalistCover(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := dramalistDetailParams{
		DramalistID: 8,
	}
	err := param.getDramalistCover()
	require.NoError(err)
	assert.NotEmpty(param.coverURL)
	assert.Len(param.checkedPassDramaIDs, 2)
}

func TestDramalistDetailParams_getUserRelationDramalist(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := dramalistDetailParams{
		userID:              3010224,
		DramalistID:         8,
		checkedPassDramaIDs: []int64{6, 7, 8},
		dramalistCreator: &user.MowangskUser{
			Simple: user.Simple{
				ID: 12,
			},
		},
	}
	err := param.getUserRelationDramalist()
	require.NoError(err)
	assert.True(param.userCollected)
	assert.EqualValues(3, param.userSubscribedDramaNum)
}
