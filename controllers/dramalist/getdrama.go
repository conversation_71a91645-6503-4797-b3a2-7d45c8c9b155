package dramalist

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramacornermarkstyle"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscription"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalistdramamap"
	"github.com/MiaoSiLa/missevan-go/util"
)

type dramalistGetDramaParams struct {
	mdramalistdramamap.DramalistDramasListParam

	userID              int64                                   // 用户 ID
	pagination          util.Pagination                         // 分页详情
	checkedPassDramaIDs []int64                                 // 剧单内全部已过审剧集 ID
	dramalistDramasInfo []mdramalistdramamap.MDramalistDramaMap // 剧单收录剧集信息列表

	cvNamesMap              map[int64][]string                        // 剧集 ID 对应主役声优名列表 map
	cornerMarkMap           map[int64]dramacornermarkstyle.CornerMark // 剧集 ID 对应角标信息 map
	checkedPassDramaInfoMap map[int64]dramainfo.RadioDramaDramainfo   // 剧集 ID 对应过审剧集信息 map
	subscribedDramaMap      map[int64]struct{}                        // 获取用户已追（订阅）的剧集中已过审的剧集 ID
}

type dramalistGetDramaResp struct {
	Data       []dramalistDramaInfo `json:"data"`
	Pagination util.Pagination      `json:"pagination"`
}

type dramalistDramaInfo struct {
	ID           int64                            `json:"id"`                    // 剧集 ID
	Name         string                           `json:"name"`                  // 剧集标题
	Intro        string                           `json:"intro"`                 // 剧集推荐语
	Cover        string                           `json:"cover"`                 // 剧集封面
	CoverColor   int                              `json:"cover_color"`           // 封面 RGB 颜色值（10 进制表示）
	CVs          []string                         `json:"cvs"`                   // 主役声优名列表
	ViewCount    int64                            `json:"view_count"`            // 播放次数
	IsSubscribed int                              `json:"is_subscribed"`         // 用户是否订阅
	Integrity    int8                             `json:"integrity"`             // 完结度
	CatalogName  string                           `json:"catalog_name"`          // 类型名称
	TypeName     string                           `json:"type_name"`             // 分类名称
	CornerMark   *dramacornermarkstyle.CornerMark `json:"corner_mark,omitempty"` // 剧集角标信息
}

// ActionDramaListGetDrama 获取剧单内剧集列表
/**
 * @api {get} /x/dramalist/get-drama{?dramalist_id,p,pagesize} 获取剧单内剧集列表
 *
 * @apiVersion 0.1.0
 * @apiName get-drama
 * @apiGroup /x/dramalist/
 *
 * @apiParam {Number} dramalist_id 剧单 ID
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页显示条数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 5, // 剧集 ID
 *           "name": "剧集标题",
 *           "intro": "推荐语",
 *           "cover": "https://www.test.com/cover.jpg", // 剧集封面
 *           "cover_color": 12434877, // 封面 RGB 颜色值（10 进制表示）
 *           "cvs": ["加特林", "椰子"], // 声优（主役）
 *           "view_count": 1, // 播放次数
 *           "is_subscribed": 0, // 是否追剧。0：为追剧；1：已追剧
 *           "integrity": 1, // 完结度，1: 长篇未完结；2: 长篇完结；3: 全一期
 *           "catalog_name": "日文广播剧", // 剧集类型
 *           "type_name": "乙女", // 剧集分类
 *           "corner_mark": { // 无剧集角标时不返回该字段
 *             "text": "已购",
 *             "text_color": "#FFFFFF", // 优先判断 text_start_color/text_end_color，没有的话使用这个字段
 *             "text_start_color": "#FFFFFF", // 文字渐变起始颜色。没有时不下发该字段
 *             "text_end_color": "#FFFFFF", // 文字渐变结束颜色。没有时不下发该字段
 *             "bg_start_color": "#E66465",
 *             "bg_end_color": "#E66465",
 *             "left_icon_url": "https://www.test.com/icon.png"  // 无左侧图标时不返回该字段
 *           }
 *         }
 *       ],
 *       "pagination": {
 *         "p": 1,
 *         "maxpage": 2,
 *         "count": 5,
 *         "pagesize": 3
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200140001
 * @apiError (404) {String} info 剧单不存在
 */
func ActionDramaListGetDrama(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDramalistGetDramaParams(c)
	if err != nil {
		return nil, err
	}
	err = param.getDramaList()
	if err != nil {
		return nil, err
	}
	// WORKAROUND: iOS < 6.2.5 由于客户端会闪退，故不下发 text_start_color 和 text_end_color 角标颜色渐变字段
	v := util.AppVersions{IOS: "6.2.5", Android: ""}
	equip := c.Equip()
	if equip.IsOldApp(v) {
		for dramaID, cornerMark := range param.cornerMarkMap {
			cornerMark.TextStartColor = ""
			cornerMark.TextEndColor = ""
			param.cornerMarkMap[dramaID] = cornerMark
		}
	}
	return param.buildResp(), nil
}

func newDramalistGetDramaParams(c *handler.Context) (*dramalistGetDramaParams, error) {
	dramalistID, err := c.GetParamInt64("dramalist_id")
	if err != nil || dramalistID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param := new(dramalistGetDramaParams)
	param.Page, param.PageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 查询剧单是否存在
	exists, err := mdramalist.DramalistExists(dramalistID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !exists {
		return nil, actionerrors.ErrDramalistNotFound
	}
	param.DramalistID = dramalistID
	param.userID = c.UserID()
	return param, nil
}

func (param *dramalistGetDramaParams) getDramaList() error {
	// 查询剧单内剧集信息
	var err error
	param.dramalistDramasInfo, param.pagination, err = param.DramalistDramasListParam.List()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(param.dramalistDramasInfo) == 0 {
		return nil
	}

	// 获取剧单内剧集 ID
	dramaIDs := make([]int64, 0, len(param.dramalistDramasInfo))
	for _, drama := range param.dramalistDramasInfo {
		dramaIDs = append(dramaIDs, drama.DramaID)
	}

	// 查询剧单内全部过审剧集详情
	checkedPassDramas, err := dramainfo.ListDramaInfoByIDs(dramaIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(checkedPassDramas) == 0 {
		return nil
	}
	param.checkedPassDramaInfoMap = make(map[int64]dramainfo.RadioDramaDramainfo, len(checkedPassDramas))
	for _, drama := range checkedPassDramas {
		param.checkedPassDramaIDs = append(param.checkedPassDramaIDs, drama.ID)
		param.checkedPassDramaInfoMap[drama.ID] = drama
	}

	// 获取剧集角标信息
	param.cornerMarkMap, err = dramacornermarkstyle.GetDramaCornerMark(param.userID, checkedPassDramas)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	if param.userID > 0 {
		// 获取过审剧集中用户已追剧集 ID
		param.subscribedDramaMap, err = dramasubscription.UserSubscribedDramaMap(param.userID, param.checkedPassDramaIDs)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
	}

	// 获取剧集主役声优名
	param.cvNamesMap, err = param.getDramaCVNamesMap()
	if err != nil {
		return err
	}
	return nil
}

// getDramaCVNamesMap 获取剧集主役声优名
func (param *dramalistGetDramaParams) getDramaCVNamesMap() (map[int64][]string, error) {
	// 获取剧集关联声优 ID 列表
	episodeCVs, err := dramaepisodecv.ListDramaCVByDramaIDs(param.checkedPassDramaIDs, dramaepisodecv.TypeCharacterMain)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(episodeCVs) == 0 {
		return nil, nil
	}

	cvIDs := make([]int64, 0, len(episodeCVs))
	for _, cv := range episodeCVs {
		cvIDs = append(cvIDs, cv.CvID)
	}

	// 获取声优信息列表
	seiys, err := models.ListSeiysByIDs(util.Uniq(cvIDs))
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(seiys) == 0 {
		return nil, nil
	}
	seiyMap := util.ToMap(seiys, "ID").(map[int64]models.Mowangsksoundseiy)

	cvsMap := make(map[int64][]string, len(param.checkedPassDramaIDs))
	for _, cv := range episodeCVs {
		if seiy, ok := seiyMap[cv.CvID]; ok {
			cvsMap[cv.DramaID] = append(cvsMap[cv.DramaID], seiy.Name)
		}
	}
	return cvsMap, nil
}

func (param *dramalistGetDramaParams) buildResp() dramalistGetDramaResp {
	resp := dramalistGetDramaResp{
		Pagination: param.pagination,
		Data:       make([]dramalistDramaInfo, 0, len(param.checkedPassDramaIDs)),
	}

	catalogMap := map[int64]struct{}{dramainfo.CatalogIDCnRadioDrama: {}, dramainfo.CatalogIDCnCartoon: {}}
	for _, dramalistDrama := range param.dramalistDramasInfo {
		dramaInfo, ok := param.checkedPassDramaInfoMap[dramalistDrama.DramaID]
		if !ok {
			continue
		}
		typeName := dramaInfo.TypeName
		if _, ok = catalogMap[dramaInfo.Catalog]; ok {
			// 若剧集类型为中文广播剧或中文有声漫画，则 type_name 返回空字符串进行隐藏
			typeName = ""
		}
		item := dramalistDramaInfo{
			ID:          dramalistDrama.DramaID,
			Name:        *dramaInfo.Name,
			Intro:       dramalistDrama.Intro,
			Cover:       dramaInfo.CoverURL,
			CoverColor:  dramaInfo.CoverColor,
			ViewCount:   dramaInfo.ViewCount,
			Integrity:   *dramaInfo.Integrity,
			CatalogName: dramaInfo.CatalogName,
			TypeName:    typeName,
		}
		if _, ok = param.subscribedDramaMap[dramalistDrama.DramaID]; ok {
			item.IsSubscribed = util.BoolToInt(ok)
		}
		if cornerMark, ok := param.cornerMarkMap[dramalistDrama.DramaID]; ok {
			item.CornerMark = &cornerMark
		}
		if cvNames, ok := param.cvNamesMap[dramalistDrama.DramaID]; ok {
			item.CVs = cvNames
		} else {
			item.CVs = []string{}
		}
		resp.Data = append(resp.Data, item)
	}
	return resp
}
