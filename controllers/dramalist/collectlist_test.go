package dramalist

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestCollectListTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(collectListItem{}, "id", "title", "cover", "cover_color", "collect_count", "is_collected", "drama_count", "subscribed_drama_count")
	kc.Check(collectListResp{}, "data", "pagination")
}

func TestActionDramaListCollectList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=12&p=-1&pagesize=10", false, nil)
	result, err := ActionDramaListCollectList(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试没有收藏剧单时
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=820591&p=1&pagesize=2", false, nil)
	result, err = ActionDramaListCollectList(c)
	require.NoError(err)
	require.NotNil(result)
	resp := result.(*collectListResp)
	require.NotNil(resp)
	require.NotNil(resp.Data)
	assert.Empty(resp.Data)
	require.NotNil(resp.Pagination)
	assert.EqualValues(0, resp.Pagination.Count)
	assert.EqualValues(0, resp.Pagination.MaxPage)
	assert.EqualValues(1, resp.Pagination.P)
	assert.EqualValues(2, resp.Pagination.PageSize)

	// 测试有收藏剧单时
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=820592&p=1&pagesize=2", true, nil)
	result, err = ActionDramaListCollectList(c)
	require.NoError(err)
	require.NotNil(result)
	resp = result.(*collectListResp)
	require.NotNil(resp)
	require.NotNil(resp.Data)
	assert.Len(resp.Data, 2)

	assert.EqualValues(12, resp.Data[0].ID)
	assert.Equal("测试剧单标题 12", resp.Data[0].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		resp.Data[0].Cover)
	assert.EqualValues(12434877, resp.Data[0].CoverColor)
	assert.EqualValues(2, resp.Data[0].CollectCount)
	assert.EqualValues(1, resp.Data[0].DramaCount)
	assert.EqualValues(0, resp.Data[0].SubscribedDramaCount)

	assert.EqualValues(11, resp.Data[1].ID)
	assert.Equal("测试剧单标题 11", resp.Data[1].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		resp.Data[1].Cover)
	assert.EqualValues(12434877, resp.Data[1].CoverColor)
	assert.EqualValues(2, resp.Data[1].CollectCount)
	assert.EqualValues(1, resp.Data[1].DramaCount)
	assert.EqualValues(1, resp.Data[1].SubscribedDramaCount)

	assert.EqualValues(3, resp.Pagination.Count)
	assert.EqualValues(2, resp.Pagination.MaxPage)
	assert.EqualValues(1, resp.Pagination.P)
	assert.EqualValues(2, resp.Pagination.PageSize)
}

func TestNewCollectListParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=12&p=-1&pagesize=10", false, nil)
	param, err := newCollectListParams(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试用户 ID 参数错误的情况
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=0&p=1&pagesize=10", false, nil)
	param, err = newCollectListParams(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(param)

	// 测试用户不存在的情况
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=9999999999&p=1&pagesize=10", false, nil)
	param, err = newCollectListParams(c)
	assert.Equal(actionerrors.ErrNotFound(handler.CodeUserNotFound, "该用户不存在"), err)
	assert.Nil(param)

	// 测试用户已注销的情况
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=3013623&p=1&pagesize=10", false, nil)
	param, err = newCollectListParams(c)
	assert.Equal(actionerrors.ErrUserDeleted, err)
	assert.Nil(param)

	// 测试用户设置了隐私的情况
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=3013624&p=1&pagesize=10", false, nil)
	param, err = newCollectListParams(c)
	assert.Equal(actionerrors.ErrForbidden(handler.CodeUserLimit, "UP 主设置了隐私，无法访问"), err)
	assert.Nil(param)

	// 测试不传分页参数时
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=12", false, nil)
	param, err = newCollectListParams(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(12, param.CollectLister.UserID)
	assert.EqualValues(1, param.CollectLister.P)
	assert.EqualValues(20, param.CollectLister.PageSize)
	assert.EqualValues(0, param.visitorUserID)

	// 测试参数正常
	c = handler.NewTestContext(http.MethodGet, "/x/dramalist/collect-list?user_id=12&p=2&pagesize=10", true, nil)
	param, err = newCollectListParams(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(12, param.CollectLister.UserID)
	assert.EqualValues(2, param.CollectLister.P)
	assert.EqualValues(10, param.CollectLister.PageSize)
	assert.EqualValues(12, param.visitorUserID)
}

func TestCollectListParams_collectList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := &collectListParams{
		CollectLister: mcollectdramalist.CollectLister{
			P:        1,
			PageSize: 2,
			UserID:   820591,
		},
		visitorUserID: 12,
	}
	// 测试没有收藏的剧单时
	resp, err := param.collectList()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Data)
	assert.Empty(resp.Data)
	require.NotNil(resp.Pagination)
	assert.EqualValues(0, resp.Pagination.Count)
	assert.EqualValues(0, resp.Pagination.MaxPage)
	assert.EqualValues(1, resp.Pagination.P)
	assert.EqualValues(2, resp.Pagination.PageSize)

	// 测试有收藏信息时
	param.CollectLister.UserID = 820592
	resp, err = param.collectList()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Data)
	assert.Len(resp.Data, 2)

	assert.EqualValues(12, resp.Data[0].ID)
	assert.Equal("测试剧单标题 12", resp.Data[0].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		resp.Data[0].Cover)
	assert.EqualValues(12434877, resp.Data[0].CoverColor)
	assert.EqualValues(2, resp.Data[0].CollectCount)
	assert.EqualValues(1, resp.Data[0].DramaCount)
	assert.EqualValues(0, resp.Data[0].SubscribedDramaCount)

	assert.EqualValues(11, resp.Data[1].ID)
	assert.Equal("测试剧单标题 11", resp.Data[1].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		resp.Data[1].Cover)
	assert.EqualValues(12434877, resp.Data[1].CoverColor)
	assert.EqualValues(2, resp.Data[1].CollectCount)
	assert.EqualValues(1, resp.Data[1].DramaCount)
	assert.EqualValues(1, resp.Data[1].SubscribedDramaCount)

	// 验证 param.dramalistIDs
	assert.Equal([]int64{12, 11}, param.dramalistIDs)
}

func TestCollectListParams_fillDramalistInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := &collectListParams{
		CollectLister: mcollectdramalist.CollectLister{UserID: 820592},
		visitorUserID: 12,
	}
	// 测试剧单信息为空时
	collectListItems := make([]*collectListItem, 0)
	err := param.fillDramalistInfo(collectListItems)
	require.NoError(err)
	assert.Len(collectListItems, 0)

	// 测试剧单信息不为空，但剧单内没有剧集时
	param.dramalistIDs = []int64{100}
	collectListItems = []*collectListItem{{ID: 100, Title: "测试剧单", CollectCount: 1}}
	err = param.fillDramalistInfo(collectListItems)
	require.NoError(err)
	assert.Len(collectListItems, 1)
	// 测试返回默认值
	assert.EqualValues(100, collectListItems[0].ID)
	assert.EqualValues("测试剧单", collectListItems[0].Title)
	assert.EqualValues(1, collectListItems[0].CollectCount)
	assert.EqualValues(0, collectListItems[0].CoverColor)
	assert.EqualValues(0, collectListItems[0].DramaCount)
	assert.EqualValues(0, collectListItems[0].SubscribedDramaCount)

	// 测试剧单信息不为空，剧单内有过审剧集时
	param.dramalistIDs = []int64{12, 11}
	collectListItems = []*collectListItem{
		{ID: 12, Title: "测试剧单标题 12", CollectCount: 2},
		{ID: 11, Title: "测试剧单标题 11", CollectCount: 2},
	}
	err = param.fillDramalistInfo(collectListItems)
	require.NoError(err)
	assert.Len(collectListItems, 2)

	assert.EqualValues(12, collectListItems[0].ID)
	assert.Equal("测试剧单标题 12", collectListItems[0].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		collectListItems[0].Cover)
	assert.EqualValues(12434877, collectListItems[0].CoverColor)
	assert.EqualValues(2, collectListItems[0].CollectCount)
	assert.EqualValues(1, collectListItems[0].DramaCount)
	assert.EqualValues(0, collectListItems[0].SubscribedDramaCount)

	assert.EqualValues(11, collectListItems[1].ID)
	assert.Equal("测试剧单标题 11", collectListItems[1].Title)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL+"201604/15/9964ffc7ece0d56e65221d36dd9566b6083011.jpg"),
		collectListItems[1].Cover)
	assert.EqualValues(12434877, collectListItems[1].CoverColor)
	assert.EqualValues(2, collectListItems[1].CollectCount)
	assert.EqualValues(1, collectListItems[1].DramaCount)
	assert.EqualValues(1, collectListItems[1].SubscribedDramaCount)
}
