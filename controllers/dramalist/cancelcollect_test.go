package dramalist

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestActionDramaListCancelCollect(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	param := &collectParams{
		DramalistID: 0,
	}
	api := "/x/dramalist/cancel-collect"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	result, err := ActionDramaListCancelCollect(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试剧单不存在
	param = &collectParams{
		DramalistID: 10000,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionDramaListCancelCollect(c)
	assert.Equal(actionerrors.ErrDramalistNotFound, err)
	assert.Nil(result)

	// 测试取消收藏成功（未收藏剧单时，接口保持幂等性）
	param = &collectParams{
		DramalistID: 5,
	}
	// 删除收藏记录数据
	require.NoError(deleteCollectDramalist(c.UserID(), param.DramalistID))
	// 查询收藏剧单前剧单的收藏数
	beforeCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	// 测试取消收藏成功
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionDramaListCancelCollect(c)
	require.NoError(err)
	require.NotNil(result)
	resp := result.(*collectResp)
	assert.Equal("已取消收藏", resp.Msg)
	assert.Equal(mcollectdramalist.StatusNotCollected, resp.IsCollected)
	// 验证没有剧单收藏记录
	_, err = findCollectDramalist(c.UserID(), param.DramalistID)
	require.True(servicedb.IsErrNoRows(err))
	// 验证剧单收藏数量没有变化
	afterCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(beforeCollectCount, afterCollectCount)

	// 测试取消收藏成功（已收藏过该剧单时）
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.User().ID = 1
	// 验证取消收藏前有收藏记录
	mCollectDramalist1, err := findCollectDramalist(c.UserID(), param.DramalistID)
	require.NoError(err)
	// 测试取消成功
	result, err = ActionDramaListCancelCollect(c)
	require.NoError(err)
	require.NotNil(result)
	resp = result.(*collectResp)
	assert.Equal("已取消收藏", resp.Msg)
	assert.Equal(mcollectdramalist.StatusNotCollected, resp.IsCollected)
	// 验证之前的收藏记录已被删除
	mCollectDramalist2 := new(mcollectdramalist.MCollectDramalist)
	require.True(servicedb.IsErrNoRows(mcollectdramalist.MCollectDramalist{}.DB().Select("id").
		Where("id = ?", mCollectDramalist1.ID).
		Take(mCollectDramalist2).Error))
	// 验证剧单收藏数量已减 1
	afterCollectCount1, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(beforeCollectCount-1, afterCollectCount1)
}

func TestCollectParams_cancelCollect(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := &collectParams{
		DramalistID: 6,
		userID:      1,
	}

	// 测试用户已收藏剧单时
	// 验证取消收藏前有收藏记录
	mCollectDramalist, err := findCollectDramalist(param.userID, param.DramalistID)
	require.NoError(err)
	// 获取取消收藏剧单前剧单的收藏数
	beforeCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	// 取消收藏剧单
	err = param.cancelCollect()
	require.NoError(err)
	// 验证收藏记录已删除
	mCollectDramalist1 := new(mcollectdramalist.MCollectDramalist)
	require.True(servicedb.IsErrNoRows(mcollectdramalist.MCollectDramalist{}.DB().Select("id").
		Where("id = ?", mCollectDramalist.ID).
		Take(mCollectDramalist1).Error))
	// 验证收藏数量已减 1
	afterCollectCount, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(beforeCollectCount-1, afterCollectCount)

	// 测试用户未收藏剧单时取消收藏
	err = param.cancelCollect()
	require.NoError(err)
	// 验证收藏数量未减少
	afterCollectCount1, err := getDramalistCollectCount(param.DramalistID)
	require.NoError(err)
	assert.Equal(afterCollectCount, afterCollectCount1)
}
