package dramalist

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type collectParams struct {
	DramalistID int64 `form:"dramalist_id" json:"dramalist_id"` // 剧单 ID

	userID int64 // 用户 ID
}

type collectResp struct {
	Msg         string `json:"msg"`
	IsCollected int    `json:"is_collected"`
}

// ActionDramaListCollect 收藏剧单
/**
 * @api {post} /x/dramalist/collect 收藏剧单
 *
 * @apiVersion 0.1.0
 * @apiName collect
 * @apiGroup /x/dramalist/
 *
 * @apiPermission user
 *
 * @apiParam {Number} dramalist_id 剧单 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "剧单收藏成功，可在「我听-收藏」中查看",
 *       "is_collected": 1 // 是否收藏。0：未被收藏；1：已收藏
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200140001
 * @apiError (404) {String} info 剧单不存在
 *
 * @apiError (400) {Number} code 200140003
 * @apiError (400) {String} info 不能收藏自己的剧单
 */
func ActionDramaListCollect(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newCollectOrCancelParams(c)
	if err != nil {
		return nil, err
	}

	err = param.collect()
	if err != nil {
		return nil, err
	}

	// Web 和 App 使用不同的文案
	msg := "剧单收藏成功，可在「我听-收藏」中查看"
	if !c.Equip().FromApp {
		msg = "剧单收藏成功，可前往 App > 我听 > 收藏中查看"
	}
	return &collectResp{
		Msg:         msg,
		IsCollected: mcollectdramalist.StatusCollected,
	}, nil
}

func newCollectOrCancelParams(c *handler.Context) (*collectParams, error) {
	param := new(collectParams)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.DramalistID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 判断剧单是否存在
	mDramalist, err := mdramalist.FindOne(param.DramalistID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if mDramalist == nil {
		return nil, actionerrors.ErrDramalistNotFound
	}
	param.userID = c.UserID()
	if mDramalist.UserID == param.userID {
		// 不能收藏自己的剧单
		return nil, actionerrors.ErrCollectDramalistLimit
	}

	return param, nil
}

// collect 收藏剧单
func (param *collectParams) collect() error {
	// 判断是否收藏过该剧单
	collected, err := mcollectdramalist.IsCollected(param.userID, param.DramalistID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if collected {
		// 若已收藏该剧单则直接返回，保持结果幂等性
		return nil
	}
	mCollectDramalist := mcollectdramalist.MCollectDramalist{
		DramalistID: param.DramalistID,
		UserID:      param.userID,
	}
	err = servicedb.Tx(service.MainDB, func(tx *gorm.DB) error {
		// 插入剧单收藏记录
		err := tx.Create(&mCollectDramalist).Error
		if err != nil {
			if servicedb.IsUniqueError(err) {
				// 发生唯一索引错误时不报错，直接返回收藏成功
				logger.WithFields(logger.Fields{
					"user_id":      param.userID,
					"dramalist_id": param.DramalistID,
				}).Warn(err)
				// PASS
				return nil
			}
			return err
		}
		// 剧单收藏数量 +1
		err = tx.Table(mdramalist.MDramalist{}.TableName()).
			Where("id = ?", param.DramalistID).
			Update(map[string]interface{}{
				"collect_count": gorm.Expr("collect_count + ?", 1),
				"modified_time": goutil.TimeNow().Unix(),
			}).Error
		return err
	})
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	return nil
}
