package dramalist

import (
	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscription"
	"github.com/MiaoSiLa/missevan-go/models/drama/mcollectdramalist"
	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalistdramamap"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// collectListParams 请求用户已收藏剧单列表参数
type collectListParams struct {
	mcollectdramalist.CollectLister

	visitorUserID int64   // 访问者的 ID
	dramalistIDs  []int64 // 收藏的剧单 IDs
}

// collectListItem 用户已收藏剧单列表字段
type collectListItem struct {
	ID                   int64  `json:"id"`                     // 剧单 ID
	Title                string `json:"title"`                  // 剧单标题
	Cover                string `json:"cover"`                  // 剧单封面
	CoverColor           int    `json:"cover_color"`            // 封面 RGB 颜色值（10 进制表示）
	CollectCount         int64  `json:"collect_count"`          // 剧单被收藏数量
	IsCollected          int    `json:"is_collected"`           // 是否收藏。1：已收藏
	DramaCount           int64  `json:"drama_count"`            // 剧单内剧集数量
	SubscribedDramaCount int64  `json:"subscribed_drama_count"` // 剧单内已追（订阅）剧集数量
}

// collectListResp 用户已收藏剧单列表响应内容
type collectListResp struct {
	Data       []*collectListItem `json:"data"`
	Pagination util.Pagination    `json:"pagination"`
}

// ActionDramaListCollectList 获取用户已收藏剧单列表
/**
 * @api {get} /x/dramalist/collect-list{?user_id,p,pagesize} 获取用户已收藏剧单列表
 *
 * @apiVersion 0.1.0
 * @apiName collect-list
 * @apiGroup /x/dramalist/
 *
 * @apiParam {Number} user_id 被访问的用户 ID
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=30] 每页显示条数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 1, // 剧单 ID
 *           "title": "剧单标题",
 *           "cover": "https://www.test.com/cover.jpg", // 剧单封面
 *           "cover_color": 12434877, // 封面 RGB 颜色值（10 进制表示）
 *           "collect_count": 233, // 剧单被收藏数量
 *           "is_collected": 1, // 是否收藏。1：已收藏
 *           "drama_count": 20, // 剧单内剧集数量
 *           "subscribed_drama_count": 13 // 剧单内当前用户（访问者）已追（订阅）剧集数量
 *         }
 *       ],
 *       "pagination": {
 *         "p": 1,
 *         "maxpage": 2,
 *         "count": 5,
 *         "pagesize": 3
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200140001
 * @apiError (404) {String} info 剧单不存在
 *
 * @apiError (403) {Number} code 300010005
 * @apiError (403) {String} info 该用户已注销
 */
func ActionDramaListCollectList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newCollectListParams(c)
	if err != nil {
		return nil, err
	}

	// 获取剧单收藏列表
	return param.collectList()
}

func newCollectListParams(c *handler.Context) (*collectListParams, error) {
	var err error
	param := new(collectListParams)
	param.CollectLister.P, param.CollectLister.PageSize, err = c.GetParamPage(&handler.PageOption{DefaultPageSize: 30})
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	param.CollectLister.UserID, err = c.GetParamInt64("user_id")
	if err != nil || param.CollectLister.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	// 判断用户是否存在
	userInfo, err := user.FindByUserID(param.CollectLister.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if userInfo == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUserNotFound, "该用户不存在")
	}

	if user.IsDeleted(userInfo.Confirm) {
		// 判断用户是否注销，返回指定 code，客户端根据 code 做相应处理
		return nil, actionerrors.ErrUserDeleted
	}

	if user.IsPrivacy(userInfo.Confirm) {
		// 隐私设置用户不对外展示个人主页
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "UP 主设置了隐私，无法访问")
	}
	param.visitorUserID = c.UserID()
	// TODO: 之后需要根据用户配置（是否在个人主页公开“我的收藏”）来判断是否获取收藏列表

	return param, nil
}

// collectList 收藏的剧单列表
func (param *collectListParams) collectList() (*collectListResp, error) {
	collectList, pagination, err := param.CollectLister.List()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	collectListLen := len(collectList)
	resp := &collectListResp{
		Data:       make([]*collectListItem, 0, collectListLen),
		Pagination: pagination,
	}
	if collectListLen == 0 {
		return resp, nil
	}

	defaultCover := service.Storage.Parse(params.URL.DefaultCoverURL)
	for _, v := range collectList {
		param.dramalistIDs = append(param.dramalistIDs, v.ID)

		resp.Data = append(resp.Data, &collectListItem{
			ID:           v.ID,
			Title:        v.Title,
			CollectCount: v.CollectCount,
			IsCollected:  mcollectdramalist.StatusCollected,
			// 给剧单设置默认封面
			Cover: defaultCover,
		})
	}

	if err = param.fillDramalistInfo(resp.Data); err != nil {
		return nil, err
	}

	return resp, nil
}

// fillDramalistInfo 填充剧单的其他信息（剧单封面、剧单内剧集数量、剧单内已追剧集数量）
// TODO: 上线后观察接口性能（用户收藏的剧单中剧集较多时可能会有性能问题）
func (param *collectListParams) fillDramalistInfo(collectListItems []*collectListItem) error {
	if len(collectListItems) == 0 {
		return nil
	}

	// 根据剧单 IDs 获取剧单下的剧集 IDs map（以剧单 ID 为 key）
	dramaIDsMap, err := mdramalistdramamap.FindDramaIDsMapByDramalistIDs(param.dramalistIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramaIDsMap) == 0 {
		// 目前剧单都是后台配置，若剧单未配置过审剧集，需要运营处理
		logger.WithField("dramalist_ids", param.dramalistIDs).Error("所有剧单中都没有任何剧集，请联系运营处理")
		// PASS
		return nil
	}

	var allDramaIDs []int64
	for _, dramaIDs := range dramaIDsMap {
		allDramaIDs = append(allDramaIDs, dramaIDs...)
	}
	allDramaIDs = util.Uniq(allDramaIDs)
	// 获取已过审剧集信息（allDramaIDs 最大长度为 2000，需分批获取）
	checkedPassDramaInfos, err := dramainfo.ListDramaInfoByIDs(allDramaIDs)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(checkedPassDramaInfos) == 0 {
		// 目前剧单都是后台配置，若剧单未配置过审剧集，需要运营处理
		logger.WithField("dramalist_ids", param.dramalistIDs).Error("所有剧单中都没有过审的剧集，请联系运营处理")
		// PASS
		return nil
	}
	checkedPassDramaInfoMap := util.ToMap(checkedPassDramaInfos, "ID").(map[int64]dramainfo.RadioDramaDramainfo)

	// 已过审剧集 IDs
	checkedPassDramaIDs := make([]int64, 0, len(checkedPassDramaInfos))
	for _, dramaInfo := range checkedPassDramaInfos {
		checkedPassDramaIDs = append(checkedPassDramaIDs, dramaInfo.ID)
	}

	var userSubscribedDramaMap map[int64]struct{}
	if param.visitorUserID != 0 {
		// 获取当前用户（访问者）已追（订阅）的剧集 IDs
		userSubscribedDramaMap, err = dramasubscription.UserSubscribedDramaMap(param.visitorUserID, checkedPassDramaIDs)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
	}

	// 剧单信息 map（以剧单 ID 为 key）
	collectListItemMap := util.ToMap(collectListItems, "ID").(map[int64]*collectListItem)
	for dramalistID, dramaIDs := range dramaIDsMap {
		// 初始化是否已找到剧单封面
		foundDramaCover := false
		item, ok := collectListItemMap[dramalistID]
		if !ok {
			logger.WithField("dramalist_id", dramalistID).Error("剧单不存在")
			// PASS
			continue
		}
		for _, dramaID := range dramaIDs {
			if util.HasElem(checkedPassDramaIDs, dramaID) {
				// 剧单内已过审的剧集数量 +1
				item.DramaCount++

				if _, ok = userSubscribedDramaMap[dramaID]; ok {
					// 剧单内已追（订阅）剧集数量 +1
					item.SubscribedDramaCount++
				}

				if !foundDramaCover {
					// 获取剧单中第一个已过审剧集封面作为剧单封面
					dramaInfo, ok := checkedPassDramaInfoMap[dramaID]
					if ok {
						item.Cover = dramaInfo.CoverURL
						item.CoverColor = dramaInfo.CoverColor
						// 已找到剧单封面
						foundDramaCover = true
					}
				}
			}
		}
		if !foundDramaCover {
			// 未找到剧单封面，说明剧单中没有过审的剧集，目前剧单都是后台配置，若剧单未配置过审剧集，需要运营处理
			logger.WithField("dramalist_id", dramalistID).Error("剧单中没有过审的剧集，请联系运营处理")
			// PASS
		}
	}

	for i, v := range collectListItems {
		item, ok := collectListItemMap[v.ID]
		if ok {
			collectListItems[i] = item
		}
	}

	return nil
}
