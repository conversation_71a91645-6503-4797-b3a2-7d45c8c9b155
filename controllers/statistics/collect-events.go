package statistics

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 页面类型
const (
	PageTypeNative     = iota + 1 // 原生界面
	PageTypeWebViewJSB            // 客户端 webview
	PageTypeWeb                   // 浏览器网页
)

// EventIDSegments 定义了正确的 event_id 应该包含的段数（由 . 分割），例：public.ad.source.0.0.sys
const EventIDSegments = 6

type eventFromUser struct {
	EventID       string `json:"event_id"`
	EventCategory int    `json:"event_category"`
	EventIDFrom   string `json:"event_id_from"`
	LoadType      int    `json:"load_type"`
	PVStart       int64  `json:"pv_start"`
	PVEnd         int64  `json:"pv_end"`
	Duration      int64  `json:"duration"`
	Network       int    `json:"network"`
	Ctime         int64  `json:"ctime"`
	UploadTime    int64  `json:"upload_time"`
	FTS           int64  `json:"fts"`
	Channel       string `json:"channel"`
	UA            string `json:"ua"`
	PageType      int64  `json:"page_type"`
	Version       string `json:"version"`   // 客户端版本号
	DeviceID      string `json:"device_id"` // 设备信息，需要为 JSON 格式，参考 https://github.com/MiaoSiLa/requirements-doc/issues/1056

	ExtendedFields json.RawMessage `json:"extended_fields,omitempty"` // NOTICE: 如果没有 omitempty 会导致空值时解析成 "null"
	UserID         int64           `json:"user_id"`                   // 登录的用户 id，未登录是 0
	Staging        bool            `json:"staging,omitempty"`         // 客户端上报的该数据是否是测试环境的数据
	UUID           string          `json:"uuid,omitempty"`
}

// Event 埋点事件，与 2020 年 9 月及之后的数据库结构相对应
// 新版本不会 9 月前发，所以新版本的数据不会存到 8 月的表里，会写到 9 月更新后的表里
type Event struct {
	UserID         int64         `gorm:"column:user_id"` // 登录的用户 id，未登录是 0
	EventID        string        `gorm:"column:event_id"`
	EventCategory  int           `gorm:"column:event_category"`
	EventIDFrom    string        `gorm:"column:event_id_from"`
	LoadType       int           `gorm:"column:load_type"`
	PageType       int64         `gorm:"column:page_type"`
	PVStart        int64         `gorm:"column:pv_start"`
	PVEnd          int64         `gorm:"column:pv_end"`
	Duration       int64         `gorm:"column:duration"`
	FTS            int64         `gorm:"column:fts"`
	Channel        string        `gorm:"column:channel"`
	Version        string        `gorm:"column:version"`
	OS             util.Platform `gorm:"column:os"`
	EquipID        string        `gorm:"column:equip_id"`
	BUVID          string        `gorm:"column:buvid"`
	Network        int           `gorm:"column:network"`
	IP             string        `gorm:"column:ip"`
	ExtendedFields *string       `gorm:"column:extended_fields"`
	CreateTime     int64         `gorm:"column:create_time"`
	ModifiedTime   int64         `gorm:"column:modified_time"`
	Ctime          int64         `gorm:"column:ctime"`
	UploadTime     int64         `gorm:"column:upload_time"`
	DeviceID       *string       `gorm:"column:device_id"`
	UUID           string        `gorm:"column:uuid"`
	UA             string        `gorm:"column:ua"`
}

// TableName 返回当月对应的表名
func (Event) TableName() string {
	return "events_tracking_log_" + util.TimeNow().Format("20060102")
}

type collectEventsTask struct {
	events     []*eventFromUser
	eventsToDB []Event
	equip      *util.Equipment
	buvid      string
	ip         string
	channel    string
	userAgent  string
}

func (t *collectEventsTask) load(c *handler.Context) error {
	// 解析 json
	err := c.Bind(&t.events)
	if err != nil {
		return handler.ErrBadRequest
	}

	if len(t.events) <= 0 {
		return handler.ErrBadRequest
	}

	// 获取公共参数
	equip := c.Equip()
	if equip == nil {
		return handler.ErrBadRequest
	}
	t.ip = c.ClientIP()
	t.equip = equip
	t.buvid = c.BUVID()
	t.channel = c.C.GetHeader("channel")
	t.userAgent = c.UserAgent()

	if len(t.events) > 1000 {
		logger.WithFields(logger.Fields{"user_id": c.UserID(), "equip_id": equip.EquipID, "buvid": t.buvid,
			"ip": t.ip}).Warn("There are more than 1000 records in this request of collect-events.")
	}
	return t.get()
}

func (t *collectEventsTask) get() error {
	// TODO: 后续迁移到 consumer 后，改成直接从本地 ipip 数据库获取，不调用 RPC 接口。
	ipInfo, err := goclient.GetIPInfo(nil, t.ip)
	if err != nil {
		logger.WithFields(logger.Fields{"ip": t.ip}).Warnf("GetIPInfo error: %v", err)
		// PASS
	}

	now := util.TimeNow()
	eventsToDB := make([]Event, 0, len(t.events))

	isProd := util.IsProdEnv()
	// 根据公共参数填充 events
	for _, event := range t.events {
		// 线上不接收测试环境的数据
		if isProd && event.Staging {
			continue
		}

		// 测试环境中 event_id 错误的话不入库，以便测试时发现没有数据可以排查
		if event.Staging && len(strings.Split(event.EventID, ".")) != EventIDSegments {
			continue
		}

		// 判断新旧数据
		var newData bool
		switch t.equip.OS {
		case util.IOS:
			// iOS 可能同时包含新旧数据
			// iOS 传了 version 字段为新数据，否则为旧数据
			newData = event.Version != ""
		case util.Android:
			// 安卓根据版本号判断是否为新旧数据
			newData = !t.equip.IsAppOlderThan("", "5.4.4")
		case util.HarmonyOS:
			newData = true
		default:
			newData = true
		}
		if !newData {
			// 旧数据可直接跳过，不需要记录
			continue
		}

		newEvent := Event{
			UserID:        event.UserID,
			EventID:       event.EventID,
			EventCategory: event.EventCategory,
			EventIDFrom:   event.EventIDFrom,
			LoadType:      event.LoadType,
			PVStart:       event.PVStart,
			PVEnd:         event.PVEnd,
			Duration:      event.Duration,
			Network:       event.Network,
			FTS:           event.FTS,
			Channel:       event.Channel,
			OS:            t.equip.OS,
			IP:            t.ip,
			EquipID:       t.equip.EquipID,
			BUVID:         t.buvid,
			ModifiedTime:  now.Unix(),
			UUID:          event.UUID,
			UA:            subStrUA(event.UA, t.userAgent),
		}
		if newEvent.Channel == "" {
			newEvent.Channel = t.channel
		}
		if event.Version != "" {
			newEvent.Version = event.Version
		} else if event.UA != "" {
			ua := util.NewEquipment(event.UA)
			newEvent.Version = ua.AppVersion
		}
		if newEvent.Version == "" {
			newEvent.Version = t.equip.AppVersion
		}

		if event.PageType != 0 {
			newEvent.PageType = event.PageType
		} else {
			newEvent.PageType = PageTypeNative
		}
		if event.UploadTime != 0 {
			newEvent.UploadTime = event.UploadTime
		} else {
			newEvent.UploadTime = int64(util.NewTimeUnixMilli(now))
		}
		if event.DeviceID != "" {
			var deviceID struct{}
			// Unmarshal 验证了客户端传过来的 DeviceID 为 json 对象的字符串
			if json.Unmarshal([]byte(event.DeviceID), &deviceID) == nil {
				newEvent.DeviceID = &event.DeviceID
			}
		}
		newEvent.Ctime = event.Ctime
		newEvent.CreateTime = newEvent.Ctime / 1000

		var extendedFields map[string]interface{}
		if event.ExtendedFields == nil {
			extendedFields = make(map[string]interface{}, 1)
		} else {
			var mapFields map[string]string
			// Unmarshal 验证了客户端传过来的 ExtendedFields 只能为 map[string]string 格式
			if err := json.Unmarshal(event.ExtendedFields, &mapFields); err != nil {
				byteArray, _ := json.Marshal(event)
				fields := logger.Fields{"equip_id": t.equip.EquipID, "data": string(byteArray)}
				logger.WithFields(fields).Warnf("unmarshal data error: %v", err)
				// PASS
				continue
			}
			extendedFields = make(map[string]interface{}, len(mapFields)+1)
			for k, v := range mapFields {
				extendedFields[k] = v
			}
		}
		if !ipInfo.IsEmpty() {
			extendedFields["geoip"] = ipInfo
		}
		if len(extendedFields) > 0 {
			extendedFieldsBytes, err := json.Marshal(extendedFields)
			if err != nil {
				return handler.NewActionError(http.StatusInternalServerError, handler.CodeUnknownError, err.Error())
			}
			extendedFieldsStr := string(extendedFieldsBytes)
			newEvent.ExtendedFields = &extendedFieldsStr
		}

		eventsToDB = append(eventsToDB, newEvent)
	}
	t.eventsToDB = eventsToDB

	return nil
}

func (t *collectEventsTask) process(db *gorm.DB) error {
	// 保存
	return servicedb.SplitBatchInsert(db, Event{}.TableName(), t.eventsToDB, 1000, true)
}

// ActionCollectEvents 采集埋点信息
/**
 * @api {post} /statistics/collect-events 采集埋点信息
 * @apiDescription 客户端调用时机的举例（客户端在多个条件都满足的时候，只调用一次即可）：
 * 都是在有数据的情况下：1. 每隔一段时间上报（比如 15 分钟），当数据积累超过一定大小（比如 15 条）时，立即上报; 2. 切到后台的时候; 3. 登录的时候
 * 4. 退出登录的时候; 5. app 升级之后调用把老数据上传; 6. app 启动
 * 支持客户端通过 gzip 压缩数据
 *
 * @apiVersion 0.1.0
 * @apiName collect-events
 * @apiGroup statistics
 *
 * @apiParamExample {json} Request-Example:
 *     [
 *       {
 *         "event_id": "xxxx", // 事件 ID
 *         "event_category": 1, // 事件类型 1. PAGEVIEW; 2. CLICK; 3. EXPOSURE. 下面有些字段根据事件类型可不传
 *         "event_id_from": "xxxx", // 来源页面
 *         "load_type": 0, // PV 的浏览类型 0. 正常浏览; 1. 回退浏览
 *         "pv_start": 1567750756000, // 页面进入时间，需要传毫秒时间戳
 *         "pv_end": 1567750848000, // 页面离开时间，需要传毫秒时间戳
 *         "duration": 1000, // 页面展现的时长. 单位毫秒
 *         "network": 1, // 0. UNKNOWN; 1. WIFI; 2. CELLULAR; 3. OFFLINE; 4. OTHERNET
 *         "ctime": 1567750890000, // 事件发生时间，为毫秒级时间戳
 *         "channel": "xxx", // 渠道标识
 *         "ua": "MissEvanApp/4.2.4 (iOS;12.0.1;iPhone7,2)", // User-Agent
 *         "version": "4.2.4", // 客户端版本
 *         "page_type": 1,
 *         "upload_time": 1567750890000, // 客户端上传时间，为毫秒级时间戳
 *         "fts": 1567750890000, // App 首次运行时间，毫秒级时间戳
 *         "device_id": "{}", // 设备信息，是一个 JSON 字符串，参考 https://github.com/MiaoSiLa/requirements-doc/issues/1056
 *
 *         // 可选参数：
 *         "user_id": 12, // 用户 ID，未登录可不传
 *         "extended_fields": {
 *           "args1": "1",
 *           "args2": "2",
 *           "args3": "3"
 *         }
 *       }
 *     ]
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "保存成功"
 *     }
 */
func ActionCollectEvents(c *handler.Context) (handler.ActionResponse, error) {
	var t collectEventsTask
	err := t.load(c)
	if err != nil {
		return nil, err
	}

	err = t.process(service.LogDB)
	if err != nil {
		return nil, err
	}

	return "保存成功", nil
}

// subStrUA 返回 ua 字段值（ua 长度最多为 500）
// 优先从请求体中获取 ua，如请求体中不存在，则使用请求头中的 ua
func subStrUA(bodyUA, headerUA string) string {
	ua := bodyUA
	if ua == "" {
		ua = headerUA
	}
	// 数据库中该字段的长度最多为 500
	return util.Substr(ua, 0, 500)
}
