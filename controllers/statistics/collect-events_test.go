package statistics

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCollectEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	nowSec := now.Unix()
	nowMilli := now.UnixMilli()
	util.SetTimeNow(func() time.Time {
		return now
	})
	defer util.SetTimeNow(nil)

	ua := "MissEvanApp/4.3.4 (iOS;12.0.1;iPhone7,2)"
	c := handler.NewTestContext("POST", "/", true, []eventFromUser{{
		EventID:       "1",
		EventCategory: 2,
		EventIDFrom:   "xx",
		LoadType:      1,
		PVStart:       1567750756,
		PVEnd:         1567750848,
		Duration:      1000,
		Network:       1,
		Ctime:         nowMilli,
		Version:       "4.3.4",
		UserID:        12,
	}})
	c.C.Request.Header.Set("Content-Type", "application/json")
	c.C.Request.Header.Set("channel", "new_channel")
	c.C.Request.Header.Set("User-Agent", ua)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "equip_id",
		Value: "test_equip_id",
	})
	c.SetClientIP("127.0.0.1")

	var task collectEventsTask
	assert.NoError(task.load(c))

	require.Len(task.eventsToDB, 1)
	actualEvent := task.eventsToDB[0]
	// The content of ExtendedFields is tested in tests below.
	// We don't care about the content of ExtendedFields in this test, so just assert it is not nil.
	// And then set it to nil to compare the rest of the fields.
	assert.NotNil(actualEvent.ExtendedFields)
	actualEvent.ExtendedFields = nil
	assert.Equal(Event{
		UserID:        12,
		EventID:       "1",
		EventCategory: 2,
		EventIDFrom:   "xx",
		LoadType:      1,
		PageType:      1,
		PVStart:       1567750756,
		PVEnd:         1567750848,
		Duration:      1000,
		Channel:       "new_channel",
		Version:       "4.3.4",
		OS:            util.IOS,
		EquipID:       "test_equip_id",
		Network:       1,
		IP:            "127.0.0.1",
		CreateTime:    nowSec,
		ModifiedTime:  nowSec,
		Ctime:         nowMilli,
		UploadTime:    nowMilli,
		UA:            ua,
	}, actualEvent)

	tutil.TestRollbackTx(t, service.LogDB, func(tx *gorm.DB) error {
		return task.process(tx)
	})
}

func strPtr(s string) *string {
	return &s
}

func TestGet(t *testing.T) {
	now := util.TimeNow()
	nowSec := now.Unix()
	nowMilli := now.UnixMilli()
	util.SetTimeNow(func() time.Time {
		return now
	})
	defer util.SetTimeNow(nil)

	baseTask := collectEventsTask{
		equip:   util.NewEquipment("MissEvanApp/4.3.4 (iOS;12.0.1;iPhone7,2)"),
		ip:      "127.0.0.1",
		channel: "channel",
	}
	baseEvent := eventFromUser{
		EventID:       "1",
		EventCategory: 2,
		EventIDFrom:   "xx",
		LoadType:      1,
		PVStart:       1567750756,
		PVEnd:         1567750848,
		Duration:      1000,
		Network:       1,
		Ctime:         nowMilli,
		UA:            "MissEvanApp/4.2.4 (iOS;12.0.1;iPhone7,2)",
		Version:       "4.2.4",
		UserID:        12,
		Staging:       false,
	}

	t.Run("TestBasic", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		task := baseTask
		event := baseEvent
		event.UUID = "fake_uuid"
		task.events = []*eventFromUser{&event}
		assert.NoError(task.get())

		require.Len(task.eventsToDB, 1)
		actualEvent := task.eventsToDB[0]
		// The content of ExtendedFields is tested in tests below.
		// We don't care about the content of ExtendedFields in this test, so just assert it is not nil.
		// And then set it to nil to compare the rest of the fields.
		assert.NotNil(actualEvent.ExtendedFields)
		actualEvent.ExtendedFields = nil
		assert.Equal(Event{
			UserID:        event.UserID,
			EventID:       event.EventID,
			EventCategory: event.EventCategory,
			EventIDFrom:   event.EventIDFrom,
			LoadType:      event.LoadType,
			PageType:      PageTypeNative,
			PVStart:       event.PVStart,
			PVEnd:         event.PVEnd,
			Duration:      event.Duration,
			FTS:           event.FTS,
			Channel:       task.channel,
			Version:       "4.2.4",
			OS:            util.IOS,
			EquipID:       task.equip.EquipID,
			BUVID:         task.buvid,
			Network:       event.Network,
			IP:            task.ip,
			CreateTime:    nowSec,
			ModifiedTime:  nowSec,
			Ctime:         nowMilli,
			UploadTime:    nowMilli,
			DeviceID:      nil,
			UUID:          event.UUID,
			UA:            event.UA,
		}, actualEvent)
	})

	t.Run("TestDeviceID", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		tests := []struct {
			deviceID string
			want     *string
		}{
			{`123`, nil},
			{`"123"`, nil},
			{`abc`, nil},
			{`"abc"`, nil},
			{`["a","b","c"]`, nil},
			{`{"open_udid":"xx","buvid"}`, nil},
			{`{"open_udid":"xx","buvid":"xxx","idfa":"xx"}`, strPtr(`{"open_udid":"xx","buvid":"xxx","idfa":"xx"}`)},
		}
		task := baseTask
		for _, test := range tests {
			event := baseEvent
			event.DeviceID = test.deviceID
			task.events = append(task.events, &event)
		}
		assert.NoError(task.get())

		require.Len(task.eventsToDB, len(tests))
		for i, test := range tests {
			actual := task.eventsToDB[i].DeviceID
			if test.want == nil {
				assert.Nil(actual)
			} else {
				require.NotNil(actual)
				assert.JSONEq(*test.want, *actual)
			}
		}
	})

	t.Run("TestIgnoreStagingEventsWithWrongEventID", func(t *testing.T) {
		assert := assert.New(t)

		task := baseTask
		task.events = make([]*eventFromUser, 10)
		for i := range task.events {
			event := baseEvent
			event.Staging = true
			if i%2 == 0 {
				event.EventID = "public.ad.source.0.0.sys"
			} else {
				event.EventID = "wrong.event.id"
			}
			task.events[i] = &event
		}

		assert.NoError(task.get())
		assert.Len(task.eventsToDB, len(task.events)/2)
	})

	t.Run("TestIgnoreStagingEventsInProd", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		require.NoError(os.Setenv(util.EnvDeploy, util.DeployEnvProd))

		task := baseTask
		task.events = make([]*eventFromUser, 10)
		for i := range task.events {
			event := baseEvent
			if i%2 == 0 {
				event.Staging = true
			}
			task.events[i] = &event
		}

		assert.NoError(task.get())
		assert.Len(task.eventsToDB, len(task.events)/2)
	})

	t.Run("TestExtendedFields", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		rpcCalls := 0
		ipInfo := goclient.IPInfo{
			CityName:    "上海",
			RegionName:  "上海",
			CountryCode: "CN",
			CountryName: "中国",
			ISP:         "电信",
		}
		ipInfoStr, err := json.Marshal(ipInfo)
		require.NoError(err)
		t.Cleanup(mrpc.SetMock("go://util/geoip", func(i interface{}) (interface{}, error) {
			rpcCalls++
			return ipInfo, nil
		}))

		type test struct {
			extendedFields json.RawMessage
			want           json.RawMessage
		}
		validTests := []test{
			{
				nil,
				json.RawMessage(fmt.Sprintf(`{"geoip":%s}`, ipInfoStr)),
			},
			{
				json.RawMessage(`{}`),
				json.RawMessage(fmt.Sprintf(`{"geoip":%s}`, ipInfoStr)),
			},
			{
				json.RawMessage(`{"a":"123"}`),
				json.RawMessage(fmt.Sprintf(`{"a":"123","geoip":%s}`, ipInfoStr)),
			},
			{
				json.RawMessage(`{"a":"123","b":"456"}`),
				json.RawMessage(fmt.Sprintf(`{"a":"123","b":"456","geoip":%s}`, ipInfoStr)),
			},
			{
				json.RawMessage(`{"a":"123","geoip":"456"}`),
				json.RawMessage(fmt.Sprintf(`{"a":"123","geoip":%s}`, ipInfoStr)),
			},
		}
		tests := append(validTests, []test{
			{json.RawMessage(`[1,2,3]`), nil},
			{json.RawMessage(`{"a":123}`), nil},
			{json.RawMessage(`{"a":{"b":"123"}}`), nil},
		}...)
		task := baseTask
		task.ip = "*******"
		for _, test := range tests {
			event := baseEvent
			event.ExtendedFields = test.extendedFields
			task.events = append(task.events, &event)
		}
		assert.NoError(task.get())
		assert.Equal(1, rpcCalls)

		require.Len(task.eventsToDB, len(validTests))
		for i, test := range validTests {
			actual := task.eventsToDB[i].ExtendedFields
			require.NotNil(actual)
			assert.JSONEq(string(test.want), *actual)
		}
	})

	t.Run("TestGeoIPInfo", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		var ipErr bool
		ipInfo := goclient.IPInfo{
			CityName:    "上海",
			RegionName:  "上海",
			CountryCode: "CN",
			CountryName: "中国",
			ISP:         "电信",
		}
		ipInfoStr, err := json.Marshal(ipInfo)
		require.NoError(err)
		t.Cleanup(mrpc.SetMock("go://util/geoip", func(i interface{}) (interface{}, error) {
			if ipErr {
				return nil, errors.New("error")
			}
			return ipInfo, nil
		}))

		tests := []struct {
			ipErr          bool
			extendedFields json.RawMessage
			want           json.RawMessage
		}{
			{
				true, nil,
				nil,
			},
			{
				true, json.RawMessage(`{"a":"123"}`),
				json.RawMessage(`{"a":"123"}`),
			},
			{
				false, nil,
				json.RawMessage(fmt.Sprintf(`{"geoip":%s}`, ipInfoStr)),
			},
			{
				false, json.RawMessage(`{"a":"123"}`),
				json.RawMessage(fmt.Sprintf(`{"a":"123","geoip":%s}`, ipInfoStr)),
			},
		}

		for _, test := range tests {
			task := baseTask
			task.ip = "*******"
			event := baseEvent
			event.ExtendedFields = test.extendedFields
			task.events = []*eventFromUser{&event}
			ipErr = test.ipErr

			assert.NoError(task.get())

			require.Len(task.eventsToDB, 1)
			actual := task.eventsToDB[0].ExtendedFields
			if test.want == nil {
				assert.Nil(actual)
			} else {
				require.NotNil(actual)
				assert.JSONEq(string(test.want), *actual)
			}
		}
	})
}

func TestSubStrUA(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(subStrUA("bodyua", "headerua"), "bodyua")
	assert.Equal(subStrUA("", "headerua"), "headerua")

	ua := subStrUA(strings.Repeat("1", 550), "headerua")
	assert.Len(ua, 500)
}
