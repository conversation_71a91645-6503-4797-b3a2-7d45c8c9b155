package statistics

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	jsoniter "github.com/json-iterator/go"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/discovery"
	"github.com/MiaoSiLa/missevan-go/models/search"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

var (
	json2 = jsoniter.ConfigCompatibleWithStandardLibrary
)

// ActionCollectSearchClicks 采集点击记录
/**
 * @api {post} /statistics/collect-search-clicks 采集点击记录
 * @apiDescription 该接口数字类型参数支持传字符串 \
 * 埋点文档：https://info.missevan.com/pages/viewpage.action?pageId=90770852
 * @apiVersion 0.1.0
 * @apiGroup statistics
 *
 * @apiParam {Object[]} clicks 点击记录数组
 * @apiParam {number=0,1,2,3} clicks.event_type 事件类型（0：搜索概况，1：点击联想词，2：点击结果项，3：点击 tab 页）
 * @apiParam {Number} clicks.hint_count 联想词数量
 * @apiParam {String} clicks.input 输入框内容
 * @apiParam {String} clicks.query 实际搜索词
 * @apiParam {Number} clicks.ipv 本次搜索点击的次数
 * @apiParam {Number} clicks.item_id 被点击项的 ID
 * @apiParam {number=0,1,2,3,4,5} clicks.item_origin 被点击项来源（0：直接搜索，1：联想词搜索，2：点击热搜词，3：大家都在听，4：搜索历史，5：默认搜索）
 * @apiParam {Number} clicks.item_rank 被点击项位置（从 1 开始）
 * @apiParam {number=0,1,2} clicks.item_rank_type 排序方式（0：默认排序，1：播放量，2：最新发布）
 * @apiParam {String} clicks.item_title 被点击项的标题
 * @apiParam {Number} clicks.item_type 被点击项类型（0：音频，1：UP 主，2：音单，4：声优，5：剧集，\
 * 6：直播，7：特型，8：频道，10xx：专题卡子项）
 * @apiParam {number=0,1,2,3,4,5,6,7,8,9} clicks.item_catalog_id 被点击项筛选分区（0：全部分区，1：有声漫画，2：广播剧，3：音乐，\
 * 4：声音恋人，5：电台，6：日抓，7：听书，8：铃声，9：放松）
 * @apiParam {number=0,1} [clicks.item_is_insert=0] 被点击项是否被人工干预（0：否，1：是）
 * @apiParam {number=0,1,2,3,5,6} clicks.origin 来源页（0：首页，1 发现页，2：搜索结果页，3：大家都在听，5：索引页，6：其他）
 * @apiParam {Number} clicks.result_count 结果数量
 * @apiParam {number=0,1,2,3,4,5,6,8} clicks.search_type 搜索类型（1：UP 主，2：音单，3：综合，4：声优，5：剧集，6：直播，8：频道）
 * @apiParam {Number} clicks.user_id 用户 ID
 * @apiParam {String} clicks.ops_request_misc 搜索请求返回的 ops_request_misc 信息
 * @apiParam {String} clicks.request_id 搜索请求返回的 request_id 信息
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {String} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCollectSearchClicks(c *handler.Context) (handler.ActionResponse, error) {
	var clicks []search.Click
	err := json2.NewDecoder(c.C.Request.Body).Decode(&clicks)
	if err != nil || len(clicks) == 0 {
		return nil, actionerrors.ErrParams
	}

	var (
		os           int
		equipID      string
		isOldAppData bool
	)
	equip := c.Equip()
	buvid := c.BUVID()
	channel := c.C.GetHeader("channel")
	if equip != nil {
		os = int(equip.OS)
		equipID = equip.EquipID
		// 当 iOS 版本小于 6.0.2，Android 版本小于 6.0.3 视为老版本的的埋点采集
		isOldAppData = equip.IsOldApp(util.AppVersions{IOS: "6.0.2", Android: "6.0.3"})
	}

	now := util.TimeNow().Unix()

	ctx := context.Background()
	isProd := util.IsProdEnv()

	for i := range clicks {
		// 线上不接收测试环境的数据
		if isProd && clicks[i].Staging {
			continue
		}
		clicks[i].Input = strings.TrimSpace(clicks[i].Input)
		clicks[i].ItemTitle = strings.TrimSpace(clicks[i].ItemTitle)

		/* REVIEW 用户切换了账号，两个账号的数据都会传过来，这里就不判断了
		var userID int64
		if user := c.User(); user != nil {
			userID = user.ID
		}
		// 对于客户端所传的 user_id 与服务端获取的不一致时，则对该条记录进行丢弃
		if userID != *clicks[i].UserID {
			continue
		} */

		err := clicks[i].Check(isOldAppData)
		if err != nil {
			return nil, handler.NewActionError(http.StatusBadRequest, handler.CodeInvalidParam, err.Error())
		}

		if isOldAppData && *clicks[i].EventType == search.EventTypeClickTab {
			clicks[i].Origin = search.OriginSearchPage
		}
		clicks[i].CreateTime = now
		clicks[i].ModifiedTime = now
		clicks[i].OS = os
		clicks[i].EquipID = equipID
		clicks[i].BUVID = buvid
		clicks[i].Channel = channel
		shouldRecordSearchItem := *clicks[i].EventType == search.EventTypeClickItem &&
			(clicks[i].ItemType == search.ItemTypeTopicCardDrama || clicks[i].ItemType == search.ItemTypeTopicCardMore)
		if shouldRecordSearchItem {
			specialSearchItem, err := discovery.FindActiveSpecialTopicCard(clicks[i].Query, now)
			if err != nil {
				logger.Errorf("find active special topic card error: %v", err)
				// PASS
			} else if specialSearchItem != nil {
				clicks[i].SearchItemID = &specialSearchItem.ID
				clicks[i].SearchItemTitle = &specialSearchItem.Title
			}
		}

		var key string
		if userID := c.UserID(); userID == 0 {
			key = keys.DatabusKeySearchClicks1.Format(fmt.Sprintf(":%08x", util.CRC32(c.ClientIP())))
		} else {
			key = keys.DatabusKeySearchClicks1.Format(strconv.FormatInt(userID, 10))
		}

		err = service.Databus.AppLogPub.Send(ctx, key, clicks[i])
		if err != nil {
			logger.Errorf("databus AppLogPub send error: %v", err)
			// PASS
		}
	}

	return "success", nil
}
