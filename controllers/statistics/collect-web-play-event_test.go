package statistics

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestValidateCSRFToken(t *testing.T) {
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1671465600, 0) // 2022-12-20 00:00:00
	})
	defer util.SetTimeNow(nil)
	timestampForToken := util.TimeNow().Unix() - util.SecondOneDay
	salt := 123456789
	sign := util.HmacSha256(webPlayLogSecretKey, fmt.Sprintf("%d %d", salt, timestampForToken))
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  csrfTokenName,
		Value: fmt.Sprintf("%d_%s_%d", salt, sign, timestampForToken),
	})
	isLegalToken := validateCSRFToken(c)
	assert.True(isLegalToken)

	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	isLegalToken = validateCSRFToken(c)
	assert.False(isLegalToken)

	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  csrfTokenName,
		Value: "",
	})
	isLegalToken = validateCSRFToken(c)
	assert.False(isLegalToken)

	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  csrfTokenName,
		Value: fmt.Sprintf("%s_%d", sign, timestampForToken),
	})
	isLegalToken = validateCSRFToken(c)
	assert.False(isLegalToken)

	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  csrfTokenName,
		Value: fmt.Sprintf("%d_%s_%d", salt, sign, timestampForToken-util.SecondOneDay-1),
	})
	isLegalToken = validateCSRFToken(c)
	assert.False(isLegalToken)

	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	c.C.Request.AddCookie(&http.Cookie{
		Name:  csrfTokenName,
		Value: fmt.Sprintf("%d_%s_%d", salt, "", timestampForToken),
	})
	isLegalToken = validateCSRFToken(c)
	assert.False(isLegalToken)
}

func TestActionPlaylogWeb(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1671465600, 0) // 2022-12-20 00:00:00
	})
	defer util.SetTimeNow(nil)
	testLocationInfo := goclient.IPInfo{
		CityName:    "Shanghai",
		RegionName:  "Shanghai",
		CountryCode: "CN",
		CountryName: "China",
	}
	cancel := mrpc.SetMock("go://util/geoip", func(input interface{}) (output interface{}, err error) {
		return testLocationInfo, nil
	})
	defer cancel()
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	p := playLogWebParams{
		SoundID:          123,
		PageType:         1,
		SessionID:        "1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed",
		Mode:             1,
		PlayerMode:       1,
		LastPlayPosition: 999,
		SoundDuration:    1000,
		StartTime:        1671465600000,
		EndTime:          1671465602000,
		OperationType:    1,
		SessionDuration:  1000,
		PlayedDuration:   1000,
		PauseDuration:    19,
		Purchased:        util.NewInt(1),
		DramaID:          util.NewInt64(1234),
		CatalogID:        1234,
	}
	c := handler.NewTestContext(http.MethodPost, "/statistics/playlog-web", true, p)
	p.UserID = c.UserID()
	testIP := "**************"
	c.SetClientIP(testIP)
	p.IP = c.ClientIP()
	locationInfoBytes, err := json.Marshal(testLocationInfo)
	require.NoError(err)
	p.LocationInfo = string(locationInfoBytes)
	p.UserAgent = c.UserAgent()
	p.EquipID = c.EquipID()
	p.BUVID = c.BUVID()
	p.Channel = c.C.GetHeader("channel")
	p.Referrer = c.Request().Referer()
	p.CreateTime = util.TimeNow().Unix()
	info, err := ActionPlaylogWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)

	// 验证数据的正确性
	expectLogBytes, err := json.Marshal(p)
	require.NoError(err)
	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	message := <-pubMsgs
	assert.Equal("sound_play_log:web:12", message.Key)
	actualLogBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectLogBytes, actualLogBytes)

	// 测试用户未登录时的 databus key
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-web", false, p)
	c.SetClientIP(testIP)
	info, err = ActionPlaylogWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)
	pubMsgs = service.Databus.AppLogPub.DebugPubMsgs()
	message = <-pubMsgs
	assert.Equal("sound_play_log:web::8857f3a5", message.Key)

	// 测试新版本数据上报
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-web?v=2", true, p)
	c.SetClientIP(testIP)
	info, err = ActionPlaylogWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)
	p.More = &more{Version: playlogVersion2}
	expectLogBytes, err = json.Marshal(p)
	require.NoError(err)
	pubMsgs = service.Databus.AppLogPub.DebugPubMsgs()
	message = <-pubMsgs
	actualLogBytes, err = message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectLogBytes, actualLogBytes)

	// 测试参数错误
	p.SoundID = 0
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-web", true, p)
	_, err = ActionPlaylogWeb(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试剧集分区上报
	p.SoundID = 123
	testDramaID := int64(1)
	p.DramaID = &testDramaID
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-web?v=2", true, p)
	c.SetClientIP(testIP)
	info, err = ActionPlaylogWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)
	p.More = &more{Version: playlogVersion2, DramaCatalogID: int64(89)}
	expectLogBytes, err = json.Marshal(p)
	require.NoError(err)
	pubMsgs = service.Databus.AppLogPub.DebugPubMsgs()
	message = <-pubMsgs
	actualLogBytes, err = message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectLogBytes, actualLogBytes)
}

func TestActionPlaylogHeartbeatWeb(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1671465600, 0) // 2022-12-20 00:00:00
	})
	defer util.SetTimeNow(nil)
	testLocationInfo := goclient.IPInfo{
		CityName:    "Shanghai",
		RegionName:  "Shanghai",
		CountryCode: "CN",
		CountryName: "China",
	}
	cancel := mrpc.SetMock("go://util/geoip", func(input interface{}) (output interface{}, err error) {
		return testLocationInfo, nil
	})
	defer cancel()
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	p := playLogHeartbeatWebParams{
		SoundID:          123,
		PageType:         1,
		SessionID:        "1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed",
		Mode:             1,
		PlayerMode:       1,
		LastPlayPosition: 1000,
		HeartbeatTime:    1671465600,
		HeartbeatNum:     10,
		IsPaused:         1,
	}
	c := handler.NewTestContext(http.MethodPost, "/statistics/playlog-heartbeat-web", true, p)
	p.UserID = c.UserID()
	testIP := "**************"
	c.SetClientIP(testIP)
	p.IP = c.ClientIP()
	locationInfoBytes, err := json.Marshal(testLocationInfo)
	require.NoError(err)
	p.LocationInfo = string(locationInfoBytes)
	p.UserAgent = c.UserAgent()
	p.EquipID = c.EquipID()
	p.BUVID = c.BUVID()
	p.Channel = c.C.GetHeader("channel")
	p.Referrer = c.Request().Referer()
	p.CreateTime = util.TimeNow().Unix()
	info, err := ActionPlaylogHeartbeatWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)

	// 验证数据的正确性
	expectLogBytes, err := json.Marshal(p)
	require.NoError(err)
	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	message := <-pubMsgs
	assert.Equal("sound_play_log_heartbeat:web:12", message.Key)
	actualLogBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectLogBytes, actualLogBytes)

	// 测试用户未登录时的 databus key
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-heartbeat-web", false, p)
	c.SetClientIP(testIP)
	info, err = ActionPlaylogHeartbeatWeb(c)
	require.NoError(err)
	assert.EqualValues("success", info)
	pubMsgs = service.Databus.AppLogPub.DebugPubMsgs()
	message = <-pubMsgs
	assert.Equal("sound_play_log_heartbeat:web::8857f3a5", message.Key)

	// 测试参数错误
	p.SoundID = 0
	c = handler.NewTestContext(http.MethodPost, "/statistics/playlog-heartbeat-web", true, p)
	_, err = ActionPlaylogHeartbeatWeb(c)
	assert.Equal(actionerrors.ErrParams, err)
}
