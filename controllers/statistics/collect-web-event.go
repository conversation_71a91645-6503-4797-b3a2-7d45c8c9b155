package statistics

import (
	"encoding/json"
	"io"
	"net/http"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func (t *collectEventsTask) loadWeb(c *handler.Context) error {
	var event eventFromUser
	data, err := io.ReadAll(c.C.Request.Body)
	if err != nil {
		return handler.ErrBadRequest
	}
	_ = c.C.Request.Body.Close()
	err = json.Unmarshal(data, &event)
	if err != nil {
		return handler.ErrBadRequest
	}
	event.UserID = c.UserID()

	if event.Ctime == 0 {
		event.Ctime = int64(util.NewTimeUnixMilli(util.TimeNow()))
	}
	t.events = []*eventFromUser{
		&event,
	}
	// 获取公共参数
	t.equip = c.Equip()
	t.ip = c.ClientIP()
	t.buvid = c.BUVID()
	t.userAgent = c.UserAgent()
	return t.getWeb()
}

func (t *collectEventsTask) getWeb() error {
	now := util.TimeNow()
	eventsToDB := make([]Event, 0, len(t.events))

	// 根据公共参数填充 events
	for _, event := range t.events {
		newEvent := Event{
			UserID:        event.UserID,
			EventID:       event.EventID,
			EventCategory: event.EventCategory,
			EventIDFrom:   event.EventIDFrom,
			LoadType:      event.LoadType,
			PageType:      PageTypeWeb,
			PVStart:       event.PVStart,
			PVEnd:         event.PVEnd,
			Duration:      event.Duration,
			Network:       event.Network,
			Channel:       event.Channel,
			UA:            subStrUA(event.UA, t.userAgent),
			BUVID:         t.buvid,
			IP:            t.ip,
			Ctime:         event.Ctime,
			ModifiedTime:  util.TimeNow().Unix(),
			CreateTime:    event.Ctime / 1000,
		}
		if t.equip != nil {
			newEvent.EquipID = t.equip.EquipID
			newEvent.OS = t.equip.OS
		}
		if event.UploadTime != 0 {
			newEvent.UploadTime = event.UploadTime
		} else {
			newEvent.UploadTime = int64(util.NewTimeUnixMilli(now))
		}
		if event.ExtendedFields != nil {
			mapFields := make(map[string]string)
			// Unmarshal 验证了客户端传过来的 ExtendedFields 只能为 map[string]string 格式
			if err := json.Unmarshal(event.ExtendedFields, &mapFields); err != nil {
				return handler.NewActionError(http.StatusInternalServerError, handler.CodeUnknownError, err.Error())
			}
			extendedFields := string(event.ExtendedFields)
			newEvent.ExtendedFields = &extendedFields
		}
		eventsToDB = append(eventsToDB, newEvent)
	}
	t.eventsToDB = eventsToDB
	return nil
}

// ActionCollectWebEvent 采集 Web 埋点信息
/**
 * @api {post} /statistics/collect-web-event 采集埋点信息
 * @apiDescription web 前端需要及时上报埋点信息时，调用该接口
 *
 * @apiVersion 0.1.0
 * @apiName collect-web-event
 * @apiGroup statistics
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "event_id": "xxxx", // 事件 ID
 *       "event_category": 1, // 事件类型 1. PAGEVIEW; 2. CLICK; 3. EXPOSURE. 下面有些字段根据事件类型可不传
 *       "event_id_from": "xxxx", // 来源页面
 *       "load_type": 0, // PV 的浏览类型 0. 正常浏览; 1. 回退浏览
 *       "network": 0, // 0. UNKNOWN; 1. WIFI; 2. CELLULAR; 3. OFFLINE; 4. OTHERNET
 *       "ua": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36", // User-Agent
 *       // 可选参数：
 *       "extended_fields": {
 *         "args1": "1",
 *         "args2": "2",
 *         "args3": "3"
 *       }
 *     }
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "保存成功"
 *     }
 */
func ActionCollectWebEvent(c *handler.Context) (handler.ActionResponse, error) {
	var t collectEventsTask
	err := t.loadWeb(c)
	if err != nil {
		return nil, err
	}
	err = t.process(service.LogDB)
	if err != nil {
		return nil, err
	}
	return "保存成功", nil
}
