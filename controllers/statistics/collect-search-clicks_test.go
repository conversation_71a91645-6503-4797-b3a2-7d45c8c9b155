package statistics

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"testing"

	"github.com/json-iterator/go/extra"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/discovery"
	"github.com/MiaoSiLa/missevan-go/models/search"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	servicesearch "github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

var clicks1 = []search.Click{
	{
		EventType:      util.NewInt(0),
		HintCount:      4,
		Input:          "魔道祖师✨",
		IPV:            3,
		ItemID:         1,
		ItemOrigin:     0,
		ItemRank:       1,
		ItemRankType:   0,
		ItemTitle:      "魔道祖师《第一季》",
		ItemType:       0,
		ItemCatalogID:  0,
		ItemIsInsert:   1,
		Origin:         2,
		ResultCount:    20,
		SearchType:     1,
		UserID:         util.NewInt64(2233),
		OpsRequestMisc: url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		RequestID:      "153432217417441333635673",
	},
	{
		EventType:      util.NewInt(1),
		HintCount:      4,
		Input:          "魔道祖师✨",
		Query:          "魔道",
		IPV:            3,
		ItemID:         1,
		ItemOrigin:     0,
		ItemRank:       1,
		ItemRankType:   0,
		ItemTitle:      "魔道祖师《第一季》",
		ItemType:       1,
		ItemCatalogID:  1,
		ItemIsInsert:   0,
		Origin:         2,
		ResultCount:    20,
		SearchType:     2,
		UserID:         util.NewInt64(2233),
		OpsRequestMisc: url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		RequestID:      "153432217417441333635673",
	},
	{
		EventType:      util.NewInt(2),
		HintCount:      4,
		Input:          "魔道祖师✨",
		Query:          "魔道",
		IPV:            3,
		ItemID:         1,
		ItemOrigin:     3,
		ItemRank:       1,
		ItemRankType:   2,
		ItemTitle:      "魔道祖师《第一季》",
		ItemType:       0,
		ItemCatalogID:  0,
		ItemIsInsert:   1,
		Origin:         0,
		ResultCount:    20,
		SearchType:     3,
		UserID:         util.NewInt64(2233),
		OpsRequestMisc: url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		RequestID:      "153432217417441333635673",
	},
	{
		EventType:      util.NewInt(3),
		HintCount:      4,
		Input:          "魔道祖师✨",
		IPV:            3,
		ItemID:         1,
		ItemOrigin:     4,
		ItemRank:       1,
		ItemRankType:   0,
		ItemTitle:      "魔道祖师《第一季》",
		ItemType:       4,
		ItemCatalogID:  3,
		ItemIsInsert:   0,
		Origin:         2,
		ResultCount:    20,
		SearchType:     4,
		UserID:         util.NewInt64(2233),
		OpsRequestMisc: url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		RequestID:      "153432217417441333635673",
	},
}

var clicks2 = []map[string]interface{}{
	{
		"event_type":       "0",
		"hint_count":       "4",
		"input":            "",
		"query":            "魔道",
		"ipv":              "3",
		"item_id":          "1",
		"item_origin":      "5",
		"item_rank":        "1",
		"item_rank_type":   "0",
		"item_title":       "魔道祖师《第一季》",
		"item_type":        5,
		"item_catalog_id":  "4",
		"item_is_insert":   "0",
		"origin":           "2",
		"result_count":     "20",
		"search_type":      "5",
		"user_id":          "0",
		"ops_request_misc": url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		"request_id":       "153432217417441333635673",
	},
	{
		"event_type":       "1",
		"hint_count":       "4",
		"input":            "魔道",
		"query":            "魔道祖师",
		"ipv":              "3",
		"item_id":          "1",
		"item_origin":      "1",
		"item_rank":        "1",
		"item_rank_type":   "0",
		"item_title":       "魔道祖师《第一季》",
		"item_type":        6,
		"item_catalog_id":  "5",
		"item_is_insert":   "0",
		"origin":           "2",
		"result_count":     "20",
		"search_type":      "6",
		"user_id":          "0",
		"ops_request_misc": url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		"request_id":       "153432217417441333635673",
	},
	{
		"event_type":       "2",
		"hint_count":       "4",
		"input":            "",
		"query":            "魔道",
		"ipv":              "3",
		"item_id":          "1",
		"item_origin":      "2",
		"item_rank":        "1",
		"item_rank_type":   "0",
		"item_title":       "魔道祖师《第一季》",
		"item_type":        "1001",
		"item_catalog_id":  "6",
		"item_is_insert":   "0",
		"origin":           "2",
		"result_count":     "20",
		"search_type":      "8",
		"user_id":          "0",
		"ops_request_misc": url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		"request_id":       "153432217417441333635673",
	},
	{
		"event_type":       "3",
		"hint_count":       "4",
		"input":            "",
		"query":            "魔道",
		"ipv":              "3",
		"item_id":          "1",
		"item_origin":      "1",
		"item_rank":        "1",
		"item_rank_type":   "0",
		"item_title":       "魔道祖师《第一季》",
		"item_type":        "7",
		"item_catalog_id":  "7",
		"item_is_insert":   "0",
		"origin":           "2",
		"result_count":     "20",
		"search_type":      "8",
		"user_id":          "0",
		"ops_request_misc": url.QueryEscape(`{"request_id":"153432217417441333635673", "scm":"a.b.c.d"}`),
		"staging":          true,
		"request_id":       "153432217417441333635673",
	},
}

func TestActionCollectSearchClicks(t *testing.T) {
	extra.RegisterFuzzyDecoders()
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	c := handler.NewTestContext(http.MethodPost, "/statistics/collect-search-clicks", false, clicks1)
	c.SetClientIP("127.0.0.1")
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.9.8 (iOS;12.0.1;iPhone7,2)")
	_, err := ActionCollectSearchClicks(c)
	require.NoError(t, err)

	c = handler.NewTestContext(http.MethodPost, "/statistics/collect-search-clicks", true, clicks1)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.8.1 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	require.NoError(t, os.Setenv(util.EnvDeploy, util.DeployEnvProd))
	_, err = ActionCollectSearchClicks(c)
	require.NoError(t, err)

	c = handler.NewTestContext(http.MethodPost, "/statistics/collect-search-clicks", false, clicks2)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.0.2 (iOS;12.0.1;iPhone7,2)")
	_, err = ActionCollectSearchClicks(c)
	require.NoError(t, err)

	c = handler.NewTestContext(http.MethodPost, "/statistics/collect-search-clicks", true, clicks2)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.0.3 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	_, err = ActionCollectSearchClicks(c)
	require.NoError(t, err)

	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	// clicks2 最后一条是测试环境数据，线上不接收测试环境的数据
	require.Len(t, pubMsgs, (len(clicks1)+(len(clicks2)-1))*2)
	message := make([]*databus.Message, len(pubMsgs))
	for i := 0; i < len(message); i++ {
		message[i] = <-pubMsgs
	}
	require.Equal(t, keys.DatabusKeySearchClicks1.Format(fmt.Sprintf(":%08x", util.CRC32("127.0.0.1"))), message[0].Key)
	require.Equal(t, "search-clicks:12", message[len(message)-1].Key)

	tests := []struct {
		name           string
		eventType      int
		itemType       int
		wantSearchItem bool
	}{
		// EventType test cases
		{
			name:           "NonClickItemEventShouldNotRecordSearchItem",
			eventType:      search.EventTypeClickTab,
			itemType:       1000,
			wantSearchItem: false,
		},
		{
			name:           "ClickItemEventShouldRecordSearchItem",
			eventType:      search.EventTypeClickItem,
			itemType:       1000,
			wantSearchItem: true,
		},

		// ItemType test cases
		{
			name:           "ItemTypeTopicCardDramaShouldRecordSearchItem",
			eventType:      search.EventTypeClickItem,
			itemType:       search.ItemTypeTopicCardDrama,
			wantSearchItem: true,
		},
		{
			name:           "ItemTypeTopicCardMoreShouldRecordSearchItem",
			eventType:      search.EventTypeClickItem,
			itemType:       search.ItemTypeTopicCardMore,
			wantSearchItem: true,
		},
		{
			name:           "OtherItemTypesShouldNotRecordSearchItem",
			eventType:      search.EventTypeClickItem,
			itemType:       0,
			wantSearchItem: false,
		},
		{
			name:           "OtherItemTypesShouldNotRecordSearchItem2",
			eventType:      search.EventTypeClickItem,
			itemType:       1002,
			wantSearchItem: false,
		},
	}

	setupTestSpecialSearchItem := func(t *testing.T) {
		item := discovery.SpecialSearchItem{
			ID:          999,
			Title:       "多啦A梦",
			SearchWords: "多啦A梦,机器猫,小叮当",
			StartTime:   util.TimeNow().Unix(),
			Status:      discovery.StatusEnabled,
			Type:        servicesearch.SpecialTopicCard,
		}
		require.NoError(t, item.DB().Create(&item).Error)
		t.Cleanup(func() {
			require.NoError(t, item.DB().Delete(&item).Error)
		})
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setupTestSpecialSearchItem(t)

			// Asset that the request is handled successfully
			c := handler.NewTestContext(http.MethodPost, "/statistics/collect-search-clicks", false, []search.Click{
				{
					EventType: util.NewInt(tt.eventType),
					Query:     "多啦A梦",
					ItemType:  tt.itemType,
					UserID:    util.NewInt64(2233),
				},
			})
			c.C.Request.Header.Set("User-Agent", "MissEvanApp/6.0.2 (iOS;12.0.1;iPhone7,2)")
			_, err := ActionCollectSearchClicks(c)
			require.NoError(t, err)

			// Assert that the message is published to the databus
			pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
			require.Len(t, pubMsgs, 1)
			msg := <-pubMsgs

			// Assert that the message is unmarshalled successfully
			var value search.Click
			err = json.Unmarshal(msg.Value, &value)
			require.NoError(t, err)

			// Assert that the search item is recorded or not
			if tt.wantSearchItem {
				require.Equal(t, int64(999), *value.SearchItemID)
				require.Equal(t, "多啦A梦", *value.SearchItemTitle)
			} else {
				require.Empty(t, value.SearchItemID)
				require.Empty(t, value.SearchItemTitle)
			}
		})
	}
}
