package statistics

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 有效日志记录进行位运算标识（比特位第二位为 1）
	playLogAttribValid = 2
)

// ListenLog 播放日志
type ListenLog struct {
	Duration     float64 `json:"duration" gorm:"column:duration"`           // 音频的总时长（单位：毫秒）
	PlayDuration float64 `json:"play_duration" gorm:"column:play_duration"` // 不为暂停状态下的播放时长（单位：毫秒）
	PlayTime     float64 `json:"play_time" gorm:"column:play_time"`         // 开始播放时的时间戳（单位：秒）
	SoundID      int64   `json:"sound_id" gorm:"column:sound_id"`           // 播放音频 ID
	StayDuration float64 `json:"stay_duration" gorm:"column:stay_duration"` // 播放页停留时长（播放或暂停）（单位：毫秒）
	UserID       int64   `json:"user_id" gorm:"column:user_id"`             // 用户 ID
	Online       *int    `json:"online" gorm:"column:online"`               // 开始播放时是否在有网环境
	LoopTimes    int     `json:"loop_times" gorm:"column:loop_times"`       // 单曲重复播放时的循环次数
	Referer      struct {
		Name   string `json:"name,omitempty"`   // 来源页名称
		Page   int    `json:"page,omitempty"`   // 第几页（无分页 0）
		Order  int    `json:"order,omitempty"`  // 排序方式
		Sort   int    `json:"sort,omitempty"`   // 排序序号（无排序 0）
		Source string `json:"source,omitempty"` // 页面来源详细信息
	} `json:"referer" gorm:"-"` // 进入音频播放页的来源

	IsPayer   int    `json:"-" gorm:"column:is_payer"`   // 是否为付费用户，0：非付费用户；1：付费用户
	DramaID   int64  `json:"-" gorm:"column:drama_id"`   // 音频所属剧集 ID
	CatalogID int    `json:"-" gorm:"column:catalog_id"` // 音频所属分类 ID
	OS        int    `json:"-" gorm:"column:os"`         // 设备平台类型，1：Android；2：iOS
	UA        string `json:"-" gorm:"column:ua"`         // User-Agent
	IP        string `json:"-" gorm:"column:ip"`         // 用户 IP
	EquipID   string `json:"-" gorm:"column:equip_id"`   // 设备号
	BUVID     string `json:"-" gorm:"column:buvid"`      // BUVID
	Channel   string `json:"-" gorm:"column:channel"`    // 渠道标识
	Address   string `json:"-" gorm:"column:address"`    // IP 对应地理位置
	Attribute int    `json:"-" gorm:"column:attribute"`  // 数据属性。目前包含是否在回收站（软删除），
	// 是否有效（播放时长超过音频总时长 25%）两个属性，比特位第一位存 1 标识已被软删除，比特位第二位存 1 标识有效
	RefererJSON string `json:"-" gorm:"column:referer"`       // struct Referer 的 json 字符串
	CreateTime  int64  `json:"-" gorm:"column:create_time"`   // 创建时间
	ModifyTime  int64  `json:"-" gorm:"column:modified_time"` // 最后修改时间
	Staging     bool   `json:"staging,omitempty" gorm:"-"`    // 客户端上报的该数据是否是测试环境的数据
}

// TableName 返回当月对应的表名
func (ListenLog) TableName() string {
	return util.TimeNow().Format("listen_log_200601")
}

// ActionAddPlayLog 添加播放日志
func ActionAddPlayLog(c *handler.Context) (handler.ActionResponse, error) {
	listenLogs, err := getPlayLog(c)
	if err != nil {
		return nil, err
	}
	if len(listenLogs) <= 0 {
		return "No logs to save", nil
	}

	err = helper.SplitBatchInsert(service.LogDB, listenLogs[0].TableName(), listenLogs, 1000, true)
	if err != nil {
		return "保存播放日志失败", err
	}
	return "保存成功", nil
}

func getPlayLog(c *handler.Context) ([]*ListenLog, error) {
	var allLogs []ListenLog
	err := c.BindJSON(&allLogs)
	if err != nil || len(allLogs) == 0 {
		return nil, handler.ErrBadRequest
	}
	logs := make([]*ListenLog, 0, len(allLogs))
	isProd := util.IsProdEnv()
	for i := range allLogs {
		// 线上不接收测试环境的数据
		if isProd && allLogs[i].Staging {
			continue
		}
		if allLogs[i].SoundID > 0 && allLogs[i].PlayDuration > 0 && allLogs[i].StayDuration >= allLogs[i].PlayDuration &&
			allLogs[i].Duration > 0 && allLogs[i].PlayTime > 0 {
			logs = append(logs, &allLogs[i])
		}
	}
	if len(logs) == 0 {
		return nil, nil
	}

	var soundIDs []int64
	soundDramas := make(map[int64]int64)
	for _, v := range logs {
		if _, ok := soundDramas[v.SoundID]; !ok {
			soundDramas[v.SoundID] = 0
			soundIDs = append(soundIDs, v.SoundID)
		}
	}
	var res []struct {
		DramaID int64 `json:"drama_id"`
		SoundID int64 `json:"sound_id"`
	}
	reqData := map[string]interface{}{"sound_ids": soundIDs}
	err = c.MRPC("drama://api/get-dramaid-by-soundid", reqData, &res)
	if err != nil {
		logger.Errorf("mrpc(get-dramaid-by-soundid) error: %v", err)
		// PASS
	}
	for _, v := range res {
		soundDramas[v.SoundID] = v.DramaID
	}

	soundCatalogs, err := sound.GetSoundCatalogIDs(soundIDs)
	if err != nil {
		logger.Errorf("GetSoundCatalogIDs error: %v", err)
		// PASS
	}

	// TODO: 通过登录态获取 user_id
	var userIDs []int64
	mapUserIDPayer := make(map[int64]bool)
	for _, v := range logs {
		if v.UserID != 0 {
			if _, ok := mapUserIDPayer[v.UserID]; !ok {
				mapUserIDPayer[v.UserID] = false
				userIDs = append(userIDs, v.UserID)
			}

		}
	}

	payers, err := transaction.GetPayerIDs(userIDs)
	if err != nil {
		logger.Errorf("GetPayerIDs error: %v", err)
		// PASS
	}
	for _, v := range payers {
		mapUserIDPayer[v] = true
	}

	foundIPInfo := make(map[string]goclient.IPInfo)

	buvid := c.BUVID()
	channel := c.C.GetHeader("channel")
	ua := c.UserAgent()
	ip := c.ClientIP()
	for i := range logs {
		// TODO: 若传递了用户 ID，则使用该值，否则使用登录用户 ID，未登录时默认值为 0
		if mapUserIDPayer[logs[i].UserID] {
			logs[i].IsPayer = 1
		}
		logs[i].DramaID = soundDramas[logs[i].SoundID]
		logs[i].CatalogID = soundCatalogs[logs[i].SoundID]
		if equip := c.Equip(); equip != nil {
			logs[i].EquipID = equip.EquipID
			logs[i].OS = int(equip.OS)
		}
		logs[i].BUVID = buvid
		logs[i].Channel = channel
		logs[i].UA = ua
		logs[i].IP = ip
		logs[i].Address = "{}"
		record, ok := foundIPInfo[logs[i].IP]
		if !ok {
			record, err = goclient.GetIPInfo(nil, logs[i].IP)
			if err != nil {
				logger.WithField("ip", logs[i].IP).Errorf("GetIPInfo error: %v", err)
				// PASS
			} else {
				foundIPInfo[logs[i].IP] = record
			}
		}
		if !record.IsEmpty() {
			address := map[string]interface{}{
				"country_code": record.CountryCode,
				"country":      record.CountryName,
				"province":     record.RegionName,
				"city":         record.CityName,
			}
			jsn, _ := json.Marshal(address)
			logs[i].Address = string(jsn)
		}

		// 当音频播放时长达到音频总时长的 25% 时，评定为有效数据，用 attribute 字段比特位第二位为 1 标识
		// 比特位第一位默认为 0，标识未被软删除
		if logs[i].Duration > 0 && float64(logs[i].PlayDuration)/float64(logs[i].Duration) > 0.25 {
			logs[i].Attribute = playLogAttribValid
		}
		jsn, _ := json.Marshal(logs[i].Referer)
		logs[i].RefererJSON = string(jsn)
		if logs[i].Online == nil {
			online := 1
			logs[i].Online = &online
		}
		if logs[i].LoopTimes < 1 {
			logs[i].LoopTimes = 1
		}
		now := util.TimeNow().Unix()
		logs[i].CreateTime = now
		logs[i].ModifyTime = now
	}
	return logs, nil
}
