package statistics

import (
	"net/http/httptest"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()

	db := service.LogDB
	schemaSQL, err := tutil.GetSplitedTableSchema(db, "listen_log", "_"+util.TimeNow().Format("200601"))
	if err != nil {
		logger.Fatal(err)
	}
	err = db.Exec(schemaSQL).Error
	if err != nil {
		logger.Fatal(err)
	}
	schemaSQL, err = tutil.GetSplitedTableSchema(db, "events_tracking_log", "_"+util.TimeNow().Format("20060102"))
	if err != nil {
		logger.Fatal(err)
	}
	err = db.Exec(schemaSQL).Error
	if err != nil {
		logger.Fatal(err)
	}
	os.Exit(m.Run())
}

func TestGetEquipKey(t *testing.T) {
	assert := assert.New(t)

	r := httptest.NewRequest("GET", "/statistics/add-play-log", nil)
	_, err := GetEquipKey(r)
	assert.EqualError(err, "key 不存在")

	r = httptest.NewRequest("GET", "/path", nil)
	_, err = GetEquipKey(r)
	assert.NoError(err)

	r = httptest.NewRequest("GET", "/path", nil)
	r.Header.Set("User-Agent", "MissEvanApp/4.7.1 (iOS;12.0.1;iPhone7,2)")
	key, err := GetEquipKey(r)
	assert.NoError(err)
	assert.Empty(key)
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler(new(config.Config))
	assert.Equal("statistics", h.Name)
	assert.Len(h.Middlewares, 1)
	assert.Len(h.SubHandlers, 2)
}

func TestGzipHandler(t *testing.T) {
	assert := assert.New(t)
	h := gzipHandler(new(config.Config))
	assert.Equal("", h.Name)
	assert.Len(h.Middlewares, 1)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "/add-play-log", "/collect-search-clicks", "/collect-events")
}

func TestAppHandler(t *testing.T) {
	assert := assert.New(t)
	h := appHandler(new(config.Config))
	assert.Equal("", h.Name)
	assert.Len(h.Middlewares, 1)
	assert.Len(h.SubHandlers, 1)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "/get-sign-key")
}

func TestWebHandler(t *testing.T) {
	assert := assert.New(t)
	h := webHandler(new(config.Config))
	assert.Equal("", h.Name)
	assert.Len(h.Middlewares, 0)
	assert.Len(h.SubHandlers, 0)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "/collect-web-event", "/playlog-web", "/playlog-heartbeat-web")
}
