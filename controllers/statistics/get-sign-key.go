package statistics

import (
	"fmt"
	"math/big"
	"net/http"
	"time"

	"github.com/monnand/dhkx"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// ActionGetSignKey returns the public key to the client.
/**
 * @api {post} /statistics/get-sign-key 获取密钥
 *
 * @apiVersion 0.1.0
 * @apiName get-sign-key
 * @apiGroup statistics
 *
 * @apiParam {String} public_key 客户端使用约定的 DH 算法得到的公钥（10 进制）
 *
 * @apiSuccess {Number} code 请求成功或失败返回码
 * @apiSuccess {Object} info 这里旧版本是一个 String，代表返回给客户端的公钥（10 进制）
 * @apiSuccess {Number} info.expire 有效时间，单位秒
 * @apiSuccess {String} info.pubkey 返回给客户端的公钥（10 进制）
 *
 * @apiSuccessExample 旧版本 <= iOS 4.3.3, Android 5.2.4:
 *     {
 *       "code": 0,
 *       "info": "9311944372197310564554456445454"
 *     }
 * @apiSuccessExample 新版本 >= iOS 4.3.4, Android 5.2.5:
 *     {
 *       "code": 0,
 *       "info": {
 *         "expire": 1800,
 *         "pubkey":"9311944372197310564554456445454",
 *       }
 *     }
 */
func ActionGetSignKey(c *handler.Context) (handler.ActionResponse, error) {
	equip := c.Equip()
	// WORKAROUND: 安卓 5.6.0 及以后版本，iOS 4.7.1 及以后版本使用固定 key 生成签名，不再请求 get-sign-key 接口
	if !equip.IsAppOlderThan("4.7.1", "5.6.0") {
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeUnknownError, "当前版本的客户端不可请求该接口")
	}
	if equip == nil || equip.EquipID == "" {
		errMsg := fmt.Sprintf("Can not get equip_id: %v", equip)
		return nil, handler.NewActionError(http.StatusBadRequest, handler.CodeInvalidParam, errMsg)
	}

	exp := time.Minute * 30
	realExp := exp + time.Minute*5 // 防止客户端还在使用

	if equip.IsAppOlderThan("4.3.4", "5.2.5") {
		return getAndStoreSignKey(c, equip.EquipID, realExp)
	}
	pub, err := getAndStoreSignKey(c, equip.EquipID, realExp)
	return handler.M{
		"pubkey": pub,
		"expire": exp.Seconds(),
	}, err
}

func getAndStoreSignKey(c *handler.Context, equipID string, duration time.Duration) (string, error) {
	privateKey, sharedKey, err := getSignedKey(c)
	if err != nil {
		return "", err
	}

	// store the 10-based shared key string to redis
	err = service.Redis.Set(serviceredis.KeyRequestSignUUID1.Format(equipID), sharedKey.String(),
		duration).Err()
	if err != nil {
		return "", err
	}
	// privateKey.String() returns the public key from the private key.
	return privateKey.String(), nil
}

func getSignedKey(c *handler.Context) (privateKey, sharedKey *dhkx.DHKey, err error) {
	var input struct {
		PublicKey string `json:"public_key" form:"public_key"` // base 10
	}
	err = c.Bind(&input)
	if err != nil || input.PublicKey == "" {
		return nil, nil, handler.ErrBadRequest
	}
	theirPub, ok := new(big.Int).SetString(input.PublicKey, 10)
	if !ok {
		return nil, nil, handler.ErrBadRequest
	}

	prime, ok1 := new(big.Int).SetString(params.DH.Prime, 16)
	generator, ok2 := new(big.Int).SetString(params.DH.Generator, 16)
	if !ok1 || !ok2 {
		errMsg := "Bad config of params"
		return nil, nil, handler.NewActionError(http.StatusInternalServerError, handler.CodeUnknownError, errMsg)
	}

	group := dhkx.CreateGroup(prime, generator)
	privateKey, err = group.GeneratePrivateKey(nil)
	if err != nil {
		return nil, nil, err
	}

	sharedKey, err = group.ComputeKey(dhkx.NewPublicKey(theirPub.Bytes()), privateKey)
	if err != nil {
		return nil, nil, err
	}
	return privateKey, sharedKey, nil
}
