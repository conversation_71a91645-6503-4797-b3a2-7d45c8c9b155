package statistics

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCollectWebEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试脏数据
	err := service.LogDB.Table(Event{}.TableName()).Where("event_id IN (?, ?)", "webview_event_test",
		"web_event_test").Delete("").Error
	require.NoError(err)

	eventParams := eventFromUser{
		EventID:       "webview_event_test",
		EventCategory: 2,
		LoadType:      1,
		Network:       1,
	}
	c := handler.NewTestContext("POST", "/", true, eventParams)
	c.C.Request.Header.Set("Content-Type", "text/plain")
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) "+
		"AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MissEvanApp/4.5.6 (Theme Light; NetType WiFi)")
	c.C.Request.Header.Set("X-Real-Ip", "127.0.0.1")
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "equip_id",
		Value: "test_equip_id",
	})
	data, err := ActionCollectWebEvent(c)
	require.NoError(err)
	assert.Equal("保存成功", data)
	// TODO: 该包内的单元测试使用 SQLite
	time.Sleep(time.Second)
	// 验证数据插入成功并且相关值正确
	var event Event
	err = service.LogDB.Table(Event{}.TableName()).Where("event_id = ?", "webview_event_test").
		First(&event).Error
	require.NoError(err)
	assert.Equal(eventParams.EventCategory, event.EventCategory)
	assert.Equal(eventParams.LoadType, event.LoadType)
	assert.Equal(int64(PageTypeWeb), event.PageType)
	assert.Equal(eventParams.Network, event.Network)
	assert.Equal(util.MobileWeb, event.OS)
	assert.Equal("test_equip_id", event.EquipID)

	// 测试从浏览器中请求的情况
	eventParams = eventFromUser{
		EventID:       "web_event_test",
		EventCategory: 2,
		LoadType:      1,
		PageType:      PageTypeWebViewJSB,
		Network:       1,
	}
	c = handler.NewTestContext("POST", "/", true, eventParams)
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "+
		"(KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36")
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "equip_id",
		Value: "",
	})
	data, err = ActionCollectWebEvent(c)
	require.NoError(err)
	assert.Equal("保存成功", data)
	// 验证数据插入成功并且相关值正确
	err = service.LogDB.Table(Event{}.TableName()).Where("event_id = ?", "web_event_test").
		First(&event).Error
	require.NoError(err)
	assert.Equal(eventParams.EventCategory, event.EventCategory)
	assert.Equal(eventParams.LoadType, event.LoadType)
	assert.Equal(int64(PageTypeWeb), event.PageType)
	assert.Equal(eventParams.Network, event.Network)
	assert.Equal(util.Web, event.OS)
	assert.Equal("", event.EquipID)

	// 删除测试脏数据
	err = service.LogDB.Table(Event{}.TableName()).Where("event_id IN (?, ?)", "webview_event_test",
		"web_event_test").Delete("").Error
	require.NoError(err)
}

func TestLoadWeb(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventParams := eventFromUser{
		EventID:       "webview_event_test",
		EventCategory: 2,
		LoadType:      1,
		PageType:      PageTypeWebViewJSB,
		Network:       1,
	}
	c := handler.NewTestContext("POST", "/", true, eventParams)
	c.C.Request.Header.Set("Content-Type", "text/plain")
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) "+
		"AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MissEvanApp/4.5.6 (Theme Light; NetType WiFi)")
	c.C.Request.Header.Set("X-Real-Ip", "127.0.0.1")
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "equip_id",
		Value: "test_equip_id",
	})
	var task collectEventsTask
	err := task.loadWeb(c)
	require.NoError(err)
	assert.Equal(1, len(task.eventsToDB))
	assert.Equal("webview_event_test", task.events[0].EventID)
}

func TestGetWeb(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventParams := eventFromUser{
		EventID:       "webview_event_test",
		EventCategory: 2,
		LoadType:      1,
		PageType:      PageTypeWebViewJSB,
		Network:       1,
	}
	c := handler.NewTestContext("POST", "/", true, eventParams)
	c.C.Request.Header.Set("Content-Type", "text/plain")
	c.C.Request.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) "+
		"AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MissEvanApp/4.5.6 (Theme Light; NetType WiFi)")
	c.C.Request.Header.Set("X-Real-Ip", "127.0.0.1")
	c.C.Request.AddCookie(&http.Cookie{
		Name:  "equip_id",
		Value: "test_equip_id",
	})
	var task collectEventsTask
	err := task.loadWeb(c)
	require.NoError(err)
	err = task.getWeb()
	require.NoError(err)
	assert.Equal(1, len(task.eventsToDB))
	assert.Equal("webview_event_test", task.events[0].EventID)
}
