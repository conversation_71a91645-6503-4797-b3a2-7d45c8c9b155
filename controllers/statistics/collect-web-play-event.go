package statistics

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	csrfTokenName   = "M_CSRF_TOKEN"
	csrfValidPeriod = 2 * util.SecondOneDay
)

const (
	databusLogFromApp = "app"
	databusLogFromWeb = "web"
)

const (
	playlogVersion1 = iota + 1
	playlogVersion2
)

// TODO: 待接口上线，需要配置在配置文件里
const webPlayLogSecretKey = "testkey"

func validateCSRFToken(c *handler.Context) bool {
	cookie, err := c.C.Request.Cookie(csrfTokenName)
	if err != nil {
		if errors.Is(err, http.ErrNoCookie) {
			return false
		}
		logger.Error(err)
		// PASS
		return false
	}
	values := strings.Split(cookie.Value, "_")
	if len(values) != 3 {
		return false
	}
	salt, sign, timestampStr := values[0], values[1], values[2]
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		logger.WithField("csrf_token", cookie.Value).Error(err)
		// PASS
		return false
	}
	if util.TimeNow().Unix()-timestamp > csrfValidPeriod {
		return false
	}
	return sign == util.HmacSha256(webPlayLogSecretKey, fmt.Sprintf("%s %d", salt, timestamp))
}

type more struct {
	Version        int   `json:"version"`
	DramaCatalogID int64 `json:"drama_catalog_id,omitempty"` // 剧集所属分区 ID
}

type playLogWebParams struct {
	SoundID          int64   `form:"sound_id" json:"sound_id"`
	PageType         int     `form:"page_type" json:"page_type"`
	SessionID        string  `form:"session_id" json:"session_id"`
	Mode             int     `form:"mode" json:"mode"`
	PlayerMode       int     `form:"player_mode" json:"player_mode"`
	LastPlayPosition int64   `form:"last_play_position" json:"last_play_position"`
	SoundDuration    int64   `form:"sound_duration" json:"sound_duration"`
	StartTime        int64   `form:"start_time" json:"start_time"`
	EndTime          int64   `form:"end_time" json:"end_time"`
	OperationType    int     `form:"operation_type" json:"operation_type"`
	Completion       float64 `form:"completion" json:"completion"`
	SessionDuration  int64   `form:"session_duration" json:"session_duration"`
	PlayedDuration   int64   `form:"played_duration" json:"played_duration"`
	PauseDuration    int64   `form:"pause_duration" json:"pause_duration"`
	Purchased        *int    `form:"purchased" json:"purchased,omitempty"`
	DramaID          *int64  `form:"drama_id" json:"drama_id,omitempty"`
	CatalogID        int64   `form:"catalog_id" json:"catalog_id"`

	// Generated parameters
	UserID       int64  `form:"-" json:"user_id"`
	IP           string `form:"-" json:"ip"`
	LocationInfo string `form:"-" json:"location_info"`
	UserAgent    string `form:"-" json:"user_agent"`
	EquipID      string `form:"-" json:"equip_id"`
	BUVID        string `form:"-" json:"buvid"`
	Channel      string `form:"-" json:"channel"`
	Referrer     string `form:"-" json:"referrer"`
	More         *more  `form:"-" json:"more,omitempty"`
	CreateTime   int64  `form:"-" json:"create_time"`
	Vip          int    `form:"-" json:"vip"`
	PayType      int    `form:"-" json:"pay_type"`

	version int
}

// ActionPlaylogWeb 音频播放事件上报
/**
 * @api {post} /statistics/playlog-web{?v} web 音频播放事件上报
 * @apiVersion 0.1.0
 * @apiGroup statistics
 *
 * @apiParam (Query) {number=1,2} [v=1] 上报数据的版本
 * @apiParam {Number} sound_id 音频 ID
 * @apiParam {number=1,2,3} page_type 页面类型（1 电脑网页、2 手机网页、3 webview）
 * @apiParam {String} session_id 记录用户开始收听某音频到结束收听的周期（数据格式：UUID v4）
 * @apiParam {number=0,1,2,3,4} mode 开始播放时，音频所处的播放模式（0 正常播放、1 单曲循环、2 列表循环、3 随机播放、4 列表播放）
 * @apiParam {number=1,2} player_mode 音频播放器模式（1 正常播放器、2 催眠专享播放器）
 * @apiParam {Number} last_play_position 每次上报时播放器的实际播放位置（单位：毫秒）
 * @apiParam {Number} sound_duration 音频时长（单位：毫秒）
 * @apiParam {Number} start_time 每条日志开始记录的时间戳（单位：毫秒）
 * @apiParam {Number} end_time 每条日志结束记录的时间戳（单位：毫秒）
 * @apiParam {number=1,2,3,4,5} operation_type 上报时机标识（1 开始播放、2 结束播放、3 暂停、4 浏览器关闭、5 继续播放）
 * @apiParam {Number} completion 剩余未播放时长占音频总时长的比例
 * @apiParam {Number} session_duration 播放停留时长（单位：毫秒）
 * @apiParam {Number} played_duration 用户（设备）播放该音频的时长（单位：毫秒）
 * @apiParam {Number} pause_duration 播放过程中暂停的时长（单位：毫秒）
 * @apiParam {number=0,1} [purchased] 该音频是否已付费（0 未付费、1 已付费），非付费音频不传此字段
 * @apiParam {Number} [drama_id] 音频所属剧集 ID, 无所属剧集的音频不传此字段
 * @apiParam {Number} catalog_id 音频所属分类 ID
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {String} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionPlaylogWeb(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newPlayLogWebParams(c)
	if err != nil {
		return nil, err
	}
	err = p.sendLog()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func newPlayLogWebParams(c *handler.Context) (*playLogWebParams, error) {
	p := new(playLogWebParams)
	v, err := c.GetDefaultParamInt("v", playlogVersion1)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	p.version = v
	err = c.Bind(p)
	if err != nil || !p.validate() {
		return nil, actionerrors.ErrParams
	}

	p.UserID = c.UserID()
	p.IP = c.ClientIP()
	record, err := goclient.GetIPInfo(nil, p.IP)
	if err != nil {
		logger.WithField("ip", p.IP).Errorf("GetIPInfo error: %v", err)
		// PASS
	} else if !record.IsEmpty() {
		recordBytes, _ := json.Marshal(record)
		p.LocationInfo = string(recordBytes)
	}
	p.UserAgent = c.UserAgent()
	p.EquipID = c.EquipID()
	p.BUVID = c.BUVID()
	p.Channel = c.C.GetHeader("channel")
	p.Referrer = c.Request().Referer()
	if p.version != playlogVersion1 {
		p.More = &more{Version: p.version}
	}
	p.CreateTime = util.TimeNow().Unix()
	return p, nil
}

func (p *playLogWebParams) validate() bool {
	_, err := uuid.FromString(p.SessionID)
	return err == nil && util.HasElem([]int{playlogVersion1, playlogVersion2}, p.version) &&
		p.SoundID > 0 && util.HasElem([]int{1, 2, 3}, p.PageType) &&
		util.HasElem([]int{0, 1, 2, 3, 4}, p.Mode) &&
		util.HasElem([]int{1, 2}, p.PlayerMode) &&
		util.HasElem([]int{1, 2, 3, 4, 5, 6, 7}, p.OperationType) &&
		(p.Purchased == nil || (p.Purchased != nil && util.HasElem([]int{0, 1}, *p.Purchased))) &&
		(p.DramaID == nil || (p.DramaID != nil && *p.DramaID > 0))
}

func (p *playLogWebParams) sendLog() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	var key string
	if p.UserID != 0 {
		key = keys.DatabusKeySoundPlayLog2.Format(databusLogFromWeb, strconv.FormatInt(p.UserID, 10))
		if p.DramaID != nil {
			episode, err := dramaepisode.FindEpisodeBySoundID(p.SoundID)
			if err != nil {
				logger.WithField("sound_id", p.SoundID).Errorf("获取音频对应单集信息出错：%v", err)
				// PASS
			}
			if episode != nil {
				p.PayType = episode.PayType
				p.Vip = episode.Vip
				if *p.DramaID != episode.DramaID {
					logger.WithFields(logger.Fields{
						"log_drama_id": p.DramaID,
						"drama_id":     episode.DramaID,
						"episode_id":   episode.ID,
					}).Warn("log 上报剧集 ID 与单集所属剧集 ID 不一致")
					p.DramaID = &episode.DramaID
				}
			}
		}
	} else {
		key = keys.DatabusKeySoundPlayLog2.Format(databusLogFromWeb, fmt.Sprintf(":%08x", util.CRC32(p.IP)))
	}
	// 记录剧集所属分区
	if p.DramaID != nil {
		dramaCatalogID, err := dramainfo.FindDramaCatalogID(*p.DramaID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"drama_id": *p.DramaID,
				"sound_id": p.SoundID,
			}).Errorf("获取音频所属剧集分区信息出错：%v", err)
			// PASS
		}
		if dramaCatalogID > 0 {
			if p.More != nil {
				p.More.DramaCatalogID = dramaCatalogID
			} else {
				p.More = &more{DramaCatalogID: dramaCatalogID}
			}
		}
	}
	err := service.Databus.AppLogPub.Send(ctx, key, p)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}

type playLogHeartbeatWebParams struct {
	SoundID          int64  `form:"sound_id" json:"sound_id"`
	PageType         int    `form:"page_type" json:"page_type"`
	SessionID        string `form:"session_id" json:"session_id"`
	Mode             int    `form:"mode" json:"mode"`
	PlayerMode       int    `form:"player_mode" json:"player_mode"`
	LastPlayPosition int64  `form:"last_play_position" json:"last_play_position"`
	HeartbeatTime    int64  `form:"heartbeat_time" json:"heartbeat_time"`
	HeartbeatNum     int    `form:"heartbeat_num" json:"heartbeat_num"`
	IsPaused         int    `form:"is_paused" json:"is_paused"`

	// Generated parameters
	UserID       int64  `form:"-" json:"user_id"`
	IP           string `form:"-" json:"ip"`
	LocationInfo string `form:"-" json:"location_info"`
	UserAgent    string `form:"-" json:"user_agent"`
	EquipID      string `form:"-" json:"equip_id"`
	BUVID        string `form:"-" json:"buvid"`
	Channel      string `form:"-" json:"channel"`
	Referrer     string `form:"-" json:"referrer"`
	CreateTime   int64  `form:"-" json:"create_time"`
}

// ActionPlaylogHeartbeatWeb web 音频播放心跳上报
/**
 * @api {post} /statistics/playlog-heartbeat-web web 音频播放心跳上报
 * @apiVersion 0.1.0
 * @apiGroup statistics
 *
 * @apiParam {Number} sound_id 音频 ID
 * @apiParam {number=1,2,3} page_type 页面类型（1 电脑网页、2 手机网页、3 webview）
 * @apiParam {String} session_id 记录用户开始收听某音频到结束收听的周期（数据格式：UUID v4）
 * @apiParam {number=0,1,2,3,4} mode 开始播放时，音频所处的播放模式（0 正常播放、1 单曲循环、2 列表循环、3 随机播放、4 列表播放）
 * @apiParam {number=1,2} player_mode 音频播放器模式（1 正常播放器、2 催眠专享播放器）
 * @apiParam {Number} last_play_position 每次上报时播放器的实际播放位置（单位：毫秒）
 * @apiParam {Number} heartbeat_time 每条日志开始记录的时间戳（单位：毫秒）
 * @apiParam {Number} heartbeat_num 同一 session_id 下的第几次心跳
 * @apiParam {number=0,1} is_paused 心跳时播放器是否暂停（0 未暂停、1 已暂停）
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {String} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionPlaylogHeartbeatWeb(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newPlayLogHeartbeatWebParams(c)
	if err != nil {
		return nil, err
	}
	err = p.sendLog()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func newPlayLogHeartbeatWebParams(c *handler.Context) (*playLogHeartbeatWebParams, error) {
	p := new(playLogHeartbeatWebParams)
	err := c.Bind(p)
	if err != nil || !p.validate() {
		return nil, actionerrors.ErrParams
	}

	p.UserID = c.UserID()
	p.IP = c.ClientIP()
	record, err := goclient.GetIPInfo(nil, p.IP)
	if err != nil {
		logger.WithField("ip", p.IP).Errorf("GetIPInfo error: %v", err)
		// PASS
	} else {
		recordBytes, _ := json.Marshal(record)
		p.LocationInfo = string(recordBytes)
	}
	p.UserAgent = c.UserAgent()
	p.EquipID = c.EquipID()
	p.BUVID = c.BUVID()
	p.Channel = c.C.GetHeader("channel")
	p.Referrer = c.Request().Referer()
	p.CreateTime = util.TimeNow().Unix()
	return p, nil
}

func (p *playLogHeartbeatWebParams) validate() bool {
	_, err := uuid.FromString(p.SessionID)
	return err == nil && p.SoundID > 0 && util.HasElem([]int{1, 2, 3}, p.PageType) &&
		util.HasElem([]int{0, 1, 2, 3, 4}, p.Mode) &&
		util.HasElem([]int{1, 2}, p.PlayerMode) &&
		util.HasElem([]int{0, 1}, p.IsPaused)
}

func (p *playLogHeartbeatWebParams) sendLog() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	var key string
	if p.UserID != 0 {
		key = keys.DatabusKeySoundPlayLogHeartbeat2.Format(databusLogFromWeb, strconv.FormatInt(p.UserID, 10))
	} else {
		key = keys.DatabusKeySoundPlayLogHeartbeat2.Format(databusLogFromWeb, fmt.Sprintf(":%08x", util.CRC32(p.IP)))
	}
	err := service.Databus.AppLogPub.Send(ctx, key, p)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}
