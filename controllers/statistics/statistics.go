package statistics

import (
	"net/http"

	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/apisign"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "statistics",
		Middlewares: gin.HandlersChain{
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			appHandler(conf),
			webHandler(conf),
		},
	}
}

// webHandler returns the registered web handler
func webHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Actions: map[string]*handler.Action{
			"/collect-web-event":     handler.NewAction(handler.POST, ActionCollectWebEvent, false),
			"/playlog-web":           handler.NewAction(handler.POST, ActionPlaylogWeb, false),
			"/playlog-heartbeat-web": handler.NewAction(handler.POST, ActionPlaylogHeartbeatWeb, false),
		},
	}
}

// appHandler returns the registered web handler
func appHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Middlewares: gin.HandlersChain{
			apisign.MiddlewarePerRequestKey([]byte(conf.HTTP.APPAPISignKey), GetEquipKey),
		},
		Actions: map[string]*handler.Action{
			"/get-sign-key": handler.NewAction(handler.POST, ActionGetSignKey, false),
		},
		SubHandlers: []handler.Handler{gzipHandler(conf)},
	}
}

// 目前 apisign 的处理对于 content-encoding 为 gzip 的 form 的提交支持有问题，这里先分开使用
func gzipHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Middlewares: gin.HandlersChain{
			gzip.Gzip(gzip.DefaultCompression, gzip.WithDecompressFn(gzip.DefaultDecompressHandle)),
		},
		Actions: map[string]*handler.Action{
			"/add-play-log":          handler.NewAction(handler.POST, ActionAddPlayLog, false),
			"/collect-search-clicks": handler.NewAction(handler.POST, ActionCollectSearchClicks, false),
			"/collect-events":        handler.NewAction(handler.POST, ActionCollectEvents, false),
		},
	}
}

// GetEquipKey returns the signing key corresponding to the equipment
func GetEquipKey(r *http.Request) (string, error) {
	specURLs := []string{
		"/statistics/add-play-log",
		"/statistics/collect-search-clicks",
		"/statistics/collect-events",
	}

	if r.UserAgent() != "" {
		e := util.NewEquipment(r.UserAgent())
		// WORKAROUND: 安卓 5.6.0 及以后版本，iOS 4.7.1 及以后版本使用固定 key 生成签名
		if !e.IsAppOlderThan("4.7.1", "5.6.0") {
			return "", nil
		}
	}

	for _, v := range specURLs {
		if r.URL.Path == v {
			equip, err := util.ParseEquipment(r)
			if err != nil {
				return "", err
			}
			keystr, err := service.Redis.Get(serviceredis.KeyRequestSignUUID1.Format(equip.EquipID)).Result()
			if serviceredis.IsRedisNil(err) {
				err = &apisign.ErrSign{
					Message: "key 不存在",
				}
			}
			return keystr, err
		}
	}

	return "", nil
}
