package statistics

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/monnand/dhkx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestGetSignKey(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	client, _ := dhkx.GetGroup(14)
	clientPriv, _ := client.GeneratePrivateKey(nil)
	clientPub := clientPriv.String()

	c := handler.CreateTestContext(false)
	body := strings.NewReader("public_key=" + clientPub)
	c.C.Request = httptest.NewRequest("POST", "/get-sign-key", body)
	c.C.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	serverPriv, serverShare, err := getSignedKey(c)
	require.NoError(err)

	serverPub := dhkx.NewPublicKey(serverPriv.Bytes())

	clientShare, _ := client.ComputeKey(serverPub, clientPriv)
	assert.Equal(clientShare.Bytes(), serverShare.Bytes())
}

func TestActionGetSignKey(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	client, _ := dhkx.GetGroup(14)
	clientPriv, _ := client.GeneratePrivateKey(nil)
	clientPub := clientPriv.String()

	c := handler.CreateTestContext(false)
	body := strings.NewReader("public_key=" + clientPub)
	c.C.Request = httptest.NewRequest("POST", "/get-sign-key", body)
	c.C.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.3.4 (iOS;12.0.1;iPhone7,2)")
	c.C.Request.AddCookie(&http.Cookie{Name: "equip_id", Value: "test-equip-id"})

	resp, err := ActionGetSignKey(c)
	require.NoError(err)
	result := resp.(handler.M)
	assert.EqualValues(1800, result["expire"])

	c = handler.CreateTestContext(false)
	c.C.Request = httptest.NewRequest("POST", "/get-sign-key", body)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/4.7.1 (iOS;12.0.1;iPhone7,2)")
	resp, err = ActionGetSignKey(c)
	assert.Nil(resp)
	require.EqualError(err, "当前版本的客户端不可请求该接口")

	c = handler.CreateTestContext(false)
	c.C.Request = httptest.NewRequest("POST", "/get-sign-key", body)
	c.C.Request.Header.Set("User-Agent", "MissEvanApp/5.6.0 (Android;8.0.0;honor FRD-DL00 HWFRD)")
	resp, err = ActionGetSignKey(c)
	assert.Nil(resp)
	require.EqualError(err, "当前版本的客户端不可请求该接口")
}
