package statistics

import (
	"bytes"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestListenLog(t *testing.T) {
	assert := assert.New(t)
	util.SetTimeNow(func() time.Time {
		return time.Date(2020, 11, 3, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)
	assert.Equal("listen_log_202011", ListenLog{}.TableName())
}

func TestAddPlayLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(os.Setenv(util.EnvDeploy, util.DeployEnvProd))
	// REVIEW: 还原一条正常交易记录
	trans := transaction.TransactionLog{
		FromID: 1,
		Status: transaction.StatusSuccess,
	}
	err := trans.DB().FirstOrCreate(&trans, trans).Error
	require.NoError(err)

	playlog := []ListenLog{{
		Duration:     60,
		PlayDuration: 60,
		PlayTime:     1257894000,
		SoundID:      1,
		StayDuration: 65,
		UserID:       1,
	}}
	c := handler.NewTestContext("POST", "/add-play-log", false, playlog)

	savelog, err := getPlayLog(c)
	require.NoError(err)
	assert.NotEmpty(savelog)
	assert.Equal(1, *savelog[0].Online)
	assert.Equal(1, savelog[0].LoopTimes)
	assert.Equal(1, savelog[0].IsPayer)
	assert.Equal(playLogAttribValid, savelog[0].Attribute)
	assert.Equal("{}", savelog[0].RefererJSON)

	jsn := []byte(`[{"duration":1760,"loop_times":1,"online":1,"play_duration":2013.623,"play_time":1558512439,
"referer":{"name":"recommend","order":0,"page":0,"sort":5,"source":"might_like"},
"sound_id":33,"stay_duration":2013.623,"user_id":3456800},
{"duration":520567,"loop_times":1,"online":1,"play_duration":3349,"play_time":1558512441,
"referer":{"name":"drama_details","order":0,"page":0,"sort":1,"source":"9888"},
"sound_id":536892,"stay_duration":3349,"user_id":3456800}]`)
	c.C.Request = httptest.NewRequest("POST", "/add-play-log", bytes.NewReader(jsn))

	savelog, err = getPlayLog(c)
	require.NoError(err)
	assert.Len(savelog, 2)
	err = helper.BatchInsert(service.LogDB, savelog[0].TableName(), savelog)
	require.NoError(err)

	playlog = []ListenLog{{
		Duration:     60,
		PlayDuration: 60,
		PlayTime:     1257894000,
		SoundID:      1,
		StayDuration: 65,
		UserID:       1,
		Staging:      true,
	}, {
		Duration:     60,
		PlayDuration: 60,
		PlayTime:     1257894000,
		SoundID:      1,
		StayDuration: 65,
		UserID:       2,
		Staging:      false,
	}}
	c = handler.NewTestContext("POST", "/add-play-log", false, playlog)
	savelog, err = getPlayLog(c)
	require.NoError(err)
	require.Equal(1, len(savelog))
	assert.Equal(int64(2), savelog[0].UserID)
	require.NoError(os.Setenv(util.EnvDeploy, "uat"))
	c = handler.NewTestContext("POST", "/add-play-log", false, playlog)
	savelog, err = getPlayLog(c)
	require.NoError(err)
	require.Equal(2, len(savelog))
	tutil.PrintJSON(savelog)
	assert.Equal(int64(1), savelog[0].UserID)
	require.NoError(os.Setenv(util.EnvDeploy, util.DeployEnvProd))
	playlog[1].Staging = true
	c = handler.NewTestContext("POST", "/add-play-log", false, playlog)
	savelog, err = getPlayLog(c)
	require.NoError(err)
	require.Empty(savelog)
}
