package app

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler(nil)
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	assert.Equal("app", h.Name)
	checker.Check(h.Actions, "/download", "/info")
}
