package app

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/appupdate"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(appInfoResp{}, "name", "version", "developer", "size", "update_date",
		"privacy_url", "permission_url", "hot_update_url")
}

func TestActionInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodGet, "/x/app/info", false, nil)
	_, err := ActionInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/info?device=%d", -1), false, nil)
	_, err = ActionInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/info?device=%d", 3), false, nil)
	_, err = ActionInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 准备测试数据
	now := util.TimeNow().Unix()
	appWindows := appupdate.AppUpdate{
		ID:         3,
		Title:      "猫耳FM",
		Version:    "22",
		Intro:      "1.2.0",
		Status:     appupdate.StatusPublished,
		Device:     appupdate.DeviceWindows,
		UpdateTime: now,
		Size:       49.21,
		AppURL:     "test://test.json",
	}
	appIOS := appupdate.AppUpdate{
		ID:         4,
		Title:      "猫耳FM",
		Version:    "10",
		Intro:      "1.0.0",
		Status:     appupdate.StatusPublished,
		Device:     appupdate.DeviceIOS,
		Size:       29.126,
		UpdateTime: now,
	}
	defer func() {
		err = service.DB.Table(appupdate.TableName()).
			Where("id IN (?)", []int64{appWindows.ID, appIOS.ID}).
			Delete(&appupdate.AppUpdate{}).Error
		assert.NoError(err)
	}()
	err = service.DB.Table(appupdate.TableName()).Create(&appIOS).Error
	require.NoError(err)
	err = service.DB.Table(appupdate.TableName()).Create(&appWindows).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/info?device=%d", appupdate.DeviceWindows), false, nil)
	r, err := ActionInfo(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*appInfoResp)

	assert.Equal(util.Float2DP(49.21), resp.Size)
	assert.Equal(appWindows.Intro, resp.Version)
	assert.Equal("Maoer Co.", resp.Developer)
	assert.Equal(time.Unix(now, 0).Format(util.TimeFormatYMD), resp.UpdateDate)
	assert.Equal("https://link.missevan.com/rule/privacy", resp.PrivacyURL)
	assert.Equal("https://link.missevan.com/rule/permission", resp.PermissionURL)
	assert.Equal("http://static-test.missevan.com/test.json", resp.HotUpdateURL)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/x/app/info?device=%d", appupdate.DeviceIOS), false, nil)
	r, err = ActionInfo(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*appInfoResp)

	b, err := json.Marshal(resp)
	require.NoError(err)
	assert.NotContains(string(b), "29.126")
	assert.Equal(appIOS.Intro, resp.Version)
	assert.Equal("Maoer Co.", resp.Developer)
	assert.Equal(time.Unix(now, 0).Format(util.TimeFormatYMD), resp.UpdateDate)
	assert.Equal("https://link.missevan.com/rule/privacy", resp.PrivacyURL)
	assert.Equal("https://link.missevan.com/rule/permission", resp.PermissionURL)
	assert.Empty(resp.HotUpdateURL)
}
