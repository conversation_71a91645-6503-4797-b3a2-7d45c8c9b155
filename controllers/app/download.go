package app

import (
	"net/http"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/appupdate"
	"github.com/MiaoSiLa/missevan-go/service"
)

// ActionDownload 客户端下载接口
// TODO: 暂时仅支持 PC 端下载
/**
 * @api {get} /x/app/download 客户端下载接口
 * @apiDescription 下载客户端使用，目前仅支持直播助手
 * @apiVersion 0.1.0
 * @apiName app-download
 * @apiGroup app
 *
 * @apiParam {Number} device 客户端类型 0: 安卓, 2: 直播助手（PC）
 *
 */
func ActionDownload(c *handler.Context) (handler.ActionResponse, error) {
	device, err := c.GetParamInt("device")
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	switch device {
	case appupdate.DeviceWindows:
		app, err := appupdate.Latest(appupdate.DeviceWindows)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if app == nil || app.AppURL2 == "" {
			return nil, actionerrors.ErrServerInternal(errAppNotFound, nil)
		}
		c.C.Redirect(http.StatusFound, service.Storage.Parse(app.AppURL2))
		return nil, handler.ErrRawResponse
	}
	return nil, actionerrors.ErrDownloadLimit
}
