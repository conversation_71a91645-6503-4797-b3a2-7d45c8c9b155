package backend

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTestService()
	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	assert.Equal("backend/go", h.Name)
	assert.Len(h.Middlewares, 1)
	assert.Len(h.SubHandlers, 1)
}

func TestSiteHander(t *testing.T) {
	assert := assert.New(t)
	// kc := tutil.NewKeyChecker(t, tutil.Actions)

	handler := siteHander()
	assert.Equal(handler.Name, "site")
	assert.Len(handler.Middlewares, 1)
}
