package backend

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

// Handler returns the registered handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "backend/go",
		Middlewares: gin.HandlersChain{
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			siteHander(),
		},
	}
}

func siteHander() handler.Handler {
	return handler.Handler{
		Name: "site",
		Middlewares: gin.Handlers<PERSON>hain{
			user.IsRole(role.ServiceGroup, role.QAGroup, role.Developer),
		},
		Actions: map[string]*handler.Action{},
	}
}
