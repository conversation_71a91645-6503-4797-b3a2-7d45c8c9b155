package event

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestPrizesCounts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	duration := 5 * time.Minute
	p := prizesCounts{
		event: &mevent.Simple{
			ID:      0,
			EndTime: now.Add(duration).Unix(),
		},
	}
	err := p.ClearAll()
	require.NoError(err)

	ok, err := p.Incr(123, -1)
	assert.NoError(err)
	assert.True(ok)

	ok, err = p.Incr(123, 1)
	require.NoError(err)
	assert.True(ok)
	ttl, err := service.Redis.TTL(keys.KeyEventPrizes1.Format(p.event.ID)).Result()
	require.NoError(err)
	assert.GreaterOrEqual(util.TimeNow().Add(ttl).UnixNano(), now.Add(duration).UnixNano())

	ok, err = p.Incr(123, 1)
	require.NoError(err)
	assert.False(ok)

	m, err := p.GetAll()
	require.NoError(err)
	assert.Equal(1, m[123])

	require.NoError(p.ClearAll())
}
