package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestGetPrizeConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var thisEvent mevent.Simple
	err := service.DB.Where("id = ?", eventIDNyaaWheel).Take(&thisEvent).Error
	require.NoError(err)
	require.NoError(service.Redis.Del(
		keys.KeyEventPrizes1.Format(thisEvent.ID)).Err())

	conf := getPrizeConfig(&thisEvent)

	key := keyEventPoolPrizeConfigs3.Format(eventIDNyaaWheel, 0, 0)
	config, found := cachePrizes.Get(key)
	require.True(found)
	assert.Equal(conf, config)
}

func TestNyaaWheel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	extField := `{"draw_start_time":0}`
	service.DB.Table(mevent.TableName()).Where("id = ?", 116).UpdateColumn("extended_fields", extField)

	endTime := util.TimeNow().AddDate(0, 1, 0)
	service.DB.Table(mevent.TableName()).Where("id = ?", 116).UpdateColumn("end_time", endTime.Unix())

	c := handler.NewTestContext(http.MethodGet, "myprizes?event_id=116", true, nil)
	lock := keys.LockEventUserPoint2.Format(eventIDNyaaWheel, c.UserID())
	_ = service.Redis.Del(lock).Err()

	// 一次免费抽奖机会
	resp, err := actionNyaaWheelMyPrizes(c)
	require.NoError(err)
	assert.EqualValues(1, resp.(handler.M)["free_chance"])
	tutil.PrintJSON(resp)

	// 免费抽奖
	c = handler.NewTestContext(http.MethodPost, "draw?event_id=116&free=1", true, nil)
	_, err = actionNyaaWheelDraw(c)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "draw?event_id=116&free=1", true, nil)
	_, err = actionNyaaWheelDraw(c)
	assert.Equal(errLimitFree, err)

	// + 100 积分
	c = handler.NewTestContext(http.MethodPost, "draw?event_id=116&free=0", true, nil)
	key := serviceredis.KeyCarnivalPointUserID1.Format(c.UserID())
	err = service.Redis.HIncrBy(key, "total_point", 100).Err()
	require.NoError(err)

	// 积分抽奖
	_, err = actionNyaaWheelDraw(c)
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "draw?event_id=116&free=0", true, nil)
	_, err = actionNyaaWheelDraw(c)
	assert.Equal(errLimitScore, err)

	// 剩余免费抽奖机会是 0
	resp, err = actionNyaaWheelMyPrizes(c)
	require.NoError(err)
	assert.EqualValues(0, resp.(handler.M)["free_chance"])
	tutil.PrintJSON(resp)

	// 剩余积分是 0
	score, err := service.Redis.HGet(key, "total_point").Int()
	require.NoError(err)
	assert.Equal(0, score)
}

func TestAddPointToUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var point0 int64
	err := service.DB.Table(user.MowangskUser{}.TableName()).
		Where("id = ?", 12).Select("point").Row().Scan(&point0)
	require.NoError(err)

	err = addPointToUser(1, 12)
	assert.NoError(err)

	var point1 int64
	err = service.DB.Table(user.MowangskUser{}.TableName()).
		Where("id = ?", 12).Select("point").Row().Scan(&point1)
	require.NoError(err)

	assert.Equal(point0+1, point1)
}
