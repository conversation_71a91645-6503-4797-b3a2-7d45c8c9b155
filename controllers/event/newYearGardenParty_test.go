package event

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionNewYearGardenPartyDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/?event_id=148", true, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 12999,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(148)
	userID := c.UserID()
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(100), Member: strconv.FormatInt(userID, 10)}).Err())
	e, err := mevent.FindSimple(148)
	require.NoError(err)
	defer util.SetTimeNow(nil)
	util.SetTimeNow(func() time.Time {
		return time.Unix(e.DrawEndTime, 0).Add(-time.Minute)
	})
	data, err := actionNewYearGardenPartyDraw(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	prize, ok := data.(handler.M)
	require.True(ok)
	up, ok := prize["prize"]
	require.True(ok)
	userPrize, ok := up.(*models.EventUserPrize)
	require.True(ok)
	util.SetTimeNow(nil)
	if _, ok := models.IsPrize(userPrize.Name, prizeRegex); ok {
		// 等待小鱼干发放完毕
		time.Sleep(3 * time.Second)
		var ma messageassign.MessageAssign
		err := service.DB.Where("recuid = ?", c.UserID()).Order("id ASC").Find(&ma).Error
		require.NoError(err)
		assert.Equal(fmt.Sprintf("亲爱的用户，恭喜您在【猫耳FM新春游园会 - 新春牛牛转盘】中获得奖品：%s。奖品已发放至您的账户，请注意查收。", userPrize.Name), ma.Content)
	}
}
