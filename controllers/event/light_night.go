package event

import (
	"errors"
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/drawpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	confirmExchange int = iota + 1
)

type exchangeParams struct {
	GoodsID int64        `form:"goods_id" json:"goods_id"`
	Confirm util.BitMask `form:"confirm" json:"confirm"`
}

/**
* @api {get} /v2/event/myprizes?event_id=163 光夜之歌用户获取自己兑换的商品和当前拥有的光夜币
* @apiVersion 0.1.0
* @apiGroup event
*
* @apiParam {Number} [p=1] 第几页
* @apiParam {Number} [pagesize=20] 每页个数
*
* @apiSuccess (200) {Number} code CodeSuccess = 0.
* @apiSuccess (200) {Object} info
*
* @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 0,       // 免费抽奖次数，此活动用不到该字段，总是返回 0
 *           "purchase_times": 0,   // 非免费抽奖次数，此活动用不到该字段，总是返回 0
 *           "support_free": false, // 当前活动是否支持免费抽奖，此活动用不到该字段，总是返回 false
 *           "point": 100 // 剩余积分
 *         },
 *         "prizes": [ // 用户已兑换的商品，最新兑换的排在前边
 *           {
 *             "user_id": 12,
 *             "prize_id": 8549,
 *             "create_time": 1611840257,
 *             "name": "头像框",
 *             "image_url": "https://www.uat.missevan.com/files/2021-01-27/574270c393f3022bddda21dec27f1a8a.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
*
* @apiError (403) {Number} code CodeLoginRequired = 100010006
* @apiError (403) {string} info "请先登录"
*/

/**
 * @api {post} /v2/event/exchange?event_id=163 光夜之歌用户兑换商品
 * @apiVersion 0.1.0
 * @apiGroup event
 * @apiParam {Number} goods_id 商品 ID，取值为 1 到 7，1 对应煦光颂歌 7d，2 对应永夜回音 7d，3 对应晨曦百合 7d，4 对应夜雾玫瑰 7d，5 对应光夜颂歌×1，6 对应白色礼赞×1，7 对应凤鸣天下×1
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0, 位 1 用于兑换
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 兑换成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 兑换商品的时间
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要兑换 煦光颂歌 7d 吗？" // 提示信息
 *     }
 *   }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 */
func actionLightNightExchange(c *handler.Context) (handler.ActionResponse, error) {
	return exchangeGoods(eventIDLightNight, c)
}

func exchangeGoods(eventID int64, c *handler.Context) (handler.ActionResponse, error) {
	userID := c.UserID()
	var params exchangeParams
	err := c.Bind(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	var dc drawConfig
	e, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if e == nil {
		return nil, actionerrors.ErrEventNotFound
	}
	goodsID := params.GoodsID
	goods, errMsg, err := exchangeable(e, dc, userID, goodsID)
	if err != nil {
		return nil, err
	}
	if !params.Confirm.IsSet(confirmExchange) {
		return nil, actionerrors.ErrConfirmRequired(errMsg, 1, true)
	}
	lock := keys.LockEventUserPoint2.Format(eventID, userID)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)
	userGoods := models.EventUserPrize{
		UserID:   userID,
		PrizeID:  goods.prize.PrizeID,
		EventID:  eventID,
		Name:     goods.prize.Name,
		ImageURL: goods.prize.ImageURL,
	}
	if err := userGoods.Create(); err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	now := dc.TimeNow()
	expireDuration := time.Unix(e.EndTime, 0).Add(30 * 24 * time.Hour).Sub(now)
	if goods.ExchangeLimit > 0 {
		if err := increaseCount(eventID, userID, goodsID, expireDuration); err != nil {
			return nil, err
		}
	}
	dp := drawpoint.Param{EventID: eventID, UserID: userID, CurrentTime: now.Unix()}
	if _, err := dp.MinusDrawPoint(goods.Price, expireDuration); err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	goods.gs.send(c)
	return handler.M{"prize": userGoods}, nil
}

func exchangeable(e *mevent.Simple, dc drawConfig, userID, goodsID int64) (*goods, string, error) {
	nowUnix := dc.TimeNow().Unix()
	if nowUnix < e.DrawStartTime || nowUnix >= e.DrawEndTime {
		return nil, "", actionerrors.ErrForbidden(handler.CodeUnknownError, "当前不在兑换时间内哦~")
	}
	eventID := e.ID
	goods, err := goodsByID(eventID, goodsID)
	if err != nil {
		return nil, "", err
	}
	dp := drawpoint.Param{EventID: eventID, UserID: userID, CurrentTime: nowUnix}
	point, err := dp.DrawPoint()
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	if point < goods.Price {
		msg := "积分不足"
		switch eventID {
		case eventIDSpringPK:
			msg = "守护币不足"
		case eventID196:
			msg = "兑换币不足！"
		}
		return nil, "", actionerrors.ErrForbidden(handler.CodeUnknownError, msg)
	}
	if goods.ExchangeLimit > 0 {
		currentCount, err := exchangeCount(eventID, userID, goodsID)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		if currentCount >= goods.ExchangeLimit {
			return nil, "", actionerrors.ErrForbidden(handler.CodeUnknownError, "兑换次数已达上限")
		}
	}
	var msg string
	switch eventID {
	case eventIDSpringPK:
		msg = fmt.Sprintf("确定要消耗 %d 守护币兑换<br>%s 吗？", goods.Price/1000, html.EscapeString(goods.prize.Name))
	case eventID196:
		msg = fmt.Sprintf("确定要消耗 %d 通宝兑换<br>%s 吗？", goods.Price, html.EscapeString(goods.prize.Name))
	default:
		msg = fmt.Sprintf("确定要兑换 %s 吗？", html.EscapeString(goods.prize.Name))
	}
	return goods, msg, nil
}

type prizeNameInfo struct {
	poolID  int64
	goodsID int64
	name    string
}

// parsePrizeName 获取兑换商品 ID，奖池 ID 和奖品名
func parsePrizeName(fullName string) (*prizeNameInfo, error) {
	if !strings.HasPrefix(fullName, "#") {
		return &prizeNameInfo{0, 0, fullName}, nil
	}
	r := strings.SplitN(fullName, " ", 2)
	if len(r) != 2 {
		return nil, fmt.Errorf("invalid prize name: %s", fullName)
	}
	if len(r[0]) < 2 {
		return nil, fmt.Errorf("invalid prize name: %s", fullName)
	}
	idList := strings.Split(r[0][1:], ",")
	if len(idList) == 0 {
		return nil, fmt.Errorf("invalid prize name: %s", fullName)
	}
	poolID, err := strconv.ParseInt(idList[0], 10, 64)
	if err != nil {
		return nil, err
	}
	goodsID := int64(0)
	if len(idList) > 1 {
		goodsID, err = strconv.ParseInt(idList[1], 10, 64)
		if err != nil {
			return nil, err
		}
	}
	info := prizeNameInfo{poolID: poolID, goodsID: goodsID, name: strings.TrimSpace(r[1])}
	return &info, nil
}

func goodsByID(eventID, goodsID int64) (*goods, error) {
	allGoods := allGoods(eventID)
	var goods *goods
	for _, g := range allGoods {
		if g.ID == goodsID {
			goods = g
			break
		}
	}
	if goods == nil {
		return nil, actionerrors.ErrParams
	}
	_, prizeConfigs, err := getGamePrizeConfigs(eventID, 0, goodsID)
	if err != nil {
		return nil, err
	}
	if len(prizeConfigs) == 0 {
		return nil, actionerrors.ErrServerInternal(fmt.Errorf("找不到 ID 为 %d 的商品的配置", goodsID), nil)
	}
	if len(prizeConfigs) > 1 {
		logger.WithFields(logger.Fields{
			"event_id": eventID,
			"goods_id": goodsID,
		}).Error("数据库商品配置错误")
		return nil, actionerrors.ErrServerInternal(errors.New("数据库配置错误"), nil)
	}
	goods.prize = prizeConfigs[0]
	return goods, nil
}

type goods struct {
	ID            int64
	Price         int64
	ExchangeLimit int64
	gs            goodsSend
	prize         GamePrizeConfig
}

type goodsSend interface {
	send(c *handler.Context)
}

type backpackGift struct {
	ID            int64     // 礼物 ID
	Num           int64     // 礼物数量
	EffectiveTime time.Time // 截止有效时间
}

func (b backpackGift) send(c *handler.Context) {
	sendBackpack(c, b.Num, b.ID, c.UserID(), b.EffectiveTime.Unix())
}

type appearance struct {
	ID                int64         // 外观 ID
	Type              int           // 外观类型
	EffectiveDuration time.Duration // 有效时间
}

func (a appearance) send(c *handler.Context) {
	sendAppearance(c, a.Type, int64(a.EffectiveDuration.Seconds()), a.ID, c.UserID())
}

type giftCustom struct {
	GiftID int64
	Day    int64
}

func (a giftCustom) send(c *handler.Context) {
	sendGiftCustom(c, a.Day, a.GiftID, c.UserID())
}

func allGoods(eventID int64) []*goods {
	if eventID == eventID196 {
		return []*goods{
			{ID: 1, Price: 100, ExchangeLimit: 0, gs: backpackGift{30007, 1, time.Date(2022, 6, 6, 0, 0, 0, 0, time.Local)}},   // "1k 热度卡 × 1"
			{ID: 2, Price: 3500, ExchangeLimit: 0, gs: appearance{50053, appearanceTypeBadge, 7 * 24 * time.Hour}},             // "鲤跃福归称号 × 7d"
			{ID: 3, Price: 5000, ExchangeLimit: 0, gs: appearance{40112, appearanceTypeAvatarFrame, 7 * 24 * time.Hour}},       // "鲤跃福归头像框 × 7d"
			{ID: 4, Price: 6000, ExchangeLimit: 0, gs: appearance{10026, appearanceTypeMessageBubble, 7 * 24 * time.Hour}},     // "鲤跃福归气泡框 × 7d"
			{ID: 5, Price: 15000, ExchangeLimit: 0, gs: appearance{20031, appearanceTypeVehicle, 7 * 24 * time.Hour}},          // "风裁花使座驾 × 7d"
			{ID: 6, Price: 20000, ExchangeLimit: 0, gs: appearance{20032, appearanceTypeVehicle, 7 * 24 * time.Hour}},          // "鲤跃福归座驾 × 7d"
			{ID: 7, Price: 50000, ExchangeLimit: 0, gs: backpackGift{40039, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}}, // "天鹅王座 × 1"
			{ID: 8, Price: 80000, ExchangeLimit: 0, gs: backpackGift{40045, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}}, // "凤鸣天下 × 1"
			{ID: 9, Price: 80000, ExchangeLimit: 0, gs: backpackGift{40046, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}}, // "神浴光礼 × 1"
		}
	}
	return nil
}

func increaseCount(eventID, userID, goodsID int64, expireDuration time.Duration) error {
	key := keys.KeyEventUserExchangeCount2.Format(eventID, userID)
	field := strconv.FormatInt(goodsID, 10)
	pipe := service.Redis.TxPipeline()
	pipe.HIncrBy(key, field, 1)
	pipe.Expire(key, expireDuration)
	_, err := pipe.Exec()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}

func exchangeCount(eventID, userID, goodsID int64) (int64, error) {
	key := keys.KeyEventUserExchangeCount2.Format(eventID, userID)
	count, err := service.Redis.HGet(key, strconv.FormatInt(goodsID, 10)).Int64()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return 0, nil
		}
		return 0, actionerrors.ErrServerInternal(err, nil)
	}
	return count, nil
}
