package event

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
)

/**
 * @api {get} /v2/event/myprizes?event_id=205 猫猫星球获取抽中的奖品列表
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccessExample {json} 获取成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 0, // 免费抽奖的次数
 *           "purchase_times": 0, // 非免费抽奖的次数
 *           "support_free": true, // 是否支持免费抽奖
 *           "point": 0 // 剩余抽奖积分
 *         },
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 1930,
 *             "create_time": 1591877143,
 *             "name": "20 鱼干",
 *             "image_url": "https://static.missevan.com/mimages/202006/16/bc9665720d27d09037bf49b8ed666f0b143626.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 */

/**
 * @api {post} /v2/event/draw?event_id=205 猫猫星球点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 1944,
 *           "create_time": 1591877738,
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/mimages/202006/16/55559bf67b0a142309d48b59b8fd4433143626.png"
 *         },
 *         "point": 0 // 剩余抽奖积分
 *       }
 *     }
 */
func actionMaoMaoStarDraw(c *handler.Context) (handler.ActionResponse, error) {
	userID := c.UserID()
	// 获取是否还有免费抽奖
	var dc drawConfig
	_, err := mevent.FindSimpleWithExtendedFields(eventIDMaoMaoStar, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	date := dc.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventIDMaoMaoStar, date)
	isMember, err := service.Redis.SIsMember(freeDrawUsersKey, userID).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	var poolID int64
	if !isMember {
		// 免费奖池
		poolID = 1
	} else {
		// 积分奖池
		poolID = 2
	}
	prize, drawPoint, err := draw(eventIDMaoMaoStar, userID, poolID)
	if err != nil {
		return nil, err
	}
	// 抽中小鱼干后，发放至用户账户并发送系统通知
	if point, ok := models.IsPrize(prize.Name, latestPointPrizeRegex); ok {
		// 下发小鱼干
		if err = addUserPoint(userID, int64(point)); err != nil {
			return nil, err
		}
		msg := pushservice.SystemMsg{
			UserID:  userID,
			Title:   "M712 八周年庆 - 猫猫星球中奖通知",
			Content: fmt.Sprintf("亲爱的用户，恭喜您在【M712 八周年庆 - 猫猫星球】中获得奖品：【%s】。奖品已发放至您的账户，请注意查收", prize.Name),
		}
		err = service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{msg}, nil)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return handler.M{"prize": prize, "point": drawPoint}, nil
}
