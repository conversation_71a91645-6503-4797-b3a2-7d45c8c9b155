package event

import (
	"fmt"
	"html"
	"regexp"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
)

var latestPointPrizeRegex = regexp.MustCompile(`小鱼干×([1-9]\d*)`)

/**
 * @api {get} /v2/event/myprizes?event_id=161 猫耳学院暑期纳新用户获取自己抽到的奖品和剩余抽奖次数
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 1,     // 免费抽奖次数
 *           "purchase_times": 0, // 非免费抽奖次数
 *           "support_free": true // 当前活动是否支持免费抽奖
 *         },
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 8549,
 *             "create_time": 1611840257,
 *             "name": "头像框",
 *             "image_url": "http://static.missevan.com/mimages/201610/25/f82632ba5db10cb6c6141d92df29414f194403.jpg"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */

/**
 * @api {post} /v2/event/draw?event_id=161 猫耳学院暑期纳新点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 抽到奖品的时间
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */
func actionSummerVacationDraw(c *handler.Context) (handler.ActionResponse, error) {
	userID := c.UserID()
	userPrize, _, err := draw(eventIDSummerVacation, userID, 0)
	if err != nil {
		return nil, err
	}
	if point, ok := models.IsPrize(userPrize.Name, latestPointPrizeRegex); ok {
		// 下发小鱼干
		if err = addUserPoint(userID, int64(point)); err != nil {
			return nil, err
		}
		// 发送系统通知
		util.Go(func() {
			msg := pushservice.SystemMsg{
				UserID:  userID,
				Title:   "【活动】猫耳学院暑期纳新",
				Content: fmt.Sprintf("恭喜您在猫耳学院暑期纳新的幸运抽奖环节中获得：%s。奖品已发放至您的账户，请注意查收~", html.EscapeString(userPrize.Name)),
			}
			if err = service.PushService.SendSystemMsg([]pushservice.SystemMsg{msg}); err != nil {
				logger.Error(err)
				// PASS
			}
		})
	}
	return handler.M{"prize": userPrize}, nil
}
