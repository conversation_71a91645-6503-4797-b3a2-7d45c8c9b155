package event

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/models/db/eventpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
)

/**
 * @api {get} /v2/event/myprizes?event_id=204 盲盒剧场获取抽中的奖品列表
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccessExample {json} 获取成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 0, // 免费抽奖的次数
 *           "purchase_times": 0, // 非免费抽奖的次数
 *           "support_free": true, // 是否支持免费抽奖
 *           "point": 0 // 剩余抽奖积分
 *         },
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 1930,
 *             "create_time": 1591877143,
 *             "name": "小鱼干×20",
 *             "image_url": "https://static.missevan.com/mimages/202006/16/bc9665720d27d09037bf49b8ed666f0b143626.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 1
 *         }
 *       }
 *     }
 */

/**
 * @api {post} /v2/event/draw?event_id=204 盲盒剧场抽奖
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 1944,
 *           "create_time": 1591877738,
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/mimages/202006/16/55559bf67b0a142309d48b59b8fd4433143626.png"
 *         },
 *         "draw": {
 *           "free_times": 0, // 免费抽奖的次数
 *           "purchase_times": 0, // 非免费抽奖的次数
 *           "support_free": true, // 是否支持免费抽奖
 *           "point": 1 // 剩余抽奖积分
 *         },
 *       }
 *     }
 */
func actionTheatreDraw(c *handler.Context) (handler.ActionResponse, error) {
	eventID := eventIDTheatre
	var dc eventpoint.DrawConfig
	e, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if e == nil {
		return nil, actionerrors.ErrEventNotFound
	}
	nowUnix := dc.TimeNow().Unix()
	if nowUnix < e.DrawStartTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "抽奖未开始")
	}
	if nowUnix >= e.DrawEndTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "抽奖已结束")
	}

	userID := c.UserID()
	unlock, err := lockEventUserPoint(eventID, userID)
	if err != nil {
		return nil, err
	}
	defer unlock()

	param := eventpoint.NewDrawParam(e, userID, &dc)
	if param.FreePool == nil && param.PointPool == nil {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "不在抽奖时间内")
	}
	info, prize, err := param.Draw()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if prize == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足")
	}

	// 抽中小鱼干后，发放至用户账户并发送系统通知
	if point, ok := anprize.IsPrize(prize.Name, latestPointPrizeRegex); ok {
		err = addUserPoint(userID, int64(point))
		if err != nil {
			return nil, err
		}
		msg := pushservice.SystemMsg{
			UserID:  userID,
			Title:   "恭喜您在盲盒剧场抽奖活动中奖！",
			Content: fmt.Sprintf("亲爱的用户，恭喜您在【盲盒剧场抽奖活动】中抽到虚拟奖品【%s】。奖品已发放至您的账户，请注意查收。", prize.Name),
		}
		err = service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{msg}, nil)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return &DrawResp{
		Prize: prize,
		Draw:  info,
		Point: util.NewInt64(info.Point),
	}, nil
}
