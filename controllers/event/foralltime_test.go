package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/game"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionForAllTimePrizes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/?event_id=131", false, nil)
	data, err := actionForAllTimePrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	re := data.(drawResult)
	assert.Equal(re.TimesLeft, 0)

	c = handler.NewTestContext(http.MethodGet, "/?event_id=131", true, nil)
	err = service.DB.Create(&game.MGameSubscribe{
		UserID: c.UserID(),
		GameID: gameIDForAllTime,
	}).Error
	require.NoError(err)
	err = service.DB.Where("user_id = ?", c.UserID()).Delete(models.EventUserPrize{}).Error
	require.NoError(err)
	data, err = actionForAllTimePrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	re = data.(drawResult)
	assert.Equal(re.TimesLeft, 1)
}

func TestActionForAllTimeDrawPrizes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	err := service.DB.Where("user_id = ?", 12).Delete(models.EventUserPrize{}).Error
	require.NoError(err)

	err = service.Redis.Del(keys.KeyEventPrizes1.Format(eventIDForAllTime)).Err()
	require.NoError(err)

	startTime := int64(9123456789)
	endTime := startTime + 10
	updateTime := func() {
		dt := drawTime{StartTime: startTime, EndTime: endTime}
		e := mevent.Simple{ID: eventIDForAllTime}
		err := service.DB.
			Assign(map[string]interface{}{"extended_fields": tutil.SprintJSON(dt)}).
			FirstOrCreate(&e).Error
		require.NoError(err)
	}
	updateTime()
	c := handler.NewTestContext(http.MethodPost, "/?event_id=131", true, nil)
	_, err = actionForAllTimeDraw(c)
	assert.EqualError(err, "活动未开始")
	startTime = 0
	endTime = 100
	updateTime()
	c = handler.NewTestContext(http.MethodPost, "/?event_id=131", true, nil)
	_, err = actionForAllTimeDraw(c)
	assert.EqualError(err, "活动已结束")

	endTime += util.TimeNow().Unix() + 100
	updateTime()
	c = handler.NewTestContext(http.MethodPost, "/?event_id=131", true, nil)
	data, err := actionForAllTimeDraw(c)
	require.NoError(err)
	assert.NotNil(data)
	tutil.PrintJSON(data)

	data, err = actionForAllTimePrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	re := data.(drawResult)
	assert.Equal(re.TimesLeft, 0)

	c = handler.NewTestContext(http.MethodPost, "/?event_id=131", true, nil)
	_, err = actionForAllTimeDraw(c)
	assert.EqualError(err, "抽奖次数不足")
}
