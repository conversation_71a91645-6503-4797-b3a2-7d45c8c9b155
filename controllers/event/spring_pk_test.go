package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionSpringPKExchange(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/?event_id=191", true, nil)
	_, err := actionSpringPKExchange(c)
	require.Equal(actionerrors.ErrParams, err)
}
