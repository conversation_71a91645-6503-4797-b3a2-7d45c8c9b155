package event

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func mockGameRoles(c *handler.Context) (bool, error) {
	return true, nil
}

func TestShjhActions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.Redis.LPush(serviceredis.KeyEventGameCode1.Format(eventIDShjh), "p3z7E5zE7kpC").Err()
	require.NoError(err)

	c := handler.NewTestContext("GET", "/?event_id=110", true, nil)

	err = service.DB.Table(models.EventUserPrize{}.TableName()).Where("user_id = ?", c.UserID()).Delete("").Error
	require.NoError(err)

	hasGameRoles = mockGameRoles
	defer func() {
		hasGameRoles = rpcGameRoles
	}()

	data, err := actionShjhMyPrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	re := data.(drawResult)
	assert.Equal(maxUserPrizesNum, re.TimesLeft)
	assert.Empty(re.Prizes)

	_, endTime, err := mevent.EventTime(eventIDShjh)
	require.NoError(err)
	require.NotNil(endTime)
	defer util.SetTimeNow(nil)
	util.SetTimeNow(func() time.Time {
		return time.Unix(*endTime-10, 0)
	})

	c = handler.NewTestContext("POST", "/?event_id=110", true, nil)
	data, err = actionShjhDraw(c)
	require.NoError(err)
	tutil.PrintJSON(data)

	c = handler.NewTestContext("GET", "/?event_id=110", false, nil)
	data, err = actionShjhAllPrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)

	c = handler.NewTestContext("GET", "/?event_id=110", true, nil)
	data, err = actionShjhMyPrizes(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	re = data.(drawResult)
	assert.Equal(maxUserPrizesNum-1, re.TimesLeft)
	assert.Len(re.Prizes, 1)

	err = service.Redis.Del(serviceredis.KeyEventGameCode1.Format(eventIDShjh)).Err()
	require.NoError(err)

	c = handler.NewTestContext("POST", "/?event_id=110", true, nil)
	_, err = actionShjhDraw(c)
	assert.EqualError(err, "抽奖次数不足！")

	util.SetTimeNow(func() time.Time {
		return time.Unix(*endTime+10, 0)
	})
	c = handler.NewTestContext("POST", "/?event_id=110", true, nil)
	_, err = actionShjhDraw(c)
	assert.EqualError(err, "活动已结束")
}
