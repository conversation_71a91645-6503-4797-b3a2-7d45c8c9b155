package event

import (
	"regexp"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
)

/**
 * @api {get} /v2/event/myprizes?event_id=157 直播超粉嘉年华活动用户获取自己抽到的奖品，剩余抽奖次数，剩余碎片数量
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "point": 10,          // 碎片数量
 *           "free_times": 0,      // 免费抽奖次数
 *           "charge_times": 1,    // 非免费抽奖次数
 *           "support_free": false // 当前活动是否支持免费抽奖
 *         },
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 12355,
 *             "create_time": 1618998243,
 *             "name": "头像框 × 7d",
 *             "image_url": "https://www.uat.missevan.com/files/2021-01-27/574270c393f3022bddda21dec27f1a8a.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */

/**
 * @api {post} /v2/event/draw?event_id=157 直播超粉嘉年华活动点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 12355,         // 奖项 ID
 *           "create_time": 1618998243, // 抽到奖品的时间
 *           "name": "头像框 × 7d",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */
func actionSuperFanDraw(c *handler.Context) (handler.ActionResponse, error) {
	userPrize, _, err := draw(eventIDSuperFan, c.UserID(), 0)
	if err != nil {
		return nil, err
	}
	matchAndGivePrize(c, userPrize)
	return handler.M{"prize": userPrize}, nil
}

var exclusiveGift1 = `专属礼物 - 天鹅王座 × 15d`
var exclusiveGift2 = `专属礼物 - 童心奇旅 × 7d`
var avatarFrameRegex = regexp.MustCompile(`“宠爱之名”头像框\s?×\s?([1-9]\d*)d`)
var cardBoxRegex = regexp.MustCompile(`“宠爱之名”名片框\s?×\s?([1-9]\d*)d`)
var carRegex = regexp.MustCompile(`“流心璀璨”座驾\s?×\s?([1-9]\d*)d`)
var sweetRegex = regexp.MustCompile(`甜蜜蜜\s?×\s?([1-9]\d*)`)
var heatCard10K = `10k 热度卡`
var luckyCoinRegex = regexp.MustCompile(`幸运币\s?×\s?([1-9]\d*)`)

// matchAndSendPrize 匹配礼物并且发放礼物
func matchAndGivePrize(c *handler.Context, prize *models.EventUserPrize) {
	prizeName := prize.Name
	userID := prize.UserID
	if prizeName == exclusiveGift1 || prizeName == exclusiveGift2 {
		// 用户专属礼物，活动结束后手动配置
		return
	}
	if n, ok := models.IsPrize(prizeName, avatarFrameRegex); ok {
		// 发放“宠爱之名”头像框到外观中心，外观 ID 是 40006，有效日期为 n 天
		sendAppearance(c, appearanceTypeAvatarFrame, int64(n*24*int(time.Hour.Seconds())), 40006, userID)
	}
	if n, ok := models.IsPrize(prizeName, cardBoxRegex); ok {
		// 发放“宠爱之名”名片框到外观中心，外观 ID 是 30009，有效日期为 n 天
		sendAppearance(c, appearanceTypeCardFrame, int64(n*24*int(time.Hour.Seconds())), 30009, userID)
	}
	if n, ok := models.IsPrize(prizeName, carRegex); ok {
		// 发放“流心璀璨”座驾到外观中心，外观 ID 是 20003，有效日期为 n 天
		sendAppearance(c, appearanceTypeVehicle, int64(n*24*int(time.Hour.Seconds())), 20003, userID)
	}
	if n, ok := models.IsPrize(prizeName, sweetRegex); ok {
		// 发放甜蜜蜜到背包，礼物 ID 是 30004，有效期至：2021.06.30 23:59:59
		sendBackpack(c, int64(n), 30004, userID, time.Date(2021, 6, 30, 23, 59, 59, 0, time.Local).Unix())
	}
	if prizeName == heatCard10K {
		// 发放 10k 热度卡到背包，礼物 ID 是 30002，有效期至：2021.06.30 23:59:59
		sendBackpack(c, int64(1), 30002, userID, time.Date(2021, 6, 30, 23, 59, 59, 0, time.Local).Unix())
	}
	if n, ok := models.IsPrize(prizeName, luckyCoinRegex); ok {
		// 发放幸运币
		addLuckyCoin(c, userID, int64(n), eventIDSuperFan)
	}
}

func sendBackpack(c *handler.Context, num, giftID, userID, endTime int64) {
	reqData := map[string]interface{}{
		"num":      num,
		"gift_id":  giftID,
		"end_time": endTime,
		"user_ids": []int64{userID},
	}
	var res interface{}
	err := c.MRPC("live://user/backpack/add", reqData, &res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func addLuckyCoin(c *handler.Context, userID, point, eventID int64) {
	reqData := map[string]interface{}{
		"event_id": eventID,
		"point":    point,
		"user_id":  userID,
	}
	var res interface{}
	err := c.MRPC("go://event/drawpoint/update", reqData, &res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
