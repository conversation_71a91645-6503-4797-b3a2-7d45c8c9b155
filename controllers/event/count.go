package event

import (
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 奖品已抽取数量信息在活动结束后的保留时间
	defaultKeepTime = 30 * 24 * time.Hour
)

type prizesCounts struct {
	event *mevent.Simple
}

// Incr increments the number of picked prizeID prize by one if it is smaller than its totalNum, except when the totalNum is less than 0,
// which means infinite in this case.
// Incr returns true if the supplied prizeID prize has not ran out.
func (p prizesCounts) Incr(prizeID int64, totalNum int64) (bool, error) {
	if totalNum < 0 {
		return true, nil
	}
	key := keys.KeyEventPrizes1.Format(p.event.ID)
	field := strconv.FormatInt(prizeID, 10)

	pipe := service.Redis.TxPipeline()
	cmd := pipe.HIncrBy(key, field, 1)
	pipe.Expire(key, time.Unix(p.event.EndTime, 0).Add(defaultKeepTime).Sub(util.TimeNow()))
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	count := cmd.Val()
	switch {
	case count > totalNum:
		err = service.Redis.HIncrBy(key, field, -1).Err()
		return false, err
	default:
		return true, nil
	}
}

// GetAll returns all prizes' current counting number
func (p prizesCounts) GetAll() (map[int64]int /*map[prize_id]count_of_prizes_given_to_user*/, error) {
	result := make(map[int64]int)
	key := keys.KeyEventPrizes1.Format(p.event.ID)
	m, err := service.Redis.HGetAll(key).Result()
	if err != nil {
		return nil, err
	}
	for k, v := range m {
		kk, err := strconv.ParseInt(k, 10, 64)
		if err != nil {
			return nil, err
		}
		vv, err := strconv.Atoi(v)
		if err != nil {
			return nil, err
		}
		result[kk] = vv
	}
	return result, nil
}

func (p prizesCounts) ClearAll() error {
	key := keys.KeyEventPrizes1.Format(p.event.ID)
	return service.Redis.Del(key).Err()
}
