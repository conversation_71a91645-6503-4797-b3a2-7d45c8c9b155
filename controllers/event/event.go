package event

import (
	"encoding/json"
	"errors"
	"regexp"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/patrickmn/go-cache"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	usermiddleware "github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/db/eventpoint"
	"github.com/MiaoSiLa/missevan-go/models/db/eventuserprize"
	"github.com/MiaoSiLa/missevan-go/models/drawpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicecache "github.com/MiaoSiLa/missevan-go/service/cache"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 积分活动兑换类型
const (
	eventTypeDraw      = iota // 抽奖
	eventTypeExchange         // 兑换
	eventTypeSubscribe        // 订阅抽奖
)

const (
	eventIDShjh               = 110
	eventIDNyaaWheel          = 116
	eventIDForAllTime         = 131 // 时空中的绘旅人预约抽奖活动
	eventIDLearningChallenges = 133 // 国庆学习挑战抽奖活动
	eventIDNewYearGardenParty = 148 // 新春游园会
	eventIDNewYearGreetings   = 151 // 新岁迎佳音
	eventIDSuperFan           = 157 // 直播超粉嘉年华活动
	eventIDSummerVacation     = 161 // 猫耳学院暑期纳新
	eventIDLightNight         = 163 // 光夜之歌
	eventIDAnnualLive         = 175 // 2021 直播年度盛典
	eventIDSpringPK           = 191 // 春日集结（PK 活动）
	eventID196                = 196 // 风月宴知音活动
	eventIDDramaLottery       = 201 // M星直播活动追剧抽奖
	eventIDMaoMaoStar         = 205 // 猫猫星球抽奖
	eventIDYGL                = 210 // 摇光录预约抽奖活动

	eventIDTheatre int64 = 204 // 盲盒剧场抽奖
	eventID214     int64 = 214 // 星座许愿
)

const (
	gameIDForAllTime = 4 // 时空中的绘旅人游戏 ID
)

var (
	cachePrizes = cache.New(5*time.Minute, 10*time.Minute)

	randSource = util.NewLockedSource(util.TimeNow().Unix())

	// keyEventPoolPrizeConfigs3 奖品配置缓存，三个参数分别为活动 ID，奖池 ID 和商品 ID
	keyEventPoolPrizeConfigs3 servicecache.KeyFormat = "event:%d:pool:%d:goods:%d:prize_configs"
)

var (
	errData               = errors.New("数据错误")
	errGamePrizeNotFound  = errors.New("未找到奖品配置")
	errPrizeNotConfigured = errors.New("抽奖价格未配置")
)

// GamePrizeConfig 游戏活动的奖品
type GamePrizeConfig struct {
	PrizeID  int64  `json:"prize_id"`
	Name     string `json:"name"`
	ImageURL string `json:"image_url"`

	Count int `json:"count"` // 奖品剩余的数量

	weight   int
	totalNum int
}

// DrawResp 抽奖结果响应
type DrawResp struct {
	Prize *eventuserprize.EventUserPrize `json:"prize"`
	Draw  *eventpoint.UserDrawInfo       `json:"draw,omitempty"`
	// WORKAROUND: 兼容旧抽奖组件，待前端使用 draw 后删除
	Point *int64 `json:"point,omitempty"`
}

// HandlerV2 返回 v2/event handler
func HandlerV2(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "v2/event",
		Middlewares: gin.HandlersChain{
			usermiddleware.Middleware(),
		},
		Actions: map[string]*handler.Action{
			"allprizes": handler.NewAction(handler.GET, actionAllPrizes, false),
			"myprizes":  handler.NewAction(handler.GET, actionMyPrizes, true),
			"draw":      handler.NewAction(handler.POST, actionDraw, true),
			"exchange":  handler.NewAction(handler.POST, actionExchange, true),
		},
	}
}

// Handler 返回 event handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "event",
		Actions: map[string]*handler.Action{
			"allprizes": handler.NewAction(handler.GET, actionAllPrizes, false),
			"myprizes":  handler.NewAction(handler.GET, actionMyPrizes, true),
			"draw":      handler.NewAction(handler.POST, actionDraw, true),
			"exchange":  handler.NewAction(handler.POST, actionExchange, true),
		},
	}
}

func actionAllPrizes(c *handler.Context) (handler.ActionResponse, error) {
	eventID, _ := c.GetParamInt("event_id")
	switch eventID {
	case eventIDShjh:
		return actionShjhAllPrizes(c)
	case eventIDNyaaWheel:
	default:
	}
	return nil, actionerrors.ErrEventNotFound
}

func actionMyPrizes(c *handler.Context) (handler.ActionResponse, error) {
	eventID, _ := c.GetParamInt64("event_id")
	// TODO: 旧逻辑待删除
	switch eventID {
	case eventIDShjh:
		return actionShjhMyPrizes(c)
	case eventIDNyaaWheel:
		return actionNyaaWheelMyPrizes(c)
	case eventIDForAllTime:
		return actionForAllTimePrizes(c)
	case eventIDLearningChallenges:
		return actionLearningChallengesPrizes(c)
	case eventIDNewYearGardenParty:
		return getUserPrizeAndDrawInfo(eventIDNewYearGardenParty, 0, c)
	case eventIDNewYearGreetings:
		return getUserPrizeAndDrawInfo(eventIDNewYearGreetings, 0, c)
	case eventIDSuperFan:
		return getUserPrizeAndDrawInfo(eventIDSuperFan, 0, c)
	case eventIDSummerVacation:
		return getUserPrizeAndDrawInfo(eventIDSummerVacation, 0, c)
	case eventIDLightNight:
		return getUserPrizeAndDrawInfo(eventIDLightNight, 0, c)
	case eventIDAnnualLive:
		poolID, _ := c.GetParamInt64("pool_id")
		if poolID != 1 && poolID != 2 {
			return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "奖池错误")
		}
		return getUserPrizeAndDrawInfo(eventIDAnnualLive, poolID, c)
	case eventIDSpringPK:
		return getUserPrizeAndDrawInfo(eventIDSpringPK, 0, c)
	case eventID196:
		return getUserPrizeAndDrawInfo(eventID196, 0, c)
	case eventIDDramaLottery, eventIDYGL:
		return buildMyPrizesResp(eventID, eventTypeSubscribe, c)
	case eventIDMaoMaoStar:
		userID := c.UserID()
		myPrizes, err := getMyPrizes(eventIDMaoMaoStar, userID, 0, c)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		// 因为需要返回 purchase_times 非免费抽奖的次数，而猫猫星球抽奖活动 poolID 为 2 是积分奖池，所以获取抽奖配置信息时，poolID 传 2
		drawInfo, err := getDrawInfo(eventIDMaoMaoStar, userID, 2)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		return prizeAndDrawInfo{
			Draw:       *drawInfo,
			Prizes:     myPrizes.Prizes,
			Pagination: myPrizes.Pagination,
		}, nil
	case eventIDTheatre:
		return buildMyPrizesResp(eventID, eventTypeDraw, c)
	case eventID214:
		return buildMyPrizesResp(eventID, eventTypeExchange, c)
	}
	return nil, actionerrors.ErrEventNotFound
}

type myPrizesResp struct {
	Draw       *eventpoint.UserDrawInfo        `json:"draw"`
	Prizes     []eventuserprize.EventUserPrize `json:"prizes"`
	Pagination util.Pagination                 `json:"pagination"`
}

func buildMyPrizesResp(eventID int64, eventType int, c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, err
	}
	if eventType == eventTypeSubscribe {
		return userapi.GetActivityLotteryRecord(c.UserID(), eventID, p, pageSize, c.ClientIP())
	}

	var dc eventpoint.DrawConfig
	e, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if e == nil {
		return nil, actionerrors.ErrEventNotFound
	}

	var info *eventpoint.UserDrawInfo
	switch eventType {
	case eventTypeDraw:
		info, err = eventpoint.NewDrawParam(e, c.UserID(), &dc).UserDrawInfo()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	case eventTypeExchange:
		info, err = eventpoint.NewExchangeParam(e, c.UserID(), &dc).UserDrawInfo()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	default:
		panic("错误的活动类型")
	}
	data, pa, err := eventuserprize.ListMyPrize(eventID, c.UserID(), p, pageSize)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return myPrizesResp{
		Draw:       info,
		Prizes:     data,
		Pagination: pa,
	}, nil
}

func actionDraw(c *handler.Context) (handler.ActionResponse, error) {
	eventID, _ := c.GetParamInt64("event_id")
	switch eventID {
	case eventIDSuperFan:
		return actionSuperFanDraw(c)
	case eventIDSummerVacation:
		return actionSummerVacationDraw(c)
	case eventIDAnnualLive:
		return actionAnnualCeremonyDraw(c)
	case eventIDMaoMaoStar:
		return actionMaoMaoStarDraw(c)
	case eventIDTheatre:
		return actionTheatreDraw(c)
	}
	return nil, actionerrors.ErrEventNotFound
}

func actionExchange(c *handler.Context) (handler.ActionResponse, error) {
	eventID, _ := c.GetParamInt64("event_id")
	// TODO: 旧活动待删除
	switch eventID {
	case eventIDLightNight:
		return actionLightNightExchange(c)
	case eventIDSpringPK:
		return actionSpringPKExchange(c)
	case eventID196:
		return actionEvent196Exchange(c)
	case eventID214:
		return actionExchange214(c)
	default:
	}
	return nil, actionerrors.ErrEventNotFound
}

type myPrizes struct {
	Prizes     []models.EventUserPrize `json:"prizes"`
	Pagination util.Pagination         `json:"pagination"`
}

type prizeAndDrawInfo struct {
	Draw       drawInfo                `json:"draw"`
	Prizes     []models.EventUserPrize `json:"prizes"`
	Pagination util.Pagination         `json:"pagination"`
}

type drawInfo struct {
	FreeTimes     int64 `json:"free_times"`     // 免费抽奖的次数
	PurchaseTimes int64 `json:"purchase_times"` // 非免费抽奖的次数
	SupportFree   bool  `json:"support_free"`   // 当前活动是否支持免费抽奖
	Point         int64 `json:"point"`          // 剩余抽奖积分
	Cost          int64 `json:"-"`              // 单次抽奖需要消耗的积分
}

func getMyPrizes(eventID, userID int64, poolID int64, c *handler.Context) (*myPrizes, error) {
	userPrize := models.EventUserPrize{
		UserID:  userID,
		EventID: eventID,
	}
	_, prizeConfigs, err := getGamePrizeConfigs(eventID, poolID, 0)
	if err != nil {
		return nil, err
	}
	prizeIDs := make([]int64, len(prizeConfigs))
	for i, v := range prizeConfigs {
		prizeIDs[i] = v.PrizeID
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, err
	}
	prizes, pa, err := userPrize.Find(prizeIDs, p, pageSize)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	for i, mine := range prizes {
		for _, config := range prizeConfigs {
			if mine.PrizeID == config.PrizeID {
				prizes[i].Name = config.Name
				prizes[i].ImageURL = config.ImageURL
				break
			}
		}
		if prizes[i].Name == "" {
			prizes[i].Name = "未知奖品"
			logger.WithField("prize_id", mine.PrizeID).Error("未知奖品")
		}
	}
	return &myPrizes{
		Prizes:     prizes,
		Pagination: pa,
	}, nil
}

func getUserPrizeAndDrawInfo(eventID, poolID int64, c *handler.Context) (*prizeAndDrawInfo, error) {
	userID := c.UserID()
	myPrizes, err := getMyPrizes(eventID, userID, poolID, c)
	if err != nil {
		return nil, err
	}
	info, err := getDrawInfo(eventID, userID, poolID)
	if err != nil {
		return nil, err
	}
	return &prizeAndDrawInfo{
		Draw:       *info,
		Prizes:     myPrizes.Prizes,
		Pagination: myPrizes.Pagination,
	}, nil
}

// drawConfig 抽奖配置，包含每天免费抽奖机会和单次抽奖需要消耗多少积分
type drawConfig struct {
	DrawPrice            json.RawMessage `json:"draw_price,omitempty"`              // 兼容多个奖池的写法。如果是一个奖池，数据库存储格式为 "draw_price": 1，如果是免费奖池，数据库存储格式为 "draw_price": 0，如果是多个奖池，数据库存储格式为 "draw_price": {"1": 10, "2": 20}
	DailyFreeDraw        int64           `json:"daily_free_draw,omitempty"`         // 每天的免费抽奖次数，目前尚未支持单独针对指定奖池的限制，需要时再加下
	DrawPointDailyUpdate bool            `json:"draw_point_daily_update,omitempty"` // 抽奖积分是否是每日更新
	TimeOffset           int64           `json:"time_offset,omitempty"`             // 关于抽奖的时间的偏移量，单位为秒
}

func (dc drawConfig) TimeNow() time.Time {
	if dc.TimeOffset == 0 {
		return util.TimeNow()
	}
	return util.TimeNow().Add(time.Duration(dc.TimeOffset) * time.Second)
}

func (dc *drawConfig) poolDrawPrice(poolID int64) (int64, error) {
	if poolID == 0 {
		var price int64
		err := json.Unmarshal(dc.DrawPrice, &price)
		if err != nil {
			return 0, err
		}
		return price, nil
	}
	var price map[string]int64
	err := json.Unmarshal(dc.DrawPrice, &price)
	if err != nil {
		return 0, err
	}
	cost, ok := price[strconv.FormatInt(poolID, 10)]
	if !ok {
		return 0, errPrizeNotConfigured
	}
	return cost, nil
}

// getDrawInfo 判断是否有抽奖次数,返回抽奖次数的提示和抽奖积分
func getDrawInfo(eventID, userID, poolID int64) (*drawInfo, error) {
	var dc drawConfig
	_, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	poolDrawPrice, err := dc.poolDrawPrice(poolID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if poolDrawPrice < 0 {
		return nil, actionerrors.ErrServerInternal(errors.New("抽奖单价为负数"), logger.Fields{"event_id": eventID, "pool_id": poolID})
	}
	var freeCount int64
	// supportFreeDraw 标识当前活动是否支持免费抽奖
	var supportFreeDraw bool
	if dc.DailyFreeDraw > 0 {
		supportFreeDraw = true
		// 目前只支持一天免费抽一次奖
		date := dc.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
		freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
		isMember, err := service.Redis.SIsMember(freeDrawUsersKey, userID).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if !isMember {
			freeCount = 1
		}
	}
	dp := drawpoint.Param{EventID: eventID, UserID: userID, CurrentTime: dc.TimeNow().Unix(), DailyUpdate: dc.DrawPointDailyUpdate}
	point, err := dp.DrawPoint()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	var purchaseTimes int64
	if poolDrawPrice > 0 {
		purchaseTimes = point / poolDrawPrice
	}
	// 返回非免费抽奖次数和免费抽奖次数等
	return &drawInfo{
		Point:         point,
		PurchaseTimes: purchaseTimes,
		FreeTimes:     freeCount,
		SupportFree:   supportFreeDraw,
		Cost:          poolDrawPrice,
	}, nil
}

// 抽奖
func draw(eventID, userID, poolID int64) (*models.EventUserPrize, int64, error) {
	var dc drawConfig
	e, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, 0, actionerrors.ErrServerInternal(err, nil)
	}
	if e == nil {
		return nil, 0, actionerrors.ErrEventNotFound
	}
	nowUnix := dc.TimeNow().Unix()
	if nowUnix < e.DrawStartTime {
		return nil, 0, actionerrors.ErrForbidden(handler.CodeUnknownError, "抽奖未开始")
	}
	if nowUnix >= e.DrawEndTime {
		return nil, 0, actionerrors.ErrForbidden(handler.CodeUnknownError, "抽奖已结束")
	}
	lock := keys.LockEventUserPoint2.Format(eventID, userID)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, 0, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, 0, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)
	info, err := getDrawInfo(eventID, userID, poolID)
	if err != nil {
		return nil, 0, err
	}
	point := info.Point
	if info.FreeTimes <= 0 && info.PurchaseTimes <= 0 {
		return nil, 0, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足")
	}
	thanksIdx, prizeConfigs, err := getGamePrizeConfigs(eventID, poolID, 0)
	if err != nil {
		return nil, 0, err
	}
	_, rd, err := generateDistribution(prizeConfigs)
	if err != nil {
		return nil, 0, err
	}
	prizeN := rd.NextInt()
	if prizeN > len(prizeConfigs) {
		return nil, 0, actionerrors.ErrServerInternal(errData, nil)
	}
	prizesCounter := prizesCounts{
		event: e,
	}
	ok, err = prizesCounter.Incr(prizeConfigs[prizeN].PrizeID, int64(prizeConfigs[prizeN].totalNum))
	if err != nil {
		return nil, 0, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		logger.WithFields(logger.Fields{
			"prize_id": prizeConfigs[prizeN].PrizeID,
			"user_id":  userID,
		}).Warn("奖品已抽完，设置返回某个不限数量的奖品")
		// 一般返回的都是谢谢参与
		prizeN = thanksIdx
	}

	userPrize := models.EventUserPrize{
		UserID:  userID,
		PrizeID: prizeConfigs[prizeN].PrizeID,
		EventID: eventID,

		Name:     prizeConfigs[prizeN].Name,
		ImageURL: prizeConfigs[prizeN].ImageURL,
	}

	if err := userPrize.Create(); err != nil {
		if prizeN != thanksIdx {
			logger.WithFields(logger.Fields{
				"user_id":  userID,
				"prize_id": userPrize.PrizeID,
			}).Error("抽奖失败，奖品剩余数量可能需要调整")
		}
		return nil, 0, actionerrors.ErrServerInternal(err, nil)
	}

	// 扣除用户抽奖机会
	if info.FreeTimes > 0 {
		date := time.Unix(nowUnix, 0).Format(util.TimeFormatYMDWithNoSpace)
		freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
		// 存放用掉免费抽奖次数的用户列表的 key 的过期时间是在活动结束时间的基础上加 30 天
		expireDuration := time.Unix(e.EndTime, 0).Add(30 * 24 * time.Hour).Sub(util.TimeNow())
		// 使用事务操作，保证 SAdd 的时候的 key 总是有过期时间
		pipe := service.Redis.TxPipeline()
		pipe.SAdd(freeDrawUsersKey, strconv.FormatInt(userID, 10))
		pipe.Expire(freeDrawUsersKey, expireDuration)
		_, err := pipe.Exec()
		if err != nil {
			return nil, 0, actionerrors.ErrServerInternal(err, nil)
		}
	} else {
		// 目前所有活动存储积分的过期时间都是在活动结束时间的基础上加 30 天
		expireDuration := time.Unix(e.EndTime, 0).Add(30 * 24 * time.Hour).Sub(util.TimeNow())
		dp := drawpoint.Param{EventID: eventID, UserID: userID, CurrentTime: nowUnix, DailyUpdate: dc.DrawPointDailyUpdate}
		point, err = dp.MinusDrawPoint(info.Cost, expireDuration)
		if err != nil {
			return nil, 0, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return &userPrize, point, nil
}

var pointRegex = regexp.MustCompile(`^([1-9]\d*)\s*鱼干$`)

// addUserPoint 自动下发小鱼干
func addUserPoint(userID, point int64) error {
	if err := user.AddUserPoint(userID, int(point)); err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}

func generateDistribution(prizeConfigs []GamePrizeConfig) (w []int, d util.Distribution, err error) {
	w = make([]int, len(prizeConfigs))
	for i, v := range prizeConfigs {
		w[i] = v.weight
	}

	d, err = util.NewDiscreteDistribution(w, randSource, false)
	return
}

// getGamePrizeConfigs 每次返回了新复制出来的 results
func getGamePrizeConfigs(eventID, poolID, goodsID int64) (prizeGameCodeIdx int, results []GamePrizeConfig, err error) {
	cacheKey := keyEventPoolPrizeConfigs3.Format(eventID, poolID, goodsID)
	var prizeConfig []GamePrizeConfig
	obj, ok := cachePrizes.Get(cacheKey)
	if ok {
		prizeConfig = obj.([]GamePrizeConfig)
	} else {
		prizeConfig, err = getGamePrizeConfigsFromDB(eventID, poolID, goodsID)
		if err != nil {
			return prizeGameCodeIdx, nil, err
		}
		cachePrizes.Set(cacheKey, prizeConfig, 5*time.Minute)
	}
	for i, v := range prizeConfig {
		if v.totalNum == -1 {
			prizeGameCodeIdx = i
			break
		}
	}
	if prizeGameCodeIdx == -1 {
		return prizeGameCodeIdx, nil, errGamePrizeNotFound
	}
	results = make([]GamePrizeConfig, len(prizeConfig))
	copy(results, prizeConfig)
	return prizeGameCodeIdx, results, nil
}

// getGamePrizeConfigsFromDB returns configs sorted by weights, if poolID is 0, returns all prize configs
func getGamePrizeConfigsFromDB(eventID, poolID, goodsID int64) ([]GamePrizeConfig, error) {
	// TODO: 应该查询一次数据库就存入所有奖池的缓存
	configs, err := models.ListPrizes(eventID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	results := make([]GamePrizeConfig, 0, len(configs))
	allPool := 1
	allGoods := 2
	flag := 0
	if poolID == 0 {
		flag |= allPool
	}
	if goodsID == 0 {
		flag |= allGoods
	}
	for _, v := range configs {
		idInfo, err := parsePrizeName(v.Name)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		found := false
		switch flag {
		case allPool | allGoods:
			found = true
		case allGoods:
			found = idInfo.poolID == poolID
		case allPool:
			found = idInfo.goodsID == goodsID
		case 0:
			found = idInfo.goodsID == goodsID && idInfo.poolID == poolID
		}
		if found {
			v.Name = idInfo.name
			results = append(results, GamePrizeConfig{
				PrizeID:  v.ID,
				Name:     v.Name,
				ImageURL: v.Pic,
				weight:   v.Probability,
				totalNum: v.Num,
			})
		}
	}
	sort.Slice(results, func(i, j int) bool {
		return results[i].weight < results[j].weight
	})
	return results, nil
}

func lockEventUserPoint(eventID, userID int64) (func(), error) {
	lockKey := keys.LockEventUserPoint2.Format(eventID, userID)
	ok, err := service.Redis.SetNX(lockKey, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	return func() {
		err := service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			return
		}
	}, nil
}
