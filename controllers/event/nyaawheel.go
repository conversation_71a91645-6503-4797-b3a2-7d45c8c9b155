package event

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

var (
	errLimitFree  = actionerrors.ErrForbidden(handler.CodeUserLimit, "今天的免费抽奖达到上限")
	errLimitScore = actionerrors.ErrForbidden(handler.CodeUserLimit, "抽奖积分不足")
)

type configuration struct {
	prizesFree  []GamePrizeConfig
	prizesScore []GamePrizeConfig
	distrFree   util.Distribution
	distrScore  util.Distribution
}

var m sync.Mutex

// 不要直接修改返回的指针所指向的 configuration，需要的时候复制
func getPrizeConfig(event *mevent.Simple) *configuration {
	m.Lock()
	defer m.Unlock()

	key := keyEventPoolPrizeConfigs3.Format(eventIDNyaaWheel, 0, 0)
	conf, found := cachePrizes.Get(key)
	if found {
		return conf.(*configuration)
	}

	prizes, err := models.ListPrizes(eventIDNyaaWheel)
	if err != nil {
		logger.Error(err)
		return nil
	}
	prizesFree := make([]GamePrizeConfig, 0, len(prizes)/2)
	prizesScore := make([]GamePrizeConfig, 0, len(prizes)/2)
	for _, v := range prizes {
		s := strings.Split(v.Pic, "?")
		if len(s) < 2 {
			logger.WithField("pic", v.Pic).Error(errData)
			return nil
		}
		imageURL := s[0]
		if s[1] == "0" {
			prizesFree = append(prizesFree, GamePrizeConfig{
				PrizeID:  v.ID,
				Name:     v.Name,
				ImageURL: imageURL,
				weight:   v.Probability,
				totalNum: v.Num,
			})
		} else {
			prizesScore = append(prizesScore, GamePrizeConfig{
				PrizeID:  v.ID,
				Name:     v.Name,
				ImageURL: imageURL,
				weight:   v.Probability,
				totalNum: v.Num,
			})
		}
	}
	prizesCounter := prizesCounts{
		event: event,
	}
	mapPrizeCounted, err := prizesCounter.GetAll()
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		// 奖品抽完的情况下，权重算作 0
		for i, v := range prizesFree {
			if v.totalNum >= 0 && mapPrizeCounted[v.PrizeID] >= v.totalNum {
				prizesFree[i].weight = 0
			}
		}
		for i, v := range prizesScore {
			if v.totalNum >= 0 && mapPrizeCounted[v.PrizeID] >= v.totalNum {
				prizesScore[i].weight = 0
			}
		}
	}
	_, distrFree, err := generateDistribution(prizesFree)
	if err != nil {
		logger.Error(err)
		return nil
	}
	_, distrScore, err := generateDistribution(prizesScore)
	if err != nil {
		logger.Error(err)
		return nil
	}

	newConf := &configuration{
		prizesFree:  prizesFree,
		prizesScore: prizesScore,
		distrFree:   distrFree,
		distrScore:  distrScore,
	}
	cachePrizes.Set(key, newConf, 5*time.Minute)
	return newConf
}

/**
 * @api {get} /v2/event/myprizes?event_id=116 喵喵转盘用户获取自己抽到的奖品
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 1930,
 *             "create_time": 1591877143,
 *             "name": "20 鱼干",
 *             "image_url": "https://static.missevan.com/mimages/202006/16/bc9665720d27d09037bf49b8ed666f0b143626.png"
 *           }
 *           ...
 *         ],
 *         "free_chance": 0, // 是否有免费抽奖机会：是 - 1, 否 - 0
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {String} info "请先登录"
 */
func actionNyaaWheelMyPrizes(c *handler.Context) (handler.ActionResponse, error) {
	var thisEvent mevent.Simple
	err := service.DB.Where("id = ?", eventIDNyaaWheel).Take(&thisEvent).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrEventNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	prizeConfig := getPrizeConfig(&thisEvent)
	if prizeConfig == nil {
		return nil, actionerrors.ErrServerInternal(errData, nil)
	}

	myprizes := []models.EventUserPrize{}
	err = service.DB.
		Where("user_id = ? AND event_id = ?", c.UserID(), eventIDNyaaWheel).
		Order("id ASC").
		Find(&myprizes).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	for i := range myprizes {
		assigned := false
		for _, v := range prizeConfig.prizesFree {
			if myprizes[i].PrizeID == v.PrizeID {
				myprizes[i].Name = v.Name
				myprizes[i].ImageURL = v.ImageURL
				assigned = true
				break
			}
		}
		if assigned {
			continue
		}
		for _, v := range prizeConfig.prizesScore {
			if myprizes[i].PrizeID == v.PrizeID {
				myprizes[i].Name = v.Name
				myprizes[i].ImageURL = v.ImageURL
				break
			}
		}
	}

	// 返回是否有免费抽奖机会（一天一次）
	lock := keys.LockEventUserPoint2.Format(eventIDNyaaWheel, c.UserID())
	exists, err := service.Redis.Exists(lock).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	return handler.M{
		"prizes":      myprizes,
		"free_chance": 1 - exists,
	}, nil
}

/*
 * @api {post} /v2/event/draw?event_id=116&free=0,1 喵喵转盘点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 1944,
 *           "create_time": 1591877738,
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/mimages/202006/16/55559bf67b0a142309d48b59b8fd4433143626.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403 请先登录) {Number} code CodeLoginRequired = 100010006
 * @apiError (403 请先登录) {String} info "请先登录"
 *
 * @apiError (403 活动未开始) {Number} code CodeUnknownError = 100010007
 * @apiError (403 活动未开始) {String} info "活动未开始"
 *
 * @apiError (403 活动已结束) {Number} code CodeUnknownError = 100010007
 * @apiError (403 活动已结束) {String} info "活动已结束"
 *
 * @apiError (403 抽奖积分不足) {Number} code CodeUserLimit = 200020005
 * @apiError (403 抽奖积分不足) {String} info "抽奖积分不足"
 *
 * @apiError (403 今天的免费抽奖达到上限) {Number} code CodeUserLimit = 200020005
 * @apiError (403 今天的免费抽奖达到上限) {String} info "今天的免费抽奖达到上限"
 */
func actionNyaaWheelDraw(c *handler.Context) (handler.ActionResponse, error) {
	var thisEvent mevent.Simple
	err := service.DB.Where("id = ?", eventIDNyaaWheel).Take(&thisEvent).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrEventNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if thisEvent.ExtendedFields == "" {
		return nil, actionerrors.ErrServerInternal(errors.New("extended_fields 为空"), nil)
	}

	var extendedFields struct {
		DrawStartTime int64 `json:"draw_start_time"`
	}
	err = json.Unmarshal([]byte(thisEvent.ExtendedFields), &extendedFields)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	now := util.TimeNow()
	if now.Before(time.Unix(extendedFields.DrawStartTime, 0)) {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动未开始")
	}
	if now.After(time.Unix(thisEvent.EndTime, 0)) {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动已结束")
	}

	prizeConfig := getPrizeConfig(&thisEvent)
	if prizeConfig == nil {
		return nil, actionerrors.ErrServerInternal(errData, nil)
	}

	free, _ := c.GetParamInt("free")
	if free != 0 {
		lock := keys.LockEventUserPoint2.Format(eventIDNyaaWheel, c.UserID())
		expiredAt := util.NextDayTime(now)
		ok, err := service.Redis.SetNX(lock, "1", expiredAt.Sub(now)).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if !ok {
			return nil, errLimitFree
		}
	} else {
		key := serviceredis.KeyCarnivalPointUserID1.Format(c.UserID())
		remainingPoint, err := service.Redis.HIncrBy(key, "total_point", -100).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if remainingPoint < 0 {
			err = service.Redis.HIncrBy(key, "total_point", 100).Err()
			if err != nil {
				return nil, actionerrors.ErrServerInternal(err, nil)
			}
			return nil, errLimitScore
		}
	}

	var (
		prizeConf []GamePrizeConfig
		distr     util.Distribution
	)
	if free != 0 {
		prizeConf = prizeConfig.prizesFree
		distr = prizeConfig.distrFree
	} else {
		prizeConf = prizeConfig.prizesScore
		distr = prizeConfig.distrScore
	}

	index := distr.NextInt()
	if index >= len(prizeConf) {
		return nil, actionerrors.ErrServerInternal(errData, nil)
	}
	nyaaPrizesCounter := &prizesCounts{
		event: &thisEvent,
	}
	ok, err := nyaaPrizesCounter.Incr(prizeConf[index].PrizeID, int64(prizeConf[index].totalNum))
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		var found bool
		for i, v := range prizeConf {
			if v.Name == "谢谢参与" {
				index = i
				found = true
				break
			}
		}
		if !found {
			logger.WithField("free", free).Error(`没有找到“谢谢参与”奖`)
			return nil, actionerrors.ErrServerInternal(errData, nil)
		}
	}
	userPrize := models.EventUserPrize{
		UserID:  c.UserID(),
		PrizeID: prizeConf[index].PrizeID,
		EventID: eventIDNyaaWheel,

		Name:     prizeConf[index].Name,
		ImageURL: prizeConf[index].ImageURL,
	}

	err = service.DB.Create(&userPrize).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	if point, ok := models.IsPrize(userPrize.Name, pointRegex); ok {
		err = addPointToUser(point, userPrize.UserID)
		if err != nil {
			return nil, err
		}
	}

	return handler.M{"prize": userPrize}, nil
}

func addPointToUser(point int, userID int64) error {
	err := service.DB.Table(user.MowangskUser{}.TableName()).
		Where("id = ?", userID).
		Update("point", gorm.Expr("point + ?", point)).Error
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	msg := pushservice.SystemMsg{
		UserID:  userID,
		Title:   "【M712 狂欢祭喵喵转盘】恭喜中奖",
		Content: fmt.Sprintf("亲爱的用户，恭喜您在【M712 狂欢祭喵喵转盘】中获得奖品: %d 鱼干。奖品已发放至您的账户，请注意查收。", point),
	}
	err = service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{msg},
		&pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
