package event

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLightNightExchange(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/?event_id=163", true, nil)
	_, err := actionLightNightExchange(c)
	assert.Equal(actionerrors.ErrParams, err)
}

func TestExchangeGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1002)
	goodsID := int64(1)
	eventID := int64(eventID196)

	newContext := func(confirm util.BitMask) *handler.Context {
		body := exchangeParams{
			GoodsID: goodsID,
			Confirm: confirm,
		}
		c := handler.NewTestContext(http.MethodPost, fmt.Sprintf("/?event_id=%d", eventID), true, body)
		userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
			return &user.User{
				IUser: user.IUser{
					ID: userID,
				},
			}, nil
		})
		c.C.Set("user", userFunc)
		return c
	}
	e, err := mevent.FindSimple(eventID)
	require.NoError(err)
	assert.NotNil(e)
	util.SetTimeNow(func() time.Time {
		return time.Unix(e.DrawStartTime, 0).Add(time.Minute)
	})
	defer util.SetTimeNow(nil)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	_, err = exchangeGoods(eventID, newContext(0))
	require.EqualError(err, "兑换币不足！")
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(3000), Member: strconv.FormatInt(userID, 10)}).Err())
	lock := keys.LockEventUserPoint2.Format(eventID, userID)
	require.NoError(service.Redis.SetNX(lock, "1", time.Minute).Err())
	limitKey := keys.KeyEventUserExchangeCount2.Format(eventID196, userID)
	require.NoError(service.Redis.Del(limitKey).Err())
	_, err = exchangeGoods(eventID, newContext(1))
	require.EqualError(err, "操作频繁，请稍后再试")
	key = keys.KeyEventUserExchangeCount2.Format(eventID, userID)
	field := strconv.FormatInt(goodsID, 10)
	require.NoError(service.Redis.HDel(key, field).Err())
	require.NoError(service.Redis.Del(lock).Err())
	resp, err := exchangeGoods(eventID, newContext(1))
	require.NoError(err)
	tutil.PrintJSON(resp)
}

func TestExchangeable(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1003)
	goodsID := int64(1)
	now := util.TimeNow()
	eventID := int64(eventID196)
	event := &mevent.Simple{
		ID:            eventID,
		DrawStartTime: now.Add(-time.Minute).Unix(),
		DrawEndTime:   now.Add(2 * time.Minute).Unix(),
	}
	dc := drawConfig{
		TimeOffset: 0,
	}
	_, msg, err := exchangeable(event, dc, userID, 10)
	require.Equal(actionerrors.ErrParams, err)
	assert.Empty(msg)
	dc.TimeOffset = -int64(2 * time.Minute.Seconds())
	_, _, err = exchangeable(event, dc, userID, goodsID)
	require.Equal(actionerrors.ErrForbidden(handler.CodeUnknownError, "当前不在兑换时间内哦~"), err)
	dc.TimeOffset = int64(time.Minute.Seconds())
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	_, _, err = exchangeable(event, dc, userID, goodsID)
	require.Equal(actionerrors.ErrForbidden(handler.CodeUnknownError, "兑换币不足！"), err)
	limitKey := keys.KeyEventUserExchangeCount2.Format(eventID, userID)
	require.NoError(service.Redis.Del(limitKey).Err())
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(3000), Member: strconv.FormatInt(userID, 10)}).Err())
	g, msg, err := exchangeable(event, dc, userID, goodsID)
	require.NoError(err)
	assert.Equal("确定要消耗 100 通宝兑换<br>1k 热度卡 × 1 吗？", msg)
	assert.Equal("1k 热度卡 × 1", g.prize.Name)
	key = keys.KeyEventUserExchangeCount2.Format(eventID, userID)
	require.NoError(service.Redis.HSet(key, strconv.FormatInt(goodsID, 10), 3).Err())
}

func TestMatchAndGiveGoods(t *testing.T) {
	c := handler.NewTestContext(http.MethodGet, "", true, nil)

	goods := allGoods(eventID196)
	giftCustom := goods[len(goods)-1].gs
	giftCustom.send(c)
}

func TestIncreaseCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1004)
	goodsID := int64(2)
	expireTime := 2 * time.Second
	key := keys.KeyEventUserExchangeCount2.Format(eventIDLightNight, userID)
	field := strconv.FormatInt(goodsID, 10)
	require.NoError(service.Redis.HDel(key, field).Err())
	require.NoError(increaseCount(eventIDLightNight, userID, goodsID, expireTime))
	count, err := service.Redis.HGet(key, strconv.FormatInt(goodsID, 10)).Int64()
	require.NoError(err)
	assert.Equal(int64(1), count)
	require.NoError(service.Redis.HDel(key, field).Err())
}

func TestExchangeCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1005)
	goodsID := int64(2)
	eventID := int64(eventIDLightNight)
	key := keys.KeyEventUserExchangeCount2.Format(eventIDLightNight, userID)
	field := strconv.FormatInt(goodsID, 10)
	require.NoError(service.Redis.HDel(key, field).Err())
	count, err := exchangeCount(eventID, userID, goodsID)
	require.NoError(err)
	assert.Equal(int64(0), count)
	require.NoError(service.Redis.HIncrBy(key, field, 1).Err())
	count, err = exchangeCount(eventID, userID, goodsID)
	require.NoError(err)
	assert.Equal(int64(1), count)
	require.NoError(service.Redis.HDel(key, field).Err())
}

func TestParsePrizeName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	name := "1k 热度卡 × 1"
	info, err := parsePrizeName(name)
	require.NoError(err)
	require.NotNil(info)
	assert.Zero(info.poolID)
	assert.Zero(info.goodsID)
	assert.Equal("1k 热度卡 × 1", info.name)
	name = "#1 1k 热度卡 × 1"
	info, err = parsePrizeName(name)
	require.NoError(err)
	require.NotNil(info)
	assert.Equal(int64(1), info.poolID)
	assert.Zero(info.goodsID)
	assert.Equal("1k 热度卡 × 1", info.name)
	name = "#2,3 1k 热度卡 × 1"
	info, err = parsePrizeName(name)
	require.NoError(err)
	require.NotNil(info)
	assert.Equal(int64(2), info.poolID)
	assert.Equal(int64(3), info.goodsID)
	assert.Equal("1k 热度卡 × 1", info.name)
	name = "# 1k 热度卡 × 1"
	_, err = parsePrizeName(name)
	require.Equal(fmt.Errorf("invalid prize name: %s", name), err)
	name = "#,1 1k 热度卡 × 1"
	_, err = parsePrizeName(name)
	require.NotNil(err)
}

func TestGoodsByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goods, err := goodsByID(196, 1)
	require.NoError(err)
	require.NotNil(goods)
	assert.Equal("1k 热度卡 × 1", goods.prize.Name)

	_, err = goodsByID(196, 10)
	require.Equal(actionerrors.ErrParams, err)
}

func TestAllGoods(t *testing.T) {
	assert := assert.New(t)
	assert.Equal([]*goods{
		{ID: 1, Price: 100, ExchangeLimit: 0, gs: backpackGift{30007, 1, time.Date(2022, 6, 6, 0, 0, 0, 0, time.Local)}},
		{ID: 2, Price: 3500, ExchangeLimit: 0, gs: appearance{50053, appearanceTypeBadge, 7 * 24 * time.Hour}},
		{ID: 3, Price: 5000, ExchangeLimit: 0, gs: appearance{40112, appearanceTypeAvatarFrame, 7 * 24 * time.Hour}},
		{ID: 4, Price: 6000, ExchangeLimit: 0, gs: appearance{10026, appearanceTypeMessageBubble, 7 * 24 * time.Hour}},
		{ID: 5, Price: 15000, ExchangeLimit: 0, gs: appearance{20031, appearanceTypeVehicle, 7 * 24 * time.Hour}},
		{ID: 6, Price: 20000, ExchangeLimit: 0, gs: appearance{20032, appearanceTypeVehicle, 7 * 24 * time.Hour}},
		{ID: 7, Price: 50000, ExchangeLimit: 0, gs: backpackGift{40039, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}},
		{ID: 8, Price: 80000, ExchangeLimit: 0, gs: backpackGift{40045, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}},
		{ID: 9, Price: 80000, ExchangeLimit: 0, gs: backpackGift{40046, 1, time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)}},
	}, allGoods(eventID196))
}
