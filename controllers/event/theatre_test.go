package event

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/db/eventpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionTheatreDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventID := eventIDTheatre
	e, err := mevent.FindSimple(eventID)
	require.NoError(err)
	require.NotNil(e)

	// 活动不存在
	err = service.DB.Table(e.TableName()).Where("id = ?", e.ID).Delete("").Error
	require.NoError(err)
	url := fmt.Sprintf("/?event_id=%d", eventID)
	c := handler.NewTestContext(http.MethodPost, url, true, nil)
	_, err = actionDraw(c)
	assert.Equal(actionerrors.ErrEventNotFound, err)

	// 抽奖未开始
	now := time.Date(2022, 06, 28, 0, 0, 0, 0, time.Local)
	e.DrawStartTime = now.Unix()
	e.DrawEndTime = now.Unix() + 10
	require.NoError(service.DB.Save(e).Error)
	util.SetTimeNow(func() time.Time {
		return now.Add(-time.Second)
	})
	defer util.SetTimeNow(nil)
	_, err = actionDraw(c)
	assert.EqualError(err, "抽奖未开始")

	util.SetTimeNow(func() time.Time {
		return now.Add(10 * time.Second)
	})
	_, err = actionDraw(c)
	assert.EqualError(err, "抽奖已结束")

	util.SetTimeNow(func() time.Time { return now })
	_, err = actionDraw(c)
	assert.EqualError(err, "不在抽奖时间内")

	// 抽奖成功
	dc := eventpoint.DrawConfig{
		DrawPools: []*eventpoint.PoolConfig{
			{
				PoolID:   1,
				PoolType: eventpoint.PoolTypeDraw,
			},
		},
	}
	e.ExtendedFields = tutil.SprintJSON(dc)
	require.NoError(service.DB.Save(e).Error)
	date := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
	require.NoError(service.Redis.SRem(freeKey, 12).Err())
	var ok bool
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(interface{}) (interface{}, error) {
		ok = true
		return "success", nil
	})
	defer cancel()
	r, err := actionDraw(c)
	require.NoError(err)
	var resp *DrawResp
	require.IsType(resp, r)
	resp = r.(*DrawResp)
	assert.NotNil(resp.Prize)
	require.NotNil(resp.Draw)
	assert.Zero(resp.Draw.FreeTimes)
	assert.True(ok)

	// 次数不足
	_, err = actionDraw(c)
	assert.EqualError(err, "抽奖次数不足")
}
