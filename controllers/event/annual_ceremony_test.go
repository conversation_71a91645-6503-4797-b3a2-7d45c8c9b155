package event

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionAnnualCeremonyDraw(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, fmt.Sprintf("/?event_id=%d&pool_id=%d", eventIDAnnualLive, 1), true, nil)
	c.User().ID = 12999
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventIDAnnualLive)
	userID := c.UserID()
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(100), Member: userID}).Err())
	e, err := mevent.FindSimple(eventIDAnnualLive)
	require.NoError(err)
	defer util.SetTimeNow(nil)
	util.SetTimeNow(func() time.Time {
		return time.Unix(e.DrawEndTime, 0).Add(-time.Minute)
	})
	data, err := actionAnnualCeremonyDraw(c)
	require.NoError(err)
	tutil.PrintJSON(data)
}
