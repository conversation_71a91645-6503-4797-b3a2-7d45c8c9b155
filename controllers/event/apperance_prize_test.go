package event

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
)

func TestNewAppearancePrize(t *testing.T) {
	assert := assert.New(t)

	name := `心动捕手头像框\s×\s([1-9]\d*)d`
	a := newAppearancePrize(name, 21)
	assert.Equal(a.Regexp.String(), name)
	assert.Equal(int64(21), a.PrizeID)
}

func TestAppearanceTypeByName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(appearanceTypeAvatarFrame, appearanceTypeByName("心动捕手头像框 × 7d"))
	assert.Equal(appearanceTypeMessageBubble, appearanceTypeByName("夜雾玫瑰气泡框 × 7d"))
	assert.Equal(appearanceTypeVehicle, appearanceTypeByName("暮与同酒座驾 × 7d"))
	assert.Equal(appearanceTypeUnknown, appearanceTypeByName("3k 热度卡"))
}

func TestHandleAppearancePrize(t *testing.T) {
	prizeList := []string{
		"心动捕手头像框 × 7d", "小小园丁头像框 × 7d", "宠爱之名头像框 × 7d", "煦光颂歌头像框 × 7d",
		"永夜回音头像框 × 7d", "化蝶仙头像框 × 7d", "晨曦百合气泡框 × 7d", "夜雾玫瑰气泡框 × 7d",
		"星河入梦座驾 × 3d", "Super Love 头像框 × 14d", "化蝶仙头像框 × 7d", "光焰华冠头像框 × 14d",
		"夜华荣冕头像框 × 14d", "流心璀璨座驾 × 7d", "朝以同歌座驾 × 7d", "暮与同酒座驾 × 7d",
	}
	p := &models.EventUserPrize{UserID: 12}
	c := handler.NewTestContext(http.MethodPost, fmt.Sprintf("/?event_id=%d&pool_id=%d", eventIDSuperFan, 1), true, nil)
	for _, v := range prizeList {
		p.Name = v
		handleAppearancePrize(c, p)
	}
}

func TestSendAppearance(t *testing.T) {
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	sendAppearance(c, appearanceTypeVehicle, int64(24*int(time.Hour.Seconds())), 11, c.UserID())
}

func TestSendGiftCustom(t *testing.T) {
	c := handler.NewTestContext(http.MethodPost, "/?event_id=191", true, nil)
	sendGiftCustom(c, 1, 13, 12)
}
