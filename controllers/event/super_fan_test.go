package event

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSuperFanDraw(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, fmt.Sprintf("/?event_id=%d", eventIDSuperFan), true, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 12999,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventIDSuperFan)
	userID := c.UserID()
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(100), Member: userID}).Err())
	e, err := mevent.FindSimple(eventIDSuperFan)
	require.NoError(err)
	defer util.SetTimeNow(nil)
	util.SetTimeNow(func() time.Time {
		return time.Unix(e.DrawEndTime, 0).Add(-time.Minute)
	})
	data, err := actionSuperFanDraw(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	prize, ok := data.(handler.M)
	require.True(ok)
	up, ok := prize["prize"]
	require.True(ok)
	userPrize, ok := up.(*models.EventUserPrize)
	require.True(ok)
	util.SetTimeNow(nil)
	tutil.PrintJSON(userPrize)
}

func TestMatchAndGivePrize(t *testing.T) {
	c := handler.NewTestContext(http.MethodGet, "/?event_id=157", true, nil)

	prize := &models.EventUserPrize{
		UserID: c.UserID(),
	}
	prize.Name = "“宠爱之名”头像框 × 7d"
	matchAndGivePrize(c, prize)
	prize.Name = "“宠爱之名”名片框 × 7d"
	matchAndGivePrize(c, prize)
	prize.Name = "“流心璀璨”座驾 × 7d"
	matchAndGivePrize(c, prize)
	prize.Name = "专属礼物 - 天鹅王座 × 15d"
	matchAndGivePrize(c, prize)
	prize.Name = "专属礼物 - 童心奇旅 × 7d"
	matchAndGivePrize(c, prize)
	prize.Name = "幸运币 × 6"
	matchAndGivePrize(c, prize)
	prize.Name = "10k 热度卡"
	matchAndGivePrize(c, prize)
	prize.Name = "甜蜜蜜 × 999"
	matchAndGivePrize(c, prize)
}

func TestSend(t *testing.T) {
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	sendBackpack(c, 11, 11, c.UserID(), util.TimeNow().Add(time.Hour).Unix())
	addLuckyCoin(c, c.UserID(), 11, eventIDSuperFan)
}

func TestPrizeRegexp(t *testing.T) {
	assert := assert.New(t)

	n, ok := models.IsPrize("“宠爱之名”头像框 × 7d", avatarFrameRegex)
	assert.True(ok)
	assert.Equal(7, n)
	n, ok = models.IsPrize("“宠爱之名”头像框×7d", avatarFrameRegex)
	assert.True(ok)
	assert.Equal(7, n)

	n, ok = models.IsPrize("“宠爱之名”名片框 × 7d", cardBoxRegex)
	assert.True(ok)
	assert.Equal(7, n)
	n, ok = models.IsPrize("“宠爱之名”名片框× 7d", cardBoxRegex)
	assert.True(ok)
	assert.Equal(7, n)

	n, ok = models.IsPrize("“流心璀璨”座驾 × 7d", carRegex)
	assert.True(ok)
	assert.Equal(7, n)
	n, ok = models.IsPrize("“宠爱之名”名片框×7d", cardBoxRegex)
	assert.True(ok)
	assert.Equal(7, n)

	n, ok = models.IsPrize("甜蜜蜜 × 999", sweetRegex)
	assert.True(ok)
	assert.Equal(999, n)
	n, ok = models.IsPrize("甜蜜蜜×999", sweetRegex)
	assert.True(ok)
	assert.Equal(999, n)

	n, ok = models.IsPrize("幸运币 × 6", luckyCoinRegex)
	assert.True(ok)
	assert.Equal(6, n)
	n, ok = models.IsPrize("幸运币 ×5", luckyCoinRegex)
	assert.True(ok)
	assert.Equal(5, n)

	n, ok = models.IsPrize("小鱼干×20", latestPointPrizeRegex)
	assert.True(ok)
	assert.Equal(20, n)
}
