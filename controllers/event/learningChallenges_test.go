package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLearningChallengesPrizes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(service.DB.Where("user_id = ? and event_id = ?", 12, 133).Delete(&models.EventUserPrize{}).Error)
	require.NoError(service.Redis.Del(generateConditionsDrawKey()).Err())

	c := handler.NewTestContext(http.MethodGet, "/?event_id=133", true, nil)
	data, err := actionLearningChallengesPrizes(c)
	require.NoError(err)
	re := data.(drawResult)
	assert.Empty(re.Prizes)
	assert.Zero(re.TimesLeft)

	userPrize := &models.EventUserPrize{
		UserID:     12,
		EventID:    eventIDLearningChallenges,
		PrizeID:    4981,
		CreateTime: util.TimeNow().Unix(),
	}
	require.NoError(service.DB.Create(&userPrize).Error)

	data, err = actionLearningChallengesPrizes(c)
	require.NoError(err)
	re = data.(drawResult)
	assert.NotEmpty(re.Prizes)
	assert.Zero(re.TimesLeft)

	require.NoError(service.Redis.HSet(generateConditionsDrawKey(), 12, 1).Err())
	data, err = actionLearningChallengesPrizes(c)
	require.NoError(err)
	re = data.(drawResult)
	assert.NotEmpty(re.Prizes)
	assert.NotZero(re.TimesLeft)
}

func TestActionLearningChallengesDraw(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	require.NoError(service.DB.Where("user_id = ? and event_id = ?", 12, 133).Delete(&models.EventUserPrize{}).Error)

	require.NoError(service.Redis.Del(keys.KeyEventPrizes1.Format(eventIDLearningChallenges)).Err())
	require.NoError(service.Redis.Del(generateConditionsDrawKey()).Err())

	startTime := util.TimeNow().Unix() + 10000
	endTime := startTime + 10
	updateTime := func() {
		err := service.DB.Table(mevent.TableName()).Where("id = ?", eventIDLearningChallenges).
			Updates(map[string]interface{}{"start_time": startTime, "end_time": endTime}).Error
		require.NoError(err)
	}
	updateTime()

	c := handler.NewTestContext(http.MethodGet, "/?event_id=133", true, nil)
	_, err := actionLearningChallengesDraw(c)
	assert.EqualError(err, "活动未开始")

	startTime = 0
	endTime = 100
	updateTime()
	c = handler.NewTestContext(http.MethodPost, "/?event_id=133", true, nil)
	_, err = actionLearningChallengesDraw(c)
	assert.EqualError(err, "活动已结束")

	endTime += util.TimeNow().Unix() + 100
	updateTime()
	c = handler.NewTestContext(http.MethodPost, "/?event_id=133", true, nil)
	_, err = actionLearningChallengesDraw(c)
	assert.EqualError(err, "抽奖次数不足")

	require.NoError(service.Redis.HSet(generateConditionsDrawKey(), 12, 1).Err())
	data, err := actionLearningChallengesDraw(c)
	require.NoError(err)
	assert.NotNil(data)
	tutil.PrintJSON(data)

	_, err = actionLearningChallengesDraw(c)
	assert.EqualError(err, "抽奖次数不足")
}
