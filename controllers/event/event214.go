package event

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/db/eventpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
)

/**
 * @api {get} /x/event/myprizes?event_id=214 214 星座许愿兑换记录
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "point": 100 // 剩余积分
 *         },
 *         "prizes": [ // 用户已兑换的商品，最新兑换的排在前边
 *           {
 *             "user_id": 12,
 *             "prize_id": 8549,
 *             "create_time": 1611840257,
 *             "name": "头像框",
 *             "image_url": "https://www.uat.missevan.com/files/2021-01-27/574270c393f3022bddda21dec27f1a8a.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 */

/**
 * @api {post} /x/event/exchange?event_id=214 214 星座许愿兑换
 * @apiVersion 0.1.0
 * @apiGroup event
 * @apiParam {Number} goods_id 商品 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0, 位 1 用于兑换
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 兑换成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 兑换商品的时间
 *           "name": "奖品名称",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         },
 *         "draw": {
 *           "point": 100 // 兑换后剩余积分
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要兑换 煦光颂歌 7d 吗？" // 提示信息
 *     }
 *   }
 */
func actionExchange214(c *handler.Context) (handler.ActionResponse, error) {
	var req exchangeParams
	err := c.Bind(&req)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	eventID := eventID214
	var dc eventpoint.DrawConfig
	e, err := mevent.FindSimpleWithExtendedFields(eventID, &dc)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if e == nil {
		return nil, actionerrors.ErrEventNotFound
	}
	nowUnix := dc.TimeNow().Unix()
	if nowUnix < e.DrawStartTime || nowUnix >= e.DrawEndTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "当前不在购买时间内哦~")
	}
	param := eventpoint.NewExchangeParam(e, c.UserID(), &dc)
	g, err := param.Goods(req.GoodsID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUnknownError, "未找到对应商品")
	}

	info, err := param.UserDrawInfo()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if info.Point < g.Price {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "星愿币不足")
	}
	if g.Limit > 0 {
		num, err := param.ExchangeNum(g)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if num >= g.Limit {
			return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "兑换次数已达上限")
		}
	}
	if req.Confirm == 0 {
		return nil, actionerrors.ErrConfirmRequired(
			fmt.Sprintf("确定要消耗 %d 星愿币购买 %s 吗？", g.Price, g.GoodsName()), 1)
	}
	unlock, err := lockEventUserPoint(eventID, param.UserID)
	if err != nil {
		return nil, err
	}
	defer unlock()
	info, prize, err := param.Exchange(g)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if prize == nil {
		return nil, actionerrors.ErrForbidden(handler.CodeInvalidParam, "星愿币不足")
	}
	return &DrawResp{
		Prize: prize,
		Draw:  info,
	}, nil
}
