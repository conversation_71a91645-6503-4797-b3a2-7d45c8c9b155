package event

import (
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/game"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	forAllTimePrizesMaxNum = 1
)

// drawTime 抽奖开始时间结束时间
type drawTime struct {
	StartTime int64 `json:"draw_start_time"`
	EndTime   int64 `json:"draw_end_time"`
}

/**
 * @api {get} /v2/event/myprizes?event_id=131 时空中的绘旅人用户获取自己抽到的奖品和剩余抽奖次数
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 656,           // 奖项 ID
 *             "create_time": 1587477832, // 抽到奖品的时间
 *             "name": "谢谢参与",
 *             "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *           }
 *           ...
 *         ],
 *         "times_left": 0,               // 剩余抽奖次数
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */
func actionForAllTimePrizes(c *handler.Context) (handler.ActionResponse, error) {
	myPrizes := make([]models.EventUserPrize, 0)

	ok, err := game.MGameSubscribe{}.Exists(c.UserID(), gameIDForAllTime)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return drawResult{
			TimesLeft: 0,
			Prizes:    myPrizes,
		}, nil
	}

	err = service.DB.Where("user_id = ? AND event_id = ?", c.UserID(), eventIDForAllTime).Order("id ASC").Find(&myPrizes).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	_, gamePrizeConfigs, err := getGamePrizeConfigs(eventIDForAllTime, 0, 0)
	if err != nil {
		return nil, err
	}
	for i, mine := range myPrizes {
		for _, config := range gamePrizeConfigs {
			if mine.PrizeID == config.PrizeID {
				myPrizes[i].Name = config.Name
				myPrizes[i].ImageURL = config.ImageURL
				break
			}
		}
		if myPrizes[i].Name == "" {
			myPrizes[i].Name = "未知奖品名"
			logger.WithField("prize_id", mine.PrizeID).Error("未知奖品名")
		}
	}

	timesLeft := forAllTimePrizesMaxNum - len(myPrizes)
	if timesLeft <= 0 {
		myPrizes = myPrizes[:forAllTimePrizesMaxNum]
		timesLeft = 0
	}

	return drawResult{
		TimesLeft: timesLeft,
		Prizes:    myPrizes,
	}, nil
}

/**
 * @api {post} /v2/event/draw?event_id=131 时空中的绘旅人点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 抽到奖品的时间
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "活动未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "活动已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */
func actionForAllTimeDraw(c *handler.Context) (handler.ActionResponse, error) {
	var event mevent.Simple
	err := service.DB.Where("id = ?", eventIDForAllTime).First(&event).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrEventNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if event.ExtendedFields == "" {
		return nil, actionerrors.ErrServerInternal(errors.New("extended_fields 为空"), nil)
	}

	var dt drawTime
	err = json.Unmarshal([]byte(event.ExtendedFields), &dt)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	nowUnix := util.TimeNow().Unix()
	if nowUnix < dt.StartTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动未开始")
	}
	if nowUnix > dt.EndTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动已结束")
	}
	lock := keys.LockEventUserPoint2.Format(eventIDForAllTime, c.UserID())
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "")
	}
	defer service.Redis.Del(lock)

	var myPrizesCount int
	err = service.DB.Model(&models.EventUserPrize{}).Where("user_id = ? AND event_id = ?", c.UserID(), eventIDForAllTime).
		Count(&myPrizesCount).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if myPrizesCount >= forAllTimePrizesMaxNum {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足")
	}

	ok, err = game.MGameSubscribe{}.Exists(c.UserID(), gameIDForAllTime)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足")
	}

	thanksIdx, gamePrizeConfigs, err := getGamePrizeConfigs(eventIDForAllTime, 0, 0)
	if err != nil {
		return nil, err
	}
	_, rd, err := generateDistribution(gamePrizeConfigs)
	if err != nil {
		return nil, err
	}

	prizeN := rd.NextInt()
	if gamePrizeConfigs[prizeN].totalNum != -1 {
		count, err := service.Redis.HIncrBy(
			keys.KeyEventPrizes1.Format(eventIDForAllTime),
			strconv.FormatInt(gamePrizeConfigs[prizeN].PrizeID, 10),
			1,
		).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if count > int64(gamePrizeConfigs[prizeN].totalNum) {
			logger.WithFields(logger.Fields{
				"prize_id": gamePrizeConfigs[prizeN].PrizeID,
				"user_id":  c.UserID(),
			}).Warn("奖品已抽完，设置返回谢谢参与")
			// 下标设置为谢谢参与
			prizeN = thanksIdx
		}
	}

	userPrize := models.EventUserPrize{
		UserID:  c.UserID(),
		PrizeID: gamePrizeConfigs[prizeN].PrizeID,
		EventID: eventIDForAllTime,

		Name:     gamePrizeConfigs[prizeN].Name,
		ImageURL: gamePrizeConfigs[prizeN].ImageURL,
	}

	err = service.DB.Create(&userPrize).Error
	if err != nil {
		if prizeN != thanksIdx {
			logger.WithFields(logger.Fields{
				"user_id":  c.UserID(),
				"prize_id": userPrize.PrizeID,
			}).Error("抽奖失败，奖品剩余数量可能需要调整")
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return handler.M{"prize": userPrize}, nil
}
