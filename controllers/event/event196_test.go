package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionEvent196Exchange(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/?event_id=196", true, nil)
	_, err := actionEvent196Exchange(c)
	assert.Equal(actionerrors.ErrParams, err)
}
