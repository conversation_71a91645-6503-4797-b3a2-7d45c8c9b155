package event

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

/**
 * @api {post} /v2/event/draw?event_id=175&pool_id=:pool_id 直播年度盛典新点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {number=1,2} pool_id 奖池类型，1：追光祈愿，2：璀璨祈愿
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 抽到奖品的时间
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         },
 *         "left_point": 10
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */

/**
 * @api {post} /v2/event/myprizes?event_id=175&pool_id=:pool_id 直播年度盛典用户获取自己某个奖池抽到的奖品和剩余抽奖次数
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {number=1,2} pool_id 奖池类型，1：追光祈愿，2：璀璨祈愿
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 0,
 *           "purchase_times": 4,
 *           "support_free": false,
 *           "point": 40
 *         },
 *         "prizes": [
 *           {
 *             "user_id": 100,
 *             "prize_id": 18414,
 *             "create_time": 1637033398,
 *             "name": "小鱼干×20",
 *             "image_url": "https://static-test.missevan.com/prize/202106/25/bcfa9e6184c18ce1c4cc3c82ef0f0b6e162450.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "抽奖已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */

func actionAnnualCeremonyDraw(c *handler.Context) (handler.ActionResponse, error) {
	userID := c.UserID()
	poolID, _ := c.GetParamInt64("pool_id")
	if poolID != 1 && poolID != 2 {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "奖池错误")
	}
	userPrize, leftPoint, err := draw(eventIDAnnualLive, userID, poolID)
	if err != nil {
		return nil, err
	}

	handleAppearancePrize(c, userPrize)
	return handler.M{"prize": userPrize, "left_point": leftPoint}, nil
}

var appearanceGifts = []*appearancePrize{
	newAppearancePrize(`心动捕手头像框 × ([1-9]\d*)d`, 40074),
	newAppearancePrize(`小小园丁头像框 × ([1-9]\d*)d`, 40075),
	newAppearancePrize(`宠爱之名头像框 × ([1-9]\d*)d`, 40076),
	newAppearancePrize(`煦光颂歌头像框 × ([1-9]\d*)d`, 40077),
	newAppearancePrize(`永夜回音头像框 × ([1-9]\d*)d`, 40078),
	newAppearancePrize(`化蝶仙头像框 × ([1-9]\d*)d`, 40079),
	newAppearancePrize(`晨曦百合气泡框 × ([1-9]\d*)d`, 10014),
	newAppearancePrize(`夜雾玫瑰气泡框 × ([1-9]\d*)d`, 10015),
	newAppearancePrize(`星河入梦座驾 × ([1-9]\d*)d`, 20016),
	newAppearancePrize(`Super Love 头像框 × ([1-9]\d*)d`, 40080),
	newAppearancePrize(`化蝶仙气泡框 × ([1-9]\d*)d`, 10016),
	newAppearancePrize(`光焰华冠头像框 × ([1-9]\d*)d`, 40081),
	newAppearancePrize(`夜华荣冕头像框 × ([1-9]\d*)d`, 40082),
	newAppearancePrize(`流心璀璨座驾 × ([1-9]\d*)d`, 20017),
	newAppearancePrize(`朝以同歌座驾 × ([1-9]\d*)d`, 20018),
	newAppearancePrize(`暮与同酒座驾 × ([1-9]\d*)d`, 20019),
}
