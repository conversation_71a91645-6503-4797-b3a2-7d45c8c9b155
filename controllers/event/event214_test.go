package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/models/db/eventpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionExchange214(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dc := eventpoint.DrawConfig{
		DrawPools: []*eventpoint.PoolConfig{
			{
				PoolType: eventpoint.PoolTypeExchange,
				ShopGoods: []*eventpoint.Goods{
					{
						ID:    1,
						Price: 0,
					},
				},
			},
		},
	}
	require.NoError(service.DB.Table(mevent.TableName()).Where("id = ?", eventID214).
		Update("extended_fields", tutil.SprintJSON(dc)).Error)
	prizeKey := keys.LocalKeyEventAnPrize1.Format(eventID214)
	service.Cache5Min.SetDefault(prizeKey, []anprize.AnPrize{
		{
			NameInfo: anprize.PrizeNameInfo{
				GoodsID: 1,
				Name:    "test",
			},
		},
	})

	c := handler.NewTestContext(http.MethodPost, "?event_id=214", true,
		handler.M{"goods_id": 1})
	_, err := actionExchange214(c)
	assert.EqualError(err, "确定要消耗 0 星愿币购买 test 吗？")

	c = handler.NewTestContext(http.MethodPost, "?event_id=214", true,
		handler.M{"goods_id": 1, "confirm": 1})
	_, err = actionExchange214(c)
	assert.NoError(err)
}
