package event

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

/**
 * @api {get} /v2/event/myprizes?event_id=196 风月宴知音活动用户获取自己的兑换记录
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "point": 5500 // 通宝数量
 *         },
 *         "prizes": [
 *           {
 *             "id": 2218,
 *             "user_id": 221,
 *             "prize_id": 20621,
 *             "create_time": 1644834514, // 兑换的时间，秒级时间戳
 *             "name": "欧皇称号 × 7d",
 *             "image_url": "https://static-test.maoercdn.com/prize/202106/25/46c478dbcd6bd24a8b86437b6b659da8162450.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */

/**
 * @api {post} /v2/event/exchange?event_id=196 风月宴知音活动用户兑换奖品
 * @apiVersion 0.1.0
 * @apiGroup event
 * @apiParam {number{1-9}} goods_id 商品 ID，
 *                                  1 对应 1k 热度卡 × 1，2 对应鲤跃福归称号 × 7d，3 对应鲤跃福归头像框 × 7d，
 *                                  4 对应鲤跃福归气泡框 × 7d，5 对应风裁花使座驾 × 7d，6 对应鲤跃福归座驾 × 7d，
 *                                  7 对应天鹅王座 × 1，8 对应凤鸣天下 × 1，9 对应神浴光礼 × 1
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0, 位 1 用于兑换
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 兑换成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "id": 5683,
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 兑换的时间，为秒级时间戳
 *           "name": "欧皇称号 × 7d",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 确认消息内容:
 *     {
 *       "code": 100010020,
 *       "info": {
 *         "confirm": 1, // 再次请求需要传递的参数值
 *         "msg": "确认要消耗 100 通宝兑换 鲤跃福归称号 × 7d 吗？" // 提示信息
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 */

func actionEvent196Exchange(c *handler.Context) (handler.ActionResponse, error) {
	return exchangeGoods(eventID196, c)
}
