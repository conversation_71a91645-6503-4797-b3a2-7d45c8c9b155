package event

import (
	"regexp"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	conditionsDrawCount cache.KeyFormat = "meet:conditions:user:%s"
)

const (
	drawAllow = iota + 1
	drawDone
)

var (
	pointPrizeRegex = regexp.MustCompile(`小鱼干\s([1-9]\d*)`)
)

/**
 * @api {get} /v2/event/myprizes?event_id=133 国庆学习挑战活动用户获取自己抽到的奖品和剩余抽奖次数
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 656,           // 奖项 ID
 *             "create_time": 1587477832, // 抽到奖品的时间
 *             "name": "谢谢参与",
 *             "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *           }
 *           ...
 *         ],
 *         "times_left": 0,               // 剩余抽奖次数
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */
func actionLearningChallengesPrizes(c *handler.Context) (handler.ActionResponse, error) {
	userPrize := models.EventUserPrize{
		UserID:  c.UserID(),
		EventID: eventIDLearningChallenges,
	}
	myPrizes, _, err := userPrize.Find(nil, 1, util.DefaultPageSize)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	_, prizeConfigs, err := getGamePrizeConfigs(eventIDLearningChallenges, 0, 0)
	if err != nil {
		return nil, err
	}
	for i, mine := range myPrizes {
		for _, config := range prizeConfigs {
			if mine.PrizeID == config.PrizeID {
				myPrizes[i].Name = config.Name
				myPrizes[i].ImageURL = config.ImageURL
				break
			}
		}
		if myPrizes[i].Name == "" {
			myPrizes[i].Name = "未知奖品名"
			logger.WithField("prize_id", mine.PrizeID).Error("未知奖品名")
		}
	}

	var timesLeft int
	if ok := learningChallengesDrawCount(c.UserID()); ok {
		timesLeft = 1
	}
	return drawResult{
		TimesLeft: timesLeft,
		Prizes:    myPrizes,
	}, nil
}

/**
 * @api {post} /v2/event/draw?event_id=133 国庆学习挑战活动点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 抽到奖品的时间
 *           "name": "谢谢参与",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "活动未开始"
 * @apiError (403) {Number} code CodeLoginRequired = 100010007
 * @apiError (403) {string} info "活动已结束"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足"
 */
func actionLearningChallengesDraw(c *handler.Context) (handler.ActionResponse, error) {
	startTime, endTime, err := mevent.EventTime(eventIDLearningChallenges)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if startTime == nil || endTime == nil {
		return nil, actionerrors.ErrEventNotFound
	}
	nowUnix := util.TimeNow().Unix()
	if nowUnix < *startTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动未开始")
	}
	if nowUnix > *endTime {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动已结束")
	}
	lock := keys.LockEventUserPoint2.Format(eventIDLearningChallenges, c.UserID())
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "")
	}
	defer service.Redis.Del(lock)
	if ok := learningChallengesDrawCount(c.UserID()); !ok {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足")
	}

	thanksIdx, prizeConfigs, err := getGamePrizeConfigs(eventIDLearningChallenges, 0, 0)
	if err != nil {
		return nil, err
	}
	_, rd, err := generateDistribution(prizeConfigs)
	if err != nil {
		return nil, err
	}

	prizeN := rd.NextInt()
	if prizeConfigs[prizeN].totalNum != -1 {
		count, err := service.Redis.HIncrBy(
			keys.KeyEventPrizes1.Format(eventIDLearningChallenges),
			strconv.FormatInt(prizeConfigs[prizeN].PrizeID, 10),
			1,
		).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if count > int64(prizeConfigs[prizeN].totalNum) {
			logger.WithFields(logger.Fields{
				"prize_id": prizeConfigs[prizeN].PrizeID,
				"user_id":  c.UserID(),
			}).Warn("奖品已抽完，设置返回谢谢参与")
			// 下标设置为谢谢参与
			prizeN = thanksIdx
		}
	}

	userPrize := models.EventUserPrize{
		UserID:  c.UserID(),
		PrizeID: prizeConfigs[prizeN].PrizeID,
		EventID: eventIDLearningChallenges,

		Name:     prizeConfigs[prizeN].Name,
		ImageURL: prizeConfigs[prizeN].ImageURL,
	}

	if err := userPrize.Create(); err != nil {
		if prizeN != thanksIdx {
			logger.WithFields(logger.Fields{
				"user_id":  c.UserID(),
				"prize_id": userPrize.PrizeID,
			}).Error("抽奖失败，奖品剩余数量可能需要调整")
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	userID := strconv.FormatInt(c.UserID(), 10)
	// 抽奖次数标记成已抽奖
	err = service.Redis.HSet(generateConditionsDrawKey(), userID, drawDone).Err()
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  c.UserID(),
			"prize_id": userPrize.PrizeID,
		}).Error("抽奖次数设置失败，奖品剩余数量可能需要调整")
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	if point, ok := models.IsPrize(userPrize.Name, pointPrizeRegex); ok {
		if err := user.AddUserPoint(userPrize.UserID, point); err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return handler.M{"prize": userPrize}, nil
}

// learningChallengesDrawCount 判断是否有抽奖次数
func learningChallengesDrawCount(userID int64) bool {
	value, err := service.Redis.HGet(generateConditionsDrawKey(), strconv.FormatInt(userID, 10)).Int()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return false
	}
	return value == drawAllow
}

// generateConditionsDrawKey 生成获取抽奖次数的缓存 KEY
func generateConditionsDrawKey() string {
	date := util.TimeNow().Format("20060102")
	return conditionsDrawCount.Format(date)
}
