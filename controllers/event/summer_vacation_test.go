package event

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSummerVacationDraw(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/?event_id=161", true, nil)
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return &user.User{
			IUser: user.IUser{
				ID: 12998,
			},
		}, nil
	})
	c.C.Set("user", userFunc)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(151)
	userID := c.UserID()
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(1), Member: strconv.FormatInt(userID, 10)}).Err())
	e, err := mevent.FindSimple(151)
	require.NoError(err)
	defer util.SetTimeNow(nil)
	util.SetTimeNow(func() time.Time {
		return time.Unix(e.DrawEndTime, 0).Add(-time.Minute)
	})
	data, err := actionNewYearGreetingsDraw(c)
	require.NoError(err)
	tutil.PrintJSON(data)
	prize, ok := data.(handler.M)
	require.True(ok)
	up, ok := prize["prize"]
	require.True(ok)
	_, ok = up.(*models.EventUserPrize)
	require.True(ok)
	util.SetTimeNow(nil)
}
