package event

import (
	"regexp"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
)

const (
	// 外观物品类型未知
	appearanceTypeUnknown = iota
	// 外观物品类型为座驾
	appearanceTypeVehicle
	// 外观物品类型为头像框
	appearanceTypeAvatarFrame
	// 外观物品类型为名片框
	appearanceTypeCardFrame
	// 外观物品类型为气泡框
	appearanceTypeMessageBubble
	// 外观物品类型为称号
	appearanceTypeBadge
)

type appearancePrize struct {
	Regexp  *regexp.Regexp
	PrizeID int64
}

func newAppearancePrize(re string, id int64) *appearancePrize {
	return &appearancePrize{
		Regexp:  regexp.MustCompile(re),
		PrizeID: id,
	}
}

// appearanceTypeByName 根据外观的名称获取外观类型
func appearanceTypeByName(name string) int {
	if strings.Contains(name, "头像框") {
		return appearanceTypeAvatarFrame
	}
	if strings.Contains(name, "气泡框") {
		return appearanceTypeMessageBubble
	}
	if strings.Contains(name, "座驾") {
		return appearanceTypeVehicle
	}
	return appearanceTypeUnknown
}

// handleAppearancePrize 处理奖品，把奖品发到用户的外观中心
func handleAppearancePrize(c *handler.Context, prize *models.EventUserPrize) {
	prizeName := prize.Name
	userID := prize.UserID
	for _, v := range appearanceGifts {
		if n, ok := models.IsPrize(prizeName, v.Regexp); ok {
			appearanceType := appearanceTypeByName(prizeName)
			if appearanceType == appearanceTypeUnknown {
				logger.Errorf("unknown appearance type or prize: %s", prizeName)
				// PASS
				break
			}
			sendAppearance(c, appearanceType, int64(n*24*int(time.Hour.Seconds())), v.PrizeID, userID)
			break
		}
	}
}

func sendAppearance(c *handler.Context, appearanceType int, duration, appearanceID, userID int64) {
	reqData := map[string]interface{}{
		"type":          appearanceType,
		"duration":      duration,
		"appearance_id": appearanceID,
		"user_id":       userID,
	}
	var res interface{}
	err := c.MRPC("live://user/appearance/add", reqData, &res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func sendGiftCustom(c *handler.Context, day, giftID, userID int64) {
	reqData := map[string]interface{}{
		"day":     day,
		"gift_id": giftID,
		"user_id": userID,
	}
	var res interface{}
	err := c.MRPC("live://user/gift/custom/add", reqData, &res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
