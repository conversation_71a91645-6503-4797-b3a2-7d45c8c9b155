package event

import (
	"encoding/json"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	cancel := mrpc.SetMock("live://user/appearance/add", func(i interface{}) (interface{}, error) {
		logger.Debug(tutil.SprintJSON(i))
		return "success", nil
	})
	defer cancel()
	cancel = mrpc.SetMock("live://user/backpack/add", func(i interface{}) (interface{}, error) {
		logger.Debug(tutil.SprintJSON(i))
		return "success", nil
	})
	defer cancel()
	cancel = mrpc.SetMock("live://user/gift/custom/add", func(i interface{}) (interface{}, error) {
		logger.Debug(tutil.SprintJSON(i))
		return "success", nil
	})
	defer cancel()

	m.Run()
	err := service.Redis.Del(keys.KeyEventPrizes1.Format(eventIDShjh)).Err()
	if err != nil {
		logger.Error(err)
	}
}

func TestGetMyPrizes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)
	eventID := int64(eventIDMaoMaoStar)
	require.NoError(service.DB.Where("user_id = ? AND event_id = ?", userID, eventID).Delete(models.EventUserPrize{}).Error)
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	myPrizes, err := getMyPrizes(eventID, userID, 0, c)
	require.NoError(err)
	assert.Empty(myPrizes.Prizes)

	userPrize := models.EventUserPrize{
		UserID:  userID,
		EventID: eventID,
		PrizeID: 27,
	}
	require.NoError(service.DB.Create(&userPrize).Error)
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	myPrizes, err = getMyPrizes(eventID, userID, 0, c)
	require.NoError(err)
	assert.Len(myPrizes.Prizes, 1)
	assert.Equal(util.Pagination{
		Count:    1,
		MaxPage:  1,
		P:        1,
		PageSize: 20,
	}, myPrizes.Pagination)
}

func TestGetUserPrizeAndDrawInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)
	eventID := int64(eventIDNewYearGreetings)
	require.NoError(service.DB.Where("user_id = ? AND event_id = ?", userID, eventID).Delete(models.EventUserPrize{}).Error)
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	data, err := getUserPrizeAndDrawInfo(eventID, 0, c)
	require.NoError(err)
	assert.Empty(data.Prizes)

	userPrize := models.EventUserPrize{
		UserID:  userID,
		EventID: eventID,
		PrizeID: 7,
	}
	for i := 0; i < 25; i++ {
		u := userPrize
		require.NoError(service.DB.Create(&u).Error)
	}
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	data, err = getUserPrizeAndDrawInfo(eventID, 0, c)
	require.NoError(err)
	require.Len(data.Prizes, 20)
	assert.Equal(util.Pagination{
		Count:    25,
		MaxPage:  2,
		P:        1,
		PageSize: 20,
	}, data.Pagination)
	c = handler.NewTestContext(http.MethodPost, "?p=1", true, nil)
	data, err = getUserPrizeAndDrawInfo(eventID, 0, c)
	require.NoError(err)
	require.Len(data.Prizes, 20, "默认的 pagesize 是 20")
	assert.Equal(util.Pagination{
		Count:    25,
		MaxPage:  2,
		P:        1,
		PageSize: 20,
	}, data.Pagination)
	c = handler.NewTestContext(http.MethodPost, "?p=2", true, nil)
	data, err = getUserPrizeAndDrawInfo(eventID, 0, c)
	require.NoError(err)
	require.Len(data.Prizes, 5, "第二页有 5 条数据")
	assert.Equal(util.Pagination{
		Count:    25,
		MaxPage:  2,
		P:        2,
		PageSize: 20,
	}, data.Pagination)

	c = handler.NewTestContext(http.MethodPost, "?p=2&pagesize=6", true, nil)
	data, err = getUserPrizeAndDrawInfo(eventID, 0, c)
	require.NoError(err)
	assert.Len(data.Prizes, 6, "第二页有 6 条数据")
	assert.Equal(util.Pagination{
		Count:    25,
		MaxPage:  5,
		P:        2,
		PageSize: 6,
	}, data.Pagination)
}

func TestPoolDrawPrice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dc := drawConfig{
		DrawPrice: json.RawMessage(`1`),
	}
	p, err := dc.poolDrawPrice(0)
	require.NoError(err)
	assert.Equal(int64(1), p)
	dc = drawConfig{
		DrawPrice: json.RawMessage(`{"1": 10, "2": 20}`),
	}
	_, err = dc.poolDrawPrice(0)
	assert.Error(err)
	p, err = dc.poolDrawPrice(1)
	require.NoError(err)
	assert.Equal(int64(10), p)
}

func TestGetDrawInfo(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)
	eventID := int64(eventIDNewYearGreetings)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	info, err := getDrawInfo(eventID, userID, 0)
	require.NoError(err)
	assert.Equal(int64(0), info.Point)
	assert.Equal(int64(0), info.PurchaseTimes)
	assert.Equal(int64(0), info.FreeTimes)
	assert.False(info.SupportFree)

	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: float64(3), Member: userID}).Err())
	info, err = getDrawInfo(eventID, userID, 0)
	assert.Equal(int64(3), info.Point)
	require.NoError(err)
	assert.Equal(int64(3), info.PurchaseTimes)
	assert.Equal(int64(0), info.FreeTimes)

	eventID = int64(eventIDNewYearGardenParty)
	key = serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	date := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
	require.NoError(service.Redis.SRem(freeDrawKey, userID).Err())
	info, err = getDrawInfo(eventID, userID, 0)
	require.NoError(err)
	assert.Equal(int64(0), info.Point)
	assert.Equal(int64(0), info.PurchaseTimes)
	assert.Equal(int64(1), info.FreeTimes)
	assert.True(info.SupportFree)

	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(300), Member: strconv.FormatInt(userID, 10)}).Err())
	info, err = getDrawInfo(eventID, userID, 0)
	require.NoError(err)
	assert.Equal(int64(300), info.Point)
	assert.Equal(int64(3), info.PurchaseTimes)
	assert.Equal(int64(1), info.FreeTimes)

	eventID = int64(eventIDAnnualLive)
	key = serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: float64(10), Member: userID}).Err())
	info, err = getDrawInfo(eventID, userID, 1)
	require.NoError(err)
	assert.Equal(int64(10), info.Point)
	assert.Equal(int64(1), info.PurchaseTimes)
	assert.Equal(int64(0), info.FreeTimes)
	assert.False(info.SupportFree)

	eventID = eventIDMaoMaoStar
	var dc drawConfig
	_, err = mevent.FindSimpleWithExtendedFields(eventID, &dc)
	require.NoError(err)
	date = dc.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
	require.NoError(service.Redis.SRem(freeDrawUsersKey, userID).Err())
	pointDrawUsersKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZAdd(pointDrawUsersKey, &redis.Z{Score: float64(100), Member: userID}).Err())
	// 测试免费奖池
	info, err = getDrawInfo(eventID, userID, 1)
	require.NoError(err)
	assert.Equal(int64(100), info.Point)
	assert.Equal(int64(0), info.PurchaseTimes)
	assert.Equal(int64(1), info.FreeTimes)
	assert.Equal(int64(0), info.Cost)
	assert.True(info.SupportFree)

	// 测试积分奖池
	info, err = getDrawInfo(eventID, userID, 2)
	require.NoError(err)
	assert.Equal(int64(100), info.Point)
	assert.Equal(int64(1), info.PurchaseTimes)
	assert.Equal(int64(1), info.FreeTimes)
	assert.Equal(int64(100), info.Cost)
	assert.True(info.SupportFree)
}

func TestDraw(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)
	eventID := int64(eventIDNewYearGreetings)
	require.NoError(service.DB.Where("user_id = ? and event_id = ?", userID, eventID).Delete(&models.EventUserPrize{}).Error)
	require.NoError(service.Redis.Del(keys.KeyEventPrizes1.Format(eventID)).Err())
	startTime := util.TimeNow().Unix() + 10000
	endTime := startTime + 10
	updateTime := func() {
		err := service.DB.Table(mevent.TableName()).Where("id = ?", eventID).
			Updates(map[string]interface{}{"draw_start_time": startTime, "draw_end_time": endTime}).Error
		require.NoError(err)
	}
	updateTime()
	_, _, err := draw(eventID, userID, 0)
	assert.EqualError(err, "抽奖未开始")
	startTime = 0
	endTime = 100
	updateTime()
	_, _, err = draw(eventID, userID, 0)
	assert.EqualError(err, "抽奖已结束")
	endTime += util.TimeNow().Unix() + 100
	updateTime()
	lock := keys.LockEventUserPoint2.Format(eventID, userID)
	service.Redis.Del(lock)
	_, err = service.Redis.SetNX(lock, "1", time.Minute).Result()
	require.NoError(err)
	_, _, err = draw(eventID, userID, 0)
	assert.Equal(actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试"), err)
	service.Redis.Del(lock)

	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(2), Member: strconv.FormatInt(userID, 10)}).Err())
	data, point, err := draw(eventID, userID, 0)
	require.NoError(err)
	assert.NotNil(data)
	assert.Equal(int64(1), point, "使用了 1 积分抽奖，还剩 1 积分")
	tutil.PrintJSON(data)
	data, point, err = draw(eventID, userID, 0)
	require.NoError(err)
	assert.NotNil(data)
	assert.Zero(point, "使用了 1 积分抽奖，还剩 0 积分")
	tutil.PrintJSON(data)
	_, _, err = draw(eventID, userID, 0)
	assert.EqualError(err, "抽奖次数不足")

	eventID = eventIDNewYearGardenParty
	date := util.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
	_, err = service.Redis.SRem(freeDrawUsersKey, userID).Result()
	require.NoError(err)
	key = serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(200), Member: strconv.FormatInt(userID, 10)}).Err())
	updateTime()
	data, point, err = draw(eventID, userID, 0)
	require.NoError(err)
	assert.Equal(int64(200), point, "使用了免费抽奖机会不扣除积分")
	tutil.PrintJSON(data)

	eventID = eventIDAnnualLive
	key = serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(200), Member: strconv.FormatInt(userID, 10)}).Err())
	updateTime()
	data, point, err = draw(eventID, userID, 1)
	require.NoError(err)
	assert.Equal(int64(190), point, "在奖池 1 抽奖，使用掉 10 积分，剩 190 积分")
	tutil.PrintJSON(data)
	data, point, err = draw(eventID, userID, 2)
	require.NoError(err)
	assert.Equal(int64(170), point, "在奖池 2 抽奖，使用掉 20 积分，剩 170 积分")
	tutil.PrintJSON(data)

	eventID = eventIDMaoMaoStar
	var dc drawConfig
	_, err = mevent.FindSimpleWithExtendedFields(eventIDMaoMaoStar, &dc)
	require.NoError(err)
	date = dc.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawUsersKey = keys.KeyDailyFreeDrawUsers2.Format(eventIDMaoMaoStar, date)
	require.NoError(service.Redis.SRem(freeDrawUsersKey, userID).Err())
	pointDrawUsersKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZAdd(pointDrawUsersKey, &redis.Z{Score: float64(0), Member: strconv.FormatInt(userID, 10)}).Err())
	_, point, err = draw(eventID, userID, 1)
	require.NoError(err)
	assert.Equal(int64(0), point, "在奖池 1 抽奖，使用掉 0 积分，剩 0 积分")
	require.NoError(service.Redis.ZAdd(pointDrawUsersKey, &redis.Z{Score: float64(100), Member: strconv.FormatInt(userID, 10)}).Err())
	_, point, err = draw(eventID, userID, 2)
	require.NoError(err)
	assert.Equal(int64(0), point, "在奖池 2 抽奖，使用掉 100 积分，剩 0 积分")
}

func TestTimeNow(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	dc := drawConfig{TimeOffset: 0}
	assert.Equal(now, dc.TimeNow())
	dc = drawConfig{TimeOffset: 60}
	assert.Equal(now.Add(time.Minute), dc.TimeNow())
}

func TestAddUserPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)
	err := addUserPoint(userID, 10)
	require.Nil(err)
	var point int64
	err = service.DB.Table(user.MowangskUser{}.TableName()).
		Select("point").Where("id = ?", userID).Row().Scan(&point)
	require.NoError(err)
	previousPoint := point
	err = addUserPoint(userID, 10)
	require.Nil(err)
	err = service.DB.Table(user.MowangskUser{}.TableName()).
		Select("point").Where("id = ?", userID).Row().Scan(&point)
	require.NoError(err)
	assert.Equal(previousPoint+10, point)
}

func TestGenerateDistribution(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	pc := []GamePrizeConfig{
		{
			weight: 1,
		},
		{
			weight: 2,
		},
	}
	w, d, err := generateDistribution(pc)
	require.NoError(err)
	assert.NotNil(d)
	assert.Equal([]int{1, 2}, w)
}

func TestGetGamePrizeConfigs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventID := int64(eventIDAnnualLive)
	key := keyEventPoolPrizeConfigs3.Format(eventID, 1, 0)
	cachePrizes.Delete(key)
	idx, prizes, err := getGamePrizeConfigs(eventID, 1, 0)
	require.NoError(err)
	assert.Len(prizes, 4)
	assert.Greater(idx, -1)
	obj, ok := cachePrizes.Get(key)
	require.True(ok)
	assert.Equal(prizes, obj)

	idx, prizes, err = getGamePrizeConfigs(eventID, 1, 0)
	require.NoError(err)
	assert.Len(prizes, 4)
	assert.Greater(idx, -1)
	_, prizes1, _ := getGamePrizeConfigs(eventID, 1, 0)
	assert.Equal(prizes, prizes1)

	idx, prizes, err = getGamePrizeConfigs(eventID, 2, 0)
	require.NoError(err)
	assert.Len(prizes, 4)
	assert.Greater(idx, -1)
	_, prizes1, _ = getGamePrizeConfigs(eventID, 2, 0)
	assert.Equal(prizes, prizes1)
}

func TestGetGamePrizeConfigsFromDB(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	prizes, err := getGamePrizeConfigsFromDB(eventIDAnnualLive, 0, 0)
	require.NoError(err)
	assert.Len(prizes, 8)
	prizes, err = getGamePrizeConfigsFromDB(eventIDAnnualLive, 1, 0)
	require.NoError(err)
	tutil.PrintJSON(prizes)
	assert.Len(prizes, 4)
	prizes, err = getGamePrizeConfigsFromDB(eventIDAnnualLive, 2, 0)
	require.NoError(err)
	assert.Len(prizes, 4)
	tutil.PrintJSON(prizes)
	prizes, err = getGamePrizeConfigsFromDB(eventID196, 0, 0)
	require.NoError(err)
	assert.Len(prizes, 1)
}

func TestBuildMyPrizesResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/myprizes", true, nil)
	r, err := buildMyPrizesResp(eventID196, eventTypeDraw, c)
	require.NoError(err)
	assert.NotNil(r)

	r, err = buildMyPrizesResp(eventID196, eventTypeExchange, c)
	require.NoError(err)
	assert.NotNil(r)

	ok := false
	cancel := mrpc.SetMock(userapi.URIActivityLotteryRecord, func(input interface{}) (output interface{}, err error) {
		ok = true
		return true, nil
	})
	defer cancel()

	_, err = buildMyPrizesResp(eventID196, eventTypeSubscribe, c)
	require.NoError(err)
	assert.True(ok)
}
