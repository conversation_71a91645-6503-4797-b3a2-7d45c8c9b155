package event

import (
	"fmt"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	maxUserPrizesNum = 1
	// shjhAppID        = 3
)

/*
 * @api {get} /v2/event/allprizes?event_id=110 所有奖品和剩余数量
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [ // 按奖励档位从高到低排序
 *         {
 *           "prize_id": 650,
 *           "name": "Nintendo Switch Lite",
 *           "image_url": "https://static.missevan.com/standalone/event/110/1.c43b5372.png",
 *           "count": 1
 *         },
 *         {
 *           "prize_id": 651,
 *           "name": "盒蛋",
 *           "image_url": "https://static.missevan.com/standalone/event/110/2.8cb8bf2c.png",
 *           "count": 6
 *         },
 *         {
 *           "prize_id": 652,
 *           "name": "随机肉螈背包",
 *           "image_url": "https://static.missevan.com/standalone/event/110/3.ae29620f.png",
 *           "count": 7
 *         },
 *         {
 *           "prize_id": 653,
 *           "name": "立牌",
 *           "image_url": "https://static.missevan.com/standalone/event/110/4.0cf09877.png",
 *           "count": 20
 *         },
 *         {
 *           "prize_id": 654,
 *           "name": "肉螈小挂件",
 *           "image_url": "https://static.missevan.com/standalone/event/110/5.eba54061.png",
 *           "count": 30
 *         },
 *         {
 *           "prize_id": 655,
 *           "name": "平安符",
 *           "image_url": "https://static.missevan.com/standalone/event/110/6.20ed1405.png",
 *           "count": 50
 *         },
 *         {
 *           "prize_id": 656,
 *           "name": "游戏兑换码",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png",
 *           "count": -1, // -1 代表不限量
 *         }
 *       ]
 *     }
 */
func actionShjhAllPrizes(c *handler.Context) (handler.ActionResponse, error) {
	count, err := service.Redis.HGetAll(keys.KeyEventPrizes1.Format(eventIDShjh)).Result()
	if err != nil {
		return nil, err
	}
	_, gamePrizeConfigs, err := getGamePrizeConfigs(eventIDShjh, 0, 0)
	if err != nil {
		return nil, err
	}
	for i, v := range gamePrizeConfigs {
		prizeID := strconv.FormatInt(v.PrizeID, 10)
		if c := count[prizeID]; c != "" {
			if n, _ := strconv.Atoi(c); n < v.totalNum {
				gamePrizeConfigs[i].Count = v.totalNum - n
			} else {
				gamePrizeConfigs[i].Count = 0
			}
		} else {
			gamePrizeConfigs[i].Count = v.totalNum
		}
	}
	return gamePrizeConfigs, nil
}

type drawResult struct {
	TimesLeft int                     `json:"times_left"`
	Prizes    []models.EventUserPrize `json:"prizes"`
}

/*
 * @api {get} /v2/event/myprizes?event_id=110 用户获取自己抽到的奖品和剩余抽奖次数
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prizes": [
 *           {
 *             "user_id": 12,
 *             "prize_id": 656,           // 奖项 ID
 *             "create_time": 1587477832, // 抽到奖品的时间
 *             "name": "游戏兑换码",
 *             "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *           }
 *           ...
 *         ],
 *         "times_left": 0,               // 剩余抽奖次数
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */
func actionShjhMyPrizes(c *handler.Context) (handler.ActionResponse, error) {
	// 判断 user_id 用户是否创建游戏角色
	ok, err := hasGameRoles(c)
	if err != nil {
		return nil, err
	}
	if !ok {
		return drawResult{}, nil
	}

	myprizes := []models.EventUserPrize{}
	err = service.DB.Where("user_id = ? AND event_id = ?", c.UserID(), eventIDShjh).Order("id ASC").Find(&myprizes).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	_, gamePrizeConfigs, err := getGamePrizeConfigs(eventIDShjh, 0, 0)
	if err != nil {
		return nil, err
	}
	for i, mine := range myprizes {
		for _, config := range gamePrizeConfigs {
			if mine.PrizeID == config.PrizeID {
				myprizes[i].Name = config.Name
				myprizes[i].ImageURL = config.ImageURL
				break
			}
		}
		if myprizes[i].Name == "" {
			myprizes[i].Name = "未知奖品名"
			logger.WithField("prize_id", mine.PrizeID).Error("未知奖品名")
		}
	}

	timesLeft := maxUserPrizesNum - len(myprizes)
	if timesLeft <= 0 {
		myprizes = myprizes[:maxUserPrizesNum]
		timesLeft = 0
	}

	return drawResult{
		TimesLeft: timesLeft,
		Prizes:    myprizes,
	}, nil
}

/*
 * @api {post} /v2/event/draw?event_id=110 点击抽奖的接口
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 抽奖成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 抽到奖品的时间
 *           "name": "游戏兑换码",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 * @apiError (404) {Number} code CodeLoginRequired = 201010002
 * @apiError (404) {string} info "抽奖次数不足！"
 */
func actionShjhDraw(c *handler.Context) (handler.ActionResponse, error) {
	_, endTime, err := mevent.EventTime(eventIDShjh)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if endTime == nil {
		return nil, actionerrors.ErrEventNotFound
	}
	if util.TimeNow().After(time.Unix(*endTime, 0)) {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "活动已结束")
	}

	lock := keys.LockEventUserPoint2.Format(eventIDShjh, c.UserID())
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "")
	}
	defer service.Redis.Del(lock)

	var myprizes []models.EventUserPrize
	err = service.DB.Where("user_id = ? AND event_id = ?", c.UserID(), eventIDShjh).Find(&myprizes).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(myprizes) >= maxUserPrizesNum {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足！")
	}

	// 判断 user_id 用户是否创建游戏角色
	ok, err = hasGameRoles(c)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, actionerrors.ErrNotFound(handler.CodeInvalidParam, "抽奖次数不足！")
	}

	prizeGameCodeIdx, gamePrizeConfigs, err := getGamePrizeConfigs(eventIDShjh, 0, 0)
	if err != nil {
		return nil, err
	}
	_, rd, err := generateDistribution(gamePrizeConfigs)
	if err != nil {
		return nil, err
	}

	prizeN := rd.NextInt()

	if gamePrizeConfigs[prizeN].totalNum != -1 {
		count, err := service.Redis.HIncrBy(
			keys.KeyEventPrizes1.Format(eventIDShjh),
			strconv.FormatInt(gamePrizeConfigs[prizeN].PrizeID, 10),
			1,
		).Result()
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if count > int64(gamePrizeConfigs[prizeN].totalNum) {
			logger.WithFields(logger.Fields{
				"prize_id": gamePrizeConfigs[prizeN].PrizeID,
				"user_id":  c.UserID(),
			}).Warn("奖品抽完了，给兑换码")
			prizeN = prizeGameCodeIdx
		}
	}

	var gameCode string
	if prizeN == prizeGameCodeIdx {
		gameCode, err = service.Redis.RPop(serviceredis.KeyEventGameCode1.Format(eventIDShjh)).Result()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if gameCode == "" {
			logger.WithField("user_id", c.UserID()).Error("游戏兑换码抽完了")
			return handler.M{"prize": models.EventUserPrize{Name: "谢谢参与"}}, nil
		}
		userID := c.UserID()
		util.Go(func() {
			msg := pushservice.SystemMsg{
				UserID: userID,
				Title:  "山海镜花首发抽奖中奖通知",
				Content: fmt.Sprintf(`恭喜龙子大人抽中山海镜花（猫耳FM安卓渠道）游戏礼包码一份：<a href="copy:%s">%s</a>。龙子大人可前往游戏主页 > 活动 > 兑换中心使用本礼包码。`+
					"注意：该礼包码仅限猫耳FM渠道使用，使用期限为：2020.07.31 23:59 之前。山海之旅，幸有诸君相伴。", gameCode, gameCode),
			}
			err := service.PushService.SendSystemMsg([]pushservice.SystemMsg{msg})
			if err != nil {
				logger.Error(err)
				// PASS
			}
		})
	}
	userPrize := models.EventUserPrize{
		UserID:  c.UserID(),
		PrizeID: gamePrizeConfigs[prizeN].PrizeID,
		EventID: eventIDShjh,

		Name:     gamePrizeConfigs[prizeN].Name,
		ImageURL: gamePrizeConfigs[prizeN].ImageURL,
	}
	if gameCode != "" {
		userPrize.GameCode = &gameCode
	}
	err = service.DB.Create(&userPrize).Error
	if err != nil {
		if prizeN != prizeGameCodeIdx {
			logger.WithFields(logger.Fields{
				"user_id":  c.UserID(),
				"prize_id": userPrize.PrizeID,
			}).Error("抽奖失败，奖品剩余数量可能需要调整")
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return handler.M{"prize": userPrize}, nil
}

var hasGameRoles = rpcGameRoles

func rpcGameRoles(c *handler.Context) (bool, error) {
	var r []map[string]interface{}
	// 活动已结束暂时不需要访问游戏 SDK 服务器的接口了
	// err := service.MRPC.Call(
	// 	"game://user/roles", c.ClientIP(),
	// 	map[string]interface{}{
	// 		"user_id": c.UserID(),
	// 		"app_id":  shjhAppID,
	// 	}, &r,
	// )
	// if err != nil {
	// 	return false, err
	// }
	return len(r) > 0, nil
}
