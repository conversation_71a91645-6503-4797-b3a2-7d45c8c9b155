package event

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

/**
 * @api {get} /v2/event/myprizes?event_id=191 春日集结活动用户获取自己的兑换记录
 * @apiVersion 0.1.0
 * @apiGroup event
 *
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "draw": {
 *           "free_times": 0,
 *           "purchase_times": 5, // 守护币数量
 *           "support_free": false,
 *           "point": 5500 // PK 值
 *         },
 *         "prizes": [
 *           {
 *             "id": 2218,
 *             "user_id": 221,
 *             "prize_id": 20621,
 *             "create_time": 1644834514,
 *             "name": "小闪电 × 10",
 *             "image_url": "https://static-test.maoercdn.com/prize/202106/25/46c478dbcd6bd24a8b86437b6b659da8162450.png"
 *           },
 *           {
 *             "id": 2229,
 *             "user_id": 221,
 *             "prize_id": 20621,
 *             "create_time": 1644834513,
 *             "name": "小闪电 × 10",
 *             "image_url": "https://static-test.maoercdn.com/prize/202106/25/46c478dbcd6bd24a8b86437b6b659da8162450.png"
 *           }
 *         ],
 *         "pagination": {
 *           "count": 23,
 *           "maxpage": 6,
 *           "p": 1,
 *           "pagesize": 4
 *         }
 *       }
 *     }
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 * @apiError (403) {string} info "请先登录"
 */

/**
 * @api {post} /v2/event/exchange?event_id=191 春日集结活动用户兑换奖品
 * @apiVersion 0.1.0
 * @apiGroup event
 * @apiParam {number{1-8}} goods_id 商品 ID，
 *                                  1 对应小闪电 × 10，2 对应小闪电 × 50，3 对应小闪电 × 100，4 对应守护星称号 × 7d，
 *                                  5 对应告守护星头像框 × 7d，6 对应告守护星气泡框 × 7d，7 对应【洒落银河】赠送资格 × 15d，8 对应【樱落 SP】赠送资格 × 15d
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0, 位 1 用于兑换
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 *
 * @apiSuccessExample {json} 兑换成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "prize": {
 *           "id": 5683,
 *           "user_id": 12,
 *           "prize_id": 656,           // 奖项 ID
 *           "create_time": 1587477832, // 兑换商品的时间
 *           "name": "小闪电 × 10",
 *           "image_url": "https://static.missevan.com/standalone/event/110/7.6805be47.png"
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} 确认消息内容:
 *     {
 *       "code": 100010020,
 *       "info": {
 *         "confirm": 1, // 再次请求需要传递的参数值
 *         "msg": "确定要消耗 1 守护币兑换<br>小闪电 × 10 吗？" // 提示信息
 *       }
 *     }
 *
 * @apiError (403) {Number} code CodeLoginRequired = 100010006
 */
func actionSpringPKExchange(c *handler.Context) (handler.ActionResponse, error) {
	return exchangeGoods(eventIDSpringPK, c)
}
