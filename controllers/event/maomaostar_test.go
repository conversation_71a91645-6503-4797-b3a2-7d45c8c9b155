package event

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionMaoMaoStarDraw(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 设置免费抽奖次数
	var dc drawConfig
	_, err := mevent.FindSimpleWithExtendedFields(eventIDMaoMaoStar, &dc)
	require.NoError(err)
	date := dc.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	freeDrawUsersKey := keys.KeyDailyFreeDrawUsers2.Format(eventIDMaoMaoStar, date)
	require.NoError(service.Redis.SRem(freeDrawUsersKey, 12).Err())
	// 设置积分
	pointDrawUsersKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventIDMaoMaoStar)
	require.NoError(service.Redis.ZAdd(pointDrawUsersKey, &redis.Z{Score: float64(100), Member: strconv.FormatInt(12, 10)}).Err())

	// 测试先免费抽奖
	c := handler.NewTestContext(http.MethodPost, fmt.Sprintf("/?event_id=%d", eventIDMaoMaoStar), true, nil)
	c.User().ID = 12
	_, err = actionMaoMaoStarDraw(c)
	require.NoError(err)
	// 断言已使用免费抽奖次数
	isMember, err := service.Redis.SIsMember(freeDrawUsersKey, 12).Result()
	require.NoError(err)
	assert.True(isMember)
	// 断言未使用积分
	point, err := service.Redis.ZScore(pointDrawUsersKey, strconv.FormatInt(c.User().ID, 10)).Result()
	require.NoError(err)
	assert.Equal(float64(100), point, "在奖池 1 抽奖，使用掉 0 积分，剩 100 积分")

	// 测试免费抽奖次数用完后，使用积分抽奖
	_, err = actionMaoMaoStarDraw(c)
	require.NoError(err)
	// 断言使用积分抽奖
	point, err = service.Redis.ZScore(pointDrawUsersKey, strconv.FormatInt(c.User().ID, 10)).Result()
	require.NoError(err)
	assert.Equal(float64(0), point, "在奖池 2 抽奖，使用掉 100 积分，剩 0 积分")
}
