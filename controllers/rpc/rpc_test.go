package rpc

import (
	"encoding/json"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	conf := config.BuildDefaultConf()
	handler := Handler(&conf)

	assert.Equal(handler.Name, "rpc")
	assert.Equal(1, len(handler.Middlewares))
	assert.Equal(12, len(handler.SubHandlers))

	kc.Check(handler, "/echo")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	conf := config.BuildDefaultConf()
	handler := HandlerV2(&conf)
	assert.Equal(handler.Name, "rpc")
	assert.Equal(1, len(handler.Middlewares))
	assert.Equal(1, len(handler.SubHandlers))
}

func TestScanHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	handler := scanHandler()
	assert.Equal(handler.Name, "scan")
	kc.Check(handler.Actions, "/text", "/text/detail", "/risk", "/image", "/image/detail", "/check-pm", "/im")
}

func TestMessageHandler(t *testing.T) {
	assert := assert.New(t)
	handler := messageHandler()
	assert.Equal(handler.Name, "message")
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(handler.Actions, "/add-dm", "/get-dm", "/like-dm",
		"/del-dm", "/add-comment", "/add-subcomment", "/like", "/dislike", "/report", "/exclusive-emote")
}

func TestDiscoveryHandler(t *testing.T) {
	assert := assert.New(t)
	handler := discoveryHandler()

	assert.Equal(handler.Name, "discovery")
	assert.Empty(tutil.KeyExists(tutil.Actions, handler.Actions,
		"/search", "/search-card", "/suggest", "/searchcount", "/sync-ad-koc-word", "/clean-koc-cache"))
}

func TestUtilHandler(t *testing.T) {
	assert := assert.New(t)

	h := utilHandler()
	assert.Equal("util", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "/geoip", "/is-office-ip", "/addadminlog", "/shorturl",
		"/auth-roles", "/auth-user-ids")
}

func TestDramaHandler(t *testing.T) {
	assert := assert.New(t)
	handler := dramaHandler()

	assert.Equal(handler.Name, "drama")
	assert.Empty(tutil.KeyExists(tutil.Actions, handler.Actions, "get-dramaid-by-soundid",
		"get-dramas", "must-get-dramas", "check-status", "get-drama-episodes", "check-drama-refined",
		"get-derivatives-by-ip", "ipr-dramas-subscribed", "feed-notice"))
}

func TestEventHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	h := eventHandler()
	assert.Equal("event", h.Name)
	kc.Check(h, "listongoing", "listliverank")
}

func TestActionEcho(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := strings.NewReader(`{"t":1}`)
	c := handler.NewTestContext(http.MethodPost, "/echo", false, param)
	s, err := actionEcho(c)
	require.NoError(err)
	b, e := json.Marshal(s)
	require.NoError(e)
	assert.Equal(`{"t":1}`, string(b))
}

func TestUserHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	h := userHandler()
	assert.Equal("user", h.Name)
	kc.Check(h, "block-status", "block-list", "block-status-list", "updatepoint")
}

func TestHistoryHandler(t *testing.T) {
	assert := assert.New(t)
	h := historyHandler()
	assert.Equal("history", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "/add-play-history", "/del-play-history", "/clear-play-history", "/del-live-play-history",
		"/clear-live-play-history", "/get-play-history", "/add-radio-play-history", "/get-radio-play-history")
}

func TestPersonHandler(t *testing.T) {
	assert := assert.New(t)
	h := personHandler()

	assert.Equal("person", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "follow", "unfollow")
}

func TestCronHandler(t *testing.T) {
	assert := assert.New(t)

	h := cronHandler()
	assert.Equal("cron", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "discovery/clean-search-intervention")
}

func TestSoundHandler(t *testing.T) {
	assert := assert.New(t)

	h := soundHandler()
	assert.Equal("sound", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "get-recommend")
}

func TestUtilHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := utilHandlerV2()
	assert.Equal("util", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "/parse-user-agent")
}
