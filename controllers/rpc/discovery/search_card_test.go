package discovery

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/discovery"
	"github.com/MiaoSiLa/missevan-go/models/discovery/card"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

var (
	testUserID          int64 = 346286
	testNotExistsUserID int64 = 99999
)

func TestActionSearchCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := search.Params{
		Keyword:  "测试关键词超过最大长度测试关键词超过最大长度测试关键词超过最大长度",
		UserID:   testUserID,
		Page:     1,
		PageSize: 3,
	}
	require.Greater(len(p.Keyword), searchWordMaxLength)
	c := handler.NewRPCTestContext("/rpc/discovery/search-card", p)
	result, err := ActionSearchCard(c)
	require.NoError(err)
	require.IsType(&searchCardResp{}, result)
	assert.Nil(result.(*searchCardResp).SpecialCard)
	assert.Nil(result.(*searchCardResp).UpCard)
	assert.Nil(result.(*searchCardResp).GameCard)

	p.Keyword = "测试勿删"
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", p)
	result, err = ActionSearchCard(c)
	require.NoError(err)
	require.IsType(&searchCardResp{}, result)
	assert.NotNil(result.(*searchCardResp).SpecialCard)
	upCard := result.(*searchCardResp).UpCard
	require.NotNil(upCard)
	assert.Equal(testUserID, upCard.ID)
	require.Len(upCard.WorkList, 3)
	require.IsType(&card.SoundWork{}, upCard.WorkList[0])
	assert.Equal(card.TagLive, upCard.WorkList[0].(*card.SoundWork).Tag)
	require.IsType(&card.DramaWork{}, upCard.WorkList[1])
	assert.Equal(card.TagHotDrama, upCard.WorkList[1].(*card.DramaWork).Tag)
	require.IsType(&card.DramaWork{}, upCard.WorkList[2])
	assert.Equal(card.TagNewDrama, upCard.WorkList[2].(*card.DramaWork).Tag)
	require.NotNil(result.(*searchCardResp).GameCard)

	// 测试非第一页不搜索 UP 主卡片
	p.Page = 2
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", p)
	result, err = ActionSearchCard(c)
	require.NoError(err)
	require.IsType(&searchCardResp{}, result)
	assert.Nil(result.(*searchCardResp).UpCard)
	assert.Nil(result.(*searchCardResp).GameCard)

	p.Page = 1
	p.Keyword = "不存在的关键词"
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", p)
	result, err = ActionSearchCard(c)
	require.NoError(err)
	require.IsType(&searchCardResp{}, result)
	assert.Nil(result.(*searchCardResp).UpCard)
	assert.Nil(result.(*searchCardResp).SpecialCard)
	assert.Nil(result.(*searchCardResp).GameCard)

	// 测试参数不合法
	p.Keyword = ""
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", p)
	_, err = ActionSearchCard(c)
	assert.EqualError(err, "参数不合法")
}

func TestNewSearchCardParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数不合法
	param := &searchCardParams{
		Keyword: "",
		UserID:  testUserID,
		Page:    1,
	}
	c := handler.NewRPCTestContext("/rpc/discovery/search-card", param)
	_, err := newSearchCardParams(c)
	assert.EqualError(err, "参数不合法")

	param.Keyword = "测试"
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", param)
	result, err := newSearchCardParams(c)
	require.NoError(err)
	assert.IsType(&searchCardParams{}, result)
	assert.Equal(3, result.pageSize)

	param.Page = 2
	c = handler.NewRPCTestContext("/rpc/discovery/search-card", param)
	result, err = newSearchCardParams(c)
	require.NoError(err)
	assert.Equal(5, result.pageSize)
}

func TestSearchCardParams_searchSpecialCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &searchCardParams{
		Page:     1,
		pageSize: 3,
		searchParam: &search.Params{
			Keyword:  "不存在的关键词",
			UserID:   testUserID,
			Page:     1,
			PageSize: 3,
		},
	}
	assert.Nil(param.searchSpecialCard())

	param.searchParam.Keyword = "测试特型（勿删）"
	r1 := param.searchSpecialCard()
	require.NotNil(r1)
	assert.NotEmpty(r1.Data)
	require.NotNil(r1.Pagination)
	assert.EqualValues(3, r1.Pagination.PageSize)
	assert.EqualValues(1, r1.Pagination.P)
	assert.EqualValues(2, r1.Pagination.MaxPage)
	assert.EqualValues(8, r1.Pagination.Count)

	// 测试第二页
	param.searchParam.Page = 2
	param.searchParam.PageSize = 5
	r2 := param.searchSpecialCard()
	require.NotNil(r2)
	assert.NotEmpty(r2.Data)
	assert.EqualValues(r1.Data[0].ID.(int64)-3, r2.Data[0].ID)
}

func TestSearchCardParams_searchUpCard(t *testing.T) {
	assert := assert.New(t)

	p := &searchCardParams{
		Page: 1,
		searchParam: &search.Params{
			Keyword:  "测试勿删",
			UserID:   testUserID,
			Page:     1,
			PageSize: 3,
		},
	}
	r := p.searchUpCard()
	assert.NotNil(r)
	assert.Equal(testUserID, r.ID)

	p.searchParam.Keyword = "不存在的关键词"
	r = p.searchUpCard()
	assert.Nil(r)
}

func TestSearchCardParams_searchGameCard(t *testing.T) {
	assert := assert.New(t)

	p := &searchCardParams{
		Page: 1,
		searchParam: &search.Params{
			Keyword:  "测试勿删",
			UserID:   testUserID,
			Page:     1,
			PageSize: 3,
		},
	}
	gameCard := p.searchGameCard()
	assert.NotNil(gameCard)
	v := reflect.ValueOf(*gameCard)
	fieldsCount := v.NumField()
	for i := 0; i < fieldsCount; i++ {
		assert.NotEmpty(v.Field(i).Interface())
	}

	p.searchParam.Keyword = "不存在的关键词"
	gameCard = p.searchGameCard()
	assert.Nil(gameCard)
}

func TestGetUpCardWorks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &discovery.UPCardMore{
		UserID:      testUserID,
		IsShowDrama: card.ShowTypeEnable,
		IsShowLive:  card.ShowTypeEnable,
	}
	// 测试既需要「展示直播回放」也需要「展示剧集作品」
	r, err := getUpCardWorks(m, []int64{1})
	require.NoError(err)
	require.Len(r, 3)
	require.IsType(&card.SoundWork{}, r[0])
	assert.Equal(card.TagLive, r[0].(*card.SoundWork).Tag)
	require.IsType(&card.DramaWork{}, r[1])
	assert.Equal(card.TagHotDrama, r[1].(*card.DramaWork).Tag)
	require.IsType(&card.DramaWork{}, r[2])
	assert.Equal(card.TagNewDrama, r[2].(*card.DramaWork).Tag)

	// 测试只需要「展示剧集作品」
	m.IsShowLive = 0
	r, err = getUpCardWorks(m, []int64{1})
	require.NoError(err)
	require.Len(r, 3)
	require.IsType(&card.DramaWork{}, r[0])
	assert.Equal(card.TagHotDrama, r[0].(*card.DramaWork).Tag)
	require.IsType(&card.DramaWork{}, r[1])
	assert.Equal(card.TagNewDrama, r[1].(*card.DramaWork).Tag)
	require.IsType(&card.DramaWork{}, r[2])
	assert.Equal(card.TagNewDrama, r[2].(*card.DramaWork).Tag)

	// 测试只需要「展示直播回放」
	m.IsShowDrama = 0
	m.IsShowLive = card.ShowTypeEnable
	r, err = getUpCardWorks(m, []int64{1})
	require.NoError(err)
	require.Len(r, 3)
	require.IsType(&card.SoundWork{}, r[0])
	assert.EqualValues(card.TagLive, r[0].(*card.SoundWork).Tag)
	require.IsType(&card.SoundWork{}, r[1])
	assert.EqualValues(card.TagSound, r[1].(*card.SoundWork).Tag)
	require.IsType(&card.SoundWork{}, r[2])
	assert.EqualValues(card.TagSound, r[2].(*card.SoundWork).Tag)

	// 测试既不需要「展示直播回放」也不需要「展示剧集作品」
	m.IsShowLive = 0
	r, err = getUpCardWorks(m, []int64{1})
	require.NoError(err)
	assert.Empty(r)
}

func TestFindHotDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := findHotDrama(testNotExistsUserID, []int64{})
	require.NoError(err)
	assert.Nil(result)

	result, err = findHotDrama(testUserID, []int64{1})
	require.NoError(err)
	require.NotNil(result)
	assert.Equal("单元测试剧集我的王妃", *result.Name)
}

func TestFindLatestDramas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := findLatestDramas(testUserID, 999, []int64{1}, 1)
	require.NoError(err)
	require.Len(result, 1)
	testExcludeDramaID := result[0].ID

	result, err = findLatestDramas(testNotExistsUserID, 0, []int64{}, 1)
	require.NoError(err)
	assert.Empty(result)

	result, err = findLatestDramas(testUserID, testExcludeDramaID, []int64{1}, 1)
	require.NoError(err)
	require.Len(result, 1)
	assert.NotEqual(testExcludeDramaID, result[0].ID)

	result, err = findLatestDramas(testUserID, 0, []int64{1}, 2)
	require.NoError(err)
	assert.Len(result, 2)
}
