package discovery

import (
	"sync"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

type matchedDocCount struct {
	Type  int    `json:"type"`
	Total int    `json:"total"`
	Name  string `json:"name"`
}

// 标识数组元素个数
var searchTypes = [...]int{
	// TypeSound, 音频搜索的数量综合搜索给出
	search.TypeUser,
	search.TypeAlbum,
	search.TypeSeiy,
	search.TypeDrama,
	search.TypeLive,
	search.TypeChannel,
}

var searchNameMap = map[int]string{
	search.TypeSound:   "音频",
	search.TypeUser:    "UP 主",
	search.TypeAlbum:   "音单",
	search.TypeSeiy:    "声优",
	search.TypeDrama:   "剧集",
	search.TypeLive:    "直播",
	search.TypeChannel: "频道",
}

// ActionSearchCount 搜索关键词匹配数
/**
 * @api {post} /rpc/discovery/searchcount 搜索关键词匹配数
 * @apiName discovery/searchcount
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} keyword 搜索关键词
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [{
 *       "type": 1,
 *       "total": 8,
 *       "name": "UP 主"
 *     }, {
 *       "type": 2,
 *       "total": 5000,
 *       "name": "音单"
 *     }, {
 *       "type": 4,
 *       "total": 8,
 *       "name": "声优"
 *     }, {
 *       "type": 5,
 *       "total": 20,
 *       "name": "剧集"
 *     }, {
 *       "type": 6,
 *       "total": 37,
 *       "name": "直播"
 *     }, {
 *       "type": 8,
 *       "total": 0,
 *       "name": "频道"
 *     }]
 *   }
 * @apiError (400) {Number} code 201010001
 * @apiError (400) {String} info 参数错误
 */
func ActionSearchCount(c *handler.Context) (handler.ActionResponse, error) {
	var params search.Params

	if err := loadParams(c, &params, searchWordMaxLength, processSearchParams); err != nil {
		return nil, err
	}

	result := make([]matchedDocCount, len(searchTypes))
	isRestricted := isRestrictedSearch(params, c.ClientIP())
	var wg sync.WaitGroup
	wg.Add(len(searchTypes))

	for i, v := range searchTypes {
		go func(par search.Params, i int, stype int) {
			// TODO: 后续需要加 defer PanicHandler
			defer wg.Done()

			var total int
			// 被限制的搜索（仅有特殊字符或含屏蔽词）或搜索类型为音单的搜索返回空列表
			if !isRestricted && stype != search.TypeAlbum {
				par.SearchType = stype
				setAppParam(&par)
				setAppName(&par)
				resp, err := getSearch(par)
				if err == nil {
					total = resp.Result.ViewTotal
				} else {
					logger.Errorf("search searchcount error: %v", err)
				}
			}
			result[i] = matchedDocCount{
				Type:  stype,
				Total: total,
				Name:  searchNameMap[stype],
			}
		}(params, i, v)
	}

	wg.Wait()

	return result, nil
}
