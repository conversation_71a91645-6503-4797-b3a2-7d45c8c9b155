package discovery

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/main/mkockeyword"
	"github.com/MiaoSiLa/missevan-go/models/main/msearchinterventionkeyword"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 搜索词最长长度为 60 字节
	searchWordMaxLength = 60
)

var (
	// 颜文字
	// 支持匹配版本 1.0 - 13.1 (注：13.0，13.1 版本部分浏览器无法显示的没有匹配完全)
	// 14.0 版本计划于 2021 年底发布，最终发布的候选将在 2022 年底进入主要平台
	// emoji 百科: https://emojipedia.org/
	//regEmoji = regexp.MustCompile(
	//	`[\x{1F004}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F7E0}-\x{1F7EB}\x{1F90D}-\x{1F9FF}` +
	//		`\x{1FA70}-\x{1FA95}\x{203C}-\x{2049}\x{2122}-\x{21AA}\x{231A}-\x{24C2}\x{25AA}-\x{25FE}\x{2600}-\x{26FF}` +
	//		`\x{2700}-\x{27BF}\x{2934}\x{2935}\x{2B05}-\x{2B55}\x{3030}-\x{3299}\x{FE0F}]`,
	//)

	// 过滤其他符号和不可见字符
	// 除了过滤 emoji 还有一些颜文字符号 如：✧ ▽ ✿ ♪ ❤
	// \p{Cc} ASCII 或 Latin-1 控制字符 0x00-0x1F 与 0x7F-0x9F
	// \p{So} 其他非文本符号
	// https://zh.wikipedia.org/wiki/%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F
	regEmoji = regexp.MustCompile(
		`[\p{Cc}\p{So}]`,
	)
)

// ActionSearch 搜索内容
/**
 * @api {post} /rpc/discovery/search 搜索内容
 * @apiName discovery/search
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {number=0,1,2,4,5,6,7,8,9,10} search_type 搜索类型（0 音频, 1 UP 主, 2 音单, 4 声优, 5 剧集, 6 直播间, 7 特型, 8 频道, 9 弹幕, 10 专题卡）
 * @apiParam {number=0,1,2,3} scenario 搜索场景（0 主搜（首页、发现页），1 个人搜索（个人主页音频），2 后台搜索（评论搜索后台），3 社团搜索（社团剧集搜索页））
 * @apiParam {String} keyword 搜索关键词
 * @apiParam {number=0,1,2} [sort=0] 排序方式（0 默认排序，1 播放量，2 更新时间）
 * @apiParam {number=-1,0,1} [sensitive=0] 敏感等级（-1 被限制用户获取内容，0 不获取敏感内容，1 获取敏感内容）
 * @apiParam {Number} page 第几页
 * @apiParam {Number} page_size 每页个数
 * @apiParam {Number} [view_user_id] 被查看的对应用户 ID
 * @apiParam {Number[]} [pay_type] 付费类型（0 免费，1 单集付费，2 整剧付费）
 * @apiParam {Number} [cid=0] 音频搜索时指定的分类 ID
 * @apiParam {Number[]} [org_user_ids] 社团剧集成员 ID
 * @apiParam {String} [suggest_request_id=""] 引导词的阿里云请求来源 ID
 * @apiParam {String} [extra_filters=""] 额外的过滤条件，仅支持音频、弹幕、剧集搜索时使用
 * @apiParam {String} [extra_queries=""] 额外的查询条件（缩小查询范围），JSON string，例：{"index1": value1} \
 * index1 必须是非模糊索引，否则将舍弃，value1 可以是字符串或者数字类型
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": "OK",
 *       "errors": [],
 *       "request_id": "156655084419726777026851",
 *       "result": {
 *         "total": 6374, // 满足查询条件的总结果数
 *         "num": 1, // 本次请求实际返回的结果数
 *         "viewtotal": 5000, // 查询条件实际可取到的总结果数（分页总数以此为准）
 *         "items": [{
 *           "catalog_id": "42",
 *           "checked": "1",
 *           "comment_count": "42306",
 *           "comments_count": "7012",
 *           "cover_image": "201902/05/55d7047ed0ab462f72837fe3342a500d033401.jpg",
 *           "create_time": "1549337649",
 *           "duration": "1805518",
 *           "id": "1160553",
 *           "index_name": "m_sound",
 *           "pay_type": "0",
 *           "soundstr": "《杀破狼》广播剧 番外(四)",
 *           "soundurl": "201902/05/a74bbe47de5b5dfb7c903e86bb82890e043308.mp3",
 *           "soundurl_128": "128BIT/201902/05/a74bbe47de5b5dfb7c903e86bb82890e043308.mp3",
 *           "soundurl_32": "32BIT/201902/05/a74bbe47de5b5dfb7c903e86bb82890e043308.mp3",
 *           "soundurl_64": "MP3/201902/05/a74bbe47de5b5dfb7c903e86bb82890e043308.mp3",
 *           "sub_comments_count": "641",
 *           "user_id": "441514",
 *           "username": "729声工场",
 *           "view_count": "767382"
 *         }]
 *       },
 *       "ops_request_misc": "%7B%22request%5Fid%22%3A%22156655084419726777026851%22%2C%22scm%22%3A%2220140713.110004903..%22%7D"
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response-Special、SpecialTopicCard:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": "OK",
 *       "errors": [],
 *       "request_id": "166655084419726777026851",
 *       "result": {
 *         "total": 1, // 满足查询条件的总结果数
 *         "num": 1, // 本次请求实际返回的结果数
 *         "viewtotal": 5000, // 查询条件实际可取到的总结果数（分页总数以此为准）
 *         "items": [{
 *           "id": "1",
 *           "create_time": "1549337649",
 *           "modified_time": "1549337649",
 *           "title": "专题卡测试标题",
 *           "drama_ids": "42001,42002",
 *           "url": "https://static-test.missevan.com/",
 *           "ip_id": "127001",
 *           "background": "oss://image/works/201805/22/762f97d2f6cf4e328d27c6462a8f1b35145512.png",
 *           "cover": "oss://image/works/201805/22/772f97d2f6cf4e328d27c6462a8f1b35145512.png",
 *           "start_time": "1649916396",
 *           "color": "#ffffff"
 *         }]
 *       },
 *       "ops_request_misc": "%8B%22request%5Fid%22%3A%22156655084419726777026851%22%2C%22scm%22%3A%2220140713.110004903..%22%7D"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010001
 * @apiError (400) {String} info 参数错误
 */
func ActionSearch(c *handler.Context) (handler.ActionResponse, error) {
	var params search.Params

	if err := loadParams(c, &params, searchWordMaxLength, processSearchParams); err != nil {
		return nil, err
	}
	if params.Page <= 0 || params.PageSize <= 0 {
		return nil, handler.ErrInvalidParam
	}

	// 为特殊字符（关键词被过滤掉特殊字符导致为空）或屏蔽词时，返回空数据而不报错，避免用户察觉异常
	if isRestrictedSearch(params, c.ClientIP()) {
		return search.Response{}.Empty(), nil
	}

	return getSearch(params)
}

func isRestrictedSearch(params search.Params, clientIP string) bool {
	if params.Keyword == "" {
		return true
	}
	// WORKAROUND: 弹幕搜索属于审核后台的搜索，不公开，此处不需要检查文本是否被屏蔽、过滤
	// 之后迁移弹幕审核到 ADB 之后需要删除此处针对 search.TypeDanmaku 的判断
	if params.SearchType == search.TypeDanmaku {
		return false
	}
	// 精准匹配搜索白名单中的关键词时不需要检查文本是否被屏蔽、过滤
	if isAllowedSearchKeyword(params.Keyword) {
		return false
	}
	return hasForbiddenWords(clientIP, params)
}

func getSearch(params search.Params) (*search.Response, error) {
	var searchFunc func(search.Params) (*search.Response, error)
	switch params.SearchType {
	case search.TypeSound:
		searchFunc = searchSound
	case search.TypeUser:
		searchFunc = searchUser
	case search.TypeAlbum:
		searchFunc = searchAlbum
	case search.TypeSeiy:
		searchFunc = searchSeiy
	case search.TypeDrama:
		searchFunc = searchDrama
	case search.TypeLive:
		searchFunc = searchLive
	case search.TypeSpecial:
		searchFunc = searchSpecial
	case search.TypeChannel:
		searchFunc = searchChannel
	case search.TypeDanmaku:
		searchFunc = searchDanmaku
	case search.TypeSpecialTopicCard:
		searchFunc = searchSpecialTopicCard
	default:
		return nil, actionerrors.ErrParams
	}

	return searchFunc(params)
}

// searchSound search sound data by keyword
func searchSound(params search.Params) (*search.Response, error) {
	filter := make([]string, 1, 4)
	switch params.Sensitive {
	case -1: // 对内容进行过滤（被限制用户）
		// 用法：notin(field, "number1|number2") 其中（notin 不能变更大小写，第二参数必须以双引号括起）
		// 参考文档地址：https://help.aliyun.com/document_detail/29131.html
		filter[0] = fmt.Sprintf(`checked = 1 OR (notin(catalog_id, "%s") AND checked = 2)`, "71|73|77")
	case 0: // 不获取敏感内容（未答题用户）（默认）
		filter[0] = "checked = 1"
	default: // 可获取敏感内容
		filter[0] = "checked = 1 OR checked = 2"
	}

	if params.ViewUserID > 0 {
		// 搜索个人主页音频
		if params.ViewUserID == params.UserID {
			filter[0] = fmt.Sprintf("checked >= %d", sound.CheckedTranscodeFailed)
			filter = append(filter, fmt.Sprintf("user_id = %d", params.ViewUserID))
		} else {
			// 屏蔽用户主页不可见的、无法被搜的音频
			filter = append(filter, fmt.Sprintf("user_id = %d AND (refined & %d = 0) AND (refined & %d = 0)", params.ViewUserID, sound.RefinedBlock, sound.RefinedSearchLimit))
		}
	} else {
		// 屏蔽无法被搜的音频
		filter = append(filter, fmt.Sprintf("(refined & %d = 0)", sound.RefinedSearchLimit))
	}

	if params.Cid > 0 {
		// 搜索指定分类音频
		filter = append(filter, fmt.Sprintf("catalog_id = %d", params.Cid))
	}

	rankName, ok := params.AppParam.SortName(params.Sort)
	if !ok {
		return nil, actionerrors.ErrParams
	}
	if len(params.PayType) > 0 {
		payTypeRange := util.JoinIntArray(params.PayType, "|")
		filter = append(filter, fmt.Sprintf(`in(pay_type, "%s")`, payTypeRange))
	}
	params.Filter = filter
	if params.ExtraFilters != "" {
		params.Filter = append(params.Filter, params.ExtraFilters)
	}
	params.SecondRankName = rankName

	return service.OpenSearch.Search(params)
}

func isDramaKeywordPass(keyword string) bool {
	isForbidden, err := forbiddenwords.HasForbiddenWords(forbiddenwords.ForbiddenWordTypeDrama, keyword)
	if err != nil {
		logger.Errorf("check drama keyword error: %v", err)
		return true
	}
	return !isForbidden
}

// searchDrama search drama data by keyword
func searchDrama(params search.Params) (*search.Response, error) {
	// 剧集搜索时获取置顶剧集的剧集 ID
	mkockeyword.Reassign(&params)
	// 主搜默认排序全部分区时或搜索剧集时触发
	if params.Scenario == search.ScenarioMainSearch &&
		params.Sort == search.SortDefault && params.ExtraFilters == "" {
		// 剧集搜索时获取设置固定剧集参数
		// TODO: 后续其他搜索也有固定内容的需求，考虑使用工厂或者策略模式实现
		msearchinterventionkeyword.SetInsertParams(&params)
	}

	pass := isDramaKeywordPass(params.Keyword)
	if !pass {
		resp := search.Response{}.Empty()
		return resp, nil
	}

	filter := make([]string, 1, 5)

	if params.ViewUserID > 0 && params.ViewUserID == params.UserID {
		// 获取除审核未通过以外的剧集
		filter[0] = fmt.Sprintf("checked != %d", dramainfo.CheckedDiscontinued)
	} else {
		// 获取过审剧集
		filter[0] = fmt.Sprintf("checked = %d", dramainfo.CheckedPass)
	}
	if params.Sensitive <= 0 {
		// 不获取报警剧集
		filter = append(filter, "police = 0")
		// TODO: 当 police 字段与 checked 字段整合后，则过滤条件根据是否已答题情况有如下情况：
		// 已答题 $filters = ['checked in("1|3")']; （checked = 3 为报警）
		// 未答题 $filters = ['checked = 1']
	}
	if params.ViewUserID > 0 {
		filter = append(filter, fmt.Sprintf("user_id = %d", params.ViewUserID))
	} else if len(params.OrgUserIDs) > 0 {
		userIDRange := util.JoinIntArray(params.OrgUserIDs, "|")
		// 用法：in(field, "number1|number2") 其中（in 不能变更大小写，第二参数必须以双引号括起）
		// 参考文档地址：https://help.aliyun.com/document_detail/51260.html
		filter = append(filter, fmt.Sprintf(`in(user_id, "%s")`, userIDRange))
	}
	if len(params.PayType) > 0 {
		payTypeRange := util.JoinIntArray(params.PayType, "|")
		filter = append(filter, fmt.Sprintf(`in(pay_type, "%s")`, payTypeRange))
	}

	allFixedTargetIDsLength := len(params.AllFixedTargetIDs)
	if allFixedTargetIDsLength != 0 {
		// 生成的过滤子句: filter=notin(id, "1|3")
		filter = append(filter, fmt.Sprintf(`notin(id, "%s")`, strings.Join(params.AllFixedTargetIDs, "|")))
	}

	rankName, ok := params.AppParam.SortName(params.Sort)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	params.Filter = filter
	if params.ExtraFilters != "" {
		params.Filter = append(params.Filter, params.ExtraFilters)
	}

	params.SecondRankName = rankName

	response, err := service.OpenSearch.Search(params)
	if err != nil {
		return nil, err
	}

	currentActiveFixedParamsLength := len(params.CurrentActiveFixedParams)
	if currentActiveFixedParamsLength == 0 {
		response.AddResultTotal(allFixedTargetIDsLength)
		return response, nil
	}

	dramaIDs := make([]int64, 0, currentActiveFixedParamsLength)
	for _, fixedParam := range params.CurrentActiveFixedParams {
		dramaIDs = append(dramaIDs, fixedParam.TargetID)
	}
	// 查询剧集
	dramaMap, err := findDramaMapByIDs(dramaIDs)
	if err != nil {
		logger.Error(err)
		// PASS
		return response, nil
	}

	insertDataItems := make([]search.InsertDataItem, 0, currentActiveFixedParamsLength)
	for _, fixedParam := range params.CurrentActiveFixedParams {
		dramaInfo, ok := dramaMap[fixedParam.TargetID]
		if !ok {
			continue
		}
		dramaInfo.IsInsert = search.IsInsert
		dramaInfoBytes, err := json.Marshal(dramaInfo)
		if err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		insertDataItems = append(insertDataItems, search.InsertDataItem{
			Index: fixedParam.TargetIndex,
			Item:  dramaInfoBytes,
		})
	}
	response.InsertResultItems(insertDataItems, allFixedTargetIDsLength)
	return response, nil
}

// confirm 每一位的作用
// 文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/product/用户_confirm_字段值约定.md
// NOTICE: 此处需要和别的地方的注销用户 confirm 定义保持同步
const confirmDeleted uint = 1 << 12 // 已注销的用户

// searchUser search user data by keyword
func searchUser(params search.Params) (*search.Response, error) {
	var ok bool
	params.Filter = []string{fmt.Sprintf("confirm & %d = 0", confirmDeleted)}
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchAlbum search album data by keyword
func searchAlbum(params search.Params) (*search.Response, error) {
	filter := make([]string, 1, 2)
	// 不显示私有音单（第 5 位为 1 表示私有音单）
	filter[0] = "(refined & 16) = 0"
	if params.Sensitive <= 0 {
		filter = append(filter, "checked = 1")
	}

	params.Filter = filter
	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchSeiy search CV data by keyword
func searchSeiy(params search.Params) (*search.Response, error) {
	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchSpecial search special item data by keyword
func searchSpecial(params search.Params) (*search.Response, error) {
	filter := make([]string, 1)
	filter[0] = fmt.Sprintf("type = %d", search.SpecialDefault)
	params.Filter = filter
	// 特型搜索第一页和后续页的 pageSize 有差异，后续页需要特殊处理取数时的 offset
	if params.Page > 1 {
		params.SkipOffset -= search.SpecialFirstAndAfterPageSizeDiff
	}

	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchSpecialTopicCard search special_topic_card item data by keyword
func searchSpecialTopicCard(params search.Params) (*search.Response, error) {
	filter := make([]string, 3)

	nowUnix := util.TimeNow().Unix()
	filter[0] = fmt.Sprintf("type = %d", search.SpecialTopicCard)
	filter[1] = "status = 1"
	filter[2] = fmt.Sprintf("start_time <= %d", nowUnix)
	params.Filter = filter

	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchChannel search channel data by keyword
func searchChannel(params search.Params) (*search.Response, error) {
	filter := make([]string, 1)
	filter[0] = "recommended = 1 AND sort_channel > 5"

	params.Filter = filter
	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchLive search live data by keyword
func searchLive(params search.Params) (*search.Response, error) {
	var ok bool
	// WORKAROUND: 直播应用没有连用户表，解决用户表大量更新导致延迟的问题，暂时无法判断用户注销情况
	// params.Filter = []string{fmt.Sprintf("confirm & %d = 0", confirmDeleted)}
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// searchDanmaku search danmaku data by keyword
func searchDanmaku(params search.Params) (*search.Response, error) {
	if params.ExtraFilters != "" {
		params.Filter = append(params.Filter, params.ExtraFilters)
	}
	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrParams
	}

	return service.OpenSearch.Search(params)
}

// ActionCleanKocCache 清除 KOC 缓存
/**
 * @api {post} /rpc/discovery/clean-koc-cache 清除 KOC 缓存
 * @apiName discovery/clean-koc-cache
 * @apiVersion 0.1.0
 * @apiGroup rpc
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": true // 缓存是否清除成功
 *     }
 *   }
 */
func ActionCleanKocCache(c *handler.Context) (handler.ActionResponse, error) {
	key := serviceredis.KeyOpenSearchKocKeywordTargetID0.Format()
	err := service.LRURedis.Del(key).Err()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithField("key", key).Error("清除 redis 缓存失败")

		return handler.M{"status": false}, nil
	}

	return handler.M{"status": true}, nil
}
