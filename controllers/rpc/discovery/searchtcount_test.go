package discovery

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

func TestSearchCount(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := search.Params{
		Keyword: "魔道",
		UserID:  346286,
	}
	c := handler.NewTestContext("GET", "/rpc/discovery/searchcount", false, params)
	c.Request().Header.Set("Content-Type", "application/json")
	c.Request().Header.Set("X-Forwarded-For", "127.0.0.1")

	info, err := ActionSearchCount(c)
	require.NoError(err)
	assert.NotNil(info)

	// TypeSound、TypeSpecialTopicCard 由综合搜索给出，TypeSpecial 不在此返回，TypeDanmaku 仅在审核接口使用
	searchNum := reflect.ValueOf(service.OpenSearch.Apps).NumField() - 4
	require.Len(searchTypes, searchNum)
	require.Len(searchTypes, len(searchNameMap)-1)

	data := info.([]matchedDocCount)
	assert.Equal(len(data), searchNum)
	for _, v := range data {
		if v.Type == search.TypeDrama {
			assert.GreaterOrEqual(v.Total, 1)
		}
	}

	params.Keyword = searchForbiddenWordsTestingList[0].(string)
	c = handler.NewTestContext("GET", "/rpc/discovery/searchcount", false, params)
	info, err = ActionSearchCount(c)
	require.NoError(err)
	require.IsType([]matchedDocCount{}, data)
	data = info.([]matchedDocCount)
	assert.Len(data, len(searchTypes))
	for _, v := range data {
		assert.NotEmpty(v.Name)
		assert.Zero(v.Total)
	}
}
