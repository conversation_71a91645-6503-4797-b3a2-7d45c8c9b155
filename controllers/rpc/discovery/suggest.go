package discovery

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 搜索建议词最长长度为 18 字节
const suggestWordMaxLength = 18

// ActionSuggest 搜索建议
/**
 * @api {post} /rpc/discovery/suggest 搜索建议
 * @apiName discovery/suggest
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {number=0,1,2,4,5,6,7,8} search_type 搜索类型（0 音频, 1 UP 主, 2 音单, 4 声优, 5 剧集, 6 直播间, 7 特型, 8 频道, 9 弹幕）
 * @apiParam {String} keyword 搜索关键词
 * @apiParam {Number} page_size 每页个数
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "errors": null,
 *       "request_id": "156681001619722133843492",
 *       "searchtime": 0.083965,
 *       "suggestions": [{
 *         "suggestion": "三体有声小说第一部：地球往事(哈哈笑)[50回]"
 *       }, {
 *         "suggestion": "三体有声小说第三部：死神永生(哈哈笑)[99回]"
 *       }]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010001
 * @apiError (400) {String} info 参数错误
 */
func ActionSuggest(c *handler.Context) (handler.ActionResponse, error) {
	var params search.Params

	if err := loadParams(c, &params, suggestWordMaxLength, processSuggestParams); err != nil {
		return nil, err
	}
	if params.Keyword == "" || hasForbiddenWords(c.ClientIP(), params) {
		// 为特殊字符（关键词被过滤掉特殊字符导致为空）或屏蔽词时，返回空数据而不报错，避免用户察觉异常
		return search.SuggestResponse{}.Empty(), nil
	}

	if params.SearchType == search.TypeDrama {
		pass := isDramaKeywordPass(params.Keyword)
		if !pass {
			return &search.SuggestResponse{
				Errors:      []search.ResponseError{},
				Suggestions: []search.SuggestItem{},
			}, nil
		}
	}

	return service.OpenSearch.Suggest(params)
}

func loadParams(c *handler.Context, params *search.Params, keywordMaxLength int, moreProcess func(*handler.Context, *search.Params)) error {
	err := c.BindJSON(params)
	if err != nil || params.Keyword == "" {
		return actionerrors.ErrParams
	}
	if ok := setAppParam(params); !ok {
		return actionerrors.ErrParams
	}
	if ok := setAppName(params); !ok {
		return actionerrors.ErrParams
	}
	if params.ExtraQueries != "" {
		params.ExtraQueryIndexMap, err = search.SupportExtraIndexMap(params.ExtraQueries, params.AppParam.Indexes)
		if err != nil {
			return actionerrors.ErrParamMsg(err.Error())
		}
	}
	// TODO: 等调用方增加 user_id 后删除
	u, err := user.FromToken(c.Token(), c.ClientIP())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if u != nil {
		params.UserID = u.ID
	}
	truncateTooLongKeyword(params, keywordMaxLength)
	if moreProcess != nil {
		moreProcess(c, params)
	}
	return nil
}

// setAppName set open search app param by search type
func setAppParam(p *search.Params) bool {
	app, ok := service.OpenSearch.App(p.SearchType)
	if !ok {
		return false
	}

	p.AppParam = app
	return true
}

// setAppName set open search app name by search type
// NOTICE: 预发环境中如果 Redis 中有配置对应开放搜索应用名称的开放搜索应用 ID，则自动使用应用 ID
func setAppName(p *search.Params) bool {
	app, ok := service.OpenSearch.App(p.SearchType)
	if !ok {
		p.AppName = ""
		return false
	}

	// 默认开放搜索应用名称
	p.AppName = app.Name
	// 如果是预发环境则自动从 redis 获取，获取失败或是值为空，则使用默认值
	if util.IsPreEnv() {
		r, err := service.Redis.Get(serviceredis.KeyOpenSearchOfflineAppID1.Format(app.Name)).Result()
		if err != nil && !serviceredis.IsRedisNil(err) {
			// 获取失败时打印 warn 日志
			logger.WithField("app_name", app.Name).Warnf("failed to get opensearch offline app_id: %v", err)
			// PASS
		}
		if r != "" {
			p.AppName = r
		}
	}

	return ok
}
