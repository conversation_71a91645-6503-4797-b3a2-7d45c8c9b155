package discovery

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/url"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestSuggest(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	c := handler.CreateTestContext(true)

	params := search.Params{
		Keyword:    "阿",
		PageSize:   10,
		SearchType: search.TypeSound,
	}
	reqBody, _ := json.Marshal(params)

	req, err := http.NewRequest("GET", "/rpc/discovery/suggest", bytes.NewBuffer(reqBody))
	require.NoError(err)
	req.Header.Set("Content-Type", "application/json")
	c.C.Request = req
	info, err := ActionSuggest(c)
	require.NoError(err)
	assert.NotNil(info)

	data := info.(*search.SuggestResponse)
	assert.GreaterOrEqual(len(data.Suggestions), 1)
	assert.Contains(data.Suggestions[0].Suggestion, "阿")

	// 测试关键词为屏蔽词时返回空结果
	params.Keyword = "ASMR"
	reqBody, _ = json.Marshal(params)
	c = handler.NewRPCTestContext("/rpc/discovery/suggest", bytes.NewBuffer(reqBody))
	info, err = ActionSuggest(c)
	require.NoError(err)
	result := info.(*search.SuggestResponse)
	assert.Empty(result.Suggestions)
}

func TestTruncateTooLongKeyword(t *testing.T) {
	assert := assert.New(t)

	params := search.Params{
		Keyword:    "阿",
		UserID:     346286,
		PageSize:   10,
		SearchType: search.TypeSound,
	}

	truncateTooLongKeyword(&params, suggestWordMaxLength)
	assert.Equal("阿", params.Keyword)

	params.Keyword = "统一康师傅方便面"
	truncateTooLongKeyword(&params, suggestWordMaxLength)
	assert.Equal("统一康师傅方", params.Keyword)

	params.Keyword = "abcdeABCDEabcdeABCDE"
	truncateTooLongKeyword(&params, suggestWordMaxLength)
	assert.Equal("abcdeABCDEabcdeABC", params.Keyword)

	params.Keyword = "abcdeABCDE一二三"
	truncateTooLongKeyword(&params, suggestWordMaxLength)
	assert.Equal("abcdeABCDE一二", params.Keyword)
}

func TestRemoveEmoji(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := search.Params{
		Keyword:    ` 中aB⚽]🔑💊$😂5!@#$%^&*()|🤗🤔🤐☟🤘👌⭐🤣🤫🤣🤥🤠🈚🤢🤧🖤💛💰♈まさとも뫼이빨【товарищ】w(ﾟДﾟ)wヽ(✿ﾟ▽ﾟ)ノ(๑•̀ㅂ•́)و✧♪(^∇^*)(゜-゜)つロ 干杯~ `,
		UserID:     346286,
		PageSize:   10,
		SearchType: search.TypeSound,
	}

	removeEmoji(&params)
	assert.Equal(`中aB]$5!@#$%^&*()|まさとも뫼이빨【товарищ】w(ﾟДﾟ)wヽ(ﾟﾟ)ノ(๑•̀ㅂ•́)و(^∇^*)(゜-゜)つロ 干杯~`, params.Keyword)

	params.Keyword = " 一二三abc123 💓 "
	removeEmoji(&params)
	assert.Equal(`一二三abc123`, params.Keyword)
	params.Keyword = " 🥣 🔑 "
	removeEmoji(&params)
	assert.Empty(params.Keyword)

	// 测试过滤不可见字符，空格除外
	params.Keyword = `一二三	
					abco 123	`
	removeEmoji(&params)
	assert.Equal("一二三abco 123", params.Keyword)

	// 测试包含 NULL 不可见文本的情况，%00 对应 ASCII 为 NULL
	decode, err := url.QueryUnescape("一二三%00abc123")
	require.NoError(err)
	params.Keyword = decode
	removeEmoji(&params)
	assert.Equal("一二三abc123", params.Keyword)
}

func TestProcessSuggestParams(t *testing.T) {
	assert := assert.New(t)
	c := handler.CreateTestContext(true)
	params := search.Params{
		Keyword:    `【d\-**是ئۇيغۇر تىلى不是了134348 Ббיום הולדת שמח Вв Гг Дд Ее Ёё Жж88--]\*"dfe02. 秘密の遊び  CV:古le françai川慎＆中泽まさとも在工在VbD】EFk`,
		PageSize:   10,
		SearchType: search.TypeSound,
	}
	processSuggestParams(c, &params)
	assert.Equal(`d是不是了13434888dfe02秘密の遊ひCV古lefranai川慎中泽まさとも在工在VbDEFk`, params.Keyword)

	params.Keyword = `\+[Well, it's ok==+)`
	processSuggestParams(c, &params)
	assert.Equal(`Well, it's ok`, params.Keyword)
}

func TestProcessSearchParams(t *testing.T) {
	assert := assert.New(t)
	c := handler.CreateTestContext(true)
	params := search.Params{
		Keyword:    `杀破狼`,
		PageSize:   10,
		SearchType: search.TypeSound,
	}
	processSearchParams(c, &params)

	params.SearchType = 15
	assert.PanicsWithValue("Error search type: 15", func() {
		processSearchParams(c, &params)
	})
}

func TestLoadParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var params search.Params
	body := handler.M{
		"keyword":     "",
		"search_type": search.TypeSound,
	}
	c := handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	err := loadParams(c, &params, 10, nil)
	assert.EqualError(err, "参数错误")

	body = handler.M{
		"keyword":     "阿",
		"search_type": 1000,
	}
	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	err = loadParams(c, &params, 10, nil)
	assert.EqualError(err, "参数错误")

	body = handler.M{
		"keyword":     "阿",
		"search_type": search.TypeSound,
	}
	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	assert.NoError(loadParams(c, &params, 10, nil))

	// 获取环境变量
	originalEnv := os.Getenv(util.EnvDeploy)
	defer func() {
		os.Setenv(util.EnvDeploy, originalEnv)
	}()

	// 设定为预发环境以测试预发环境下自动获取 redis 中存储的 app_id
	os.Setenv(util.EnvDeploy, util.DeployEnvPre)

	// 设定 redis 中存储的 app_id 值
	app, ok := service.OpenSearch.App(search.TypeSound)
	assert.True(ok)
	key := serviceredis.KeyOpenSearchOfflineAppID1.Format(app.Name)
	err = service.Redis.Set(key, "test_id", 2*time.Second).Err()
	require.NoError(err)

	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	assert.NoError(loadParams(c, &params, 10, nil))
	assert.Equal("test_id", params.AppName)

	// 测试当值为空的时候是否是默认的应用名称
	err = service.Redis.Set(key, "", 2*time.Second).Err()
	require.NoError(err)

	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	assert.NoError(loadParams(c, &params, 10, nil))
	assert.Equal(app.Name, params.AppName)

	// 还原环境后应该是正常的 AppParam.Name
	os.Setenv(util.EnvDeploy, originalEnv)
	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	assert.NoError(loadParams(c, &params, 10, nil))
	assert.Equal(app.Name, params.AppName)

	body = handler.M{
		"keyword":       "阿",
		"search_type":   search.TypeDanmaku,
		"extra_queries": `{"sound_id": 2233}`,
	}
	c = handler.NewRPCTestContext("/rpc/discovery/suggest", body)
	err = loadParams(c, &params, 10, nil)
	require.NoError(err)
	assert.Contains(params.ExtraQueryIndexMap, "sound_id")
}

func TestSetAppParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := &search.Params{
		Keyword:    "阿",
		UserID:     346286,
		PageSize:   20,
		SearchType: -1,
	}

	assert.False(setAppParam(params))
	params.SearchType = search.TypeDrama

	assert.True(setAppParam(params))
	sortNameOriginal, ok := service.OpenSearch.Apps.Drama.SortName(0)
	require.True(ok)
	sortNameParam, ok := params.AppParam.SortName(0)
	require.True(ok)
	assert.Equal(sortNameOriginal, sortNameParam)
	assert.Equal(service.OpenSearch.Apps.Drama.Name, params.AppParam.Name)

	params.SearchType = 100
	assert.False(setAppName(params))
}

func TestSetAppName(t *testing.T) {
	assert := assert.New(t)

	params := &search.Params{
		Keyword:    "阿",
		UserID:     346286,
		PageSize:   20,
		SearchType: search.TypeDrama,
	}

	assert.True(setAppName(params))
	assert.Equal(service.OpenSearch.Apps.Drama.Name, params.AppName)

	params.SearchType = 100
	assert.False(setAppName(params))
}
