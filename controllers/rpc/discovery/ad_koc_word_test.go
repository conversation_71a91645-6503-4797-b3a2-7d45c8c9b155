package discovery

import (
	"bytes"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

func TestActionSyncAdKOCWord(t *testing.T) {
	require := require.New(t)

	bodyBytes, _ := json.Marshal(adKOCParams{
		Type:  syncAdKOCWordTypeUpdateOrInsert,
		Word:  "",
		Alias: []string{},
	})
	ctx := handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes))
	_, err := ActionSyncAdKOCWord(ctx)
	require.EqualError(err, actionerrors.ErrParams.Error())

	bodyBytes, _ = json.Marshal(adKOCParams{
		Type:  syncAdKOCWordTypeUpdateOrInsert,
		Word:  "apple",
		Alias: []string{"iphone"},
	})
	ctx = handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes))
	_, err = ActionSyncAdKOCWord(ctx)
	require.NoError(err)
}

func TestAdKOCParamsLoad(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var params adKOCParams
	bodyBytes, _ := json.Marshal(adKOCParams{
		Type:  syncAdKOCWordTypeUpdateOrInsert,
		Word:  "",
		Alias: []string{},
	})
	err := params.load(handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes)))
	assert.EqualError(err, actionerrors.ErrParams.Error())

	bodyBytes, _ = json.Marshal(adKOCParams{
		Type:  syncAdKOCWordTypeUpdateOrInsert,
		Word:  "abc",
		Alias: []string{},
	})
	err = params.load(handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes)))
	assert.EqualError(err, actionerrors.ErrParams.Error())

	bodyBytes, _ = json.Marshal(adKOCParams{
		Type:  987654321,
		Word:  "abc",
		Alias: []string{"cde"},
	})
	err = params.load(handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes)))
	assert.EqualError(err, actionerrors.ErrParams.Error())

	bodyBytes, _ = json.Marshal(adKOCParams{
		Type:  syncAdKOCWordTypeUpdateOrInsert,
		Word:  "abc",
		Alias: []string{"cde"},
	})
	err = params.load(handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes)))
	require.NoError(err)
	assert.Equal("abc", params.Word)
	assert.Equal([]string{"cde"}, params.Alias)
	assert.Equal(search.ManagementCmdAdd, params.cmd)

	bodyBytes, _ = json.Marshal(adKOCParams{
		Type: syncAdKOCWordTypeDelete,
		Word: "efg",
	})
	err = params.load(handler.NewRPCTestContext("/rpc/discovery/sync-ad-koc-word", bytes.NewBuffer(bodyBytes)))
	require.NoError(err)
	assert.Equal("efg", params.Word)
	assert.Equal(search.ManagementCmdDelete, params.cmd)
}

func TestAdKOCParamsSyncToOpenSearch(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := adKOCParams{
		cmd:   search.ManagementCmdAdd,
		Word:  "apple",
		Alias: []string{"iphone"},
	}
	resp, err := params.syncToOpenSearch()
	require.NoError(err)
	ok := resp.(bool)
	assert.True(ok)

	params = adKOCParams{
		cmd:  search.ManagementCmdDelete,
		Word: "apple",
	}
	resp, err = params.syncToOpenSearch()
	require.NoError(err)
	ok = resp.(bool)
	assert.True(ok)
}
