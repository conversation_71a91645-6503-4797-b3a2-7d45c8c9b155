package discovery

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

// 包含中文、日文或数字
var regUnicodeLetter = regexp.MustCompile(`[\p{Han}\p{Katakana}\p{Hiragana}0-9]+`)

// 英文短句（如 it's ok，或 Hello World）
var regAlphaLetter = regexp.MustCompile(`.*?([a-zA-Z]+(.*[a-zA-Z])?).*`)

// 数字或文字（英文、中文或日文）
var regNumberOrLetter = regexp.MustCompile(`([\p{Han}\p{Katakana}\p{Hiragana}0-9]|[a-zA-Z])+`)

func removeEmoji(params *search.Params) {
	params.Keyword = strings.TrimSpace(regEmoji.ReplaceAllString(params.Keyword, ""))
}

func processSearchParams(c *handler.Context, params *search.Params) {
	removeEmoji(params)

	if _, err := strconv.Atoi(params.Keyword); err == nil {
		params.IsKeywordNumber = true
	}
	sceneTagKey := getABTestSceneTagKey(params.SearchType)
	if params.IsKeywordNumber {
		sceneTagKey += "_id"
	}
	if sceneTag := service.Redis.HGet(serviceredis.KeyOpenSearchConfig0.Format(), fmt.Sprintf("abtest_scene_tag_%s", sceneTagKey)); sceneTag.Val() != "" {
		params.ABTest = fmt.Sprintf("scene_tag:%s,flow_divider:%s", sceneTag.Val(), c.ClientIP())
	}
}

// 获取 AB 测试的场景标识对应的 Key
func getABTestSceneTagKey(searchType int) (sceneTagKey string) {
	switch searchType {
	case search.TypeSound:
		sceneTagKey = "sound"
	case search.TypeUser:
		sceneTagKey = "user"
	case search.TypeAlbum:
		sceneTagKey = "album"
	case search.TypeSeiy:
		sceneTagKey = "seiy"
	case search.TypeDrama:
		sceneTagKey = "drama"
	case search.TypeLive:
		sceneTagKey = "live"
	case search.TypeSpecial:
		sceneTagKey = "special"
	case search.TypeChannel:
		sceneTagKey = "channel"
	case search.TypeDanmaku:
		sceneTagKey = "danmaku"
	case search.TypeSpecialTopicCard:
		sceneTagKey = "special_topic_card"
	default:
		panic(fmt.Sprintf("Error search type: %d", searchType))
	}

	return
}

// 联想词过滤规则：英文词（短句）去除首尾空格及符号，其它词去除全部空格及符号
// https://github.com/MiaoSiLa/requirements-doc/tree/master/2018-12-18 搜索的智能联想/README.md
func processSuggestParams(_ *handler.Context, params *search.Params) {
	if regUnicodeLetter.MatchString(params.Keyword) {
		params.Keyword = strings.Join(regNumberOrLetter.FindAllString(params.Keyword, -1), "")
	}
	params.Keyword = regAlphaLetter.ReplaceAllString(params.Keyword, `${1}`)
}

func truncateTooLongKeyword(params *search.Params, maxLength int) {
	if len(params.Keyword) <= maxLength {
		return
	}
	var length int
	word := make([]rune, 0, maxLength)
	for _, w := range params.Keyword {
		length += len(string(w))
		if length > maxLength {
			break
		}
		word = append(word, w)
	}
	params.Keyword = string(word)
}

func isAllowedSearchKeyword(keyword string) bool {
	key := serviceredis.KeyOpenSearchKeywordAllowList0.Format()
	val, err := service.Redis.SIsMember(key, keyword).Result()
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return val
}

// TODO: 调整写法，添加判定的屏蔽等级
func hasForbiddenWords(clientIP string, params search.Params) bool {
	keyword := params.Keyword
	isForbidden, err := forbiddenwords.HasForbiddenWords(forbiddenwords.ForbiddenWordTypeSearch, keyword)
	if err != nil {
		logger.Errorf("search check text error: %v", err)
		// PASS
	} else if isForbidden {
		return true
	}

	checkResults, _, err := userapi.CheckUserTexts(userapi.TextScanTask{
		Text:   []string{keyword},
		Scene:  scan.SceneSearch,
		UserID: params.UserID,
		IP:     clientIP,
		AdFree: false,
	})
	if err != nil {
		logger.Errorf("search check text error: %v", err)
		return false
	}
	return !checkResults[0].Pass
}
