package discovery

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

// KOC 词汇操作类型
const (
	syncAdKOCWordTypeUpdateOrInsert = iota
	syncAdKOCWordTypeDelete
)

type adKOCParams struct {
	Word  string   `json:"word"`
	Alias []string `json:"alias"`
	Type  int      `json:"type"`

	cmd string
}

// ActionSyncAdKOCWord 同步 KOC 词汇
/**
 * @api {post} /rpc/discovery/sync-ad-koc-word 同步 KOC 词汇
 * @apiName discovery/sync-ad-koc-word
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {number=0,1} [type=0] 操作类型（0 新加或覆盖，1 删除）
 * @apiParam {String} word KOC 词
 * @apiParam {String[]} [alias] 同义词（type=0 时必填）
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 * @apiError (400) {Number} code 201010001
 * @apiError (400) {String} info 参数错误
 */
func ActionSyncAdKOCWord(ctx *handler.Context) (handler.ActionResponse, error) {
	var params adKOCParams
	if err := params.load(ctx); err != nil {
		return nil, err
	}

	return params.syncToOpenSearch()
}

func (params *adKOCParams) load(ctx *handler.Context) error {
	if err := ctx.BindJSON(params); err != nil || params.Word == "" {
		return actionerrors.ErrParams
	}
	switch params.Type {
	case syncAdKOCWordTypeUpdateOrInsert:
		if len(params.Alias) == 0 {
			return actionerrors.ErrParams
		}
		params.cmd = search.ManagementCmdAdd
	case syncAdKOCWordTypeDelete:
		params.cmd = search.ManagementCmdDelete
	default:
		return actionerrors.ErrParams
	}

	return nil
}

func (params *adKOCParams) syncToOpenSearch() (handler.ActionResponse, error) {
	err := service.OpenSearch.PushItemToAllSynonym(params.cmd, params.Word, params.Alias)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	err = service.OpenSearch.PushItemToAllSuggestDenyList(params.cmd, params.Word)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	err = service.OpenSearch.PushItemToAllUserAnalyzer(params.cmd, params.Word, params.Word)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	return true, nil
}
