package discovery

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

var searchForbiddenWordsTestingList = []interface{}{"ASMR", `[|]^$*.-+(\)`, "考前答案"}
var allowedSearchKeywordTestingList = []interface{}{"禁断"}

func TestMain(m *testing.M) {
	service.InitTest()

	cleanup := search.MockOpenSearchManagementServer(service.OpenSearch)
	defer cleanup()

	forbiddenWordsKey := forbiddenwords.RedisKey(forbiddenwords.ForbiddenWordTypeSearch)
	service.Redis.SAdd(forbiddenWordsKey, searchForbiddenWordsTestingList...)
	allowedListKey := serviceredis.KeyOpenSearchKeywordAllowList0.Format()
	service.Redis.SAdd(allowedListKey, allowedSearchKeywordTestingList...)
	code := m.Run()

	service.Redis.SRem(forbiddenWordsKey, searchForbiddenWordsTestingList...)
	service.Redis.SRem(allowedListKey, allowedSearchKeywordTestingList...)
	os.Exit(code)
}

func TestGetABTestSceneTagKey(t *testing.T) {
	assert := assert.New(t)

	sceneTagKey := getABTestSceneTagKey(search.TypeSound)
	assert.Equal("sound", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeUser)
	assert.Equal("user", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeAlbum)
	assert.Equal("album", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeSeiy)
	assert.Equal("seiy", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeDrama)
	assert.Equal("drama", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeLive)
	assert.Equal("live", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeSpecial)
	assert.Equal("special", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeChannel)
	assert.Equal("channel", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeDanmaku)
	assert.Equal("danmaku", sceneTagKey)

	sceneTagKey = getABTestSceneTagKey(search.TypeSpecialTopicCard)
	assert.Equal("special_topic_card", sceneTagKey)

	assert.PanicsWithValue("Error search type: 15", func() {
		sceneTagKey = getABTestSceneTagKey(15)
	})
}

func TestIsAllowedSearchKeyword(t *testing.T) {
	assert := assert.New(t)

	isAllowedKeyword := isAllowedSearchKeyword(allowedSearchKeywordTestingList[0].(string))
	assert.True(isAllowedKeyword)

	isAllowedKeyword = isAllowedSearchKeyword("非白名单关键词")
	assert.False(isAllowedKeyword)
}
