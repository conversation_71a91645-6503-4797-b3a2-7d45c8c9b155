package discovery

import (
	"bytes"
	"encoding/json"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCleanKocCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/rpc/discovery/clean-koc-cache", false, nil)
	info, err := ActionCleanKocCache(c)
	require.NoError(err)
	data := info.(handler.M)
	assert.Equal(true, data["status"])

	// 生成测试缓存
	key := serviceredis.KeyOpenSearchKocKeywordTargetID0.Format()
	require.NoError(service.LRURedis.HSet(key, "测试关键词", 1).Err())

	c = handler.NewTestContext(http.MethodPost, "/rpc/discovery/clean-koc-cache", false, nil)
	info, err = ActionCleanKocCache(c)
	require.NoError(err)
	data = info.(handler.M)
	assert.Equal(true, data["status"])

	// 确认缓存是否清除
	val := service.LRURedis.Exists(key).Val()
	assert.Equal(int64(0), val)
}

func TestSearch(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params := search.Params{
		Keyword:    "阿",
		Sort:       0,
		UserID:     346286,
		Sensitive:  0,
		Page:       0,
		PageSize:   20,
		SearchType: search.TypeSound,
	}
	reqBody, _ := json.Marshal(params)
	c := handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	_, err := ActionSearch(c)
	require.EqualError(err, handler.ErrInvalidParam.Message)

	params.Page = search.MaxSearchResultCount * 2
	reqBody, _ = json.Marshal(params)
	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err := ActionSearch(c)
	require.NoError(err)
	assert.NotNil(info)
	data := info.(*search.Response)
	assert.Len(data.Result.Items, 0)

	params.Page = 1
	params.Keyword = "▽"
	c = handler.NewRPCTestContext("/rpc/discovery/search", params)
	info, err = ActionSearch(c)
	require.NoError(err)
	assert.Equal(search.Response{}.Empty(), info)

	params.Keyword = "啊"
	reqBody, _ = json.Marshal(params)
	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	assert.NotNil(info)

	data = info.(*search.Response)
	assert.GreaterOrEqual(len(data.Result.Items), 1)
	type ItemDetail struct {
		IndexName string `json:"index_name"`
		PayType   string `json:"pay_type"`
	}

	var detail ItemDetail
	err = json.Unmarshal(data.Result.Items[0], &detail)
	assert.NoError(err)
	assert.Equal("uat_m_sound", detail.IndexName)

	params.PayType = []int{sound.PayByDrama}
	params.PageSize = 5
	reqBody, _ = json.Marshal(params)
	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	assert.NotNil(info)
	data = info.(*search.Response)
	assert.GreaterOrEqual(len(data.Result.Items), 1)
	for _, item := range data.Result.Items {
		var tmp ItemDetail
		assert.NoError(json.Unmarshal(item, &tmp))
		payType, err := strconv.Atoi(tmp.PayType)
		assert.NoError(err)
		assert.Equal(sound.PayByDrama, payType)
	}

	// 测试搜索条件为只有 emoji 和空格的时候，返回空结果
	params = search.Params{
		Keyword:    "♪ ❤",
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeLive,
	}
	reqBody, _ = json.Marshal(params)

	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	assert.NotNil(info)

	// 测试关键词为屏蔽词时返回空结果
	params.Keyword = "ASMR"
	reqBody, _ = json.Marshal(params)
	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	result := info.(*search.Response)
	assert.Equal("OK", result.Status)
	assert.Empty(result.Result.Items)

	// 测试 drama 无 koc 投放
	params = search.Params{
		Keyword:    "1999",
		Sort:       0,
		Sensitive:  0,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeDrama,
	}
	reqBody, _ = json.Marshal(params)

	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	type dramaDetail struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}
	result = info.(*search.Response)
	for _, item := range result.Result.Items {
		var tmpDrama dramaDetail
		assert.NoError(json.Unmarshal(item, &tmpDrama))
		_, err := strconv.Atoi(tmpDrama.ID)
		assert.NoError(err)
	}

	// 测试 drama 投放关键词承接
	params = search.Params{
		Keyword:    "貌合神离",
		Sort:       0,
		UserID:     346286,
		Sensitive:  0,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeDrama,
	}
	reqBody, _ = json.Marshal(params)

	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	result = info.(*search.Response)
	var dramaNormalDetail dramaDetail
	require.NoError(json.Unmarshal(result.Result.Items[0], &dramaNormalDetail))
	dramaID, err := strconv.Atoi(dramaNormalDetail.ID)
	require.NoError(err)
	assert.Equal(1386, dramaID)
	assert.Equal("现世异闻1-3", dramaNormalDetail.Name)

	// 测试 drama 投放关键词承接-数字类型
	params = search.Params{
		Keyword:    "1978",
		Sort:       0,
		UserID:     346286,
		Sensitive:  0,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeDrama,
	}
	reqBody, _ = json.Marshal(params)

	c = handler.NewRPCTestContext("/rpc/discovery/search", bytes.NewBuffer(reqBody))
	info, err = ActionSearch(c)
	require.NoError(err)
	result = info.(*search.Response)
	var tmpDramaNumber dramaDetail
	require.NoError(json.Unmarshal(result.Result.Items[0], &tmpDramaNumber))
	dramaID, err = strconv.Atoi(tmpDramaNumber.ID)
	require.NoError(err)
	assert.Equal(1239, dramaID)
	assert.Equal("不测的恋情", tmpDramaNumber.Name)
}

func TestIsRestrictedSearch(t *testing.T) {
	assert := assert.New(t)

	params := search.Params{
		Keyword: "",
	}
	ok := isRestrictedSearch(params, "")
	assert.True(ok)

	params.Keyword = "A"
	params.SearchType = search.TypeDanmaku
	ok = isRestrictedSearch(params, "")
	assert.False(ok)

	params.SearchType = search.TypeSound
	params.Keyword = allowedSearchKeywordTestingList[0].(string)
	ok = isRestrictedSearch(params, "")
	assert.False(ok)

	params.Keyword = searchForbiddenWordsTestingList[0].(string)
	ok = isRestrictedSearch(params, "")
	assert.True(ok)
}

func TestSearchDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := search.Params{
		Keyword:    "貌合神离",
		Scenario:   search.ScenarioBackendSearch,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeDrama,
		AppName:    service.OpenSearch.Apps.Drama.Name,
		AppParam:   service.OpenSearch.Apps.Drama,
	}
	response0, err := searchDrama(params)
	require.NoError(err)
	require.NotNil(response0)

	// 测试有置顶且有固定剧集的场景
	params.Scenario = search.ScenarioMainSearch
	response1, err := searchDrama(params)
	require.NoError(err)
	require.NotNil(response1)
	require.Greater(len(response1.Result.Items), 4)

	// 测试中间插入
	var insertedDrama dramaSearch
	err = json.Unmarshal(response1.Result.Items[1], &insertedDrama)
	require.NoError(err)
	dramaInfoMap, err := findDramaMapByIDs([]int64{1, 2, 5, 10623})
	require.NoError(err)
	dramaInfo1, ok := dramaInfoMap[1]
	require.True(ok)
	assert.Equal(dramaInfo1.ID, insertedDrama.ID)

	// 测试末尾插入
	err = json.Unmarshal(response1.Result.Items[len(response1.Result.Items)-1], &insertedDrama)
	require.NoError(err)
	dramaInfo2, ok := dramaInfoMap[2]
	require.True(ok)
	assert.Equal(dramaInfo2.ID, insertedDrama.ID)

	// 测试 response1.Result 其他字段
	require.Equal(response0.Result.Num+3, response1.Result.Num)
	require.Equal(response0.Result.Total+3, response1.Result.Total)
	require.Equal(response0.Result.ViewTotal+3, response1.Result.ViewTotal)

	// 测试第二页
	params = search.Params{
		Keyword:    "貌合神离",
		Scenario:   search.ScenarioMainSearch,
		Page:       2,
		PageSize:   3,
		SearchType: search.TypeDrama,
		AppName:    service.OpenSearch.Apps.Drama.Name,
		AppParam:   service.OpenSearch.Apps.Drama,
	}
	response2, err := searchDrama(params)
	require.NoError(err)
	require.NotNil(response2)
	require.Len(response2.Result.Items, 3)
	type dramaDetail struct {
		ID string `json:"id"`
	}
	var response1Index3Drama dramaDetail
	err = json.Unmarshal(response1.Result.Items[3], &response1Index3Drama)
	require.NoError(err)
	var response2Index0Drama dramaDetail
	err = json.Unmarshal(response2.Result.Items[0], &response2Index0Drama)
	require.NoError(err)
	assert.Equal(response1Index3Drama.ID, response2Index0Drama.ID)
	err = json.Unmarshal(response2.Result.Items[1], &insertedDrama)
	require.NoError(err)
	dramaInfo5, ok := dramaInfoMap[5]
	require.True(ok)
	assert.Equal(dramaInfo5.ID, insertedDrama.ID)

	// 测试剧集后移到第二页，第一页不再展示挪动的剧集
	params = search.Params{
		Keyword:    "测试",
		Scenario:   search.ScenarioMainSearch,
		Page:       2,
		PageSize:   2,
		SearchType: search.TypeDrama,
		AppName:    service.OpenSearch.Apps.Drama.Name,
		AppParam:   service.OpenSearch.Apps.Drama,
	}
	response3, err := searchDrama(params)
	require.NoError(err)
	require.NotNil(response3)
	require.Greater(len(response3.Result.Items), 0)
	err = json.Unmarshal(response3.Result.Items[0], &insertedDrama)
	require.NoError(err)
	dramaInfo10623, ok := dramaInfoMap[10623]
	require.True(ok)
	assert.Equal(dramaInfo10623.ID, insertedDrama.ID)

	params = search.Params{
		Keyword:    "测试",
		Scenario:   search.ScenarioMainSearch,
		Page:       1,
		PageSize:   2,
		SearchType: search.TypeDrama,
		AppName:    service.OpenSearch.Apps.Drama.Name,
		AppParam:   service.OpenSearch.Apps.Drama,
	}
	response4, err := searchDrama(params)
	require.NoError(err)
	require.NotNil(response4)
	require.Greater(len(response4.Result.Items), 0)
	var firstPageDramaDetail dramaDetail
	for _, item := range response4.Result.Items {
		err = json.Unmarshal(item, &firstPageDramaDetail)
		require.NoError(err)
		assert.NotEqual(insertedDrama.ID, firstPageDramaDetail.ID)
	}
}

func TestSearchSpecial(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params1 := search.Params{
		Keyword:    "测试特型（勿删）",
		Page:       1,
		PageSize:   3,
		SearchType: search.TypeUser,
		AppName:    service.OpenSearch.Apps.Special.Name,
		AppParam:   service.OpenSearch.Apps.Special,
	}
	resp, err := searchSpecial(params1)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)
	type item struct {
		ID string `json:"id"`
	}
	var firstPage item
	err = json.Unmarshal(resp.Result.Items[0], &firstPage)
	require.NoError(err)
	firstPageFirstID, err := strconv.Atoi(firstPage.ID)
	require.NoError(err)

	// 测试第二页
	params2 := search.Params{
		Keyword:    "测试特型（勿删）",
		Page:       2,
		PageSize:   5,
		SearchType: search.TypeUser,
		AppName:    service.OpenSearch.Apps.Special.Name,
		AppParam:   service.OpenSearch.Apps.Special,
	}
	resp, err = searchSpecial(params2)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)
	var secondPage item
	err = json.Unmarshal(resp.Result.Items[0], &secondPage)
	require.NoError(err)
	secondPageFirstID, err := strconv.Atoi(secondPage.ID)
	require.NoError(err)
	assert.Equal(firstPageFirstID-params1.PageSize, secondPageFirstID)
}

func TestSearchSpecialTopicCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := search.Params{
		Keyword:    "测试123",
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeSpecialTopicCard,
		AppName:    service.OpenSearch.Apps.SpecialTopicCard.Name,
		AppParam:   service.OpenSearch.Apps.SpecialTopicCard,
	}
	resp, err := searchSpecialTopicCard(params)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)
	type searchItem struct {
		StartTime string `json:"start_time"`
	}
	var item searchItem
	err = json.Unmarshal(resp.Result.Items[0], &item)
	require.NoError(err)
	startTime, err := strconv.Atoi(item.StartTime)
	require.NoError(err)
	assert.True(util.TimeNow().Unix() >= int64(startTime))
}

func TestSearchFilterDeletedUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := search.Params{
		Keyword:    "注销",
		UserID:     346286,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeUser,
		AppName:    service.OpenSearch.Apps.User.Name,
		AppParam:   service.OpenSearch.Apps.User,
	}
	resp, err := searchUser(params)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)
	// TODO: 需要断言 params.Filter 中的信息
	for _, v := range resp.Result.Items {
		type userItem struct {
			UserID  string `json:"id"`
			Confirm string `json:"confirm"`
		}
		var item userItem
		err := json.Unmarshal(v, &item)
		require.NoError(err)
		require.NotEmpty(item.UserID)
		confirm, err := strconv.Atoi(item.Confirm)
		require.NoError(err)
		assert.NotEqual(confirmDeleted, uint(confirm)&confirmDeleted)
	}

	sortName, ok := service.OpenSearch.Apps.User.SortName(0)
	require.True(ok)
	params = search.Params{
		Keyword:        "注销",
		UserID:         346286,
		Page:           1,
		PageSize:       20,
		SearchType:     search.TypeUser,
		AppName:        service.OpenSearch.Apps.User.Name,
		AppParam:       service.OpenSearch.Apps.User,
		SecondRankName: sortName,
	}
	resp, err = service.OpenSearch.Search(params)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)

	resultsCount := 0
	for _, v := range resp.Result.Items {
		type userItem struct {
			UserID  string `json:"id"`
			Confirm string `json:"confirm"`
		}
		var item userItem
		err := json.Unmarshal(v, &item)
		require.NoError(err)
		require.NotEmpty(item.UserID)
		confirm, err := strconv.Atoi(item.Confirm)
		require.NoError(err)
		if uint(confirm)&confirmDeleted == confirmDeleted {
			resultsCount++
		}
	}
	assert.NotZero(resultsCount)
}

func TestSearchLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := search.Params{
		Keyword:    "注销",
		UserID:     346286,
		Page:       1,
		PageSize:   20,
		SearchType: search.TypeLive,
		AppName:    service.OpenSearch.Apps.Live.Name,
		AppParam:   service.OpenSearch.Apps.Live,
	}
	resp, err := searchLive(params)
	require.NoError(err)
	require.NotEmpty(resp.Result.Items)
	deletedUserStatus := -1
	for _, v := range resp.Result.Items {
		type userItem struct {
			UserID string `json:"creator_id"`
			Status string `json:"status"`
		}
		var item userItem
		err := json.Unmarshal(v, &item)
		require.NoError(err)
		require.NotEmpty(item.UserID)
		status, err := strconv.Atoi(item.Status)
		require.NoError(err)
		// 测试 OpenSearch 过滤条件 status >= 0
		assert.Less(deletedUserStatus, status)
	}
}

func TestHasForbiddenWords(t *testing.T) {
	assert := assert.New(t)

	params := search.Params{
		Keyword: "火锅",
		UserID:  346286,
	}
	ip := "127.0.0.1"
	assert.False(hasForbiddenWords(ip, params))

	for _, v := range searchForbiddenWordsTestingList {
		params.Keyword = v.(string)
		assert.True(hasForbiddenWords(ip, params))
	}
}

func TestIsDramaKeyword(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := forbiddenwords.RedisKey(forbiddenwords.ForbiddenWordTypeDrama)
	err := service.Redis.SAdd(key, "自然音").Err()
	require.NoError(err)

	resp := isDramaKeywordPass("自然音")
	assert.False(resp)
}
