package discovery

import (
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/util"
)

// dramaSearch 开放搜索 drama 实例默认显示字段
type dramaSearch struct {
	ID         int64  `gorm:"column:id" json:"id"`                   // 剧集 ID
	Name       string `gorm:"column:name" json:"name"`               // 剧集名称
	Cover      string `gorm:"column:cover" json:"cover"`             // 剧集海报
	Abstract   string `gorm:"column:abstract" json:"abstract"`       // 剧集简介
	Integrity  int    `gorm:"column:integrity" json:"integrity"`     // 完结度
	Author     string `gorm:"column:author" json:"author"`           // 原作者
	Type       int    `gorm:"column:type" json:"type"`               // 分类
	Newest     string `gorm:"column:newest" json:"newest"`           // 更新至
	UserName   string `gorm:"column:username" json:"username"`       // 所属用户名
	Checked    int    `gorm:"column:checked" json:"checked"`         // 审核状态（0 为未审核，1 为审核通过，2 为审核未通过，3 报警，4 合约到期下架）
	Catalog    int64  `gorm:"column:catalog" json:"catalog"`         // 分类 ID
	Alias      string `gorm:"column:alias" json:"alias"`             // 别名
	PayType    int    `gorm:"column:pay_type" json:"pay_type"`       // 付费类型（0 免费，1 单音付费，2 剧集付费）
	Police     int    `gorm:"column:police" json:"police"`           // 是否报警
	CoverColor int    `gorm:"column:cover_color" json:"cover_color"` // RGB 颜色值
	ViewCount  int64  `gorm:"column:view_count" json:"view_count"`   // 查看次数

	IsInsert int `gorm:"-" json:"is_insert,omitempty"` // 是否被人工干预
}

// findDramaMapByIDs 根据剧集 IDs 查询剧集
func findDramaMapByIDs(ids []int64) (map[int64]dramaSearch, error) {
	uniqIDs := util.Uniq(ids)
	findLength := len(uniqIDs)
	if findLength == 0 {
		return map[int64]dramaSearch{}, nil
	}

	dramaSearches := make([]dramaSearch, 0, findLength)
	err := dramainfo.RadioDramaDramainfo{}.DB().
		Select("id, name, cover, abstract, integrity, author, type, newest, "+
			"username, checked, catalog, alias, pay_type, police, cover_color, view_count").
		Where("id IN (?)", uniqIDs).
		// 获取过审并且不报警的剧集
		Where("checked = ? AND police = 0", dramainfo.CheckedPass).
		Find(&dramaSearches).Error
	if err != nil {
		return nil, err
	}

	dramaMap := util.ToMap(dramaSearches, "ID").(map[int64]dramaSearch)

	searchedLength := len(dramaSearches)
	if searchedLength != findLength {
		diffIDs := make([]int64, 0, findLength-searchedLength)
		for _, v := range uniqIDs {
			if _, ok := dramaMap[v]; ok {
				continue
			}
			diffIDs = append(diffIDs, v)
		}
		logger.Errorf("剧集 IDs: %v 不存在或相关属性不符合要求", diffIDs)
	}
	return dramaMap, nil
}
