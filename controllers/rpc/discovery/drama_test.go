package discovery

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestDramaSearchTags(t *testing.T) {
	kc := tutil.New<PERSON>eyChecker(t, tutil.JSON)
	kc.Check(dramaSearch{}, "id", "name", "cover", "abstract", "integrity", "author", "type", "newest",
		"username", "checked", "catalog", "alias", "pay_type", "police", "cover_color", "view_count", "is_insert")

	kc = tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(dramaSearch{}, "id", "name", "cover", "abstract", "integrity", "author", "type", "newest",
		"username", "checked", "catalog", "alias", "pay_type", "police", "cover_color", "view_count")
}

func TestFindDramaMapByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaMap, err := findDramaMapByIDs([]int64{})
	require.NoError(err)
	require.NotNil(dramaMap)
	assert.Len(dramaMap, 0)

	dramaMap, err = findDramaMapByIDs([]int64{999})
	require.NoError(err)
	require.NotNil(dramaMap)
	assert.Len(dramaMap, 0)

	dramaMap, err = findDramaMapByIDs([]int64{1})
	require.NoError(err)
	require.NotNil(dramaMap)
	dramaInfo1, ok := dramaMap[1]
	require.True(ok)
	assert.NotEmpty(dramaInfo1.Name)

	dramaMap, err = findDramaMapByIDs([]int64{1, 2, 3, 3, 9999, 9999})
	require.NoError(err)
	require.NotNil(dramaMap)
	assert.Len(dramaMap, 2)
}
