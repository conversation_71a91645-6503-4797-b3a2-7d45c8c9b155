package discovery

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/discovery"
	"github.com/MiaoSiLa/missevan-go/models/discovery/card"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/live"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 关注类型
const (
	followTypeUnfollow = iota
	followTypeFollow
)

type searchCardParams struct {
	Keyword string `json:"keyword"`
	UserID  int64  `json:"user_id"`
	Page    int    `json:"page"`
	// OS: 设备类型
	// TODO: 根据 os 返回业务需要的数据
	OS util.Platform `json:"os"`

	pageSize    int
	searchParam *search.Params
}

type searchCardResp struct {
	SpecialCard *card.SpecialCard `json:"special_card,omitempty"`
	UpCard      *card.UpCard      `json:"up_card,omitempty"`
	GameCard    *card.GameCard    `json:"game_card,omitempty"`
}

// ActionSearchCard 搜索卡片
/**
 * @api {post} /rpc/discovery/search-card 搜索卡片
 * @apiVersion 0.1.0
 * @apiName search-card
 * @apiGroup rpc
 *
 * @apiParam {String} keyword 搜索关键词
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} page 第几页
 * @apiParam {number=1,2,6} [os] 客户端设备类型，1: 安卓；2: iOS；6: HarmonyOS
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       code: 0,
 *       info: {
 *         "special_card": { // 无数据命中或搜索出错不返回此字段
 *           "data": [
 *             {
 *               "id": 22,
 *               "url": "missevan://omikuji/draw?work_id=2",
 *               "cover": "http://static-test.maoercdn.com/search/covers/202205/27/daa14798f83fc4a2a165531.jpg"
 *             }
 *           ],
 *           "pagination": {
 *             "p": 1,
 *             "maxpage": 2,
 *             "count": 5,
 *             "pagesize": 3
 *           },
 *           "ops_request_misc": "%7B%22request%5Fid%22%3A%22167307504116798137821052%22%2C%22scm%22%3A%2220140713.110058138..%22%7D"
 *         },
 *         "up_card": { // 非第一页、无数据命中、搜索出错不返回此字段
 *           "id": 1,
 *           "username": "InVinCiblezzz",
 *           "iconurl": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
 *           "authenticated": 0, // 加 V 认证标识 1：黑 V，2：金 V，3：蓝 V
 *           "fans_num": 11,
 *           "work_summary": {
 *             "type": 1, // 1：声音，2：剧集作品
 *             "num": 10 // 作品数量
 *           },
 *           "intro": "知名主播 ｜ 配音演员", // UP 主的简短介绍（非 HTML）
 *           "followed": 1, // 0：未关注，1：已关注，游客访问、UP 主本人查看不返回此字段
 *           "work_list": [ // 作品列表，若可展示内容不足三个，返回空数组
 *             {
 *               "type": 5, // 动态类型（2：剧集，3：音频，5：直播动态）
 *               "room_id": 100000,
 *               "title": "直播间标题",
 *               "cover_url": "https://static-test.maoercdn.com/icon01.png",
 *               "tag": "直播",
 *               "tag_color": "#FF0000"
 *             },
 *             {
 *               "type": 2, // 剧集
 *               "drama_id": 13259,
 *               "name": "哈佛优等生最欣赏的200个人生故事",
 *               "pay_type": 2, // 付费类型（0：免费 1：单音付费 2：剧集付费）
 *               "username": "UP 主",
 *               "view_count": 123,
 *               "cover": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
 *               "tag": "热剧",
 *               "tag_color": "#FFA500"
 *             },
 *             {
 *               "type": 3, // 音频
 *               "sound_id": 83185,
 *               "soundstr": "小黄人版iphone铃声",
 *               "duration": 28447,
 *               "view_count": 46409,
 *               "front_cover": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
 *               "video": true,
 *               "tag": "音频",
 *               "tag_color": "#FF0000"
 *             }
 *           ]
 *         },
 *         "game_card": { // 非第一页、无数据命中或搜索出错不返回此字段
 *           "id": 2, // 游戏 ID
 *           "url": "http://test.com/mevent/102718", // 点击卡片跳转链接
 *           "cover": "http://static-test.maoercdn.com/game/images/cover.jpg", // 日间模式下卡片背景图
 *           "dark_cover": "http://static-test.maoercdn.com/game/images/dark_cover.jpg", // 黑夜模式下卡片背景图
 *           "icon": "https://static-test.missevan.com/game/images/icon.jpg", // 游戏图标
 *           "btn_color": "#000000", // 日间模式下按钮颜色
 *           "dark_btn_color": "#FFFFFF", // 黑夜模式下按钮颜色
 *           "name": "游戏 2", // 游戏名
 *           "tag": "二次元,养成", // 游戏标签，按半角逗号分隔
 *           "intro": "简介 2", // 简介
 *           "status": 1, // 状态，1：未预约；2：已预约；3：开放下载
 *           "show_on_os": 1, // 客户端展示，比特位第一位为 1 时表示展示在 Android 客户端上，第二位为 1 时表示展示在 iOS 客户端上
 *           "download_url": "https://www.test.com/x/gamecenter/download?game_id=2", // 游戏安卓客户端下载路径
 *           "package_name": "com.missevan.app", // 游戏安卓安装包
 *           "package_version_code": 1 // 游戏安卓安装包版本号
 *         }
 *       }
 *     }
 */
func ActionSearchCard(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newSearchCardParams(c)
	if err != nil {
		return nil, err
	}
	// WORKAROUND: 如果搜索词超过最大长度限制，卡片搜索属于精准匹配所以不会匹配到结果，直接返回空卡片
	if len(p.Keyword) > searchWordMaxLength {
		return &searchCardResp{}, nil
	}
	// TODO: 增加搜索专题卡逻辑
	return &searchCardResp{
		SpecialCard: p.searchSpecialCard(),
		UpCard:      p.searchUpCard(),
		GameCard:    p.searchGameCard(),
	}, nil
}

func newSearchCardParams(c *handler.Context) (*searchCardParams, error) {
	params := new(searchCardParams)
	err := c.BindJSON(params)
	if err != nil || params.Keyword == "" || params.UserID < 0 ||
		params.Page <= 0 {
		return nil, handler.ErrInvalidParam
	}
	// pageSize 只作用在搜索特型上，第一页和后续页的 pageSize 不同
	// 文档地址：https://github.com/MiaoSiLa/requirements-doc/blob/master/2019-02-15%20搜索特型%20App%20及后台/README.md
	params.pageSize = 3
	if params.Page > 1 {
		params.pageSize += search.SpecialFirstAndAfterPageSizeDiff
	}
	params.searchParam = &search.Params{
		Keyword:  params.Keyword,
		UserID:   params.UserID,
		Page:     params.Page,
		PageSize: params.pageSize,
	}
	return params, nil
}

// searchSpecialCard 搜索特型
func (p *searchCardParams) searchSpecialCard() *card.SpecialCard {
	result, err := searchCard(*p.searchParam, search.TypeSpecial, search.SpecialDefault)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	count := len(result.Result.Items)
	if count == 0 {
		return nil
	}
	data := make([]card.SpecialCardItem, 0, count)
	for _, v := range result.Result.Items {
		var s card.SpecialCardItem
		err = json.Unmarshal(v, &s)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil
		}
		s.ID, err = strconv.ParseInt(s.ID.(string), 10, 64)
		if err != nil {
			logger.Error(err)
			continue
		}
		if s.Cover == "" {
			s.Cover = service.Storage.Parse(params.URL.DefaultCoverURL)
		} else {
			s.Cover = service.Storage.Parse(s.Cover)
		}
		data = append(data, s)
	}
	var pagination util.Pagination
	// 第一页的 pageSize 总是比后续页小，故计算第一页的 maxPage 时需要按后续页的 pageSize 来计算
	if p.Page == 1 {
		pagination = util.MakePagination(int64(result.Result.ViewTotal+search.SpecialFirstAndAfterPageSizeDiff),
			int64(p.Page), int64(p.pageSize+search.SpecialFirstAndAfterPageSizeDiff))
		pagination.PageSize = int64(p.pageSize)
	} else {
		pagination = util.MakePagination(int64(result.Result.ViewTotal+search.SpecialFirstAndAfterPageSizeDiff),
			int64(p.Page), int64(p.pageSize))
	}
	// 订正 count
	pagination.Count -= int64(search.SpecialFirstAndAfterPageSizeDiff)
	return &card.SpecialCard{
		Data:           data,
		Pagination:     pagination,
		OpsRequestMisc: result.OpsRequestMisc,
	}
}

// searchUpCard 搜索 UP 主卡片
func (p *searchCardParams) searchUpCard() *card.UpCard {
	// 仅第一页需要返回 UP 主卡片
	if p.Page != 1 {
		return nil
	}
	result, err := searchCard(*p.searchParam, search.TypeSpecialTopicCard, search.SpecialUpCard)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if len(result.Result.Items) == 0 {
		return nil
	}
	var ssi discovery.SpecialSearchItemResult
	err = json.Unmarshal(result.Result.Items[0], &ssi)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	var ssiUpCardMore discovery.UPCardMore
	err = json.Unmarshal([]byte(ssi.More), &ssiUpCardMore)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	upCard := &card.UpCard{
		Intro: ssiUpCardMore.Intro,
	}

	u, err := user.FindByUserID(ssiUpCardMore.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if u == nil {
		return nil
	}
	upCard.ID = u.ID
	upCard.UserName = u.UserName
	upCard.IconURL = u.IconURL
	upCard.Authenticated = u.Authenticated
	upCard.FansNum = u.FansNum

	workSummary := new(card.WorkSummary)
	var cvIDs []int64
	// 当「展示剧集作品」时，需要获取剧集作品的数量，否则需要获取音频的数量
	if ssiUpCardMore.IsShowDrama == card.ShowTypeEnable {
		workSummary.Type = card.WorkSummaryTypeDrama
		cvIDs, err = models.ListSeiyIDsByUserID(ssiUpCardMore.UserID)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil
		}
		workSummary.Num, err = dramainfo.CountUserDrama(ssiUpCardMore.UserID, cvIDs)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil
		}
	} else {
		workSummary.Type = card.WorkSummaryTypeSound
		workSummary.Num, err = sound.CountUserSound(ssiUpCardMore.UserID, []int{sound.TypeNormal, sound.TypeMusic, sound.TypeInteractive}, sound.RefinedBlock)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil
		}
	}
	upCard.WorkSummary = workSummary

	if p.UserID != 0 && p.UserID != ssiUpCardMore.UserID {
		followed, err := person.HasFollowed(p.UserID, ssiUpCardMore.UserID)
		if err != nil {
			logger.Error(err)
			// PASS
			return nil
		}
		if followed {
			upCard.Followed = util.NewInt(followTypeFollow)
		} else {
			upCard.Followed = util.NewInt(followTypeUnfollow)
		}
	}

	upCard.WorkList, err = getUpCardWorks(&ssiUpCardMore, cvIDs)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	return upCard
}

func (p *searchCardParams) searchGameCard() *card.GameCard {
	// 仅第一页需要返回游戏预约卡片
	if p.Page != 1 {
		return nil
	}
	// 游戏预约卡片搜索时复用专题卡配置
	result, err := searchCard(*p.searchParam, search.TypeSpecialTopicCard, search.SpecialGameCard)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if len(result.Result.Items) == 0 {
		return nil
	}
	var ssi discovery.SpecialSearchItemResult
	// 目前一个关键词配置一个游戏预约卡片
	err = json.Unmarshal(result.Result.Items[0], &ssi)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	var ssiGameCardMore discovery.GameCardMore
	err = json.Unmarshal([]byte(ssi.More), &ssiGameCardMore)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	gameCard, err := card.GetGameCard(p.UserID, ssiGameCardMore.GameID, ssiGameCardMore.ShowOnOS)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	return gameCard
}

func searchCard(params search.Params, searchAppType int, specialType int) (*search.Response, error) {
	params.SearchType = searchAppType
	// 特型搜索第一页和后续页的 pageSize 有差异，后续页需要特殊处理取数时的 offset
	if specialType == search.SpecialDefault && params.Page > 1 {
		params.SkipOffset -= search.SpecialFirstAndAfterPageSizeDiff
	}
	// 一个关键词只会有一个 UP 主卡片配置
	if specialType == search.SpecialUpCard {
		params.PageSize = 1
	}
	if ok := setAppParam(&params); !ok {
		return nil, handler.ErrInvalidParam
	}
	if ok := setAppName(&params); !ok {
		return nil, handler.ErrInvalidParam
	}
	params.Filter = []string{"status = 1", fmt.Sprintf("type = %d", specialType)}

	var ok bool
	params.SecondRankName, ok = params.AppParam.SortName(0)
	if !ok {
		return nil, actionerrors.ErrServerInternal(errors.New("排序配置有误"), logger.Fields{"type": searchAppType})
	}
	return service.OpenSearch.Search(params)
}

func getUpCardWorks(m *discovery.UPCardMore, cvIDs []int64) ([]interface{}, error) {
	var result []interface{}
	// 获取用户开播信息
	live, err := live.FindOpenLiveByUserID(m.UserID)
	if err != nil {
		return nil, err
	}
	if live != nil {
		result = append(result, card.NewLiveWork(live))
	}
	// 需要「展示直播回放」并且直播间未开播或无直播间
	if m.IsShowLive == card.ShowTypeEnable && live == nil {
		// 最近 1 个直播回放（时间倒序）
		soundLives, err := sound.ListSoundByUserID(m.UserID, []int{sound.TypeLive}, 1)
		if err != nil {
			return nil, err
		}
		if len(soundLives) > 0 {
			result = append(result, card.NewSoundWorks([]sound.MSound{soundLives[0]}, card.TagLive)[0])
		}
	}
	// 需要「展示剧集作品」
	if m.IsShowDrama == card.ShowTypeEnable {
		// 获取参与度最高的剧
		hotDrama, err := findHotDrama(m.UserID, cvIDs)
		if err != nil {
			return nil, err
		}
		var hotDramaID int64
		if hotDrama != nil {
			result = append(result, card.NewDramaWork(hotDrama, card.TagHotDrama))
			hotDramaID = hotDrama.ID
		}
		latestDramaCountLimit := 2
		// 当也需要「展示直播回放」时只需要展示 1 个最新创建的剧集
		if m.IsShowLive == card.ShowTypeEnable {
			latestDramaCountLimit = 1
		}
		latestDramas, err := findLatestDramas(m.UserID, hotDramaID, cvIDs, latestDramaCountLimit)
		if err != nil {
			return nil, err
		}
		if len(latestDramas) > 0 {
			for _, v := range latestDramas {
				result = append(result, card.NewDramaWork(v, card.TagNewDrama))
			}
		}
	}
	// 内容数量不足 3 个时用最新投稿的非直播回放类音频稿件补位（时间倒序）
	if len(result) < 3 {
		sounds, err := sound.ListSoundByUserID(m.UserID, []int{sound.TypeNormal, sound.TypeMusic, sound.TypeInteractive}, 3-len(result))
		if err != nil {
			return nil, err
		}
		// 若可展示内容不足三个，则不返回 UP 主卡片作品信息
		if len(sounds)+len(result) < 3 {
			return []interface{}{}, nil
		}
		soundWorks := card.NewSoundWorks(sounds, card.TagSound)
		for _, v := range soundWorks {
			result = append(result, v)
		}
	}
	return result[0:3], err
}

// findHotDrama 自己创建 + 参与配音的剧中，获取参与度最高的剧，优先按照角色类型进行排序其次按照播放量倒序排序
func findHotDrama(userID int64, cvIDs []int64) (*dramainfo.RadioDramaDramainfo, error) {
	// 角色类型 1：主役 2：协役 3：龙套
	// 查询出来的角色类型 0：主役 1 创建 2：协役 3：龙套
	// 获取用户创建的播放量最高的剧（角色类型 main = 1）和用户参与配音（main IN (0, 2, 3)）的播放量最高的剧（main ASC, view_count DESC）
	// 按照主役 > 创建 > 协役 > 龙套的顺序返回播放量最高的一条剧集信息
	subQuery := fmt.Sprintf(`
SELECT t1.main, t1.id, t1.name, t1.cover, t1.username, t1.view_count, t1.pay_type FROM (
SELECT 1 AS main, id, name, cover, username, view_count, pay_type
FROM %s
WHERE checked = ? AND user_id = ? AND police = 0 AND NOT (refined & ?)
ORDER BY view_count DESC, id DESC LIMIT 1) AS t1
`, dramainfo.RadioDramaDramainfo{}.TableName())
	values := make([]interface{}, 3, 6)
	var refinedValue util.BitMask
	refinedValue.Set(dramainfo.RefinedSearchHidden)
	values[0], values[1], values[2] = dramainfo.CheckedPass, userID, refinedValue

	if len(cvIDs) != 0 {
		subQuery += fmt.Sprintf(`UNION ALL
SELECT t2.main, t2.id, t2.name, t2.cover, t2.username, t2.view_count, t2.pay_type FROM (
SELECT IF(a.main = 1, 0, a.main) main, b.id, b.name, b.cover, b.username, b.view_count, b.pay_type
FROM %s AS a
LEFT JOIN %s AS b ON a.drama_id = b.id
WHERE b.checked = ? AND b.police = 0 AND NOT (b.refined & ?) AND a.cv_id IN (?)
ORDER BY a.main ASC, b.view_count DESC, b.id DESC LIMIT 1) AS t2
`, dramaepisodecv.RadioDramaEpisodeCv{}.TableName(), dramainfo.RadioDramaDramainfo{}.TableName())
		values = append(values, []interface{}{dramainfo.CheckedPass, refinedValue, cvIDs}...)
	}

	sqlExpr := fmt.Sprintf("SELECT id, name, cover, username, view_count, pay_type FROM ( %s ) AS c", subQuery)
	var drama dramainfo.RadioDramaDramainfo
	err := service.DramaDB.
		Raw(sqlExpr, values...).
		Order("c.main ASC, c.view_count DESC, c.id DESC").Take(&drama).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &drama, nil
}

// findLatestDramas 自己创建 + 参与配音的剧中，最新创建的剧（时间倒序）
func findLatestDramas(userID, excludeDramaID int64, cvIDs []int64, limit int) ([]*dramainfo.RadioDramaDramainfo, error) {
	var createFilterIDSqlExpr string
	if excludeDramaID != 0 {
		createFilterIDSqlExpr = fmt.Sprintf("AND id <> %d", excludeDramaID)
	}
	subQuery := fmt.Sprintf(`
SELECT t1.id, t1.name, t1.cover, t1.username, t1.view_count, t1.pay_type, t1.create_time FROM (
SELECT id, name, cover, username, view_count, pay_type, create_time
FROM %s
WHERE checked = ? AND police = 0 AND NOT (refined & ?) AND user_id = ? %s
ORDER BY create_time DESC, id DESC LIMIT %d) AS t1
`, dramainfo.RadioDramaDramainfo{}.TableName(), createFilterIDSqlExpr, limit)
	values := make([]interface{}, 3, 6)
	var refinedValue util.BitMask
	refinedValue.Set(dramainfo.RefinedSearchHidden)
	values[0], values[1], values[2] = dramainfo.CheckedPass, refinedValue, userID

	if len(cvIDs) != 0 {
		var participateFilterIDSqlExpr string
		if excludeDramaID != 0 {
			participateFilterIDSqlExpr = fmt.Sprintf("AND b.id <> %d", excludeDramaID)
		}
		subQuery += fmt.Sprintf(`UNION
SELECT t2.id, t2.name, t2.cover, t2.username, t2.view_count, t2.pay_type, t2.create_time FROM (
SELECT b.id, b.name, b.cover, b.username, b.view_count, b.pay_type, b.create_time
FROM %s AS a
LEFT JOIN %s AS b ON a.drama_id = b.id
WHERE b.checked = ? AND b.police = 0 AND NOT (b.refined & ?) AND a.cv_id IN (?) %s
GROUP BY b.id
ORDER BY b.create_time DESC, b.id DESC LIMIT %d) AS t2
`, dramaepisodecv.RadioDramaEpisodeCv{}.TableName(), dramainfo.RadioDramaDramainfo{}.TableName(), participateFilterIDSqlExpr, limit)
		values = append(values, []interface{}{dramainfo.CheckedPass, refinedValue, cvIDs}...)
	}

	sqlExpr := fmt.Sprintf("SELECT id, name, cover, username, view_count, pay_type, create_time FROM ( %s ) AS c", subQuery)
	var dramas []*dramainfo.RadioDramaDramainfo
	err := service.DramaDB.
		Raw(sqlExpr, values...).
		Order("c.create_time DESC, id DESC").Limit(limit).Scan(&dramas).Error
	if err != nil {
		return nil, err
	}
	for _, drama := range dramas {
		if err = drama.AfterFind(); err != nil {
			return nil, err
		}
	}
	return dramas, nil
}
