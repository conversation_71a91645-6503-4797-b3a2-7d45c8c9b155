package drama

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/checkeddramareview"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(dramaCheckStatusParams{}, "drama_id")
	kc.Check(dramaCheckStatusResp{}, "checked", "reviewing")
}

func TestActionDramaCheckStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	params := handler.M{"drama_id": 0}
	c := handler.NewTestContext(http.MethodPost, "/rpc/drama/check-status", false, params)
	_, err := ActionDramaCheckStatus(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试剧集不存在
	params = handler.M{"drama_id": 10000000}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/check-status", false, params)
	_, err = ActionDramaCheckStatus(c)
	assert.Equal(actionerrors.ErrDramaNotFound, err)

	// 测试剧集不是再审状态
	params = handler.M{"drama_id": 4}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/check-status", false, params)
	resp, err := ActionDramaCheckStatus(c)
	require.NoError(err)
	info := resp.(dramaCheckStatusResp)
	assert.Equal(dramainfo.CheckedPending, info.Checked)
	assert.False(info.Reviewing)

	// 测试剧集是再审状态
	params = handler.M{"drama_id": 5}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/check-status", false, params)
	resp, err = ActionDramaCheckStatus(c)
	require.NoError(err)
	info = resp.(dramaCheckStatusResp)
	assert.True(info.Reviewing)

	// 测试剧集无再审记录的情况
	require.NoError(checkeddramareview.CheckedDramaReview{}.DB().
		Delete(checkeddramareview.CheckedDramaReview{}, "drama_id = ?", 5).Error)
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/check-status", false, params)
	resp, err = ActionDramaCheckStatus(c)
	require.NoError(err)
	info = resp.(dramaCheckStatusResp)
	assert.False(info.Reviewing)
}
