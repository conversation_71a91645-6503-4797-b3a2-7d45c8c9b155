package drama

import (
	"sort"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/checkeddramareview"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaipr"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscribeplaylog"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscription"
	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

type reqGetDramas struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"page_size"`
	Params   struct {
		Name   string  `json:"name"`
		Author string  `json:"author"`
		ID     []int64 `json:"id"`
		UserID []int64 `json:"user_id"`
	} `json:"params"`
}

type getDramaEpisodesParams struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
	UserID  int64 `json:"user_id"`  // 用户 ID

	dramaInfo          dramainfo.RadioDramaDramainfo
	dramaEpisodes      []dramaepisode.RadioDramaEpisode
	checkedDramaReview checkeddramareview.CheckedDramaReview // 待审核的剧集信息
	sounds             []msound.MSound
	like               *bool
}

type getDramaEpisodesResp struct {
	Drama     dramainfo.RadioDramaDramainfo `json:"drama"`
	Episodes  episodesResp                  `json:"episodes"`
	Like      *bool                         `json:"like,omitempty"`
	ViewCount int64                         `json:"view_count"`
}

type episodesResp struct {
	Ft      []dramaepisode.RadioDramaEpisode `json:"ft"`
	Music   []dramaepisode.RadioDramaEpisode `json:"music"`
	Episode []dramaepisode.RadioDramaEpisode `json:"episode"`
}

// ActionGetDramas 根据自定义查询条件或剧集信息
/**
 * @api {post} /rpc/drama/get-dramas 根据自定义查询条件或剧集信息
 * @apiVersion 0.1.0
 * @apiGroup rpc/drama
 *
 * @apiParam {Number} page 页数
 * @apiParam {Number} page_size 每页显示条数
 * @apiParam {Object} params 查询的条件参数
 * @apiParam {String} [params.name] 剧集名称
 * @apiParam {String} [params.author] 原作者
 * @apiParam {Number[]} [params.id] 剧集 ID
 * @apiParam {Number[]} [params.user_id] 所属用户 ID
 *
 * @apiSuccess {Number} code 请求是否成功
 * @apiSuccess {Object} info 请求后的结果信息
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "count": 1550,
 *         "dramas": [
 *           {
 *             "id": 465,
 *             "name": "三生三世枕上书",
 *             "cover": "http://...d3928f9115847.jpeg",
 *             "author": "小明",
 *             ...
 *           },
 *           {
 *             "id": 466,
 *             "name": "三生三世",
 *             "cover": "http://...d3928f9115847.jpeg",
 *             "author": "小明",
 *             ...
 *           }
 *         ]
 *       }
 *     }
 */
func ActionGetDramas(c *handler.Context) (handler.ActionResponse, error) {
	var req reqGetDramas
	err := c.BindJSON(&req)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	valid := false
	db := service.DramaDB.Select("id, name, author, cover, user_id, create_time").
		Table(dramainfo.RadioDramaDramainfo{}.TableName())
	if req.Params.Name != "" {
		db = db.Where("name LIKE ?", servicedb.ToLikeStr(req.Params.Name))
		valid = true
	}
	if req.Params.Author != "" {
		db = db.Where("author LIKE ?", servicedb.ToLikeStr(req.Params.Author))
		valid = true
	}
	if len(req.Params.ID) > 0 {
		db = db.Where("id IN (?)", req.Params.ID)
		valid = true
	}
	if len(req.Params.UserID) > 0 {
		db = db.Where("user_id IN (?)", req.Params.UserID)
		valid = true
	}

	if !valid {
		return nil, actionerrors.ErrParams
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	dramas := make([]dramainfo.RadioDramaDramainfo, 0)
	pagination := util.MakePagination(count, req.Page, req.PageSize)
	if pagination.Valid() {
		err = pagination.ApplyTo(db).Find(&dramas).Error
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}

	ret := make([]struct {
		ID         int64   `json:"id"`          // 剧集 ID
		Name       *string `json:"name"`        // 剧集名称
		Cover      string  `json:"cover"`       // 剧集海报的全路径
		Author     *string `json:"author"`      // 原作者
		UserID     int64   `json:"user_id"`     // 所属用户 ID
		CreateTime int64   `json:"create_time"` // 发布时间
	}, len(dramas))
	for i, v := range dramas {
		ret[i].ID = v.ID
		ret[i].Name = v.Name
		ret[i].Cover = v.CoverURL
		ret[i].Author = v.Author
		ret[i].UserID = v.UserID
		ret[i].CreateTime = v.CreateTime
	}
	return handler.M{
		"count":  count,
		"dramas": ret,
	}, nil
}

// ActionMustGetDramas 检查剧集数据
/**
 * @api {post} /rpc/drama/must-get-dramas 检查剧集数据
 * @apiDescription 若剧集数据存在则返回数据否则返回空
 * @apiVersion 0.1.0
 * @apiName must-get-dramas
 * @apiGroup rpc/drama
 *
 * @apiParam {Number[]} drama_id 剧集 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object[]} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": [
 *         {
 *           "id": 27,
 *           "name": "冲撞",
 *           "cover": "http://static.missevan.com/dramacoversmini/201807/11/ba854fe2e84d114247.png",
 *           "abstract": "成叶集团的董事长为了收服他所钦定的接班人",
 *           "integrity": "囧囧有神",
 *           "age": 1,
 *           "origin": 1,
 *           "author": "晓春",
 *           "create_time": 1460702276,
 *           "lastupdate_time": 1505751766,
 *           "view_count": 1241,
 *           "ip": 0,
 *           "ipname": null,
 *           "type": 4,
 *           "newest": "第三期",
 *           "organization_id": 0,
 *           "user_id": 77090,
 *           "username": "审核_暗切线",
 *           "checked": 1,
 *           "create_time": 1460702276,
 *           "lastupdate_time": 1505751766,
 *           "view_count": 1241,
 *           "catalog": 89,
 *           "alias": null,
 *           "pay_type": 2,
 *           "push": 0,
 *           "refined": 0,
 *           "type_name": "耽美",
 *           "need_pay": null,
 *           "purchased": null,
 *           "price": 3399,
 *           "catalog_name": "中文广播剧"
 *         }
 *       ]
 *     }
 */
func ActionMustGetDramas(c *handler.Context) (handler.ActionResponse, error) {
	var req struct {
		IDs []int64 `json:"drama_id"`
	}
	err := c.BindJSON(&req)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(req.IDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	dramas := make([]dramainfo.RadioDramaDramainfo, 0)
	err = service.DramaDB.Where("id IN (?)", req.IDs).Find(&dramas).Error
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(dramas) != len(req.IDs) {
		return nil, nil
	}
	return dramas, nil
}

// ActionGetDramaEpisodes 获取剧集和单集信息
/**
 * @api {post} /rpc/drama/get-drama-episodes 获取剧集和单集信息
 *
 * @apiVersion 0.1.0
 * @apiName get-drama-episodes
 * @apiGroup /rpc/drama/
 *
 * @apiParam {Number} drama_id 剧集 ID
 * @apiParam {Number} [user_id] 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "drama": {
 *           "id": 30, // 剧集 ID
 *           "name": "望星辰之草原情殇", // 剧集名称
 *           "origin": 0, // 创作类型
 *           "alias": "望星辰之草原情殇别名", // 别名
 *           "age": 1, // 年代
 *           "author": "张三", // 原作者
 *           "integrity": 1, // 完结度 1：长篇未完结；2：长篇完结；3：全一期；4：微小剧
 *           "cover": "https://static.missevan.com/dramacoversmini/dramacover/201906/06/test.jpg", // 剧集海报
 *           "type": 3, // 分类
 *           "type_name": "全年龄", // 分类名称
 *           "checked": 0, // '剧集审核状态，0：未审核；1：审核通过；2：审核未通过（临时下架）；4：合约到期下架',
 *           "catalog": 89, // 分类 ID
 *           "abstract": "单元测试剧集简介", // 剧集简介
 *           "serialize": true, // 是否已完结
 *           "pay_type": 0,  / 付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费'
 *           "num": 0, // 剧集价格
 *           "purchased": false, // 用户是否已购买过该剧集
 *           "tags": [], // 剧集标签
 *           "organization_id": 0 // 社团 ID
 *         },
 *         "episodes": [{
 *           "ft": [],
 *           "music": [],
 *           "episode": [{
 *             "id": 3, // 单集 ID
 *             "name": "测试单集", // 单集名称
 *             "drama_id": 30, // 剧集 ID
 *             "sound_id": 11, // 音频 ID
 *             "order": 1, // 序号
 *             "type": 1, // 类型, 0: 正片; 1: 访谈; 2: 音乐; 3: 更多资源
 *             "pay_type": 1, // 付费类型, 0: 免费; 1: 单音付费; 2: 剧集付费
 *             "subtitle": "单集副标题", // 单集副标题
 *             "soundstr": "测试音频" // 音频名称
 *           },
 *           {
 *             "id": 4,
 *             "name": "测试单集 1",
 *             "drama_id": 30,
 *             "sound_id": 12,
 *             "order": 2,
 *             "type": 1,
 *             "pay_type": 1,
 *             "subtitle": "单集副标题",
 *             "soundstr": "测试音频"
 *           }]
 *         }],
 *         "like": false, // 用户是否订阅
 *         "view_count": 1 // 剧集观看总量
 *       }
 *     }
 */
func ActionGetDramaEpisodes(c *handler.Context) (handler.ActionResponse, error) {
	var p getDramaEpisodesParams

	// 读取并检查请求参数
	err := p.checkParams(c)
	if err != nil {
		return nil, err
	}

	// 查询剧集信息
	err = p.findDrama()
	if err != nil {
		return nil, err
	}

	// 查询单集信息
	err = p.findDramaEpisodes()
	if err != nil {
		return nil, err
	}

	// 查询音频信息
	err = p.findDramaSounds()
	if err != nil {
		return nil, err
	}

	// 剧集访问量 +1
	err = dramainfo.IncreaseViewCount(p.DramaID, 1)
	if err != nil {
		logger.WithField("drama_id", p.DramaID).Errorf("剧集访问量加 1 失败，错误信息：%v", err)
		// PASS
	}

	return p.resp(), nil
}

func (p *getDramaEpisodesParams) checkParams(c *handler.Context) error {
	err := c.BindJSON(&p)
	if err != nil {
		return actionerrors.ErrParams
	}
	if p.DramaID <= 0 || p.UserID < 0 {
		return actionerrors.ErrParams
	}

	return nil
}

func (p *getDramaEpisodesParams) findDrama() error {
	err := dramainfo.RadioDramaDramainfo{}.DB().Take(&p.dramaInfo, "id = ?", p.DramaID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return actionerrors.ErrDramaNotFound
		}
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 对于游客和非 UP 主只能查看过审剧集
	if (p.UserID == 0 || p.UserID != p.dramaInfo.UserID) && p.dramaInfo.Checked != dramainfo.CheckedPass {
		return actionerrors.ErrDramaNotFound
	}

	if p.UserID > 0 {
		// 用户是否已购买过该剧集
		// REVIEW: UP 主自己用不用返回购买状态
		purchased, err := p.dramaInfo.IsUserPurchased(p.UserID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		p.dramaInfo.Purchased = &purchased

		// 用户是否已订阅剧集
		like, err := dramasubscription.IsUserSubscribed(p.UserID, p.DramaID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		p.like = &like
	}

	return nil
}

func (p *getDramaEpisodesParams) findDramaEpisodes() error {
	// 只返回 type 为正剧、访谈、音乐的单集，type 为更多的单集不返回
	types := []int{dramaepisode.TypeDrama, dramaepisode.TypeInterview, dramaepisode.TypeMusic}
	// order 是关键字，需要加反引号，否则 SQL 执行会报错
	err := dramaepisode.RadioDramaEpisode{}.DB().
		Select("`id`, `name`, `drama_id`, `sound_id`, `order`, `type`, `pay_type`, `subtitle`").
		Where("drama_id = ? AND type IN (?)", p.DramaID, types).
		Order("`order` ASC").
		Scan(&p.dramaEpisodes).Error
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 对于已过审剧集的 UP 主自己显示修改后处于再审中的剧集信息
	err = p.checkDramaReviewInfo()
	if err != nil {
		return err
	}

	return nil
}

func (p *getDramaEpisodesParams) findDramaSounds() error {
	if len(p.dramaEpisodes) > 0 {
		checked := checkedStatus(p.UserID == p.dramaInfo.UserID)

		soundIDs := make([]int64, 0, len(p.dramaEpisodes))
		for _, dep := range p.dramaEpisodes {
			soundIDs = append(soundIDs, dep.SoundID)
		}

		err := msound.MSound{}.DB().Select("id, soundstr, view_count").
			Where("id IN (?) AND checked IN (?)", soundIDs, checked).
			Scan(&p.sounds).Error
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
	}

	return nil
}

func (p *getDramaEpisodesParams) resp() *getDramaEpisodesResp {
	resp := getDramaEpisodesResp{
		// 只返回 type 为正剧、访谈、音乐的单集，type 为更多的单集不返回
		Episodes: episodesResp{
			Episode: make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
			Ft:      make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
			Music:   make([]dramaepisode.RadioDramaEpisode, 0, len(p.dramaEpisodes)),
		},
		Like:  p.like,
		Drama: p.dramaInfo,
	}
	if len(p.dramaEpisodes) > 0 {
		var soundStrMap = make(map[int64]string, len(p.sounds))
		for _, sound := range p.sounds {
			soundStrMap[sound.ID] = sound.Soundstr
			resp.ViewCount += sound.ViewCount
		}

		for _, dep := range p.dramaEpisodes {
			if _, ok := soundStrMap[dep.SoundID]; ok {
				switch dep.Type {
				case dramaepisode.TypeDrama:
					resp.Episodes.Episode = append(resp.Episodes.Episode, dep)
				case dramaepisode.TypeInterview:
					resp.Episodes.Ft = append(resp.Episodes.Ft, dep)
				case dramaepisode.TypeMusic:
					resp.Episodes.Music = append(resp.Episodes.Music, dep)
				}
			}
		}
	}

	return &resp
}

func (p *getDramaEpisodesParams) checkDramaReviewInfo() error {
	if p.UserID <= 0 || p.UserID != p.dramaInfo.UserID || p.dramaInfo.Checked != dramainfo.CheckedPass {
		return nil
	}

	err := checkeddramareview.CheckedDramaReview{}.DB().
		Select("name, origin, alias, age, author, integrity, cover, type, catalog, abstract, organization_id, episodes").
		Where("drama_id = ? AND delete_time = 0", p.DramaID).
		Take(&p.checkedDramaReview).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil
		}
		return actionerrors.ErrServerInternal(err, nil)
	}

	// 根据再审剧集信息重新赋值剧集信息
	p.checkedDramaReview.ReassignDramaInfo(&p.dramaInfo)

	// 根据再审单集信息重新赋值单集信息
	err = p.checkedDramaReview.ReassignDramaEpisodesInfo(&p.dramaEpisodes)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}

	return nil
}

func checkedStatus(self bool) []int {
	// 过审、报警
	if !self {
		return []int{msound.CheckedPass, msound.CheckedPolice}
	}

	// UP 主自己能看到加入剧集中的待审、过审、报警、未转码的单音
	return []int{
		msound.CheckedUnpass,
		msound.CheckedPass,
		msound.CheckedPolice,
		msound.CheckedSoundTranscode,
	}
}

type checkDramaRefinedParam struct {
	DramaIDs []int64  `json:"drama_ids"`
	Types    []string `json:"types"`

	dramaTypes map[int]string `json:"-"`
}

// ActionCheckDramaRefined 判断剧集属性
/**
 * @api {post} /rpc/drama/check-drama-refined 判断剧集属性
 *
 * @apiVersion 0.1.0
 * @apiName check-drama-refined
 * @apiGroup /rpc/drama
 *
 * @apiParam {Number[]} drama_ids 剧集 IDs
 * @apiParam {String[]} types 剧集属性类型 \
 *     risking：擦边球 \
 *     japan_forbidden：日本禁听 \
 *     interactive：互动广播剧 \
 *     lossless：无损音质广播剧 \
 *     special：特殊剧集 \
 *     sensitive：敏感剧集 \
 *     no_japan_sale：日本禁购 \
 *     no_live_recommend：无推荐直播模块的剧集 \
 *     search_hidden：剧集隐藏
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "1": { // key 为剧集 ID
 *           "check_details": {
 *             "interactive": false,
 *             "japan_forbidden": false,
 *             "risking": true
 *           }
 *         },
 *         "4": {
 *           "check_details": {
 *             "interactive": false,
 *             "japan_forbidden": true,
 *             "risking": true
 *           }
 *         }
 *       }
 *     }
 */
func ActionCheckDramaRefined(c *handler.Context) (handler.ActionResponse, error) {
	var req checkDramaRefinedParam
	if err := c.BindJSON(&req); err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(req.DramaIDs) == 0 || len(req.Types) == 0 {
		return nil, actionerrors.ErrParams
	}
	req.DramaIDs = util.Uniq(req.DramaIDs)
	var err error
	if req.dramaTypes, err = dramainfo.CheckRefinedType(req.Types); err != nil {
		return nil, actionerrors.ErrParams
	}
	info, err := dramainfo.CheckRefined(req.DramaIDs, req.dramaTypes)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return info, nil
}

// ActionIPRDramasSubscribed 用户是否订阅该 IPR 下的剧集
/**
 * @api {post} /rpc/drama/ipr-dramas-subscribed 用户是否订阅该 IPR 下的剧集
 * @apiVersion 0.1.0
 * @apiName ipr-dramas-subscribed
 * @apiGroup /rpc/drama
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} ipr_id IPR ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "subscribed": 1 // 是否订阅该 IPR 下的剧集, 0：未订阅；1：已订阅
 *       }
 *     }
 */
func ActionIPRDramasSubscribed(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID int64 `json:"user_id"`
		IPRID  int64 `json:"ipr_id"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.IPRID <= 0 {
		return nil, actionerrors.ErrParams
	}
	u, err := user.FindByUserID(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUserNotFound, "用户不存在")
	}
	ipr, err := dramaipr.FindOneByID(param.IPRID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if ipr == nil {
		return nil, actionerrors.ErrNotFound(handler.CodeUnknownError, "该 IPR 不存在")
	}

	subscribed, err := dramasubscription.IsIPRDramasSubscribed(param.IPRID, param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return handler.M{"subscribed": util.BoolToInt(subscribed)}, nil
}

// dramaFeedNoticeParams 追剧更新提醒参数
type dramaFeedNoticeParams struct {
	UserID    int64 `json:"user_id"`
	StartTime int64 `json:"start_time"`
}

// dramaFeedNoticeResp 追剧更新提醒响应结果
type dramaFeedNoticeResp struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	Cover          string `json:"cover"`
	CoverColor     int    `json:"cover_color"`
	PayType        int    `json:"pay_type"`
	Newest         string `json:"newest"`
	LastUpdateTime int64  `json:"lastupdate_time"`

	IsSaw        int    `json:"is_saw"`
	SawEpisode   string `json:"saw_episode"`
	SawEpisodeID int64  `json:"saw_episode_id"`
	SawSoundID   int64  `json:"saw_sound_id"`
}

// ActionFeedNotice 获取追剧更新提醒
/**
 * @api {post} /rpc/drama/feed-notice 获取追剧更新提醒
 * @apiVersion 0.1.0
 * @apiGroup rpc/drama
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [start_time=0] 起始时间戳（获取这个时间之后更新的剧集，单位：秒）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} 有更新提醒:
 *     {
 *       "code": 0,
 *       "info": {
 *         "id": 9888, // 剧集 ID
 *         "name": "杀破狼", // 剧集名称
 *         "cover": "https://static.maoercdn.com/dramacoversmini/201708/25/3897688370d841d63ae9574e2f317c57123406.jpg", // 剧集封面
 *         "cover_color": 8465427,
 *         "pay_type": 2,
 *         "is_saw": 1, // 剧集更新是否已被查看 0：未查看；1：已查看
 *         "saw_episode": "赐我",
 *         "saw_episode_id": 815670,
 *         "saw_sound_id": 5758410,
 *         "lastupdate_time": 1699012358, // 剧集更新时间 单位：秒
 *         "newest": "小剧场·将军与桃花"
 *       }
 *     }
 * @apiSuccessExample {json} 没有更新提醒:
 *     {
 *       "code": 0,
 *       "info": null
 *     }
 */
func ActionFeedNotice(c *handler.Context) (handler.ActionResponse, error) {
	var param dramaFeedNoticeParams
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	resp, err := param.getNotice()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return resp, nil
}

// getNotice 获取追剧更新提醒
func (param dramaFeedNoticeParams) getNotice() (*dramaFeedNoticeResp, error) {
	startTime := time.Unix(param.StartTime, 0)
	feedDramas, err := dramasubscription.GetFeedDramas(param.UserID, startTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil, nil
	}
	if len(feedDramas) == 0 {
		return nil, nil
	}
	// 获取有更新的订阅剧集 30 天内的播放量
	dramaIDs := make([]int64, 0, len(feedDramas))
	for _, v := range feedDramas {
		dramaIDs = append(dramaIDs, v.ID)
	}
	dramaViewCountMap, err := dramasubscribeplaylog.GetUserSubscribeDramaViewCount(dramaIDs, param.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if dramaViewCountMap != nil {
		for i, v := range feedDramas {
			if viewCount, ok := dramaViewCountMap[v.ID]; ok {
				feedDramas[i].ViewCount30Days = viewCount
			}
		}
	}
	// 排序：30 天内播放次数最多的剧集（播放次数相同时，选取最近收听的剧集）
	sort.Slice(feedDramas, func(i, j int) bool {
		if feedDramas[i].ViewCount30Days == feedDramas[j].ViewCount30Days {
			return feedDramas[i].UpdateTime > feedDramas[j].UpdateTime
		}
		return feedDramas[i].ViewCount30Days > feedDramas[j].ViewCount30Days
	})
	noticeDrama := &dramaFeedNoticeResp{
		ID:             feedDramas[0].ID,
		Name:           *feedDramas[0].Name,
		Cover:          feedDramas[0].CoverURL,
		CoverColor:     feedDramas[0].CoverColor,
		PayType:        int(feedDramas[0].PayType),
		IsSaw:          feedDramas[0].IsSaw,
		SawEpisode:     feedDramas[0].SawEpisode,
		SawEpisodeID:   feedDramas[0].SawEpisodeID,
		LastUpdateTime: feedDramas[0].LastUpdateTime,
		Newest:         feedDramas[0].Newest,
	}
	if noticeDrama.SawEpisodeID != 0 {
		noticeDrama.SawSoundID, err = dramaepisode.GetSoundIDByEpisodeID(noticeDrama.SawEpisodeID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return noticeDrama, nil
}
