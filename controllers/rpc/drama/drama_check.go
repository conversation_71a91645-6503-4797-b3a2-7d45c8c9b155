package drama

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/checkeddramareview"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type dramaCheckStatusParams struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
}

type dramaCheckStatusResp struct {
	Checked   int8 `json:"checked"`   // 剧集审核状态
	Reviewing bool `json:"reviewing"` // 剧集是否是再审状态
}

// ActionDramaCheckStatus 获取剧集审核状态（包含再审状态）
/**
 * @api {post} /rpc/drama/check-status 获取剧集审核状态（包含再审状态）
 *
 * @apiVersion 0.1.0
 * @apiName check-status
 * @apiGroup /rpc/drama/
 *
 * @apiParam {Number} drama_id 剧集 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "checked": 1, // 剧集审核状态（0: 未审核；1: 审核通过；2: 审核未通过（临时下架）；3: 报警（待整合）；4: 合约到期下架）
 *       "reviewing": true // 剧集是否是再审状态
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 200130001
 * @apiError (404) {String} info 该剧集不存在
 */
func ActionDramaCheckStatus(c *handler.Context) (handler.ActionResponse, error) {
	var p dramaCheckStatusParams

	// 读取并检查请求参数
	err := c.BindJSON(&p)
	if err != nil || p.DramaID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 查询剧集和剧集再审信息
	// checked_drama_review 有数据代表剧集处于再审状态
	var dsi struct {
		DramaID int64 `gorm:"column:drama_id"` // 再审表中的剧集 ID
		Checked int8  `gorm:"column:checked"`  // 审核状态
	}
	err = service.DramaDB.Table(dramainfo.RadioDramaDramainfo{}.TableName()+" AS a").
		// 复审记录没有时，b.drama_id 为 NULL
		Select("a.checked, IFNULL(b.drama_id, 0) AS drama_id").
		Joins(fmt.Sprintf("LEFT JOIN %s AS b ON a.id = b.drama_id AND b.delete_time = 0",
			checkeddramareview.CheckedDramaReview{}.TableName())).
		Where("a.id = ?", p.DramaID).
		Scan(&dsi).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrDramaNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return dramaCheckStatusResp{
		Checked:   dsi.Checked,
		Reviewing: dsi.DramaID != 0,
	}, err
}
