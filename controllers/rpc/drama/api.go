package drama

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaderivative"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// DerivativeMaxCount 获取周边信息最大返回数量
const DerivativeMaxCount = 100

// DerivativeDefaultCount 获取周边信息默认返回数量
const DerivativeDefaultCount = 10

// derivativeItem 周边信息
type derivativeItem struct {
	ElementID int64  `json:"element_id"`
	Type      int    `json:"type"`
	Title     string `json:"title"`
	Intro     string `json:"intro"`
	Cover     string `json:"cover"`
	TypeName  string `json:"type_name"`
	URL       string `json:"url,omitempty"` // 周边类型为商品（type = 1）的时候无 url
}

// ActionGetDramaIDBySoundID 根据单音 ID 获取剧集 ID
/**
 * @api {post} /rpc/drama/get-dramaid-by-soundid 根据单音 ID 获取剧集 ID
 * @apiVersion 0.1.0
 * @apiName get-dramaid-by-soundid
 * @apiGroup rpc
 *
 * @apiParam {Number[]} sound_ids 单音 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       code: 0,
 *       info: [
 *         {
 *           "drama_id": 9888,
 *           "sound_id": 562158,
 *         },
 *         {
 *           "drama_id": 26,
 *           "sound_id": 569931,
 *         }
 *       ]
 *     }
 */
func ActionGetDramaIDBySoundID(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		IDs []int64 `json:"sound_ids"`
	}
	err := c.BindJSON(&input)
	if err != nil || len(input.IDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	key := makeCacheKeyGetDramaIDBySoundID(&input.IDs)

	ids, ok := getDramaIDBySoundIDFromCache(key, input.IDs)
	if ok {
		return ids, nil
	}

	ids, err = dramainfo.GetDramaIDsBySoundIDs(input.IDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	val, err := json.Marshal(ids)
	if err == nil {
		err = service.LRURedis.Set(key, string(val), 5*time.Minute).Err()
	}
	if err != nil {
		logger.Errorf("set cache error: %v", err)
		// PASS
	}

	return ids, nil
}

// NOTICE: 会对传入的数组进行排序和去重
func makeCacheKeyGetDramaIDBySoundID(ids *[]int64) string {
	// 生成的 key 不因 ids 的次序而不同
	sort.Slice(*ids, func(i, j int) bool {
		return (*ids)[i] < (*ids)[j]
	})
	// 确保元素的唯一性
	uniqIDs := util.Uniq(*ids)
	*ids = uniqIDs
	md5Sum := md5.Sum([]byte(fmt.Sprint(ids)))
	key := keys.KeyGetDramaIDBySoundID1.Format(fmt.Sprintf("%x", md5Sum))
	return key
}

func getDramaIDBySoundIDFromCache(key string, soundIDs []int64) (ids []dramainfo.SoundIDDramaID, ok bool) {
	str, err := service.LRURedis.Get(key).Result()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Errorf("getDramaIDBySoundIDFromCache error: %v", err)
		}
		return
	}
	err = json.Unmarshal([]byte(str), &ids)
	if err != nil {
		logger.Errorf("getDramaIDBySoundIDFromCache error: %v", err)
		return
	}
	return ids, true
}

// ActionGetDervativesByIP 根据 IP ID 获取周边信息
/**
 * @api {post} /rpc/drama/get-derivatives-by-ip 获取周边信息
 * @apiVersion 0.1.0
 * @apiName get-derivatives-by-ip
 * @apiGroup rpc
 *
 * @apiParam {Number} ip_id IP ID
 * @apiParam {Number} [count=10] 周边信息返回数量（最多返回 10 条周边信息）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object[]} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       code: 0,
 *       info: [
 *         {
 *           "type": 2,
 *           "element_id": 1,
 *           "title": "运势求签",
 *           "intro": "来看看今天的运势吧",
 *           "type_name": "求签",
 *           "url": "missevan://omikuji/draw",
 *           "cover": "https://static-test.maoercdn.com/derivative/201907/11/test.png"
 *         }
 *       ]
 *     }
 */
func ActionGetDervativesByIP(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		IPID  int64 `json:"ip_id"`
		Count int   `json:"count"`
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}
	if input.IPID <= 0 || input.Count < 0 || input.Count > DerivativeMaxCount {
		return nil, actionerrors.ErrParams
	}
	if input.Count == 0 {
		input.Count = DerivativeDefaultCount
	}

	derivatives, err := dramaderivative.FindDerivativesByIP(input.IPID, input.Count)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	result := make([]derivativeItem, len(derivatives))
	if len(derivatives) == 0 {
		return result, nil
	}

	for k, v := range derivatives {
		result[k] = derivativeItem{
			Type:      v.Type,
			ElementID: v.ElementID,
			Title:     v.Title,
			Intro:     v.Intro,
			TypeName:  dramaderivative.TypeName(v.Type, v.Tag),
			URL:       v.URL,
			Cover:     v.Cover,
		}
	}

	return result, nil
}
