package drama

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramasubscribeplaylog"
	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestDramaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getDramaEpisodesParams{}, "drama_id", "user_id")
	kc.Check(getDramaEpisodesResp{}, "drama", "episodes", "like", "view_count")
	kc.Check(episodesResp{}, "ft", "music", "episode")
}

func TestActionGetDramas(t *testing.T) {
	require := require.New(t)

	var param reqGetDramas
	param.Page = 1

	c := handler.NewTestContext(http.MethodPost, "/", false, param)
	_, err := ActionGetDramas(c)
	require.Equal(actionerrors.ErrParams, err)

	param.Params.Name = "测试"
	c = handler.NewTestContext(http.MethodPost, "/", false, param)
	_, err = ActionGetDramas(c)
	require.NoError(err)
}

func TestActionMustGetDramas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var id int64
	err := service.DramaDB.Select("id").Table(dramainfo.RadioDramaDramainfo{}.TableName()).Limit(1).Row().Scan(&id)
	require.NoError(err)

	params := map[string]interface{}{
		"drama_id": []int64{-2, -3, id},
	}

	c := handler.NewTestContext(http.MethodPost, "/", true, params)
	resp, err := ActionMustGetDramas(c)
	require.NoError(err)
	assert.Nil(resp)

	params["drama_id"] = []int64{id}
	c = handler.NewTestContext(http.MethodPost, "/", true, params)
	resp, err = ActionMustGetDramas(c)
	require.NoError(err)
	assert.Len(resp.([]dramainfo.RadioDramaDramainfo), 1)
}

func TestActionGetDramaEpisodes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	params := handler.M{"drama_id": 0}
	c := handler.NewTestContext(http.MethodPost, "/rpc/drama/get-drama", false, params)
	_, err := ActionGetDramaEpisodes(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户未登录，访问未过审剧集
	params = handler.M{"drama_id": 10}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/get-drama", false, params)
	_, err = ActionGetDramaEpisodes(c)
	assert.Equal(actionerrors.ErrDramaNotFound, err)

	// 测试用户未登录，访问已过审剧集
	params = handler.M{"drama_id": 2}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/get-drama", false, params)
	resp, err := ActionGetDramaEpisodes(c)
	require.NoError(err)
	r := resp.(*getDramaEpisodesResp)
	assert.Equal(int64(2), r.Drama.ID)
	assert.Len(r.Episodes.Episode, 0)
	assert.Len(r.Episodes.Music, 1)
	assert.Len(r.Episodes.Ft, 1)
	assert.Nil(r.Like)

	// 测试用户已登录且是 UP 主自己
	params = handler.M{"drama_id": 2, "user_id": 12}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/get-drama", false, params)
	resp, err = ActionGetDramaEpisodes(c)
	require.NoError(err)
	r = resp.(*getDramaEpisodesResp)
	assert.Equal(int64(2), r.Drama.ID)
	assert.Len(r.Episodes.Episode, 2)
	assert.Len(r.Episodes.Music, 0)
	assert.Len(r.Episodes.Ft, 0)
	assert.False(*r.Like)
}

func TestCheckDramaReviewInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var p getDramaEpisodesParams
	p.DramaID = 2
	require.NoError(dramainfo.RadioDramaDramainfo{}.DB().First(&p.dramaInfo, 2).Error)
	p.UserID = p.dramaInfo.UserID
	require.NoError(dramaepisode.RadioDramaEpisode{}.DB().
		Select("`id`, `name`, `drama_id`, `sound_id`, `order`, `type`, `pay_type`, `subtitle`").
		Where("drama_id = ?", p.DramaID).
		Order("`order` ASC").
		Scan(&p.dramaEpisodes).Error)

	err := p.checkDramaReviewInfo()
	assert.NoError(err)
	assert.Equal("测试修改剧集名称 1", *p.dramaInfo.Name)
	assert.Equal("测试修改单集信息 1", p.dramaEpisodes[0].Name)
	assert.Equal("测试修改单集信息 2", p.dramaEpisodes[1].Name)
}

func TestCheckedStatus(t *testing.T) {
	assert := assert.New(t)

	checked1 := []int{msound.CheckedPass, msound.CheckedPolice}
	checked2 := []int{
		msound.CheckedUnpass,
		msound.CheckedPass,
		msound.CheckedPolice,
		msound.CheckedSoundTranscode,
	}

	checked := checkedStatus(false)
	assert.Equal(checked1, checked)

	checked = checkedStatus(true)
	assert.Equal(checked2, checked)
}

func TestActionCheckDramaRefined(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	params := checkDramaRefinedParam{
		Types: []string{dramainfo.DramaTypeRisking, dramainfo.DramaTypeJapanForbidden, dramainfo.DramaTypeInteractive},
	}
	c := handler.NewRPCTestContext("/rpc/drama/check-drama-refined", params)
	_, err := ActionCheckDramaRefined(c)
	require.EqualError(err, "参数错误")

	params.DramaIDs = []int64{1, 4, 5, 6}
	c = handler.NewRPCTestContext("/rpc/drama/check-drama-refined", params)
	res, err := ActionCheckDramaRefined(c)
	require.NoError(err)
	assert.NotNil(res)
}

func TestActionIPRDramasSubscribed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 用户订阅了 IPR 下的剧集
	c := handler.NewRPCTestContext("/rpc/drama/ipr-dramas-subscribed", handler.M{
		"user_id": 3013621,
		"ipr_id":  1,
	})
	resp, err := ActionIPRDramasSubscribed(c)
	require.NoError(err)
	assert.EqualValues(1, resp.(handler.M)["subscribed"])

	// 用户未订阅了 IPR 下的剧集
	c = handler.NewRPCTestContext("/rpc/drama/ipr-dramas-subscribed", handler.M{
		"user_id": 3013621,
		"ipr_id":  2,
	})
	resp, err = ActionIPRDramasSubscribed(c)
	require.NoError(err)
	assert.Zero(resp.(handler.M)["subscribed"])

	c = handler.NewRPCTestContext("/rpc/drama/ipr-dramas-subscribed", handler.M{
		"user_id": 9999,
		"ipr_id":  10,
	})
	_, err = ActionIPRDramasSubscribed(c)
	assert.EqualError(err, "用户不存在")

	c = handler.NewRPCTestContext("/rpc/drama/ipr-dramas-subscribed", handler.M{
		"user_id": 3013621,
		"ipr_id":  10,
	})
	_, err = ActionIPRDramasSubscribed(c)
	assert.EqualError(err, "该 IPR 不存在")
}

func TestActionFeedNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	c := handler.NewRPCTestContext("/rpc/drama/feed-notice", handler.M{
		"user_id": 0,
	})
	_, err := ActionFeedNotice(c)
	require.EqualError(err, "参数错误")

	c = handler.NewRPCTestContext("/rpc/drama/feed-notice", handler.M{
		"user_id": int64(3010224),
	})
	resp, err := ActionFeedNotice(c)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestDramaFeedNoticeParam_getNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	partition := util.BeginningOfDay(now).Unix()
	err := service.DramaDB.Table(dramasubscribeplaylog.RadioDramaSubscribePlayLog30Day{}.TableName()).
		Where("id IN (?)", []int64{1, 2, 3, 4}).
		Updates(map[string]interface{}{"create_time": partition - 86400}).Error
	require.NoError(err)

	param := dramaFeedNoticeParams{
		UserID: int64(3010224),
	}

	// 测试获取播放量最高的订阅剧集
	resp, err := param.getNotice()
	require.NoError(err)
	assert.NotNil(resp)
	assert.EqualValues(9, resp.ID)
	assert.Equal("http://static-test.missevan.com/dramacoversmini/201807/02/0eb8d2e59ef06c7b24bfeeb33cfd5b9f112930.jpg", resp.Cover)
	assert.EqualValues(12434877, resp.CoverColor)
	assert.EqualValues(2, resp.PayType)
	assert.EqualValues(1, resp.IsSaw)
	assert.Equal("测试", resp.SawEpisode)
	assert.EqualValues(10, resp.SawEpisodeID)
	assert.EqualValues(3676, resp.SawSoundID)
	assert.EqualValues(1530502200, resp.LastUpdateTime)
	assert.Equal("第四期", resp.Newest)

	// 测试播放次数相同，获取最近收听的订阅剧集
	param.StartTime = int64(1530504799)
	resp, err = param.getNotice()
	require.NoError(err)
	assert.NotNil(resp)
	assert.EqualValues(6, resp.ID)
}
