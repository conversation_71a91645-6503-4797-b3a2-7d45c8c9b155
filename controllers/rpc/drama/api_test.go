package drama

import (
	"encoding/json"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMakeCacheKeyGetDramaIDBySoundID(t *testing.T) {
	assert := assert.New(t)

	key1 := makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2, 3})
	key2 := makeCacheKeyGetDramaIDBySoundID(&[]int64{3, 1, 2})
	assert.Equal(key1, key2)

	key1 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2})
	key2 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 1, 2})
	assert.Equal(key1, key2)

	key1 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2, 2, 2, 2, 3})
	key2 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2, 3})
	assert.Equal(key1, key2)

	key1 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2})
	key2 = makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2, 3})
	assert.NotEqual(key1, key2)
}

func TestActionGetDramaIDBySoundID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// from db
	params := strings.NewReader(`{"sound_ids":[1,2,3]}`)
	c := handler.NewTestContext(http.MethodPost, "/", false, params)
	result, err := ActionGetDramaIDBySoundID(c)
	require.NoError(err)
	jsn, _ := json.Marshal(result)

	key := makeCacheKeyGetDramaIDBySoundID(&[]int64{1, 2, 3})
	val, err := service.LRURedis.Get(key).Result()
	require.NoError(err)

	assert.JSONEq(string(jsn), val)

	// from cache
	params = strings.NewReader(`{"sound_ids":[2,2,1,3]}`)
	c = handler.NewTestContext(http.MethodPost, "/", false, params)
	result, err = ActionGetDramaIDBySoundID(c)
	require.NoError(err)
	jsn, _ = json.Marshal(result)

	val, err = service.LRURedis.Get(key).Result()
	require.NoError(err)

	assert.JSONEq(string(jsn), val)
	err = service.LRURedis.Del(key).Err()
	require.NoError(err)
}

func TestActionGetDervativesByIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := handler.M{"ip_id": 0}
	c := handler.NewTestContext(http.MethodPost, "/rpc/drama/get-derivatives-by-ip", false, params)
	_, err := ActionGetDervativesByIP(c)
	assert.EqualError(err, "参数错误")

	params = handler.M{"ip_id": 1}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/get-derivatives-by-ip", false, params)
	result, err := ActionGetDervativesByIP(c)
	require.NoError(err)
	r := result.([]derivativeItem)
	require.NoError(err)
	require.NotEmpty(r)
	assert.Equal(int64(1), r[0].ElementID)
	assert.Equal("商品", r[0].TypeName)

	params = handler.M{"ip_id": 2}
	c = handler.NewTestContext(http.MethodPost, "/rpc/drama/get-derivatives-by-ip", false, params)
	result, err = ActionGetDervativesByIP(c)
	require.NoError(err)
	assert.Empty(result)
}
