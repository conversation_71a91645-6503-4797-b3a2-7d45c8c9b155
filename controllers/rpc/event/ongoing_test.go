package event

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionListOngoing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{"type": "err"}
	c := handler.NewTestContext(http.MethodPost, "/event/listongoing", false, param)
	_, err := ActionListOngoing(c)
	assert.Equal(actionerrors.ErrParams, err, "type 是字符串")

	param = handler.M{"type": -1}
	c = handler.NewTestContext(http.MethodPost, "/event/listongoing", false, param)
	_, err = ActionListOngoing(c)
	assert.Equal(actionerrors.ErrParams, err, "type 太小")

	param = handler.M{"type": mevent.TypeLimit}
	c = handler.NewTestContext(http.MethodPost, "/event/listongoing", false, param)
	_, err = ActionListOngoing(c)
	assert.Equal(actionerrors.ErrParams, err, "type 太大")

	param = handler.M{"type": nil}
	c = handler.NewTestContext(http.MethodPost, "/event/listongoing", false, param)
	r, err := ActionListOngoing(c)
	require.NoError(err)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	var resp handler.M
	require.IsType(resp, r)
	resp = r.(handler.M)
	kc.Check(r, "data")
	var data []*mevent.Simple
	require.IsType(data, resp["data"])
	data = resp["data"].([]*mevent.Simple)
	assert.NotEmpty(data)
}

func TestActionListLiveRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := ActionListLiveRank(handler.NewTestContext(http.MethodPost, "/listliverank", false, nil))
	require.NoError(err)
	resp, ok := r.(handler.M)
	require.True(ok)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(resp, "data")
	assert.NotEmpty(resp["data"])
}
