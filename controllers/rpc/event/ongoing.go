package event

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
)

// ActionListOngoing 进行中的活动
/**
 * @api {post} /rpc/event/listongoing 进行中的活动（包括未开始的活动）部分信息
 * @apiDescription 已弃用，因为活动有复合类型
 * @apiName event-listongoing
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {number{0-5}} [type] 活动类型，不传则返回全部类型，同 missevan-web.\
 *   0：音频；1：图片；2：视频；3：特殊活动（如众筹活动）4：预约活动；5：直播活动
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "event_id": 133,
 *         "title": "国庆学习挑战",
 *         "type": 3,
 *         "status": 31,
 *         "start_time": 0,
 *         "end_time": 1999999999,
 *         "extended_fields": "{\"return\":{\"point_task_start_time\":1592928000,\"lottery_start_time\":1593532800},\"draw_start_time\":1593532800}",
 *         "attr": 3
 *       }]
 *     }
 *   }
 */
func ActionListOngoing(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Type *int `json:"type"`
	}
	err := c.BindJSON(&param)
	if err != nil ||
		(param.Type != nil && (*param.Type < 0 || *param.Type >= mevent.TypeLimit)) {
		return nil, actionerrors.ErrParams
	}
	events, err := mevent.ListOngoingSimple(param.Type)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return handler.M{
		"data": events,
	}, nil
}

// ActionListLiveRank 列出进行中的直播榜单活动
/**
 * @api {post} /rpc/event/listliverank 进行中的直播榜单活动（包括未开始的活动）的部分信息
 *
 * @apiName event-listliverank
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "event_id": 133,
 *         "title": "国庆学习挑战",
 *         "type": 3,
 *         "status": 31,
 *         "start_time": 0,
 *         "end_time": 1999999999,
 *         "extended_fields": "{\"return\":{\"point_task_start_time\":1592928000,\"lottery_start_time\":1593532800},\"draw_start_time\":1593532800}",
 *         "attr": 16
 *       }]
 *     }
 *   }
 */
func ActionListLiveRank(c *handler.Context) (handler.ActionResponse, error) {
	events, err := mevent.ListLiveRankOngoingSimple()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return handler.M{
		"data": events,
	}, nil
}
