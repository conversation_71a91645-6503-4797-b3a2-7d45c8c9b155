package scan

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/anmsg"
	"github.com/MiaoSiLa/missevan-go/models/balance"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// sendStatusShamAllow 假发送
	sendStatusShamAllow = iota
	// sendStatusAllow 允许发送
	sendStatusAllow
)

const (
	// allowPMConsumptionCoinLimit 用户消费阀值（单位：钻）
	// 超过消费阀值后，将对发送人进行规则检查
	allowPMConsumptionCoinLimit = 10000
	// allowPMRegisterDurationLimit 注册时间大于两周后，可以发送私信
	allowPMRegisterDurationLimit = 14 * 86400

	// 用户每天发送私信人数限制
	messageSendUserLimitDaily = 50

	// msgToUserNotFollowingLimitDaily 每天最多给 10 个未关注自己的人发私信限制
	msgToUserNotFollowingLimitDaily = 10

	// 用户每分钟发送私信条数阀值
	userIDLimitPerMinute = 20
	// IP 每分钟发送私信条数阀值
	ipLimitPerMinute = 60
)

// checkPMParams 调用接口需要的参数
type checkPMParams struct {
	// 发送用户 ID
	FromUserID int64 `json:"from_user_id"`
	// 接收用户 ID
	ToUserID int64 `json:"to_user_id"`
	// 私信内容
	Text string `json:"text"`

	// isMsgNoLimit 是否私信人数无限
	isMsgNoLimit bool

	// toUserFollowedFromUser 收信人是否关注发信人
	toUserFollowedFromUser bool
	// toUserNotFollowingMeListExists 收信人是否在对应限制列表中
	toUserNotFollowingMeListExists bool
}

// checkPMResponse 接口响应结果
type checkPMResponse struct {
	// Status 发送状态: 0 假发送，1 真发送
	Status int `json:"status"`
	// Msg 响应信息
	Msg string `json:"msg"`
}

// validate 检查参数
func (params *checkPMParams) validate() error {
	if params.FromUserID <= 0 || params.ToUserID <= 0 || params.FromUserID == params.ToUserID {
		return actionerrors.ErrParams
	}
	return nil
}

// ActionCheckPM handler
/**
 * @api {post} /rpc/scan/check-pm 检查用户是否满足发送私信条件
 * @apiDescription 检查用户发送私信人数是否大于等于 50 人，接收用户消费大于等于 1000 元时，过滤「注册时间小于两周」或「没有消费记录」或「不是主播」等发送用户私信的规则检查
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} from_user_id 发送人
 * @apiParam {Number} to_user_id 接收用户
 * @apiParam {String} text 私信内容
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "from_user_id": 1,
 *       "to_user_id": 2
 *       "text": "大家好"
 *     }
 *
 * @apiSuccess (200) {Number} code
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "status": 0,  // 发送状态: 0 假发送，1 真发送
 *         "msg": "发送成功"  // 响应信息
 *       }
 *     }
 *
 * @apiError (500) {Number} code CodeUnknownError = 100010007
 * @apiError (500) {String} info 具体错误信息
 * @apiError (403) {Number} code CodeLoginRequired = 200020006
 * @apiError (403) {string} info "发送私信人数达到上限"
 * @apiError (403) {Number} code CodeLoginRequired = 200020008
 * @apiError (403) {string} info "M娘来不及传递私信啦~ 回血中 >-<"
 */
func ActionCheckPM(c *handler.Context) (handler.ActionResponse, error) {
	var params checkPMParams
	paramsErr := c.BindJSON(&params)
	if paramsErr != nil {
		return nil, actionerrors.ErrParams
	}
	if paramsErr = params.validate(); paramsErr != nil {
		return nil, paramsErr
	}
	// 检查发信方者是否在全局黑名单中
	inBlacklist, err := user.InBlacklist(params.FromUserID, user.BlackTypePrivateMessage)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if inBlacklist {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "您的账号有可疑记录，暂时被系统封停")
	}

	// 发送频率检测
	result, err := sendMsgLimit(params.FromUserID, c.C.ClientIP())
	if err != nil {
		return nil, err
	}
	if !result {
		return nil, actionerrors.ErrSendMessageLimit
	}

	// 发送私信人数规则
	pass, err := params.checkUserSendCount()
	if err != nil {
		return nil, err
	}
	if !pass {
		return nil, actionerrors.ErrSendMessageUserCountLimit
	}

	// 私信打招呼限制
	pass, err = params.checkFollowedAndReply()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !pass {
		return nil, actionerrors.ErrSendMsgNotReplyLimit
	}

	// 直播相关私信检查
	liveChecked, err := liveCheckPMStatus(c.UserContext(), params)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	// 实物福袋中奖者不检查【每天最多给 10 个未关注自己的人发私信】和高消费规则限制
	if !liveChecked.HasLuckyBag {
		// 检查【每天最多给 10 个未关注自己的人发私信】
		result, err = params.isNotFollowingPass()
		if err != nil {
			return nil, err
		}
		if !result {
			return nil, actionerrors.ErrSendMsgNotFollowingLimit
		}

		// 高消费规则
		pass, err = checkHighConsumptionRules(&params)
		if err != nil {
			return nil, err
		}
		if !pass {
			// 假发送
			return params.newResp(sendStatusShamAllow), nil
		}
	}

	// 直播相关私信检查结果
	if liveChecked.Status == userapi.SendStatusShamAllow {
		// 假发送
		return params.newResp(sendStatusShamAllow), nil
	}

	// 检查是否是 evil 违禁词
	if service.AntiSpam.CheckEvil(params.Text) {
		// 假发送
		return params.newResp(sendStatusShamAllow), nil
	}

	// 屏蔽词检测
	result, err = forbiddenwords.HasForbiddenWords(forbiddenwords.ForbiddenWordTypePrivateMessageFakeSend, params.Text)
	if err != nil {
		return nil, err
	}
	if result {
		// 假发送
		return params.newResp(sendStatusShamAllow), nil
	}

	// 将用户加入已私信列表
	err = addUserSendList(params.FromUserID, params.ToUserID)
	if err != nil {
		return nil, err
	}

	// 关键词通知
	sendBotNotification(params.FromUserID, params.ToUserID, params.Text)

	// 真发送
	return params.newResp(sendStatusAllow), nil
}

// checkHighConsumptionRules 高消费规则：当接收用户消费总额大于 allowPMConsumptionCoinLimit 触发
// 注册时间小于两周的用户 或 没有消费记录的用户 或 不是主播的用户的私信 等规则检查
func checkHighConsumptionRules(params *checkPMParams) (bool, error) {
	allConsumption, err := balance.GetAllConsumption(params.ToUserID)
	if err != nil {
		return false, err
	}
	if allConsumption < allowPMConsumptionCoinLimit {
		return true, nil
	}

	// 接收用户为高消费用户，触发检查发送用户规则
	result, err := checkUserRegisterDuration(params.FromUserID, allowPMRegisterDurationLimit)
	if err != nil {
		return false, actionerrors.ErrServerInternal(err, nil)
	}
	if result {
		// 用户注册时间大于 allowPMRegisterDurationLimit，真发送私信
		return true, nil
	}
	// 用户存在消费或用户是主播，真发送私信
	return liveExists(params.FromUserID) || checkUserExistConsumption(params.FromUserID), nil
}

// checkUserRegisterDuration 检查用户注册时间是否大于 durationLimit
func checkUserRegisterDuration(userID, durationLimit int64) (bool, error) {
	createTime, err := user.MowangskUser{}.GetCTimeByID(userID)
	if err != nil {
		return false, err
	}
	return createTime+durationLimit < util.TimeNow().Unix(), nil
}

// liveExists 检查用户是否为主播
func liveExists(userID int64) bool {
	l, err := models.Live{}.Exists(userID)
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return l
}

// checkUserExistConsumption 用户是否存在消费
func checkUserExistConsumption(userID int64) bool {
	b, err := balance.GetAllConsumption(userID)
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return b > 0
}

func addUserSendList(userID int64, toUserID int64) error {
	key := serviceredis.KeyMessageSendUserID1.Format(userID)
	pipe := service.Redis.Pipeline()
	pipe.SAdd(key, toUserID)
	ttl := pipe.TTL(key)
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	if ttl.Val() <= 0 {
		err = service.Redis.Expire(key, time.Hour*24).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// checkUserSendCount 检查用户发送私信人数
func (params *checkPMParams) checkUserSendCount() (bool, error) {
	confirm, err := user.MowangskUser{}.GetConfirmByID(params.FromUserID)
	if err != nil {
		logger.Error(err)
		return false, err
	}
	params.isMsgNoLimit = confirm&user.ConfirmMsgNoLimit == user.ConfirmMsgNoLimit
	if params.isMsgNoLimit {
		// 用户发送私信无限制
		return true, nil
	}

	key := serviceredis.KeyMessageSendUserID1.Format(params.FromUserID)
	counts, err := service.Redis.SCard(key).Result()
	if err != nil {
		logger.Error(err)
		return false, err
	}
	if counts < messageSendUserLimitDaily {
		return true, nil
	}
	exists, err := service.Redis.SIsMember(key, params.ToUserID).Result()
	if err != nil {
		logger.Error(err)
		return false, err
	}
	return exists, nil
}

// isNotFollowingPass 检查【每天最多给 10 个未关注自己的人发私信】是否通过
func (params *checkPMParams) isNotFollowingPass() (bool, error) {
	if params.isMsgNoLimit || params.toUserFollowedFromUser {
		return true, nil
	}
	key := keyMsgUserNotFollowingMeList(params.FromUserID)
	pipe := service.Redis.TxPipeline()
	countCmd := pipe.SCard(key)
	isMemberCmd := pipe.SIsMember(key, params.ToUserID)
	_, err := pipe.Exec()
	if err != nil {
		return false, actionerrors.ErrServerInternal(err, nil)
	}
	params.toUserNotFollowingMeListExists = isMemberCmd.Val()
	count := countCmd.Val()
	if count < msgToUserNotFollowingLimitDaily {
		return true, nil
	}
	return params.toUserNotFollowingMeListExists, nil
}

// addMsgUserNotFollowingMeList 将未关注收信人加入每日收信人未关注发信人列表
func (params *checkPMParams) addMsgUserNotFollowingMeList() {
	if params.isMsgNoLimit || params.toUserFollowedFromUser ||
		params.toUserNotFollowingMeListExists {
		return
	}

	key := keyMsgUserNotFollowingMeList(params.FromUserID)
	pipe := service.Redis.TxPipeline()
	pipe.SAdd(key, params.ToUserID)
	pipe.Expire(key, 24*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
}

func (params *checkPMParams) newResp(status int) checkPMResponse {
	params.addMsgUserNotFollowingMeList()
	return checkPMResponse{
		Status: status,
		Msg:    "发送成功",
	}
}

// sendMsgLimit 私信发送频率检测
func sendMsgLimit(userID int64, ip string) (bool, error) {
	t := util.TimeNow().Minute()
	ipKey := serviceredis.LockMessageSendIP2.Format(ip, t)
	userIDKey := serviceredis.LockMessageSendUserID2.Format(userID, t)

	pipe := service.Redis.Pipeline()
	userIDCount := pipe.Incr(userIDKey)
	ipCount := pipe.Incr(ipKey)
	pipe.Expire(ipKey, time.Minute)
	pipe.Expire(userIDKey, time.Minute)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	return userIDCount.Val() <= userIDLimitPerMinute && ipCount.Val() <= ipLimitPerMinute, nil
}

func sendBotNotification(fromUserID, toUserID int64, text string) {
	util.Go(func() {
		ok, err := forbiddenwords.HasForbiddenWords(forbiddenwords.ForbiddenWordTypePMNotify, text)
		if err != nil {
			logger.Errorf("获取 Bot 提醒词失败：%v", err)
			// PASS
		}
		if !ok {
			return
		}

		ok, err = service.Redis.SetNX(serviceredis.LockUserPMNotify1.Format(fromUserID), "1", time.Hour).Result()
		if err != nil {
			logger.Errorf("获取 Bot 发送限制失败：%v", err)
			return
		}
		if !ok {
			return
		}

		err = service.PushService.SendBotMessage(pushservice.BotMessage{
			Content: fmt.Sprintf("发件人 %d\n收件人 %d\n私信内容 %s", fromUserID, toUserID, text),
			Channel: pushservice.BotChannelPMNotify,
		})
		if err != nil {
			logger.Errorf("发送 Bot 消息失败：%v", err)
			return
		}
	})
}

func liveCheckPMStatus(ctx mrpc.UserContext, params checkPMParams) (*userapi.LiveCheckPMResp, error) {
	return userapi.LiveCheckPM(ctx, params.FromUserID, params.ToUserID)
}

func keyMsgUserNotFollowingMeList(fromUserID int64) string {
	return keys.KeyMsgUserNotFollowingMeList2.Format(fromUserID,
		util.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
}

// checkFollowedAndReply 检查【给未关注我的人发私信，在收信人关注我或回复我前，仅可发送 1 条消息】
func (params *checkPMParams) checkFollowedAndReply() (bool, error) {
	if params.isMsgNoLimit {
		return true, nil
	}
	var err error
	params.toUserFollowedFromUser, err = person.HasFollowed(params.ToUserID, params.FromUserID)
	if err != nil {
		return false, err
	}

	if !params.toUserFollowedFromUser &&
		// WORKAROUND: 需要等客户端接入后，暂时仅在非线上环境才处理这个逻辑
		!util.IsProdEnv() {
		ok, err := anmsg.Exists(params.FromUserID, params.ToUserID)
		if err != nil {
			return false, err
		}
		if ok {
			ok, err = anmsg.Exists(params.ToUserID, params.FromUserID)
			if err != nil {
				return false, err
			}
			if !ok {
				return false, nil
			}
			return true, nil
		}
	}
	return true, nil
}
