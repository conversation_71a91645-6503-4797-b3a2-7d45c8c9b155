package scan

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// TextScanParams 文本检测参数
type TextScanParams struct {
	Content string `json:"content"`
	Scene   string `json:"scene"`
	UserID  int64  `json:"user_id"`
	Type    string `json:"type"`
}

// ActionText handler
/**
 * @api {post} /rpc/scan/text 文本检测
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {String[]} text 每个文本不能为空，最多 100 个元素，每个元素的字符数不限，但总字符数不能超过 1000,000 个
 * @apiParam {String} [scene] 传 "private_message", "comment", "search" 允许广告（pass = true）
 *                            传 "" 不允许（pass = false），但都还取决于 ad_free
 *                            除了 "search"，其它的 scene 都会直接作为 bizType 传给阿里云作区分，目前的 scene 有：
 *                            私信 "private_message", 评论 "comment", 简介 "intro", 用户信息 "user_info",
 *                            弹幕 "danmaku", 直播间消息 "im", 搜索 "search"
 *
 * @apiParam {Boolean} [ad_free] 值为 false 时，如果用户是新用户（注册 3 天内）就由 user_id/IP 累计 ad/blacklist 的次数，
 *                               达到限制就把结果的 pass 置为 false；值为 true 时，覆盖 scene 的广告设置，允许广告
 *
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "pass": true,
 *         "labels": [
 *           "normal"
 *         ]
 *       },
 *       {
 *         "pass": false,
 *         "labels": [
 *           "blacklist" // 本地自定义
 *         ]
 *       },
 *       {
 *         "pass": false,
 *         "labels": [
 *           "customized" // 阿里云后台自定义
 *         ]
 *       }
 *     ]
 *   }
 */
func ActionText(c *handler.Context) (handler.ActionResponse, error) {
	result, err := scantext(c)
	if err != nil {
		return nil, err
	}
	response := make([]*scan.BaseCheckResult, len(result))
	for i := range result {
		response[i] = userapi.ConvertScanResultToBase(result[i])
	}
	return response, nil
}

// newSmartsheetParamUserInfo 创建用户信息审核的智能表格参数
func newSmartsheetParamUserInfo(params *TextScanParams) *smartsheet.SheetAddRecordParam {
	// TODO: 评论弹幕词库改为用户信息专用词库
	// 检查是否命中审核词库
	matched, matchedWord, err := forbiddenwords.GetMatchedForbiddenWord(forbiddenwords.ForbiddenWordTypeCommentNotice, params.Content)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// 只有命中审核词时才推送
	if !matched {
		return nil
	}
	// 获取用户信息
	username := ""
	userInfo, err := user.FindByUserID(params.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	} else if userInfo != nil {
		username = userInfo.UserName
	}
	return &smartsheet.SheetAddRecordParam{
		Channel: "main_review_" + scan.SceneUserInfo,
		Fields:  []string{"用户 ID", "用户昵称", "文本内容", "命中词", "类型", "发送时间"},
		Values:  []string{strconv.FormatInt(params.UserID, 10), username, params.Content, matchedWord, params.Type, util.TimeNow().Format(util.TimeFormatHMS)},
	}
}

// pushToSmartsheetUserInfo 推送用户信息审核到智能表格
func pushToSmartsheetUserInfo(params *TextScanParams) bool {
	addRecordParam := newSmartsheetParamUserInfo(params)
	if addRecordParam == nil {
		return false
	}
	err := service.Smartsheet.AddRecord(addRecordParam)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// ActionTextDetail handler
/**
 * @api {post} /rpc/scan/text/detail 文本检测，适用场景：查看具体违规信息的后台调用本 rpc 获得具体违禁信息
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {String[]} text 每个文本不能为空，最多 100 个元素，每个元素的字符数不限，但总字符数不能超过 1000,000 个
 * @apiParam {String} [scene] 传 "private_message", "comment", "search" 允许广告（pass = true）
 *                            传 "" 不允许（pass = false），但都还取决于 ad_free
 *                            除了 "search"，其它的 scene 都会直接作为 bizType 传给阿里云作区分，目前的 scene 有：
 *                            私信 "private_message", 评论 "comment", 简介 "intro", 用户信息 "user_info",
 *                            弹幕 "danmaku", 直播间消息 "im", 搜索 "search"
 *
 * @apiParam {Boolean} [ad_free] 值为 false 时，如果用户是新用户（注册 3 天内）就由 user_id/IP 累计 ad/blacklist 的次数，
 *                               达到限制就把结果的 pass 置为 false；值为 true 时，覆盖 scene 的广告设置，允许广告
 *
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "pass": true,
 *         "labels": [
 *           "normal"
 *         ],
 *         "blacklist_match": {
 *
 *         }
 *       },
 *       {
 *         "score": 0,
 *         "pass": false,
 *         "labels": [
 *           "ad",
 *           "blacklist"
 *         ],
 *         "blacklist_match": { // 如果命中黑名单会返回的具体命中信息
 *           "violation_content": [
 *             "日赚" // 违禁词具体内容
 *           ],
 *           "mode": 2, // 0：整个文本命中，1：部分文本命中，2：正则表达式命中
 *           "regex": "[日曰].{0,3}[赚賺算]", // 命中正则表达式时对应的正则表达式
 *           "pattern": "" // 如果是文本命中，黑名单中的文本
 *         }
 *       },
 *       {
 *         "score": 0,
 *         "pass": false,
 *         "labels": [
 *           "ad"
 *         ],
 *         "blacklist_match": {
 *
 *         },
 *         "aliyun_match": [ // 如果命中阿里云，具体的命中信息
 *           {
 *             "requestId": "43333B6A-ACC8-49C4-9110-FC4FDC7F90D1", // 请求阿里云文本检测接口阿里云返回的 ID
 *             "label": "ad", // 文本垃圾检测结果的分类，如果有多个分类会用逗号分隔
 *             "details": [
 *               {
 *                 "label": "ad", // 文本命中风险的分类
 *                 "context": "qq 群", // 检测文本命中的风险关键词，如果命中了关键词会返回该内容
 *                 "libName": null, // 命中自定义词库时对应的词库名，是一个 string 数组
 *                 "ruleType": null //  命中行为规则时的行为规则名，是一个 string 数组
 *               },
 *               {
 *                 "label": "ad",
 *                 "context": "24682469",
 *                 "libName": null,
 *                 "ruleType": null
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     ]
 *   }
 */
func ActionTextDetail(c *handler.Context) (handler.ActionResponse, error) {
	return scantext(c)
}

func scantext(c *handler.Context) ([]*scan.CheckResult, error) {
	var input userapi.TextScanTask
	// 为空则不会把 IP 放入黑名单
	input.IP = c.ClientIP()
	// 该字段目前没用到
	input.EquipID = c.EquipID()

	err := c.BindJSON(&input)
	if err != nil {
		return nil, err
	}
	// 检查是否是 user_info 场景，注册时 user_id = 0 不检测
	if input.Scene == scan.SceneUserInfo && len(input.Text) > 0 && input.UserID > 0 {
		util.Go(func() {
			switch len(input.Text) {
			case 1:
				// Text 只有一个元素时，可以是昵称/简介/直播间粉丝勋章名称/直播用户自定义进场欢迎语/剧集打赏留言/手机恋人角色名
				params := &TextScanParams{
					Content: input.Text[0],
					Scene:   input.Scene,
					UserID:  input.UserID,
					Type:    "昵称/简介/直播间粉丝勋章名称/直播用户自定义进场欢迎语/剧集打赏留言/手机恋人角色名",
				}
				_ = pushToSmartsheetUserInfo(params)
			case 2:
				// web 端用户信息审核时，Text 有两个元素，分别是昵称和简介
				params := &TextScanParams{
					Content: input.Text[0],
					Scene:   input.Scene,
					UserID:  input.UserID,
					Type:    "昵称",
				}
				_ = pushToSmartsheetUserInfo(params)
				params = &TextScanParams{
					Content: input.Text[1],
					Scene:   input.Scene,
					UserID:  input.UserID,
					Type:    "简介",
				}
				_ = pushToSmartsheetUserInfo(params)
			}
		})
	}
	// 审核组给用户发私信时，不检测
	if input.Scene == scan.ScenePrivateMessage {
		if ok, err := role.IsRole(input.UserID, role.AuditGroup); err == nil && ok {
			results := make([]*scan.CheckResult, len(input.Text))
			for i := range results {
				results[i] = new(scan.CheckResult)
				results[i].Pass = true
				results[i].Labels = []string{"admin"}
			}
			return results, nil
		}
	}

	results, _, err := userapi.CheckUserTexts(input)
	return results, err
}
