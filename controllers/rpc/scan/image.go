package scan

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

type imageParam struct {
	URL     string `json:"url"`
	Scene   string `json:"scene"`
	UserID  int64  `json:"user_id"`
	Version int    `json:"version,omitempty"`
}

const (
	// ImageScanVersionV1 图片审核 V1.0 的版本
	ImageScanVersionV1 = 1
	// ImageScanVersionV2 图片审核增强版的版本
	ImageScanVersionV2 = 2
)

// ActionImage 图片检测
/**
 * @api {post} /rpc/scan/image 图片检测
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} url 图片路径
 * @apiParam {String} [scene] 场景：user_info 用户信息
 * @apiParam {String} user_id 用户 ID
 * @apiParam {Number} [version] 图片检测版本，只有为 1 的时候调用图片审核 V1.0，其他的调用图片审核增强版
 *
 * @apiSuccessExample {json} Pass:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "score": 0,
 *         "pass": true,
 *         "labels": null
 *       }
 *     }
 *
 * @apiSuccessExample {json} Not pass:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "score": 80,
 *         "pass": false,
 *         "labels": ["porn"] // 增强版的 labels 和旧的不同，返回更加精细化，如：pornographic_adultContent_tii_1006
 *       }
 *     }
 */
func ActionImage(c *handler.Context) (handler.ActionResponse, error) {
	result, err := scanImage(c)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return nil, nil
	}
	baseRes := userapi.ConvertScanResultToBase(result)
	return baseRes, nil
}

// ActionImageDetail 图片检测并返回详细信息
/**
 * @api {post} /rpc/scan/image/detail 图片检测
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} url 图片路径
 * @apiParam {string="","album","user_info"} scene 检测场景
 * @apiParam {String} user_id 用户 ID
 * @apiParam {Number} [version] 图片检测版本，只有为 1 的时候调用图片审核 V1.0，其他的调用图片审核增强版
 *
 * @apiSuccessExample {json} Pass:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "score": 0,
 *         "pass": true,
 *         "labels": null,
 *         "aliyun_match": [
 *           {
 *             "requestId": "63E52C47-2E4D-5E23-B089-EE648F446E99",
 *             "taskId": "img4lV5HHRxkBL6AK3KtkbJvP-1AUog3" // 图片增强版不会返回 taskId，使用 requestId 和阿里对接
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiSuccessExample {json} Not pass:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "score": 80,
 *         "pass": false,
 *         "labels": ["porn"], // 增强版的 labels 和旧的不同，返回更加精细化，如：pornographic_adultContent_tii_1006
 *         "aliyun_match": [
 *           {
 *             "requestId": "63E52C47-2E4D-5E23-B089-EE648F446E99",
 *             "taskId": "img4lV5HHRxkBL6AK3KtkbJvP-1AUog3" // 图片增强版不会返回 taskId，使用 requestId 和阿里对接
 *           }
 *         ]
 *       }
 *     }
 */
func ActionImageDetail(c *handler.Context) (handler.ActionResponse, error) {
	return scanImage(c)
}

func scanImage(c *handler.Context) (*scan.CheckResult, error) {
	var params imageParam
	err := c.BindJSON(&params)
	if err != nil {
		return nil, ErrIllegalParam
	}
	var result *scan.CheckResult
	if params.Version == ImageScanVersionV1 {
		result, err = service.AntiSpam.CheckImage(params.URL, params.UserID, params.Scene)
	} else {
		result, err = service.AntiSpamV2.CheckImage(params.URL, params.Scene)
	}
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if result == nil {
		return nil, nil
	}
	if !result.Pass {
		// 记录用户违规图片信息
		msg := fmt.Sprintf("scene: %s, score: %f, labels: %v, request_id: %v, task_id: %s",
			params.Scene, result.Score, result.Labels, result.RequestID, result.TaskID)
		adminLog := &adminlogger.AdminLog{
			UserID:  params.UserID,
			Catalog: adminlogger.CatalogUserViolationImage,
			URL:     c.Request().URL.Path,
			Intro:   msg,
			IP:      c.ClientIP(),
		}
		if err != adminLog.InsertAdminLog() {
			logger.Error(err)
			// PASS
		}
	}
	return result, nil
}
