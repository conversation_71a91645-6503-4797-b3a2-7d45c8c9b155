package scan

import (
	"math/rand"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	labelDirect    = "direct"
	labelEvil      = "evil"
	labelBlacklist = "blacklist"
)

// ActionIM im 文字检查
/**
 * @api {post} /rpc/scan/im 直播间聊天消息内容过滤
 * @apiVersion 0.1.0
 * @apiGroup rpc/scan
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} ip 用户 IP
 * @apiParam {String} equip_id 设备号
 * @apiParam {String} api 发消息的接口路径
 * @apiParam {String} [referer] web 端的 Referer, 客户端不传
 * @apiParam {String} user_agent 用户方的 User-Agent
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} room_catalog_id 房间所属分区
 * @apiParam {Number} room_creator_id 主播 ID
 * @apiParam {Number} msg_id 消息 ID
 * @apiParam {String} content 消息具体内容
 * @apiParam {Boolean} [user_is_admin=false] 是否是房管
 * @apiParam {Boolean} [user_has_medal=false] 是否有粉丝勋章
 * @apiParam {Boolean} [allow_ad=false] 是否允许广告
 * @apiParam {string="horn","message","danmaku","diy_gift","lucky_bag","question"} [scene] 检测场景
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "pass": true,
 *         "labels": [
 *           "normal"
 *         ]
 *       },
 *       {
 *         "pass": false,
 *         "labels": [
 *           "blacklist" // 本地自定义
 *         ]
 *       },
 *       {
 *         "pass": false,
 *         "labels": [
 *           "customized" // 阿里云后台自定义
 *         ]
 *       }
 *     ]
 *   }
 */
func ActionIM(c *handler.Context) (handler.ActionResponse, error) {
	var param *gaia.ParamLiveIM
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if !param.Check(c) {
		return nil, actionerrors.ErrParams
	}

	switch param.RoomCreatorID {
	case 2952796, // 景向
		189047,   // 羊仔
		189763,   // 姜sir
		11893250, // 赵乾景
		4580231,  // 陈张太康
		4019359,  // 胡良伟
		326338,   // 锦鲤
		67494,    // 马正阳
		5932350:  // 猫耳Live
	default:
		var useAntispam float64
		config.Conf.AB.Get("scan_im_antispam", &useAntispam)
		if useAntispam >= rand.Float64() {
			// 灰度 gaia 内容过滤，此处是旧版本
			r, err := imAntispam(param)
			if err == nil {
				return r, nil
			}
			if !isAliyunExceedQuotaError(err) {
				return r, actionerrors.ErrServerInternal(err, nil)
			}
			_, err = service.Redis.SetNX(serviceredis.LockScanIMExceedQuota0.Format(), 1, 5*time.Second).Result()
			if err != nil {
				logger.Errorf("设置返回阿里云限流失败：%v", err)
				// PASS
			}
		}
	}

	scr := []*scan.CheckResult{{
		Pass:   true,
		Labels: []string{labelDirect},
	}}
	aliRes := []antispam.AliyunTextRespElem{{
		Content: param.Content,
		Results: []antispam.AliyunTextResult{{
			Label: labelDirect,
		}},
	}}
	param.SetScanResult(scr, aliRes)
	gaiaRes, err := service.Gaia.Call(servicegaia.APIRuleCheck, param)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	resp := []*scan.BaseCheckResult{userapi.ConvertScanResultToBase(scr[0])}
	assignGaiaRes(resp, gaiaRes)
	// 推送通知到智能表格进行人工审核
	util.Go(func() {
		_ = pushToSmartsheet(param, resp)
	})
	return resp, nil
}

// assignGaiaRes 将 gaia 的检查结果嵌入给没经 antispam 检查的结果
// 原始的 resp pass 应该是 true
func assignGaiaRes(resp []*scan.BaseCheckResult, gaiaRes []string) {
	for i := range gaiaRes {
		switch gaiaRes[i] {
		case gaia.DecisionSelfView: // 假发送，只添加 label 不影响 pass
			resp[0].Labels = append(resp[0].Labels, labelEvil)
		case gaia.DecisionForbid: // 屏蔽，pass 改成 false, 添加 label
			resp[0].Pass = false
			resp[0].Labels = append(resp[0].Labels, labelBlacklist)
		}
	}
}

func imAntispam(param *gaia.ParamLiveIM) ([]*scan.BaseCheckResult, error) {
	err := service.Redis.Get(serviceredis.LockScanIMExceedQuota0.Format()).Err()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Errorf("imAntispam check lock error: %v", err)
		// PASS
	}
	if err == nil {
		// 表明此时阿里云限流，返回限流的错误
		return nil, newAliyunExceedQuotaError()
	}
	task := userapi.TextScanTask{
		Text:    []string{param.Content},
		Scene:   scan.SceneIM,
		UserID:  param.UserID,
		EquipID: param.BUVID, // param.Check 已经将 EquipID 转换成 BUVID
		IP:      param.IP,
		AdFree:  param.AllowAD,
	}
	resp, aliRes, err := userapi.CheckUserTexts(task)
	if err != nil {
		return nil, err
	}
	response := make([]*scan.BaseCheckResult, len(resp))
	for i := range resp {
		response[i] = userapi.ConvertScanResultToBase(resp[i])
	}
	param.SetScanResult(resp, aliRes)
	util.Go(func() {
		_, err = service.Gaia.Call(servicegaia.APIRuleCheck, param)
		if err != nil {
			logger.Error(err)
			return
		}
	})
	return response, nil
}

func isAliyunExceedQuotaError(err error) bool {
	if err == nil {
		return false
	}
	apiError, ok := err.(*serviceutil.APIError)
	return ok && apiError.Status == antispam.StatusExceedQuota
}

func newAliyunExceedQuotaError() error {
	return &serviceutil.APIError{Status: antispam.StatusExceedQuota, Message: ""}
}

func newSmartsheetParam(param *gaia.ParamLiveIM, resp []*scan.BaseCheckResult) *smartsheet.SheetAddRecordParam {
	if param.Scene == gaia.IMSceneMessage {
		return nil
	}
	// 获取用户及主播昵称
	userName := ""
	roomCreatorName := ""
	userIDs := make([]int64, 0, 2)
	if param.MID != 0 {
		userIDs = append(userIDs, param.MID)
	}
	if param.RoomCreatorID != 0 {
		userIDs = append(userIDs, param.RoomCreatorID)
	}
	if len(userIDs) != 0 {
		userIDs = sets.Uniq(userIDs)
		userMap, err := user.FindSimpleMap(userIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		for _, u := range userMap {
			if u.ID == param.MID {
				userName = u.UserName
			}
			if u.ID == param.RoomCreatorID {
				roomCreatorName = u.UserName
			}
		}
	}
	if param.Scene == gaia.IMSceneHorn {
		checkPassMsg := "通过"
		for _, r := range resp {
			if !r.Pass || util.HasElem(r.Labels, scan.LabelEvil) {
				checkPassMsg = "不通过"
				break
			}
		}
		return &smartsheet.SheetAddRecordParam{
			Channel: "im_review_" + param.Scene,
			Fields:  []string{"直播间号", "主播昵称", "用户 ID", "用户昵称", "文本内容", "发送时间", "机审"},
			Values: []string{strconv.FormatInt(param.RoomID, 10), roomCreatorName,
				strconv.FormatInt(param.MID, 10), userName, param.Content, util.TimeNow().Format(util.TimeFormatHMS), checkPassMsg},
		}
	}
	// TODO: 评论弹幕词库改为直播专用词库
	// 检查是否命中审核词库
	matched, matchedWord, err := forbiddenwords.GetMatchedForbiddenWord(forbiddenwords.ForbiddenWordTypeCommentNotice, param.ParamFilter.Content)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 其他场景只有命中审核词时才推送
	if !matched {
		return nil
	}
	switch param.Scene {
	case gaia.IMSceneDanmaku, gaia.IMSceneLuckyBag, gaia.IMSceneQuestion:
		return &smartsheet.SheetAddRecordParam{
			Channel: "im_review_" + param.Scene,
			Fields:  []string{"直播间号", "主播昵称", "用户 ID", "用户昵称", "文本内容", "命中词", "发送时间"},
			Values: []string{strconv.FormatInt(param.RoomID, 10), roomCreatorName,
				strconv.FormatInt(param.MID, 10), userName, param.Content, matchedWord, util.TimeNow().Format(util.TimeFormatHMS),
			},
		}
	default:
		return nil
	}
}

func pushToSmartsheet(param *gaia.ParamLiveIM, resp []*scan.BaseCheckResult) bool {
	addRecordParam := newSmartsheetParam(param, resp)
	if addRecordParam == nil {
		return false
	}
	err := service.Smartsheet.AddRecord(addRecordParam)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}
