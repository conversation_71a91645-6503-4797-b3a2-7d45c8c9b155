package scan

import (
	"net"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// scenes
const (
	sceneVote     = "vote"
	sceneCoupon   = "coupon" // 营销场景
	sceneComment  = "comment"
	sceneLogin    = "login"
	sceneRegister = "register"
)

type riskParam struct {
	UserID    int64  `json:"user_id"`
	IP        string `json:"ip"`
	Scene     string `json:"scene"`
	Mobile    string `json:"mobile"`
	Email     string `json:"email"`
	RegionNum int    `json:"region_num"`
	BUVID     string `json:"buvid"`
}

// ActionRisk 封装阿里云的风险识别
/**
 * @api {post} /rpc/scan/risk 风险识别检测
 * @apiDescription 封装阿里云的注册、营销活动风险识别，根据文档的说法，可用于非高危行为的检测。\
 *                 当应用场景为 register 时，手机号和邮箱至少传递一个，其他场景不用传递。
 * @apiName risk
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {string="vote","coupon","comment","register"} scene 应用场景
 * @apiParam {String} [mobile] 注册手机号
 * @apiParam {Number} [region_num] 地区代码
 * @apiParam {String} [email] 注册邮箱
 * @apiParam {Number} [user_id] 用户 ID, 非注册场景必传
 * @apiParam {String} [buvid] BUVID 设备号, 投票、营销场景必传
 *
 * @apiParamExample {json} 投票风险识别
 *   {
 *     "scene": "vote"
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "score": 35.0, // 风险值
 *       "pass": true,
 *       "labels": []
 *     }
 *   }
 */
func ActionRisk(c *handler.Context) (handler.ActionResponse, error) {
	var param riskParam
	param.IP = c.ClientIP()
	err := c.BindJSON(&param)
	if err != nil {
		return nil, ErrIllegalParam
	}
	if len(param.Scene) == 0 {
		return nil, ErrEmptyParam
	}
	// TODO: 调用方传递 buvid 后，移除
	if param.BUVID == "" {
		param.BUVID = c.BUVID()
	}
	switch param.Scene {
	case sceneVote:
		return param.VoteAbuse()
	case sceneCoupon:
		return param.CouponAbuse()
	case sceneComment:
		return param.CommentAbuse()
	case sceneRegister:
		return param.RegisterAbuse()
	case sceneLogin:
		// TODO: finish this
		fallthrough
	default:
		return nil, ErrIllegalParam
	}
}

func (param *riskParam) CheckCouponAbuse() (*scan.BaseCheckResult, error) {
	acc, err := service.SSO.UserInfo(param.UserID)
	if err != nil {
		logger.Error(err)
		// TODO: 封装 err
		return nil, err
	}
	res, err := service.AntiSpam.CheckCouponAbuse(acc.ID, acc.Email, acc.Mobile, acc.Region, param.IP)
	if err != nil {
		return nil, err
	}
	baseRes := userapi.ConvertScanResultToBase(res)
	return baseRes, nil
}

func (param *riskParam) VoteAbuse() (handler.ActionResponse, error) {
	if param.UserID == 0 || net.ParseIP(param.IP) == nil {
		return nil, ErrEmptyParam
	}
	baseRes, err := param.CheckCouponAbuse()
	if err != nil {
		return nil, err
	}
	// NOTICE: 业务上的逻辑会引起投票一次多次记录 IP/BUVID 计数的情况
	// 比如第一次请求风险识别 -> 业务认为需要滑动验证 -> 滑动验证成功后第二次请求风险识别
	ipEvil := param.addVoteIPLimit()
	buvidEvil := param.addVoteBUVIDLimit()
	if ipEvil || buvidEvil {
		baseRes.Labels = append(baseRes.Labels, scan.LabelEvil)
	}
	return baseRes, nil
}

// CouponAbuse 营销风险识别
func (param *riskParam) CouponAbuse() (handler.ActionResponse, error) {
	if param.UserID == 0 || net.ParseIP(param.IP) == nil {
		return nil, ErrEmptyParam
	}
	baseRes, err := param.CheckCouponAbuse()
	if err != nil {
		return nil, err
	}
	return baseRes, nil
}

const (
	voteLimitPerDay       = 20
	voteKeyExpireDuration = 2 * 24 * time.Hour
)

func (param *riskParam) addVoteIPLimit() (evil bool) {
	now := util.TimeNow()
	keyIP := serviceredis.LockVoteIPLimit2.Format(param.IP, now.Day())
	pipe := service.Redis.TxPipeline()
	countIPCmd := pipe.Incr(keyIP)
	pipe.Expire(keyIP, voteKeyExpireDuration)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return countIPCmd.Val() > voteLimitPerDay
}

func (param *riskParam) addVoteBUVIDLimit() (evil bool) {
	if param.BUVID == "" {
		// web 端 BUVID 为空, 直接返回
		return
	}
	now := util.TimeNow()
	keyBUVID := serviceredis.LockVoteBUVIDLimit2.Format(param.BUVID, now.Day())
	pipe := service.Redis.TxPipeline()
	countBUVIDCmd := pipe.Incr(keyBUVID)
	pipe.Expire(keyBUVID, voteKeyExpireDuration)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return countBUVIDCmd.Val() > voteLimitPerDay
}

func (param *riskParam) CommentAbuse() (handler.ActionResponse, error) {
	if param.UserID == 0 || net.ParseIP(param.IP) == nil {
		return nil, ErrEmptyParam
	}
	return param.CheckCouponAbuse()
}

func (param *riskParam) RegisterAbuse() (handler.ActionResponse, error) {
	if net.ParseIP(param.IP) == nil || (param.Mobile == "" && param.Email == "") {
		return nil, ErrEmptyParam
	}

	res, err := service.AntiSpam.CheckRegisterAbuse(param.Email, param.Mobile, param.RegionNum, param.IP)
	if err != nil {
		return nil, err
	}
	baseRes := userapi.ConvertScanResultToBase(res)
	return baseRes, nil
}
