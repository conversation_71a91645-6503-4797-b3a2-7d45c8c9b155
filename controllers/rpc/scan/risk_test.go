package scan

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRisk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 注册风险
	input := riskParam{
		IP:        "127.0.0.1",
		Scene:     sceneRegister,
		Mobile:    "13800000000",
		RegionNum: 0,
		Email:     "",
		BUVID:     "test_id",
	}
	c := handler.NewTestContext(http.MethodPost, "", false, input)
	r, err := ActionRisk(c)
	assert.NoError(err)
	res := r.(*scan.BaseCheckResult)
	assert.False(res.Pass)
	assert.GreaterOrEqual(res.Score, float64(85))
	assert.Equal("high", res.Labels[0])

	// 投票风险
	input = riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneVote,
		BUVID:  "test_id",
	}
	c = handler.NewTestContext(http.MethodPost, "", false, input)
	r, err = ActionRisk(c)
	assert.NoError(err)
	res = r.(*scan.BaseCheckResult)
	assert.True(res.Pass)

	i := input
	i.Scene = ""
	c = handler.NewTestContext(http.MethodPost, "", false, i)
	_, err = ActionRisk(c)
	assert.Equal(ErrEmptyParam, err.(*handler.ActionError))

	i = input
	i.UserID = 0
	c = handler.NewTestContext(http.MethodPost, "", false, i)
	_, err = ActionRisk(c)
	assert.Equal(ErrEmptyParam, err.(*handler.ActionError))

	input.UserID = -999 // 不存在的用户 ID
	c = handler.NewTestContext(http.MethodPost, "", false, input)
	_, err = ActionRisk(c)
	assert.EqualError(err, "sso: 参数不可为空")

	input.Scene = sceneLogin
	c = handler.NewTestContext(http.MethodPost, "", false, input)
	_, err = ActionRisk(c)
	assert.Equal(ErrIllegalParam, err.(*handler.ActionError))

	// 投票超出设备限制
	input = riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneVote,
		Mobile: "13800000000",
		BUVID:  "test_buvid",
	}
	keyBUVID := serviceredis.LockVoteBUVIDLimit2.Format(input.BUVID, util.TimeNow().Day())
	service.Redis.Set(keyBUVID, voteLimitPerDay, 10*time.Second)
	c = handler.NewTestContext(http.MethodPost, "", false, input)
	r, err = ActionRisk(c)
	require.NoError(err)
	res = r.(*scan.BaseCheckResult)
	assert.True(res.Pass)
	assert.Contains(res.Labels, scan.LabelEvil)

	// 营销风险
	input = riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneCoupon,
		BUVID:  "test_id",
	}
	c = handler.NewTestContext(http.MethodPost, "", false, input)
	r, err = ActionRisk(c)
	assert.NoError(err)
	res = r.(*scan.BaseCheckResult)
	assert.True(res.Pass)
}

func TestCheckCouponAbuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	input := riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneComment,
		Mobile: "13800000000",
	}
	r, err := input.CheckCouponAbuse()
	require.NoError(err)
	assert.True(r.Pass)
}

func TestVoteAbuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	input := riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneVote,
		Mobile: "13800000000",
		BUVID:  "test_xxx_xx",
	}
	r, err := input.VoteAbuse()
	require.NoError(err)
	res := r.(*scan.BaseCheckResult)
	assert.True(res.Pass)
}

func TestAddVoteIPLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	ip := "123456789"
	keyIP := serviceredis.LockVoteIPLimit2.Format(ip, now.Day())

	param := &riskParam{
		IP: ip,
	}
	require.NoError(service.Redis.Del(keyIP).Err())
	// IP 未超过限制
	assert.False(param.addVoteIPLimit())

	require.NoError(service.Redis.Set(keyIP, voteLimitPerDay, 10*time.Second).Err())
	// IP 超过限制
	assert.True(param.addVoteIPLimit())
}

func TestAddVoteBUVIDLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	buvid := "12345"
	keyBUVID := serviceredis.LockVoteBUVIDLimit2.Format(buvid, now.Day())
	param := &riskParam{
		BUVID: buvid,
	}
	require.NoError(service.Redis.Del(keyBUVID).Err())
	// 设备投票次数未超过限制
	assert.False(param.addVoteBUVIDLimit())

	require.NoError(service.Redis.Set(keyBUVID, voteLimitPerDay, 10*time.Second).Err())
	// 设备投票次数超过限制
	assert.True(param.addVoteBUVIDLimit())
}

func TestRiskParam_CouponAbuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	input := riskParam{
		UserID: 12,
		IP:     "127.0.0.1",
		Scene:  sceneCoupon,
		Mobile: "13800000000",
		BUVID:  "test_xxx_xx",
	}
	r, err := input.CouponAbuse()
	require.NoError(err)
	res := r.(*scan.BaseCheckResult)
	assert.True(res.Pass)
}

func TestCommentAbuse(t *testing.T) {
	assert := assert.New(t)

	input := riskParam{
		IP:     "127.0.0.1",
		Scene:  sceneVote,
		Mobile: "13800000000",
		BUVID:  "test_xxx_xx",
	}
	_, err := input.CommentAbuse()
	assert.Equal(ErrEmptyParam, err)

	input = riskParam{
		UserID: 12,
		Scene:  sceneVote,
		Mobile: "13800000000",
		BUVID:  "test_xxx_xx",
	}
	_, err = input.CommentAbuse()
	assert.Equal(ErrEmptyParam, err)
}

func TestRegisterAbuse(t *testing.T) {
	assert := assert.New(t)
	param := riskParam{
		IP:        "",
		Scene:     "",
		Mobile:    "13112341234",
		Email:     "<EMAIL>",
		RegionNum: 0,
	}
	_, err := param.RegisterAbuse()
	assert.Equal(ErrEmptyParam, err)

	param.Mobile, param.Email = "", ""
	_, err = param.RegisterAbuse()
	assert.Equal(ErrEmptyParam, err)
}
