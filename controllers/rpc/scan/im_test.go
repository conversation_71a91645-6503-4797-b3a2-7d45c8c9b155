package scan

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestActionIM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.Redis.Del(serviceredis.LockScanIMExceedQuota0.Format()).Err())
	c := handler.NewTestContext("POST", "/scan/im", false, handler.M{"user_id": "json 出错"})
	_, err := ActionIM(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", "/scan/im", false, gaia.ParamLiveIM{})
	_, err = ActionIM(c)
	assert.Equal(actionerrors.ErrParams, err, "参数不全")

	testTimeStr := "2020-10-16 12:12:00"
	param := gaia.ParamLiveIM{
		RoomID:        1,
		RoomCatalogID: 104,
		RoomCreatorID: 12,
		MsgID:         "msg-id",
		MsgSentTime:   testTimeStr,
		ParamFilter: gaia.ParamFilter{
			API:       "/api/chatroom/message/send",
			UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
			Content:   "法轮功", // 阿里不能过，gaia 测试环境没配置能过
			EquipID:   "test-gaia",
			IP:        "127.0.0.1",
		},
	}
	c = handler.NewTestContext("POST", "/scan/im", false, param)
	r, err := ActionIM(c)
	assert.NoError(err) // gaia 情况
	require.NotEmpty(r)
	resp := r.([]*scan.BaseCheckResult)
	assert.True(resp[0].Pass)

	config.Conf.AB["scan_im_antispam"] = 1.0
	c = handler.NewTestContext("POST", "/scan/im", false, param)
	r, err = ActionIM(c)
	assert.NoError(err) // 阿里云情况
	require.NotEmpty(r)
	resp = r.([]*scan.BaseCheckResult)
	assert.False(resp[0].Pass)

	require.NoError(service.Redis.SetNX(serviceredis.LockScanIMExceedQuota0.Format(), 1, 5*time.Second).Err())
	c = handler.NewTestContext("POST", "/scan/im", false, param)
	r, err = ActionIM(c)
	assert.NoError(err)
	require.NotEmpty(r)
	resp = r.([]*scan.BaseCheckResult)
	assert.True(resp[0].Pass)
	require.NoError(service.Redis.Del(serviceredis.LockScanIMExceedQuota0.Format()).Err())

	param.RoomCreatorID = 5932350
	c = handler.NewTestContext("POST", "/scan/im", false, param)
	r, err = ActionIM(c)
	assert.NoError(err) // 特殊房间不走阿里云的情况
	require.NotEmpty(r)
	resp = r.([]*scan.BaseCheckResult)
	assert.True(resp[0].Pass)
}

func TestIMAntispam(t *testing.T) {
	assert := assert.New(t)

	resp := []*scan.BaseCheckResult{{Pass: true}}
	assignGaiaRes(resp, []string{"self_view"})
	assert.Equal([]string{"evil"}, resp[0].Labels)
	assert.True(resp[0].Pass)

	assignGaiaRes(resp, []string{"forbid"})
	assert.Equal([]string{"evil", "blacklist"}, resp[0].Labels)
	assert.False(resp[0].Pass)

}

func TestIsAliyunExceedQuotaError(t *testing.T) {
	assert := assert.New(t)

	err := newAliyunExceedQuotaError()
	assert.True(isAliyunExceedQuotaError(err))
	assert.False(isAliyunExceedQuotaError(nil))
}

func TestNewSmartsheetParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不支持的场景
	param := &gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			MID:     testUserID,
			Content: "test content",
		},
		RoomID:        233,
		RoomCreatorID: testUserID2,
		Scene:         gaia.IMSceneDanmaku,
	}
	resp := []*scan.BaseCheckResult{
		{
			Pass: true,
		},
	}
	result := newSmartsheetParam(param, resp)
	assert.Nil(result)

	// 测试支持的场景
	param.Scene = gaia.IMSceneHorn
	result = newSmartsheetParam(param, resp)
	require.NotNil(result)
	assert.Equal([]string{"直播间号", "主播昵称", "用户 ID", "用户昵称", "文本内容", "发送时间", "机审"}, result.Fields)
	require.Len(result.Values, 7)
	assert.Equal("233", result.Values[0])
	assert.NotEmpty(result.Values[1])
	assert.Equal("13", result.Values[2])
	assert.NotEmpty(result.Values[3])
	assert.Equal(param.Content, result.Values[4])
	assert.NotEmpty(result.Values[5])
	assert.Equal("通过", result.Values[6])

	// 测试检查未通过的情况
	param.Scene = gaia.IMSceneHorn
	resp[0].Pass = false
	result = newSmartsheetParam(param, resp)
	require.Len(result.Values, 7)
	assert.Equal("不通过", result.Values[6])
}

func TestPushSmartsheet(t *testing.T) {
	assert := assert.New(t)

	smartsheet.SetMockResult(smartsheet.APISheetAddRecord, smartsheet.SuccessCode, nil)
	service.Smartsheet = smartsheet.NewClient(smartsheet.TestConfig())

	// 测试推送不成功的情况（以场景不支持为例）
	param := &gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:  testUserID,
			Content: "test content",
		},
		RoomID:        233,
		RoomCreatorID: testUserID2,
		Scene:         gaia.IMSceneDanmaku,
	}
	resp := []*scan.BaseCheckResult{
		{
			Pass: false,
		},
	}
	assert.False(pushToSmartsheet(param, resp))

	// 测试推送成功的情况
	param.Scene = gaia.IMSceneHorn
	assert.True(pushToSmartsheet(param, resp))
}
