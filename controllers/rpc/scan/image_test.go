package scan

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

// TODO: 使用 mock 构建阿里云图片检测结果
func TestActionImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/scan/image"
	input := imageParam{
		URL:     "https://img.alicdn.com/tfs/TB1urBOQFXXXXbMXFXXXXXXXXXX-1442-257.png",
		Version: ImageScanVersionV1,
	}
	c := handler.NewRPCTestContext(api, input)
	r, err := ActionImage(c)
	assert.NoError(err)
	res, ok := r.(*scan.BaseCheckResult)
	require.True(ok)
	assert.Equal(true, res.Pass)
}

// TODO: 使用 mock 构建阿里云图片检测结果
func TestActionImageDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/scan/image/detail"
	input := imageParam{
		URL:     "https://img.alicdn.com/tfs/TB1urBOQFXXXXbMXFXXXXXXXXXX-1442-257.png",
		Version: ImageScanVersionV1,
	}
	c := handler.NewRPCTestContext(api, input)
	r, err := ActionImageDetail(c)
	assert.NoError(err)
	res, ok := r.(*scan.CheckResult)
	require.True(ok)
	assert.Equal(true, res.Pass)
	require.Len(res.AliyunMatch, 1)
	assert.NotEmpty(res.AliyunMatch[0].RequestID)
	assert.NotEmpty(res.AliyunMatch[0].TaskID)
}

// TODO: 使用 mock 构建阿里云图片检测结果
func TestScanImage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/scan/image/detail"
	input := imageParam{
		URL:     "https://img.alicdn.com/tfs/TB1urBOQFXXXXbMXFXXXXXXXXXX-1442-257.png",
		Version: ImageScanVersionV1,
	}
	c := handler.NewRPCTestContext(api, input)
	res, err := scanImage(c)
	assert.NoError(err)
	assert.Equal(true, res.Pass)
	require.Len(res.AliyunMatch, 1)
	assert.NotEmpty(res.AliyunMatch[0].RequestID)
	assert.NotEmpty(res.AliyunMatch[0].TaskID)

	// 测试违规图片
	var adLog adminlogger.AdminLog
	require.NoError(service.LogDB.Table(adLog.TableName()).Where("user_id = ?", input.UserID).Delete("").Error)
	input.UserID = 233
	input.URL = "https://static-test.maoercdn.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg"
	input.Version = ImageScanVersionV1
	c = handler.NewRPCTestContext(api, input)
	res, err = scanImage(c)
	assert.NoError(err)
	assert.False(res.Pass)
	require.NoError(service.LogDB.Table(adLog.TableName()).Where("user_id = ?", input.UserID).Find(&adLog).Error)
	require.Len(res.AliyunMatch, 1)
	assert.NotEmpty(res.AliyunMatch[0].RequestID)
	assert.NotEmpty(res.AliyunMatch[0].TaskID)
	require.NoError(service.LogDB.Table(adLog.TableName()).Where("user_id = ?", input.UserID).Delete("").Error)

	imageParamV2Input := imageParam{
		URL:    "https://static-test.maoercdn.com/avatars/202006/19/9fd20aa0b358f810d42234c1ec815606105211.jpg",
		UserID: 771,
	}
	c = handler.NewRPCTestContext(api, imageParamV2Input)
	res, err = scanImage(c)
	assert.NoError(err)
	assert.False(res.Pass)
	require.NoError(service.LogDB.Table(adLog.TableName()).Where("user_id = ?", imageParamV2Input.UserID).Find(&adLog).Error)
	require.Len(res.AliyunMatch, 1)
	assert.NotEmpty(res.AliyunMatch[0].RequestID)
}
