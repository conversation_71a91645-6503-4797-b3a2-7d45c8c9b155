package scan

import (
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestActionTextPrivateMessageAdmin(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	testID := 12344321
	testIDstr := strconv.Itoa(testID)
	assignment := role.AuthAssignment{
		UserID:   testIDstr,
		ItemName: string(role.AuditGroup),
	}
	err := service.DB.FirstOrCreate(&assignment, assignment).Error
	require.NoError(err)

	input := userapi.TextScanTask{
		Text:   []string{"text"},
		Scene:  scan.ScenePrivateMessage,
		UserID: int64(testID),
	}
	c := handler.NewTestContext("POST", "/", true, input)
	r, err := ActionText(c)
	require.NoError(err)
	results := r.([]*scan.BaseCheckResult)
	require.Len(results, 1)
	require.NotNil(results[0])
	require.Len(results[0].Labels, 1)
	assert.Equal("admin", results[0].Labels[0])
}

func TestActionText(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	c := handler.NewRPCTestContext("/scan/text", nil)
	_, err := ActionText(c)
	require.Error(err)

	// for testing only
	oldLimitUserAd := userapi.LimitUserAd
	userapi.LimitUserAd = 2
	defer func() { userapi.LimitUserAd = oldLimitUserAd }()

	adStr := "加微信号：353553545, 加 qq 337845818, +vx 冲zuan"

	c = handler.NewRPCTestContext("/scan/text",
		handler.M{"text": []string{"正常文案", "nmsl", adStr}})
	ret, err := ActionText(c)
	require.NoError(err)
	results, ok := ret.([]*scan.BaseCheckResult)
	require.True(ok)

	require.Len(results, 3)
	expected := []*scan.BaseCheckResult{
		{Pass: true, Labels: []string{"normal"}},
		{Pass: false, Labels: []string{"customized"}},
		{Pass: false, Labels: []string{"ad"}},
	}
	for i := range expected {
		assert.Equal(expected[i], results[i], i)
	}

	testUserID := int64(337845818)
	// clean up limit
	cleanUpUserAdLimit(testUserID)

	// redefine old user
	o := userapi.LimitUserAdRegistTime
	userapi.LimitUserAdRegistTime = time.Hour * 24 * 365 * 20 // 20 years
	defer func() { userapi.LimitUserAdRegistTime = o }()

	keyUser := serviceredis.KeyTextScanUserAdLimit1.Format(testUserID)
	require.NoError(service.Redis.Set(keyUser,
		strconv.FormatInt(userapi.LimitUserAd-1, 10), time.Minute).Err())
	for i := int64(0); i < 2; i++ {
		c := handler.NewRPCTestContext("/scan/text",
			handler.M{
				"text":    []string{adStr},
				"scene":   scan.ScenePrivateMessage,
				"user_id": testUserID,
			},
		)
		ret, err = ActionText(c)
		require.NoError(err)
		results, ok = ret.([]*scan.BaseCheckResult)
		require.True(ok)
		require.Len(results, 1)
		assert.Equal(i == 0, results[0].Pass, i)
		assert.Equal([]string{"ad"}, results[0].Labels, i)
	}

	// ad free
	c = handler.NewRPCTestContext("/scan/text", handler.M{
		"text":    []string{adStr},
		"scene":   scan.ScenePrivateMessage,
		"user_id": testUserID,
		"ad_free": true,
	})
	ret, err = ActionText(c)
	require.NoError(err)
	results, ok = ret.([]*scan.BaseCheckResult)
	require.True(ok)
	assert.Equal([]*scan.BaseCheckResult{
		{Pass: true, Labels: []string{"ad"}},
	}, results)
}

func cleanUpUserAdLimit(userID int64) {
	_, err := service.Redis.Del(serviceredis.KeyTextScanUserAdLimit1.Format(userID)).Result()
	if err != nil {
		panic(err)
	}
}

func TestActionTextDetail(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	ctx := handler.NewTestContext("POST", "", false, strings.NewReader(`{"text": ["妈的 卧槽 杂种 呆逼", "日赚 300"]}`))
	ret, err := ActionTextDetail(ctx)
	require.NoError(err)
	results, ok := ret.([]*scan.CheckResult)
	require.True(ok)
	require.Equal(2, len(results))
	require.NotNil(results[0])
	require.NotNil(results[1])
	assert.NotNil(results[0].AliyunMatch)
	assert.NotNil(results[1].BlacklistMatch)
	ctx = handler.NewTestContext("POST", "", false, strings.NewReader(`{"text": ["你好", "哈哈"]}`))
	ret, err = ActionTextDetail(ctx)
	require.NoError(err)
	results, ok = ret.([]*scan.CheckResult)
	require.True(ok)
	require.Equal(2, len(results))
	require.NotNil(results[0])
	require.NotNil(results[1])
	require.Len(results[0].Labels, 1)
	assert.True(results[0].Pass)
	assert.Equal("normal", results[0].Labels[0])
	require.Len(results[1].Labels, 1)
	assert.True(results[1].Pass)
	assert.Equal("normal", results[1].Labels[0])
}
