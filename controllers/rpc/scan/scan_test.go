package scan

import (
	"bytes"
	"encoding/json"
	"testing"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/config/ab"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	config.Conf.AB = make(ab.Config)
	service.InitTest()
	m.Run()
}

func paramToRequestBody(param interface{}) *bytes.Buffer {
	data, _ := json.Marshal(param)
	return bytes.NewBuffer(data)
}
