package scan

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"
	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/anmsg"
	"github.com/MiaoSiLa/missevan-go/models/balance"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	testUserID  = 13
	testUserID2 = 14
)

// clearTransactionLogData 删除消费记录
func deleteTestBalanceData(userID []int64) {
	balance.Balance{}.DB().Where("id IN (?)", userID).Update("all_consumption", 0)
}

// createTestLive 创建直播间
func createTestLive(id int64) {
	timeStamp := util.TimeNow().Unix()
	l := models.Live{
		ID:           id,
		RoomID:       timeStamp,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
		Title:        "直播间标题",
		UserID:       id,
	}
	// 创建直播间
	service.DB.Save(&l)
}

// TestCheckPM 测试私信过滤
func TestCheckPM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clearUserID := []int64{testUserID, testUserID2}
	deleteTestBalanceData(clearUserID)
	deleteTestCheckUserSendCount(testUserID2)
	deleteMsgLimit(testUserID, "127.0.0.1")

	liveCheckResp := &userapi.LiveCheckPMResp{
		Status:      userapi.SendStatusAllow,
		HasLuckyBag: false,
	}
	cancel := mrpc.SetMock(userapi.URILiveCheckPM, func(input interface{}) (interface{}, error) {
		return liveCheckResp, nil
	})
	defer cancel()

	// 发送缺少参数的数据
	data := checkPMParams{
		FromUserID: testUserID2,
		ToUserID:   0,
		Text:       "哈哈你们好",
	}
	url := "/rpc/scan/check-pm"
	c := handler.NewRPCTestContext(url, data)
	result, err := ActionCheckPM(c)
	assert.Equal("参数错误", err.Error())
	assert.Nil(result)

	err = service.Redis.ZAdd(serviceredis.KeyBlackList0.Format(), &redis.Z{
		Member: testUserID2,
		Score:  float64(util.TimeNow().Add(10 * time.Minute).Unix()),
	}).Err()
	require.NoError(err)
	data = checkPMParams{
		FromUserID: testUserID2,
		ToUserID:   testUserID,
		Text:       "哈哈你们好",
	}
	c = handler.NewRPCTestContext(url, data)
	_, err = ActionCheckPM(c)
	assert.EqualError(err, "您的账号有可疑记录，暂时被系统封停")

	// 触发达到发送私信人数上限
	err = service.Redis.ZRem(serviceredis.KeyBlackList0.Format(), testUserID2).Err()
	require.NoError(err)
	messageSendUserLimit := make([]string, messageSendUserLimitDaily)
	for i := 0; i < messageSendUserLimitDaily; i++ {
		messageSendUserLimit[i] = strconv.Itoa(i + 500)
	}
	key := serviceredis.KeyMessageSendUserID1.Format(data.FromUserID)
	service.Redis.SAdd(key, messageSendUserLimit)
	c = handler.NewRPCTestContext(url, data)
	_, err = ActionCheckPM(c)
	assert.Equal(actionerrors.ErrSendMessageUserCountLimit, err)

	// 解除限制
	require.NoError(service.Redis.Del(key).Err())
	// 取消关注
	require.NoError(person.Unfollow(data.ToUserID, data.FromUserID))
	// 删除私信
	require.NoError(service.MessageDB.Delete(anmsg.AnMsg{}, "small_id = ? AND big_id = ?", testUserID, testUserID2).
		Error)
	// 插入数据
	msg := &anmsg.AnMsg{
		SmallID: testUserID,
		BigID:   testUserID2,
		Msg:     "BigID 给 SmallID 发私信",
		Status:  anmsg.StatusPosterBigID}
	require.NoError(service.MessageDB.Create(msg).Error)

	// 测试【给未关注我的人发私信，在收信人关注我或回复我前，仅可发送 1 条消息】
	c = handler.NewRPCTestContext(url, data)
	_, err = ActionCheckPM(c)
	assert.Equal(actionerrors.ErrSendMsgNotReplyLimit, err)

	// 将用户设置为不限制发送
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", data.FromUserID).
		UpdateColumn("confirm", gorm.Expr("confirm | ?", user.ConfirmMsgNoLimit))

	// 设置高消费用户
	balanceInfo := balance.Balance{AllConsumption: allowPMConsumptionCoinLimit}
	balance.Balance{}.DB().Where("id = ?", data.ToUserID).Update(&balanceInfo)
	// 删除用户消费记录，设置注册时间为两周内，删除直播间
	balance.Balance{}.DB().Where("id = ?", testUserID2).Update("all_consumption", 0)
	timeStamp := util.TimeNow().Unix()
	userInfo := user.MowangskUser{CTime: timeStamp}
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID2).Update(&userInfo)
	service.DB.Delete(&models.Live{ID: data.FromUserID})

	// 给高消费用户发送私信
	c = handler.NewRPCTestContext(url, data)
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusShamAllow, result.(checkPMResponse).Status)

	// 测试实物福袋中奖用户不限制给高消费用户发送私信
	liveCheckResp.HasLuckyBag = true
	c = handler.NewRPCTestContext(url, data)
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)

	// 增加用户 testUserID2 消费金额
	balance.Balance{}.DB().Where("id = ?", testUserID2).Update("all_consumption", 1000)
	c = handler.NewRPCTestContext(url, data)
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)

	// 删除用户消费金额
	balance.Balance{}.DB().Where("id = ?", testUserID2).Update("all_consumption", 0)

	// 设置用户注册时间大于 2 周
	userInfo = user.MowangskUser{CTime: timeStamp - 2592000}
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID2).Update(&userInfo)
	c = handler.NewRPCTestContext(url, data)
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)

	// 重新设置注册时间为两周内
	userInfo = user.MowangskUser{CTime: timeStamp}
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID2).Update(&userInfo)

	// 创建直播间
	createTestLive(testUserID2)
	c = handler.NewRPCTestContext(url, data)
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)

	// 违规词屏蔽
	wordsKey := forbiddenwords.RedisKey(forbiddenwords.ForbiddenWordTypePrivateMessageFakeSend)
	cache.New(5*time.Minute, 10*time.Minute).Delete(wordsKey)
	service.Redis.SAdd(wordsKey, "测试屏蔽词")
	data.Text = "测试屏蔽词hhhhh"
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusShamAllow, result.(checkPMResponse).Status)
	data.Text = "你们好"

	// 删除用户直播间
	liveCheckResp.HasLuckyBag = false
	service.DB.Delete(&models.Live{ID: testUserID2})
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusShamAllow, result.(checkPMResponse).Status)

	// 清理消费记录
	deleteTestBalanceData(clearUserID)
	deleteTestCheckUserSendCount(testUserID2)

	// 将用户设置为不限制发送
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", data.FromUserID).
		UpdateColumn("confirm", gorm.Expr("confirm | ?", user.ConfirmMsgNoLimit))
	// 触发私信频率检测
	deleteMsgLimit(testUserID2, "127.0.0.1")
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)
	userIDKey := serviceredis.LockMessageSendUserID2.Format(testUserID2, util.TimeNow().Minute())
	service.Redis.Set(userIDKey, userIDLimitPerMinute, 0)
	// 触发用户 1 分钟发送私信阀值
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	_, err = ActionCheckPM(c)
	assert.Equal(actionerrors.ErrSendMessageLimit, err)

	deleteMsgLimit(testUserID2, "127.0.0.1")
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	result, err = ActionCheckPM(c)
	require.NoError(err)
	assert.Equal(sendStatusAllow, result.(checkPMResponse).Status)
	ipKey := serviceredis.LockMessageSendUserID2.Format(testUserID2, util.TimeNow().Minute())
	service.Redis.Set(ipKey, ipLimitPerMinute, 0)
	// 触发 ip 1 分钟发送私信阀值
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/check-from-user-condition", false, data)
	c.C.Request.Header.Set("X-Forwarded-For", "127.0.0.1")
	_, err = ActionCheckPM(c)
	assert.Equal(actionerrors.ErrSendMessageLimit, err)

	deleteMsgLimit(testUserID2, "127.0.0.1")
}

// TestCheckHighConsumptionUser 高消费接收用户流程
func TestCheckHighConsumptionUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := checkPMParams{
		FromUserID: testUserID2,
		ToUserID:   testUserID,
	}
	// 设置高消费用户
	balanceInfo := balance.Balance{AllConsumption: allowPMConsumptionCoinLimit}
	balance.Balance{}.DB().Where("id = ?", params.ToUserID).Update(&balanceInfo)

	// 删除用户消费记录，设置注册时间为两周内，删除直播间
	balance.Balance{}.DB().Where("id = ?", params.FromUserID).Update("all_consumption", 0)
	timeStamp := util.TimeNow().Unix()
	userInfo := user.MowangskUser{CTime: timeStamp}
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", params.FromUserID).Update(&userInfo)
	service.DB.Delete(&models.Live{ID: params.FromUserID})

	result, err := checkHighConsumptionRules(&params)
	require.NoError(err)
	assert.False(result)

	// 还原用户消费
	balance.Balance{}.DB().Where("id = ?", testUserID).Update("all_consumption", 0)

	result, err = checkHighConsumptionRules(&params)
	assert.NoError(err)
	assert.True(result)
}

// TestCheckLiveRoom 检查用户是否为主播
func TestLiveExists(t *testing.T) {
	assert := assert.New(t)
	// 删除直播间
	service.DB.Delete(&models.Live{ID: testUserID})
	result := liveExists(testUserID)
	assert.False(result)

	createTestLive(testUserID)
	result = liveExists(testUserID)
	assert.True(result)

	// 删除直播间
	service.DB.Delete(&models.Live{ID: testUserID})
}

// TestCheckUserCreateTime 检查用户注册时间是否大于两星期
func TestCheckUserCreateTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 设置用户时间为两周内
	now := util.TimeNow()
	userInfo := user.MowangskUser{CTime: now.Unix()}
	err := service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).Update(&userInfo).Error
	require.NoError(err)
	result, err := checkUserRegisterDuration(testUserID, allowPMConsumptionCoinLimit)
	require.NoError(err)
	assert.False(result)

	// 设置用户注册时间大于 2 周
	userInfo = user.MowangskUser{CTime: now.Add(-allowPMConsumptionCoinLimit * time.Second).Add(-time.Second).Unix()}
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).Update(&userInfo).Error
	require.NoError(err)
	result, err = checkUserRegisterDuration(testUserID, allowPMConsumptionCoinLimit)
	require.NoError(err)
	assert.True(result)
}

// TestCheckUserExistConsumption 用户是否存在消费
func TestCheckUserExistConsumption(t *testing.T) {
	assert := assert.New(t)
	// 清理消费
	balance.Balance{}.DB().Where("id = ?", testUserID).Update("all_consumption", 0)
	result := checkUserExistConsumption(testUserID)
	assert.False(result)

	// 给用户增加消费
	balance.Balance{}.DB().Where("id = ?", testUserID).Update("all_consumption", 10)

	result = checkUserExistConsumption(testUserID)
	assert.True(result)
	balance.Balance{}.DB().Where("id = ?", testUserID).Update("all_consumption", 0)
}

// deleteTestCheckUserSendCount 清理检查用户发送私信人数的测试数据
func deleteTestCheckUserSendCount(userID int64) {
	// 将用户从不限制发送列表移除
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", userID).UpdateColumn("confirm", gorm.Expr("confirm &~ ?", user.ConfirmMsgNoLimit))
	// 清理发送列表
	key := serviceredis.KeyMessageSendUserID1.Format(userID)
	service.Redis.Del(key)
}

// TestCheckUserSendCount 检查用户发送私信人数
func TestCheckPMParams_checkUserSendCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理数据
	deleteTestCheckUserSendCount(testUserID)
	params := checkPMParams{
		FromUserID: testUserID,
		ToUserID:   51,
	}
	result, err := params.checkUserSendCount()
	require.NoError(err)
	assert.True(result)

	a := make([]interface{}, messageSendUserLimitDaily)
	for i := 0; i < messageSendUserLimitDaily; i++ {
		a[i] = strconv.Itoa(i)
	}
	key := serviceredis.KeyMessageSendUserID1.Format(params.FromUserID)
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	err = service.Redis.SAdd(key, a...).Err()
	require.NoError(err)
	result, err = params.checkUserSendCount()
	require.NoError(err)
	assert.False(result)

	service.Redis.SAdd(key, params.ToUserID)
	result, err = params.checkUserSendCount()
	assert.NoError(err)
	assert.True(result)

	// 将用户设置为不限制发送
	service.Redis.SRem(key, testUserID2)
	service.DB.Table(user.MowangskUser{}.TableName()).Where("id = ?", testUserID).UpdateColumn("confirm", gorm.Expr("confirm | ?", user.ConfirmMsgNoLimit))
	result, err = params.checkUserSendCount()
	assert.NoError(err)
	assert.True(result)
	// 清理数据
	deleteTestCheckUserSendCount(testUserID)
}

func TestCheckPMParams_isNotFollowingPass(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := checkPMParams{
		FromUserID:             12,
		ToUserID:               13,
		isMsgNoLimit:           true,
		toUserFollowedFromUser: true,
	}
	// 私信人数无限用户
	ok, err := params.isNotFollowingPass()
	require.NoError(err)
	assert.True(ok)

	// 收信人关注发信人
	params.isMsgNoLimit = false
	require.NoError(person.Follow(params.ToUserID, params.FromUserID, nil))
	ok, err = params.isNotFollowingPass()
	require.NoError(err)
	assert.True(ok)

	// 未达限制
	require.NoError(person.Unfollow(params.ToUserID, params.FromUserID))
	params.toUserFollowedFromUser = false
	key := keyMsgUserNotFollowingMeList(params.FromUserID)
	require.NoError(service.Redis.Del(key).Err())
	ok, err = params.isNotFollowingPass()
	require.NoError(err)
	assert.True(ok)

	// 达到限制
	list := make([]interface{}, 0, msgToUserNotFollowingLimitDaily)
	pipe := service.Redis.Pipeline()
	for i := 0; i < msgToUserNotFollowingLimitDaily; i++ {
		list = append(list, i+100)
	}
	pipe.SAdd(key, list...)
	pipe.Expire(key, time.Minute)
	_, err = pipe.Exec()
	defer func() {
		assert.NoError(service.Redis.Del(key).Err())
	}()
	require.NoError(err)
	ok, err = params.isNotFollowingPass()
	require.NoError(err)
	assert.False(ok)
}

func TestCheckPMParams_addMsgUserNotFollowingMeList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := checkPMParams{
		FromUserID:   12,
		ToUserID:     13,
		isMsgNoLimit: true,
	}
	key := keyMsgUserNotFollowingMeList(params.FromUserID)
	require.NoError(service.Redis.SRem(key, params.ToUserID).Err())

	// 私信人数无限
	params.addMsgUserNotFollowingMeList()
	ok, err := service.Redis.SIsMember(key, params.ToUserID).Result()
	require.NoError(err)
	assert.False(ok)
	// 收信人关注发信人
	params.isMsgNoLimit = false
	params.toUserFollowedFromUser = true
	params.addMsgUserNotFollowingMeList()
	ok, err = service.Redis.SIsMember(key, params.ToUserID).Result()
	require.NoError(err)
	assert.False(ok)
	// 收信人已经在列表中
	params.toUserFollowedFromUser = false
	params.toUserNotFollowingMeListExists = true
	params.addMsgUserNotFollowingMeList()
	ok, err = service.Redis.SIsMember(key, params.ToUserID).Result()
	require.NoError(err)
	assert.False(ok)
	// 收信人未关注发信人
	params.toUserNotFollowingMeListExists = false
	params.addMsgUserNotFollowingMeList()
	ok, err = service.Redis.SIsMember(key, params.ToUserID).Result()
	require.NoError(err)
	assert.True(ok)
}

func TestCheckPMParams_newResp(t *testing.T) {
	assert := assert.New(t)

	params := checkPMParams{
		FromUserID:   12,
		ToUserID:     13,
		isMsgNoLimit: true,
	}
	status := sendStatusShamAllow
	assert.Equal(checkPMResponse{
		Status: status,
		Msg:    "发送成功",
	}, params.newResp(status))
}

func deleteMsgLimit(userID int64, ip string) {
	t := util.TimeNow().Minute()
	userKey := serviceredis.LockMessageSendUserID2.Format(userID, t)
	ipKey := serviceredis.LockMessageSendIP2.Format(ip, t)
	service.Redis.Del(userKey, ipKey)
}

// 检查私信发送频率
func TestSendMsgLimit(t *testing.T) {
	assert := assert.New(t)
	deleteMsgLimit(testUserID, "127.0.0.1")
	result, err := sendMsgLimit(testUserID, "127.0.0.1")
	assert.NoError(err)
	assert.True(result)
	for i := 0; i < userIDLimitPerMinute-1; i++ {
		result, err = sendMsgLimit(testUserID, "127.0.0.1")
		assert.NoError(err)
		assert.True(result)
	}
	// 测试用户触发每分钟发送私信阀值
	result, err = sendMsgLimit(testUserID, "127.0.0.1")
	assert.NoError(err)
	assert.False(result)

	deleteMsgLimit(testUserID, "127.0.0.1")
	for i := 0; i < ipLimitPerMinute-1; i++ {
		deleteMsgLimit(int64(i), "127.0.0.1")
	}
	result, err = sendMsgLimit(testUserID, "127.0.0.1")
	assert.NoError(err)
	assert.True(result)
	for i := 0; i < ipLimitPerMinute-1; i++ {
		result, err = sendMsgLimit(int64(i), "127.0.0.1")
		assert.NoError(err)
		assert.True(result)
	}
	// 测试 IP 触发每分钟私信阀值
	result, err = sendMsgLimit(testUserID, "127.0.0.1")
	assert.NoError(err)
	assert.False(result)
}

// 用户加入发送列表
func TestAddUserSendList(t *testing.T) {
	assert := assert.New(t)
	key := serviceredis.KeyMessageSendUserID1.Format(testUserID)
	service.Redis.Del(key)

	err := addUserSendList(testUserID, testUserID2)
	assert.NoError(err)
	val := service.Redis.SCard(key).Val()
	assert.Equal(int64(1), val)

	// 判断是否在已发送列表
	i := service.Redis.SIsMember(key, testUserID2).Val()
	assert.True(i)
	service.Redis.Del(key)
}

func TestCheckPMEvil(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URILiveCheckPM, func(input interface{}) (interface{}, error) {
		return &userapi.LiveCheckPMResp{
			Status:      userapi.SendStatusAllow,
			HasLuckyBag: false,
		}, nil
	})
	defer cancel()

	param := handler.M{
		"from_user_id": 12,
		"to_user_id":   346286,
		"text":         "evil",
	}
	c := handler.NewTestContext(http.MethodPost, "/check-pm", false, param)
	r, err := ActionCheckPM(c)
	require.NoError(err)
	resp := r.(checkPMResponse)
	assert.Equal("发送成功", resp.Msg)
	assert.Equal(sendStatusShamAllow, resp.Status)

	param["text"] = "live"
	c = handler.NewTestContext(http.MethodPost, "/check-pm", false, param)
	r, err = ActionCheckPM(c)
	require.NoError(err)
	resp = r.(checkPMResponse)
	assert.Equal("发送成功", resp.Msg)
	assert.Equal(sendStatusAllow, resp.Status)
}

func TestLiveCheckPMStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expected := &userapi.LiveCheckPMResp{
		Status:      userapi.SendStatusAllow,
		HasLuckyBag: true,
	}
	cancel := mrpc.SetMock(userapi.URILiveCheckPM, func(input interface{}) (interface{}, error) {
		body := input.(map[string]interface{})
		assert.EqualValues(9074509, body["from_user_id"])
		assert.EqualValues(9074510, body["to_user_id"])
		return expected, nil
	})
	defer cancel()

	param := checkPMParams{
		FromUserID: 9074509,
		ToUserID:   9074510,
	}
	result, err := liveCheckPMStatus(mrpc.UserContext{}, param)
	require.NoError(err)
	require.NotNil(result)
	assert.Equal(expected, result)
}

func TestCheckPMParams_checkFollowedAndReply(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := checkPMParams{
		FromUserID:   12,
		ToUserID:     13,
		isMsgNoLimit: false,
	}

	// 取消关注
	require.NoError(person.Unfollow(params.ToUserID, params.FromUserID))
	// 删除私信
	require.NoError(service.MessageDB.Delete(anmsg.AnMsg{}, "small_id = ? AND big_id = ?", 12, 13).Error)

	// 测试首次发信人给收信人可以发送一条私信
	ok, err := params.checkFollowedAndReply()
	require.NoError(err)
	assert.True(ok)

	// 插入私信数据
	msg := &anmsg.AnMsg{
		SmallID: 12,
		BigID:   13,
		Msg:     "SmallID 给 BigID 发私信",
		Status:  0}
	require.NoError(service.MessageDB.Create(msg).Error)

	// 测试未满足私信打招呼限制，不能发送私信
	ok, err = params.checkFollowedAndReply()
	require.NoError(err)
	assert.False(ok)

	require.NoError(person.Follow(params.ToUserID, params.FromUserID, nil))
	// 测试收信人关注了发信人，发信人可以给收信人无限制发私信
	ok, err = params.checkFollowedAndReply()
	require.NoError(err)
	assert.True(ok)
}
