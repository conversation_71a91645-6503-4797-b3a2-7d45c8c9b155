package person

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	testUserID   = 3013621
	testFollowID = 3013622
)

func TestTagKeys(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(tutil.KeyExists(tutil.JSON, personFollowParam{}, "user_id", "follow_user_id", "follow_from", "follow_type"))

	assert.Empty(tutil.KeyExists(tutil.JSON, personFollowResp{}, "msg", "attention"))
}

func TestIncrUserFollows(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	counterHourKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneHour)
	counterDayKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneDay)
	require.NoError(service.Redis.Del(counterHourKey, counterDayKey).Err())

	incrUserFollows(testUserID)
	count, err := service.Redis.Get(counterHourKey).Int64()
	require.NoError(err)
	assert.Equal(int64(1), count)
	count, err = service.Redis.Get(counterDayKey).Int64()
	require.NoError(err)
	assert.Equal(int64(1), count)

	require.NoError(service.Redis.Set(counterHourKey, 49, time.Minute).Err())
	incrUserFollows(testUserID)
	expireTime, err := service.Redis.TTL(counterHourKey).Result()
	require.NoError(err)
	assert.True(expireTime > time.Hour)
}

func TestCheckCounterUserFollows(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	counterHourKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneHour)
	counterDayKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneDay)

	// 测试用户 1 小时内关注数大于 50
	require.NoError(service.Redis.Set(counterHourKey, 51, time.Hour).Err())
	err := checkCounterUserFollows(testUserID, testFollowID)
	assert.EqualError(err, "关注频率较高，可过一会再试嗷~")

	// 测试用户一天内关注数大于 100
	require.NoError(service.Redis.Del(counterHourKey).Err())
	require.NoError(service.Redis.Set(counterDayKey, 101, time.Hour).Err())
	err = checkCounterUserFollows(testUserID, testFollowID)
	assert.EqualError(err, "已达到每日关注上限~")
}

func TestCheckUsers(t *testing.T) {
	assert := assert.New(t)

	// 测试关注者不存在
	assert.EqualError(checkUsers(1, testFollowID), "用户不存在")

	// 测试被关注者不存在
	assert.EqualError(checkUsers(testUserID, 1), "关注用户不存在")

	// 测试关注者与被关注者均存在
	assert.NoError(checkUsers(testUserID, testFollowID))
}

func TestNewPersonFollowParam(t *testing.T) {
	assert := assert.New(t)

	// 参数错误
	c := handler.NewRPCTestContext("", nil)
	_, err := newPersonFollowParam(c, FollowTypeUnfollow)
	assert.EqualError(err, "参数错误")
}

func TestAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info := person.MAttentionUser{
		UserActive:   testUserID,
		UserPasstive: testFollowID,
	}
	require.NoError(service.DB.Table(info.TableName()).Where("user_active = ?", info.UserActive).Delete("").Error)
	require.NoError(service.DB.Table(info.TableName()).Create(&info).Error)

	param := personFollowParam{
		UserID:       testUserID,
		FollowUserID: testFollowID,
	}
	attr := param.attention()
	assert.True(attr.IsSet(person.AttrFollowing))
}

func TestActionPersonFollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := personFollowParam{
		UserID:       testUserID,
		FollowUserID: testFollowID,
	}

	// 测试加锁
	key := serviceredis.LockUserFollow1.Format(param.UserID)
	require.NoError(service.Redis.SetNX(key, "1", 10*time.Second).Err())
	c := handler.NewRPCTestContext("/rpc/person/follow", param)
	_, err := ActionPersonFollow(c)
	assert.EqualError(err, "操作频繁，请稍后再试")
	require.NoError(service.Redis.Del(key).Err())

	counterHourKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneHour)
	counterDayKey := serviceredis.KeyCounterUserFollows2.Format(testUserID, counterOneDay)
	require.NoError(service.Redis.Del(counterHourKey, counterDayKey).Err())

	// 测试不能关注自己
	param.FollowUserID = param.UserID
	c = handler.NewRPCTestContext("/rpc/person/follow", param)
	_, err = ActionPersonFollow(c)
	assert.EqualError(err, "不能关注自己")
	param.FollowUserID = testFollowID

	// 测试关注用户在对方黑名单中
	blockUser := blackuser.Model{
		BigID:   testFollowID,
		SmallID: testUserID,
	}
	blockUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blockUser.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		Assign(blockUser).FirstOrCreate(&blockUser).Error
	require.NoError(err)
	c = handler.NewRPCTestContext("/rpc/person/follow", param)
	_, err = ActionPersonFollow(c)
	require.EqualError(err, "关注失败，请将对方移除黑名单再试")

	// 测试用户拉黑了关注者
	service.MessageDB.Table(blockUser.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		UpdateColumn("status", blackuser.StatusBigBanSmall)

	c = handler.NewRPCTestContext("/rpc/person/follow", param)
	_, err = ActionPersonFollow(c)
	require.EqualError(err, "由于对方设置，你还不能关注")

	// 删除黑名单关系
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).UpdateColumn("status", 0)

	// 测试正常关注
	service.Databus.AppLogPub.ClearDebugPubMsgs()
	c = handler.NewRPCTestContext("/rpc/person/follow", param)
	c.C.Request.Header.Set("User-Agent", "live-service/1.5.0-8")
	res, err := ActionPersonFollow(c)
	require.NoError(err)
	data := res.(personFollowResp)
	assert.Equal("关注成功", data.Msg)
	assert.True(data.Attention.IsSet(person.AttrFollowing))
	var info person.MAttentionUser
	require.NoError(service.DB.Table(info.TableName()).
		Where("user_active = ? AND user_passtive = ?", param.UserID, param.FollowUserID).Find(&info).Error)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	message := <-pubMsgs
	assert.Equal(keys.DatabusKeyUserFollowLog1.Format(param.UserID), message.Key)
	param.FollowFrom = util.NewInt(person.FollowFromLive)
	expectBytes, err := json.Marshal(followLog{
		UserID:       param.UserID,
		FollowUserID: param.FollowUserID,
		FollowFrom:   *param.FollowFrom,
		FollowType:   param.FollowType,
		CreateTime:   now.Unix(),
	})
	require.NoError(err)
	actualBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, actualBytes)

	// 测试 live databus 是否正常发送了 event
	livePubMsgs := service.Databus.LiveLogPub.DebugPubMsgs()
	liveMessage := <-livePubMsgs
	assert.Equal(keys.DatabusKeyUserFollowLog1.Format(param.UserID), liveMessage.Key)
	liveBytes, err := liveMessage.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, liveBytes)
}

func TestActionPersonUnfollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info1 := person.MAttentionUser{
		UserActive:   testUserID,
		UserPasstive: testFollowID,
	}
	require.NoError(service.DB.Table(info1.TableName()).Where("user_active = ?", info1.UserActive).Delete("").Error)
	require.NoError(service.DB.Table(info1.TableName()).Create(&info1).Error)

	info2 := person.MAttentionUser{
		UserActive:   testFollowID,
		UserPasstive: testUserID,
	}
	require.NoError(service.DB.Table(info2.TableName()).Where("user_active = ?", info2.UserActive).Delete("").Error)
	require.NoError(service.DB.Table(info2.TableName()).Create(&info2).Error)

	param := personFollowParam{
		UserID:       testUserID,
		FollowUserID: testUserID,
		FollowFrom:   util.NewInt(person.FollowFromLive),
	}

	// 测试不能取消关注自己
	c := handler.NewRPCTestContext("/rpc/person/cancelfollow", param)
	_, err := ActionPersonUnfollow(c)
	require.EqualError(err, "不能取消关注自己")

	// 测试正常取关
	param.FollowUserID = testFollowID
	c = handler.NewRPCTestContext("/rpc/person/cancelfollow", param)
	res, err := ActionPersonUnfollow(c)
	require.NoError(err)
	data := res.(personFollowResp)
	assert.Equal("取消关注成功", data.Msg)
	assert.True(data.Attention.IsSet(person.AttrFans))

	require.True(servicedb.IsErrNoRows(service.DB.Table(info1.TableName()).
		Where("user_active = ? AND user_passtive = ?", param.UserID, param.FollowUserID).Find(&info1).Error))
}
