package person

import (
	"context"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/person"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

type personFollowParam struct {
	UserID       int64          `json:"user_id"`
	FollowUserID int64          `json:"follow_user_id"`
	FollowFrom   *int           `json:"follow_from"`
	FollowType   int            `json:"follow_type"`
	EventID      *string        `json:"event_id,omitempty"`
	EventIDFrom  *string        `json:"event_id_from,omitempty"`
	TrackID      *string        `json:"track_id,omitempty"`
	OS           *util.Platform `json:"os,omitempty"`
	AutoFollow   bool           `json:"auto_follow,omitempty"`

	lockKey string
}

type followLog struct {
	UserID       int64 `json:"user_id"`
	FollowUserID int64 `json:"follow_user_id"`
	FollowFrom   int   `json:"follow_from"`

	FollowType int   `json:"follow_type"`
	CreateTime int64 `json:"create_time"`
}

// personFollowResp 接口响应结构
type personFollowResp struct {
	Msg       string       `json:"msg"`
	Attention util.BitMask `json:"attention"`
}

const (
	// counterOneHour 一小时内用户关注数计数器
	counterOneHour = "one_hour"
	// counterOneDay 自然日内用户关注数计数器
	counterOneDay = "one_day"

	// 用户每小时关注上限
	limitOneHourUserFollows = 50
	// 用户每日关注上限
	limitOneDayUserFollows = 100
)

// 关注类型
const (
	FollowTypeFollow = iota
	FollowTypeUnfollow
)

const (
	errLimitOneHourUserFollows = "关注频率较高，可过一会再试嗷~"
	errLimitOneDayUserFollows  = "已达到每日关注上限~"
)

// 关注来源 appName 常量
const (
	appNameWeb  = "missevan-web"
	appNameApp  = "missevan-app"
	appNameLive = "live-service"
)

func incrUserFollows(userID int64) {
	counterHourKey := serviceredis.KeyCounterUserFollows2.Format(userID, counterOneHour)
	counterDayKey := serviceredis.KeyCounterUserFollows2.Format(userID, counterOneDay)
	pipe := service.Redis.TxPipeline()
	// 一小时内用户关注数加 1 并获取关注数
	countHourCmd := pipe.Incr(counterHourKey)
	ttlHourCmd := pipe.TTL(counterHourKey)
	// 自然日内用户关注数加 1
	pipe.Incr(counterDayKey)
	ttlDayCmd := pipe.TTL(counterDayKey)
	if _, err := pipe.Exec(); err != nil {
		logger.Error(err)
		return
	}

	var flagCmds bool
	pipe = service.Redis.Pipeline()
	count, _ := countHourCmd.Result()
	ttl, _ := ttlHourCmd.Result()
	if count >= limitOneHourUserFollows {
		// 用户 1 小时内关注数大于等于 50，则限制其 24 小时内不能关注其他用户
		pipe.Expire(counterHourKey, 24*time.Hour)
		flagCmds = true
	} else if ttl <= time.Duration(-1) {
		// 若 redis key 的 ttl 不存在，则初始化 ttl
		pipe.Expire(counterHourKey, time.Hour)
		flagCmds = true
	}
	ttl, _ = ttlDayCmd.Result()
	if ttl <= time.Duration(-1) {
		now := util.TimeNow()
		timeEnd := util.BeginningOfDay(now).AddDate(0, 0, 1)
		// 初始化自然日内用户关注数 ttl
		pipe.Expire(counterDayKey, timeEnd.Sub(now))
		flagCmds = true
	}
	if flagCmds {
		if _, err := pipe.Exec(); err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

func checkCounterUserFollows(userID, followUserID int64) error {
	counterHourKey := serviceredis.KeyCounterUserFollows2.Format(userID, counterOneHour)
	counterDayKey := serviceredis.KeyCounterUserFollows2.Format(userID, counterOneDay)
	res, err := service.Redis.MGet(counterHourKey, counterDayKey).Result()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if len(res) < 2 {
		return nil
	}
	logFields := logger.Fields{
		"user_id":        userID,
		"follow_user_id": followUserID,
	}
	if counterHourStr, ok := res[0].(string); ok {
		var counterHour int64
		if counterHourStr != "" {
			counterHour, err = strconv.ParseInt(counterHourStr, 10, 64)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		if counterHour >= limitOneHourUserFollows {
			logger.WithFields(logFields).Warn(errLimitOneHourUserFollows)
			return actionerrors.ErrForbidden(handler.CodeUserFollowLimit, errLimitOneHourUserFollows)
		}
	}
	if counterDayStr, ok := res[1].(string); ok {
		var counterDay int64
		if counterDayStr != "" {
			counterDay, err = strconv.ParseInt(counterDayStr, 10, 64)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		if counterDay >= limitOneDayUserFollows {
			logger.WithFields(logFields).Warn(errLimitOneDayUserFollows)
			return actionerrors.ErrForbidden(handler.CodeUserFollowLimit, errLimitOneDayUserFollows)
		}
	}
	return nil
}

func checkUsers(userID, followUserID int64) error {
	users, err := user.FindSimpleMap([]int64{userID, followUserID})
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if _, ok := users[userID]; !ok {
		return actionerrors.ErrBadRequest(handler.CodeUserNotFound, "用户不存在")
	} else if _, ok = users[followUserID]; !ok {
		return actionerrors.ErrForbidden(handler.CodeUserNotFound, "关注用户不存在")
	}
	return nil
}

func newPersonFollowParam(c *handler.Context, followType int) (*personFollowParam, error) {
	var param personFollowParam
	if err := c.BindJSON(&param); err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.FollowUserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	param.FollowType = followType
	// WORKAROUND: 其他服务未接入之前，暂时通过解析 userAgent 判断关注的来源
	param.parseFrom(c.UserAgent())
	if param.UserID == param.FollowUserID {
		switch followType {
		case FollowTypeFollow:
			return nil, actionerrors.ErrBadRequest(handler.CodeCannotOperateSelf, "不能关注自己")
		case FollowTypeUnfollow:
			return nil, actionerrors.ErrBadRequest(handler.CodeCannotOperateSelf, "不能取消关注自己")
		default:
			panic("错误的请求类型")
		}
	}

	// 加锁，避免频繁操作
	param.lockKey = serviceredis.LockUserFollow1.Format(param.UserID)
	lock, err := service.Redis.SetNX(param.lockKey, 1, 2*time.Second).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !lock {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	return &param, nil
}

func (param personFollowParam) cleanup() {
	service.Redis.Del(param.lockKey)
}

func (param personFollowParam) attention() (attr util.BitMask) {
	ok, err := person.HasFollowed(param.FollowUserID, param.UserID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":        param.UserID,
			"follow_user_id": param.FollowUserID,
		}).Error(err)
		// PASS
	} else if ok {
		attr.Set(person.AttrFans)
	}
	if param.FollowType == FollowTypeFollow {
		attr.Set(person.AttrFollowing)
	}
	return
}

func (param personFollowParam) buildMore() person.MAttentionUserMore {
	return person.MAttentionUserMore{
		EventID:     param.EventID,
		EventIDFrom: param.EventIDFrom,
		TrackID:     param.TrackID,
		OS:          param.OS,
		AutoFollow:  param.AutoFollow,
	}
}

func (param *personFollowParam) parseFrom(ua string) {
	if param.FollowFrom != nil {
		if !util.HasElem([]int{person.FollowFromDefault, person.FollowFromWeb,
			person.FollowFromApp, person.FollowFromLive, person.FollowFromGameDownload}, *param.FollowFrom) {
			logger.Errorf("关注来源 %d 有误，User-Agent: %s", *param.FollowFrom, ua)
		}
		return
	}

	appName, _ := serviceutil.ParseUserAgent(ua)
	switch appName {
	case appNameWeb:
		param.FollowFrom = util.NewInt(person.FollowFromWeb)
	case appNameApp:
		param.FollowFrom = util.NewInt(person.FollowFromApp)
	case appNameLive:
		param.FollowFrom = util.NewInt(person.FollowFromLive)
	default:
		// 包括 appName 为空
		param.FollowFrom = util.NewInt(person.FollowFromDefault)
	}
}

// sendLog 生产数据到 databus
func (param personFollowParam) sendLog() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	key := keys.DatabusKeyUserFollowLog1.Format(param.UserID)
	log := &followLog{
		UserID:       param.UserID,
		FollowUserID: param.FollowUserID,
		FollowFrom:   *param.FollowFrom,
		FollowType:   param.FollowType,
		CreateTime:   util.TimeNow().Unix(),
	}
	if err := service.Databus.AppLogPub.Send(ctx, key, log); err != nil {
		logger.Error(err)
		// PASS
	}
	if err := service.Databus.LiveLogPub.Send(ctx, key, log); err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionPersonFollow 关注
/**
 * @api {post} /rpc/person/follow 关注
 * @apiVersion 0.1.0
 * @apiName follow
 * @apiGroup rpc/person
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} follow_user_id 需要关注的用户 ID
 * @apiParam {Number} follow_from 关注来源 0: 其他; 1: Web; 2: App; 3: 直播
 * @apiParam {String} [event_id] 事件 ID
 * @apiParam {String} [event_id_from] 事件来源 ID
 * @apiParam {String} [track_id] 跟踪 ID
 * @apiParam {Number} [os] 设备操作系统，由上游接口通过 equipment 信息获取后传入
 * @apiParam {Boolean} [auto_follow] 是否系统自动关注。true 表示自动关注，false 或不存在表示手动关注
 *
 * @apiSuccess {String} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "关注成功",
 *         "attention": 1 // 1：当前用户已关注被访问用户；3：已互粉
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020005
 * @apiError (403) {String} info 操作频繁，请稍后再试
 *
 * @apiError (400) {Number} code 200020002
 * @apiError (400) {String} info 不能关注自己
 *
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 由于对方设置，你还不能关注
 *
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 关注失败，请将对方移除黑名单再试
 *
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 关注频率较高，可过一会再试嗷~
 *
 * @apiError (403) {Number} code 100010007
 * @apiError (403) {String} info 已达到每日关注上限~
 */
func ActionPersonFollow(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPersonFollowParam(c, FollowTypeFollow)
	if err != nil {
		return nil, err
	}
	defer param.cleanup()

	// 限制用户关注数
	err = checkCounterUserFollows(param.UserID, param.FollowUserID)
	if err != nil {
		return nil, err
	}

	if err = checkUsers(param.UserID, param.FollowUserID); err != nil {
		return nil, err
	}
	// 查看权限
	u1BlockU2, u2BlockU1, err := blackuser.GetBlacklistRelation(param.UserID, param.FollowUserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if u2BlockU1 {
		return nil, actionerrors.ErrForbidden(handler.CodeBlockedUserByOthers, "由于对方设置，你还不能关注")
	} else if u1BlockU2 {
		return nil, actionerrors.ErrForbidden(handler.CodeBlockedUser, "关注失败，请将对方移除黑名单再试")
	}

	more := param.buildMore()
	if err = person.Follow(param.UserID, param.FollowUserID, &more); err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	incrUserFollows(param.UserID)
	attr := param.attention()
	param.sendLog()
	return personFollowResp{
		Msg:       "关注成功",
		Attention: attr,
	}, nil
}

// ActionPersonUnfollow 取消关注
/**
 * @api {post} /rpc/person/unfollow 取消关注
 * @apiVersion 0.1.0
 * @apiName follow
 * @apiGroup rpc/person
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} follow_user_id 需要取消关注用户的 ID
 * @apiParam {Number} follow_from 关注来源 0: 其他; 1: Web; 2: App; 3: 直播
 *
 * @apiSuccess {String} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "取消关注成功",
 *         "attention": 0 // 0：双方互相都未关注；2：被访问用户已关注当前用户
 *       }
 *     }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 200020005
 * @apiError (403) {String} info 操作频繁，请稍后再试
 *
 * @apiError (400) {Number} code 200020002
 * @apiError (400) {String} info 不能取消关注自己
 */
func ActionPersonUnfollow(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPersonFollowParam(c, FollowTypeUnfollow)
	if err != nil {
		return nil, err
	}
	defer param.cleanup()

	if err = person.Unfollow(param.UserID, param.FollowUserID); err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	attr := param.attention()
	param.sendLog()
	return personFollowResp{
		Msg:       "取消关注成功",
		Attention: attr,
	}, nil
}
