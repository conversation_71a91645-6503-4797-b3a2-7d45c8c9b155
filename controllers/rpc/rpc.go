package rpc

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/captcha"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/cron"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/discovery"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/drama"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/event"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/history"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/message"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/scan"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/sound"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc/user"
	rpcutil "github.com/MiaoSiLa/missevan-go/controllers/rpc/util"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
)

// Handler returns the registered handler
func Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.Middleware(conf.HTTP.RPCKey),
		},
		SubHandlers: []handler.Handler{
			scanHandler(),
			messageHandler(),
			discoveryHandler(),
			utilHandler(),
			dramaHandler(),
			eventHandler(),
			userHandler(),
			historyHandler(),
			personHandler(),
			captcha.RPCHandler(),
			cronHandler(),
			soundHandler(),
		},
		Actions: map[string]*handler.Action{
			"/echo": handler.NewAction(handler.POST, actionEcho, false),
		},
	}
}

// HandlerV2 returns the registered handlerV2
func HandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.Middleware(conf.HTTP.RPCKey),
		},
		SubHandlers: []handler.HandlerV2{
			utilHandlerV2(),
		},
	}
}

func actionEcho(c *handler.Context) (handler.ActionResponse, error) {
	var s interface{}
	err := c.BindJSON(&s)
	return s, err
}

func scanHandler() handler.Handler {
	return handler.Handler{
		Name: "scan",
		Actions: map[string]*handler.Action{
			"/text":         handler.NewAction(handler.POST, scan.ActionText, false),
			"/text/detail":  handler.NewAction(handler.POST, scan.ActionTextDetail, false),
			"/risk":         handler.NewAction(handler.POST, scan.ActionRisk, false),
			"/image":        handler.NewAction(handler.POST, scan.ActionImage, false),
			"/image/detail": handler.NewAction(handler.POST, scan.ActionImageDetail, false),
			"/check-pm":     handler.NewAction(handler.POST, scan.ActionCheckPM, false),
			"/im":           handler.NewAction(handler.POST, scan.ActionIM, false),
		},
	}
}

func messageHandler() handler.Handler {
	return handler.Handler{
		Name: "message",
		Actions: map[string]*handler.Action{
			"/add-dm":          handler.NewAction(handler.POST, message.ActionAddDm, false),
			"/get-dm":          handler.NewAction(handler.POST, message.ActionGetDm, false),
			"/del-dm":          handler.NewAction(handler.POST, message.ActionDeleteDm, false),
			"/like-dm":         handler.NewAction(handler.POST, message.ActionLikeDm, false),
			"/add-comment":     handler.NewAction(handler.POST, message.ActionAddComment, false),
			"/add-subcomment":  handler.NewAction(handler.POST, message.ActionAddSubComment, false),
			"/like":            handler.NewAction(handler.POST, message.ActionLike, false),
			"/dislike":         handler.NewAction(handler.POST, message.ActionDislike, false),
			"/report":          handler.NewAction(handler.POST, message.ActionReportResult, false),
			"/exclusive-emote": handler.NewAction(handler.POST, message.ActionExclusiveEmote, false),
		},
	}
}

func discoveryHandler() handler.Handler {
	return handler.Handler{
		Name: "discovery",
		Actions: map[string]*handler.Action{
			"/search":           handler.NewAction(handler.POST, discovery.ActionSearch, false),
			"/search-card":      handler.NewAction(handler.POST, discovery.ActionSearchCard, false),
			"/suggest":          handler.NewAction(handler.POST, discovery.ActionSuggest, false),
			"/searchcount":      handler.NewAction(handler.POST, discovery.ActionSearchCount, false),
			"/sync-ad-koc-word": handler.NewAction(handler.POST, discovery.ActionSyncAdKOCWord, false),
			"/clean-koc-cache":  handler.NewAction(handler.POST, discovery.ActionCleanKocCache, false),
		},
	}
}

func utilHandler() handler.Handler {
	return handler.Handler{
		Name: "util",
		Actions: map[string]*handler.Action{
			"/geoip":         handler.NewAction(handler.POST, rpcutil.ActionGeoIP, false),
			"/is-office-ip":  handler.NewAction(handler.POST, rpcutil.ActionIsOfficeIP, false),
			"/addadminlog":   handler.NewAction(handler.POST, rpcutil.ActionAddAdminLog, false),
			"/shorturl":      handler.NewAction(handler.POST, rpcutil.ActionShortURL, false),
			"/auth-roles":    handler.NewAction(handler.POST, rpcutil.ActionAuthRoles, false),
			"/auth-user-ids": handler.NewAction(handler.POST, rpcutil.ActionAuthUserIDs, false),
		},
	}
}

func dramaHandler() handler.Handler {
	return handler.Handler{
		Name: "drama",
		Actions: map[string]*handler.Action{
			"get-dramaid-by-soundid": handler.NewAction(handler.POST, drama.ActionGetDramaIDBySoundID, false),
			"get-dramas":             handler.NewAction(handler.POST, drama.ActionGetDramas, false),
			"must-get-dramas":        handler.NewAction(handler.POST, drama.ActionMustGetDramas, false),
			"check-status":           handler.NewAction(handler.POST, drama.ActionDramaCheckStatus, false),
			"get-drama-episodes":     handler.NewAction(handler.POST, drama.ActionGetDramaEpisodes, false),
			"check-drama-refined":    handler.NewAction(handler.POST, drama.ActionCheckDramaRefined, false),
			"get-derivatives-by-ip":  handler.NewAction(handler.POST, drama.ActionGetDervativesByIP, false),
			"ipr-dramas-subscribed":  handler.NewAction(handler.POST, drama.ActionIPRDramasSubscribed, false),
			"feed-notice":            handler.NewAction(handler.POST, drama.ActionFeedNotice, false),
		},
	}
}

func eventHandler() handler.Handler {
	return handler.Handler{
		Name: "event",
		Actions: map[string]*handler.Action{
			"listongoing":  handler.NewAction(handler.POST, event.ActionListOngoing, false),
			"listliverank": handler.NewAction(handler.POST, event.ActionListLiveRank, false),
		},
	}
}

func userHandler() handler.Handler {
	return handler.Handler{
		Name: "user",
		Actions: map[string]*handler.Action{
			"block-status":      handler.NewAction(handler.POST, rpcutil.ActionBlockStatus, false),
			"block-list":        handler.NewAction(handler.POST, rpcutil.ActionUserBlockList, false),
			"block-status-list": handler.NewAction(handler.POST, rpcutil.ActionBlockStatusList, false),

			"updatepoint": handler.NewAction(handler.POST, user.ActionUpdateUserPoint, false),
		},
	}
}

func historyHandler() handler.Handler {
	return handler.Handler{
		Name: "history",
		Actions: map[string]*handler.Action{
			"/add-play-history":        handler.NewAction(handler.POST, history.ActionAddPlayHistory, false),
			"/del-play-history":        handler.NewAction(handler.POST, history.ActionDelPlayHistory, false),
			"/clear-play-history":      handler.NewAction(handler.POST, history.ActionClearPlayHistory, false),
			"/del-live-play-history":   handler.NewAction(handler.POST, history.ActionDelLivePlayHistory, false),
			"/clear-live-play-history": handler.NewAction(handler.POST, history.ActionClearLivePlayHistory, false),
			"/get-play-history":        handler.NewAction(handler.POST, history.ActionGetPlayHistory, false),
			"/add-radio-play-history":  handler.NewAction(handler.POST, history.ActionAddRadioPlayHistory, false),
			"/get-radio-play-history":  handler.NewAction(handler.POST, history.ActionGetRadioPlayHistory, false),
		},
	}
}

func personHandler() handler.Handler {
	return handler.Handler{
		Name: "person",
		Actions: map[string]*handler.Action{
			"follow":   handler.NewAction(handler.POST, person.ActionPersonFollow, false),
			"unfollow": handler.NewAction(handler.POST, person.ActionPersonUnfollow, false),
		},
	}
}

func cronHandler() handler.Handler {
	cron := handler.Handler{
		Name: "cron",
		Actions: map[string]*handler.Action{
			"discovery/clean-search-intervention": handler.NewAction(handler.POST, cron.ActionCleanSearchIntervention, false),
		},
	}
	return cron
}

func soundHandler() handler.Handler {
	sound := handler.Handler{
		Name: "sound",
		Actions: map[string]*handler.Action{
			"get-recommend": handler.NewAction(handler.POST, sound.ActionGetRecommend, false),
		},
	}
	return sound
}

func utilHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "util",
		Actions: map[string]*handler.ActionV2{
			"/parse-user-agent": handler.NewActionV2(handler.POST, rpcutil.ActionParseUserAgent, handler.ActionOption{LoginRequired: false}),
		},
	}
}
