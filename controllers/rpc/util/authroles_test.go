package rpcutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

func TestActionAuthRoles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/rpc/util/auth-roles", nil)
	_, err := ActionAuthRoles(c)
	require.Equal(actionerrors.ErrParams, err)

	body := handler.M{
		"user_id": 9074508,
	}
	c = handler.NewRPCTestContext("/rpc/util/auth-roles", body)
	resp, err := ActionAuthRoles(c)
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(rolesResp)
	assert.Zero(len(r.Roles))

	body["user_id"] = 9074509
	c = handler.NewRPCTestContext("/rpc/util/auth-roles", body)
	resp, err = ActionAuthRoles(c)
	require.NoError(err)
	require.NotNil(resp)
	r = resp.(rolesResp)
	assert.Equal(3, len(r.Roles))
}

func TestActionAuthUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/util/auth-user-ids", nil)
	_, err := ActionAuthUserIDs(c)
	assert.Equal(actionerrors.ErrParams, err)

	roles := []string{string(role.LiveAdmin)}
	c = handler.NewRPCTestContext("/util/auth-user-ids", handler.M{"roles": roles})
	r, err := ActionAuthUserIDs(c)
	require.NoError(err)
	var resp *authUserIDsResp
	require.IsType(resp, r)
	resp = r.(*authUserIDsResp)
	assert.NotEmpty(resp.UserIDs)
}
