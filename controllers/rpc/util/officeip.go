package rpcutil

import (
	"net"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

type isOfficeIPResp struct {
	IsOfficeIP bool `json:"is_office_ip"`
}

// ActionIsOfficeIP 判断是否是办公室 IP
/**
 * @api {post} /rpc/util/is-office-ip 判断是否是办公室 IP
 * @apiDescription 失败时应降级为非办公室 IP 进行后续处理
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} ip 需要判断的 IPv4 或 IPv6 地址
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "is_office_ip": true
 *       }
 *     }
 */
func ActionIsOfficeIP(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		IP string `json:"ip"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.IP == "" || net.ParseIP(param.IP) == nil {
		return nil, actionerrors.ErrParams
	}
	key := keys.KeyAllowListOfficeIP0.Format()
	// 数据不多（N = 75），可以使用 HVALS 获取，若后续数据太多，考虑使用其他数据结构存储
	result, err := service.Redis.HVals(key).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &isOfficeIPResp{
		IsOfficeIP: util.HasElem(result, param.IP),
	}, nil
}
