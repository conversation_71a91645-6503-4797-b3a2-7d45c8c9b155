package rpcutil

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

func TestActionIsOfficeIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无缓存的情况
	key := keys.KeyAllowListOfficeIP0.Format()
	result, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	require.Zero(result)
	c := handler.NewRPCTestContext("/util/is-office-ip", handler.M{"ip": "127.0.0.1"})
	info, err := ActionIsOfficeIP(c)
	require.NoError(err)
	require.IsType(info, &isOfficeIPResp{})
	assert.False(info.(*isOfficeIPResp).IsOfficeIP)

	// 测试有缓存的情况
	m := map[string]interface{}{
		"cq1": "127.0.0.1",
		"cq2": "*********",
	}
	pipeline := service.Redis.Pipeline()
	pipeline.HSet(key, m)
	pipeline.Expire(key, time.Second*7)
	_, err = pipeline.Exec()
	require.NoError(err)
	defer service.Redis.Del(key)
	c = handler.NewRPCTestContext("/util/is-office-ip", handler.M{"ip": "127.0.0.1"})
	info, err = ActionIsOfficeIP(c)
	require.NoError(err)
	require.IsType(info, &isOfficeIPResp{})
	assert.True(info.(*isOfficeIPResp).IsOfficeIP)

	c = handler.NewRPCTestContext("/util/is-office-ip", handler.M{"ip": "*********"})
	info, err = ActionIsOfficeIP(c)
	require.NoError(err)
	require.IsType(info, &isOfficeIPResp{})
	assert.False(info.(*isOfficeIPResp).IsOfficeIP)

	// 测试参数错误的情况
	c = handler.NewRPCTestContext("/util/is-office-ip", handler.M{"ip": "999.0.0.3"})
	_, err = ActionIsOfficeIP(c)
	assert.Equal(actionerrors.ErrParams, err)
}
