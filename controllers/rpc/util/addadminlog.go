package rpcutil

import (
	"encoding/json"
	"io"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/service"
)

// ActionAddAdminLogParams params for ActionAddAdminLog
type ActionAddAdminLogParams struct {
	Data []adminlogger.AdminLog `json:"data"`
}

// ActionAddAdminLog 添加管理员日志
/**
 * @api {post} /rpc/util/addadminlog 添加管理员日志
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "data": [
 *         {
 *           "user_id": 12345, // 用户 ID
 *           "catalog": 1, // 操作类型
 *           "channel_id": 0, // 频道 ID
 *           "url": "/api/v2/admin/chatroom/image/pass", // URL
 *           "intro": "审核通过图片, ID: 2103,名称：#1247-原创 2", // 操作
 *           "ip": "127.0.0.1", // 操作 IP
 *           "create_time": 1567750848, // 操作时间，单位：秒
 *         }
 *       ]
 *     }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionAddAdminLog(c *handler.Context) (handler.ActionResponse, error) {
	body, err := io.ReadAll(c.Request().Body)
	if err != nil || len(body) <= 0 {
		return nil, actionerrors.ErrParams
	}

	params := ActionAddAdminLogParams{}
	switch body[0] {
	case '[':
		// Parse body as array to support old calls.
		if json.Unmarshal(body, &params.Data) != nil {
			return nil, actionerrors.ErrParams
		}
	case '{':
		if json.Unmarshal(body, &params) != nil {
			return nil, actionerrors.ErrParams
		}
	default:
		return nil, actionerrors.ErrParams
	}

	logs := params.Data
	if len(logs) <= 0 {
		return nil, actionerrors.ErrParams
	}

	err = helper.BatchInsert(service.LogDB, logs[0].TableName(), logs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return "success", nil
}
