package rpcutil

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

type rolesParam struct {
	UserID int64 `json:"user_id"`
}

type rolesResp struct {
	Roles []string `json:"roles"`
}

// ActionAuthRoles 获取用户后台角色
/**
 * @api {post} /rpc/util/auth-roles 获取用户后台角色
 * @apiVersion 0.1.0
 * @apiGroup rpc/util
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "roles": [
 *         "admin",
 *         "liveadmin",
 *         "subtitleadmin"
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 201010002
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionAuthRoles(c *handler.Context) (handler.ActionResponse, error) {
	var param rolesParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	roles, err := role.FindRolesByUserID(param.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return rolesResp{
		Roles: roles,
	}, nil
}

type authUserIDsResp struct {
	UserIDs []int64 `json:"user_ids"`
}

// ActionAuthUserIDs 获取拥有某些角色的用户 ID
/**
 * @api {post} /rpc/util/auth-user-ids 获取拥有某些角色的用户 ID
 * @apiVersion 0.1.0
 * @apiGroup rpc/util
 *
 * @apiParam {String[]} roles 角色列表
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_ids": [
 *         12,
 *         13,
 *         45
 *       ]
 *     }
 *   }
 *
 */
func ActionAuthUserIDs(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Roles []string `json:"roles"`
	}
	err := c.BindJSON(&param)
	if err != nil || len(param.Roles) == 0 {
		return nil, actionerrors.ErrParams
	}
	userIDs, err := role.FindUserIDsByRoles(param.Roles)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &authUserIDsResp{
		UserIDs: userIDs,
	}, nil
}
