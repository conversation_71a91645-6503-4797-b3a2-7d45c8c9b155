package rpcutil

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	os.Exit(m.Run())
}

func TestActionAddAdminLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	data := []adminlogger.AdminLog{
		{
			UserID:    1760,
			Catalog:   1,
			ChannelID: 0,
			URL:       "/api/v2/admin/chatroom/image/pass",
			Intro:     "审核通过图片,ID:2103,名称：#1247-原创1",
			IP:        "127.0.0.1",
		},
		{
			UserID:    1760,
			Catalog:   1,
			ChannelID: 0,
			URL:       "/api/v2/admin/chatroom/image/pass",
			Intro:     "审核通过图片,ID:2103,名称：#1247-原创2",
			IP:        "127.0.0.1",
		},
	}

	c := handler.NewRPCTestContext("http://localhost/rpc/util/addadminlog", "")
	_, err := ActionAddAdminLog(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewRPCTestContext("http://localhost/rpc/util/addadminlog", data)
	r, err := ActionAddAdminLog(c)
	require.NoError(err)
	assert.Equal(handler.ActionResponse("success"), r)

	c = handler.NewRPCTestContext("http://localhost/rpc/util/addadminlog", ActionAddAdminLogParams{Data: data})
	r, err = ActionAddAdminLog(c)
	require.NoError(err)
	assert.Equal(handler.ActionResponse("success"), r)
}
