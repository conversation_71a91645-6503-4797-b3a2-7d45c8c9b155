package rpcutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestParseUserAgent_tag(t *testing.T) {
	checker := tutil.NewKeyChecker(t, tutil.JSON)
	checker.Check(uaParams{}, "user_agents")
	checker.Check(uaResp{}, "data")
	checker.Check(uaItem{}, "client", "os", "device")
	checker.Check(uaClient{}, "family")
	checker.Check(uaOS{}, "family")
	checker.Check(uaDevice{}, "brand", "model")
}

func TestActionParseUserAgent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试请求出错的情况（以参数错误为例）
	api := "/rpc/util/parse-user-agent"
	params := uaParams{
		UserAgents: []string{},
	}
	c := handler.NewRPCTestContext(api, params)
	_, _, err := ActionParseUserAgent(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试正常请求的情况
	params.UserAgents = []string{"Mozilla/5.0 (Linux; Android 10; JER-AN20 Build/HUAWEIJER-AN20) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"}
	c = handler.NewRPCTestContext(api, params)
	data, message, err := ActionParseUserAgent(c)
	require.NoError(err)
	assert.Equal("", message)
	require.NotNil(data)
	uaRespData, ok := data.(uaResp)
	require.True(ok)
	assert.NotNil(uaRespData.Data)
	require.Len(uaRespData.Data, 1)
	// 验证解析结果
	assert.Equal("Chrome Mobile", uaRespData.Data[0].Client.Family)
	assert.Equal("Android", uaRespData.Data[0].OS.Family)
	assert.Equal("Huawei", uaRespData.Data[0].Device.Brand)
	assert.Equal("JER-AN20", uaRespData.Data[0].Device.Model)
}

func TestNewUaParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有传参数的时候
	api := "/rpc/util/parse-user-agent"
	c := handler.NewRPCTestContext(api, nil)
	_, err := newUaParams(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数错误的情况
	c = handler.NewRPCTestContext(api, uaParams{
		UserAgents: []string{},
	})
	_, err = newUaParams(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试参数正确的情况
	c = handler.NewRPCTestContext(api, uaParams{
		UserAgents: []string{
			"Mozilla/5.0 (Linux; Android 10; JER-AN20 Build/HUAWEIJER-AN20) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
		},
	})
	params, err := newUaParams(c)
	require.NoError(err)
	assert.NotNil(params)
	assert.Len(params.UserAgents, 1)
}
