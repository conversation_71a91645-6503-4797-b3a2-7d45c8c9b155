package rpcutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/util"
)

func TestFormatRegionName(t *testing.T) {
	assert := assert.New(t)

	regionName := formatRegionName("")
	assert.Empty(regionName)

	regionName = formatRegionName("中国台湾")
	assert.Equal("中国台湾", regionName)

	regionName = formatRegionName("台湾")
	assert.Equal("中国台湾", regionName)

	regionName = formatRegionName("上海")
	assert.Equal("上海", regionName)
}

func TestActionGeoIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := geoipParams{
		IP:   "**************",
		Lang: langZHCN,
	}
	c := handler.NewRPCTestContext("/rpc/util/geoip", params)
	resp, err := ActionGeoIP(c)
	require.NoError(err)
	require.IsType(handler.M{}, resp)
	info := resp.(handler.M)
	assert.Equal("唐山", info["city_name"])
	assert.Equal("河北", info["region_name"])
	assert.Equal("中国", info["country_name"])
	// 测试用的 IP 库 country_code 会返回空字符串
	assert.True(info["country_code"] == "" || info["country_code"] == "CN")

	// 测试大陆外中国地区列表中的地区增加“中国”前缀
	params.IP = "************"
	c = handler.NewRPCTestContext("/rpc/util/geoip", params)
	resp, err = ActionGeoIP(c)
	require.NoError(err)
	require.IsType(handler.M{}, resp)
	info = resp.(handler.M)
	assert.Equal("台中市", info["city_name"])
	assert.Equal("中国台湾", info["region_name"])
	assert.Equal("中国", info["country_name"])

	params.Lang = "FR"
	c = handler.NewRPCTestContext("/rpc/util/geoip", params)
	_, err = ActionGeoIP(c)
	assert.EqualError(err, "lang should be en or zh-cn")

	params.IP = "27.190.250164"
	c = handler.NewRPCTestContext("/rpc/util/geoip", params)
	_, err = ActionGeoIP(c)
	assert.Error(err, util.ErrInvalidIP)
}

func TestNewGeoipParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{
		"ip":   "**************",
		"lang": langZHCN,
	}
	c := handler.NewRPCTestContext("/rpc/util/geoip", body)
	params, err := newGeoipParams(c)
	require.NoError(err)
	assert.Equal("**************", params.IP)
	assert.Equal(util.IPIPLanguageCN, params.Lang)

	body["lang"] = ""
	c = handler.NewRPCTestContext("/rpc/util/geoip", body)
	params, err = newGeoipParams(c)
	require.NoError(err)
	assert.Equal("**************", params.IP)
	assert.Equal(util.IPIPLanguageEN, params.Lang)

	body["lang"] = "FA"
	c = handler.NewRPCTestContext("/rpc/util/geoip", body)
	_, err = newGeoipParams(c)
	assert.EqualError(err, "lang should be en or zh-cn")
}
