package rpcutil

import (
	"strings"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

const regionPrefixCN = "中国"

// 大陆外中国地区列表
var cnRegionList = []string{"澳门", "香港", "台湾"}

// formatRegionName 包含在大陆外中国地区列表中的地区增加“中国”前缀
// 业务场景：展示用户 IP 属地的省份/州
func formatRegionName(regionName string) string {
	if regionName == "" {
		return regionName
	}
	if util.HasElem(cnRegionList, regionName) {
		return regionPrefixCN + regionName
	}
	return regionName
}

const (
	langEN   = "en"
	langZHCN = "zh-cn"
)

type geoipParams struct {
	IP   string `json:"ip"`
	Lang string `json:"lang"`
}

// ActionGeoIP handler
/**
 * @api {post} /rpc/util/geoip 从 IP 地址获取位置信息
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} ip IP address e.g. "************" or "2404:6800:4008:802::2013"
 * @apiParam {string="en","zh-cn"} [lang="en"] 位置信息语言
 *
 * @apiSuccessExample {json} Success-Response-en:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "country_name": "China",
 *         "country_code": "CN",
 *         "region_name": "Shanghai",
 *         "city_name": "Shanghai",
 *         "isp": "ChinaTelecom"
 *       }
 *     }
 *
 * @apiSuccessExample {json} Success-Response-zh-cn:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "country_name": "中国",
 *         "country_code": "CN",
 *         "region_name": "上海",
 *         "city_name": "上海",
 *         "isp": "中国电信"
 *       }
 *     }
 */
func ActionGeoIP(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newGeoipParams(c)
	if err != nil {
		return nil, err
	}

	city, err := service.GeoIP.Get(params.IP, params.Lang)
	if err != nil {
		if err == serviceutil.ErrInvalidIP {
			return nil, handler.ErrBadRequest
		}
		return nil, err
	}
	return handler.M{
		"country_name": city.CountryName(),
		"country_code": city.CountryIsoCode(),
		"region_name":  formatRegionName(city.RegionName()),
		"city_name":    city.CityName(),
		"isp":          city.ISPName(),
	}, nil
}

func newGeoipParams(c *handler.Context) (*geoipParams, error) {
	params := new(geoipParams)
	err := c.BindJSON(params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch params.Lang {
	case "", langEN:
		params.Lang = serviceutil.IPIPLanguageEN
	case langZHCN:
		params.Lang = serviceutil.IPIPLanguageCN
	default:
		return nil, actionerrors.ErrParamMsg("lang should be en or zh-cn")
	}
	params.IP = strings.TrimSpace(params.IP)
	return params, nil
}
