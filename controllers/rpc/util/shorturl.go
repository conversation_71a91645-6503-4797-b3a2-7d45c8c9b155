package rpcutil

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
)

type shortURLParams struct {
	OriginURL string `json:"origin_url"`
}

type shortURLResponse struct {
	ShortURL string `json:"short_url"`
}

// ActionShortURL handler
/**
* @api {post} /rpc/util/shorturl 获取短链
* @apiDescription 短链有效期为一年
* @apiVersion 0.1.0
* @apiGroup rpc
*
* @apiParam {String} origin_url 原链接
*
* @apiParamExample {json} Request-Example:
*     {
*       "origin_url": "https://www.bilibili.com"
*     }
*
* @apiSuccessExample {json} Success-Response:
*     HTTP/1.1 200 OK
*     {
*       "code": 0,
*       "info": {
*         "short_url": "https://uat.b23.tv/xpiPwLL"
*       }
*     }
 */
func ActionShortURL(c *handler.Context) (handler.ActionResponse, error) {
	var param shortURLParams
	err := c.BindJSON(&param)
	if err != nil || param.OriginURL == "" {
		return nil, actionerrors.ErrParams
	}
	data, err := service.ShortURL.Add(param.OriginURL)
	if err != nil {
		return nil, err
	}
	return shortURLResponse{ShortURL: data.ShortURL}, nil
}
