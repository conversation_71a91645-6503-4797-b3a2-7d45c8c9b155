package rpcutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/shorturl"
)

func TestActionShortURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	shorturl.SetMockResult(shorturl.APIShortURLAdd, 0, map[string]interface{}{
		"short_url": "https://uat.b23.tv/xpiPwLL",
	})
	params := shortURLParams{
		OriginURL: "https://www.bilibili.com",
	}
	c := handler.NewRPCTestContext("/rpc/util/shorturl", params)
	resp, err := ActionShortURL(c)
	require.NoError(err)
	r := resp.(shortURLResponse)
	assert.Equal("https://uat.b23.tv/xpiPwLL", r.<PERSON>)
}
