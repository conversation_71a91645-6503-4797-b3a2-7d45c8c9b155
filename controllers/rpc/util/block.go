package rpcutil

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	blockListTypeBlockedByUser  = iota // 获取被用户拉黑的所有用户 ID
	blockListTypeWhoBlockedUser        // 获取用户被哪些用户 ID 拉黑
)

// ActionBlockStatus 判断 user_ids 数组中两个 userID 的拉黑关系
/**
 * @api {post} /rpc/user/block-status 判断 user_ids 数组中两个 userID 的拉黑关系
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number[2]} user_ids 用户 ID 数组
 * @apiParamExample {json} Request-Example:
 *     {
 *       "user_ids": [10, 100], // userID 数组
 *     }
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *          // 如果 block_status[0] 为 true 时 user_ids[0] 拉黑了 user_ids[1] 如果是 false 则 user_ids[0] 没有拉黑 user_ids[1]
 *          // 如果 block_status[1] 为 true 时 user_ids[1] 拉黑了 user_ids[0] 如果是 false 则 user_ids[1] 没有拉黑 user_ids[0]
 *         "block_status": [true, false]
 *       }
 *     }
 */
func ActionBlockStatus(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		UserIDs []int64 `json:"user_ids"`
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}

	if len(input.UserIDs) != 2 || input.UserIDs[0] == input.UserIDs[1] {
		return nil, handler.ErrInvalidParam
	}

	u1BlockU2, u2BlockU1, err := blackuser.GetBlacklistRelation(input.UserIDs[0], input.UserIDs[1])
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	return handler.M{
		"block_status": []bool{u1BlockU2, u2BlockU1},
	}, nil
}

type userBlockListParam struct {
	UserID int64 `json:"user_id"`
	Type   int   `json:"type,omitempty"` // 0: 返回被该用户拉黑的所有用户 ID; 1: 返回被哪些用户拉黑的用户 ID
}

type userBlockListResponse struct {
	BlockList []int64 `json:"block_list"`
}

// ActionUserBlockList 获取被该用户拉黑的所有用户 ID 和被哪些用户拉黑的用户 ID
/**
 * @api {post} /rpc/user/block-list 获取被该用户拉黑的所有用户 ID 和被哪些用户拉黑的用户 ID
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id
 * @apiParam {Number} [type=0] 0: 返回被该用户拉黑的所有用户 ID; 1: 返回被哪些用户拉黑的用户 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "block_list": [1, 22, 454, 54535] // type 为 0 时，为被该用户拉黑的所有用户 ID；type 为 1 时，为被哪些用户拉黑的用户 ID
 *     }
 *   }
 */
func ActionUserBlockList(c *handler.Context) (handler.ActionResponse, error) {
	param := new(userBlockListParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, handler.ErrBadRequest
	}
	if param.UserID <= 0 {
		return nil, handler.ErrInvalidParam
	}

	var resp userBlockListResponse
	switch param.Type {
	case blockListTypeBlockedByUser:
		resp.BlockList, err = blackuser.ListBlockedByUser(param.UserID)
	case blockListTypeWhoBlockedUser:
		resp.BlockList, err = blackuser.ListWhoBlockedUser(param.UserID)
	default:
		return nil, handler.ErrInvalidParam
	}
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &resp, nil
}

type blockStatusListParam struct {
	UserID       int64   `json:"user_id"`
	CheckUserIDs []int64 `json:"check_user_ids"`
}

type blockStatusListResponse struct {
	BlockUserList []int64 `json:"block_user_list"` // check_user_ids 中拉黑 user_id 的用户 IDs
	UserBlockList []int64 `json:"user_block_list"` // check_user_ids 中被 user_id 拉黑的用户 IDs
}

// ActionBlockStatusList 获取 check_user_ids 与 user_id 之间的拉黑关系
/**
 * @api {post} /rpc/user/block-status-list 获取 check_user_ids 与 user_id 之间的拉黑关系
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id
 * @apiParam {Number[]} check_user_ids
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "block_user_list": [1, 22, 454, 54535], // check_user_ids 中拉黑 user_id 的用户 IDs
 *       "user_block_list": [1, 22, 454, 54535] // check_user_ids 中被 user_id 拉黑的用户 IDs
 *     }
 *   }
 */
func ActionBlockStatusList(c *handler.Context) (handler.ActionResponse, error) {
	param := new(blockStatusListParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, handler.ErrBadRequest
	}
	if param.UserID <= 0 || len(param.CheckUserIDs) == 0 {
		return nil, handler.ErrInvalidParam
	}
	checkUserIDs := sets.Uniq(param.CheckUserIDs)
	blockUserList, userBlockList, err := blackuser.FindBlockUserIDs(param.UserID, checkUserIDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &blockStatusListResponse{BlockUserList: blockUserList, UserBlockList: userBlockList}, nil
}
