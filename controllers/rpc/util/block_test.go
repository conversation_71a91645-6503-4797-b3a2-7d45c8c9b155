package rpcutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestActionBlockStatus(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := blackuser.Model{
		SmallID: 10,
		BigID:   100,
	}
	m.Status.Set(blackuser.StatusSmallBanBig)
	err := service.MessageDB.Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).Assign(m).FirstOrCreate(&m).Error
	require.NoError(err)

	api := "/rpc/user/block-status"
	targetData := handler.M{"user_ids": []int64{100, 100}}
	param := handler.NewRPCTestContext(api, targetData)
	_, err = ActionBlockStatus(param)
	assert.Error(handler.ErrInvalidParam, err)

	targetData = handler.M{"user_ids": []int64{10, 100, 10}}
	param = handler.NewRPCTestContext(api, targetData)
	_, err = ActionBlockStatus(param)
	assert.Error(handler.ErrInvalidParam, err)

	targetData = handler.M{"user_ids": []int64{10, 100}}
	c := handler.NewRPCTestContext(api, targetData)
	r, err := ActionBlockStatus(c)
	require.NoError(err)
	assert.Equal(handler.M{"block_status": []bool{true, false}}, r)

	m.Status.Set(blackuser.StatusBigBanSmall)
	err = service.MessageDB.Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).Assign(m).FirstOrCreate(&m).Error
	require.NoError(err)

	c = handler.NewRPCTestContext(api, targetData)
	r, err = ActionBlockStatus(c)
	require.NoError(err)
	assert.Equal(handler.M{"block_status": []bool{true, true}}, r)

	targetData = handler.M{"user_ids": []int64{9, 10}}
	param = handler.NewRPCTestContext(api, targetData)
	r, err = ActionBlockStatus(param)
	require.NoError(err)
	assert.Equal(handler.M{"block_status": []bool{false, false}}, r)
}

func TestActionUserBlockList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/rpc/user/block-list", handler.M{"user_id": 0})
	_, err := ActionUserBlockList(c)
	require.EqualError(err, "参数不合法")

	c = handler.NewRPCTestContext("/rpc/user/block-list", handler.M{"user_id": 99})
	resp, err := ActionUserBlockList(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Len(resp.(*userBlockListResponse).BlockList, 4)

	c = handler.NewRPCTestContext("/rpc/user/block-list", handler.M{"user_id": 20222022})
	resp, err = ActionUserBlockList(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.NotNil(resp.(*userBlockListResponse).BlockList)

	c = handler.NewRPCTestContext("/rpc/user/block-list", handler.M{"user_id": 99, "type": blockListTypeWhoBlockedUser})
	resp, err = ActionUserBlockList(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Len(resp.(*userBlockListResponse).BlockList, 2)

	c = handler.NewRPCTestContext("/rpc/user/block-list", handler.M{"user_id": 99, "type": 3})
	_, err = ActionUserBlockList(c)
	require.EqualError(err, "参数不合法")
}

func TestActionBlockStatusList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数不合法
	param := blockStatusListParam{UserID: 12}
	c := handler.NewRPCTestContext("/rpc/user/block-status-list", param)
	_, err := ActionBlockStatusList(c)
	require.EqualError(err, "参数不合法")

	param = blockStatusListParam{UserID: -12, CheckUserIDs: []int64{12, 13}}
	c = handler.NewRPCTestContext("/rpc/user/block-status-list", param)
	_, err = ActionBlockStatusList(c)
	require.EqualError(err, "参数不合法")

	// 测试用户没有拉黑和被拉黑
	testCheckUserIDs := []int64{20240480, 20240481, 20240482, 18001, 18002, 18003, 18004}
	param = blockStatusListParam{UserID: 99, CheckUserIDs: testCheckUserIDs}
	c = handler.NewRPCTestContext("/rpc/user/block-status-list", param)
	resp, err := ActionBlockStatusList(c)
	require.NoError(err)
	require.NotNil(resp)
	blockList, ok := resp.(*blockStatusListResponse)
	require.True(ok)
	assert.Empty(blockList.UserBlockList)
	assert.NotNil(blockList.UserBlockList)
	assert.Empty(blockList.BlockUserList)
	assert.NotNil(blockList.BlockUserList)

	// 测试用户有拉黑和被拉黑
	param = blockStatusListParam{UserID: 19001, CheckUserIDs: testCheckUserIDs}
	c = handler.NewRPCTestContext("/rpc/user/block-status-list", param)
	resp, err = ActionBlockStatusList(c)
	require.NoError(err)
	require.NotNil(resp)
	blockList, ok = resp.(*blockStatusListResponse)
	require.True(ok)
	assert.Equal([]int64{20240480, 20240482, 18001}, blockList.UserBlockList)
	assert.Equal([]int64{20240481, 20240482, 18002, 18003}, blockList.BlockUserList)
}
