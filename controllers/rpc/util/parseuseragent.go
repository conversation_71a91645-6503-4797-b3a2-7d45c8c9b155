package rpcutil

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
)

type uaParams struct {
	UserAgents []string `json:"user_agents"`
}

type uaClient struct {
	Family string `json:"family"`
}

type uaOS struct {
	Family string `json:"family"`
}

type uaDevice struct {
	Brand string `json:"brand"`
	Model string `json:"model"`
}

type uaItem struct {
	Client uaClient `json:"client"`
	OS     uaOS     `json:"os"`
	Device uaDevice `json:"device"`
}

type uaResp struct {
	Data []uaItem `json:"data"`
}

// ActionParseUserAgent 解析 User-Agent 字符串，返回其包含的设备信息
/**
 * @api {post} /rpc/util/parse-user-agent 解析 User-Agent 字符串，返回其包含的设备信息
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String[]} user_agents User-Agent 字符串数组
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data 设备信息数组，元素顺序与请求的 user_agents 数组一致
 *
 * @apiSuccessExample {json} Success-Response-en:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [
 *           {
 *             "client": {
 *               "family": "Chrome"
 *             },
 *             "os": {
 *               "family": "Android"
 *             },
 *             "device": {
 *               "brand": "Huawei",
 *               "model": "JER-AN20"
 *             }
 *           },
 *           {
 *             "client": {
 *               "family": "Chrome"
 *             },
 *             "os": {
 *               "family": "Mac OS X",
 *             },
 *             "device": {
 *               "brand": "Apple",
 *               "model": "Mac"
 *             }
 *           }
 *         ]
 *       }
 *     }
 *
 * @apiError (400) {Number} code
 * @apiError (400) {String} message
 * @apiError (400) {Object} data
 */
func ActionParseUserAgent(c *handler.Context) (handler.ActionResponse, string, error) {
	// TODO: 之后需要可以单独判断猫耳客户端的 UA 并返回相关信息
	params, err := newUaParams(c)
	if err != nil {
		return nil, "", err
	}
	data := make([]uaItem, 0, len(params.UserAgents))
	for _, ua := range params.UserAgents {
		uaInfo := service.UserAgentParser.Parse(ua)
		data = append(data, uaItem{
			Client: uaClient{
				Family: uaInfo.UserAgent.Family,
			},
			OS: uaOS{
				Family: uaInfo.Os.Family,
			},
			Device: uaDevice{
				Brand: uaInfo.Device.Brand,
				Model: uaInfo.Device.Model,
			},
		})
	}

	return uaResp{
		Data: data,
	}, "", nil
}

func newUaParams(c *handler.Context) (*uaParams, error) {
	params := new(uaParams)
	err := c.Bind(params)
	if err != nil || len(params.UserAgents) == 0 {
		return nil, actionerrors.ErrParams
	}
	// NOTICE: ua 为空字符串时，不视作参数错误
	return params, nil
}
