package message

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	usermiddleware "github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/govern"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

type dmParams struct {
	danmaku message.MSoundComment
	from    int
	equip   common.UserEquipment
}

// ActionAddDm handler
/**
 * @api {post} /rpc/message/add-dm 发送弹幕
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} sound_id 音频 ID
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} text 弹幕文本
 * @apiParam {String} stime 弹幕时间，浮点数
 * @apiParam {Number} color 弹幕颜色
 * @apiParam {Number} size 可选，字体大小，默认 25
 * @apiParam {Number} mode 可选，显示方式，默认 滑动
 * @apiParam {Number} pool 可选，弹幕池等级，默认 常规弹幕
 * @apiParam {number=1,2} from 评论来源（1：Web，2：App）
 * @apiParam {String} ip 用户 IP
 * @apiParam {String} [user_agent] 客户端 UA
 * @apiParam {String} [origin] web 端 origin
 * @apiParam {String} [buvid] BUVID 设备号
 * @apiParam {String} [equip_id] 设备号
 * @apiParamExample {json} Request-Example:
 *     {
 *       "sound_id": 1,
 *       "text": "text",
 *       "stime": "1.5",
 *       "color": 16777215 // 对应十六进制: 0xffffff
 *     }
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info null
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "color": 16777215,
 *         "date": 1614326865,
 *         "id": 21093035,
 *         "mode": 1,
 *         "pool": 160,
 *         "size": 25,
 *         "sound_id": 65851,
 *         "stime": 0,
 *         "text": "123",
 *         "user_id": 346286
 *       }
 *     }
 *
 * @apiError (500) {number} code CodeUnknownError = 100010007
 * @apiError (500) {string} info 具体错误信息
 * @apiError (403) {number} code 具体错误代码
 * @apiError (403) {string} info 原因
 */
func ActionAddDm(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newDmParams(c)
	if err != nil {
		return nil, err
	}

	sTime, err := params.danmaku.GetSTimeFloat()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 对 stime 只保留两位小数
	params.danmaku.STime = fmt.Sprintf("%.2f", sTime)
	sTime, err = strconv.ParseFloat(params.danmaku.STime, 64)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	params.danmaku.Text = strings.TrimSpace(params.danmaku.Text)
	if params.danmaku.Text == "" {
		return nil, actionerrors.ErrParamMsg("发送的内容不可为空哦")
	}
	params.danmaku.Text = strings.TrimSpace(util.FilterSpecialCodes(params.danmaku.Text, true))
	if params.danmaku.Text == "" {
		// 只有全是特殊字符的情况才返回错误
		return nil, actionerrors.ErrParamMsg("发送的内容不能有特殊字符哦")
	}
	// 连续空格合并成一个空格
	params.danmaku.Text = strings.Join(strings.Fields(params.danmaku.Text), " ")

	inBlacklist, err := user.InBlacklist(params.danmaku.UserID, user.BlackTypeUserComment)
	if err != nil {
		logger.WithField("user_id", params.danmaku.UserID).Error(err)
		// PASS
	}
	if inBlacklist {
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeBannedUser, "您的账号有可疑记录，暂时被系统封停")
	}

	// 检查是否绑定手机号
	u := new(usermiddleware.User)
	u.ID = params.danmaku.UserID
	if isBindMobile, _ := u.IsBindMobile(); !isBindMobile {
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeBindMobile, "绑定手机就可以发送弹幕了哦")
	}

	snd, err := sound.FindMSound(params.danmaku.SoundID)
	if err != nil {
		return nil, err
	}
	if snd == nil {
		return nil, handler.NewActionError(http.StatusNotFound, handler.CodeSoundNotFound, "音频不存在")
	}

	switch snd.Checked {
	case sound.CheckedContractExpired:
		return nil, actionerrors.ErrContentExpired
	case sound.CheckedUnpass, sound.CheckedPass:
		// 仅待过审及过审音可发送弹幕
	default:
		return nil, actionerrors.ErrCanNotSendDanmaku
	}

	key := serviceredis.KeyForbiddenCommentElement1.Format(soundcomment.TypeSound)
	isMember, err := service.Redis.SIsMember(key, params.danmaku.SoundID).Result()
	if err != nil {
		return nil, err
	}
	if isMember {
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeSoundNotFound, "本音频禁止发弹幕")
	}

	params.danmaku.DramaID, err = DramaIDBySoundID(params.danmaku.SoundID, params.equip.IP)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if params.danmaku.DramaID > 0 {
		refined := []string{dramainfo.DramaTypeSpecial, dramainfo.DramaTypeSensitive, dramainfo.DramaTypeLimitAddDanmaku}
		refinedInfo, err := dramainfo.CheckRefinedMap(params.danmaku.DramaID, refined)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		err = checkDramaCommentStatus(refinedInfo)
		if err != nil {
			return nil, err
		}
		if err = limitUserAddDanmaku(refinedInfo, params.danmaku.UserID, params.danmaku.DramaID); err != nil {
			return nil, err
		}
		// 检查用户是否无权限收听会员音
		isVipUser, err := muservip.IsVip(params.danmaku.UserID)
		if err != nil {
			return nil, err
		}
		limitType, err := snd.GetLimitType(isVipUser, params.danmaku.IP, params.danmaku.UserID)
		if err != nil {
			return nil, err
		}
		if limitType == sound.LimitTypeVipPay {
			return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, "开通会员即可发送弹幕了哦~")
		}

		if util.HasElem([]int{sound.LimitTypeDrama, sound.LimitTypeEpisode}, limitType) && params.danmaku.UserID != snd.UserID {
			return nil, handler.NewActionError(http.StatusForbidden, handler.CodeSoundNotFound, "您未购买暂时不能发弹幕")
		}
	}

	// 秒对毫秒的换算倍数
	if sTime < 0 || sTime*1000 > float64(snd.Duration) {
		// TODO: error code
		return nil, handler.NewActionError(http.StatusForbidden, handler.CodeSoundNotFound, "已经超出音频时间")
	}
	ok, err := role.IsRole(params.danmaku.UserID, role.SubtitleAdmin)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 限制用户每日发送弹幕数量
	if !ok {
		// 若非弹幕维护人员，限制用户每日发送的弹幕数
		limitCount, err := reachDMLimit(params.danmaku.UserID)
		if err != nil {
			return nil, err
		}
		if limitCount > 0 {
			return nil, handler.NewActionError(http.StatusForbidden, handler.CodeUserLimit, fmt.Sprintf("今日弹幕额度已用完（%d/%d）", limitCount, limitCount))
		}
	}

	_, status2, err := blackuser.GetBlacklistRelation(params.danmaku.UserID, snd.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 当被对方拉黑后，返回弹幕信息，该弹幕信息在数据库中不存在
	if status2 {
		return params.danmaku, nil
	}
	err = addDm(&params.danmaku, snd, params.equip.IP)
	if err != nil {
		return nil, err
	}

	reqURLPath := c.Request().URL.Path
	util.Go(func() {
		err := user.SendIPLog(params.danmaku.UserID, params.equip.IP, params.from)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if params.danmaku.ID == 0 {
			// 假发送弹幕无需调用社区治理服务
			return
		}
		foundUser, err := user.FindByUserID(params.danmaku.UserID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		var username string
		if foundUser != nil {
			username = foundUser.UserName
		}
		param := govern.NewAddDM(params.danmaku, *snd, params.equip, params.danmaku.DramaID, username, reqURLPath)
		param.Call()

		// 检查命中复审词库时推送到智能表格
		checkAndPushToSmartsheetDanmaku(&params.danmaku)
	})

	return params.danmaku, nil
}

func checkAndPushToSmartsheetDanmaku(danmaku *message.MSoundComment) {
	ok, matchWord, err := forbiddenwords.GetMatchedForbiddenWord(forbiddenwords.ForbiddenWordTypeCommentNotice, danmaku.Text)
	if err != nil {
		logger.Error(err)
		return
	}
	if ok {
		_ = pushToSmartsheetDanmaku(danmaku, matchWord)
	}
}

func newSmartsheetParamDanmaku(param *message.MSoundComment, matchWord string) *smartsheet.SheetAddRecordParam {
	return &smartsheet.SheetAddRecordParam{
		Channel: "main_review_" + scan.SceneDanmaku,
		Fields:  []string{"弹幕 ID", "音频 ID", "用户 ID", "弹幕内容", "命中词", "创建时间"},
		Values: []string{strconv.FormatInt(param.ID, 10), strconv.FormatInt(param.SoundID, 10),
			strconv.FormatInt(param.UserID, 10), param.Text, matchWord, util.TimeNow().Format(util.TimeFormatHMS)},
	}
}

func pushToSmartsheetDanmaku(param *message.MSoundComment, matchWord string) bool {
	addRecordParam := newSmartsheetParamDanmaku(param, matchWord)
	if addRecordParam == nil {
		return false
	}
	err := service.Smartsheet.AddRecord(addRecordParam)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

func newDmParams(c *handler.Context) (*dmParams, error) {
	var params struct {
		SoundID int64  `json:"sound_id"`
		UserID  int64  `json:"user_id"`
		Text    string `json:"text"`
		STime   string `json:"stime"`
		Size    byte   `json:"size"`
		Color   int    `json:"color"`
		Mode    byte   `json:"mode"`
		Pool    byte   `json:"pool"`
		From    int    `json:"from"`
		common.UserEquipment
	}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	mSoundComment := message.MSoundComment{
		SoundID: params.SoundID,
		UserID:  params.UserID,
		Text:    params.Text,
		STime:   params.STime,
		Size:    params.Size,
		Color:   params.Color,
		Pool:    params.Pool,
		Mode:    params.Mode,
	}
	mSoundComment.FillData()
	return &dmParams{
		danmaku: mSoundComment,
		from:    params.From,
		equip:   params.UserEquipment,
	}, nil
}

func reachDMLimit(userID int64) (int, error) {
	now := util.TimeNow()
	nowStamp := now.Unix()

	ctime, err := user.MowangskUser{}.GetCTimeByID(userID)
	if err != nil {
		return 0, err
	}

	var limitCount int
	// 若为注册时间不满 3 天的用户，视作新用户，新老用户每日可发送弹幕额度不同
	if (nowStamp - ctime) > 3600*24*3 {
		limitCount = sound.UserDMMaxNum
	} else {
		limitCount = sound.NewUserDMMaxNum
	}

	lockKey := serviceredis.LockUserDM1.Format(userID)
	pipe := service.Redis.Pipeline()
	incr := pipe.Incr(lockKey)
	ttl := pipe.TTL(lockKey)
	_, err = pipe.Exec()
	if err != nil {
		return 0, err
	}

	if ttl.Val() < 0 {
		// 获得第二天零点时间戳
		service.Redis.Expire(lockKey, util.NextDayTime(now).Sub(now))
	}

	if incr.Val() > int64(limitCount) {
		return limitCount, nil
	}
	return 0, nil
}

func addDm(dm *message.MSoundComment, snd *sound.MSound, ip string) (err error) {
	// TODO: 滚动、顶部、底部“土拨鼠”被视为劣质弹幕，后面可以制定更好的规则用来划分弹幕等级
	switch dm.Mode {
	case message.DanmakuModeSlide:
		if strings.HasPrefix(dm.Text, "啊啊啊啊啊") {
			dm.Pool = message.DanmakuPoolSpam
		}
	case message.DanmakuModeBottom, message.DanmakuModeTop:
		if strings.HasPrefix(dm.Text, "啊啊啊啊啊啊啊啊啊啊") {
			dm.Pool = message.DanmakuPoolSpam
		}
	}
	count, err := dm.CountRepeatDanmaku()
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  dm.UserID,
			"sound_id": dm.SoundID,
			"text":     dm.Text,
		}).Error(err)
		// PASS
	} else if count >= message.DanmakuMaxRepeatCount {
		// 用户在同一音频下发送相同弹幕的数量超过了上限，则新增的弹幕等级设为低质弹幕
		dm.Pool = message.DanmakuPoolLowQuality
	}
	if dm.Pool == 0 {
		dm.Pool = message.DanmakuPoolNormal
	}

	dm.IP = ip
	dm.IPDetail, _ = getIPDetailAndLocation(ip, dm.UserID)

	isShamSend, err := dm.Validate()
	if err != nil {
		if err == message.ErrViolationComment {
			delDanmaku := message.NewDeleteDanmaku(dm, soundcomment.DeleteTypeByViolation)
			if saveErr := delDanmaku.Archive(); saveErr != nil {
				logger.Errorf("save violation danmaku error: %v", err)
				// PASS
			}
		}
		return handler.NewActionError(http.StatusBadRequest, handler.CodeInvalidParam, err.Error())
	}

	if isShamSend {
		// 假发送
		return nil
	}

	// Doc: http://gorm.io/docs/transactions.html#A-Specific-Example
	err = message.SaveDmData(dm, snd)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	return nil
}

// limitUserAddDanmaku 限制用户在指定剧集下发送弹幕的频率
func limitUserAddDanmaku(checkInfo map[string]bool, userID, dramaID int64) error {
	if !checkInfo[dramainfo.DramaTypeLimitAddDanmaku] {
		return nil
	}

	logFields := logger.Fields{"user_id": userID, "drama_id": dramaID}
	// WORKAROUND: 字幕组用户不受限制，待《天官赐福》剧集上线后删除此逻辑
	key := keys.KeySubtitleTeamUsers0.Format()
	ok, err := service.Redis.SIsMember(key, userID).Result()
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
		return nil
	}
	if ok {
		return nil
	}

	// 限制用户在指定剧集下发送弹幕频率，5 秒内仅可发送一条弹幕
	key = keys.LockUserAddDramaDanmaku2.Format(userID, dramaID)
	ok, err = service.Redis.SetNX(key, 1, 5*time.Second).Result()
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
		return nil
	}
	if !ok {
		return actionerrors.ErrBadRequest(handler.CodeUserLimit, "您发送弹幕的频率过快")
	}
	return nil
}
