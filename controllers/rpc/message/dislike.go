package message

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
)

// ActionDislike 评论点踩或取消点踩
/**
 * @api {post} /rpc/message/dislike 评论点踩或取消点踩
 * @apiDescription 该接口拥有幂等性
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} comment_id 评论或子评论 ID
 * @apiParam {number=0,1} [sub=0] 是否为子评论（0：否；1：是）
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=0,1} action 点踩或取消点踩（0：取消点踩；1：点踩）
 * @apiParamExample {json} Request-Example:
 *     {
 *       "comment_id": 1,
 *       "sub": 0,
 *       "user_id": 1,
 *       "action": 1
 *     }
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info // 当前点状态，true 为已点踩，false 为未点踩
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "status": true
 *       }
 *     }
 */
func ActionDislike(c *handler.Context) (handler.ActionResponse, error) {
	var params LikeParams
	return likeOrDisLike(c, params, soundcomment.TypeCommentDislike)
}
