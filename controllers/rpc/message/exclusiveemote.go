package message

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/memoteexclusiveelement"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/util"
)

type emoteExclusiveParam struct {
	ElementID   int64 `json:"element_id"`
	ElementType int   `json:"element_type"`

	dramaID          int64
	emoteElementID   int64
	emoteElementType int
	resp             emoteExclusiveResp
	emotes           []*memoteexclusiveelement.MEmoteExclusiveElement
}

type emoteExclusiveRespItem struct {
	PackageID int64  `json:"package_id"`    // 表情包 ID
	Status    int    `json:"status"`        // 专属表情状态，专属表情解锁后才可发送，专属表情未解锁发送窗口展示该表情包中的锁定信息（tip 信息），-1: 专属表情已下线；0: 专属表情未解锁；1: 专属表情已解锁
	Tip       string `json:"tip,omitempty"` // 解锁说明，支持 HTML。仅在 status 为 0 时下发
	// 客户端是否展示评论内的专属表情，只需判断该表情的评论发送时间（ctime 字段）是否在展示时间内，即使该专属表情包 order 为 0 或专属表情为已下线状态
	// start_time 和 end_time 不下发时表示永久展示，若只存在 start_time 表示只判断历史评论展示范围的开始时间，或者只存在 end_time 表示只判断历史评论展示范围的结束时间
	StartTime *int64 `json:"start_time,omitempty"` // 评论中专属表情展示开始时间戳，单位：秒
	EndTime   *int64 `json:"end_time,omitempty"`   // 评论中专属表情展示截止时间戳，单位：秒
}

type emoteExclusiveResp struct {
	Emotes []emoteExclusiveRespItem `json:"emotes"`
}

// ActionExclusiveEmote 获取专属表情配置
/**
 * @api {post} /rpc/message/exclusive-emote 获取专属表情配置
 *
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} element_id 评论对象 ID
 * @apiParam {Number} element_type 被评论的元素类型
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "emotes": [ // 没有专属表情时 emotes 返回 []
 *           {
 *             "package_id": 2333, // 表情包 ID
 *             "status": 0, // 专属表情状态，专属表情解锁后才可发送，专属表情未解锁发送窗口展示该表情包中的锁定信息（tip 信息），-1: 专属表情已下线；0: 专属表情未解锁；1: 专属表情已解锁
 *             "tip": "《我在无限游戏里封神》广播剧全站播放量达到 1000W 时可解锁该表情哦！<br /><a href=\"https://www.test.com\">超链接</a>", // 解锁说明，支持 HTML。仅在 status 为 0 时下发
 *             "start_time": 1684737121,  // 评论中专属表情展示开始时间戳，单位：秒，若只存在 start_time 表示只判断历史评论展示范围的开始时间
 *             "end_time": 1694737121  // 评论中专属表情展示截止时间戳，单位：秒，只存在 end_time 表示只判断历史评论展示范围的结束时间
 *           },
 *           {
 *             "package_id": 2334,
 *             "status": 1,
 *             "start_time": 1684737121,
 *             "end_time": 1694737121
 *           }
 *         ]
 *     }
 */
func ActionExclusiveEmote(c *handler.Context) (handler.ActionResponse, error) {
	param := new(emoteExclusiveParam)
	err := c.BindJSON(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if _, exists := soundcomment.LabelOfType[param.ElementType]; !exists || param.ElementID <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = param.findExclusiveEmote(c)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(param.emotes) == 0 {
		return emoteExclusiveResp{
			Emotes: []emoteExclusiveRespItem{},
		}, nil
	}
	err = param.checkEmoteExclusiveUnlocked()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	err = param.buildResp()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return param.resp, nil
}

// findExclusiveEmote 查询专属表情
func (param *emoteExclusiveParam) findExclusiveEmote(c *handler.Context) (err error) {
	if param.ElementType == soundcomment.TypeSound {
		// 目前还没有针对音频的专属表情，需要查询音频对应的剧集来获取专属表情
		param.emoteElementID, err = DramaIDBySoundID(param.ElementID, c.ClientIP())
		if err != nil {
			return err
		}
		if param.emoteElementID == 0 {
			return nil
		}
		param.dramaID = param.emoteElementID
		param.emoteElementType = memoteexclusiveelement.ElementTypeDrama
	} else {
		// TODO: 之后可能需要支持其他类型的专属表情
		return nil
	}
	param.emotes, err = memoteexclusiveelement.GetExclusiveEmotes(param.emoteElementID, param.emoteElementType)
	if err != nil {
		return err
	}
	return nil
}

// checkEmoteExclusiveUnlocked 是否解锁专属表情，如果解锁更新专属表情
// 目前只有一种根据剧集播放量来解锁专属表情的方式，后续有其他解锁方式调整此函数即可
func (param *emoteExclusiveParam) checkEmoteExclusiveUnlocked() error {
	for _, emote := range param.emotes {
		if emote.MoreInfo.Unlock {
			continue
		}

		// 判断专属表情是否解锁
		var unlock bool
		if param.emoteElementType == memoteexclusiveelement.ElementTypeDrama {
			viewCount, err := dramainfo.GetDramaViewCount(param.dramaID)
			if err != nil {
				return err
			}
			unlock = viewCount >= emote.MoreInfo.UnlockScore
		}
		if !unlock {
			continue
		}

		// 解锁专属表情，更新专属表情相关字段
		updates := map[string]interface{}{
			"modified_time": util.TimeNow().Unix(),
		}
		emote.MoreInfo.Unlock = unlock
		encodeMore, err := json.Marshal(emote.MoreInfo)
		if err != nil {
			return err
		}
		updates["more"] = string(encodeMore)
		// WORKAROUND: 理论上应该使用 more 中 unlock 字段做乐观锁，目前 sqlite 不支持 JSON_CONTAINS 函数，先暂时用 modified_time
		err = emote.DB().Where("id = ? AND modified_time = ?", emote.ID, emote.ModifiedTime).Updates(updates).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// buildResp 构造 resp
func (param *emoteExclusiveParam) buildResp() error {
	param.resp.Emotes = make([]emoteExclusiveRespItem, 0, len(param.emotes))
	for _, emote := range param.emotes {
		resp := emoteExclusiveRespItem{
			PackageID: emote.PackageID,
		}
		if emote.MoreInfo.Unlock {
			resp.Status = memoteexclusiveelement.StatusUnlock
		} else {
			resp.Status = memoteexclusiveelement.StatusLocked
		}
		if emote.StartTime > 0 {
			resp.StartTime = &emote.StartTime
		}
		if emote.EndTime > 0 {
			resp.EndTime = &emote.EndTime
			if emote.EndTime < util.TimeNow().Unix() {
				resp.Status = memoteexclusiveelement.StatusOffline
			}
		}
		if resp.Status == memoteexclusiveelement.StatusLocked {
			resp.Tip = emote.MoreInfo.Tip
		}
		param.resp.Emotes = append(param.resp.Emotes, resp)
	}
	return nil
}
