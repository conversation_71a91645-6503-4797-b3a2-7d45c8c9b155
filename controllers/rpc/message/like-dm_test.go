package message

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

const testUser1ID int64 = 1
const testUser2ID int64 = 2

func TestActionLikeDm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.MessageDB.Table(message.MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUser1ID).Delete("").Error)

	// 测试参数错误的情况
	params := LikeDmParams{
		DanmakuID:   0,
		ElementType: message.ElementTypeSound,
		UserID:      0,
		Action:      ActionTypeAdd,
	}
	api := "/rpc/message/like-dm"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionLikeDm(c)
	assert.EqualError(err, "参数错误")

	// 测试弹幕不存在的情况
	params = LikeDmParams{
		DanmakuID:   99999999999,
		ElementType: message.ElementTypeSound,
		UserID:      testUser1ID,
		Action:      ActionTypeAdd,
	}
	c = handler.NewRPCTestContext(api, params)
	_, err = ActionLikeDm(c)
	assert.EqualError(err, "该弹幕不存在")

	// 测试对弹幕进行点赞的情况
	// 创建测试用弹幕
	danmaku := message.MSoundComment{
		SoundID: 233,
		Text:    "测试弹幕 - go",
		UserID:  testUser2ID,
		LikeNum: 5,
	}
	err = danmaku.Insert(service.DB)
	require.NoError(err)
	params = LikeDmParams{
		DanmakuID:   danmaku.ID,
		ElementType: message.ElementTypeSound,
		UserID:      testUser1ID,
		Action:      ActionTypeAdd,
	}
	// 删除相关测试缓存
	lock := serviceredis.LockUserLikeDanmaku2.Format(TestUser1ID, danmaku.ID)
	require.NoError(service.Redis.Del(lock).Err())

	// 测试点赞黑名单用户弹幕
	// 创建黑名单关系
	blockUser := blackuser.Model{
		BigID:   TestUser2ID,
		SmallID: TestUser1ID,
	}
	blockUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		Assign(blockUser).FirstOrCreate(&blockUser).Error
	require.NoError(err)

	c = handler.NewRPCTestContext(api, params)
	result, err := ActionLikeDm(c)
	require.NoError(err)
	// 验证点赞成功但没有点赞数据（假点赞）
	assert.True(result.(LikeDmResp).Status)
	exists, err := message.MUserLikeDanmaku{}.Exists(service.MessageDB, message.ElementTypeSound, danmaku.ID, TestUser1ID)
	require.NoError(err)
	assert.False(exists)
	// 删除黑名单关系数据
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).UpdateColumn("status", 0)

	// 测试正常点赞弹幕的情况
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionLikeDm(c)
	require.NoError(err)
	assert.True(result.(LikeDmResp).Status)
	// 验证有点赞数据
	exists, err = message.MUserLikeDanmaku{}.Exists(service.MessageDB, message.ElementTypeSound, danmaku.ID, TestUser1ID)
	require.NoError(err)
	assert.True(exists)

	// 测试操作频繁情况下进行点赞
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	require.NoError(err)
	require.True(ok)
	c = handler.NewRPCTestContext(api, params)
	_, err = ActionLikeDm(c)
	assert.EqualError(err, "操作频繁，请稍后再试")
	require.NoError(service.Redis.Del(lock).Err())
	// 验证点赞数量
	var likeNum int64
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(6), likeNum)

	// 测试弹幕重复点赞情况（接口应保持幂等性）
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionLikeDm(c)
	assert.NoError(err)
	assert.True(result.(LikeDmResp).Status)
	// 验证有点赞数据
	exists, err = message.MUserLikeDanmaku{}.Exists(service.MessageDB, message.ElementTypeSound, danmaku.ID, TestUser1ID)
	require.NoError(err)
	assert.True(exists)

	// 测试取消点赞的情况
	params.Action = 0
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionLikeDm(c)
	assert.NoError(err)
	assert.False(result.(LikeDmResp).Status)
	// 验证无点赞数据
	exists, err = message.MUserLikeDanmaku{}.Exists(service.MessageDB, message.ElementTypeSound, danmaku.ID, TestUser1ID)
	require.NoError(err)
	assert.False(exists)
	// 验证点赞数量是否正确
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(5), likeNum)
}

func TestValidateElementType(t *testing.T) {
	assert := assert.New(t)

	assert.True(validateElementType(message.ElementTypeSound))
	assert.True(validateElementType(message.ElementTypeSound))
	assert.False(validateElementType(0))
}

func TestFindDanmakuInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.MessageDB.Table(message.MSoundComment{}.TableName()).Where("user_id = ?", testUser1ID).Delete("").Error)
	require.NoError(service.MessageDB.Table(message.MDanmaku{}.TableName()).Where("user_id = ?", testUser1ID).Delete("").Error)

	// 测试获取不存在弹幕信息的情况
	_, err := findDanmakuInfo(message.ElementTypeSound, 99999999999)
	assert.EqualError(err, "该弹幕不存在")

	// 测试获取音频弹幕信息的情况
	// 创建测试用音频弹幕
	danmaku := message.MSoundComment{
		SoundID: 233,
		Text:    "测试弹幕 - go",
		UserID:  testUser1ID,
		LikeNum: 5,
	}
	err = danmaku.Insert(service.DB)
	require.NoError(err)
	danmakuInfo, err := findDanmakuInfo(message.ElementTypeSound, danmaku.ID)
	require.NoError(err)
	assert.Equal(testUser1ID, danmakuInfo.UserID)
	assert.Equal(int64(233), danmakuInfo.ElementID)
	assert.Equal(message.ElementTypeSound, danmakuInfo.ElementType)

	// 测试获取互动剧节点弹幕信息的情况
	// 创建测试用音频弹幕
	mDanmaku := message.MDanmaku{
		ElementID:   234,
		ElementType: message.ElementTypeNode,
		Text:        "测试弹幕 - go",
		UserID:      testUser1ID,
	}
	err = mDanmaku.DB().Create(&mDanmaku).Error
	require.NoError(err)
	danmakuInfo, err = findDanmakuInfo(message.ElementTypeNode, mDanmaku.ID)
	require.NoError(err)
	assert.Equal(testUser1ID, danmakuInfo.UserID)
	assert.Equal(int64(234), danmakuInfo.ElementID)
	assert.Equal(message.ElementTypeNode, danmakuInfo.ElementType)
}
