package message

import (
	"errors"
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 评论推送标题类型
const (
	TitleTypeComment      = iota // 评论
	TitleTypeReplyComment        // 回复评论
)

// 检查并发送点赞推送消息
func checkAndSendLikeMessagePush(fromUserID, toUserID int64) error {
	isAllowed, err := user.IsMessagePushAllowed(toUserID, user.AppPushTypeLikeComment)
	if err != nil {
		return err
	}
	if !isAllowed {
		return nil
	}
	return sendLikeMessagePush(fromUserID, toUserID)
}

// sendLikeMessagePush 发送点赞推送 userID 点赞用户 ID, commentUserID 被点赞用户 ID
func sendLikeMessagePush(userID, commentUserID int64) error {
	user, err := user.FindByUserID(userID)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("给评论点赞的用户不存在")
	}
	msg := pushservice.Push{
		TargetType: pushservice.TargetUsers,
		SourceIDs:  []interface{}{commentUserID},
		Message:    "赞了你的评论",
		Title:      user.UserName,
		OpenURL:    params.MessageLikeOpenURL,
		PushType:   pushservice.PushTypeMessageLike,
		Policy: &pushservice.PushPolicy{
			Type:       pushservice.TypeCommentLike,
			Rule:       pushservice.RuleDaily,
			ExpireTime: util.TimeNow().Add(time.Hour * 3).Unix(),
		},
		Payload: map[string]interface{}{
			"image_url": user.IconURL, // 点赞用户头像 URL
		},
	}
	return service.PushService.SendPush(msg)
}

// checkAndSendCommentPush 检查并推送评论提醒 commentUser 发评论用户信息, noticesUserID 被评论用户 IDs, titleType 推送标题类型 0: 评论; 1: 回复评论
func checkAndSendCommentPush(commentUser *user.MowangskUser, noticesUserIDs []int64, commentContent string, titleType int) error {
	allowedUserIDs, err := user.AllowedPushUserIDs(noticesUserIDs, user.AppPushTypeComment)
	if err != nil {
		return err
	}
	if len(allowedUserIDs) == 0 {
		return nil
	}
	title := fmt.Sprintf("%s 评论你", commentUser.UserName)
	if titleType == TitleTypeReplyComment {
		title = fmt.Sprintf("%s 回复你", commentUser.UserName)
	}
	return sendCommentPush(allowedUserIDs, title, commentContent, commentUser.IconURL)
}

// sendCommentPush 推送评论提醒
func sendCommentPush(userIDs []int64, title, message, imageURL string) error {
	sourceIDs := make([]interface{}, len(userIDs))
	for i, userID := range userIDs {
		sourceIDs[i] = userID
	}
	msg := pushservice.Push{
		SourceIDs:  sourceIDs,
		TargetType: pushservice.TargetUsers,
		Title:      title,
		Message:    message,
		OpenURL:    params.MessageCommentOpenURL,
		PushType:   pushservice.PushTypeMessageComment,
		Policy: &pushservice.PushPolicy{
			Type:       pushservice.TypeComment,
			Rule:       pushservice.RuleDaily,
			ExpireTime: util.TimeNow().Add(time.Hour * 3).Unix(),
		},
		Payload: map[string]interface{}{
			"image_url": imageURL,
		},
	}
	return service.PushService.SendPush(msg)
}

// checkAndSendAtPush 检查并推送 @ 提醒 commentUser 发评论用户信息, atUserIDs 被 @ 用户 IDs, commentContent 评论内容
func checkAndSendAtPush(commentUser *user.MowangskUser, atUserIDs []int64, commentContent string) error {
	allowedUserIDs, err := user.AllowedPushUserIDs(atUserIDs, user.AppPushTypeAt)
	if err != nil {
		return err
	}
	if len(allowedUserIDs) == 0 {
		return nil
	}
	title := fmt.Sprintf("%s @ 你", commentUser.UserName)
	return sendAtPush(allowedUserIDs, title, commentContent, commentUser.IconURL)
}

// sendAtPush 推送 @ 提醒
func sendAtPush(userIDs []int64, title, message, imageURL string) error {
	sourceIDs := make([]interface{}, len(userIDs))
	for i, userID := range userIDs {
		sourceIDs[i] = userID
	}
	msg := pushservice.Push{
		SourceIDs:  sourceIDs,
		TargetType: pushservice.TargetUsers,
		Title:      title,
		Message:    message,
		OpenURL:    params.MessageAtOpenURL,
		PushType:   pushservice.PushTypeMessageAt,
		Policy: &pushservice.PushPolicy{
			Type:       pushservice.TypeAt,
			Rule:       pushservice.RuleDaily,
			ExpireTime: util.TimeNow().Add(time.Hour * 3).Unix(),
		},
		Payload: map[string]interface{}{
			"image_url": imageURL,
		},
	}
	return service.PushService.SendPush(msg)
}
