package message

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/govern"
)

func TestActionReportResult(t *testing.T) {
	require := require.New(t)

	comment := map[string]interface{}{
		"type":      0,
		"unique_id": 232324,
		"result":    govern.ReportResultPass,
	}
	c := handler.NewRPCTestContext("/rpc/message/report", comment)
	_, err := ActionReportResult(c)
	require.EqualError(err, "参数不合法")

	comment = map[string]interface{}{
		"type":      reportResultTypeReply,
		"unique_id": -1,
		"result":    govern.ReportResultPass,
	}
	c = handler.NewRPCTestContext("/rpc/message/report", comment)
	_, err = ActionReportResult(c)
	require.EqualError(err, "参数不合法")

	comment = map[string]interface{}{
		"type":      reportResultTypeReply,
		"unique_id": 232324,
		"result":    "",
	}
	c = handler.NewRPCTestContext("/rpc/message/report", comment)
	_, err = ActionReportResult(c)
	require.EqualError(err, "参数不合法")

	comment = map[string]interface{}{
		"type":      reportResultTypeReply,
		"unique_id": 232324,
		"result":    govern.ReportResultPass,
	}
	c = handler.NewRPCTestContext("/rpc/message/report", comment)
	_, err = ActionReportResult(c)
	require.NoError(err)
}
