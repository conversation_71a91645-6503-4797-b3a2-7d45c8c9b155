package message

import (
	"fmt"
	"net/http"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// LikeDmParams struct for like-dm api or dislike api
type LikeDmParams struct {
	DanmakuID   int64 `json:"danmaku_id"`
	ElementType int   `json:"element_type"`
	UserID      int64 `json:"user_id"`
	Action      int   `json:"action"`
}

// LikeDmResp 接口响应结构
type LikeDmResp struct {
	Status bool `json:"status"`
}

// danmakuInfo 弹幕信息
type danmakuInfo struct {
	UserID      int64
	ElementID   int64
	ElementType int
}

// ActionLikeDm 弹幕点赞或取消点赞
/**
 * @api {post} /rpc/message/like-dm 弹幕点赞或取消点赞
 * @apiDescription 该接口拥有幂等性
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} danmaku_id 弹幕 ID
 * @apiParam {number=1,2} [element_type=1] 弹幕所属元素类型，1：音频；2：互动剧节点
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=0,1} [action=0] 点赞或取消点赞（0：取消点赞；1：点赞）
 * @apiParamExample {json} Request-Example:
 *     {
 *       "danmaku_id": 1,
 *       "element_type": 1,
 *       "user_id": 1,
 *       "action": 1
 *     }
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "status": true // 当前点赞状态，true 为已点赞，false 为未点赞
 *       }
 *     }
 */
func ActionLikeDm(c *handler.Context) (handler.ActionResponse, error) {
	var params LikeDmParams
	err := c.BindJSON(&params)
	danmakuID := params.DanmakuID
	userID := params.UserID
	elementType := params.ElementType
	if elementType == 0 {
		elementType = message.ElementTypeSound
	}
	if err != nil || danmakuID <= 0 || params.Action < 0 || userID <= 0 || !validateElementType(elementType) {
		return nil, actionerrors.ErrParams
	}
	// 对操作进行加锁，避免频繁操作
	lock := serviceredis.LockUserLikeDanmaku2.Format(userID, danmakuID)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)

	danmakuInfo, err := findDanmakuInfo(elementType, danmakuID)
	if err != nil {
		return nil, err
	}
	if ActionTypeAdd == params.Action {
		// 判断当前用户和发送弹幕用户黑名单关系
		status1, status2, err := blackuser.GetBlacklistRelation(userID, danmakuInfo.UserID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if status1 || status2 {
			// 当前用户和发送弹幕用户存在黑名单关系，则为假点赞
			// 返回点赞成功状态
			return LikeDmResp{
				Status: true,
			}, nil
		}
	}

	// 点赞数据调整
	hasLike, err := message.MUserLikeDanmaku{}.Exists(service.MessageDB, elementType, danmakuID, userID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	err = servicedb.Tx(service.MessageDB, func(tx *gorm.DB) error {
		if params.Action == ActionTypeAdd && !hasLike {
			// 若为点赞行为且未点赞，新增点赞记录
			likeDanmaku := message.MUserLikeDanmaku{
				UserID:      userID,
				DanmakuID:   danmakuID,
				ElementID:   danmakuInfo.ElementID,
				ElementType: danmakuInfo.ElementType,
			}
			return likeDanmaku.Save(tx)
		} else if params.Action != ActionTypeAdd && hasLike {
			// 若为取消点赞行为且存在点赞记录时，软删除该记录
			return message.MUserLikeDanmaku{}.Delete(tx, elementType, danmakuID, userID)
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 返回当前点赞状态
	return LikeDmResp{
		Status: params.Action == ActionTypeAdd,
	}, nil
}

func findDanmakuInfo(elementType int, danmakuID int64) (*danmakuInfo, error) {
	var err error
	var danmaku *danmakuInfo
	switch elementType {
	case message.ElementTypeSound:
		// 查询音频弹幕
		mSoundComment := message.MSoundComment{}
		err = mSoundComment.DB().Select("sound_id, user_id").Where("id = ?", danmakuID).Scan(&mSoundComment).Error
		if err == nil {
			danmaku = &danmakuInfo{
				UserID:      mSoundComment.UserID,
				ElementID:   mSoundComment.SoundID,
				ElementType: elementType,
			}
		}
	case message.ElementTypeNode:
		// 查询互动剧节点弹幕
		mDanmaku := message.MDanmaku{}
		err = mDanmaku.DB().Select("element_id, user_id").Where("id = ?", danmakuID).Scan(&mDanmaku).Error
		if err == nil {
			danmaku = &danmakuInfo{
				UserID:      mDanmaku.UserID,
				ElementID:   mDanmaku.ElementID,
				ElementType: elementType,
			}
		}
	default:
		msg := fmt.Sprintf("wrong element_type: %d", elementType)
		logger.WithField("danmaku_id", danmakuID).Error(msg)
		return nil, handler.NewActionError(http.StatusBadRequest, handler.CodeInvalidParam, msg)
	}
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrDanmakuNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return danmaku, err
}

func validateElementType(elementType int) bool {
	return elementType == message.ElementTypeSound || elementType == message.ElementTypeNode
}
