package message

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestActionDeleteDm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := deleteDanmakuParams{
		DanmakuID: 233,
		UserID:    0,
	}

	// 测试错误参数
	url := "/rpc/message/del-dm"
	c := handler.NewRPCTestContext(url, params)
	_, err := ActionDeleteDm(c)
	assert.EqualError(err, "参数错误")

	// 测试弹幕不存在
	params.DanmakuID = 1
	params.UserID = 12
	c = handler.NewRPCTestContext(url, params)
	_, err = ActionDeleteDm(c)
	assert.EqualError(err, "该弹幕不存在")

	// 添加音频信息
	sound := sound.MSound{
		ID:           2333,
		UserID:       14,
		CommentCount: 2,
	}
	err = service.DB.Table(sound.TableName()).Create(&sound).Error
	require.NoError(err)

	// 添加弹幕
	delDanmaku := message.MSoundComment{
		SoundID: 2333,
		UserID:  13,
		Text:    "测试",
	}
	err = service.MessageDB.Table(delDanmaku.TableName()).Create(&delDanmaku).Error
	require.NoError(err)

	// 测试非弹幕作者，非音频作者删除弹幕
	params.DanmakuID = delDanmaku.ID
	params.UserID = 15
	c = handler.NewRPCTestContext(url, params)
	_, err = ActionDeleteDm(c)
	assert.EqualError(err, "你不是弹幕或者音频拥有者，无权限操作")

	// 测试加锁
	key := serviceredis.LockUserDeleteDanmaku2.Format(params.UserID, params.DanmakuID)
	_, err = service.Redis.SetNX(key, "1", 10*time.Second).Result()
	require.NoError(err)
	c = handler.NewRPCTestContext(url, params)
	_, err = ActionDeleteDm(c)
	assert.EqualError(err, "操作频繁，请稍后再试")
	_, err = service.Redis.Del(key).Result()
	require.NoError(err)

	// 测试音频作者删除弹幕
	params.UserID = 14
	c = handler.NewRPCTestContext(url, params)
	res, err := ActionDeleteDm(c)
	require.NoError(err)
	assert.True(res.(deleteDanmakuResp).Status)

	// 测试弹幕作者删除弹幕
	delDanmaku.ID = 0
	err = service.MessageDB.Table(delDanmaku.TableName()).Create(&delDanmaku).Error
	require.NoError(err)
	params.DanmakuID = delDanmaku.ID
	params.UserID = 13
	c = handler.NewRPCTestContext(url, params)
	res, err = ActionDeleteDm(c)
	require.NoError(err)
	assert.True(res.(deleteDanmakuResp).Status)
}
