package message

import "github.com/MiaoSiLa/missevan-go/service"

// DramaIDBySoundID 通过 soundID 获取 dramaID，获取 soundID 不存在返回 0
// Deprecated: use dramainfo.GetDramaIDsBySoundIDs
// TODO: 缓存音频 ID 对应的剧集 ID
func DramaIDBySoundID(soundID int64, clientIP string) (int64, error) {
	var res []struct {
		DramaID int64 `json:"drama_id"`
	}
	reqData := map[string]interface{}{
		"sound_ids": []int64{soundID},
	}
	err := service.MRPC.Call("drama://api/get-dramaid-by-soundid", clientIP, reqData, &res)
	if err != nil {
		return 0, err
	}
	if len(res) > 0 {
		return res[0].DramaID, nil
	}
	return 0, nil
}
