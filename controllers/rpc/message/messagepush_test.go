package message

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, TitleTypeComment)
	assert.Equal(1, TitleTypeReplyComment)
}

func TestCheckAndSendLikeMessagePush(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试正常发推送
	testUserIDs := []int64{12, 13}
	err := checkAndSendLikeMessagePush(testUserIDs[0], testUserIDs[1])
	require.NoError(err)
}

func TestSendLikeMessagePush(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试用户不存在
	testUserIDs := []int64{-1, 12, 13}
	err := sendLikeMessagePush(testUserIDs[0], testUserIDs[1])
	require.Error(err, "给评论点赞的用户不存在")

	// 测试正常发推送
	err = sendLikeMessagePush(testUserIDs[1], testUserIDs[2])
	require.NoError(err)
}

func TestCheckAndSendCommentPush(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试正常发推送
	testCommentUser := &user.MowangskUser{
		Simple: user.Simple{
			ID: testUser2ID,
		},
	}
	err := checkAndSendCommentPush(testCommentUser, []int64{1, 2}, "真不错啊", TitleTypeReplyComment)
	require.NoError(err)
}

func TestSendCommentPush(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试正常发推送
	err := sendCommentPush([]int64{101}, "真不错啊", "always 评论你", "https://test.jpg")
	require.NoError(err)
}

func TestCheckAndSendAtPush(t *testing.T) {
	require := require.New(t)

	// 模拟推送服务
	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试正常发推送
	testCommentUser := &user.MowangskUser{
		Simple: user.Simple{
			ID:       testUser2ID,
			UserName: "测试用户",
		},
	}
	err := checkAndSendAtPush(testCommentUser, []int64{1, 2}, "这是一条@测试用户 的评论")
	require.NoError(err)
}

func TestSendAtPush(t *testing.T) {
	require := require.New(t)

	// 模拟推送服务
	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	// 测试正常发推送
	err := sendAtPush([]int64{101}, "测试标题", "测试内容", "https://test.jpg")
	require.NoError(err)
}
