package message

import (
	"encoding/json"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/commentnotice"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

const testSoundID = int64(233)
const testSpecialSoundID = int64(88771)
const testDiscontinuedSoundID = int64(88772)
const testNotSoundID = int64(999999)
const testDramaID = 39588
const testUserID = 765325

func TestAllowComment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := serviceredis.KeyForbiddenCommentElement1.Format(soundcomment.TypeSound)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.SAdd(key, 365414413).Err())

	testCommentType := -999
	isAllowed, label, err := allowComment(testCommentType, 0)
	require.NoError(err)
	assert.False(isAllowed)
	assert.Empty(label)

	isAllowed, label, err = allowComment(soundcomment.TypeSound, 365414413)
	require.NoError(err)
	assert.False(isAllowed)
	assert.Equal("音频", label)

	isAllowed, label, err = allowComment(soundcomment.TypeSound, 1111)
	require.NoError(err)
	assert.True(isAllowed)
	assert.Equal("音频", label)
}

func TestCheckDramaCommentStatus(t *testing.T) {
	assert := assert.New(t)

	err := checkDramaCommentStatus(nil)
	assert.NoError(err)

	info := map[string]bool{
		dramainfo.DramaTypeSpecial: true,
	}
	err = checkDramaCommentStatus(info)
	assert.EqualError(err, "当前内容是限定音 暂不能进行此操作")

	info[dramainfo.DramaTypeSpecial] = false
	info[dramainfo.DramaTypeSensitive] = true
	err = checkDramaCommentStatus(info)
	assert.EqualError(err, "评论区已关闭")
}

func TestGetCommentEmoteNum(t *testing.T) {
	assert := assert.New(t)

	// 测试评论中没有表情
	assert.Zero(getCommentEmoteNum("[ ]"))

	assert.Zero(getCommentEmoteNum("[A B]"))

	// 测试评论中有表情
	assert.Equal(1, getCommentEmoteNum("[[]]"))

	assert.Equal(2, getCommentEmoteNum(strings.Repeat("[收听]233[]233[  ]233[\n\n]", 2)))
}

func TestActionAddComment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(346286)
	userIP := "**************"
	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		ac := sso.Account{}
		if id, ok := i.(map[string]int64)["user_id"]; ok && id != 12 {
			ac.Mobile = "***********"
		}
		return ac, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(i interface{}) (interface{}, error) {
		return []map[string]int64{{
			"drama_id": 18,
			"sound_id": 1,
		}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []handler.M{{"score": 0, "pass": true, "labels": []string{"test"}}}, nil
	})
	defer cancel()

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	cancelMain := mrpc.SetMock("mrpc://missevan-main/person/list-avatar-frame",
		func(interface{}) (interface{}, error) {
			return map[int64]userapi.AvatarFrameInfo{
				11: {
					ID:             11,
					Name:           "小耳朵11",
					AvatarFrameURL: "https://static.maoercdn.com/test_11.webp",
					IconURL:        "https://static.maoercdn.com/test_11.webp",
				},
				12: {
					ID:             12,
					Name:           "小耳朵12",
					AvatarFrameURL: "https://static.maoercdn.com/test_12.webp",
					IconURL:        "https://static.maoercdn.com/test_12.webp",
				},
				346286: {
					ID:             346286,
					Name:           "小耳朵346286",
					AvatarFrameURL: "https://static.maoercdn.com/test_346286.webp",
					IconURL:        "https://static.maoercdn.com/test_346286.webp",
				},
			}, nil
		})
	defer cancelMain()

	comment := map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment",
		"user_id":         userID,
		"from":            user.FromApp,
		"ip":              userIP,
	}
	c := handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	info, err := ActionAddComment(c)
	assert.NoError(err)
	require.IsType(soundcomment.Comment{}, info)
	res := info.(soundcomment.Comment)
	assert.Equal(userIP, res.IP)
	var ipDetail struct {
		CountryName string `json:"country_name"`
		RegionName  string `json:"region_name"`
		CityName    string `json:"city_name"`
	}
	err = json.Unmarshal(res.IPDetail, &ipDetail)
	require.NoError(err)
	assert.Equal("中国", ipDetail.CountryName)
	assert.Equal("山东", ipDetail.RegionName)
	assert.Equal("济南", ipDetail.CityName)

	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "",
		"user_id":         userID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "评论不能为空~")

	comment = map[string]interface{}{
		"c_type":          -1,
		"element_id":      1,
		"comment_content": "comment",
		"user_id":         userID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "不支持的评论类型")

	// 测试会员音需要会员才能评论
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      3679,
		"comment_content": "comment",
		"user_id":         10,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "开通会员即可评论哦~")

	// 会员评论会员音
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      3679,
		"comment_content": "comment",
		"user_id":         11,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.NoError(err)

	lock := serviceredis.LockUserComment1.Format(userID)
	service.Redis.Set(lock, soundcomment.UserCommentMaxNum, 10*time.Second)
	defer service.Redis.Del(lock)
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment",
		"user_id":         userID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "今日评论额度已用完")

	service.Redis.ZAdd(serviceredis.KeyBlackList0.Format(),
		&redis.Z{Score: float64(util.TimeNow().Add(10 * time.Second).Unix()), Member: userID})
	defer service.Redis.ZRem(serviceredis.KeyBlackList0.Format(), userID)
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      1,
		"comment_content": "comment",
		"user_id":         userID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "您的账号有可疑记录，暂时被系统封停")

	var id int64
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("mobile = ''").Limit(1).Select("id").Row().Scan(&id)
	require.NoError(err)
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment",
		"from":            user.FromApp,
		"scenario":        scenarioDefault,
		"user_id":         id,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "绑定手机就可以发送评论了哦")

	// 测试后台评论不检查是否绑定手机号
	comment["scenario"] = scenarioBackend
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.NoError(err)

	// 创建黑名单关系
	bigID := int64(3013097)
	blockUser := blackuser.Model{
		BigID:   bigID,
		SmallID: userID,
	}
	blockUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blockUser.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		Assign(blockUser).FirstOrCreate(&blockUser).Error
	require.NoError(err)
	// 测试评论对象所属 UP 主存在拉黑发表评论用户
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment",
		"user_id":         bigID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "由于 UP 主设置，你无法回复")

	// 测试发表评论用户拉黑评论对象所属 UP 主
	service.MessageDB.Table(blockUser.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		UpdateColumn("status", blackuser.StatusBigBanSmall)
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment0",
		"user_id":         bigID,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.EqualError(err, "评论失败，请将 UP 主移除黑名单再试")
	// 删除黑名单关系
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).UpdateColumn("status", 0)

	// 测试添加评论成功
	_, err = service.Redis.ZRem(serviceredis.KeyBlackList0.Format(), userID).Result()
	require.NoError(err)
	err = service.Redis.Del(lock).Err()
	require.NoError(err)
	comment = map[string]interface{}{
		"c_type":          soundcomment.TypeSound,
		"element_id":      testSoundID,
		"comment_content": "comment1",
		"user_id":         userID,
		"from":            user.FromApp,
		"ip":              userIP,
	}
	c = handler.NewRPCTestContext("/rpc/message/add-comment", comment)
	_, err = ActionAddComment(c)
	assert.NoError(err)
}

func TestValidateComment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	checkRes := &scan.CheckResult{Pass: true}
	checkText := func(userInfo util.UserContext, text string, scene string,
		opts ...scan.CheckTextOption) (*scan.CheckResult, error) {
		return checkRes, nil
	}

	c := handler.NewRPCTestContext("/", nil)
	comment := &soundcomment.Comment{}
	assert.Equal(actionerrors.ErrEmptyComment, validateComment(c, comment, 0, scenarioDefault, checkText))

	comment.Content = ""
	assert.EqualError(validateComment(c, comment, 0, scenarioDefault, checkText), "评论不能为空~")

	// 测试 emoji 为一个字符
	comment.Content = strings.Repeat("🇨🇳", soundcomment.CommentMaxLength)
	require.NoError(validateComment(c, comment, 0, scenarioDefault, checkText))

	comment.Content = strings.Repeat("a", soundcomment.CommentMaxLength+1)
	assert.Equal(actionerrors.ErrBadRequest(handler.CodeInvalidParam, "字数太多装不下啦~"),
		validateComment(c, comment, 0, scenarioDefault, checkText))

	// 测试评论中表情数量没有超过上限
	comment.Content = strings.Repeat("[ ]", commentEmoteMaxNum+1)
	require.NoError(validateComment(c, comment, 0, scenarioDefault, checkText))

	comment.Content = strings.Repeat("[收听]", commentEmoteMaxNum)
	require.NoError(validateComment(c, comment, 0, scenarioDefault, checkText))

	// 测试评论中表情数量超过上限
	comment.Content = strings.Repeat("[收听]", commentEmoteMaxNum+1)
	assert.EqualError(validateComment(c, comment, 0, scenarioDefault, checkText), "表情数量超过上限")

	comment.Content = "content"
	checkRes.Pass = false
	err := validateComment(c, comment, 0, scenarioDefault, checkText)
	assert.Equal(actionerrors.ErrCommentViolation, err)
	delComment := new(soundcomment.DeleteComment)
	require.NoError(service.MessageDB.
		Table(delComment.TableName()).
		Where("comment_content = ?", comment.Content).
		Find(&delComment).Error)
	assert.Equal(soundcomment.DeleteTypeByViolation, delComment.DeleteType)

	assert.NoError(validateComment(c, comment, 0, scenarioBackend, checkText))

	checkRes.Pass = true
	checkRes.Labels = []string{"evil"}
	require.NoError(validateComment(c, comment, 0, scenarioDefault, checkText))
	assert.Equal(soundcomment.CheckedViolation, comment.Checked)

	checkRes.Labels = []string{}
	comment.Checked = soundcomment.CheckedNormal
	require.NoError(validateComment(c, comment, 0, scenarioDefault, checkText))
	assert.Equal(soundcomment.CheckedNormal, comment.Checked)

	comment.Content = "公费追星"
	comment.Checked = soundcomment.CheckedNormal
	require.NoError(validateComment(c, comment, testDramaID, scenarioDefault, checkText))
	assert.Equal(soundcomment.CheckedViolation, comment.Checked)

	comment.Content = "你好"
	comment.Checked = soundcomment.CheckedNormal
	require.NoError(validateComment(c, comment, testDramaID, scenarioDefault, checkText))
	assert.Equal(soundcomment.CheckedNormal, comment.Checked)

	comment.Content = "你好TeSt"
	comment.Checked = soundcomment.CheckedNormal
	require.NoError(validateComment(c, comment, testDramaID, scenarioDefault, checkText))
	assert.Equal(soundcomment.CheckedViolation, comment.Checked)
}

func TestActionAddSubComment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return sso.Account{
			Mobile: "***********",
		}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(i interface{}) (interface{}, error) {
		return []map[string]int64{{
			"drama_id": 18,
			"sound_id": 1,
		}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []handler.M{{"score": 0, "pass": true, "labels": []string{"test"}}}, nil
	})
	defer cancel()

	cleanup := mrpc.SetMock("pushservice://api/push", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	cancelMain := mrpc.SetMock("mrpc://missevan-main/person/list-avatar-frame",
		func(interface{}) (interface{}, error) {
			return map[int64]userapi.AvatarFrameInfo{
				11: {
					ID:             11,
					Name:           "小耳朵11",
					AvatarFrameURL: "https://static.maoercdn.com/test_11.webp",
					IconURL:        "https://static.maoercdn.com/test_11.webp",
				},
				12: {
					ID:             12,
					Name:           "小耳朵12",
					AvatarFrameURL: "https://static.maoercdn.com/test_12.webp",
					IconURL:        "https://static.maoercdn.com/test_12.webp",
				},
				346286: {
					ID:             346286,
					Name:           "小耳朵346286",
					AvatarFrameURL: "https://static.maoercdn.com/test_346286.webp",
					IconURL:        "https://static.maoercdn.com/test_346286.webp",
				},
			}, nil
		})
	defer cancelMain()

	// 测试回复的评论不存在
	userID := int64(346286)
	subComment := map[string]interface{}{
		"comment_content": "测试子评论",
		"comment_id":      0,
		"user_id":         userID,
		"ip":              "127.0.0.1",
		"user_agent":      "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		"origin":          "aaa",
		"buvid":           "BUVID",
	}
	c := handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err := ActionAddSubComment(c)
	assert.Equal(actionerrors.ErrReplyCommentNotFound, err)

	// var comment soundcomment.Comment
	comment := soundcomment.Comment{
		ID:        1,
		UserID:    12,
		Username:  "零月",
		ElementID: 44809,
	}
	// 创建父评论用户和子评论用户的黑名单关系
	BigID := int64(3013097)
	blackUser := blackuser.Model{
		BigID:   BigID,
		SmallID: comment.UserID,
	}
	blackUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).
		Assign(blackUser).FirstOrCreate(&blackUser).Error
	require.NoError(err)

	// 测试父评论用户拉黑子评论用户
	subComment = map[string]interface{}{
		"comment_content": "测试子评论",
		"comment_id":      comment.ID,
		"user_id":         BigID,
		"ip":              "127.0.0.1",
		"user_agent":      "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		"origin":          "aaa",
		"buvid":           "BUVID",
	}
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "由于对方设置，你无法回复")

	// 测试子评论用户拉黑父评论用户
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).
		UpdateColumn("status", blackuser.StatusBigBanSmall)
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "评论失败，请将对方移除黑名单再试")
	// 删除黑名单关系
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).UpdateColumn("status", 0)

	// 创建音频 UP 主和子评论用户的黑名单关系
	UpUserID := int64(346286)
	blackUser = blackuser.Model{
		BigID:   BigID,
		SmallID: UpUserID,
	}
	blackUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).Assign(blackUser).FirstOrCreate(&blackUser).Error
	require.NoError(err)

	// 测试音频 UP 主拉黑子评论用户
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "由于 UP 主设置，你无法回复")

	// 子评论用户拉黑音频 UP 主
	service.MessageDB.Table(blackuser.Model{}.TableName()).Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).UpdateColumn("status", blackuser.StatusBigBanSmall)
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "评论失败，请将 UP 主移除黑名单再试")
	// 删除黑名单关系
	service.MessageDB.Table(blackuser.Model{}.TableName()).Where("small_id = ? AND big_id = ?", blackUser.SmallID, blackUser.BigID).UpdateColumn("status", 0)

	// 测试可以正常发送子评论
	userIP := "**************"
	ua := "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)"
	subComment = map[string]interface{}{
		"comment_content": "回复 @" + comment.Username + "：测试子评论，@保驾护航233 快来看看，@InVinCiblezz 我也来看看",
		"comment_id":      comment.ID,
		"user_id":         userID,
		"from":            user.FromApp,
		"ip":              userIP,
		"user_agent":      ua,
		"origin":          "aaa",
		"buvid":           "BUVID",
	}
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	// test json unmarshal
	var input addSubCommentParam
	err = c.Bind(&input)
	require.NoError(err)
	assert.Equal(userIP, input.UserEquipment.IP)
	assert.Equal(ua, input.UserEquipment.UserAgent)
	assert.Equal("aaa", input.UserEquipment.Origin)
	assert.Equal("BUVID", input.UserEquipment.BUVID)

	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	info, err := ActionAddSubComment(c)
	require.NoError(err)
	require.IsType(&soundcomment.SubComment{}, info)
	res := info.(*soundcomment.SubComment)
	assert.Equal(userIP, res.IP)
	assert.Equal(soundcomment.DefaultScoreRatio, res.ScoreRatio)
	assert.Equal(soundcomment.FindScoreDislikeProportionRatio(), res.ScoreDislikeProportionRatio)
	assert.EqualValues(100, res.ScoreBlacklistRatio)
	var ipDetail struct {
		CountryName string `json:"country_name"`
		RegionName  string `json:"region_name"`
		CityName    string `json:"city_name"`
	}
	err = json.Unmarshal(res.IPDetail, &ipDetail)
	require.NoError(err)
	assert.Equal("中国", ipDetail.CountryName)
	assert.Equal("山东", ipDetail.RegionName)
	assert.Equal("济南", ipDetail.CityName)
	// 断言被回复用户收到评论提醒
	var notice models.CommentNoticeFields
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, comment.UserID).Take(&notice).Error
	require.NoError(err)
	assert.Equal(notice.NoticeType, commentnotice.NoticeTypeComment)
	// 断言被 @ 用户收到 @ 提醒
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, 3013620).Take(&notice).Error
	require.NoError(err)
	assert.Equal(notice.NoticeType, commentnotice.NoticeTypeAt)
	// 断言用户自己被 @ 没有提醒消息
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, userID).Take(&notice).Error
	require.True(servicedb.IsErrNoRows(err))

	// 测试可以正常回复子评论
	err = service.DB.Where("c_user_id = ?", userID).Delete(models.CommentNotice{}).Error
	require.NoError(err)
	subComment["comment_content"] = "回复 @保驾护航233(3013620)： 测试子评论，@保驾护航233 快来看看，@InVinCiblezz 我也来看看"
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	require.NoError(err)
	// 断言被回复子评论的用户收到评论提醒
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, 3013620).Take(&notice).Error
	require.NoError(err)
	assert.Equal(notice.NoticeType, commentnotice.NoticeTypeComment)
	// 断言楼主收到评论提醒
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, comment.UserID).Take(&notice).Error
	require.NoError(err)
	assert.Equal(notice.NoticeType, commentnotice.NoticeTypeComment)
	// 断言用户自己被 @ 没有提醒消息
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Where("c_user_id = ? AND a_user_id = ?", userID, userID).Take(&notice).Error
	require.True(servicedb.IsErrNoRows(err))

	// 测试子评论中表情数量没有超过上限
	subComment["comment_content"] = strings.Repeat("[ ]", commentEmoteMaxNum+1)
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	require.NoError(err)

	subComment["comment_content"] = strings.Repeat("[收听]", commentEmoteMaxNum)
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	require.NoError(err)

	// 测试子评论中表情数量超过上限
	subComment["comment_content"] = strings.Repeat("[收听]", commentEmoteMaxNum+1)
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "表情数量超过上限")

	// 测试在评论打分黑名单的用户新增子评论的评论打分黑名单系数
	subComment["comment_content"] = "测试子评论"
	subComment["user_id"] = 12
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	info, err = ActionAddSubComment(c)
	require.NoError(err)
	require.IsType(&soundcomment.SubComment{}, info)
	assert.EqualValues(60, info.(*soundcomment.SubComment).ScoreBlacklistRatio)

	// 测试截取回复子评论前缀
	subComment["comment_content"] = "回复 @操你妈：你好啊 @零月：正常"
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.NoError(err)

	// 会员评论会员音
	subComment["user_id"] = 11
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.NoError(err)

	// 测试会员音需要会员才能评论
	subComment["user_id"] = 10
	subComment["comment_id"] = 2
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "开通会员即可评论哦~")
	subComment["comment_id"] = 1

	// 测试含多个 @ 信息也能正常拦截
	cancel = mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []handler.M{{"score": 0, "pass": false, "labels": []string{"test"}}}, nil
	})
	defer cancel()
	subComment["comment_content"] = "回复 零月@(12)：违规操你妈 @操你妈(124)：正常"
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.EqualError(err, "评论内容含有违规信息")

	// 测试发送评论被评论的用户收到消息提醒
	var id int64
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Select("id").Where("c_user_id = ? AND a_user_id = ?", userID, comment.UserID).
		Order("id ASC").Limit(1).Row().Scan(&id)
	if !servicedb.IsErrNoRows(err) {
		assert.NoError(err)
		// 删除消息提醒防止后续测试异常
		err = service.DB.Delete(models.CommentNotice{}).Error
		require.NoError(err)
	}

	// 违规评论
	cancel = mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []handler.M{{"score": 0, "pass": false, "labels": []string{"test"}}}, nil
	})
	defer cancel()
	subComment = map[string]interface{}{
		"comment_content": "回复 @" + comment.Username + "：违规",
		"comment_id":      comment.ID,
		"user_id":         userID,
		"ip":              "127.0.0.1",
		"user_agent":      "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		"origin":          "aaa",
		"buvid":           "BUVID",
	}
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.Equal(actionerrors.ErrCommentViolation, err)
	delSoundSubComment := soundcomment.DeleteSoundSubComment{}
	require.NoError(service.MessageDB.Where("comment_id = ?", comment.ID).First(&delSoundSubComment).Error)
	assert.Equal(soundcomment.DeleteTypeByViolation, delSoundSubComment.DeleteType)

	// 测试假发送用户自见，被 @ 的人没有提醒
	mrpc.SetMock("go://scan/text", func(input interface{}) (output interface{}, err error) {
		return []handler.M{{"score": 0, "pass": true, "labels": []string{"evil"}}}, nil
	})
	subComment = map[string]interface{}{
		"comment_content": "回复 @" + comment.Username + "：假发送",
		"comment_id":      comment.ID,
		"user_id":         userID,
		"from":            user.FromApp,
		"ip":              userIP,
		"user_agent":      "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		"origin":          "aaa",
		"buvid":           "BUVID",
	}
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.NoError(err)
	// 检查 @ 消息
	err = service.DB.Table(models.CommentNotice{}.TableName()).
		Select("id").Where("c_user_id = ? AND a_user_id = ?", userID, comment.UserID).
		Order("id ASC").Limit(1).Row().Scan(&id)
	require.True(servicedb.IsErrNoRows(err))

	// 测试敏感剧集不可发送评论
	cancel = mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(i interface{}) (interface{}, error) {
		return []map[string]int64{{
			"drama_id": 8,
			"sound_id": comment.ElementID,
		}}, nil
	})
	defer cancel()
	c = handler.NewRPCTestContext("/rpc/message/add-subcomment", subComment)
	_, err = ActionAddSubComment(c)
	assert.Errorf(err, "评论区已关闭")
}

func TestFilterCommentNoticeUsers(t *testing.T) {
	assert := assert.New(t)

	testUsers := []noticeUser{
		{
			userID:   1,
			username: "当前用户",
		},
		{
			userID:   2,
			username: "UP 主",
		},
		{
			userID:   3,
			username: "楼主",
		},
		{
			userID:   4,
			username: "回复子评论的用户",
		},
		{
			userID:   5,
			username: "当前用户主动 @ 的用户",
		},
	}
	testComment := soundcomment.Comment{
		UserID:   testUsers[2].userID,
		Username: testUsers[2].username,
	}
	testBeRemindedUsers := make(map[int64]string, 5)
	for _, testUser := range testUsers {
		testBeRemindedUsers[testUser.userID] = testUser.username
	}
	// 当前用户和 UP 主、楼主、回复子评论的用户均不是同一个用户
	testNoticeUsers, atUsers := filterCommentNoticeUsers(testUsers[0].userID, testComment, &testUsers[1], &testUsers[3], testBeRemindedUsers)
	exceptAtUsers := make(map[int64]string)
	exceptAtUsers[testUsers[4].userID] = testUsers[4].username
	assert.Equal(exceptAtUsers, atUsers)
	exceptRemindedUsers := make(map[int64]string)
	exceptRemindedUsers[testUsers[1].userID] = testUsers[1].username
	exceptRemindedUsers[testUsers[2].userID] = testUsers[2].username
	exceptRemindedUsers[testUsers[3].userID] = testUsers[3].username
	exceptRemindedUsers[testUsers[4].userID] = testUsers[4].username
	assert.Equal(exceptRemindedUsers, testNoticeUsers)

	// 当前用户和 UP 主是同一个用户
	delete(testBeRemindedUsers, testUsers[0].userID)
	testNoticeUsers, atUsers = filterCommentNoticeUsers(testUsers[1].userID, testComment, &testUsers[1], &testUsers[3], testBeRemindedUsers)
	assert.Equal(exceptAtUsers, atUsers)
	delete(exceptRemindedUsers, testUsers[1].userID)
	assert.Equal(exceptRemindedUsers, testNoticeUsers)

	// 当前用户和楼主是同一个用户
	testNoticeUsers, atUsers = filterCommentNoticeUsers(testUsers[2].userID, testComment, &testUsers[1], &testUsers[3], testBeRemindedUsers)
	assert.Equal(exceptAtUsers, atUsers)
	exceptRemindedUsers[testUsers[1].userID] = testUsers[1].username
	delete(exceptRemindedUsers, testUsers[2].userID)
	assert.Equal(exceptRemindedUsers, testNoticeUsers)

	// 当前用户和回复子评论的用户是同一个用户
	testNoticeUsers, atUsers = filterCommentNoticeUsers(testUsers[3].userID, testComment, &testUsers[1], &testUsers[3], testBeRemindedUsers)
	assert.Equal(exceptAtUsers, atUsers)
	exceptRemindedUsers[testUsers[2].userID] = testUsers[2].username
	delete(exceptRemindedUsers, testUsers[3].userID)
	assert.Equal(exceptRemindedUsers, testNoticeUsers)

	// 当前用户在自己发布的作品下回复自己的一级评论
	testComment.UserID = testUsers[0].userID
	testComment.Username = testUsers[0].username
	testBeRemindedUsers2 := make(map[int64]string, 1)
	testBeRemindedUsers2[testUsers[0].userID] = testUsers[0].username
	testNoticeUsers2, atUsers := filterCommentNoticeUsers(testUsers[0].userID, testComment, &testUsers[0], nil, testBeRemindedUsers2)
	assert.Zero(len(atUsers))
	assert.Zero(len(testNoticeUsers2))

	// 当前用户在活动下回复其他用户的子评论
	testBeRemindedUsers3 := make(map[int64]string, 1)
	testBeRemindedUsers3[testUsers[2].userID] = testUsers[2].username
	testBeRemindedUsers3[testUsers[3].userID] = testUsers[3].username
	testNoticeUsers3, atUsers := filterCommentNoticeUsers(testUsers[0].userID, testComment, nil, &testUsers[2], testBeRemindedUsers3)
	exceptAtUsers3 := make(map[int64]string)
	exceptAtUsers3[testUsers[3].userID] = testUsers[3].username
	assert.Equal(exceptAtUsers3, atUsers)
	exceptRemindedUsers3 := make(map[int64]string)
	exceptRemindedUsers3[testUsers[2].userID] = testUsers[2].username
	exceptRemindedUsers3[testUsers[3].userID] = testUsers[3].username
	assert.Equal(exceptRemindedUsers3, testNoticeUsers3)
}

func TestGetSubCommentCheckContentWithReplyUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userMap := map[int64]string{12: "零月"}
	comments, replyUserInfo, err := getSubCommentCheckContentWithReplyUser("回复 @零月(12)：测试子评论", userMap)
	require.NoError(err)
	assert.Equal("测试子评论", comments)
	exceptReplyUser := &noticeUser{
		userID:   12,
		username: "零月",
	}
	assert.Equal(exceptReplyUser, replyUserInfo)
	comments, replyUserInfo, err = getSubCommentCheckContentWithReplyUser("回复 @零月(15)：测试子评论", userMap)
	require.NoError(err)
	assert.Equal("回复 @零月(15)：测试子评论", comments)
	assert.Nil(replyUserInfo)

	comments, replyUserInfo, err = getSubCommentCheckContentWithReplyUser("回复 @零月(12)： 测试子评论", userMap)
	require.NoError(err)
	assert.Equal("测试子评论", comments)
	assert.Equal(exceptReplyUser, replyUserInfo)

	comments, replyUserInfo, err = getSubCommentCheckContentWithReplyUser("啊 回复 @零月(12)：测试子评论，回复 @零月(12)：测，回复 @test123s(3013097)：", userMap)
	require.NoError(err)
	assert.Equal("啊 回复 @零月(12)：测试子评论，回复 @零月(12)：测，回复 @test123s(3013097)：", comments)
	assert.Nil(replyUserInfo)

	comments, replyUserInfo, err = getSubCommentCheckContentWithReplyUser("回复 @零月(12)：测试子评论，回复 @零月(12)：测，回复 @test123s(3013097)：", userMap)
	require.NoError(err)
	assert.Equal("测试子评论，回复 @零月(12)：测，回复 @test123s(3013097)：", comments)
	assert.Equal(exceptReplyUser, replyUserInfo)
}

func TestCheckCommentElement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	notExistID := int64(-999)

	c := handler.CreateTestContext(false)
	var comment soundcomment.Comment
	comment.Type = soundcomment.TypeAlbum
	comment.ElementID = notExistID
	_, _, _, err := checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "该音单不存在")

	// 测试 UP 主评论私有音单
	m := models.MAlbum{
		ID:      2333,
		UserID:  testUserID,
		Refined: 16,
	}
	require.NoError(service.DB.Table(m.TableName()).
		Where("user_id = ?", testUserID).Delete("").Error)
	require.NoError(service.DB.Create(m).Error)
	comment.ElementID = 2333
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Nil(err)

	// 测试除 UP 外用户评论私有音单
	_, _, _, err = checkCommentElement(c, comment, testUser1ID, 0, false)
	assert.Contains(err.Error(), "音单不存在")

	comment.Type = soundcomment.TypeTag
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "频道不存在")

	comment.Type = soundcomment.TypeTopic
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "专题不存在")

	comment.Type = soundcomment.TypeEvent
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "活动不存在")

	comment.Type = soundcomment.TypeSound
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "音频不存在")

	comment.Type = soundcomment.TypeVoiceCard
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "语音不存在")

	comment.Type = soundcomment.TypeVoiceCard
	comment.ElementID = 57
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "未获得的语音不能评论哦")

	comment.Type = soundcomment.TypeOmikujiCard
	comment.ElementID = notExistID
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "语音不存在")

	comment.Type = soundcomment.TypeOmikujiCard
	comment.ElementID = 58423
	_, _, _, err = checkCommentElement(c, comment, testUserID, 0, false)
	assert.Contains(err.Error(), "未获得的语音不能评论哦")
}

func TestParseComment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var commentUserID int64 = 1
	var user user.MowangskUser
	user.ID = testUserID
	user.UserName = "GBi9d9KBKF5jlh1d5qrD"
	err := service.DB.Where("id = ?", testUserID).Assign(user).FirstOrCreate(&user).Error
	require.NoError(err)

	var comment soundcomment.Comment
	_, err = parseComment(&comment.Content, commentUserID)
	assert.Contains(err.Error(), "不能为空~")

	// 测试评论发表用户与被 @ 用户为非黑名单关系
	m := blackuser.Model{
		SmallID: commentUserID,
		BigID:   testUserID,
	}
	service.MessageDB.Table(blackuser.Model{}.TableName()).Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).UpdateColumn("status", 0)

	comment.Content = "@GBi9d9KBKF5jlh1d5qrD  "
	users, err := parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD(765325)", comment.Content)
	assert.Equal("GBi9d9KBKF5jlh1d5qrD", users[765325])

	comment.Content = "@GBi9d9KBKF5jlh1d5qrD 123"
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD(765325) 123", comment.Content)
	assert.Equal("GBi9d9KBKF5jlh1d5qrD", users[765325])

	comment.Content = "@GBi9d9KBKF5jlh1d5qrD：123"
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD(765325)：123", comment.Content)
	assert.Equal("GBi9d9KBKF5jlh1d5qrD", users[765325])

	comment.Content = "123"
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("123", comment.Content)
	assert.Empty(users)

	// 测试评论发表用户与被 @ 用户为拉黑关系
	m.Status.Set(blackuser.StatusBigBanSmall)
	err = service.MessageDB.Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).Assign(m).FirstOrCreate(&m).Error
	require.NoError(err)
	comment.Content = "@GBi9d9KBKF5jlh1d5qrD  "
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD", comment.Content)
	assert.Empty(users)

	// 测试评论发表用户与被 @ 用户为被拉黑关系
	m.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).Assign(m).FirstOrCreate(&m).Error
	require.NoError(err)
	comment.Content = "@GBi9d9KBKF5jlh1d5qrD  "
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD", comment.Content)
	assert.Empty(users)

	// 测试评论发表用户与被 @ 用户为互相拉黑关系
	comment.Content = "@GBi9d9KBKF5jlh1d5qrD  "
	users, err = parseComment(&comment.Content, commentUserID)
	require.NoError(err)
	assert.Equal("@GBi9d9KBKF5jlh1d5qrD", comment.Content)
	assert.Empty(users)
}

func TestGetIPDetailAndLocation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID1 int64 = 12
	ipDetail, ipLocation := getIPDetailAndLocation("*************", testUserID1)
	assert.NotNil(ipDetail)
	var ipDetailParams user.IPDetailParams
	err := json.Unmarshal(ipDetail, &ipDetailParams)
	require.NoError(err)
	assert.Equal("中国", ipDetailParams.CountryName)
	assert.Equal("上海", ipDetailParams.RegionName)
	assert.Equal("上海", ipDetailParams.CityName)
	assert.Equal("山东", ipDetailParams.ShowLocation)
	assert.Equal("山东", ipLocation)
}

func TestGetCommentUserInfo(t *testing.T) {
	assert := assert.New(t)

	// 测试用户没有头像框信息
	commentUser := &user.MowangskUser{
		Simple: user.Simple{
			ID: 9999,
		},
	}
	cancel := mrpc.SetMock("mrpc://missevan-main/person/list-avatar-frame",
		func(param interface{}) (interface{}, error) {
			data := param.(*userapi.ListAvatarFrameParams)
			if data.UserIDs[0] != 1 {
				return map[int64]userapi.AvatarFrameInfo{}, nil
			}
			return map[int64]userapi.AvatarFrameInfo{
				1: {
					ID:             10,
					Name:           "小耳朵",
					AvatarFrameURL: "https://static.maoercdn.com/test_4.webp",
					IconURL:        "https://static.maoercdn.com/test_5.webp",
				},
			}, nil
		})
	defer cancel()

	userInfo := getCommentUserInfo(mrpc.UserContext{}, commentUser, true, 1, 223)
	assert.Empty(userInfo.AvatarFrameURL)

	// 测试获取用户头像框信息
	commentUser.ID = 1
	userInfo = getCommentUserInfo(mrpc.UserContext{}, commentUser, true, 1, 223)
	assert.Equal(commentUser.ID, userInfo.UserID)
	assert.Equal("https://static.maoercdn.com/test_4.webp", userInfo.AvatarFrameURL)

	// 测试获取用户 vip 信息
	commentUser.ID = 10
	userInfo = getCommentUserInfo(mrpc.UserContext{}, commentUser, false, 1, 223)
	assert.Equal(0, userInfo.IsVip)

	commentUser.ID = 11
	userInfo = getCommentUserInfo(mrpc.UserContext{}, commentUser, true, 1, 223)
	assert.Equal(1, userInfo.IsVip)
}

func TestNewSmartsheetParamComment(t *testing.T) {
	assert := assert.New(t)

	// 测试正常推送评论
	label := soundcomment.LabelOfType[soundcomment.TypeSound]
	comment := &soundcomment.Comment{
		ID:        123,
		ElementID: 456,
		UserID:    789,
		Content:   "测试评论内容",
	}
	matchedWord := "测试"
	result := newSmartsheetParamComment(label, comment.ElementID, false, comment.ID, comment.UserID, comment.Content, matchedWord)

	assert.NotNil(result)
	assert.Equal("main_review_comment", result.Channel)
	assert.Equal([]string{"评论资源类型", "资源 ID", "是否子评论", "评论 ID", "用户 ID", "评论内容", "命中词", "创建时间"}, result.Fields)
	assert.Len(result.Values, 8)
	assert.Equal(label, result.Values[0])
	assert.Equal(strconv.FormatInt(comment.ElementID, 10), result.Values[1])
	assert.Equal("否", result.Values[2])
	assert.Equal(strconv.FormatInt(comment.ID, 10), result.Values[3])
	assert.Equal(strconv.FormatInt(comment.UserID, 10), result.Values[4])
	assert.Equal(comment.Content, result.Values[5])
	assert.Equal("测试", result.Values[6])
	assert.Equal(util.TimeNow().Format(util.TimeFormatHMS), result.Values[7])

	// 测试正常推送子评论
	subComment := &soundcomment.Comment{
		ID:        124,
		ElementID: 457,
		UserID:    790,
		Content:   "测试子评论内容",
	}
	matchedWord = "子评论"
	result = newSmartsheetParamComment(label, subComment.ElementID, true, subComment.ID, subComment.UserID, subComment.Content, matchedWord)

	assert.NotNil(result)
	assert.Equal("main_review_comment", result.Channel)
	assert.Equal([]string{"评论资源类型", "资源 ID", "是否子评论", "评论 ID", "用户 ID", "评论内容", "命中词", "创建时间"}, result.Fields)
	assert.Len(result.Values, 8)
	assert.Equal(label, result.Values[0])
	assert.Equal(strconv.FormatInt(subComment.ElementID, 10), result.Values[1])
	assert.Equal("是", result.Values[2])
	assert.Equal(strconv.FormatInt(subComment.ID, 10), result.Values[3])
	assert.Equal(strconv.FormatInt(subComment.UserID, 10), result.Values[4])
	assert.Equal(subComment.Content, result.Values[5])
	assert.Equal("子评论", result.Values[6])
	assert.Equal(util.TimeNow().Format(util.TimeFormatHMS), result.Values[7])
}

func TestPushToSmartsheetComment(t *testing.T) {
	assert := assert.New(t)

	smartsheet.SetMockResult(smartsheet.APISheetAddRecord, smartsheet.SuccessCode, nil)
	service.Smartsheet = smartsheet.NewClient(smartsheet.TestConfig())

	// 测试正常推送评论
	label := soundcomment.LabelOfType[soundcomment.TypeSound]
	comment := &soundcomment.Comment{
		ID:        123,
		ElementID: 456,
		UserID:    789,
		Content:   "测试评论内容",
	}
	matchedWord := "测试"
	assert.True(pushToSmartsheetComment(label, comment.ElementID, false, comment.ID, comment.UserID, comment.Content, matchedWord))

	// 测试正常推送子评论
	subComment := &soundcomment.Comment{
		ID:        124,
		ElementID: 457,
		UserID:    790,
		Content:   "测试子评论内容",
	}
	matchedWord = "子评论"
	assert.True(pushToSmartsheetComment(label, subComment.ElementID, true, subComment.ID, subComment.UserID, subComment.Content, matchedWord))
}
