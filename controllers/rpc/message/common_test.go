package message

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestDramaIDBySoundID(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock("drama://api/get-dramaid-by-soundid",
		func(interface{}) (interface{}, error) {
			type res struct {
				DramaID int64 `json:"drama_id"`
			}
			return []res{{12345}}, nil
		})
	defer cancel()
	id, err := DramaIDBySoundID(88771, "127.0.0.1")
	assert.NoError(err)
	assert.Equal(int64(12345), id)
}
