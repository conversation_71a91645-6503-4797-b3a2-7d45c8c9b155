package message

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

const TestUser1ID = 1
const TestUser2ID = 2

func TestActionLike(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误的情况
	params := LikeParams{
		CommentID: -1,
	}
	c := handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err := ActionLike(c)
	assert.EqualError(err, "参数错误")

	assert.Nil(result)

	// 测试评论不存在的情况
	params = LikeParams{
		CommentID: 9999999999,
		UserID:    TestUser1ID,
	}
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "该评论不存在")
	assert.Nil(result)

	// 测试对评论进行点赞的情况
	// 创建测试用评论
	comment := soundcomment.Comment{
		Content:   "测试评论内容 - go",
		Type:      soundcomment.TypeSound,
		ElementID: 1, // 该值不影响测试结果
		UserID:    TestUser2ID,
		Username:  "测试用户",
		Checked:   soundcomment.CheckedNormal,
	}
	err = comment.Save(service.DB)
	require.NoError(err)
	params = LikeParams{
		CommentID: comment.ID,
		Sub:       paramCommentType,
		UserID:    TestUser1ID,
		Action:    ActionTypeAdd,
	}
	// 删除相关测试缓存
	lock := serviceredis.LockUserComment3.Format(TestUser1ID, params.CommentID, params.Sub)
	require.NoError(service.Redis.Del(lock).Err())

	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证评论点赞数量冗余值正确
	var dislikeNum int64
	err = comment.DB().Select("like_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(1), dislikeNum)
	// 验证有消息提醒
	exists, err := soundcomment.LikeNotice{}.Exists(service.DB, comment.ID, TestUser1ID, paramCommentType)
	require.NoError(err)
	assert.True(exists)

	// 测试操作频繁情况下进行点赞
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	require.NoError(err)
	require.True(ok)
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "操作频繁，请稍后再试")
	assert.Nil(result)
	require.NoError(service.Redis.Del(lock).Err())

	// 测试评论重复点赞情况
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证评论点赞数量冗余值正确
	err = comment.DB().Select("like_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(1), dislikeNum)
	// 验证消息提醒不增加
	var noticeCount int
	err = service.DB.Table(soundcomment.LikeNotice{}.TableName()).
		Where("comment_id = ? AND c_user_id = ? AND sub = ?", comment.ID, TestUser1ID, paramCommentType).
		Count(&noticeCount).Error
	require.NoError(err)
	assert.Equal(1, noticeCount)

	// 创建黑名单关系
	blockUser := blackuser.Model{
		BigID:   TestUser2ID,
		SmallID: TestUser1ID,
	}
	blockUser.Status.Set(blackuser.StatusSmallBanBig)
	err = service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		Assign(blockUser).FirstOrCreate(&blockUser).Error
	require.NoError(err)

	// 测试点赞黑名单用户
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "操作失败，请将对方移除黑名单再试")
	assert.Nil(result)

	// 测试点踩黑名单用户
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "操作失败，请将对方移除黑名单再试")
	assert.Nil(result)

	// 测试黑名单用户给你的评论点赞
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		UpdateColumn("status", blackuser.StatusBigBanSmall)
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "由于对方设置，你无法操作")
	assert.Nil(result)

	// 测试黑名单用户给你的评论点踩
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionLike(c)
	assert.EqualError(err, "由于对方设置，你无法操作")
	assert.Nil(result)
	// 删除黑名单关系数据
	service.MessageDB.Table(blackuser.Model{}.TableName()).
		Where("small_id = ? AND big_id = ?", blockUser.SmallID, blockUser.BigID).
		UpdateColumn("status", 0)

	// 测试取消点赞的情况
	params.Action = 0
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.NoError(err)
	assert.False(result.(LikeResp).Status)
	// 验证评论点赞数量冗余值正确
	err = comment.DB().Select("like_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(0), dislikeNum)

	// 测试点踩后进行点赞，点踩自动取消
	// 进行点踩
	params.Action = ActionTypeAdd
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 进行点赞
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证点踩数据已消失
	exists, err = soundcomment.CommentLike{}.Exists(service.DB, comment.ID, comment.UserID, paramCommentType,
		soundcomment.TypeCommentDislike)
	require.NoError(err)
	assert.False(exists)
	var data soundcomment.Comment
	err = comment.DB().Select("like_num, dislike_num").
		Where("id = ?", comment.ID).First(&data).Error
	require.NoError(err)
	assert.Equal(int64(0), data.DislikeNum)
	assert.Equal(int64(1), data.LikeNum)

	// 删除测试数据
	require.NoError(comment.DB().Where("id = ?", comment.ID).Delete("").Error)
	require.NoError(service.DB.Table(soundcomment.CommentLike{}.TableName()).Where("userid = ?", TestUser1ID).
		Delete("").Error)
	require.NoError(service.DB.Table(soundcomment.LikeNotice{}.TableName()).Where("c_user_id = ?", TestUser1ID).
		Delete("").Error)
}
