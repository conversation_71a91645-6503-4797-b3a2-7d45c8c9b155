package message

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// deleteDanmakuParams struct for delete-dm api
type deleteDanmakuParams struct {
	DanmakuID int64 `json:"danmaku_id"`
	UserID    int64 `json:"user_id"`
}

// deleteDanmakuResp 接口响应结构
type deleteDanmakuResp struct {
	Status bool `json:"status"`
}

// TODO: 接口后续支持批量删除

// ActionDeleteDm 删除弹幕
/**
 * @api {post} /rpc/message/del-dm 删除弹幕
 * @apiDescription 该接口不拥有幂等性
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} danmaku_id 弹幕 ID
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "danmaku_id": 1,
 *       "user_id": 1
 *     }
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "status": true // 成功删除弹幕
 *       }
 *     }
 */
func ActionDeleteDm(c *handler.Context) (r handler.ActionResponse, err error) {
	var params deleteDanmakuParams
	err = c.BindJSON(&params)
	if err != nil || params.DanmakuID <= 0 || params.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	key := serviceredis.LockUserDeleteDanmaku2.Format(params.UserID, params.DanmakuID)
	lock, err := service.Redis.SetNX(key, 1, 10*time.Second).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !lock {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(key)

	danmaku := new(message.MSoundComment)
	err = message.MSoundComment{}.DB().Where("id = ?", params.DanmakuID).Find(danmaku).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrDanmakuNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	deleteType := soundcomment.DeleteTypeBySelf
	if danmaku.UserID != params.UserID {
		sound := sound.MSound{}
		// 若弹幕发送者不为删除者，判断是否为 UP 主进行删除操作
		err = service.DB.Table(sound.TableName()).Select("user_id").
			Where("id = ?", danmaku.SoundID).Find(&sound).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				return nil, actionerrors.ErrSoundNotFound
			}
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
		if sound.UserID != params.UserID {
			return nil, actionerrors.ErrNoPermissionDelDanmaku
		}
		deleteType = soundcomment.DeleteTypeByUP
	}

	err = message.DeleteDanmaku(params.DanmakuID, danmaku, deleteType)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	return deleteDanmakuResp{Status: true}, nil
}
