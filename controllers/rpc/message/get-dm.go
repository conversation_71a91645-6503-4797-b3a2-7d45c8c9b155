package message

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type getDanmakuParams struct {
	SoundID int64 `json:"sound_id"`
}

type getDanmakuResp struct {
	Danmakus []message.MSoundComment `json:"danmakus"`
}

// ActionGetDm 获取音频弹幕
/**
 * @api {post} /rpc/message/get-dm 获取音频弹幕
 * @apiDescription 调用该接口业务方需要实现弹幕缓存
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} sound_id 音频 ID
 * @apiParamExample {json} Request-Example:
 *     {
 *       "sound_id": 4673
 *     }
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "danmakus": [{"id":1,"sound_id":233,"user_id":0,"text":"","stime":"","size":0,"color":0,"mode":0,"date":0,"pool":44,"like_num":0,"checked":1}]
 *       }
 *     }
 */
func ActionGetDm(c *handler.Context) (handler.ActionResponse, error) {
	params := new(getDanmakuParams)
	err := c.BindJSON(params)
	if err != nil || params.SoundID <= 0 {
		return nil, actionerrors.ErrParams
	}
	resp := getDanmakuResp{
		Danmakus: make([]message.MSoundComment, 0),
	}
	if !sound.CheckSoundID(params.SoundID) {
		return resp, nil
	}
	mSound := new(sound.MSound)
	err = mSound.DB().Select("comment_count, duration").Where("id = ?", params.SoundID).Take(mSound).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrSoundNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if mSound.CommentCount == 0 {
		return resp, nil
	}

	needCount := message.GetNumberDanmakuBySoundDuration(int64(mSound.Duration))
	resp.Danmakus, err = message.ListDanmaku(params.SoundID, needCount)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return resp, nil
}
