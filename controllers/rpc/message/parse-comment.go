package message

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionParseComment 解析评论和子评论内容
/**
 * @api {post} /rpc/message/parse-comment 评论和子评论内容
 * @apiDescription 解析评论和子评论中的站内跳转链接，并获取跳转链接对应的文案
 *
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Object[]} comments 评论列表
 * @apiParam {Number} comments.id 评论 ID
 * @apiParam {String} comments.comment_content 评论内容
 * @apiParam {Object[]} sub_comments 子评论列表
 * @apiParam {Number} sub_comments.id 子评论 ID
 * @apiParam {String} sub_comments.comment_content 子评论内容
 *
 * @apiParamExample {json} 参数示例:
 * {
 *   "comments": [
 *     {
 *       "id": 1000,
 *       "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365"
 *     },
 *     {
 *       "id": 1001,
 *       "comment_content": "评论内容 2"
 *     }
 *   ],
 *   "sub_comments": [
 *     {
 *       "id": 100,
 *       "comment_content": "@23336666(346287) 剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365"
 *     },
 *     {
 *       "id": 1001,
 *       "comment_content": "子评论内容 2"
 *     }
 *   ]
 * }
 *
 * @apiSuccess (200) {Number} code CodeSuccess = 0.
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "comments": {
 *           "1000": { // 评论 ID
 *             "id": 1000, // 评论 ID
 *             "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365", // 评论内容
 *             "jump_url": { // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
 *               "https://www.missevan.com/mdrama/77631": { // 跳转链接地址
 *                 "title": "十年对手，一朝占有", // 跳转链接文案
 *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg" // 跳转链接前缀图标
 *               },
 *               "https://www.missevan.com/sound/player?id=9811365": { // 跳转链接地址
 *                 "title": "十年对手，一朝占有 . 第一集", // 跳转链接文案
 *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg" // 跳转链接前缀图标
 *               }
 *             }
 *           },
 *           "1001": {
 *             "id": 1001,
 *             "comment_content": "评论内容 2"
 *           }
 *         },
 *         "sub_comments": {
 *           "100": { // 子评论 ID
 *             "id": 100, // 子评论 ID
 *             "comment_content": "@23336666(346287) 剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365", // 子评论内容
 *             "jump_url": { // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
 *               "https://www.missevan.com/mdrama/77631": { // 跳转链接地址
 *                 "title": "十年对手，一朝占有", // 跳转链接文案
 *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg" // 跳转链接前缀图标
 *               },
 *               "https://www.missevan.com/sound/player?id=9811365": { // 跳转链接地址
 *                 "title": "十年对手，一朝占有 . 第一集", // 跳转链接文案
 *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg" // 跳转链接前缀图标
 *               }
 *             }
 *           },
 *           "101": {
 *             "id": 101,
 *             "comment_content": "子评论内容 2"
 *           }
 *         }
 *       }
 *     }
 */
func ActionParseComment(c *handler.Context) (handler.ActionResponse, error) {
	return nil, nil
}
