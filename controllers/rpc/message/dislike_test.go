package message

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestActionDislike(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误的情况
	params := LikeParams{
		CommentID: -1,
	}
	c := handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err := ActionDislike(c)
	assert.EqualError(err, "参数错误")

	assert.Nil(result)

	// 测试评论不存在的情况
	params = LikeParams{
		CommentID: 9999999999,
		UserID:    TestUser1ID,
	}
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.EqualError(err, "该评论不存在")
	assert.Nil(result)

	// 测试对评论进行点踩的情况
	// 创建测试用评论
	comment := soundcomment.Comment{
		Content:   "测试评论内容 - go",
		Type:      soundcomment.TypeSound,
		ElementID: 1, // 该值不影响测试结果
		UserID:    TestUser1ID,
		Username:  "测试用户",
		Checked:   soundcomment.CheckedNormal,
	}
	err = comment.Save(service.DB)
	require.NoError(err)
	params = LikeParams{
		CommentID: comment.ID,
		Sub:       paramCommentType,
		UserID:    TestUser1ID,
		Action:    ActionTypeAdd,
	}
	// 删除相关测试缓存
	lock := serviceredis.LockUserComment3.Format(TestUser1ID, params.CommentID, params.Sub)
	require.NoError(service.Redis.Del(lock).Err())

	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证评论点踩数量冗余值正确
	var dislikeNum int64
	err = soundcomment.Comment{}.DB().Select("dislike_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(1), dislikeNum)
	// 验证点踩无消息提醒
	exists, err := soundcomment.LikeNotice{}.Exists(service.DB, comment.ID, TestUser1ID, paramCommentType)
	require.NoError(err)
	assert.False(exists)

	// 测试操作频繁情况下进行点踩
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	require.NoError(err)
	require.True(ok)
	result, err = ActionDislike(c)
	assert.EqualError(err, "操作频繁，请稍后再试")
	assert.Nil(result)
	require.NoError(service.Redis.Del(lock).Err())

	// 测试接口幂等性（已点踩情况下继续点踩）
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证评论点踩数量冗余值正确
	err = soundcomment.Comment{}.DB().Select("dislike_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(1), dislikeNum)

	// 测试取消点踩的情况
	params.Action = 0
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.NoError(err)
	assert.False(result.(LikeResp).Status)
	// 验证评论点踩数量冗余值正确
	err = soundcomment.Comment{}.DB().Select("dislike_num").
		Where("id = ?", comment.ID).Row().Scan(&dislikeNum)
	require.NoError(err)
	assert.Equal(int64(0), dislikeNum)

	// 测试点赞后进行点踩，点赞自动取消
	// 进行点赞
	params.Action = ActionTypeAdd
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/like", false, params)
	result, err = ActionLike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 进行点踩
	c = handler.NewTestContext(http.MethodPost, "/rpc/message/dislike", false, params)
	result, err = ActionDislike(c)
	assert.NoError(err)
	assert.True(result.(LikeResp).Status)
	// 验证点赞数据已消失
	exists, err = soundcomment.CommentLike{}.Exists(service.DB, comment.ID, comment.UserID, paramCommentType,
		soundcomment.TypeCommentLike)
	require.NoError(err)
	assert.False(exists)
	var data soundcomment.Comment
	err = soundcomment.Comment{}.DB().Select("like_num, dislike_num").
		Where("id = ?", comment.ID).First(&data).Error
	require.NoError(err)
	assert.Equal(int64(0), data.LikeNum)
	assert.Equal(int64(1), data.DislikeNum)

	// 删除测试数据
	require.NoError(soundcomment.Comment{}.DB().Where("id = ?", comment.ID).Delete("").Error)
	require.NoError(service.DB.Table(soundcomment.CommentLike{}.TableName()).Where("userid = ?", TestUser1ID).
		Delete("").Error)
}
