package message

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestAddDm(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	cancel := mrpc.SetMock("drama://api/get-dramaid-by-soundid",
		func(interface{}) (interface{}, error) {
			type res struct {
				DramaID int64 `json:"drama_id"`
			}
			return []res{{12345}}, nil
		})
	defer cancel()

	userID := int64(346286)
	userID2 := int64(3013097)
	userIP := "**************"

	// 测试参数错误
	dm := handler.M{
		"sound_id": testSoundID,
		"user_id":  userID,
		"text":     "text",
		"stime":    "0.5",
		"color":    "#ffffff",
	}
	c := handler.NewRPCTestContext("rpc/message/add-dm", dm)
	_, err := ActionAddDm(c)
	assert.EqualError(err, "参数错误")

	// 测试您的账号有可疑记录，暂时被系统封停
	err = service.Redis.ZAdd(serviceredis.KeyBlackList0.Format(),
		&redis.Z{Score: float64(util.TimeNow().Add(10 * time.Second).Unix()), Member: userID}).Err()
	require.NoError(err)
	body := handler.M{
		"sound_id": testSoundID,
		"user_id":  userID,
		"text":     "text",
		"stime":    "0.5",
		"from":     user.FromApp,
		"ip":       userIP,
	}
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "您的账号有可疑记录，暂时被系统封停")
	require.NoError(service.Redis.ZRem(serviceredis.KeyBlackList0.Format(), userID).Err())

	// 测试绑定手机号可以发送弹幕
	var id int64
	err = service.DB.Table(user.MowangskUser{}.TableName()).Where("mobile = ''").Limit(1).Select("id").Row().Scan(&id)
	require.NoError(err)
	body["user_id"] = id
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "绑定手机就可以发送弹幕了哦")

	cancel = mrpc.SetMock("sso://get", func(i interface{}) (interface{}, error) {
		return &sso.Account{
			Mobile: "***********",
		}, nil
	})
	defer cancel()

	// 测试音频不存在
	body["sound_id"] = testNotSoundID
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "音频不存在")

	// 测试本音频禁止发弹幕
	key := serviceredis.KeyForbiddenCommentElement1.Format(soundcomment.TypeSound)
	require.NoError(service.Redis.SAdd(key, testSoundID).Err())
	body["sound_id"] = testSoundID
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "本音频禁止发弹幕")
	require.NoError(service.Redis.SRem(key, testSoundID).Err())

	// 测试下架音频不可发送弹幕
	body["sound_id"] = testDiscontinuedSoundID
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "暂不可发送弹幕")

	// 测试普通用户弹幕数超过当日发送数量限制
	key = serviceredis.LockUserDM1.Format(userID2)
	require.NoError(service.Redis.Set(key, sound.UserDMMaxNum, time.Minute).Err())
	body["sound_id"] = testSoundID
	body["user_id"] = userID2
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "今日弹幕额度已用完（500/500）")

	// 测试会员音需要开通会员才能发弹幕
	body["sound_id"] = 3679
	body["user_id"] = 10
	body["text"] = "text"
	body["stime"] = "0.5"
	body["from"] = user.FromApp
	body["ip"] = userIP
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "开通会员即可发送弹幕了哦~")

	// 测试非会员在付费剧发送弹幕
	body["sound_id"] = 8814073
	body["user_id"] = 3457148
	body["text"] = "text"
	body["stime"] = "0.5"
	body["from"] = user.FromApp
	body["ip"] = userIP
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "您未购买暂时不能发弹幕")

	// 测试会员在付费剧发送弹幕
	body["sound_id"] = 8814074
	body["user_id"] = 3457149
	body["text"] = "text"
	body["stime"] = "0.5"
	body["from"] = user.FromApp
	body["ip"] = userIP
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "您未购买暂时不能发弹幕")

	// 测试拥有字幕维护权限时是否能正常发送弹幕
	key = serviceredis.LockUserDM1.Format(userID)
	require.NoError(service.Redis.Set(key, sound.UserDMMaxNum, time.Minute).Err())
	assignment := role.AuthAssignment{
		UserID:   strconv.FormatInt(userID, 10),
		ItemName: string(role.SubtitleAdmin),
	}
	err = service.DB.FirstOrCreate(&assignment, assignment).Error
	require.NoError(err)
	defer service.DB.Where("userid = ? AND itemname = ?", strconv.FormatInt(userID, 10),
		role.SubtitleAdmin).Delete(role.AuthAssignment{})
	body = map[string]interface{}{
		"sound_id": testSoundID,
		"user_id":  userID,
		"text":     "text",
		"stime":    "0.5",
		"from":     user.FromApp,
		"ip":       userIP,
	}
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.NoError(err)

	// 测试被拉黑用户假发送，返回弹幕信息
	blackedUserID := int64(12)
	blackUserModel := &blackuser.Model{
		BigID:   userID, // testSoundID 拥有者
		SmallID: blackedUserID,
	}
	blackUserModel.Status.Set(blackuser.StatusBigBanSmall)
	err = service.MessageDB.Table(blackuser.Model{}.TableName()).Save(blackUserModel).Error
	require.NoError(err)
	body["user_id"] = blackedUserID
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	result, err := ActionAddDm(c)
	require.NoError(err)
	assert.NotNil(result)

	// 测试 color 传负值的情况
	body["user_id"] = userID
	body["color"] = -12544
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	result, err = ActionAddDm(c)
	require.NoError(err)
	require.IsType(message.MSoundComment{}, result)
	assert.EqualValues(0xffffff, result.(message.MSoundComment).Color)

	// 测试空字符串
	body["text"] = " "
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	require.EqualError(err, "发送的内容不可为空哦")

	// 测试特殊字符
	body["text"] = "\u0000\n\n"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	require.EqualError(err, "发送的内容不能有特殊字符哦")

	// 测试连续空格合并成一个空格
	body["text"] = "hello   world"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err := ActionAddDm(c)
	require.NoError(err)
	require.IsType(message.MSoundComment{}, result)
	var comment message.MSoundComment
	require.NoError(comment.DB().Select("text").Where("id = ?", res.(message.MSoundComment).ID).Find(&comment).Error)
	assert.Equal("hello world", comment.Text)

	// 测试 stime 入库是否只保留两位小数
	body["text"] = "text"
	body["stime"] = "0.23333"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err = ActionAddDm(c)
	require.NoError(err)
	require.IsType(message.MSoundComment{}, result)
	require.NoError(comment.DB().Select("stime").Where("id = ?", res.(message.MSoundComment).ID).Find(&comment).Error)
	assert.Equal("0.23", comment.STime)

	// 测试不在白名单 emoji
	body["text"] = "text\U0001F970test🤩"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err = ActionAddDm(c)
	require.NoError(err)
	assert.Equal(int64(0), res.(message.MSoundComment).ID)

	// 测试白名单 emoji 可以正常发送弹幕
	body = handler.M{
		"sound_id": testSoundID,
		"user_id":  userID,
		"text":     "test🤩",
		"stime":    "0.23333",
		"from":     user.FromApp,
		"ip":       userIP,
	}
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	info, err := ActionAddDm(c)
	require.NoError(err)
	require.IsType(message.MSoundComment{}, info)
	soundComment := info.(message.MSoundComment)
	assert.EqualValues(25, soundComment.Size)
	assert.EqualValues(1, soundComment.Mode)
	assert.EqualValues(160, soundComment.Pool)
	assert.EqualValues(0, soundComment.Color)
	assert.NotEqual(int64(0), soundComment.ID)
	assert.Equal(userIP, soundComment.IP)
	var ipDetail struct {
		CountryName string `json:"country_name"`
		RegionName  string `json:"region_name"`
		CityName    string `json:"city_name"`
	}
	err = json.Unmarshal(soundComment.IPDetail, &ipDetail)
	require.NoError(err)
	assert.Equal("中国", ipDetail.CountryName)
	assert.Equal("山东", ipDetail.RegionName)
	assert.Equal("济南", ipDetail.CityName)

	// 测试发送 "啊啊啊啊啊" 滚动弹幕
	body["text"] = "啊啊啊啊啊"
	body["mode"] = message.DanmakuModeSlide
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err = ActionAddDm(c)
	require.NoError(err)
	assert.Equal(byte(message.DanmakuPoolSpam), res.(message.MSoundComment).Pool)

	// 测试发送 "啊啊啊啊啊啊啊啊啊啊" 顶部弹幕
	body["text"] = "啊啊啊啊啊啊啊啊啊啊"
	body["mode"] = message.DanmakuModeTop
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err = ActionAddDm(c)
	require.NoError(err)
	assert.Equal(byte(message.DanmakuPoolSpam), res.(message.MSoundComment).Pool)

	// 测试用户在同一音频下发送相同弹幕的数量超过了上限
	require.NoError(service.MessageDB.Table(message.MSoundComment{}.TableName()).
		Where("sound_id = ?", testSoundID).Delete("").Error)
	for i := 0; i < 10; i++ {
		m := &message.MSoundComment{
			UserID:  userID,
			SoundID: testSoundID,
			Text:    "23332gogogo",
			Pool:    message.DanmakuPoolNormal,
		}
		require.NoError(service.MessageDB.Create(&m).Error)
	}
	body["text"] = "23332gogogo"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	res, err = ActionAddDm(c)
	require.NoError(err)
	assert.Equal(byte(message.DanmakuPoolLowQuality), res.(message.MSoundComment).Pool)

	// 测试违禁弹幕入删除库
	body["text"] = "操你妈"
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	_, err = ActionAddDm(c)
	assert.EqualError(err, "输入的弹幕中含有违规词汇喔")
	delComment := message.DeleteMSoundComment{}
	require.NoError(service.MessageDB.Table(delComment.TableName()).Where("text = ?", body["text"]).Find(&delComment).Error)
	assert.Equal(body["text"], delComment.Text)

	// 测试当前内容是限定音 暂不能进行此操作
	body = handler.M{
		"sound_id": testSpecialSoundID,
		"user_id":  userID,
		"text":     "test",
		"stime":    "0.5",
		"from":     user.FromApp,
		"ip":       userIP,
	}
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	cancel = mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(i interface{}) (interface{}, error) {
		return []map[string]int64{{
			"drama_id": 7,
		}}, nil
	})
	_, err = ActionAddDm(c)
	cancel()
	assert.EqualError(err, "当前内容是限定音 暂不能进行此操作")

	// 测试当前内容是禁止评论
	c = handler.NewRPCTestContext("rpc/message/add-dm", body)
	cancel = mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(i interface{}) (interface{}, error) {
		return []map[string]int64{{
			"drama_id": 8,
		}}, nil
	})
	defer cancel()
	_, err = ActionAddDm(c)
	assert.EqualError(err, "评论区已关闭")
}

func TestReachDMLimit(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	u := handler.CreateTestUser()
	lockKey := serviceredis.LockUserDM1.Format(u.ID)
	service.Redis.Set(lockKey, strconv.FormatUint(sound.UserDMMaxNum-1, 10), 0)

	limit, err := reachDMLimit(u.ID)
	require.NoError(err)
	assert.Equal(0, limit)

	limit, err = reachDMLimit(u.ID)
	require.NoError(err)
	assert.Equal(sound.UserDMMaxNum, limit)

	ttl, err := service.Redis.TTL(lockKey).Result()
	require.NoError(err)
	assert.NotEqual(-1*time.Second, ttl)

	service.Redis.Del(lockKey)
}

func TestLimitUserAddDanmaku(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testLimitDramaID := int64(11)
	key := keys.LockUserAddDramaDanmaku2.Format(testUserID, testLimitDramaID)
	require.NoError(service.Redis.Del(key).Err())

	err := limitUserAddDanmaku(nil, testUserID, testDramaID)
	require.NoError(err)

	checkInfo := map[string]bool{dramainfo.DramaTypeLimitAddDanmaku: true}
	err = limitUserAddDanmaku(checkInfo, testUserID, testLimitDramaID)
	require.NoError(err)

	key = keys.KeySubtitleTeamUsers0.Format()
	require.NoError(service.Redis.SAdd(key, testUserID).Err())
	err = limitUserAddDanmaku(checkInfo, testUserID, testLimitDramaID)
	require.NoError(err)
	require.NoError(service.Redis.SRem(key, testUserID).Err())

	err = limitUserAddDanmaku(checkInfo, testUserID, testLimitDramaID)
	assert.EqualError(err, "您发送弹幕的频率过快")
}

func TestNewSmartsheetParamDanmaku(t *testing.T) {
	assert := assert.New(t)

	danmaku := &message.MSoundComment{
		ID:      123,
		SoundID: 456,
		UserID:  789,
		Text:    "测试弹幕内容",
	}
	matchedWord := "测试"
	result := newSmartsheetParamDanmaku(danmaku, matchedWord)

	assert.NotNil(result)
	assert.Equal("main_review_"+scan.SceneDanmaku, result.Channel)
	assert.Equal([]string{"弹幕 ID", "音频 ID", "用户 ID", "弹幕内容", "命中词", "创建时间"}, result.Fields)
	assert.Len(result.Values, 6)
	assert.Equal(strconv.FormatInt(danmaku.ID, 10), result.Values[0])
	assert.Equal(strconv.FormatInt(danmaku.SoundID, 10), result.Values[1])
	assert.Equal(strconv.FormatInt(danmaku.UserID, 10), result.Values[2])
	assert.Equal(danmaku.Text, result.Values[3])
	assert.Equal("测试", result.Values[4])
	assert.Equal(util.TimeNow().Format(util.TimeFormatHMS), result.Values[5])
}

func TestPushToSmartsheetDanmaku(t *testing.T) {
	assert := assert.New(t)

	smartsheet.SetMockResult(smartsheet.APISheetAddRecord, smartsheet.SuccessCode, nil)
	service.Smartsheet = smartsheet.NewClient(smartsheet.TestConfig())

	// 测试正常推送弹幕
	danmaku := &message.MSoundComment{
		ID:      123,
		SoundID: 456,
		UserID:  789,
		Text:    "测试弹幕内容",
	}
	matchedWord := "测试"
	assert.True(pushToSmartsheetDanmaku(danmaku, matchedWord))
}
