package message

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// LikeParams struct for like api or dislike api
type LikeParams struct {
	CommentID int64 `json:"comment_id"`
	Sub       int   `json:"sub"`
	UserID    int64 `json:"user_id"`
	Action    int   `json:"action"`
}

// LikeResp 接口响应结构
type LikeResp struct {
	Status bool `json:"status"`
}

const (
	// paramCommentType 评论类型
	paramCommentType int = 0
	// paramSubCommentType 子评论类型
	paramSubCommentType int = 1
)

// ActionTypeAdd 评论点赞或点踩
const ActionTypeAdd = 1

// ActionLike 评论点赞或取消点赞
/**
 * @api {post} /rpc/message/like 评论点赞或取消点赞
 * @apiDescription 该接口拥有幂等性
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} comment_id 评论或子评论 ID
 * @apiParam {number=0,1} [sub=0] 是否为子评论（0：否；1：是）
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=0,1} [action=0] 点赞或取消点赞（0：取消点赞；1：点赞）
 * @apiParamExample {json} Request-Example:
 *     {
 *       "comment_id": 1,
 *       "sub": 0,
 *       "user_id": 1,
 *       "action": 1
 *     }
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info // 当前点赞状态，true 为已点赞，false 为未点赞
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "status": true
 *       }
 *     }
 */
func ActionLike(c *handler.Context) (handler.ActionResponse, error) {
	var params LikeParams
	return likeOrDisLike(c, params, soundcomment.TypeCommentLike)
}

// 点赞或点踩
func likeOrDisLike(c *handler.Context, params LikeParams, likeType int) (handler.ActionResponse, error) {
	err := c.BindJSON(&params)
	if err != nil || params.CommentID <= 0 || params.Sub < 0 || params.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	// 对操作进行加锁，避免频繁操作
	userID := params.UserID
	lock := serviceredis.LockUserComment3.Format(userID, params.CommentID, params.Sub)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUserLimit, "操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)

	var commentInterface interface{}
	isSub := params.Sub == paramSubCommentType
	var commentUserID int64
	if isSub {
		comment := soundcomment.SubComment{}
		err = comment.DB().Where("id = ?", params.CommentID).First(&comment).Error
		commentUserID = comment.UserID
		commentInterface = comment
	} else {
		comment := soundcomment.Comment{}
		err = comment.DB().Where("id = ?", params.CommentID).First(&comment).Error
		commentUserID = comment.UserID
		commentInterface = comment
	}
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrCommentNotFound
		}
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	// 黑名单限制
	err = checkBlacklistRelation(userID, commentUserID)
	if err != nil {
		return nil, err
	}

	var addLikeNotice bool
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		hasLike, err := soundcomment.CommentLike{}.Exists(tx, params.CommentID, userID, params.Sub, likeType)
		if err != nil {
			return err
		}
		if params.Action == ActionTypeAdd {
			if hasLike {
				// 若已点赞或点踩，则直接返回结果
				return nil
			}
			// 新增相关记录
			commentLike := soundcomment.CommentLike{
				CommentID: params.CommentID,
				Sub:       params.Sub,
				UserID:    userID,
				Type:      likeType,
			}
			// TODO: 需要忽略唯一索引错误
			err = commentLike.Save(tx)
			if err != nil {
				return err
			}
			if likeType == soundcomment.TypeCommentLike {
				// 若为点赞，则创建点赞消息提醒
				addLikeNotice, err = soundcomment.AddLikeNotice(tx, commentInterface, userID, isSub)
				return err
			}
			return nil
		}
		// 取消点赞或取消点踩时，删除相关记录
		if !hasLike {
			// 若已无记录，则直接返回结果
			return nil
		}
		// TODO: 需调整为软删除
		return soundcomment.CommentLike{}.Delete(tx, params.CommentID, userID, params.Sub, likeType, true)
	})
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if addLikeNotice {
		util.Go(func() {
			// 为新增的点赞推送提醒消息
			err = checkAndSendLikeMessagePush(userID, commentUserID)
			if err != nil {
				logger.WithFields(logger.Fields{
					"from_user_id":    userID,
					"comment_user_id": commentUserID,
					"comment_id":      params.CommentID,
					"sub":             params.Sub,
				}).Errorf("发送点赞推送消息时出错：%v", err)
				// PASS
			}
		})
	}
	return LikeResp{
		Status: params.Action == ActionTypeAdd,
	}, nil
}

// checkBlacklistRelation 验证是否能点赞或点踩成功
func checkBlacklistRelation(userID, commentUserID int64) error {
	status1, status2, err := blackuser.GetBlacklistRelation(userID, commentUserID)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if status2 {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, "由于对方设置，你无法操作")
	} else if status1 {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, "操作失败，请将对方移除黑名单再试")
	}
	return nil
}
