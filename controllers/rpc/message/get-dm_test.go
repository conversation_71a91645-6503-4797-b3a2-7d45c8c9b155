package message

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
)

const (
	testDmSoundID = 233332
)

func TestGetDm(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试错误参数
	c := handler.NewRPCTestContext("/rpc/message/get-dm", nil)
	_, err := ActionGetDm(c)
	assert.EqualError(err, "参数错误")

	// 测试获取特殊音频弹幕
	params := getDanmakuParams{
		SoundID: sound.SoundNotExists,
	}
	c = handler.NewRPCTestContext("/rpc/message/get-dm", params)
	res, err := ActionGetDm(c)
	require.NoError(err)
	assert.Len(res.(getDanmakuResp).Danmakus, 0)

	// 创建音频
	mSound := new(sound.MSound)
	require.NoError(mSound.DB().Where("id = ?", testDmSoundID).Delete("").Error)
	mSound.ID = testDmSoundID
	mSound.CommentCount = 20
	mSound.Duration = 3 * 1e4
	require.NoError(service.DB.Create(mSound).Error)

	// 创建弹幕
	require.NoError(service.MessageDB.Table(message.MSoundComment{}.TableName()).
		Where("sound_id = ?", testSoundID).Delete("").Error)
	for i := 0; i < 20; i++ {
		danmaku := message.MSoundComment{
			SoundID: testDmSoundID,
			Text:    fmt.Sprintf("23333_%d", i),
			Pool:    message.DanmakuPoolNormal,
		}
		if i < 10 {
			danmaku.Pool = message.DanmakuPoolInferiorQuality
		}
		require.NoError(service.MessageDB.Create(&danmaku).Error)
	}

	// 测试用户获取弹幕
	params.SoundID = testDmSoundID
	c = handler.NewRPCTestContext("/rpc/message/get-dm", params)
	res, err = ActionGetDm(c)
	require.NoError(err)
	assert.Len(res.(getDanmakuResp).Danmakus, 10)
}
