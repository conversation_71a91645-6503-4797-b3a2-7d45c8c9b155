package message

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/jinzhu/gorm"
	"github.com/rivo/uniseg"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	usermiddleware "github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/govern"
	"github.com/MiaoSiLa/missevan-go/models/blackuser"
	"github.com/MiaoSiLa/missevan-go/models/commentnotice"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/models/vip/muservip"
	"github.com/MiaoSiLa/missevan-go/models/voice"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// commentEmoteMaxNum 评论表情数量上限
const commentEmoteMaxNum = 20

// 评论场景
const (
	scenarioDefault = iota
	scenarioBackend
)

// noticeUser 需要发送提醒消息的用户信息
type noticeUser struct {
	userID   int64
	username string
}

func allowComment(elementType int, elementID int64) (bool, string, error) {
	str, ok := soundcomment.LabelOfType[elementType]
	if !ok {
		return false, "", nil
	}
	key := serviceredis.KeyForbiddenCommentElement1.Format(elementType)
	isMember, err := service.Redis.SIsMember(key, elementID).Result()
	if err != nil {
		return false, "", err
	}
	return !isMember, str, nil
}

func validateComment(c util.UserContext, comment *soundcomment.Comment, dramaID int64, scenario int, checkText goclient.CheckTextFunc) error {
	if comment.Content == "" {
		return actionerrors.ErrEmptyComment
	}
	comment.Content = strings.TrimSpace(util.FilterSpecialCodes(comment.Content, false))
	// 优先进行空格和换行处理
	// 参考文档地址：https://info.missevan.com/pages/viewpage.action?pageId=28283751
	comment.Content = soundcomment.ReplaceCommentLine(comment.Content)
	if uniseg.GraphemeClusterCount(comment.Content) > soundcomment.CommentMaxLength {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "字数太多装不下啦~")
	}
	if utf8.RuneCountInString(comment.Content) < soundcomment.CommentMinLength {
		return actionerrors.ErrEmptyComment
	}
	if ok := soundcomment.CheckCommentPunctuation(comment.Content); !ok {
		return actionerrors.ErrBadRequest(handler.CodeInvalidParam, "不可发送单个标点符号")
	}
	if getCommentEmoteNum(comment.Content) > commentEmoteMaxNum {
		return actionerrors.ErrBadRequest(handler.CodeCommentEmoteLimit, "表情数量超过上限")
	}

	// 后台评论无需进行文字检查
	if scenario == scenarioBackend {
		return nil
	}

	result, err := checkText(c, comment.Content, scan.SceneComment)
	if err != nil {
		return err
	}
	if !result.Pass {
		delComment := soundcomment.NewDeleteComment(comment, soundcomment.DeleteTypeByViolation)
		if err = delComment.Archive(); err != nil {
			logger.Errorf("save violation comment error: %v", err)
			// PASS
		}
		return actionerrors.ErrCommentViolation
	}
	if util.HasElem(result.Labels, scan.LabelEvil) {
		comment.Checked = soundcomment.CheckedViolation
	}
	if dramaID > 0 && comment.Checked != soundcomment.CheckedViolation {
		isShamSend := forbiddenwords.CheckText(forbiddenwords.CheckTypeDramaComment, dramaID, comment.Content)
		if isShamSend {
			comment.Checked = soundcomment.CheckedViolation
		}
	}
	return nil
}

// checkCommentUserRelation 验证是否可以发送评论
func checkCommentUserRelation(sender, receiver, upUserID int64) error {
	// 发表评论时，优先判断用户和 UP 主的黑名单关系
	// 发表评论的用户和 UP 主为黑名单关系时，则禁止其回复其他用户
	if upUserID > 0 {
		upInBlockList, inUpBlockList, err := blackuser.GetBlacklistRelation(sender, upUserID)
		if err != nil {
			return actionerrors.ErrServerInternal(err, nil)
		}
		if inUpBlockList {
			return actionerrors.ErrForbidden(handler.CodeUnknownError, "由于 UP 主设置，你无法回复")
		} else if upInBlockList {
			return actionerrors.ErrForbidden(handler.CodeUnknownError, "评论失败，请将 UP 主移除黑名单再试")
		}
		// 接收评论的用户和当前 UP 主为同一个用户时（发表父评论的场景），不需要进行下面的黑名单关系判断
		if receiver == upUserID {
			return nil
		}
	}
	status1, status2, err := blackuser.GetBlacklistRelation(sender, receiver)
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if status2 {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, "由于对方设置，你无法回复")
	} else if status1 {
		return actionerrors.ErrForbidden(handler.CodeUnknownError, "评论失败，请将对方移除黑名单再试")
	}
	return nil
}

// checkDramaCommentStatus 检查剧集评论状态
func checkDramaCommentStatus(refinedInfo map[string]bool) error {
	if refinedInfo[dramainfo.DramaTypeSpecial] {
		return handler.NewActionError(http.StatusForbidden, handler.CodeSoundNotFound, "当前内容是限定音 暂不能进行此操作")
	}
	if refinedInfo[dramainfo.DramaTypeSensitive] {
		return handler.NewActionError(http.StatusForbidden, handler.CodeUnknownError, "评论区已关闭")
	}
	return nil
}

var emoteRegex = regexp.MustCompile(`\[[^\]\s]+?\]`)

// getCommentEmoteNum 获取评论中表情数量
func getCommentEmoteNum(commentContent string) int {
	return len(emoteRegex.FindAllString(commentContent, -1))
}

type contextWorkaround struct {
	*handler.Context
	userID int64
}

func (cw *contextWorkaround) UserID() int64 {
	return cw.userID
}

// ActionAddComment 添加评论
/**
 * @api {post} /rpc/message/add-comment 添加评论
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {number=1,2,4,6,7,8,9} c_type 评论类型（1：单音评论，2：音单评论，3：新闻评论，4：为频道，5：用户评论 \
 * 6：专题评论，7：活动评论，8：语音包评论，9：为求签语音评论）
 * @apiParam {Number} element_id 对象 ID
 * @apiParam {String} comment_content 评论内容
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=1,2} from 评论来源（1：Web，2：App）
 * @apiParam {String} ip 用户 IP
 * @apiParam {string=0,1} [scenario=0] 评论场景（0：用户评论，1：后台评论）
 * @apiParam {String} [user_agent] 客户端 UA
 * @apiParam {String} [origin] web 端 origin
 * @apiParam {String} [buvid] BUVID 设备号
 * @apiParam {String} [equip_id] 设备号
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "id": 1294033,
 *         "comment_content": "2233",
 *         "c_type": 1,
 *         "element_id": 3610,
 *         "ctime": 1614251172,
 *         "sub_comment_num": 0,
 *         "like_num": 0,
 *         "floor": 0,
 *         "ip_location": "山东",
 *         "user": {
 *           "user_id": 3456847, // 用户 ID
 *           "username": "木头人测试", // 用户昵称
 *           "iconurl": "https://static-test.missevan.com/avatars/202009/25/7375331e832236d58033fac58c9599a1105442.png", // 用户头像地址
 *           "authenticated": 0, // 认证标识，1：黑 V；2：金 V；3：蓝 V
 *           "avatar_frame_url": "http://static-test.missevan.com/test_4.webp", // 用户头像框地址，没有时不下发
 *           "is_vip": 1 // 用户是否为会员。0：否；1：是
 *         }
 *       }
 *     }
 */
func ActionAddComment(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		Type           int    `json:"c_type"`
		ElementID      int64  `json:"element_id"`
		CommentContent string `json:"comment_content"`
		UserID         int64  `json:"user_id"`
		From           int    `json:"from"`
		Scenario       int    `json:"scenario"`

		common.UserEquipment
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}

	ok, label, err := allowComment(input.Type, input.ElementID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		if label == "" {
			return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "不支持的评论类型")
		}
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, fmt.Sprintf("本%s禁止评论", label))
	}

	// 判断用户是否存在
	commentUser, err := user.FindByUserID(input.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if commentUser == nil {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "该用户不存在")
	}

	// 后台评论不检查手机号
	if input.Scenario != scenarioBackend {
		// 检查是否绑定手机号
		u := new(usermiddleware.User)
		u.ID = input.UserID
		if isBindMobile, _ := u.IsBindMobile(); !isBindMobile {
			return nil, actionerrors.ErrForbidden(handler.CodeBindMobile, "绑定手机就可以发送评论了哦")
		}
	}

	// 检查评论用户是否在黑名单中
	inBlacklist, err := user.InBlacklist(input.UserID, user.BlackTypeUserComment)
	if err != nil {
		logger.WithField("user_id", input.UserID).Error(err)
		// PASS
	}
	if inBlacklist {
		return nil, actionerrors.ErrForbidden(handler.CodeBannedUser, "您的账号有可疑记录，暂时被系统封停")
	}
	isVipUser, err := muservip.IsVip(input.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	// 检查是否是限定音
	var dramaID int64
	if input.Type == soundcomment.TypeSound {
		dramaID, err = DramaIDBySoundID(input.ElementID, input.IP)
		if err != nil {
			return nil, err
		}
		if dramaID > 0 {
			refined := []string{dramainfo.DramaTypeSpecial, dramainfo.DramaTypeSensitive}
			refinedInfo, err := dramainfo.CheckRefinedMap(dramaID, refined)
			if err != nil {
				return nil, actionerrors.ErrServerInternal(err, nil)
			}
			if err = checkDramaCommentStatus(refinedInfo); err != nil {
				return nil, err
			}
		}
	}

	// 用户评论数限制
	ok, err = soundcomment.CheckLimit(input.UserID)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, actionerrors.ErrBadRequest(handler.CodeUserLimit, "今日评论额度已用完")
	}

	comment := soundcomment.Comment{
		Type:      input.Type,
		ElementID: input.ElementID,
		Content:   input.CommentContent,
	}
	upUserID, upUserName, title, err := checkCommentElement(c, comment, input.UserID, dramaID, isVipUser)
	if err != nil {
		return nil, err
	}
	if upUserID != 0 {
		err = checkCommentUserRelation(input.UserID, upUserID, upUserID)
		if err != nil {
			return nil, err
		}
	}

	commentRule := soundcomment.NewCommentRule(soundcomment.CommentRuleComment, input.Type, input.UserID, upUserID, input.ElementID)
	checked, err := commentRule.IsRepeatComment(input.ElementID, input.CommentContent)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if checked {
		return nil, actionerrors.ErrCommentRepeat
	}

	checked, err = commentRule.IsLimited()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if checked {
		return nil, actionerrors.ErrCommentFrequency
	}

	// 被 @ 的用户
	atUsers, err := parseComment(&comment.Content, input.UserID)
	if err != nil {
		return nil, err
	}

	beRemindedUsers := make(map[int64]string)
	for k, v := range atUsers {
		beRemindedUsers[k] = v
	}

	if upUserID != 0 && upUserID != input.UserID {
		// UP 主不收到 @ 提醒（而收到评论提醒）
		delete(atUsers, upUserID)
		beRemindedUsers[upUserID] = upUserName
	}

	comment.IP = input.IP
	comment.IPDetail, comment.IPLocation = getIPDetailAndLocation(input.IP, input.UserID)

	comment.UserID = commentUser.ID
	comment.Username = commentUser.UserName

	comment.ScoreRatio = soundcomment.DefaultScoreRatio
	comment.ScoreDislikeProportionRatio = soundcomment.FindScoreDislikeProportionRatio()
	comment.ScoreBlacklistRatio = soundcomment.FindScoreBlacklistRatio(comment.UserID)

	sc := util.SmartUserContext{
		UID:         input.UserID,
		IP:          c.ClientIP(),
		UserToken:   c.Token(),
		UserEquipID: c.EquipID(),
		UA:          c.UserAgent(),
		Req:         c.Request(),
	}
	if err = validateComment(sc, &comment, dramaID, input.Scenario, goclient.CheckText); err != nil {
		return nil, err
	}
	var isAddedCommentNotice bool
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := comment.Save(tx)
		if err != nil {
			return err
		}
		if comment.Checked == soundcomment.CheckedNormal {
			// 正常评论需要给相关用户进行消息提醒
			// 如果处理添加评论提示消息不成功，则返回错误信息

			// 自己评论自己的不进行提醒
			delete(beRemindedUsers, input.UserID)
			if len(beRemindedUsers) != 0 {
				err = models.AddCommentNotice(tx, commentUser.ID, commentUser.UserName, atUsers, beRemindedUsers,
					comment.Type, comment.ElementID, comment.ID, title, commentnotice.IsNotSub)
				if err != nil {
					return err
				}
				// beRemindedUsers 中可能包含 UP 主和被 @ 的用户，当 UP 主在自己作品下发评论并 @ 了其他用户时，beRemindedUsers 不为空，因此发评论推送前需检查当前用户非 UP 主
				if input.UserID != upUserID {
					isAddedCommentNotice = true
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	err = commentRule.Add(comment.ID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	reqURLPath := c.Request().URL.Path
	if comment.Type == soundcomment.TypeSound {
		util.Go(func() {
			param := govern.NewAddComment(comment, input.UserEquipment, upUserID, dramaID, title, reqURLPath)
			param.Call()
		})
	}
	util.Go(func() {
		err := user.SendIPLog(input.UserID, input.IP, input.From)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		// 推送评论提醒
		if isAddedCommentNotice {
			err = checkAndSendCommentPush(commentUser, []int64{upUserID}, comment.Content, TitleTypeComment)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}

		// 向被 @ 的用户推送消息
		if len(atUsers) > 0 {
			atUserIDs := make([]int64, 0, len(atUsers))
			for userID := range atUsers {
				atUserIDs = append(atUserIDs, userID)
			}
			err = checkAndSendAtPush(commentUser, atUserIDs, comment.Content)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}

		err = soundcomment.SendLog(input.UserID, comment.Type, comment.ElementID, comment.ID, false, 0, dramaID)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		// 检查命中复审词库时推送到智能表格
		checkAndPushToSmartsheetComment(label, comment.ElementID, false, comment.ID, comment.UserID, comment.Content)
	})

	// 获取用户信息
	comment.User = getCommentUserInfo(c.UserContext(), commentUser, isVipUser, input.Type, input.ElementID)

	return comment, nil
}

func checkAndPushToSmartsheetComment(label string, elementID int64, isSubComment bool, commentID, userID int64, content string) {
	ok, matchedWord, err := forbiddenwords.GetMatchedForbiddenWord(forbiddenwords.ForbiddenWordTypeCommentNotice, content)
	if err != nil {
		logger.Error(err)
		return
	}
	if ok {
		_ = pushToSmartsheetComment(label, elementID, isSubComment, commentID, userID, content, matchedWord)
	}
}

func newSmartsheetParamComment(label string, elementID int64, isSubComment bool, commentID, userID int64, content string, matchedWord string) *smartsheet.SheetAddRecordParam {
	isSubCommentStr := "否"
	if isSubComment {
		isSubCommentStr = "是"
	}
	return &smartsheet.SheetAddRecordParam{
		Channel: "main_review_" + scan.SceneComment,
		Fields:  []string{"评论资源类型", "资源 ID", "是否子评论", "评论 ID", "用户 ID", "评论内容", "命中词", "创建时间"},
		Values:  []string{label, strconv.FormatInt(elementID, 10), isSubCommentStr, strconv.FormatInt(commentID, 10), strconv.FormatInt(userID, 10), content, matchedWord, util.TimeNow().Format(util.TimeFormatHMS)},
	}
}

func pushToSmartsheetComment(label string, elementID int64, isSubComment bool, commentID, userID int64, content string, matchedWord string) bool {
	addRecordParam := newSmartsheetParamComment(label, elementID, isSubComment, commentID, userID, content, matchedWord)
	if addRecordParam == nil {
		return false
	}
	err := service.Smartsheet.AddRecord(addRecordParam)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

type addSubCommentParam struct {
	CommentContent string `json:"comment_content"`
	CommentID      int64  `json:"comment_id"`
	UserID         int64  `json:"user_id"`
	From           int    `json:"from"`

	dramaID int64
	common.UserEquipment
}

// ActionAddSubComment 添加子评论
/**
 * @api {post} /rpc/message/add-subcomment 添加子评论
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {String} comment_content 评论内容
 * @apiParam {Number} comment_id 评论 ID
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=1,2} from 评论来源（1：Web，2：App）
 * @apiParam {String} ip 用户 IP
 * @apiParam {String} [user_agent] 客户端 UA
 * @apiParam {String} [origin] web 端 origin
 * @apiParam {String} [buvid] BUVID 设备号
 * @apiParam {String} [equip_id] 设备号
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "id": 1294034,
 *         "comment_content": "回复信息",
 *         "comment_id": 1,
 *         "ctime": 1614251172,
 *         "like_num": 0,
 *         "floor": 0,
 *         "user": {
 *           "user_id": 3456847, // 用户 ID
 *           "username": "木头人测试", // 用户昵称
 *           "iconurl": "https://static-test.missevan.com/avatars/202009/25/7375331e832236d58033fac58c9599a1105442.png", // 用户头像地址
 *           "authenticated": 0, // 认证标识，1：黑 V；2：金 V；3：蓝 V
 *           "avatar_frame_url": "http://static-test.missevan.com/test_4.webp", // 用户头像框地址，没有时不下发
 *           "is_vip": 1 // 用户是否为会员。0：否；1：是
 *         }
 *       }
 *     }
 */
func ActionAddSubComment(c *handler.Context) (handler.ActionResponse, error) {
	var input addSubCommentParam
	err := c.BindJSON(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// WORKAROUND: 客户端调整后，这个判断可以去掉
	// TAPD 地址：https://www.tapd.bilibili.co/35612194/prong/stories/view/1135612194002487810
	if input.CommentID <= 0 {
		// 拉黑用户后，在评论提醒页，之前与该用户的评论互动提醒还会显示
		// 此时回复之前的评论，客户端传过来的 commentID 是 0，产品要求提示“回复的评论不存在”
		// 新版本客户端会调整成用户不能操作回复
		return nil, actionerrors.ErrReplyCommentNotFound
		// return nil, actionerrors.ErrParams
	}
	input.CommentContent = strings.TrimSpace(input.CommentContent)
	if input.CommentContent == "" {
		return nil, actionerrors.ErrEmptyComment
	}
	if getCommentEmoteNum(input.CommentContent) > commentEmoteMaxNum {
		return nil, actionerrors.ErrBadRequest(handler.CodeCommentEmoteLimit, "表情数量超过上限")
	}
	u := new(usermiddleware.User)
	u.ID = input.UserID
	// 检查是否绑定手机号
	if isBindMobile, _ := u.IsBindMobile(); !isBindMobile {
		return nil, actionerrors.ErrForbidden(handler.CodeBindMobile, "绑定手机就可以发送评论了哦")
	}
	// 检查评论用户是否在黑名单中
	inBlacklist, err := user.InBlacklist(input.UserID, user.BlackTypeUserComment)
	if err != nil {
		logger.WithField("user_id", input.UserID).Error(err)
		// PASS
	}
	if inBlacklist {
		return nil, actionerrors.ErrForbidden(handler.CodeBannedUser, "您的账号有可疑记录，暂时被系统封停")
	}
	// 用户评论数限制
	ok, err := soundcomment.CheckLimit(u.ID)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, actionerrors.ErrBadRequest(handler.CodeUserLimit, "今日评论额度已用完")
	}

	var comment soundcomment.Comment
	err = comment.DB().Where("id = ?", input.CommentID).Take(&comment).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrNotFound(handler.CodeCommentNotFound, "回复的评论已消失在异次元~")
		}
		return nil, err
	}
	// 检查是否是限定音
	if comment.Type == soundcomment.TypeSound {
		input.dramaID, err = DramaIDBySoundID(comment.ElementID, input.IP)
		if err != nil {
			return nil, err
		}
		if input.dramaID > 0 {
			refined := []string{dramainfo.DramaTypeSpecial, dramainfo.DramaTypeSensitive}
			refinedInfo, err := dramainfo.CheckRefinedMap(input.dramaID, refined)
			if err != nil {
				return nil, actionerrors.ErrServerInternal(err, nil)
			}
			if err = checkDramaCommentStatus(refinedInfo); err != nil {
				return nil, err
			}
		}
	}
	ok, label, err := allowComment(comment.Type, comment.ElementID)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, actionerrors.ErrForbidden(handler.CodeUnknownError, fmt.Sprintf("本%s禁止评论", label))
	}

	// FIXME: 在回复子评论时，需要检查拉黑关系做禁止评论限制
	cw := &contextWorkaround{
		Context: c,
		userID:  u.ID,
	}
	subComment, err := addSubComment(&input, cw, input.CommentContent, comment)
	if err != nil {
		return nil, err
	}

	util.Go(func() {
		err := user.SendIPLog(input.UserID, input.IP, input.From)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		err = soundcomment.SendLog(input.UserID, comment.Type, comment.ElementID, subComment.ID, true, comment.ID, input.dramaID)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		// 检查命中复审词库时推送到智能表格
		checkAndPushToSmartsheetComment(label, comment.ElementID, true, subComment.ID, subComment.UserID, subComment.CommentContent)
	})

	return subComment, nil
}

var commentPrefixRegex = regexp.MustCompile(`^回复 @.*?\((\d+)\)：`)

// getSubCommentCheckContentWithReplyUser 获取过滤后的子评论文本、所回复用户信息
func getSubCommentCheckContentWithReplyUser(comment string, beRemindedUsers map[int64]string) (string, *noticeUser, error) {
	var u *noticeUser
	// 正则获取回复子评论前缀字符
	if commentPrefix := commentPrefixRegex.FindStringSubmatch(comment); len(commentPrefix) > 1 {
		// 获取用户 ID
		userID, err := strconv.ParseInt(commentPrefix[1], 10, 64)
		if err != nil {
			return "", nil, actionerrors.ErrServerInternal(err, nil)
		}
		// 判断用户是否合法
		if username, ok := beRemindedUsers[userID]; ok &&
			fmt.Sprintf("回复 @%s(%d)：", username, userID) == commentPrefix[0] {
			comment = strings.TrimPrefix(comment, commentPrefix[0])
			u = &noticeUser{
				userID:   userID,
				username: username,
			}
		}
	}
	return strings.TrimSpace(comment), u, nil
}

func addSubComment(input *addSubCommentParam, c *contextWorkaround, commentContent string, comment soundcomment.Comment) (*soundcomment.SubComment, error) {
	// 判断用户是否存在
	commentUser, err := user.FindByUserID(input.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if commentUser == nil {
		return nil, actionerrors.ErrBadRequest(handler.CodeInvalidParam, "该用户不存在")
	}

	isVipUser, err := muservip.IsVip(commentUser.ID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	upUserID, upUsername, title, err := checkCommentElement(c, comment, commentUser.ID, input.dramaID, isVipUser)
	if err != nil {
		return nil, err
	}
	// 黑名单限制
	err = checkCommentUserRelation(commentUser.ID, comment.UserID, upUserID)
	if err != nil {
		return nil, err
	}

	commentRule := soundcomment.NewCommentRule(soundcomment.CommentRuleSubComment, comment.Type, input.UserID, upUserID, comment.ElementID)
	checked, err := commentRule.IsRepeatComment(input.CommentID, input.CommentContent)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if checked {
		return nil, actionerrors.ErrCommentRepeat
	}

	checked, err = commentRule.IsLimited()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if checked {
		return nil, actionerrors.ErrCommentFrequency
	}

	// 获取需要提醒的用户（包含评论提醒、@ 提醒）信息
	beRemindedUsers, err := parseComment(&commentContent, input.UserID)
	if err != nil {
		return nil, err
	}

	subComment := soundcomment.SubComment{
		Floor:          comment.SubCommentNum + 1,
		CommentID:      comment.ID,
		CommentContent: commentContent,
		UserID:         commentUser.ID,
		Username:       commentUser.UserName,
		Checked:        soundcomment.CheckedNormal,
	}
	err = subComment.Validate()
	if err != nil {
		return nil, actionerrors.ErrBadRequest(handler.CodeFailedToComment, err.Error())
	}

	// 获取回复子评论内容
	commentCheckContent, replyUserInfo, err := getSubCommentCheckContentWithReplyUser(subComment.CommentContent, beRemindedUsers)
	if err != nil {
		return nil, err
	}
	if commentCheckContent == "" {
		return nil, actionerrors.ErrEmptyComment
	}

	result, err := goclient.CheckText(c, commentCheckContent, scan.SceneComment)
	if err != nil {
		return nil, err
	}

	subComment.IP = input.IP
	subComment.IPDetail, _ = getIPDetailAndLocation(input.IP, input.UserID)

	subComment.ScoreRatio = soundcomment.DefaultScoreRatio
	subComment.ScoreDislikeProportionRatio = soundcomment.FindScoreDislikeProportionRatio()
	subComment.ScoreBlacklistRatio = soundcomment.FindScoreBlacklistRatio(subComment.UserID)

	if !result.Pass {
		delComment := soundcomment.NewDeleteSoundSubComment(&subComment, soundcomment.DeleteTypeByViolation)
		if err = delComment.Archive(); err != nil {
			logger.Errorf("save violation subcomment error: %v", err)
			// PASS
		}
		return nil, actionerrors.ErrCommentViolation
	}
	if util.HasElem(result.Labels, scan.LabelEvil) {
		subComment.Checked = soundcomment.CheckedViolation
	}

	if input.dramaID > 0 && comment.Checked != soundcomment.CheckedViolation {
		isShamSend := forbiddenwords.CheckText(forbiddenwords.CheckTypeDramaComment, input.dramaID, subComment.CommentContent)
		if isShamSend {
			subComment.Checked = soundcomment.CheckedViolation
		}
	}

	// 需要推送“评论”提醒的用户 IDs，最多需要向两位用户推送“评论”提醒
	commentUserIDs := make([]int64, 0, 2)
	// 需要推送“回复评论”提醒的用户 IDs，仅需要向当前被回复的用户推送“回复评论”提醒
	replyCommentUserIDs := make([]int64, 0, 1)
	// 保存被 @ 的用户信息，用于后续推送
	var atUserMap map[int64]string
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := subComment.Save(tx)
		if err != nil {
			return err
		}
		if subComment.Checked == soundcomment.CheckedNormal {
			var upUserInfo *noticeUser
			if upUserID != 0 {
				upUserInfo = &noticeUser{
					userID:   upUserID,
					username: upUsername,
				}
			}
			allNoticeUsers, atUsers := filterCommentNoticeUsers(input.UserID, comment, upUserInfo, replyUserInfo, beRemindedUsers)
			atUserMap = atUsers
			if len(allNoticeUsers) != 0 {
				err = models.AddCommentNotice(tx, commentUser.ID, commentUser.UserName, atUsers, allNoticeUsers,
					comment.Type, comment.ElementID, subComment.ID, comment.Content, commentnotice.IsSub)
				if err != nil {
					return err
				}
				// 统计需要推送“评论”提醒、“回复评论”提醒的用户
				// “回复评论”提醒优先级高于“评论”，即回复 UP 主或楼主的评论时，仅向其推送一条“回复评论”提醒
				if replyUserInfo == nil {
					// 用户直接回复楼主
					replyUserInfo = &noticeUser{userID: comment.UserID}
				}
				if replyUserInfo.userID != input.UserID {
					// 向所回复用户推送“回复评论”提醒
					replyCommentUserIDs = append(replyCommentUserIDs, replyUserInfo.userID)
				}
				if !util.HasElem([]int64{input.UserID, replyUserInfo.userID}, comment.UserID) {
					// 向楼主推送“评论”提醒
					commentUserIDs = append(commentUserIDs, comment.UserID)
				}
				if !util.HasElem([]int64{input.UserID, replyUserInfo.userID, comment.UserID, 0}, upUserID) {
					// 向 UP 主推送“评论”提醒
					commentUserIDs = append(commentUserIDs, upUserID)
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	err = commentRule.Add(subComment.ID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	reqURLPath := c.Request().URL.Path
	if comment.Type == soundcomment.TypeSound {
		util.Go(func() {
			dramaID, err := DramaIDBySoundID(comment.ElementID, input.IP)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			param := govern.NewAddSubComment(comment, subComment, input.UserEquipment, upUserID, dramaID, title, commentContent, reqURLPath)
			param.Call()
		})
	}

	util.Go(func() {
		// 推送“评论”提醒
		if len(commentUserIDs) != 0 {
			err = checkAndSendCommentPush(commentUser, commentUserIDs, commentCheckContent, TitleTypeComment)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		// 推送“回复评论”提醒
		if len(replyCommentUserIDs) != 0 {
			err = checkAndSendCommentPush(commentUser, replyCommentUserIDs, commentCheckContent, TitleTypeReplyComment)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		// 向被 @ 的用户推送消息
		if len(atUserMap) > 0 {
			atUserIDs := make([]int64, 0, len(atUserMap))
			for userID := range atUserMap {
				atUserIDs = append(atUserIDs, userID)
			}
			err = checkAndSendAtPush(commentUser, atUserIDs, commentCheckContent)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	})

	// 获取用户信息
	subComment.User = getCommentUserInfo(c.UserContext(), commentUser, isVipUser, comment.Type, comment.ElementID)
	return &subComment, nil
}

// filterCommentNoticeUsers 获取需要提醒的用户信息，依次返回所有需要提醒的用户信息、需要 @ 提醒的用户信息
func filterCommentNoticeUsers(inputUserID int64, comment soundcomment.Comment, upUserInfo *noticeUser, replyUserInfo *noticeUser, beRemindedUsers map[int64]string) (allNoticeUsers, atUsers map[int64]string) {
	beRemindedUsersNum := len(beRemindedUsers)
	// 如果 beRemindedUsers 中不包含 UP 主、楼主、被回复子评论的用户，后续最多增加 3 位需要评论提醒的用户
	allNoticeUsers = make(map[int64]string, beRemindedUsersNum+3)
	atUsers = make(map[int64]string, beRemindedUsersNum)
	for userID, userName := range beRemindedUsers {
		if inputUserID != userID {
			// 自己评论或 @ 自己不进行提醒
			allNoticeUsers[userID] = userName
			atUsers[userID] = userName
		}
	}
	// 向 UP 主发送评论提醒消息
	if upUserInfo != nil && upUserInfo.userID != inputUserID {
		allNoticeUsers[upUserInfo.userID] = upUserInfo.username
		delete(atUsers, upUserInfo.userID)
	}
	// 向楼主发送评论提醒消息
	if comment.UserID != inputUserID {
		allNoticeUsers[comment.UserID] = comment.Username
		delete(atUsers, comment.UserID)
	}
	// 向被回复子评论用户发送评论提醒消息
	if replyUserInfo != nil && replyUserInfo.userID != inputUserID {
		allNoticeUsers[replyUserInfo.userID] = replyUserInfo.username
		delete(atUsers, replyUserInfo.userID)
	}
	return allNoticeUsers, atUsers
}

// checkCommentElement 检查评论的元素是否存在
func checkCommentElement(c models.Context, comment soundcomment.Comment, userID int64, dramaID int64, isVipUser bool) (upUserID int64,
	upUserName string, title string, err error) {
	switch comment.Type {
	case soundcomment.TypeSound:
		var msound sound.MSound
		var limitType int
		err = service.DB.Where("id = ?", comment.ElementID).Take(&msound).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrSoundNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		if msound.Checked == sound.CheckedContractExpired {
			err = actionerrors.ErrContentExpired
			return
		}
		if userID != msound.UserID {
			limitType, err = msound.GetLimitType(isVipUser, c.ClientIP(), userID)
			if err != nil {
				err = actionerrors.ErrServerInternal(err, nil)
				return
			}
			if limitType == sound.LimitTypeVipPay {
				err = actionerrors.ErrForbidden(handler.CodeUnknownError, "开通会员即可评论哦~")
				return
			}
			if util.HasElem([]int{sound.LimitTypeDrama, sound.LimitTypeEpisode}, int(limitType)) {
				err = actionerrors.ErrNeedPurchaseToComment
				return
			}
		}
		upUserID = msound.UserID
		upUserName = msound.Username
		title = msound.Soundstr
	case soundcomment.TypeAlbum:
		var album models.MAlbum
		err = service.DB.Where("id = ?", comment.ElementID).Take(&album).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrAlbumNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		if album.UserID != userID && util.BitMask(album.Refined).IsSet(models.RefinedPrivateAlbum) {
			err = actionerrors.ErrAlbumNotFound
			return
		}
		upUserID = album.UserID
		upUserName = album.UserName
		title = album.Title
	// TODO: 删除 case
	case soundcomment.TypeTag:
		var tag models.MTag
		err = service.DB.Where("id = ? AND recommended = ?", comment.ElementID, models.TagRecommended).Take(&tag).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrTagNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		title = tag.Name
	case soundcomment.TypeTopic:
		var topic models.Topic
		err = service.DB.Where("id = ?", comment.ElementID).Take(&topic).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrTopicNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		title = topic.Title
	case soundcomment.TypeEvent:
		var event *mevent.Simple
		event, err = mevent.FindSimple(comment.ElementID)
		if err != nil {
			err = actionerrors.ErrServerInternal(err, nil)
			return
		}
		if event == nil {
			err = actionerrors.ErrEventNotFound
			return
		}
		title = event.Title
	case soundcomment.TypeVoiceCard:
		var card voice.Card
		var getCard voice.GetCard
		var specials = []int{voice.SpecialNormal, voice.SpecialFestival, voice.SpecialEpisode, voice.SpecialFree, voice.SpecialHotcard}
		err = service.VoiceDB.Where("id = ? AND special IN (?)", comment.ElementID, specials).Take(&card).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrCardNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		if card.Special != voice.SpecialFree && card.Special != voice.SpecialHotcard {
			err = service.VoiceDB.Where("user_id = ? AND card_id = ?", userID, comment.ElementID).Take(&getCard).Error
			if err != nil {
				if servicedb.IsErrNoRows(err) {
					// 未找到记录的情况下提示"未获得的语音不能评论哦"
					err = actionerrors.ErrNeedGetCard
				} else {
					err = actionerrors.ErrServerInternal(err, nil)
				}
				return
			}
		}
		title = card.Title
	case soundcomment.TypeOmikujiCard:
		var card voice.Card
		var getCard voice.GetCard
		var specials = []int{voice.SpecialOmikuji, voice.SpecialOmikujiEpisode}
		err = service.VoiceDB.Where("id = ? AND special IN (?)", comment.ElementID, specials).Take(&card).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				err = actionerrors.ErrCardNotFound
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		err = service.VoiceDB.Where("user_id = ? AND card_id = ?", userID, comment.ElementID).Take(&getCard).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				// 未找到记录的情况下提示"未获得的语音不能评论哦"
				err = actionerrors.ErrNeedGetCard
			} else {
				err = actionerrors.ErrServerInternal(err, nil)
			}
			return
		}
		title = card.Title
	default:
		return 0, "", "", handler.ErrBadRequest
	}
	return
}

var atRegexp = regexp.MustCompile(`(@([\x{2E80}-\x{9FFF}A-Za-z0-9_]+?))(：|\s|$)`)

// parseComment 解析评论中的 @user
func parseComment(commentContent *string, commentUserID int64) (map[int64]string, error) {
	atUsers := make(map[int64]string)

	*commentContent = strings.TrimSpace(*commentContent)
	if *commentContent == "" {
		return atUsers, actionerrors.ErrEmptyComment
	}

	// group[0] = group[1] + group[3]
	// group[1] = @group[2]
	*commentContent = util.ReplaceAllStringSubmatchFunc(atRegexp, *commentContent, func(group []string) string {
		var user user.MowangskUser
		// TODO: 这个操作目前是在 for 循环中，之后挪到外面去
		err := service.DB.Where("username = ?", group[2]).Take(&user).Error
		if err != nil {
			return group[0]
		}
		// TODO: 语音/运势卡
		// @ 黑名单关系的用户时，没有 @ 的通知提醒
		status1, status2, err := blackuser.GetBlacklistRelation(commentUserID, user.ID)
		if err != nil {
			logger.Error(err)
			// PASS
		} else if !status1 && !status2 {
			// 双方都为非拉黑状态时可以收到 @ 通知
			// 且 @ 用户名称后面拼接用户 ID
			atUsers[user.ID] = user.UserName
			return group[1] + "(" + strconv.FormatInt(user.ID, 10) + ")" + group[3]
		}
		// 存在黑名单关系时，被 @ 的一方收不到 @ 通知
		// 且 @ 用户名称后面不拼接用户 ID（点击评论中的用户名不能跳转到其个人主页）
		return group[0]
	})

	return atUsers, nil
}

// getIPDetailAndLocation 获取 IP 详情和地理位置
func getIPDetailAndLocation(ip string, userID int64) (json.RawMessage, string) {
	ipDetailParams, err := user.ParseIPAndGetIPDetailParams(ip, userID)
	if err != nil {
		logger.Error(err)
		return nil, ""
	}
	ipDetail, err := json.Marshal(ipDetailParams)
	if err != nil {
		logger.Error(err)
		return nil, ""
	}
	ipLocation := user.IPLocationFromIPDetailParams(ip, ipDetailParams)
	return ipDetail, ipLocation
}

// getCommentUserInfo 获取用户信息
func getCommentUserInfo(c mrpc.UserContext, commentUser *user.MowangskUser, isVipUser bool, cType int, elementID int64) *soundcomment.CommentUserInfo {
	userInfo := &soundcomment.CommentUserInfo{
		UserID:        commentUser.ID,
		Username:      commentUser.UserName,
		IconURL:       commentUser.IconURL,
		Authenticated: commentUser.Authenticated,
	}
	userAvatarFrames, err := userapi.ListAvatarFrame(c, &userapi.ListAvatarFrameParams{
		UserIDs:     []int64{commentUser.ID},
		Scene:       userapi.SceneComment,
		ElementType: util.NewInt(cType),
		ElementID:   util.NewInt64(elementID),
	})
	if err != nil {
		logger.WithField("user_id", userInfo.UserID).Errorf("获取用户头像框失败：%v", err)
		// PASS
		return userInfo
	}
	if len(userAvatarFrames) == 1 {
		userInfo.AvatarFrameURL = userAvatarFrames[commentUser.ID].AvatarFrameURL
	}
	userInfo.IsVip = util.BoolToInt(isVipUser)
	return userInfo
}
