package message

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/govern"
	"github.com/MiaoSiLa/missevan-go/service"
)

const (
	reportResultTypeReply = iota + 1
	reportResultTypeDM
)

// ActionReportResult 上报审核结果
/**
 * @api {post} /rpc/message/report 上报审核结果
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} unique_id B站上报 ID
 * @apiParam {number=1,2} type 上报类型 1: 评论 2: 弹幕
 * @apiParam {string="pass","reject"} result 审核结果
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionReportResult(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		UniqueID int64  `json:"unique_id"`
		Type     int    `json:"type"`
		Result   string `json:"result"`
	}
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}
	if input.UniqueID <= 0 {
		return nil, handler.ErrInvalidParam
	}
	if input.Result != govern.ReportResultPass && input.Result != govern.ReportResultReject {
		return nil, handler.ErrInvalidParam
	}

	var param govern.ContentParam
	switch input.Type {
	case reportResultTypeReply:
		param.CommonParam = govern.NewContentReplyParam()
	case reportResultTypeDM:
		param.CommonParam = govern.NewContentDMParam()
	default:
		return nil, handler.ErrInvalidParam
	}
	sendParam := param.NewReportResult(input.Result, input.UniqueID)
	err = service.Govern.ReportResult(sendParam)
	if err != nil {
		return nil, err
	}
	return "success", nil
}
