package history

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
)

func TestActionAddRadioPlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 11

	// 删除测试数据
	radioHistory := history.MRadioHistory{}
	db := radioHistory.DB()
	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)

	// 测试参数错误的情况
	params := addRadioHistoryParams{
		SoundID:    1,
		Completion: -1,
	}
	api := "/rpc/history/add-radio-play-history"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionAddRadioPlayHistory(c)
	assert.EqualError(err, "参数错误")

	// 测试新增音频历史记录
	testSoundID := int64(1)
	params = addRadioHistoryParams{
		UserID:     testUserID,
		SoundID:    testSoundID,
		Completion: 0.5,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionAddRadioPlayHistory(c)
	require.NoError(err)
	assert.Equal("添加成功", result.(string))
	// 验证数据已新增
	err = db.Where("user_id = ? AND sound_id = ? AND delete_time = ?",
		testUserID, testSoundID, history.DeleteTimeNotDeleted).
		Take(&radioHistory).Error
	require.NoError(err)
	assert.Equal("{\"completion\":0.5}", *radioHistory.More)
}
