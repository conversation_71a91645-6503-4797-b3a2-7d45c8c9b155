package history

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
)

type delHistoryParams struct {
	UserID  int64   `json:"user_id"`
	IDs     []int64 `json:"ids"`
	RoomIDs []int64 `json:"room_ids"`
}

type clearHistoryParams struct {
	UserID int64 `json:"user_id"`
}

// 操作类型
const (
	// DeleteHistoryTypeDel 通过 ID 删除
	DeleteHistoryTypeDel = 0
	// DeleteHistoryTypeClear 全部删除
	DeleteHistoryTypeClear = 1
)

// ActionDelPlayHistory 删除用户播放历史记录
/**
 * @api {post} /rpc/history/del-play-history 删除用户播放历史记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number[]} ids 历史记录 IDs，最多支持 500 条
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "删除成功"
 *     }
 */
func ActionDelPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params delHistoryParams
	err := c.BindJSON(&params)
	if err != nil || !params.validate() {
		return nil, actionerrors.ErrParams
	}
	var elementIDs []int64
	elementIDs, err = history.FindElementIDsByIDs(history.ElementTypeLiveRoom, params.IDs)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	_, err = history.Del(params.UserID, params.IDs, nil)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(elementIDs) > 0 {
		err = userapi.DeleteUserLiveViewLog(params.UserID, DeleteHistoryTypeDel, elementIDs...)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}

	return "删除成功", nil
}

// ActionClearPlayHistory 清空用户播放历史记录
/**
 * @api {post} /rpc/history/clear-play-history 清空用户播放历史记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "删除成功"
 *     }
 */
func ActionClearPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params clearHistoryParams
	err := c.BindJSON(&params)
	if err != nil || params.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = history.Clear(params.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	err = userapi.DeleteUserLiveViewLog(params.UserID, DeleteHistoryTypeClear)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}

	// 即使（软）删除了 0 条，也返回删除成功
	return "删除成功", nil
}

// ActionDelLivePlayHistory 删除用户收听直播历史记录
/**
 * @api {post} /rpc/history/del-live-play-history 删除用户收听直播历史记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number[]} room_ids 直播间 IDs，最多支持 500 条
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "删除成功"
 *     }
 */
func ActionDelLivePlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params delHistoryParams
	err := c.BindJSON(&params)
	if err != nil || !params.validateLive() {
		return nil, actionerrors.ErrParams
	}
	_, err = history.DelLive(params.UserID, params.RoomIDs, nil)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return "删除成功", nil
}

// ActionClearLivePlayHistory 清空用户收听直播历史记录
/**
 * @api {post} /rpc/history/clear-live-play-history 清空用户收听直播历史记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "删除成功"
 *     }
 */
func ActionClearLivePlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params clearHistoryParams
	err := c.BindJSON(&params)
	if err != nil || params.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = history.ClearLive(params.UserID)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	// 即使（软）删除了 0 条，也返回删除成功
	return "删除成功", nil
}

func (p *delHistoryParams) validate() bool {
	if p.UserID <= 0 || len(p.IDs) == 0 || int64(len(p.IDs)) > history.MaxCount() {
		return false
	}
	return true
}

func (p *delHistoryParams) validateLive() bool {
	if p.UserID <= 0 || len(p.RoomIDs) == 0 || int64(len(p.RoomIDs)) > history.MaxCount() {
		return false
	}
	return true
}
