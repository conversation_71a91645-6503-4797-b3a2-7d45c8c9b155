package history

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/util"
)

type addRadioHistoryParams struct {
	UserID     int64   `json:"user_id"`
	SoundID    int64   `json:"sound_id"`
	Completion float64 `json:"completion,omitempty"`
}

// ActionAddRadioPlayHistory 新增催眠专享播放历史记录
/**
 * @api {post} /rpc/history/add-radio-play-history 新增催眠专享播放历史记录接口
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} sound_id 催眠专享音频 ID
 * @apiParam {Number} [completion] 播放进度百分比，浮点数
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "添加成功"
 *     }
 */
func ActionAddRadioPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	params := addRadioHistoryParams{}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if params.UserID <= 0 || params.SoundID <= 0 {
		return nil, actionerrors.ErrParams
	}
	if params.Completion < 0 || params.Completion > 1 {
		// NOTICE: Completion 为 float64，可能会有精度问题
		logger.WithFields(logger.Fields{
			"user_id":  params.UserID,
			"sound_id": params.SoundID,
		}).Warnf("invalid completion: %f", params.Completion)
		return nil, actionerrors.ErrParams
	}
	err = history.AddRadioHistory(params.UserID, params.SoundID, util.TimeNow(), params.Completion, nil)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return "添加成功", nil
}
