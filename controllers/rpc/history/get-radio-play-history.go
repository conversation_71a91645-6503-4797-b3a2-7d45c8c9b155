package history

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/util"
)

type getRadioHistoryParams struct {
	UserID   int64  `json:"user_id"`
	Marker   string `json:"marker"`
	PageSize int64  `json:"page_size"`

	lastAccessTime util.TimeUnixMilli
}

type radioHistoryResp struct {
	Data       []history.RadioPlayHistory `json:"data"`
	Pagination util.MarkerPagination      `json:"pagination"`
}

// ActionGetRadioPlayHistory 获取催眠专享历史播放记录
/**
 * @api {post} /rpc/history/get-radio-play-history 获取历史播放记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} marker 分页标记
 * @apiParam {Number} page_size 每页条数
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [
 *           {
 *             "id": 5,  // 历史记录 ID
 *             "sound_id": 3,  // 催眠专享音频 ID
 *             "title": "来啦!",
 *             "front_cover": "http://static.missevan.com/test/cover.png",
 *             "background_cover": "http://static.missevan.com/test/background.png",
 *             "access_time": 1642390046000 // 访问时间（毫秒）
 *           }
 *         ],
 *         "pagination": {
 *           "has_more": true,  // 是否有更多数据
 *           "marker": "1617072073590"
 *         }
 *       }
 *     }
 */
func ActionGetRadioPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params getRadioHistoryParams
	err := c.BindJSON(&params)
	if err != nil || !params.validate() {
		return nil, actionerrors.ErrParams
	}
	historyList, hasMore, err := history.FindRadioHistoryList(params.UserID, params.PageSize, params.lastAccessTime)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	var marker string
	historyListLen := len(historyList)
	if historyListLen > 0 {
		lastAccessTime := historyList[historyListLen-1].AccessTime
		marker = strconv.FormatInt(int64(lastAccessTime), 10)
	}
	return radioHistoryResp{
		Data: historyList,
		Pagination: util.MarkerPagination{
			HasMore: hasMore,
			Marker:  marker,
		},
	}, nil
}

func (p *getRadioHistoryParams) validate() bool {
	if p.UserID <= 0 || p.PageSize <= 0 || p.PageSize > handler.MaxPageSize {
		return false
	}
	if p.Marker == "" {
		p.lastAccessTime = util.TimeUnixMilli(0)
	} else {
		lastAccessTime, err := strconv.ParseInt(p.Marker, 10, 64)
		if err != nil || lastAccessTime < 0 {
			return false
		}
		p.lastAccessTime = util.TimeUnixMilli(lastAccessTime)
	}
	return true
}
