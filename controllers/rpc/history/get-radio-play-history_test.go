package history

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/models/mradiosound"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGetRadioPlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testUserID int64 = 1122333
	const testSoundID int64 = 1234

	// 测试参数错误的情况
	params := getRadioHistoryParams{}
	api := "/rpc/history/add-radio-play-history"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionGetRadioPlayHistory(c)
	require.Equal(actionerrors.ErrParams, err)

	// 测试用户无历史记录的情况
	params = getRadioHistoryParams{
		UserID:   testUserID,
		PageSize: 60,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionGetRadioPlayHistory(c)
	require.NoError(err)
	historyResp := result.(radioHistoryResp)
	assert.Empty(historyResp.Data)
	assert.False(historyResp.Pagination.HasMore)
	assert.Equal("", historyResp.Pagination.Marker)

	// 测试有历史记录的情况
	radioHistory := history.MRadioHistory{
		UserID:     testUserID,
		SoundID:    testSoundID,
		DeleteTime: history.DeleteTimeNotDeleted,
		AccessTime: util.TimeUnixMilli(1),
	}
	require.NoError(history.MRadioHistory{}.DB().Create(&radioHistory).Error)
	radioSound := mradiosound.MRadioSound{
		ID:              testSoundID,
		Title:           "test",
		Cover:           "test://test/cover.png",
		BackgroundCover: "test://test/background.png",
	}
	require.NoError(mradiosound.MRadioSound{}.DB().Save(&radioSound).Error)
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionGetRadioPlayHistory(c)
	require.NoError(err)
	historyResp = result.(radioHistoryResp)
	assert.Equal(1, len(historyResp.Data))
	assert.False(historyResp.Pagination.HasMore)
	assert.Equal("1", historyResp.Pagination.Marker)
}

func TestGetRadioHistoryParamsValidate(t *testing.T) {
	assert := assert.New(t)

	p := getRadioHistoryParams{
		UserID:   0,
		Marker:   "0",
		PageSize: 1,
	}
	assert.False(p.validate())

	p = getRadioHistoryParams{
		UserID:   233,
		Marker:   "-1",
		PageSize: 1,
	}
	assert.False(p.validate())

	p = getRadioHistoryParams{
		UserID:   233,
		Marker:   "0",
		PageSize: -1,
	}
	assert.False(p.validate())

	p = getRadioHistoryParams{
		UserID:   233,
		Marker:   "",
		PageSize: 1,
	}
	assert.True(p.validate())
	assert.Equal(util.TimeUnixMilli(0), p.lastAccessTime)

	p = getRadioHistoryParams{
		UserID:   233,
		Marker:   "1",
		PageSize: 1,
	}
	assert.True(p.validate())
	assert.Equal(util.TimeUnixMilli(1), p.lastAccessTime)
}
