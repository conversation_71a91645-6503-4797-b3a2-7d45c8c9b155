package history

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(soundInfo{},
		"user_id", "username", "sound_id", "soundstr", "view_count", "duration",
		"front_cover", "comment_count", "comments_count", "sub_comments_count", "point", "catalog_id")
	kc.Check(dramaItem{},
		"cover", "cover_color", "drama_id",
		"more", "name", "need_pay", "pay_type", "view_count")
	kc.Check(dramaMore{}, "last_play_sound", "node")
	kc.Check(dramaMoreLastPlaySound{},
		"catalog_id", "comment_count", "comments_count", "duration", "id", "name",
		"point", "soundstr", "sub_comments_count", "view_count", "front_cover", "sound_id", "user_id", "username")
	kc.Check(dramaMoreNode{}, "duration", "id", "title")
	kc.Check(liveItem{},
		"catalog_id", "catalog_name", "cover",
		"room_id", "status", "title", "user_id", "username")
}

func TestActionGetPlayHistory(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := util.TimeNow()
	params := getHistoryParams{}
	c := handler.NewRPCTestContext("/rpc/history/add-play-history", params)
	_, err := ActionGetPlayHistory(c)
	require.Equal(actionerrors.ErrParams, err)

	testUserID := int64(3013091)
	db := history.MUserHistory{}.DB()
	require.NoError(db.Table(history.MUserHistory{}.TableName()).Where("user_id IN (?)", []int64{testUserID, testUserID}).
		Delete("").Error)
	more := `{"completion": 0.23}`
	userHistory := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   3,
		ElementType: history.ElementTypeSound,
		AccessTime:  2,
		More:        &more,
	}
	require.NoError(db.Save(&userHistory).Error)

	more = `{"node": {"id": 1, "completion": 0.79}, "last_play_sound": {"id": 3674}}`
	userHistory = history.MUserHistory{
		UserID:      testUserID,
		ElementID:   4,
		ElementType: history.ElementTypeDrama,
		AccessTime:  3,
		More:        &more,
	}
	require.NoError(db.Save(&userHistory).Error)

	params = getHistoryParams{
		UserID:         testUserID,
		LastAccessTime: util.NewTimeUnixMilli(now),
		PageSize:       10,
	}
	c = handler.NewRPCTestContext("/rpc/history/add-play-history", params)
	historyResult, err := ActionGetPlayHistory(c)
	require.NoError(err)
	tutil.PrintJSON(historyResult)

	// 测试无历史记录的情况
	params.UserID = 99999999
	c = handler.NewRPCTestContext("/rpc/history/add-play-history", params)
	historyResult, err = ActionGetPlayHistory(c)
	require.NoError(err)
	result := historyResult.(resp)
	assert.Len(result.Data, 0)
	assert.False(result.HasMore)
	assert.Zero(result.LastAccessTime)
}

func TestGetHistoryParamsValidate(t *testing.T) {
	assert := assert.New(t)

	p := getHistoryParams{
		UserID: 0,
	}
	assert.False(p.validate())
	p.PageSize = handler.MaxPageSize + 1
	assert.False(p.validate())
	p = getHistoryParams{
		UserID:   1,
		PageSize: 10,
	}
	assert.True(p.validate())
}

func TestIsAllowSoundCheckedStatus(t *testing.T) {
	assert := assert.New(t)

	assert.True(isAllowSoundCheckedStatus(sound.CheckedUnpass))
	assert.True(isAllowSoundCheckedStatus(sound.CheckedPass))
	assert.True(isAllowSoundCheckedStatus(sound.CheckedContractExpired))
}

func TestAddSoundDetails(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := []*soundItem{
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeSound},
			soundInfo: soundInfo{SoundID: 3}},
		{baseHistoryItem: baseHistoryItem{
			ID:          233,
			ElementType: history.ElementTypeSound},
			soundInfo: soundInfo{SoundID: 233}},
		{baseHistoryItem: baseHistoryItem{
			ID:          234,
			ElementType: history.ElementTypeSound},
			soundInfo: soundInfo{SoundID: 234}},
		{baseHistoryItem: baseHistoryItem{
			ID:          236,
			ElementType: history.ElementTypeSound},
			soundInfo: soundInfo{SoundID: 236}},
	}
	require.NoError(addSoundDetails(l, []int64{3, 233, 234, 236}))
	assert.Equal("已失效音频", l[0].SoundStr)
	assert.Empty(l[0].Username)
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", l[0].FrontCover)
	assert.Equal("测试音频", l[1].SoundStr)
	assert.Equal("InVinCiblezz", l[1].Username)
	assert.Equal("测试获取剧集信息（音频 1）", l[2].SoundStr)
	assert.Equal("InVinCiblezz", l[2].Username)
	assert.Equal("http://static-test.missevan.com/coversmini/201701/24/92610e061e2c05937f8c823f93857c57091415.png", l[1].FrontCover)
	assert.Equal(int64(67939), l[2].ViewCount)
	assert.Equal("已失效音频", l[3].SoundStr)
}

func TestAddDramaDetails(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data := []*dramaItem{
		{baseHistoryItem: baseHistoryItem{
			ID:          400,
			ElementType: history.ElementTypeDrama},
			DramaID: 400,
			More: &dramaMore{
				Node: &dramaMoreNode{
					ID: 1,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          5,
			ElementType: history.ElementTypeDrama},
			DramaID: 5,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3674,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3674,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3675,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3676,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3674,
				},
				Node: &dramaMoreNode{
					ID: 1,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
			More: &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: 3674,
				},
				Node: &dramaMoreNode{
					ID: 3,
				},
			},
		},
		{baseHistoryItem: baseHistoryItem{
			ID:          3,
			ElementType: history.ElementTypeDrama},
			DramaID: 3,
		},
	}
	dramaIDs := []int64{3, 4, 5}
	episodeSoundIDs := []int64{3674, 3675}
	nodeIDs := []int64{1, 2}
	testUserID := int64(3013092)
	require.NoError(addDramaDetails(data, dramaIDs, episodeSoundIDs, nodeIDs, testUserID))

	assert.Equal("已失效剧集", data[0].Name)
	assert.Equal("已失效音频", data[1].More.LastPlaySound.Name)
	assert.Equal("测试获取剧集 2", data[2].Name)
	assert.Equal(&dramaMore{
		LastPlaySound: &dramaMoreLastPlaySound{
			ID:         3674,
			Name:       "测试获取剧集信息（单集 7）",
			UserID:     346286,
			Username:   "InVinCiblezz",
			SoundID:    3674,
			FrontCover: "http://static-test.missevan.com/coversmini/201701/24/92610e061e2c05937f8c823f93857c57091415.png",

			SoundStr:         "测试限定音频",
			Duration:         2768,
			ViewCount:        67939,
			CommentCount:     103,
			CommentsCount:    133,
			SubCommentsCount: 107,
			Point:            263,
			CatalogID:        23,
		},
	}, data[2].More)
	assert.Equal("测试获取剧集 2", data[3].Name)
	assert.Equal(&dramaMore{
		LastPlaySound: &dramaMoreLastPlaySound{
			ID:         0,
			Name:       "已失效音频",
			SoundStr:   "已失效音频",
			FrontCover: "http://static-test.missevan.com/coversmini/nocover.png",
		},
	}, data[4].More)
	assert.Equal(&dramaMore{
		LastPlaySound: &dramaMoreLastPlaySound{
			ID: 3674,
		},
		Node: &dramaMoreNode{
			ID:       1,
			Title:    "烬 · 第一章 - 华丽开场",
			Duration: 4579,
		},
	}, data[5].More)
	assert.Equal("已失效节点", data[6].More.Node.Title)
	tutil.PrintJSON(data[7])
	assert.Equal("测试获取剧集 2", data[7].Name)
	assert.Equal(dramainfo.NeedPayUnpaid, *data[7].NeedPay)
	assert.Nil(data[7].More)
}

func TestAddLiveRoomDetails(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dataList := []*liveItem{
		{
			RoomID: 999997,
			baseHistoryItem: baseHistoryItem{
				ElementType: history.ElementTypeLiveRoom},
		},
		{
			RoomID: 999998,
			baseHistoryItem: baseHistoryItem{
				ElementType: history.ElementTypeSound},
		},
		{
			RoomID: 999999,
			baseHistoryItem: baseHistoryItem{
				ElementType: history.ElementTypeLiveRoom},
		},
		{
			RoomID: 1000000,
			baseHistoryItem: baseHistoryItem{
				ElementType: history.ElementTypeLiveRoom},
		},
	}
	require.NoError(addLiveRoomDetails(dataList, []int64{999997, 999998, 999999}))
	assert.Equal("测试直播间 1", dataList[0].Title)
	assert.Equal("直播", dataList[0].CatalogName)
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", dataList[0].Cover)
	assert.Equal(liveItem{
		RoomID: 999998,
		baseHistoryItem: baseHistoryItem{
			ElementType: history.ElementTypeSound},
	}, *dataList[1])
	assert.Equal("测试直播间 3", dataList[2].Title)
	assert.Equal("音频", dataList[2].CatalogName)
	assert.Equal("已失效直播间", dataList[3].Title)
}
