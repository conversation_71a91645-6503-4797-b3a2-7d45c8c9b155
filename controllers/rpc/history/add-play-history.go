package history

import (
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/util"
)

type addHistoryParams struct {
	UserID       int64   `json:"user_id"`
	ElementID    int64   `json:"element_id"`
	ElementType  int     `json:"element_type"`
	NodeID       int64   `json:"node_id,omitempty"`
	SubElementID int64   `json:"sub_element_id,omitempty"`
	Completion   float64 `json:"completion,omitempty"`
}

// ActionAddPlayHistory 新增播放历史记录
/**
 * @api {post} /rpc/history/add-play-history 新增播放历史记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} element_id 元素 ID
 * @apiParam {number=1,2,3} element_type 元素 ID（1 音频，2 剧集，3 直播间）
 * @apiParam {Number} [node_id] 互动剧节点 ID，当 element_type 为剧集且为互动剧时传入
 * @apiParam {Number} [sub_element_id] 子元素 ID，当 element_type 为剧集时传入当前播放的音频 ID
 * @apiParam {Number} [completion] 播放进度百分比，浮点数，当 element_type 为音频或剧集时传入
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "添加成功"
 *     }
 */
func ActionAddPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params addHistoryParams
	err := c.BindJSON(&params)
	if err != nil || !params.validate() {
		return nil, actionerrors.ErrParams
	}
	switch params.ElementType {
	case history.ElementTypeSound:
		err = history.AddSound(params.UserID, params.ElementID, util.TimeNow(), params.Completion, nil)
	case history.ElementTypeDrama:
		err = history.AddDrama(params.UserID, params.ElementID, params.SubElementID, params.NodeID, util.TimeNow(), params.Completion, nil)
	case history.ElementTypeLiveRoom:
		err = history.AddLiveRoom(params.UserID, params.ElementID, util.TimeNow(), nil)
	default:
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return "添加成功", nil
}

func (p *addHistoryParams) validate() bool {
	if p.UserID <= 0 || p.ElementID <= 0 || p.ElementType <= 0 ||
		p.SubElementID < 0 || p.NodeID < 0 {
		return false
	}
	if p.Completion < 0 || p.Completion > 1 {
		// NOTOCE: Completion 为 float64，可能会有精度问题
		logger.WithFields(logger.Fields{
			"user_id":      p.UserID,
			"element_id":   p.ElementID,
			"element_type": p.ElementType,
		}).Warnf("invalid completion: %f", p.Completion)
		return false
	}
	return true
}
