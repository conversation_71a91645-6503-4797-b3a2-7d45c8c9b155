package history

import (
	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/catalog"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/models/live"
	"github.com/MiaoSiLa/missevan-go/models/node/msoundnode"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

type getHistoryParams struct {
	UserID         int64              `json:"user_id"`
	LastAccessTime util.TimeUnixMilli `json:"last_access_time"`
	PageSize       int64              `json:"page_size"`
	ElementTypes   []int              `json:"element_types"`
}

type resp struct {
	Data           []interface{}      `json:"data"`
	HasMore        bool               `json:"has_more"`
	LastAccessTime util.TimeUnixMilli `json:"last_access_time"`
}

type baseHistoryItem struct {
	ID          int64              `json:"id"` // ID 为 m_user_history 表的主键 ID，可以根据该 ID 进行删除记录等操作
	ElementType int                `json:"element_type"`
	AccessTime  util.TimeUnixMilli `json:"access_time"`
}

// TODO: soundInfo 目前只有一个结构体会用，没必要单独定义
type soundInfo struct {
	UserID           int64  `json:"user_id"`
	Username         string `json:"username"`
	SoundID          int64  `json:"sound_id"`
	SoundStr         string `json:"soundstr"`
	ViewCount        int64  `json:"view_count"`
	Duration         int    `json:"duration"`
	FrontCover       string `json:"front_cover"`
	CommentCount     int64  `json:"comment_count"`
	CommentsCount    int64  `json:"comments_count"`
	SubCommentsCount int64  `json:"sub_comments_count"`
	Point            int64  `json:"point"`
	CatalogID        int64  `json:"catalog_id"`
}

type soundItem struct {
	baseHistoryItem
	soundInfo
}

type dramaItem struct {
	baseHistoryItem
	DramaID    int64      `json:"drama_id"`
	Name       string     `json:"name"`
	Cover      string     `json:"cover"`
	CoverColor int        `json:"cover_color"`
	ViewCount  int64      `json:"view_count"`
	PayType    uint32     `json:"pay_type"`
	NeedPay    *int       `json:"need_pay"`
	More       *dramaMore `json:"more,omitempty"`
}

type dramaMore struct {
	LastPlaySound *dramaMoreLastPlaySound `json:"last_play_sound,omitempty"`
	Node          *dramaMoreNode          `json:"node,omitempty"`
}

// TODO: dramaMoreLastPlaySound 里面的字段可以用 soundInfo 代替，且这些 struct 可以挪到 models
type dramaMoreLastPlaySound struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	UserID     int64  `json:"user_id"`
	Username   string `json:"username"`
	SoundID    int64  `json:"sound_id"`
	FrontCover string `json:"front_cover"`

	SoundStr         string `json:"soundstr"`
	Duration         int    `json:"duration"`
	ViewCount        int64  `json:"view_count"`
	CommentCount     int64  `json:"comment_count"`
	CommentsCount    int64  `json:"comments_count"`
	SubCommentsCount int64  `json:"sub_comments_count"`
	Point            int64  `json:"point"`
	CatalogID        int64  `json:"catalog_id"`
}

type dramaMoreNode struct {
	ID       int64  `json:"id"`
	Title    string `json:"title"`
	Duration int64  `json:"duration"`
}

type liveItem struct {
	baseHistoryItem
	UserID      int64  `json:"user_id"`
	Username    string `json:"username"`
	RoomID      int64  `json:"room_id"`
	Title       string `json:"title"`
	Cover       string `json:"cover"`
	CatalogName string `json:"catalog_name"`
	Status      int    `json:"status"`
	CatalogID   int64  `json:"catalog_id"`
}

// ActionGetPlayHistory 获取历史播放记录
/**
 * @api {post} /rpc/history/get-play-history 获取历史播放记录
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} last_access_time 最后历史记录访问时间戳（毫秒），用于分页，返回的数据的访问时间是这个时间之前的。传入 0 时表示从最新的一条记录开始取
 * @apiParam {Number} page_size 每页条数
 * @apiParam {Number[]} [element_types] 当需要获取指定类型的历史记录时传入，若不传或传空，则返回所有类型的历史记录
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [
 *           {
 *             "id": 5,  // 历史记录 ID（删除历史记录时传此值）
 *             "element_type": 1,  // 元素类型：1 音频、2 剧集、3 直播间
 *             "sound_id": 3,
 *             "soundstr": "来啦!",
 *             "view_count": 2,
 *             "duration": 1120,  // 音频时长（毫秒）
 *             "front_cover": "http://static.missevan.com/covers/oss://201701/24/d337b82e692709.png",
 *             "user_id": 1,
 *             "username": "昵称_YG5op67Y",
 *             "comment_count": 100,
 *             "comments_count": 90,
 *             "sub_comments_count": 80,
 *             "point": 10,
 *             "soundstr": "来啦!",
 *             "catalog_id": 104,
 *             "access_time": 1642390046000 // 访问时间（毫秒）
 *           },
 *           {
 *             "id": 6,
 *             "element_type": 3,
 *             "room_id": 999777,
 *             "title": "room-title",
 *             "cover": "http://foo.com/bar.png",
 *             "catalog_name": "闲聊",
 *             "catalog_id": 106,
 *             "user_id": 3,
 *             "username": "暗切线",
 *             "status": 1,  // 1 开播中，0 未开播
 *             "access_time": 1642390046001
 *           },
 *           {
 *             "id": 4,
 *             "element_type": 2,
 *             "drama_id": 34,
 *             "name": "囧囧有神",
 *             "cover": "http://static.missevan.com/dramacoversmini/201807/02/4d9c68c818509.jpg",
 *             "cover_color": 12434877,
 *             "view_count": 10853,
 *             "pay_type": 2,
 *             "need_pay": 1,
 *             "more": {
 *               "last_play_sound": {
 *                 "id": 1,
 *                 "name": "第六期",
 *                 "duration": 1120,  // 音频时长（毫秒）
 *                 "view_count": 563,
 *                 "comment_count": 100,
 *                 "comments_count": 90,
 *                 "sub_comments_count": 80,
 *                 "point": 10,
 *                 "soundstr": "来啦!",
 *                 "catalog_id": 104
 *               }
 *             },
 *             "access_time": 1642390046002
 *           },
 *           {
 *             "id": 9,
 *             "element_type": 2,
 *             "drama_id": 8432,
 *             "name": "互动广播剧《猎场》",
 *             "cover": "http://static.missevan.com/dramacoversmini/201807/02/4d9c68c818509.jpg",
 *             "cover_color": 12434877,
 *             "view_count": 10853,
 *             "pay_type": 2,
 *             "need_pay": 1,
 *             "more": {
 *               "last_play_sound": {
 *                 "id": 3674
 *               },
 *               "node": {
 *                 "id": 677,
 *                 "title": "序幕",
 *                 "duration": 303120
 *               }
 *             },
 *             "access_time": 1642390046003
 *           }
 *         ],
 *         "has_more": true,  // 是否有更多数据
 *         "last_access_time": 1617072073590
 *       }
 *     }
 */
func ActionGetPlayHistory(c *handler.Context) (handler.ActionResponse, error) {
	var params getHistoryParams
	err := c.BindJSON(&params)
	if err != nil || !params.validate() {
		return nil, actionerrors.ErrParams
	}
	historys, hasMore, err := history.FindList(params.UserID, params.PageSize, params.LastAccessTime, params.ElementTypes)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	historysLen := len(historys)
	if historysLen == 0 {
		return resp{
			Data: []interface{}{},
		}, nil
	}
	soundIDs := make([]int64, 0, historysLen)
	dramaIDs := make([]int64, 0, historysLen)
	episodeSoundIDs := make([]int64, 0, historysLen)
	nodeIDs := make([]int64, 0, historysLen)
	roomIDs := make([]int64, 0, historysLen)
	// 给列表添加基本信息
	data := make([]interface{}, 0, historysLen)
	soundItemList := make([]*soundItem, 0, historysLen)
	dramaItemList := make([]*dramaItem, 0, historysLen)
	liveItemList := make([]*liveItem, 0, historysLen)
	for _, historyRow := range historys {
		baseItem := baseHistoryItem{
			ID:          historyRow.ID,
			ElementType: historyRow.ElementType,
			AccessTime:  historyRow.AccessTime,
		}
		switch historyRow.ElementType {
		case history.ElementTypeSound:
			soundIDs = append(soundIDs, historyRow.ElementID)
			s := &soundItem{
				baseHistoryItem: baseItem,
				soundInfo: soundInfo{
					SoundID: historyRow.ElementID,
				},
			}
			soundItemList = append(soundItemList, s)
			data = append(data, s)
		case history.ElementTypeDrama:
			d := &dramaItem{
				baseHistoryItem: baseItem,
				DramaID:         historyRow.ElementID,
			}
			dramaIDs = append(dramaIDs, historyRow.ElementID)
			if historyRow.MoreInfo.LastPlaySound != nil {
				episodeSoundIDs = append(episodeSoundIDs, historyRow.MoreInfo.LastPlaySound.ID)
				d.More = &dramaMore{
					LastPlaySound: &dramaMoreLastPlaySound{
						ID: historyRow.MoreInfo.LastPlaySound.ID,
					},
				}
			}
			if historyRow.MoreInfo.Node != nil {
				nodeID := historyRow.MoreInfo.Node.ID
				nodeIDs = append(nodeIDs, nodeID)
				d.More = &dramaMore{
					Node: &dramaMoreNode{
						ID: nodeID,
					},
				}
			}
			dramaItemList = append(dramaItemList, d)
			data = append(data, d)
		case history.ElementTypeLiveRoom:
			roomIDs = append(roomIDs, historyRow.ElementID)
			l := &liveItem{
				baseHistoryItem: baseItem,
				RoomID:          historyRow.ElementID,
			}
			liveItemList = append(liveItemList, l)
			data = append(data, l)
		default:
			logger.WithFields(logger.Fields{
				"element_type": historyRow.ElementType,
				"element_id":   historyRow.ElementID,
				"user_id":      historyRow.UserID,
			}).Error("未知的元素类型")
			// PASS
			continue
		}
	}
	// 添加历史记录详情
	if len(soundIDs) > 0 {
		err = addSoundDetails(soundItemList, soundIDs)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	if len(dramaIDs) > 0 {
		err = addDramaDetails(dramaItemList, dramaIDs, episodeSoundIDs, nodeIDs, params.UserID)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	if len(roomIDs) > 0 {
		err = addLiveRoomDetails(liveItemList, roomIDs)
		if err != nil {
			return nil, actionerrors.ErrServerInternal(err, nil)
		}
	}
	return resp{
		Data:           data,
		HasMore:        hasMore,
		LastAccessTime: historys[historysLen-1].AccessTime,
	}, nil
}

func (p *getHistoryParams) validate() bool {
	if p.UserID <= 0 || p.LastAccessTime < 0 || p.PageSize <= 0 || p.PageSize > handler.MaxPageSize {
		return false
	}
	return true
}

func isAllowSoundCheckedStatus(checked int) bool {
	// CheckedUnpass 是为了保证 UP 主看自己已转码未过审的音频没问题
	allowCheckedStatus := []int{sound.CheckedUnpass, sound.CheckedPass, sound.CheckedContractExpired}
	return util.HasElem(allowCheckedStatus, checked)
}

// 给收听音频历史记录添加详细信息
func addSoundDetails(dataList []*soundItem, soundIDs []int64) error {
	sounds := make([]sound.MSound, 0, len(soundIDs))
	err := service.DB.Table(sound.MSound{}.TableName()).
		Select(`id, checked, soundstr, intro, view_count, duration, cover_image, user_id, username, 
		catalog_id, comment_count, comments_count, sub_comments_count, point, soundstr`).
		Where("id IN (?)", soundIDs).
		Find(&sounds).Error
	if err != nil {
		return err
	}
	soundsLen := len(sounds)
	soundsMap := make(map[int64]sound.MSound)
	if soundsLen > 0 {
		soundsMap = util.ToMap(sounds, "ID").(map[int64]sound.MSound)
	}
	// 可在历史记录中查看的音频状态
	for index, data := range dataList {
		soundInfo, ok := soundsMap[data.SoundID]
		if !ok || !isAllowSoundCheckedStatus(soundInfo.Checked) {
			// 若音频不可查看，则相关字段给到失效默认值
			// TODO: 对 soundInfo 内信息的处理应该放在 models
			dataList[index].SoundStr = "已失效音频"
			dataList[index].FrontCover = service.Storage.Parse(params.URL.DefaultCoverURL)
			continue
		}
		dataList[index].ViewCount = soundInfo.ViewCount
		dataList[index].Duration = soundInfo.Duration
		dataList[index].FrontCover = soundInfo.FrontCover
		dataList[index].UserID = soundInfo.UserID
		dataList[index].Username = soundInfo.Username
		dataList[index].CommentCount = soundInfo.CommentCount
		// TODO: 这块的处理产品文档还没有给到，待产品文档给到后再看是否要调整
		dataList[index].CommentsCount = soundInfo.CommentsCount
		dataList[index].SubCommentsCount = soundInfo.SubCommentsCount
		dataList[index].Point = soundInfo.Point
		dataList[index].CatalogID = int64(soundInfo.CatalogID)
		dataList[index].SoundStr = soundInfo.Soundstr
		// 音频历史记录暂不返回播放进度信息 More
	}
	return nil
}

// addDramaDetails 给收听剧集历史记录添加详细信息
func addDramaDetails(dataList []*dramaItem, dramaIDs, episodeSoundIDs, nodeIDs []int64, userID int64) error {
	// 获取剧集信息
	dramaLen := len(dramaIDs)
	dramas := make([]dramainfo.RadioDramaDramainfo, 0, dramaLen)
	dramaDB := service.DramaDB
	err := dramaDB.Table(dramainfo.RadioDramaDramainfo{}.TableName()).
		Select("id, name, pay_type, cover, cover_color").
		Where("id IN (?)", dramaIDs).
		Find(&dramas).Error
	if err != nil {
		return err
	}
	// 剧集添加 need_pay 信息
	err = dramainfo.CheckNeedPay(dramas, userID)
	if err != nil {
		return err
	}
	dramasMap := make(map[int64]dramainfo.RadioDramaDramainfo)
	if len(dramas) > 0 {
		dramasMap = util.ToMap(dramas, "ID").(map[int64]dramainfo.RadioDramaDramainfo)
	}
	// 获取音频信息
	sounds := make([]sound.MSound, 0, len(episodeSoundIDs))
	err = service.DB.Table(sound.MSound{}.TableName()).Select(`id, catalog_id, duration, user_id, username, cover_image, soundstr, checked,
	view_count, comment_count, point, comments_count, sub_comments_count`).
		Where("id IN (?)", episodeSoundIDs).Find(&sounds).Error
	if err != nil {
		return err
	}
	soundsMap := util.ToMap(sounds, "ID").(map[int64]sound.MSound)
	// 获取单集信息
	episodes := make([]dramaepisode.RadioDramaEpisode, 0, len(episodeSoundIDs))
	err = dramaDB.Table(dramaepisode.RadioDramaEpisode{}.TableName()).Select("drama_id, name").
		Where("sound_id IN (?)", episodeSoundIDs).Find(&episodes).Error
	if err != nil {
		return err
	}
	episodesMap := util.ToMap(episodes, "DramaID").(map[int64]dramaepisode.RadioDramaEpisode)
	// 获取节点信息
	nodeLen := len(nodeIDs)
	nodes := make([]msoundnode.MSoundNode, 0, nodeLen)
	if nodeLen > 0 {
		err = service.DB.Table(msoundnode.MSoundNode{}.TableName()).Select("id, sound_id, title, duration").
			Where("id IN (?)", nodeIDs).Find(&nodes).Error
		if err != nil {
			return err
		}
	}
	nodesMap := util.ToMap(nodes, "ID").(map[int64]msoundnode.MSoundNode)

	// 有效历史记录中每个剧仅会存在一条记录，故此处可用 drama_id 做节点 key 构造一对一的关系
	for index, data := range dataList {
		drama, ok := dramasMap[data.DramaID]
		if !ok {
			// 剧集不存在时，相关字段给到失效默认值
			dataList[index].Name = "已失效剧集"
			dataList[index].Cover = service.Storage.Parse(params.URL.DefaultCoverURL)
			continue
		}
		dataList[index].Cover = drama.CoverURL
		dataList[index].CoverColor = drama.CoverColor
		dataList[index].ViewCount = drama.ViewCount
		dataList[index].PayType = drama.PayType
		dataList[index].NeedPay = drama.NeedPay
		if drama.Name != nil {
			dataList[index].Name = *drama.Name
		}
		if data.More == nil {
			continue
		}
		var soundInfo sound.MSound
		var soundExists bool
		if data.More.LastPlaySound != nil {
			soundInfo, soundExists = soundsMap[data.More.LastPlaySound.ID]
			if !soundExists || !isAllowSoundCheckedStatus(soundInfo.Checked) {
				// 若该单集音频不可查看，则相关字段给到失效默认值
				dataList[index].More.LastPlaySound.ID = 0
				dataList[index].More.LastPlaySound.Name = "已失效音频"
				dataList[index].More.LastPlaySound.SoundStr = "已失效音频"
				dataList[index].More.LastPlaySound.FrontCover = service.Storage.Parse(params.URL.DefaultCoverURL)
				continue
			}
		} else {
			dataList[index].More.LastPlaySound = &dramaMoreLastPlaySound{}
		}
		episode, ok := episodesMap[data.DramaID]
		if !ok {
			// 单集不存在时，相关字段给到失效默认值
			dataList[index].More.LastPlaySound.ID = 0
			dataList[index].More.LastPlaySound.Name = "已失效音频"
			dataList[index].More.LastPlaySound.SoundStr = "已失效音频"
			dataList[index].More.LastPlaySound.FrontCover = service.Storage.Parse(params.URL.DefaultCoverURL)
			continue
		}
		// var more dramaMore
		if data.More.Node == nil {
			// 若不为互动剧，则返回单集和音频信息
			dataList[index].More = &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID:               soundInfo.ID,
					Name:             episode.Name,
					UserID:           soundInfo.UserID,
					Username:         soundInfo.Username,
					SoundID:          soundInfo.ID,
					FrontCover:       soundInfo.FrontCover,
					SoundStr:         soundInfo.Soundstr,
					Duration:         soundInfo.Duration,
					ViewCount:        soundInfo.ViewCount,
					CommentCount:     soundInfo.CommentCount,
					CommentsCount:    soundInfo.CommentsCount,
					SubCommentsCount: soundInfo.SubCommentsCount,
					Point:            soundInfo.Point,
					CatalogID:        int64(soundInfo.CatalogID),
					// 暂不返回播放进度
				},
			}
		} else {
			// 若为互动剧，则返回互动剧节点信息
			nodeInfo, ok := nodesMap[data.More.Node.ID]
			if !ok {
				// 节点不存在时，相关字段给到失效默认值
				dataList[index].More.Node.Title = "已失效节点"
				continue
			}
			dataList[index].More = &dramaMore{
				LastPlaySound: &dramaMoreLastPlaySound{
					ID: soundInfo.ID,
				},
				Node: &dramaMoreNode{
					ID:       nodeInfo.ID,
					Title:    nodeInfo.Title,
					Duration: nodeInfo.Duration,
				},
			}
		}
	}
	return nil
}

// addLiveRoomDetails 给用户收听直播历史记录添加详细信息
func addLiveRoomDetails(dataList []*liveItem, liveRoomIDs []int64) error {
	liveRoomInfos := make([]live.Live, 0, len(liveRoomIDs))
	err := live.Live{}.DB().Where("room_id IN (?)", liveRoomIDs).
		Select("room_id, user_id, cover, catalog_id, title, status").
		Find(&liveRoomInfos).Error
	if err != nil {
		return err
	}
	userIDList := make([]int64, 0, len(liveRoomIDs))
	catalogIDList := make([]int64, 0, len(liveRoomIDs))
	liveRoomInfoMap := make(map[int64]live.Live, len(liveRoomInfos))
	for _, r := range liveRoomInfos {
		userIDList = append(userIDList, r.UserID)
		catalogIDList = append(catalogIDList, r.CatalogID)
		liveRoomInfoMap[r.RoomID] = r
	}
	var users []user.Simple
	err = service.DB.Table(user.Simple{}.TableName()).Where("id IN (?)", userIDList).
		Select("id, username").
		Find(&users).Error
	if err != nil {
		return err
	}
	usernameMap := make(map[int64]user.Simple)
	if len(users) > 0 {
		usernameMap = util.ToMap(users, "ID").(map[int64]user.Simple)
	}

	type catalogName struct {
		ID          int64  `gorm:"column:id"`
		CatalogName string `gorm:"column:catalog_name"`
	}
	var catalogNameList []catalogName
	err = service.DB.Table(catalog.TableName()).Where("id IN (?)", catalogIDList).
		Select("id, catalog_name").
		Find(&catalogNameList).Error
	if err != nil {
		logger.Error(err)
		// PASS
	}
	catalogNameMap := make(map[int64]catalogName)
	if len(catalogNameList) > 0 {
		catalogNameMap = util.ToMap(catalogNameList, "ID").(map[int64]catalogName)
	}

	// 可在历史记录中查看的音频状态
	for index, data := range dataList {
		if data.ElementType != history.ElementTypeLiveRoom {
			continue
		}
		liveRoom, ok := liveRoomInfoMap[data.RoomID]
		if !ok {
			dataList[index].Title = "已失效直播间"
			dataList[index].Cover = service.Storage.Parse(params.URL.DefaultCoverURL)
			continue
		}
		catalogName := catalogNameMap[liveRoom.CatalogID].CatalogName
		if catalogName == "" {
			// 分类不存在时，返回“直播”字样
			catalogName = "直播"
		}
		username := usernameMap[liveRoom.UserID].UserName
		dataList[index].Title = liveRoom.Title
		dataList[index].Cover = liveRoom.CoverURL
		dataList[index].CatalogID = liveRoom.CatalogID
		dataList[index].CatalogName = catalogName
		dataList[index].UserID = liveRoom.UserID
		dataList[index].Username = username
		dataList[index].Status = liveRoom.Status
	}
	return nil
}
