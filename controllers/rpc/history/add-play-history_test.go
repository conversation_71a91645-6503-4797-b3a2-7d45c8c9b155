package history

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
)

func TestValidate(t *testing.T) {
	assert := assert.New(t)

	// 测试参数正确的情况
	p := addHistoryParams{
		UserID:      1,
		ElementID:   1,
		ElementType: 1,
	}
	assert.True(p.validate())

	// 测试参数错误的情况
	p = addHistoryParams{
		UserID:      0,
		ElementID:   1,
		ElementType: 1,
		Completion:  -1,
	}
	assert.False(p.validate())
}

func TestActionAddPlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 11

	// 删除测试数据
	userHistory := history.MUserHistory{}
	db := userHistory.DB()
	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)

	// 测试参数错误的情况
	params := addHistoryParams{
		ElementID:   1,
		ElementType: 1,
		Completion:  -1,
	}
	api := "/rpc/history/add-play-history"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionAddPlayHistory(c)
	assert.EqualError(err, "参数错误")

	// 测试新增音频历史记录
	params = addHistoryParams{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: history.ElementTypeSound,
		Completion:  0.5,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionAddPlayHistory(c)
	require.NoError(err)
	assert.Equal("添加成功", result.(string))
	// 验证数据已新增
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 1, history.ElementTypeSound, history.DeleteTimeNotDeleted).
		Take(&userHistory).Error
	require.NoError(err)
	assert.Equal("{\"completion\":0.5}", *userHistory.More)

	// 测试新增剧集历史记录
	params = addHistoryParams{
		UserID:       testUserID,
		ElementID:    1,
		ElementType:  history.ElementTypeDrama,
		SubElementID: 1,
		Completion:   0.6666,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionAddPlayHistory(c)
	require.NoError(err)
	assert.Equal("添加成功", result.(string))
	// 验证数据已新增
	userHistory = history.MUserHistory{}
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 1, history.ElementTypeDrama, history.DeleteTimeNotDeleted).
		Take(&userHistory).Error
	require.NoError(err)
	assert.Equal("{\"last_play_sound\":{\"id\":1,\"completion\":0.6666}}", *userHistory.More)

	// 测试新增用户收听直播历史记录
	params = addHistoryParams{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: history.ElementTypeLiveRoom,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err = ActionAddPlayHistory(c)
	require.NoError(err)
	assert.Equal("添加成功", result.(string))
	// 验证数据已新增
	userHistory = history.MUserHistory{}
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 1, history.ElementTypeLiveRoom, history.DeleteTimeNotDeleted).
		Take(&userHistory).Error
	require.NoError(err)
	assert.Nil(userHistory.More)
}
