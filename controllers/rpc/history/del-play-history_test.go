package history

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/history"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestDelHistoryParamsValidate(t *testing.T) {
	assert := assert.New(t)

	// 测试参数正确的情况
	p := delHistoryParams{
		IDs:    []int64{1, 2},
		UserID: 1,
	}
	assert.True(p.validate())
	assert.Equal(2, len(p.IDs))

	// 测试参数错误的情况
	p = delHistoryParams{
		UserID: -1,
	}
	assert.False(p.validate())
}

func TestActionDelPlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 12

	// 测试参数错误的情况
	params := delHistoryParams{
		UserID: -1,
	}
	api := "/rpc/history/del-play-history"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionDelPlayHistory(c)
	assert.EqualError(err, "参数错误")

	now := util.TimeNow().Unix()
	// 新增音频历史记录
	userHistory := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: history.ElementTypeSound,
		AccessTime:  util.TimeUnixMilli(now),
	}
	db := userHistory.DB()
	err = db.Save(&userHistory).Error
	require.NoError(err)

	// 测试删除成功的情况
	params = delHistoryParams{
		IDs:    []int64{userHistory.ID},
		UserID: testUserID,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionDelPlayHistory(c)
	require.NoError(err)
	assert.Equal("删除成功", result.(string))
	// 验证数据已被删除
	var r history.MUserHistory
	require.NoError(db.Where("id = ?", userHistory.ID).Scan(&r).Error)
	require.GreaterOrEqual(r.ModifiedTime, now)
	require.GreaterOrEqual(r.DeleteTime, now)
}

func TestActionClearPlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 13

	// 测试参数错误的情况
	params := clearHistoryParams{
		UserID: -1,
	}
	api := "/rpc/history/clear-play-history"
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionClearPlayHistory(c)
	assert.EqualError(err, "参数错误")

	// 新增音频历史记录
	userHistory := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   2,
		ElementType: history.ElementTypeSound,
		AccessTime:  1,
	}
	db := userHistory.DB()
	err = db.Save(&userHistory).Error
	require.NoError(err)

	// 测试删除用户历史记录
	cancel := mrpc.SetMock(userapi.URIUserLiveViewLogDelete, func(input interface{}) (output interface{}, err error) {
		return "删除成功", nil
	})
	defer cancel()

	params = clearHistoryParams{
		UserID: testUserID,
	}
	c = handler.NewRPCTestContext(api, params)
	now := util.TimeNow().Unix()
	result, err := ActionClearPlayHistory(c)
	require.NoError(err)
	assert.Equal("删除成功", result.(string))
	// 验证数据已被删除
	var r history.MUserHistory
	require.NoError(db.Where("id = ?", userHistory.ID).Scan(&r).Error)
	require.GreaterOrEqual(r.ModifiedTime, now)
	require.GreaterOrEqual(r.DeleteTime, now)
}

func TestActionDelLivePlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 12

	// 测试参数错误的情况
	api := "/rpc/history/del-live-play-history"
	params := delHistoryParams{
		UserID: -1,
	}
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionDelLivePlayHistory(c)
	assert.EqualError(err, "参数错误")

	// 测试参数错误的情况
	params = delHistoryParams{
		RoomIDs: []int64{},
		UserID:  testUserID,
	}
	c = handler.NewRPCTestContext(api, params)
	_, err = ActionDelLivePlayHistory(c)
	assert.EqualError(err, "参数错误")

	now := util.TimeNow().Unix()
	// 新增用户收听直播历史记录 1
	userHistory := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   3,
		ElementType: history.ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	userHistoryDB := userHistory.DB()
	require.NoError(userHistoryDB.Save(&userHistory).Error)

	// 新增用户收听直播历史记录 2
	userHistory2 := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   4,
		ElementType: history.ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	require.NoError(userHistoryDB.Save(&userHistory2).Error)

	// 测试删除成功的情况
	params = delHistoryParams{
		RoomIDs: []int64{userHistory.ElementID, userHistory2.ElementID},
		UserID:  testUserID,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionDelLivePlayHistory(c)
	require.NotNil(result)
	require.NoError(err)
	assert.Equal("删除成功", result.(string))
	// 验证数据已被删除
	var r history.MUserHistory
	require.NoError(userHistoryDB.Where("id = ?", userHistory.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)

	require.NoError(userHistoryDB.Where("id = ?", userHistory2.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)
}

func TestActionClearLivePlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 13

	// 测试参数错误的情况
	api := "/rpc/history/clear-live-play-history"
	params := clearHistoryParams{
		UserID: -1,
	}
	c := handler.NewRPCTestContext(api, params)
	_, err := ActionClearLivePlayHistory(c)
	assert.EqualError(err, "参数错误")

	now := util.TimeNow().Unix()
	// 新增用户收听直播历史记录 1
	userHistory := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   5,
		ElementType: history.ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	userHistoryDB := userHistory.DB()
	require.NoError(userHistoryDB.Save(&userHistory).Error)

	// 新增用户收听直播历史记录 2
	userHistory2 := history.MUserHistory{
		UserID:      testUserID,
		ElementID:   6,
		ElementType: history.ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	require.NoError(userHistoryDB.Save(&userHistory2).Error)

	// 测试删除成功的情况
	params = clearHistoryParams{
		UserID: testUserID,
	}
	c = handler.NewRPCTestContext(api, params)
	result, err := ActionClearLivePlayHistory(c)
	require.NotNil(result)
	require.NoError(err)
	assert.Equal("删除成功", result.(string))
	// 验证数据已被删除
	var r history.MUserHistory
	require.NoError(userHistoryDB.Where("id = ?", userHistory.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)

	require.NoError(userHistoryDB.Where("id = ?", userHistory2.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)
}
