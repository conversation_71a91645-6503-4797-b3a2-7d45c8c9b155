package user

import (
	"encoding/json"
	"errors"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/util"
)

type updateUserPointParam struct {
	UserID int64           `json:"user_id"`
	Point  int             `json:"point"`
	Type   int             `json:"type"`
	Origin int             `json:"origin"`
	More   json.RawMessage `json:"more"`
}

// ActionUpdateUserPoint 增加减少用户小鱼干
/**
 * @api {post} /rpc/user/updatepoint 增加减少用户小鱼干
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} point 小鱼干数量，正数为增加，负数为减少
 * @apiParam {number{1-11}} type 小鱼干操作分类；详情可见：https://info.missevan.com/pages/viewpage.action?pageId=53188232
 * @apiParam {number=0,1,2} origin 操作来源：0：Web；1：手机网页；2：App
 * @apiParam {Object} [more] 小鱼干变动日志的额外信息
 *
 * @apiSuccessExample Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionUpdateUserPoint(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newUpdateUserPointParam(c)
	if err != nil {
		return nil, err
	}
	err = param.updatePoint()
	if err != nil {
		return nil, err
	}

	return "success", nil
}

func newUpdateUserPointParam(c *handler.Context) (*updateUserPointParam, error) {
	param := new(updateUserPointParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if param.UserID <= 0 || param.Point == 0 || param.Type <= 0 ||
		!util.HasElem([]int{user.TypeOriginWeb, user.TypeOriginMobileWeb, user.TypeOriginApp}, param.Origin) {
		return nil, actionerrors.ErrParams
	}

	return param, nil
}

// updatePoint 更新小鱼干数量，并且发送日志消息
func (u *updateUserPointParam) updatePoint() error {
	info := user.PointDetailLog{
		CreateTime: util.TimeNow().Unix(),
		UserID:     u.UserID,
		Num:        u.Point,
		Origin:     u.Origin,
		Type:       u.Type,
		More:       u.More,
	}
	ok, err := info.UpdatePoint()
	if err != nil {
		return actionerrors.ErrServerInternal(err, nil)
	}
	if !ok {
		if u.Point < 0 {
			return actionerrors.ErrNotEnoughPoint
		}
		return actionerrors.ErrServerInternal(errors.New("更新小鱼干数量失败"), nil)
	}

	return nil
}
