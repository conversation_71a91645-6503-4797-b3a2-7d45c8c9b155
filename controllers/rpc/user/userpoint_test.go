package user

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionUpdateUserPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(3013097)

	key := keys.DatabusKeyUserPointDetailLog1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	u, err := user.FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	expectedPoint := u.Point

	body := handler.M{
		"user_id":  0,
		"point":    1,
		"type":     1,
		"origin":   user.TypeOriginWeb,
		"event_id": 123,
		"sound_id": 123,
	}
	c := handler.NewRPCTestContext("/rpc/user/increase-point", body)
	_, err = ActionUpdateUserPoint(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["user_id"] = testUserID
	c = handler.NewRPCTestContext("/rpc/user/increase-point", body)
	resp, err := ActionUpdateUserPoint(c)
	require.NoError(err)
	assert.Equal("success", resp)

	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	assert.Equal(key, m.Key)

	u, err = user.FindByUserID(testUserID)
	require.NoError(err)
	assert.Equal(expectedPoint+1, u.Point)
}

func TestNewUpdateUserPointParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{
		"user_id":  0,
		"point":    1,
		"type":     1,
		"origin":   user.TypeOriginWeb,
		"event_id": 123,
		"sound_id": 123,
	}
	c := handler.NewRPCTestContext("/rpc/user/increase-point", body)
	_, err := newUpdateUserPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["user_id"] = 123
	body["origin"] = 3
	c = handler.NewRPCTestContext("/rpc/user/increase-point", body)
	_, err = newUpdateUserPointParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["origin"] = user.TypeOriginWeb
	c = handler.NewRPCTestContext("/rpc/user/increase-point", body)
	param, err := newUpdateUserPointParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(body["user_id"], param.UserID)
}

func TestUpdateUserPointParam_updatePoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(3013097)

	key := keys.DatabusKeyUserPointDetailLog1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	u, err := user.FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	expectedPoint := u.Point

	param := updateUserPointParam{
		UserID: testUserID,
		Point:  1,
		Type:   1,
		Origin: 1,
		More:   json.RawMessage(`{"event_id": 123, "sound_id": 123}`),
	}
	require.NoError(param.updatePoint())

	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	assert.Equal(key, m.Key)

	u, err = user.FindByUserID(testUserID)
	require.NoError(err)
	assert.Equal(expectedPoint+1, u.Point)

	param.Point = -1
	require.NoError(param.updatePoint())

	msgs = service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m = <-msgs
	assert.Equal(key, m.Key)

	u, err = user.FindByUserID(testUserID)
	require.NoError(err)
	assert.Equal(expectedPoint, u.Point)
}
