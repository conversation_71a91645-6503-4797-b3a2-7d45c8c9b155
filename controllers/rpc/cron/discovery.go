package cron

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/main/msearchinterventionkeyword"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

type cleanParam struct {
	activeMap map[int][]int64
	cleanMap  map[int][]int64
}

type cleanResp struct {
	CleanNum int64 `json:"clean_num"`
}

// ActionCleanSearchIntervention 清理干预搜索配置
// 运行周期：15 */5 * * * *
/**
 * @api {post} /rpc/cron/discovery/clean-search-intervention 清理干预搜索配置
 * @apiDescription 清理失效配置，每 5 分钟运行一次
 * @apiVersion 0.1.0
 * @apiGroup rpc/cron/discovery
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "clean_num": 1
 *     }
 *   }
 */
func ActionCleanSearchIntervention(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newCleanParam()
	if err != nil {
		return nil, err
	}
	if param == nil || len(param.cleanMap) == 0 {
		return &cleanResp{CleanNum: 0}, nil
	}
	resp, err := param.clean()
	if err != nil {
		return nil, err
	}
	param.sendAdminLog(c)
	return resp, nil
}

func newCleanParam() (*cleanParam, error) {
	msiks, err := msearchinterventionkeyword.FindActiveInterventions()
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	if len(msiks) == 0 {
		return nil, nil
	}

	param := &cleanParam{
		activeMap: make(map[int][]int64),
		cleanMap:  make(map[int][]int64),
	}
	for _, msik := range msiks {
		param.activeMap[msik.TargetType] = append(param.activeMap[msik.TargetType], msik.TargetID)
	}

	param.findCleanMap()
	return param, nil
}

func (param *cleanParam) findCleanMap() {
	for targetType, targetIDs := range param.activeMap {
		switch targetType {
		case search.TypeDrama:
			invalidDramaIDs := findInvalidDramaIDs(targetIDs)
			if len(invalidDramaIDs) > 0 {
				param.cleanMap[targetType] = invalidDramaIDs
			}
		default:
			logger.WithFields(logger.Fields{
				"target_type": targetType,
				"target_ids":  targetIDs,
			}).Error("干预类型有误，暂时只支持清理剧集干预")
			// PASS
		}
	}
}

func (param *cleanParam) clean() (*cleanResp, error) {
	cleanNum, err := msearchinterventionkeyword.CleanSearchIntervention(param.cleanMap)
	if err != nil {
		return nil, actionerrors.ErrServerInternal(err, nil)
	}
	return &cleanResp{
		CleanNum: cleanNum,
	}, nil
}

func (param *cleanParam) sendAdminLog(c *handler.Context) {
	var msg string
	for k, v := range param.cleanMap {
		msg += fmt.Sprintf("干预类型 %d，干预元素 ID %v ", k, v)
	}
	msg += "由于干预内容不合规，被系统设置状态为已停止"
	adminLog := &adminlogger.AdminLog{
		Catalog: adminlogger.CatalogKeywordSearchIntervention,
		URL:     c.Request().URL.Path,
		Intro:   msg,
		IP:      c.ClientIP(),
	}
	// TODO: 后续改成调用 rpc 接口
	err := adminLog.InsertAdminLog()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func findInvalidDramaIDs(targetIDs []int64) []int64 {
	uniqIDs := util.Uniq(targetIDs)
	activeDramaIDs, err := dramainfo.FindActiveDramaIDs(uniqIDs)
	if err != nil {
		logger.Error(err)
		// PASS
		return []int64{}
	}

	invalidDramaIDs := make([]int64, 0, len(uniqIDs)-len(activeDramaIDs))
	for _, targetID := range uniqIDs {
		if util.HasElem(activeDramaIDs, targetID) {
			continue
		}
		invalidDramaIDs = append(invalidDramaIDs, targetID)
	}
	return invalidDramaIDs
}
