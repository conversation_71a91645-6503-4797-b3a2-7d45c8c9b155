package cron

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/main/msearchinterventionkeyword"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionCleanSearchIntervention(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewRPCTestContext("/rpc/cron/discovery/clean-search-intervention", nil)
	info, err := ActionCleanSearchIntervention(c)
	require.NoError(err)
	require.IsType(&cleanResp{}, info)
	resp := info.(*cleanResp)
	assert.EqualValues(3, resp.CleanNum)

	info, err = ActionCleanSearchIntervention(c)
	require.NoError(err)
	require.IsType(&cleanResp{}, info)
	resp = info.(*cleanResp)
	assert.Zero(resp.CleanNum)
}

func TestNewCleanParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	msik := msearchinterventionkeyword.MSearchInterventionKeyword{
		Keyword:    "测试 newCleanParam",
		TargetType: search.TypeDrama,
		Status:     1,
		StartTime:  0,
		EndTime:    4086556140,
	}
	err := msearchinterventionkeyword.MSearchInterventionKeyword{}.
		DB().Create(&msik).Error
	require.NoError(err)

	param, err := newCleanParam()
	require.NoError(err)
	require.NotNil(param)
	assert.Len(param.activeMap, 1)
}

func TestCleanParam_findCleanMap(t *testing.T) {
	assert := assert.New(t)

	param := cleanParam{
		activeMap: map[int][]int64{
			5: {1, 2, 3},
			0: {1, 2, 3},
		},
		cleanMap: make(map[int][]int64),
	}
	param.findCleanMap()
	assert.EqualValues(map[int][]int64{5: {3}}, param.cleanMap)
}

func TestCleanParam_clean(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	msik := msearchinterventionkeyword.MSearchInterventionKeyword{
		TargetType: search.TypeDrama,
		TargetID:   999999999,
		Status:     1,
	}
	err := msearchinterventionkeyword.MSearchInterventionKeyword{}.
		DB().Create(&msik).Error
	require.NoError(err)

	param := cleanParam{
		cleanMap: map[int][]int64{
			5: {999999999},
		},
	}
	resp, err := param.clean()
	require.NoError(err)
	assert.EqualValues(1, resp.CleanNum)
}

func TestFindInvalidDramaIDs(t *testing.T) {
	assert := assert.New(t)

	invalidDramaIDs := findInvalidDramaIDs([]int64{1, 2, 3})
	assert.EqualValues([]int64{3}, invalidDramaIDs)

	invalidDramaIDs = findInvalidDramaIDs([]int64{})
	assert.Empty(invalidDramaIDs)

	notExistsDramaIDs := []int64{999999, 9999999}
	invalidDramaIDs = findInvalidDramaIDs(notExistsDramaIDs)
	assert.EqualValues(notExistsDramaIDs, invalidDramaIDs)
}
