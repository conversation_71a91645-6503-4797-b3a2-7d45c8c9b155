package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestActionGetRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	c := handler.NewRPCTestContext("/rpc/sound/get-recommend", recommendParams{
		SoundID: 100001,
	})
	result, err := ActionGetRecommend(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试正常返回
	c = handler.NewRPCTestContext("/rpc/sound/get-recommend", recommendParams{
		SoundID: 100001,
		Num:     3,
	})
	result, err = ActionGetRecommend(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok := result.(recommendResp)
	require.True(ok)
	assert.Equal(3, len(resp.Sounds))

	// 测试推荐音频为空
	c = handler.NewRPCTestContext("/rpc/sound/get-recommend", recommendParams{
		SoundID: 1217690,
		Num:     3,
	})
	result, err = ActionGetRecommend(c)
	require.NoError(err)
	require.NotNil(result)
	resp, ok = result.(recommendResp)
	require.True(ok)
	assert.Equal(0, len(resp.Sounds))
}
