# missevan-go

![build state](https://ci-badges.maoer.co/badges/MiaoSiLa/missevan-go/build.svg) ![coverage state](https://ci-badges.maoer.co/badges/MiaoSiLa/missevan-go/coverage.svg)

## Start

```sh
go get github.com/MiaoSiLa/missevan-go
cd $GOPATH/src/github.com/MiaoSiLa/missevan-go/
cp config/config.{example.,}yml
cp config/params.{example.,}yml
go build
./missevan-go [mode] -config-dir ./config
```
