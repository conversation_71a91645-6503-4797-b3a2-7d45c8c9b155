ARG registry_prefix

FROM missevan-go:scratch AS app

FROM ${registry_prefix}geoip-data

COPY --from=app /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=app /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=app /etc/passwd /etc/
COPY --from=app /bin/bat /bin/missevan-go /bin/
# nobody writable
COPY --from=app --chown=65534:65534 /etc/missevan-go /etc/missevan-go

WORKDIR /

EXPOSE 3032
USER nobody

HEALTHCHECK --interval=30s --timeout=2s --start-period=5s \
  CMD ["/bin/bat", "-print=", "-pretty=false", "http://localhost:3032/health"]

ENTRYPOINT ["/bin/missevan-go"]
