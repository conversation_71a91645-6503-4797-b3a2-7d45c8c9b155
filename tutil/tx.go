// +build !release

package tutil

import (
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRollbackTx tests error (e.g. SQL error) returned by function f and will always rollback.
func TestRollbackTx(t *testing.T, db *gorm.DB, f func(*gorm.DB) error) {
	t.Helper()
	tx := db.Begin()
	require.NoError(t, tx.Error)
	defer tx.Rollback()
	assert.NoError(t, f(tx))
}
