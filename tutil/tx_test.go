package tutil

import (
	"math/rand"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestTestRollbackTx(t *testing.T) {
	require := require.New(t)
	db, err := servicedb.InitDatabase(&servicedb.Config{
		Host:         "mysql.srv.maoer.co",
		Port:         3306,
		User:         "root",
		Name:         "app_missevan",
		Pass:         "rootmysql",
		MaxIdleConns: servicedb.DefaultMaxIdleConns(),
		MaxLifeTime:  "10s",
	})
	require.NoError(err)
	utime := rand.Int31()
	f := func(tx *gorm.DB) error {
		return tx.Table("mowangskuser").Where("id = 12").Update("utime", utime).Error
	}
	TestRollbackTx(t, db, f)
	var res []int64
	require.NoError(db.Table("mowangskuser").Where("id = 12").Pluck("utime", &res).Error)
	require.NotEqual(utime, res[0])
}
