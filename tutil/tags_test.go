package tutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type testStruct struct {
	gormDefaultPK

	A int ` bson:"-" form:"a" gorm:"column:a;primary_key" json:"a" yaml:"a"`
	B int ` bson:"b" form:"-" gorm:"column:b" json:"-" yaml:"b"`
	C int ` bson:"c" form:"c" gorm:"column:c" json:"c,omitempty" yaml:"c"`
	d int ` bson:"d" form:"d" gorm:"column:d" yaml:"d"`
	S int
}

type gormDefaultPK struct {
	ID int64 `gorm:"column:id"`
}

func TestTagKeyExists(t *testing.T) {
	assert := assert.New(t)

	v := testStruct{d: 1} // 使用下 d 防止 lint 报错
	assert.Empty(KeyExists(BSON, v, "b", "c", "S"))
	assert.Empty(KeyExists(GORM, v, "a", "b", "c", "s"))
	assert.Empty(KeyExists(JSON, &v, "a", "c", "S"))
	assert.Empty(KeyExists(YAML, &v, "a", "b", "c", "S"))
	assert.Empty(KeyExists(FORM, &v, "a", "c", "S"))
	assert.Equal([]string{"S", "c"}, KeyExists(JSON, &v, "a"))
}

func TestTagKeysOption(t *testing.T) {
	assert := assert.New(t)

	assert.Equal([]string{"c"}, JSON.Keys(&testStruct{}, "omitempty"))
	assert.Equal([]string{"id"}, GORM.Keys(gormDefaultPK{}, "pk"))
}
