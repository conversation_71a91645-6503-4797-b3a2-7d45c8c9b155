// +build !release

package tutil

import (
	"reflect"
	"strings"
	"unicode"

	"github.com/jinzhu/gorm"
)

// TagParser 通用 tag keyParser
type TagParser struct {
	TagName string
}

// Keys 获取 v 所含 tag keys
func (tp TagParser) Keys(v interface{}, option string) []string {
	t := reflect.TypeOf(v)
	if t.Kind() == reflect.Ptr {
		t = reflect.TypeOf(reflect.Indirect(reflect.ValueOf(v)).Interface())
	}
	pre := make([]string, 0, t.NumField())
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		if unicode.IsLower(rune(f.Name[0])) {
			continue
		}

		t := f.Tag.Get(tp.TagName)
		if t != "" && t != "-" {
			pre = append(pre, t)
		}
		// TODO: mongodb 没有 bson:",inline" 的情况还是会有 tag
		if t == "" && !f.Anonymous {
			// gorm 没有 tag 时按 gorm 字段默认规则导出
			// 其他类型的 tag 按照默认字段导出
			if tp.TagName == "gorm" {
				gormTagName := "column:" + gorm.ToColumnName(f.Name)
				pre = append(pre, gormTagName)
			} else {
				pre = append(pre, f.Name)
			}
		}
	}

	res := make([]string, 0, len(pre))
	appendNotEmpty := func(key string) {
		if key != "" {
			res = append(res, key)
		}
	}

	switch tp.TagName {
	case "gorm":
		// 解析 tags
		tags := make([]map[string]string, len(pre))
		for i := range pre {
			parts := strings.Split(pre[i], ";")
			m := make(map[string]string, len(parts))
			for j := range parts {
				gormTag := strings.Split(parts[j]+":", ":") // 保证每部分至少可以分割成两部分
				m[gormTag[0]] = gormTag[1]
			}
			tags[i] = m
		}
		// 开始处理 tags
		if option == "pk" {
			idExists := false
			for i := range tags {
				if _, ok := tags[i]["primary_key"]; ok {
					appendNotEmpty(tags[i]["column"])
					continue
				}
				if tags[i]["column"] == "id" {
					idExists = true
				}
			}
			// NOTICE: 额外的 primary_key 会覆盖默认 id 的主键
			if len(res) == 0 && idExists {
				appendNotEmpty("id")
			}
			return res
		}
		for i := range tags {
			if key, ok := tags[i]["column"]; ok {
				res = append(res, key)
			}
		}
		return res
	default: // bson, form, json, yaml 等
		if option == "omitempty" {
			for i := range pre {
				if strings.Contains(pre[i], ",omitempty") { // 防止 tag 就是 omitempty
					appendNotEmpty(strings.Split(pre[i], ",")[0])
				}
			}
			return res
		}
		for i := range pre {
			appendNotEmpty(strings.Split(pre[i], ",")[0])
		}
		return res
	}
}

// Name name
func (tp TagParser) Name() string {
	return tp.TagName
}
