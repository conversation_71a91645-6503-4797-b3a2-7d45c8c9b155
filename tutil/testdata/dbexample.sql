-- sql example
CREATE TABLE IF NOT EXISTS `mowangskuser` (
  `id` bigint(12) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`username` varchar(120) NOT NULL DEFAULT '' COMMENT 'username'
);

INSERT INTO `mowangskuser` (`id`, `username`) VALUES (10, 'bless');

CREATE TABLE IF NOT EXISTS `foo` (
  `id` bigint(12) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`field_1` varchar(120) NOT NULL DEFAULT ''
  ,`field_2` varchar(120) NOT NULL DEFAULT ''
  , PRIMARY KEY (id)
  ,KEY 'idx_field1_field2' (field_1, field_2)
  ,INDEX 'idx_field2' ('field_1')
  ,UNIQUE KEY 'uk_field2' ('field_1')
) ENGINE=InnoDB CHARACTER SET=utf8 COLLATE=utf8_general_ci;
