//go:build cgo
// +build cgo

package tutil

import (
	"math"
	"path"
	"runtime"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestQueryFixPK(t *testing.T) {
	assert := assert.New(t)
	s := "CREATE TABLE `live_image_review` (\n`id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY\n);"
	after := queryFixPK(s)
	assert.Equal("CREATE TABLE `live_image_review` (\n`id` int(10) unsigned NOT NULL PRIMARY KEY AUTOINCREMENT\n);", after)
}

func TestQueryFixINT(t *testing.T) {
	assert := assert.New(t)

	s := "CREATE TABLE `live_image_review` (\n`id` int(10) unsigned NOT NULL PRIMARY KEY AUTOINCREMENT\n);"
	after := queryFixINT(s)
	assert.Equal("CREATE TABLE `live_image_review` (\n`id` INTEGER  NOT NULL PRIMARY KEY AUTOINCREMENT\n);", after)
}

func TestQueryFixCOMMENT(t *testing.T) {
	assert := assert.New(t)

	s := "... COMMENT 'test'\n);"
	after := queryFixCOMMENT(s)
	assert.Equal("... \n);", after)

	s = "... COMMENT \"test\"\n);"
	after = queryFixCOMMENT(s)
	assert.Equal("... \n);", after)

	s = "... COMMENT \"t'es't\"\n);"
	after = queryFixCOMMENT(s)
	assert.Equal("... \n);", after)

	s = "... COMMENT 't\"es\"t'\n);"
	after = queryFixCOMMENT(s)
	assert.Equal("... \n);", after)

	s = "... COMMENT 't\"es\\'t'\n);"
	after = queryFixCOMMENT(s)
	assert.Equal("... \n);", after)
}

func TestQueryFixCREATETABLE(t *testing.T) {
	assert := assert.New(t)

	// idx 测试
	s := "CREATE TABLE ...(...\n  ,KEY idx_a_b (`a`, `b`)\n  ..."
	after := queryFixCREATETABLE(s)
	assert.Equal("CREATE TABLE ...(...\n  \n  ...", after)
	s = "CREATE TABLE ...(\n  ,UNIQUE qk_a_b (`a`, `b`)\n  ..."
	after = queryFixCREATETABLE(s)
	assert.Equal("CREATE TABLE ...(\n  \n  ...", after)
	s = "CREATE TABLE ...(\n  ,INDEX qk_a_b (a, b)\n  ..."
	after = queryFixCREATETABLE(s)
	assert.Equal("CREATE TABLE ...(\n  \n  ...", after)

	// PRIMARY 测试
	s = "CREATE TABLE `live_image_review` (\n" +
		"`id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\n" +
		",PRIMARY KEY (id)\n" +
		");"
	after = queryFixCREATETABLE(s)
	assert.Equal("CREATE TABLE `live_image_review` (\n`id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\n\n);", after)

	// 测试 table ENGINE
	s = "CREATE TABLE ... (...) ENGINE=InnoDB DEFAULT CHARSET=utf8;"
	after = queryFixCREATETABLE(s)
	assert.Equal("CREATE TABLE ... (...);", after)
}

func TestNewSqlite(t *testing.T) {
	require := require.New(t)

	conf := &ConfigSqlite{
		DBFile: "testdata/test.db",
	}
	db := NewSqlite(conf, nil)
	require.NotNil(db)

	conf.QueryFile = "testdata/dbexample.sql"
	db = NewSqlite(conf, db)
	require.NotNil(db)
}

func TestNewSqlite2(t *testing.T) {
	require := require.New(t)

	conf := &ConfigSqlite{
		DBFile: "testdata/test_v2.db",
	}
	db := NewSqlite2(conf)
	require.NotNil(db)
	sqlDB, err := db.DB()
	require.NoError(err)
	require.NoError(sqlDB.Close())

	conf.QueryFile = "testdata/dbexample.sql"
	db = NewSqlite2(conf)
	require.NotNil(db)
}

func TestReadQueries(t *testing.T) {
	assert := assert.New(t)

	assert.NotEmpty(ReadQueries("testdata/dbexample.sql"))
}

func TestSqliteUDF(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	db := NewSqlite(&ConfigSqlite{
		DBFile:    "testdata/test2.db",
		QueryFile: "testdata/dbexample.sql",
	}, nil)

	type UDFVal struct {
		CeilVal           int     `gorm:"column:ceil_val"`
		FloorVal          int     `gorm:"column:floor_val"`
		IfLeftVal         string  `gorm:"column:if_left_val"`
		IfRightVal        string  `gorm:"column:if_right_val"`
		RandVal           float64 `gorm:"column:rand_val"`
		EXPVal1           float64 `gorm:"column:exp_val1"`
		EXPVal2           float64 `gorm:"column:exp_val2"`
		ConcatVal         string  `gorm:"column:concat_val"`
		ConcatWSVal       string  `gorm:"column:concat_ws_val"`
		Greatest          string  `gorm:"column:greatest_val"`
		Least             string  `gorm:"column:least_val"`
		FromUnixtimeVal   string  `gorm:"column:from_unixtime_val"`
		UnixTimestampVal1 int64   `gorm:"column:unix_timestamp_val1"`
		UnixTimestampVal2 int64   `gorm:"column:unix_timestamp_val2"`
		UnixTimestampVal3 int64   `gorm:"column:unix_timestamp_val3"`
		UnixTimestampVal4 int64   `gorm:"column:unix_timestamp_val4"`
		UnixTimestampVal5 int64   `gorm:"column:unix_timestamp_val5"`
		STRVal            string  `gorm:"column:str_val"`
	}
	var val UDFVal
	err := db.Debug().
		Select(
			"Ceil(-2.3) AS ceil_val, " +
				"FLOOR(-2.3) AS floor_val, " +
				"IF(1 > -3, 99, 'B') AS if_left_val, " +
				"IF(1 > 3, 99, 'B') AS if_right_val, " +
				"RAND() AS rand_val, " +
				"EXP(1.0) as exp_val1, " +
				"EXP(1.2) as exp_val2, " +
				"CONCAT('A', 'B', 'C') AS concat_val, " +
				"CONCAT_WS('|', 'A', 'B') AS concat_ws_val, " +
				"GREATEST('|', 100, 'B') AS greatest_val, " +
				"LEAST('|', 100, 'B') AS least_val, " +
				"FROM_UNIXTIME(1614666035, '%Y-%m-%d %H:%i:%s %T') AS from_unixtime_val, " +
				"UNIX_TIMESTAMP() AS unix_timestamp_val1, " +
				"UNIX_TIMESTAMP('2021-03-02') AS unix_timestamp_val2, " +
				"UNIX_TIMESTAMP('2021-03-02 14') AS unix_timestamp_val3, " +
				"UNIX_TIMESTAMP('2021-03-02 14:25') AS unix_timestamp_val4, " +
				"UNIX_TIMESTAMP('2021-03-02 14:25:38') AS unix_timestamp_val5, " +
				`STR('A\nB') as str_val`).
		Table("mowangskuser").First(&val).Error
	require.NoError(err)
	assert.Equal(-2, val.CeilVal)
	assert.Equal(-3, val.FloorVal)
	assert.Equal("99", val.IfLeftVal)
	assert.Equal("B", val.IfRightVal)
	assert.Greater(val.RandVal, float64(0))
	assert.Less(val.RandVal, float64(1))
	assert.Equal(math.Pow(math.E, 1), val.EXPVal1)
	assert.Equal(math.Pow(math.E, 1.2), val.EXPVal2)
	assert.Equal("ABC", val.ConcatVal)
	assert.Equal("A|B", val.ConcatWSVal)
	assert.Equal("|", val.Greatest)
	assert.Equal("100", val.Least)
	assert.Equal("2021-03-02 14:20:35 14:20:35", val.FromUnixtimeVal)
	nowStamp := time.Now().Unix() //nolint:forbidigo
	assert.LessOrEqual(val.UnixTimestampVal1, nowStamp)
	assert.GreaterOrEqual(val.UnixTimestampVal1, nowStamp-30)
	assert.Equal(int64(1614614400), val.UnixTimestampVal2)
	assert.Equal(int64(1614664800), val.UnixTimestampVal3)
	assert.Equal(int64(1614666300), val.UnixTimestampVal4)
	assert.Equal(int64(1614666338), val.UnixTimestampVal5)
	assert.Equal("A\nB", val.STRVal)

	t.Run("TestSqliteUDFGreatestLeast", func(t *testing.T) {
		var compareUDF struct {
			HasString  string  `gorm:"has_string"`
			HasFloat64 float64 `gorm:"has_float64"`
			OnlyInt64  int64   `gorm:"only_int64"`
		}
		// LEAST
		err := db.Select("LEAST(15, '100', 20) AS has_string, " +
			"LEAST(15.5, 100, 20) AS has_float64, " +
			"LEAST(15, 100, 20) AS only_int64").Table("mowangskuser").Find(&compareUDF).Error
		require.NoError(err)
		assert.Equal("100", compareUDF.HasString)
		assert.Equal(float64(15.5), compareUDF.HasFloat64)
		assert.Equal(int64(15), compareUDF.OnlyInt64)

		err = db.Select("LEAST() AS has_string").Table("mowangskuser").Find(&compareUDF).Error
		assert.EqualError(err, "1582 - Incorrect parameter count in the call to native function 'LEAST'")

		// GREATEST
		err = db.Select("GREATEST(150, '2', 15) AS has_string, " +
			"GREATEST(150.5, 100, 20) AS has_float64, " +
			"GREATEST(150, 100, 20) AS only_int64").Table("mowangskuser").Find(&compareUDF).Error
		require.NoError(err)
		assert.Equal("2", compareUDF.HasString)
		assert.Equal(float64(150.5), compareUDF.HasFloat64)
		assert.Equal(int64(150), compareUDF.OnlyInt64)

		err = db.Select("GREATEST() AS has_string").Table("mowangskuser").Find(&compareUDF).Error
		assert.EqualError(err, "1582 - Incorrect parameter count in the call to native function 'GREATEST'")
	})

	t.Run("TestSqliteUDFFindInSet", func(t *testing.T) {
		var findInSetUDF struct {
			HasString string `gorm:"has_string"`
		}
		err := db.Select("FIND_IN_SET('A', 'A,B,C') AS has_string").Table("mowangskuser").Find(&findInSetUDF).Error
		require.NoError(err)
		assert.Equal("1", findInSetUDF.HasString)
	})
}

func TestSqlite2UDF(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	db := NewSqlite2(&ConfigSqlite{
		DBFile:    "testdata/test2_v2.db",
		QueryFile: "testdata/dbexample.sql",
	})
	defer func() {
		sqlDB, _ := db.DB()
		_ = sqlDB.Close()
	}()

	type UDFVal struct {
		CeilVal           int     `gorm:"column:ceil_val"`
		FloorVal          int     `gorm:"column:floor_val"`
		IfLeftVal         string  `gorm:"column:if_left_val"`
		IfRightVal        string  `gorm:"column:if_right_val"`
		RandVal           float64 `gorm:"column:rand_val"`
		EXPVal1           float64 `gorm:"column:exp_val1"`
		EXPVal2           float64 `gorm:"column:exp_val2"`
		ConcatVal         string  `gorm:"column:concat_val"`
		ConcatWSVal       string  `gorm:"column:concat_ws_val"`
		Greatest          string  `gorm:"column:greatest_val"`
		Least             string  `gorm:"column:least_val"`
		FromUnixtimeVal   string  `gorm:"column:from_unixtime_val"`
		UnixTimestampVal1 int64   `gorm:"column:unix_timestamp_val1"`
		UnixTimestampVal2 int64   `gorm:"column:unix_timestamp_val2"`
		UnixTimestampVal3 int64   `gorm:"column:unix_timestamp_val3"`
		UnixTimestampVal4 int64   `gorm:"column:unix_timestamp_val4"`
		UnixTimestampVal5 int64   `gorm:"column:unix_timestamp_val5"`
		STRVal            string  `gorm:"column:str_val"`
	}
	var val UDFVal
	err := db.Debug().
		Select(
			"Ceil(-2.3) AS ceil_val, " +
				"FLOOR(-2.3) AS floor_val, " +
				"IF(1 > -3, 99, 'B') AS if_left_val, " +
				"IF(1 > 3, 99, 'B') AS if_right_val, " +
				"RAND() AS rand_val, " +
				"EXP(1.0) as exp_val1, " +
				"EXP(1.2) as exp_val2, " +
				"CONCAT('A', 'B', 'C') AS concat_val, " +
				"CONCAT_WS('|', 'A', 'B') AS concat_ws_val, " +
				"GREATEST('|', 100, 'B') AS greatest_val, " +
				"LEAST('|', 100, 'B') AS least_val, " +
				"FROM_UNIXTIME(1614666035, '%Y-%m-%d %H:%i:%s %T') AS from_unixtime_val, " +
				"UNIX_TIMESTAMP() AS unix_timestamp_val1, " +
				"UNIX_TIMESTAMP('2021-03-02') AS unix_timestamp_val2, " +
				"UNIX_TIMESTAMP('2021-03-02 14') AS unix_timestamp_val3, " +
				"UNIX_TIMESTAMP('2021-03-02 14:25') AS unix_timestamp_val4, " +
				"UNIX_TIMESTAMP('2021-03-02 14:25:38') AS unix_timestamp_val5, " +
				`STR('A\nB') as str_val`).
		Table("mowangskuser").Take(&val).Error
	require.NoError(err)
	assert.Equal(-2, val.CeilVal)
	assert.Equal(-3, val.FloorVal)
	assert.Equal("99", val.IfLeftVal)
	assert.Equal("B", val.IfRightVal)
	assert.Greater(val.RandVal, float64(0))
	assert.Less(val.RandVal, float64(1))
	assert.Equal(math.Pow(math.E, 1), val.EXPVal1)
	assert.Equal(math.Pow(math.E, 1.2), val.EXPVal2)
	assert.Equal("ABC", val.ConcatVal)
	assert.Equal("A|B", val.ConcatWSVal)
	assert.Equal("|", val.Greatest)
	assert.Equal("100", val.Least)
	assert.Equal("2021-03-02 14:20:35 14:20:35", val.FromUnixtimeVal)
	nowStamp := time.Now().Unix() //nolint:forbidigo
	assert.LessOrEqual(val.UnixTimestampVal1, nowStamp)
	assert.GreaterOrEqual(val.UnixTimestampVal1, nowStamp-30)
	assert.Equal(int64(1614614400), val.UnixTimestampVal2)
	assert.Equal(int64(1614664800), val.UnixTimestampVal3)
	assert.Equal(int64(1614666300), val.UnixTimestampVal4)
	assert.Equal(int64(1614666338), val.UnixTimestampVal5)
	assert.Equal("A\nB", val.STRVal)

	t.Run("TestSqlite2UDFGreatestLeast", func(t *testing.T) {
		var compareUDF struct {
			HasString  string  `gorm:"has_string"`
			HasFloat64 float64 `gorm:"has_float64"`
			OnlyInt64  int64   `gorm:"only_int64"`
		}
		// LEAST
		err := db.Select("LEAST(15, '100', 20) AS has_string, " +
			"LEAST(15.5, 100, 20) AS has_float64, " +
			"LEAST(15, 100, 20) AS only_int64").Table("mowangskuser").Find(&compareUDF).Error
		require.NoError(err)
		assert.Equal("100", compareUDF.HasString)
		assert.Equal(float64(15.5), compareUDF.HasFloat64)
		assert.Equal(int64(15), compareUDF.OnlyInt64)

		err = db.Select("LEAST() AS has_string").Table("mowangskuser").Find(&compareUDF).Error
		assert.EqualError(err, "1582 - Incorrect parameter count in the call to native function 'LEAST'")

		// GREATEST
		err = db.Select("GREATEST(150, '2', 15) AS has_string, " +
			"GREATEST(150.5, 100, 20) AS has_float64, " +
			"GREATEST(150, 100, 20) AS only_int64").Table("mowangskuser").Find(&compareUDF).Error
		require.NoError(err)
		assert.Equal("2", compareUDF.HasString)
		assert.Equal(float64(150.5), compareUDF.HasFloat64)
		assert.Equal(int64(150), compareUDF.OnlyInt64)

		err = db.Select("GREATEST() AS has_string").Table("mowangskuser").Find(&compareUDF).Error
		assert.EqualError(err, "1582 - Incorrect parameter count in the call to native function 'GREATEST'")
	})

	t.Run("TestSqlite2UDFFindInSet", func(t *testing.T) {
		var findInSetUDF struct {
			HasString string `gorm:"has_string"`
		}
		err := db.Select("FIND_IN_SET('A', 'A,B,C') AS has_string").Table("mowangskuser").Find(&findInSetUDF).Error
		require.NoError(err)
		assert.Equal("1", findInSetUDF.HasString)
	})
}

func TestSqliteUDAF(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	db := NewSqlite(&ConfigSqlite{
		DBFile:    "testdata/test3.db",
		QueryFile: "testdata/dbexample.sql",
	}, nil)

	err := db.Exec("INSERT INTO foo(field_1, field_2)" +
		"VALUES" +
		"('A', '报幕')," +
		"('A', '导演')," +
		"('A', '协役')," +
		"('B', '报幕')," +
		"('B', '旁白')," +
		"('C', '助手')," +
		"('C', '路人');",
	).Error
	require.NoError(err)

	type UDAFVal struct {
		Field1         string `gorm:"column:field_1"`
		GroupConcatVal string `gorm:"column:group_concat_val"`
	}
	var val []*UDAFVal
	err = db.Table("foo").
		Select("field_1, GROUP_CONCAT(field_2) AS group_concat_val").
		Group("field_1").Order("field_1").Scan(&val).Error
	require.NoError(err)
	assert.Len(val, 3)
	assert.Equal("A", val[0].Field1)
	assert.Equal("报幕/导演/协役", val[0].GroupConcatVal)
	assert.Equal("B", val[1].Field1)
	assert.Equal("报幕/旁白", val[1].GroupConcatVal)
	assert.Equal("C", val[2].Field1)
	assert.Equal("助手/路人", val[2].GroupConcatVal)
}

func TestGetSplitedTableSchema(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, file, _, _ := runtime.Caller(0)
	queryLogFile := path.Dir(file) + "/../testdata/test_missevan_log.sql"
	conf := &ConfigSqlite{
		DBFile:    "log.db",
		QueryFile: queryLogFile,
	}
	db := NewSqlite(conf, nil)
	s, err := GetSplitedTableSchema(db, "collection", "_flag")
	require.NoError(err)
	excepted :=
		"CREATE TABLE  IF NOT EXISTS `collection_flag` (\n" +
			"  `id` INTEGER NOT NULL\n" +
			"  ,`name` varchar(45) DEFAULT NULL\n" +
			"  ,`create_time` INTEGER DEFAULT NULL\n" +
			")"
	assert.Equal(excepted, s)
}
