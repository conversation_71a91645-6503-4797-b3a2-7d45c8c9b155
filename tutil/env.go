//go:build !release
// +build !release

package tutil

import (
	"fmt"
	"go/build"
	"os"
	"runtime"
	"strings"
)

var debug bool

func init() {
	for _, arg := range os.Args {
		if arg == "-test.v=true" {
			debug = true
			break
		}
	}
}

// PkgPath package path
// 匹配 MiaoSiLa 下的项目
func PkgPath() string {
	_, f, _, _ := runtime.Caller(1)
	a := strings.Split(f, "MiaoSiLa/")
	f = a[len(a)-1]
	index := strings.LastIndex(f, "/")
	return f[0:index]
}

// Debug debug 信息
func Debug(a ...interface{}) {
	if debug {
		fmt.Println(a...) //nolint:forbidigo
	}
}

// Debugf debug 信息
func Debugf(format string, a ...interface{}) {
	if debug {
		fmt.Println(fmt.Sprintf(format, a...)) //nolint:forbidigo
	}
}

// CGOEnalbed cgo 是否开启
func CGOEnalbed() bool {
	return build.Default.CgoEnabled
}
