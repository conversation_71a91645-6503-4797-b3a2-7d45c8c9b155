// +build !release

package tutil

import (
	"errors"
	"time"

	"github.com/go-redis/redis/v7"
)

// PubSub redis PubSUB
type PubSub struct {
	*redis.PubSub
	ch      <-chan *redis.Message
	Timeout time.Duration
}

// NewPubSub new PubSub
// timeout[0] 是收消息超时时间
func NewPubSub(pubSub *redis.PubSub, timeout ...time.Duration) *PubSub {
	ps := &PubSub{
		PubSub: pubSub,
		ch:     pubSub.Channel(), // 调用了 ps.Channel() 才正式建立 PubSub
	}
	if len(timeout) > 0 {
		ps.Timeout = timeout[0]
	} else {
		ps.Timeout = time.Second
	}
	// 等待一段时间保证订阅成功先于后续的 PUBLISH
	time.Sleep(100 * time.Millisecond)
	return ps
}

// Receive 收取一条消息
func (ps *PubSub) Receive() string {
	timer := time.NewTimer(ps.Timeout)
	for {
		select {
		case m := <-ps.ch:
			return m.Payload
		case <-timer.C:
			return ""
		}
	}
}

// ReceiveMany 收取多条消息
func (ps *PubSub) ReceiveMany(n int) []string {
	res := make([]string, 0, n)
	timer := time.NewTimer(ps.Timeout)
	for i := 0; i < n; i++ {
		select {
		case m := <-ps.ch:
			res = append(res, m.Payload)
		case <-timer.C:
			return res
		}
	}
	return res
}

// ReceiveUntil 收取消息直到 f 返回 true
func (ps *PubSub) ReceiveUntil(f func(payload string) (bool, error)) error {
	timer := time.NewTimer(ps.Timeout)
	for {
		select {
		case m := <-ps.ch:
			Debug(m.String())
			ok, err := f(m.Payload)
			if err != nil {
				return err
			}
			if ok {
				return nil
			}
		case <-timer.C:
			return errors.New("timeout")
		}
	}
}
