package tutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMapStringKeyExists(t *testing.T) {
	assert := assert.New(t)
	testMap := map[string]int{"aa": 1, "bb": 2}

	assert.Empty(KeyExists(MapString, testMap, "aa", "bb"))
	assert.Equal([]string{"aa"}, KeyExists(MapString, testMap, "bb"))
	assert.Equal([]string{"cc"}, KeyExists(MapString, testMap, "aa", "bb", "cc"))
}

func TestNewKeyChecker(t *testing.T) {
	kc := NewKeyChecker(t, JSON)
	kc.Check(testStruct{}, "a", "c", "S")
	kc.CheckOmitEmpty(testStruct{}, "c")
	kc = NewKeyChecker(t, GORM)
	kc.Check(testStruct{}, "a", "b", "c", "s")
}

func TestMapStringName(t *testing.T) {
	assert.Equal(t, "map[string]*", MapString.Name())
}
