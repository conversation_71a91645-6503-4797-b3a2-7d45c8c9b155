package tutil

import (
	"errors"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestPubSub(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	client := redis.NewClient(&redis.Options{
		Addr:     "redis.srv.maoer.co:6379",
		Password: "",
		DB:       3,
		PoolSize: 10,
	})

	_, err := client.Ping().Result()
	require.NoError(err)
	key := "missevan-go/tutil"
	pubSub := client.PSubscribe(key)
	defer pubSub.Close()

	var ps *PubSub
	t.Run("NewPubSub", func(t *testing.T) {
		// 测试 PubSub
		ps = NewPubSub(pubSub)
		assert.Equal(time.Second, ps.Timeout)
		assert.NotNil(ps.ch)
		ps = NewPubSub(pubSub, 100*time.Millisecond)
		assert.Equal(100*time.Millisecond, ps.Timeout)
	})
	require.NotNil(ps)
	t.Run("Receive", func(t *testing.T) {
		payload := "TestReceive"
		require.NoError(client.Publish(key, payload).Err())
		assert.Equal(payload, ps.Receive())
		before := util.TimeNow()
		assert.Empty(ps.Receive())
		after := util.TimeNow()
		assert.True(after.After(before.Add(ps.Timeout)))
	})
	t.Run("ReceiveMany", func(t *testing.T) {
		var payloads [10]string
		for i := 0; i < len(payloads); i++ {
			payloads[i] = strconv.Itoa(i)
			require.NoError(client.Publish(key, payloads[i]).Err())
		}
		assert.Equal(payloads[:4], ps.ReceiveMany(4))
		before := util.TimeNow()
		assert.Equal(payloads[4:], ps.ReceiveMany(10))
		after := util.TimeNow()
		assert.True(after.After(before.Add(ps.Timeout)))
	})
	t.Run("ReceiveUntil", func(t *testing.T) {
		var count int
		f := func(payload string) (bool, error) {
			count++
			switch payload {
			case "true":
				return true, nil
			case "error":
				return false, errors.New("return error")
			default:
				return false, nil
			}
		}
		require.NoError(client.Publish(key, "false").Err())
		require.NoError(client.Publish(key, "true").Err())
		require.NoError(client.Publish(key, "error").Err())
		assert.NoError(ps.ReceiveUntil(f))
		assert.Equal(2, count)
		assert.EqualError(ps.ReceiveUntil(f), "return error")
		assert.Equal(3, count)
		before := util.TimeNow()
		assert.EqualError(ps.ReceiveUntil(f), "timeout")
		after := util.TimeNow()
		assert.True(after.After(before.Add(ps.Timeout)))
	})
}
