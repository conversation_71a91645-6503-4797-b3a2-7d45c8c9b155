//go:build cgo && !release
// +build cgo,!release

package tutil

import (
	"database/sql"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"math/rand"
	"os"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/mattn/go-sqlite3"
	"gorm.io/driver/sqlite"
	gorm2 "gorm.io/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
)

// sqliteUDFCeil: CEIL 函数，向上取整
func sqliteUDFCeil(num float64) int64 {
	return int64(math.Ceil(num))
}

// sqliteUDFFloor: FLOOR 函数，向下取整
func sqliteUDFFloor(num float64) int64 {
	return int64(math.Floor(num))
}

// sqliteUDFRand: RAND 函数，取 0~1 之间的随机数
func sqliteUDFRand() float64 {
	return rand.Float64()
}

// sqliteUDFEXP: EXP 函数，取自然常数 e 的 exp 次幂
func sqliteUDFEXP(exp float64) (float64, error) {
	return math.Pow(math.E, exp), nil
}

// sqliteUDFIf: IF 函数，如果表达式为真取左值，否则取右值
func sqliteUDFIf(expr bool, val1 interface{}, val2 interface{}) string {
	if expr {
		return fmt.Sprintf("%v", val1)
	}

	return fmt.Sprintf("%v", val2)
}

// sqliteUDFConcat: CONCAT 函数，实现字符串/数字拼接
func sqliteUDFConcat(vals ...interface{}) string {
	return sqliteUDFConcatWs("", vals...)
}

// sqliteUDFConcatWs: CONCAT_WS 函数，以指定分隔符实现字符串/数字拼接
func sqliteUDFConcatWs(sep string, vals ...interface{}) string {
	var args []string
	for _, val := range vals {
		args = append(args, fmt.Sprintf("%v", val))
	}
	return strings.Join(args, sep)
}

func sqliteCompareFlag(vals []interface{}) (hasString, hasFloat64 bool) {
	for i := range vals {
		// 注册的函数入参是 interface 的时候只需要支持四种格式
		// interface{} arguments are given the direct translation
		// of the SQLite data type: int64 for INTEGER, float64 for FLOAT,
		// []byte for BLOB, string for TEXT.
		switch vals[i].(type) {
		case string:
			hasString = true
		case []byte:
			hasString = true
		case int64:
		case float64:
			hasFloat64 = true
		default:
			panic(fmt.Sprintf("待确认的类型：%v", reflect.TypeOf(vals[i])))
		}
	}
	return
}

func sqliteNumToFloat64(num interface{}) float64 {
	switch v := num.(type) {
	case float64:
		return v
	case int64:
		return float64(v)
	default:
		panic("类型不是 sqlite 数字类型")
	}
}

// sqliteUDFGreatest: GREATEST 函数，取大值
func sqliteUDFGreatest(vals ...interface{}) (string, error) {
	if len(vals) < 2 {
		return "", errors.New("1582 - Incorrect parameter count in the call to native function 'GREATEST'")
	}
	// 检查入参类型
	hasString, hasFloat64 := sqliteCompareFlag(vals)
	if hasString {
		ret := fmt.Sprint(vals[0])
		for i := 1; i < len(vals); i++ {
			tmp := fmt.Sprint(vals[i])
			if tmp > ret {
				ret = tmp
			}
		}
		return ret, nil
	}
	// 有 float64 则转换成 float64 比较
	if hasFloat64 {
		ret := sqliteNumToFloat64(vals[0])
		for i := 1; i < len(vals); i++ {
			tmp := sqliteNumToFloat64(vals[i])
			if tmp > ret {
				ret = tmp
			}
		}
		return fmt.Sprint(ret), nil
	}
	ret := vals[0].(int64)
	for i := 1; i < len(vals); i++ {
		tmp := vals[i].(int64)
		if tmp > ret {
			ret = tmp
		}
	}
	return fmt.Sprint(ret), nil
}

// sqliteUDFLeast: LEAST 函数，取小值
func sqliteUDFLeast(vals ...interface{}) (string, error) {
	if len(vals) < 2 {
		return "", errors.New("1582 - Incorrect parameter count in the call to native function 'LEAST'")
	}
	// 检查入参类型
	hasString, hasFloat64 := sqliteCompareFlag(vals)
	if hasString {
		ret := fmt.Sprint(vals[0])
		for i := 1; i < len(vals); i++ {
			tmp := fmt.Sprint(vals[i])
			if tmp < ret {
				ret = tmp
			}
		}
		return ret, nil
	}
	// 有 float64 则转换成 float64 比较
	if hasFloat64 {
		ret := sqliteNumToFloat64(vals[0])
		for i := 1; i < len(vals); i++ {
			tmp := sqliteNumToFloat64(vals[i])
			if tmp < ret {
				ret = tmp
			}
		}
		return fmt.Sprint(ret), nil
	}
	ret := vals[0].(int64)
	for i := 1; i < len(vals); i++ {
		tmp := vals[i].(int64)
		if tmp < ret {
			ret = tmp
		}
	}
	return fmt.Sprint(ret), nil
}

// sqliteFromUnixtime FROM_UNIXTIME 函数，对时间戳进行格式输出
func sqliteFromUnixtime(unixTime int64, timeFormat string) string {
	if unixTime < 0 {
		return ""
	}

	var layout string
	var flag bool
	for _, s := range timeFormat {
		if s == '%' {
			flag = true
		} else {
			if flag {
				switch s {
				case 'Y':
					layout += "2006"
				case 'm':
					layout += "01"
				case 'd':
					layout += "02"
				case 'H':
					layout += "15"
				case 'i':
					layout += "04"
				case 's':
					layout += "05"
				case 'T':
					layout += "15:04:05"
				default:
					layout += string(s)
				}
				flag = false
			} else {
				layout += string(s)
			}
		}
	}

	tm := time.Unix(unixTime, 0)
	return tm.Format(layout)
}

// sqliteUDFSTR
func sqliteUDFSTR(input string) (string, error) {
	return strconv.Unquote(fmt.Sprintf(`"%s"`, input))
}

var sqliteUnixTimestampReg = regexp.MustCompile(`^([0-9]{4}).+?([0-9]{2}).+?([0-9]{2})(.+?([0-9]{2}))?(.+?([0-9]{2}))?(.+?([0-9]{2}))?`)

// sqliteUnixTimestamp UNIX_TIMESTAMP 函数，取时间戳
// 例 UNIX_TIMESTAMP(), UNIX_TIMESTAMP('2021-03-24'), UNIX_TIMESTAMP('2021-03-24 21'), UNIX_TIMESTAMP('2021-03-24 21:05'), UNIX_TIMESTAMP('2021-03-24 21:05:12')
func sqliteUnixTimestamp(timeStr ...string) int64 {
	if len(timeStr) == 0 {
		return time.Now().Unix() //nolint:forbidigo
	}
	if len(timeStr) > 1 {
		panic("Incorrect parameter count in the call to function 'UNIX_TIMESTAMP'")
	}

	ret := sqliteUnixTimestampReg.FindStringSubmatch(timeStr[0])
	if len(ret) == 0 {
		return 0
	}
	year := ret[1]
	month := ret[2]
	day := ret[3]
	hour := ret[5]
	minute := ret[7]
	second := ret[9]

	layout := "2006-01-02"
	timeVal := year + "-" + month + "-" + day
	if hour != "" {
		layout += " 15"
		timeVal += " " + hour
		if minute != "" {
			layout += ":04"
			timeVal += ":" + minute
			if second != "" {
				layout += ":05"
				timeVal += ":" + second
			}
		}
	}
	tm, err := time.ParseInLocation(layout, timeVal, time.Local)
	if err != nil {
		log.Fatal(err)
	}

	return tm.Unix()
}

// sqliteUDAFGroupConcat: 自定义聚合函数 GROUP_CONCAT，以 "/" 为分隔符
// 暂时无法像 MySQL GROUP_CONCAT(<column> SEPARATOR <sep>) 函数自定义分隔符
func sqliteUDAFGroupConcat() *groupConcat {
	return &groupConcat{}
}

type groupConcat struct {
	ctx      string
	rowCount int64
}

func (gc *groupConcat) Step(val string) {
	if gc.rowCount == 0 {
		gc.ctx = val
	} else {
		gc.ctx = gc.ctx + "/" + val
	}
	gc.rowCount++
}

func (gc *groupConcat) Done() string {
	return gc.ctx
}

func sqliteUDFFindInSet(find string, set string) bool {
	arr := strings.Split(set, ",")
	// TODO: golang 版本更新到 v1.21 后，可以直接使用 slices 包 Contains() 函数
	for _, v := range arr {
		if v == find {
			return true
		}
	}

	return false
}

const sqliteExtDriver = "sqlite_ext"

var sqliteExtOnce sync.Once

// SqliteExt 注册了 UDF 的 SQLite 实例
func SqliteExt(dbFile string) (*sql.DB, error) {
	// https://github.com/mattn/go-sqlite3/pull/229/files
	sqliteExtOnce.Do(func() {
		sql.Register(sqliteExtDriver, &sqlite3.SQLiteDriver{
			ConnectHook: func(conn *sqlite3.SQLiteConn) error {
				if err := conn.RegisterFunc("CEIL", sqliteUDFCeil, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("FLOOR", sqliteUDFFloor, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("RAND", sqliteUDFRand, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("EXP", sqliteUDFEXP, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("IF", sqliteUDFIf, true); err != nil {
					return err
				}
				if err := conn.RegisterFunc("CONCAT", sqliteUDFConcat, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("CONCAT_WS", sqliteUDFConcatWs, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("GREATEST", sqliteUDFGreatest, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("LEAST", sqliteUDFLeast, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("FROM_UNIXTIME", sqliteFromUnixtime, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("UNIX_TIMESTAMP", sqliteUnixTimestamp, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("STR", sqliteUDFSTR, false); err != nil {
					return err
				}
				if err := conn.RegisterFunc("FIND_IN_SET", sqliteUDFFindInSet, true); err != nil {
					return err
				}

				if err := conn.RegisterAggregator("GROUP_CONCAT", sqliteUDAFGroupConcat, true); err != nil {
					return err
				}
				return nil
			},
		})
	})
	// DSN 参数说明: https://github.com/mattn/go-sqlite3/blob/1157a4212dcb650962563f67cd405794e9115b45/sqlite3.go#L908-L1010
	return sql.Open(sqliteExtDriver, fmt.Sprintf("file:%s?cache=shared&_loc=auto", dbFile))
}

// NewSqlite new sqlite Database
func NewSqlite(conf *ConfigSqlite, oldDB *gorm.DB) *gorm.DB {
	if oldDB != nil {
		oldDB.Close()
	}

	if !conf.KeepOldDB {
		_ = os.Remove(conf.DBFile)
	}

	sqliteExtDB, err := SqliteExt(conf.DBFile)
	if err != nil {
		log.Fatalf("init sqlite error: %v, db file: %s", err, conf.DBFile)
	}
	// https://github.com/go-gorm/gorm/issues/1085
	db, err := gorm.Open("sqlite3", sqliteExtDB)
	if err != nil {
		log.Fatalf("gorm error: %v, db file: %s", err, conf.DBFile)
	}
	if conf.QueryFile == "" {
		return db
	}
	queries, err := ReadQueries(conf.QueryFile)
	if err != nil {
		log.Fatalf("read query error: %v, query file: %s", err, conf.QueryFile)
	}
	_, err = db.DB().Exec(queries)
	if err != nil {
		log.Fatalf("exec error: %v, query file: %s", err, conf.QueryFile)
	}
	return db
}

// NewSqlite2 new sqlite Database via gorm2
func NewSqlite2(conf *ConfigSqlite) *gorm2.DB {
	if !conf.KeepOldDB {
		_ = os.Remove(conf.DBFile)
	}

	sqliteExtDB, err := SqliteExt(conf.DBFile)
	if err != nil {
		log.Fatalf("init sqlite error: %v, db file: %s", err, conf.DBFile)
	}
	db, err := gorm2.Open(sqlite.Dialector{Conn: sqliteExtDB}, &gorm2.Config{
		NowFunc: func() time.Time {
			return util.TimeNow()
		},
	})
	if err != nil {
		log.Fatalf("gorm2 error: %v, db file: %s", err, conf.DBFile)
	}
	if conf.QueryFile == "" {
		return db
	}
	queries, err := ReadQueries(conf.QueryFile)
	if err != nil {
		log.Fatalf("read query error: %v, query file: %s", err, conf.QueryFile)
	}
	err = db.Exec(queries).Error
	if err != nil {
		log.Fatalf("exec error: %v, query file: %s", err, conf.QueryFile)
	}
	return db
}

// ReadQueries 从文件中读取 sql queries
// queries 规则:
// 不支持添加索引
// 不支持单独一行 PRIMARY KEY (`id`)
func ReadQueries(filePath string) (string, error) {
	raw, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	// 删除 raw 中的注释
	queries := string(raw)
	queries = queryFixPK(queries)
	queries = queryFixINT(queries)
	queries = queryFixCOMMENT(queries)
	queries = queryFixCREATETABLE(queries)
	// TODO: 添加索引
	return queries, nil
}

func queryFixPK(queries string) string {
	re := regexp.MustCompile(`(?i)\sAUTO_INCREMENT\s+PRIMARY\s+KEY(\s|,)`)
	re2 := regexp.MustCompile(`(?i)(\s)(AUTO_INCREMENT\s+PRIMARY\s+KEY)(\s|,)`)
	queries = re.ReplaceAllStringFunc(queries, func(s string) string {
		parts := re2.FindStringSubmatch(s)
		parts[2] = "PRIMARY KEY AUTOINCREMENT"
		return strings.Join(parts[1:], "")
	})
	return queries
}

func queryFixINT(queries string) string {
	re := regexp.MustCompile(`(?i)\s([a-zA-Z]*INT)(\s+|\s*\(\d+\)\s+)(UNSIGNED(:?\s|,))?`)
	re2 := regexp.MustCompile(`(?i)(\s)([A-Z]*INT)(\s+|\s*\(\d+\)\s+)(UNSIGNED(:?\s|,))?`)
	queries = re.ReplaceAllStringFunc(queries, func(s string) string {
		parts := re2.FindStringSubmatch(s)
		parts[2] = "INTEGER"
		parts[3] = " "
		parts[4] = ""
		return strings.Join(parts[1:], "")
	})
	return queries
}

func queryFixCOMMENT(queries string) string {
	re := regexp.MustCompile(`(?i)COMMENT\s+'.*?[^\\]'`)
	s := re.ReplaceAllString(queries, "")
	re = regexp.MustCompile(`(?i)COMMENT\s+".*?[^\\]"`)
	s = re.ReplaceAllString(s, "")
	return s
}

func queryFixCREATETABLE(queries string) string {
	// remove 'KEY idx_appid_status (`app_id`, `status`)'
	// remove 'PRIMARY KEY (`id`)'
	re := regexp.MustCompile(`(?i)CREATE TABLE\s+.*?\([\s\S]*?,\s*(KEY|INDEX|UNIQUE|PRIMARY).*?\)`)
	re2 := regexp.MustCompile(`(?i)(CREATE TABLE\s+.*?\([\s\S]*?)(,\s*(KEY|INDEX|UNIQUE|PRIMARY).*?\))`)
	loop := true
	for loop {
		loop = false
		queries = re.ReplaceAllStringFunc(queries, func(s string) string {
			loop = true
			parts := re2.FindStringSubmatch(s)
			parts[2] = ""
			parts[3] = "" // sub match
			return strings.Join(parts[1:], "")
		})
	}

	// remove 'ENGINE=InnoDB CHARACTER SET=utf8 COLLATE=utf8_general_ci'
	re = regexp.MustCompile(`(?i)CREATE TABLE\s+.*?\([\s\S]*?\S\s*\)[^\)]*?;`)
	re2 = regexp.MustCompile(`(?i)(CREATE TABLE\s+.*?\([\s\S]*?\S\s*\))([^\)]*?)(;)`)
	queries = re.ReplaceAllStringFunc(queries, func(s string) string {
		parts := re2.FindStringSubmatch(s)
		parts[2] = ""
		return strings.Join(parts[1:], "")
	})
	return queries
}

// GetSplitedTableSchema 获取分表的 schema
func GetSplitedTableSchema(db *gorm.DB, tableName string, flag string) (schema string, err error) {
	var originalSchema string
	err = db.Select("sql").
		Table("sqlite_master").
		Where("type = ?", "table").
		Where("name = ?", tableName).
		Row().Scan(&originalSchema)
	if err != nil {
		return
	}
	schema = strings.Replace(
		originalSchema,
		fmt.Sprintf("`%s`", tableName),
		fmt.Sprintf(" IF NOT EXISTS `%s%s`", tableName, flag),
		1)

	if runtime.GOOS == "windows" {
		schema = strings.Replace(schema, "\r\n", "\n", -1)
	}

	return
}
