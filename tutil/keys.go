//go:build !release
// +build !release

package tutil

import (
	"reflect"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
)

// keyChecker
var (
	Actions ActionsParser

	BSON = TagParser{TagName: "bson"}
	GORM = TagParser{TagName: "gorm"}
	JSON = TagParser{TagName: "json"}
	YAML = TagParser{TagName: "yaml"}
	FORM = TagParser{TagName: "form"}

	MapString MapStringParser
)

type keyParser interface {
	Keys(v interface{}, option string) []string
	Name() string
}

// KeyExists 检查 v 中是否存在对应的 keys
// Deprecated: 改用 KeyChecker
func KeyExists(parser keyParser, v interface{}, keys ...string) []string {
	kp := make(keyPairs, len(keys))
	kp.add(parser.Keys(v, "")...)
	return kp.match(keys...)
}

// KeyChecker key checker
type Key<PERSON>he<PERSON> struct {
	kp     keyParser
	assert *assert.Assertions
	t      *testing.T
}

// NewKeyChecker new key checker
func NewKeyChecker(t *testing.T, kp keyParser) *KeyChecker {
	return &KeyChecker{t: t, assert: assert.New(t), kp: kp}
}

// Check check keys
func (kc KeyChecker) Check(v interface{}, keys ...string) {
	structName := reflect.TypeOf(v).Name()
	kc.assert.Emptyf(KeyExists(kc.kp, v, keys...), "checker: %s, type: %s",
		kc.kp.Name(), structName)
	if debug {
		if _, ok := kc.kp.(TagParser); ok {
			if kc.kp.Name() == "gorm" {
				kc.t.Logf("%s PRIMARY KEY: %v", structName, kc.kp.Keys(v, "pk"))
			} else {
				kc.t.Logf("%s omitempty key: %v", structName, kc.kp.Keys(v, "omitempty"))
			}
		}
	}
}

// CheckOmitEmpty 检查 omitempty 的 keys
func (kc KeyChecker) CheckOmitEmpty(v interface{}, keys ...string) {
	kp := make(keyPairs, len(keys))
	kp.add(kc.kp.Keys(v, "omitempty")...)
	kc.assert.Emptyf(kp.match(keys...), "checker: %s, type: %s",
		kc.kp.Name(), reflect.TypeOf(v).Name())
}

// ----------------------------------------------------------------------------

// keyPairs 辅助 KeyExists 用
type keyPairs map[string]int

func (kp keyPairs) add(keys ...string) {
	for i := 0; i < len(keys); i++ {
		kp[keys[i]] = 1
	}
}

func (kp keyPairs) match(keys ...string) []string {
	for i := 0; i < len(keys); i++ {
		kp[keys[i]] |= 2
	}
	res := make([]string, 0, len(kp))
	for keys, status := range kp {
		if status != 3 {
			res = append(res, keys)
		}
	}
	sort.Strings(res)
	return res
}

// MapStringParser map[string]xxx keyParser
type MapStringParser struct{}

// Keys 获取 map[string]xxx 所含  keys
// NOTICE: 未检查 v 是否合理
func (MapStringParser) Keys(v interface{}, option string) []string {
	t := reflect.ValueOf(v)
	res := make([]string, 0, t.Len())
	for _, key := range t.MapKeys() {
		res = append(res, key.String())
	}
	return res
}

// Name name
func (MapStringParser) Name() string {
	return "map[string]*"
}
