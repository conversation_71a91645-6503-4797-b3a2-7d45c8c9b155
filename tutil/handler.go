//go:build !release
// +build !release

package tutil

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ToRequestBody 让入参转换成 request 的 body
// io.Reader 不做处理
// string, []byte 直接转成响应的 io.Reader
// 其他参数 json.Marshal 之后转成 *bytes.Reader
func ToRequestBody(param interface{}) io.Reader {
	switch p := param.(type) {
	case io.Reader:
		return p
	case []byte:
		return bytes.NewReader(p)
	case string:
		return strings.NewReader(p)
	default:
		data, err := json.Marshal(p)
		if err != nil {
			panic(err)
		}
		return bytes.NewReader(data)
	}
}

// PrintJSON debug 下打印所传结构体
func PrintJSON(v interface{}) {
	if debug {
		b, err := json.MarshalIndent(v, "", "  ")
		if err != nil {
			log.Println("error:", err)
			return
		}
		fmt.Println(string(b)) //nolint:forbidigo
	}
}

// SprintJSON 返回格式化后 json 字符串
// 比如配合 assert.JSONEq 使用
// 出错了会 panic
func SprintJSON(v interface{}) string {
	b, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		panic(err)
	}
	return string(b)
}

// ActionsParser actions keyParser
type ActionsParser struct{}

// Keys 获取 handler.Handler.Actions 和 map[string]*handler.Action 的 keys
// NOTICE: 未检查 actionsOrHandler 是否合理
func (ActionsParser) Keys(v interface{}, option string) []string {
	t := reflect.ValueOf(v)
	if t.Kind() == reflect.Ptr {
		t = reflect.ValueOf(reflect.Indirect(t).Interface())
	}
	if t.Kind() == reflect.Struct {
		t = t.FieldByName("Actions")
	}
	res := make([]string, 0, t.Len())
	for _, key := range t.MapKeys() {
		res = append(res, key.String())
	}
	return res
}

// Name name
func (ActionsParser) Name() string {
	return "actions"
}

// GinHandler hander for gin
type GinHandler interface {
	Mount(*gin.Engine)
}

// RunMockServer 运行 mock 服务，port 参数为 0 则为随机选择端口
func RunMockServer(r *gin.Engine, port int, h ...GinHandler) string {
	for i := range h {
		h[i].Mount(r)
	}

	// 如果传入的参数 port 为 0，则随机分配端口
	l, err := net.Listen("tcp", net.JoinHostPort("127.0.0.1", strconv.Itoa(port)))
	if err != nil {
		panic(err)
	}

	go func() {
		err := http.Serve(l, r)
		if err != nil {
			panic(err)
		}
	}()
	time.Sleep(100 * time.Millisecond) // 防止访问先于路由运行
	return l.Addr().String()
}

// RunMockTLSServer 运行 tls mock 服务，port 参数为 0 则为随机选择端口
func RunMockTLSServer(certPath, certKeyPath string, r *gin.Engine, port int, h ...GinHandler) string {
	for i := range h {
		h[i].Mount(r)
	}

	// 如果传入的参数 port 为 0，则随机分配端口
	l, err := net.Listen("tcp", net.JoinHostPort("127.0.0.1", strconv.Itoa(port)))
	if err != nil {
		panic(err)
	}

	go func() {
		err := http.ServeTLS(l, r, certPath, certKeyPath)
		if err != nil {
			panic(err)
		}
	}()
	time.Sleep(100 * time.Millisecond) // 防止访问先于路由运行
	return l.Addr().String()
}

// RunMockServerWithHTTPHandlerFunc 运行 mock 服务（不依赖相关项目代码，避免循环引入问题）
func RunMockServerWithHTTPHandlerFunc(actions map[string]http.HandlerFunc) (mockServer *httptest.Server, cleanup func()) {
	mux := http.NewServeMux()
	mockServer = httptest.NewServer(mux)
	for route, handlerFunc := range actions {
		mux.HandleFunc(route, handlerFunc)
	}
	return mockServer, func() {
		mockServer.Close()
	}
}
