//go:build !cgo && !release
// +build !cgo,!release

package tutil

import (
	"database/sql"
	"errors"

	"github.com/jinzhu/gorm"
)

// SqliteExt 不做任何操作
func SqliteExt(dbFile string) (*sql.DB, error) {
	return nil, errors.New("not implement")
}

// NewSqlite return oldDB
func NewSqlite(conf *ConfigSqlite, oldDB *gorm.DB) *gorm.DB {
	return oldDB
}

// ReadQueries 不做任何操作
func ReadQueries(filePath string) (string, error) {
	return "", errors.New("not implement")
}
