package tutil

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"math/big"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"golang.org/x/net/http2"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestToRequestBody(t *testing.T) {
	assert := assert.New(t)

	assert.Panics(func() { ToRequestBody(func() {}) })
	reader := ToRequestBody(map[string]interface{}{"test": true})
	b, err := ioutil.ReadAll(reader)
	assert.NoError(err)
	assert.Equal(`{"test":true}`, string(b))
	reader = ToRequestBody("testtest")
	b, err = ioutil.ReadAll(reader)
	assert.NoError(err)
	assert.Equal("testtest", string(b))
	reader = ToRequestBody([]byte(`{"test":true}`))
	b, err = ioutil.ReadAll(reader)
	assert.NoError(err)
	assert.Equal(`{"test":true}`, string(b))

	reader = ToRequestBody(strings.NewReader("test"))
	assert.IsType(reader, &strings.Reader{})
}

func TestPrintJSON(t *testing.T) {
	// 只是运行下
	a := true
	PrintJSON(a)
	var b chan int
	PrintJSON(b)
}

func TestSprintJSON(t *testing.T) {
	assert := assert.New(t)
	a := map[string]int{"test": 1}
	assert.JSONEq(`{"test":1}`, SprintJSON(a))
	var b chan int
	assert.PanicsWithError("json: unsupported type: chan int", func() { SprintJSON(b) })
}

func TestActionsKeyExists(t *testing.T) {
	assert := assert.New(t)

	var h struct {
		Actions map[string]int
	}
	h.Actions = map[string]int{"aaa": 1}
	assert.Equal([]string{"aaa", "bb"}, KeyExists(Actions, h.Actions, "bb"))
	assert.Empty(KeyExists(Actions, h, "aaa"))
}

func TestRunMockServer(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := gin.Default()
	h := exampleHandler{}
	addr := RunMockServer(r, 10240, &h)
	assert.Equal("127.0.0.1:10240", addr)
	resp, err := http.Get("http://" + addr)
	require.NoError(err)
	assert.Equal(200, resp.StatusCode)

	r = gin.Default()
	h = exampleHandler{}
	addr = RunMockServer(r, 0, &h)
	resp, err = http.Get("http://" + addr)
	require.NoError(err)
	assert.Equal(200, resp.StatusCode)
}

type exampleHandler struct{}

func (exampleHandler) Mount(r *gin.Engine) {
	r.GET("/", func(c *gin.Context) {})
}

func TestActionName(t *testing.T) {
	assert.Equal(t, "actions", Actions.Name())
}

func TestRunMockWithHttpHandlerFunc(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	server, cleanup := RunMockServerWithHTTPHandlerFunc(map[string]http.HandlerFunc{
		"/ping": func(writer http.ResponseWriter, request *http.Request) {
			writer.WriteHeader(http.StatusOK)
			fmt.Fprintf(writer, `pong`)
		},
	})
	defer cleanup()

	resp, err := http.Get(server.URL + "/ping")
	require.NoError(err)
	require.Equal(http.StatusOK, resp.StatusCode)
	result, err := ioutil.ReadAll(resp.Body)
	require.NoError(err)
	assert.Equal("pong", string(result))
}

func generateCerts() error {
	certPath := "../testdata/server.crt"
	_, certPathErr := os.Stat(certPath)
	certKeyPath := "../testdata/server.pem"
	_, certKeyPathErr := os.Stat(certPath)
	if certPathErr == nil && certKeyPathErr == nil {
		return nil
	}

	// 创建 CA 私钥
	caPrivKey, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		return err
	}

	// 创建 CA 证书
	ca := &x509.Certificate{
		SerialNumber:          big.NewInt(1),
		Subject:               pkix.Name{CommonName: "test.local"},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(0, 0, 1),
		IsCA:                  true,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth, x509.ExtKeyUsageServerAuth},
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
	}
	// 生成 CA
	caBytes, err := x509.CreateCertificate(rand.Reader, ca, ca, &caPrivKey.PublicKey, caPrivKey)
	if err != nil {
		return err
	}

	caCert, err := x509.ParseCertificate(caBytes)
	if err != nil {
		return err
	}

	// 生成服务端私钥
	privKey, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		return err
	}

	// 创建服务端证书
	serverCertTemplate := &x509.Certificate{
		SerialNumber: big.NewInt(2),
		Subject: pkix.Name{
			CommonName: "test.local",
		},
		NotBefore:   time.Now(),
		NotAfter:    time.Now().AddDate(0, 3, 0),
		ExtKeyUsage: []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth, x509.ExtKeyUsageServerAuth},
		KeyUsage:    x509.KeyUsageDigitalSignature,
	}

	// 使用 CA 私钥签名并创建服务端证书
	certBytes, err := x509.CreateCertificate(rand.Reader, serverCertTemplate, caCert, &privKey.PublicKey, caPrivKey)
	if err != nil {
		return err
	}

	certPem := new(bytes.Buffer)
	pem.Encode(certPem, &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certBytes,
	})

	err = ioutil.WriteFile(certPath, certPem.Bytes(), 0644)
	if err != nil {
		return err
	}

	certPrivKeyPEM := new(bytes.Buffer)
	pem.Encode(certPrivKeyPEM, &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privKey),
	})

	err = ioutil.WriteFile(certKeyPath, certPrivKeyPEM.Bytes(), 0644)
	if err != nil {
		return err
	}

	return nil
}

func TestTimeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	certPath := "../testdata/server.crt"
	certKeyPath := "../testdata/server.pem"

	// 如果没有的话，创建测试证书
	err := generateCerts()
	require.NoError(err)

	defer func() {
		err = os.Remove(certPath)
		assert.NoError(err)
		err = os.Remove(certKeyPath)
		assert.NoError(err)
	}()

	r := gin.New()
	h := timeoutHandler{}
	addr := RunMockTLSServer(certPath, certKeyPath, r, 0, &h)

	c := &http.Client{
		Timeout: time.Second,
	}

	c.Transport = &http2.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		StrictMaxConcurrentStreams: true,
	}
	req, err := http.NewRequest(http.MethodGet, fmt.Sprintf("https://%s/normal/%d?timeout=1", addr, 0), nil)
	require.NoError(err)
	resp, err := c.Do(req)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(http.StatusOK, resp.StatusCode)

	content, err := ioutil.ReadAll(resp.Body)
	require.NoError(err)
	require.NotEmpty(content)

	var result map[string]interface{}
	err = json.Unmarshal(content, &result)
	require.NoError(err)
	assert.Zero(result["code"])
}

type timeoutHandler struct{}

func (timeoutHandler) Mount(r *gin.Engine) {
	r.GET("/normal/:name", func(c *gin.Context) {
		c.JSON(http.StatusOK, map[string]interface{}{"code": 0})
	})
}
