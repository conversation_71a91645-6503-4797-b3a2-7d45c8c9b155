//go:build !release
// +build !release

package tutil

import (
	"fmt"
	"time"
)

// PrintExecutionTime 打印函数的运行时长
// 在遇到性能问题时，可以通过该函数辅助判断问题所在
func PrintExecutionTime(f ...func() string) {
	for i := 0; i < len(f); i++ {
		before := time.Now() //nolint:forbidigo
		s := f[i]()
		d := time.Since(before)                           //nolint:forbidigo
		fmt.Printf("%s 运行时长：%dms\n", s, d.Milliseconds()) //nolint:forbidigo
	}
}
