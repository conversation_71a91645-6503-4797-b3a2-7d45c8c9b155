package web

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config"
)

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := new(config.Config)
	h := v2Handler(conf)
	assert.Equal("v2", h.Name)
	assert.Equal(2, len(h.Middlewares))
	require.Equal(1, len(h.SubHandlers))

	h = xHandler(conf)
	assert.Equal("x", h.Name)
	assert.Equal(2, len(h.Middlewares))
	require.Equal(5, len(h.SubHandlers))

	hV2 := xHandlerV2(conf)
	assert.Equal("x", hV2.Name)
	assert.Equal(1, len(hV2.Middlewares))
	require.Equal(1, len(hV2.SubHandlers))
}
