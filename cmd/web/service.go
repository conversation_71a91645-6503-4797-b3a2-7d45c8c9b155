package web

import (
	"errors"
	"math/rand"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	"github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 需要去掉不用的实例化
func initService(conf *service.Config) (err error) {
	rand.Seed(util.TimeNow().UnixNano())

	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	if conf.LogDB != (servicedb.Config{}) {
		service.LogDB, err = servicedb.InitDatabase(&conf.LogDB)
		if err != nil {
			return
		}
	}
	if conf.DramaDB != (servicedb.Config{}) {
		service.DramaDB, err = servicedb.InitDatabase(&conf.DramaDB)
		if err != nil {
			return
		}
	}
	if conf.MainDB != (servicedb.Config{}) {
		service.MainDB, err = servicedb.InitDatabase(&conf.MainDB)
		if err != nil {
			return
		}
	}
	if conf.PayDB != (servicedb.Config{}) {
		service.PayDB, err = servicedb.InitDatabase(&conf.PayDB)
		if err != nil {
			return
		}
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return
	}

	appDatabusConf, ok := conf.Databus["app_log_pub"]
	if !ok {
		return errors.New("databus config app_log_pub not found")
	}
	service.Databus.AppLogPub = databus.New(&appDatabusConf)

	liveDatabusConf, ok := conf.Databus["live_log_pub"]
	if !ok {
		return errors.New("databus config live_log_pub not found")
	}
	service.Databus.LiveLogPub = databus.New(&liveDatabusConf)

	service.Storage = storage.NewClient(conf.Storage)
	service.Upload, err = upload.NewClient(conf.Upload, service.Storage)
	if err != nil {
		return
	}
	service.Geetest = geetest.NewClient(conf.Geetest)
	service.GeetestLowRisk = geetest.NewClient(conf.GeetestLowRisk)
	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return
	}
	service.PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return
	}
	service.SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return
	}
	service.Captcha, err = captcha.NewClient(conf.Captcha)
	if err != nil {
		return
	}
	return nil
}
