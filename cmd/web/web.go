package web

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/json-iterator/go/extra"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/app"
	"github.com/MiaoSiLa/missevan-go/controllers/backend"
	"github.com/MiaoSiLa/missevan-go/controllers/captcha"
	"github.com/MiaoSiLa/missevan-go/controllers/dramalist"
	"github.com/MiaoSiLa/missevan-go/controllers/dramareview"
	"github.com/MiaoSiLa/missevan-go/controllers/event"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/controllers/site"
	"github.com/MiaoSiLa/missevan-go/controllers/statistics"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/apisign"
	"github.com/MiaoSiLa/missevan-go/middlewares/security"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/web"
)

// Command web
type Command struct {
}

// Name of the Command
func (*Command) Name() string {
	return "web"
}

// Run the Command
func (c *Command) Run(conf *config.Config) error {
	g, ctx := errgroup.WithContext(context.Background())
	err := initService(&conf.Service)
	if err != nil {
		logger.Fatalf("service error: %v", err)
	}
	extra.RegisterFuzzyDecoders()
	srv := newWebServer(conf)
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})
	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
		err = ctx.Err()
		if err != nil {
			return err
		}
	case <-osCh:
	}
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown server failed: %v", err)
		return err
	}
	return nil
}

func newWebServer(conf *config.Config) *http.Server {
	r := web.NewRouterEngine(conf)
	r.Use(security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...))

	statistics := statistics.Handler(conf)
	statistics.Mount(r)

	event := event.HandlerV2(conf)
	event.Mount(r)

	v2 := v2Handler(conf)
	v2.Mount(r)

	x := xHandler(conf)
	x.Mount(r)

	xV2 := xHandlerV2(conf)
	xV2.Mount(r)

	backendHandler := backend.Handler()
	backendHandler.Mount(r)

	srv := web.NewServer(conf, r)
	return srv
}

// TODO: 待删除
func v2Handler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "v2",
		Middlewares: gin.HandlersChain{
			appSign(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			// WORKAROUND: iOS >= 6.0.2、Android >= 6.0.2 将 /v2 的路由换成了 /x，老版本还存在 /v2 的请求
			site.Handler(conf),
		},
	}
}

func xHandler(conf *config.Config) handler.Handler {
	return handler.Handler{
		Name: "x",
		Middlewares: gin.HandlersChain{
			appSign(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.Handler{
			site.Handler(conf),
			app.Handler(conf),
			captcha.Handler(),
			event.Handler(),
			dramalist.Handler(),
		},
	}
}

func xHandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "x",
		Middlewares: gin.HandlersChain{
			// 暂时只针对 Web 项目使用，App 项目不适用，因此暂时不做验签处理
			// appSignV2(conf),
			user.Middleware(),
		},
		SubHandlers: []handler.HandlerV2{
			dramareview.Handler(),
		},
	}
}

func appSign(conf *config.Config) gin.HandlerFunc {
	signFunc := apisign.Middleware([]byte(config.Conf.HTTP.APPAPISignKey))
	return func(c *gin.Context) {
		equip, err := util.ParseEquipment(c.Request)
		if err != nil {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, gin.H{
					"code": v.Code,
					"info": v.Message,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}

func appSignV2(conf *config.Config) gin.HandlerFunc {
	signFunc := apisign.MiddlewareV2([]byte(config.Conf.HTTP.APPAPISignKey))
	return func(c *gin.Context) {
		equip, err := util.ParseEquipment(c.Request)
		if err != nil {
			if v, ok := err.(*handler.ActionError); ok {
				c.AbortWithStatusJSON(v.Status, handler.BasicResponseV2{
					Code:    v.Code,
					Message: v.Message,
				})
				return
			}
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if equip.FromApp {
			// 里面有 c.Next()
			signFunc(c)
			return
		}
		c.Next()
	}
}
