package cmd

import (
	"github.com/MiaoSiLa/missevan-go/cmd/consumer"
	"github.com/MiaoSiLa/missevan-go/cmd/rpc"
	"github.com/MiaoSiLa/missevan-go/cmd/web"
	"github.com/MiaoSiLa/missevan-go/config"
)

// Command interface
type Command interface {
	Name() string
	Run(conf *config.Config) error
	// TODO: 需要一个初始化的接口
}

var (
	commands = []Command{
		&web.Command{},
		&rpc.Command{},
		&consumer.Command{},
	}
)

// Register register commands
func Register() map[string]Command {
	return register(commands)
}

func register(cmds []Command) map[string]Command {
	commandMap := make(map[string]Command)
	for _, cmd := range cmds {
		commandMap[cmd.Name()] = cmd
	}
	return commandMap
}
