package rpc

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/controllers/rpc"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/web"
)

// Command rpc
type Command struct {
}

// Name of the Command
func (*Command) Name() string {
	return "rpc"
}

// Run the Command
func (c *Command) Run(conf *config.Config) error {
	g, ctx := errgroup.WithContext(context.Background())
	err := initService(&conf.Service)
	if err != nil {
		logger.Fatalf("service error: %v", err)
	}
	srv := newRPCServer(conf)
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})
	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
		err = ctx.Err()
		if err != nil {
			return err
		}
	case <-osCh:
	}
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown server failed: %v", err)
		return err
	}
	return nil
}

func newRPCServer(conf *config.Config) *http.Server {
	r := web.NewRouterEngine(conf)
	h := rpc.Handler(conf)
	h.Mount(r)

	hV2 := rpc.HandlerV2(conf)
	hV2.Mount(r)

	srv := web.NewServer(conf, r)
	return srv
}
