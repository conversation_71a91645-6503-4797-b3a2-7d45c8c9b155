package rpc

import (
	"errors"
	"math/rand"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	"github.com/MiaoSiLa/missevan-go/service/antispamv2"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	servicegovern "github.com/MiaoSiLa/missevan-go/service/bilibili/govern"
	serviceshorturl "github.com/MiaoSiLa/missevan-go/service/bilibili/shorturl"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/geetest"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/smartsheet"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Init services
func initService(conf *service.Config) (err error) {
	rand.Seed(util.TimeNow().UnixNano())

	// TODO: 只初始化需要的服务
	dbDefaultConfig := servicedb.Config{}
	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return
	}
	if conf.MessageDB != dbDefaultConfig {
		service.MessageDB, err = servicedb.InitDatabase(&conf.MessageDB)
		if err != nil {
			return
		}
	}
	if conf.LogDB != dbDefaultConfig {
		service.LogDB, err = servicedb.InitDatabase(&conf.LogDB)
		if err != nil {
			return
		}
	}
	if conf.DramaDB != dbDefaultConfig {
		service.DramaDB, err = servicedb.InitDatabase(&conf.DramaDB)
		if err != nil {
			return
		}
	}
	if conf.VoiceDB != dbDefaultConfig {
		service.VoiceDB, err = servicedb.InitDatabase(&conf.VoiceDB)
		if err != nil {
			return
		}
	}
	service.MainDB, err = servicedb.InitDatabase(&conf.MainDB)
	if err != nil {
		return
	}
	service.PayDB, err = servicedb.InitDatabase(&conf.PayDB)
	if err != nil {
		return
	}
	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return
	}
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return
	}

	appDatabusConf, ok := conf.Databus["app_log_pub"]
	if !ok {
		return errors.New("databus config app_log_pub not found")
	}
	service.Databus.AppLogPub = databus.New(&appDatabusConf)

	liveDatabusConf, ok := conf.Databus["live_log_pub"]
	if !ok {
		return errors.New("databus config live_log_pub not found")
	}
	service.Databus.LiveLogPub = databus.New(&liveDatabusConf)

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return err
	}
	service.PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		return err
	}
	service.SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		return err
	}
	service.Gaia, err = servicegaia.NewClient(conf.Gaia)
	if err != nil {
		return err
	}

	service.Govern, err = servicegovern.NewClient(conf.Govern)
	if err != nil {
		return err
	}

	service.ShortURL, err = serviceshorturl.NewClient(conf.ShortURL)
	if err != nil {
		return err
	}

	service.Storage = storage.NewClient(conf.Storage)
	service.AntiSpam, err = antispam.NewClient(&conf.AntiSpam)
	if err != nil {
		return
	}
	service.AntiSpamV2, err = antispamv2.NewClient(&conf.AntiSpamV2)
	if err != nil {
		return
	}
	service.Geetest = geetest.NewClient(conf.Geetest)
	service.GeetestLowRisk = geetest.NewClient(conf.GeetestLowRisk)
	service.OpenSearch, err = search.NewClient(&conf.OpenSearch)
	if err != nil {
		return
	}

	service.GeoIP, err = serviceutil.NewIPIPReader(&conf.IPIP)
	if err != nil {
		return
	}
	service.UserAgentParser, err = serviceutil.NewUserAgentParser(&conf.UserAgentParser)
	if err != nil {
		return
	}
	service.Smartsheet = smartsheet.NewClient(conf.Smartsheet)

	return nil
}
