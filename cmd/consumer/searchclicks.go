package consumer

import (
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/search"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	servicesearch "github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	clickTraceID  = "Alibaba"
	clickBHVType  = "click"
	clickItemType = "other"
)

var clickOpenSearchPoolSize = 100

var (
	debugCh chan error
)

// 数据采集文档：https://help.aliyun.com/document_detail/131547.html
func getOpenSearchClickRecord(click *search.Click) util.Dict {
	// 暂不采集人工干预以及专题卡子项的点击记录
	if click.ItemIsInsert == servicesearch.IsInsert || search.IsTopicCardItemType(click.ItemType) {
		return nil
	}
	appName := click.OpenSearchAppName()
	if appName == "" {
		logger.WithField("item_type", click.ItemType).Error("empty opensearch app name")
		// PASS
		return nil
	}

	// 埋点采集时未登录用户 click.UserID 不为 nil，所以 pushUserID 总是不为空
	var pushUserID string
	if click.UserID != nil {
		pushUserID = service.OpenSearch.GeneratePushUserID(*click.UserID)
	}
	basicFields := map[string]interface{}{
		"user_id":    pushUserID,
		"biz_id":     click.ItemType,
		"trace_id":   clickTraceID,
		"trace_info": click.OpsRequestMisc,
		"rn":         click.RequestID,
		"bhv_type":   clickBHVType,
		"bhv_time":   strconv.FormatInt(click.CreateTime, 10),
		"item_id":    strconv.FormatInt(click.ItemID, 10),
		"item_type":  clickItemType,
		"reach_time": click.CreateTime,
	}
	return util.Dict{
		"app_name":     appName,
		"basic_fields": basicFields,
	}
}

var (
	pushToOpenSearchLock         = &sync.Mutex{}
	pushToOpenSearchCacheLockKey = keys.LockPushSearchClicks0.Format()
	pushToOpenSearchCacheListKey = keys.KeyPushSearchClicksList0.Format()
)

func pushToOpenSearch(records []util.Dict) {
	if len(records) == 0 {
		return
	}

	// TODO: refine
	util.Go(func() {
		var (
			err error
		)
		defer func() {
			if debugCh != nil {
				debugCh <- err
			}
		}()

		pushToOpenSearchLock.Lock()
		defer pushToOpenSearchLock.Unlock()
		// 避免不同实例的 consumer 同时操作 push 数据，使用 redis 加锁
		unlock, ok := pushToOpenSearchCacheLock()
		if !ok {
			// 若加锁失败，则数据直接放入 redis 队列中暂存
			err = saveToPushCacheList(records)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			return
		}
		defer unlock()

		cacheListTotalLen, err := service.Redis.LLen(pushToOpenSearchCacheListKey).Result()
		if err != nil {
			logger.Error(err)
			return
		}
		recordsLen := len(records)
		if (int(cacheListTotalLen) + recordsLen) < clickOpenSearchPoolSize {
			// 需要满足推送条数再请求推送，避免频繁请求；若不满足，则数据直接放入 redis 队列中暂存
			err = saveToPushCacheList(records)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			return
		}

		pipe := service.Redis.TxPipeline()
		// 将缓存队列中的数据都取出进行推送（由于每满一定数量就会推送，所以队列不会太长）
		lRangeCmd := pipe.LRange(pushToOpenSearchCacheListKey, 0, -1)
		// 删除缓存队列
		pipe.Del(pushToOpenSearchCacheListKey)
		if _, err = pipe.Exec(); err != nil {
			// 若为取出缓存队列数据时出错，新的数据暂存入 redis 队列中的操作也大概率会失败，所以直接丢弃这部分数据
			// 若为删除缓存队列数据时出错，则不请求推送点击数据，避免数据被重复推送的情况
			logger.Error(err)
			return
		}
		clickDataList, _ := lRangeCmd.Result()
		clickDataListLen := len(clickDataList)
		pool := make([]util.Dict, 0, clickDataListLen+recordsLen)
		for _, val := range clickDataList {
			var clickData util.Dict
			err = json.Unmarshal([]byte(val), &clickData)
			if err != nil {
				logger.Error(err)
				continue
			}
			pool = append(pool, clickData)
		}
		pool = append(pool, records...)
		recordGroup := util.GroupBy(pool, "app_name")
		for appName, items := range recordGroup {
			recordBuffer := make([]map[string]interface{}, 0, len(items))
			for _, item := range items {
				m := item["basic_fields"].(map[string]interface{})
				if len(m) > 0 {
					recordBuffer = servicesearch.AddClickRecord(recordBuffer, m)
				}
			}
			err = service.OpenSearch.CommitClickData(recordBuffer, appName, nil)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	})
}

func saveToPushCacheList(records []util.Dict) error {
	elements := make([]string, 0, len(records))
	for _, val := range records {
		clickDataBytes, err := json.Marshal(val)
		if err != nil {
			logger.Error(err)
			continue
		}
		elements = append(elements, string(clickDataBytes))
	}
	if len(elements) == 0 {
		return nil
	}
	return service.Redis.RPush(pushToOpenSearchCacheListKey, elements).Err()
}

func pushToOpenSearchCacheLock() (func(), bool) {
	unlock := func() {
		err := service.Redis.Del(pushToOpenSearchCacheLockKey).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	for i := 0; i < 3; i++ {
		if i != 0 {
			// 加锁失败时，等待 200ms 重新尝试加锁
			time.Sleep(200 * time.Millisecond)
		}
		// 推送请求的超时时间为 10s，次数设置锁过期时间 double
		ok, err := service.Redis.SetNX(pushToOpenSearchCacheLockKey, "1", 20*time.Second).Result()
		if err != nil {
			logger.Error(err)
			// PASS
			return nil, false
		}
		if ok {
			return unlock, ok
		}
	}
	logger.Warn("推送搜索点击数据到 OpenSearch 时，加锁失败")
	return nil, false
}
