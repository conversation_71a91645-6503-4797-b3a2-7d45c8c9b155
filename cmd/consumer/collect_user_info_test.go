package consumer

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestCollectUserIP_updateUserAddendumIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(2233, 0)
	})
	defer util.SetTimeNow(nil)
	testUserID := int64(1)
	info := collectUserIPInfo{
		UserID:     testUserID,
		IP:         "**************",
		CreateTime: util.TimeNow().Unix(),
		From:       1,
	}
	infoRawMessage, err := json.Marshal(info)
	require.NoError(err)
	ui := &collectUserIP{
		msg: &databus.Message{
			Key:   keys.DatabusKeyCollectUserIPLog1.Format(testUserID),
			Value: infoRawMessage,
		},
	}
	ui.updateUserAddendumIP()
	var testAddendum user.Addendum
	err = user.Addendum{}.DB().Where("id = ?", testUserID).Take(&testAddendum).Error
	require.NoError(err)
	assert.Equal(info.IP, testAddendum.IP)
	assert.EqualValues(2233, info.CreateTime)
	assert.Equal(1, info.From)
	var testIPDetail struct {
		CountryName string `json:"country_name"`
		RegionName  string `json:"region_name"`
		CityName    string `json:"city_name"`
	}
	err = json.Unmarshal([]byte(*testAddendum.IPDetail), &testIPDetail)
	require.NoError(err)
	assert.Equal("中国", testIPDetail.CountryName)
	assert.Equal("山东", testIPDetail.RegionName)
	assert.Equal("济南", testIPDetail.CityName)

	// 测试错误的 IP
	info.IP = "127.188809.0.1"
	errorIPRawMessage, err := json.Marshal(info)
	require.NoError(err)
	ui.msg.Value = errorIPRawMessage
	ui.updateUserAddendumIP()
	err = user.Addendum{}.DB().Where("id = ?", testUserID).Take(&testAddendum).Error
	require.NoError(err)
	assert.NotEqual(info.IP, testAddendum.IP)

	info.IP = ""
	errorIPRawMessage, err = json.Marshal(info)
	require.NoError(err)
	ui.msg.Value = errorIPRawMessage
	ui.updateUserAddendumIP()
	err = user.Addendum{}.DB().Where("id = ?", testUserID).Take(&testAddendum).Error
	require.NoError(err)
	assert.NotEqual(info.IP, testAddendum.IP)

	// 测试 key 不匹配
	info.IP = "**************"
	value, err := json.Marshal(info)
	require.NoError(err)
	ui.msg.Value = value
	ui.msg.Key = "isNotCollectUserIPLogKey"
	ui.updateUserAddendumIP()
	err = user.Addendum{}.DB().Where("id = ?", testUserID).Take(&testAddendum).Error
	require.NoError(err)
	assert.NotEqual(info.IP, testAddendum.IP)
}
