package consumer

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

const testUserID = int64(1990)

func assertRedisNum(t *testing.T, expectNum int) {
	assert := assert.New(t)
	require := require.New(t)

	key := serviceredis.KeyEquipPlay1.Format(testUserID)
	num, err := service.Redis.ZCard(key).Result()
	require.NoError(err)
	assert.EqualValues(expectNum, num)
}

func assertRedisScore(t *testing.T, equipID string, expectScore int64) {
	assert := assert.New(t)
	require := require.New(t)

	key := serviceredis.KeyEquipPlay1.Format(testUserID)
	if expectScore == 0 {
		_, err := service.Redis.ZScore(key, equipID).Result()
		require.True(serviceredis.IsRedisNil(err))
	} else {
		score, err := service.Redis.ZScore(key, equipID).Result()
		require.NoError(err)
		assert.EqualValues(expectScore, score)
	}
}

func TestPlayLimit_updatePlayLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := serviceredis.KeyEquipPlay1.Format(testUserID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	now := util.TimeNow()
	cancel := util.SetTimeNow(func() time.Time { return now })
	defer cancel()

	// key 不匹配
	p := playLimit{
		msg: &databus.Message{
			Key: "pay_log:1990",
		},
	}
	assert.NotPanics(func() { p.updatePlayLimit() })

	// 用户 ID 为 0
	newMessage := func(i *playSoundInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySoundPlayLog2.Format("web", testUserID),
			Value: v,
		}
	}
	p.msg = newMessage(&playSoundInfo{})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisNum(t, 0)

	testEquipID1, testEquipID2, testEquipID3 := "5b29bec2-cfd8-b345-6b66-ece42b359995", "6b29bec2-cfd8-b345-6b66-ece42b359995", "7b29bec2-cfd8-b345-6b66-ece42b359995"

	// 低于 6.3.6 版本客户端播放不写入限制
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "MissEvanApp/6.3.5 (iOS;16.0;iPhone15,1)",
		EquipID:       testEquipID2,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeStart,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID2, 0)
	assertRedisNum(t, 0)

	// 播放免费且非会员音频不写入限制信息
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		EquipID:       testEquipID1,
		UserID:        testUserID,
		PayType:       sound.SoundFree,
		OperationType: playlogOperationTypeStart,
		Vip:           dramaepisode.VipNot,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID1, 0)
	assertRedisNum(t, 0)

	// 获取到的设备号为空时不写入限制信息
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		EquipID:       "",
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeStart,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, "", 0)
	assertRedisNum(t, 0)

	// Web 播放付费音频正常写入限制
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		EquipID:       testEquipID1,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeStart,
		Vip:           dramaepisode.VipNot,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID1, now.Unix())
	assertRedisNum(t, 1)

	// Web 播放会员音频正常写入限制
	err = service.Redis.Del(key).Err()
	require.NoError(err)
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
		EquipID:       testEquipID1,
		UserID:        testUserID,
		PayType:       sound.SoundFree,
		OperationType: playlogOperationTypeStart,
		Vip:           dramaepisode.VipLimit,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID1, now.Unix())
	assertRedisNum(t, 1)

	// 大于等于 6.3.6 版本客户端播放写入限制信息
	_ = util.SetTimeNow(func() time.Time {
		return time.Unix(now.Add(5*time.Second).Unix(), 0)
	})

	err = service.Redis.Del(key).Err()
	require.NoError(err)
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "MissEvanApp/6.3.6 (Android;13;vivo V2002A PD2019)",
		EquipID:       testEquipID3,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeStart,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID3, now.Add(5*time.Second).Unix())
	assertRedisNum(t, 1)

	// 大于等于 6.3.6 版本客户端继续播放更新限制信息
	_ = util.SetTimeNow(func() time.Time {
		return time.Unix(now.Add(10*time.Second).Unix(), 0)
	})

	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "MissEvanApp/6.3.6 (Android;13;vivo V2002A PD2019)",
		EquipID:       testEquipID3,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeContinue,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	// 断言设备号的分值被更新
	assertRedisScore(t, testEquipID3, now.Add(10*time.Second).Unix())
	assertRedisNum(t, 1)
	// 断言 key 有效期
	expire, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.EqualValues(time.Duration(params.UserEquipPlayLimit.EffectiveDuration+5), expire.Seconds())

	// 播放设备超出限制时，后续设备信息无法写入
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "MissEvanApp/6.3.6 (Android;13;vivo V2002A PD2019)",
		EquipID:       testEquipID2,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypeContinue,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID3, now.Add(10*time.Second).Unix())
	assertRedisScore(t, testEquipID2, 0)
	assertRedisNum(t, 1)

	// 大于等于 6.3.6 版本客户端暂停播放移除限制信息
	p.msg = newMessage(&playSoundInfo{
		UserAgent:     "MissEvanApp/6.3.6 (Android;13;vivo V2002A PD2019)",
		EquipID:       testEquipID3,
		UserID:        testUserID,
		PayType:       sound.PayBySound,
		OperationType: playlogOperationTypePause,
	})
	assert.NotPanics(func() { p.updatePlayLimit() })
	assertRedisScore(t, testEquipID3, 0)
	assertRedisNum(t, 0)
}
