package consumer

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/search"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestPushToOpenSearch(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	clickOpenSearchPoolSize = 5
	channelSize := 2
	debugCh = make(chan error, channelSize)

	err := service.Redis.Del(pushToOpenSearchCacheLockKey, pushToOpenSearchCacheListKey).Err()
	require.NoError(err)
	_, err = service.Redis.SetNX(pushToOpenSearchCacheLockKey, "1", 200*time.Millisecond).Result()
	require.NoError(err)

	var clicks []*search.Click
	for i := 0; i < 2; i++ {
		clicks = append(clicks, &search.Click{
			EventType: util.NewInt(search.EventTypeClickSearch),
			Input:     "魔道 go test",
		})
	}

	var opensearchData []util.Dict
	for _, click := range clicks {
		opensearchData = append(opensearchData, getOpenSearchClickRecord(click))
	}

	// 验证不满足推送条数的情况
	for i := 0; i < channelSize; i++ {
		pushToOpenSearch(opensearchData)
	}

	for i := 0; i < channelSize; i++ {
		<-debugCh
		// Skip
		// assert.NoError(err)
	}
	// 验证 redis list 中添加了数据
	listLen, err := service.Redis.LLen(pushToOpenSearchCacheListKey).Result()
	require.NoError(err)
	assert.EqualValues(4, listLen)

	// 验证满足推送条数的情况（缓存中数据被取出）
	pushToOpenSearch(opensearchData)
	<-debugCh
	listLen, err = service.Redis.LLen(pushToOpenSearchCacheListKey).Result()
	require.NoError(err)
	assert.Zero(listLen)
}

func TestSaveToPushCacheList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	err := service.Redis.Del(pushToOpenSearchCacheListKey).Err()
	require.NoError(err)
	defer service.Redis.Del(pushToOpenSearchCacheListKey)

	// 测试保存数据正常
	records := []util.Dict{map[string]interface{}{
		"a": 1,
	}}
	err = saveToPushCacheList(records)
	require.NoError(err)
	// 验证数据正确
	val, err := service.Redis.LRange(pushToOpenSearchCacheListKey, 0, 0).Result()
	require.NoError(err)
	require.Len(val, 1)
	assert.Equal("{\"a\":1}", val[0])

	// 测试后续保存的数据被放到队列尾部
	records = []util.Dict{map[string]interface{}{
		"b": 2,
	}}
	err = saveToPushCacheList(records)
	require.NoError(err)
	// 验证数据正确
	val, err = service.Redis.LRange(pushToOpenSearchCacheListKey, 0, 1).Result()
	require.NoError(err)
	require.Len(val, 2)
	assert.Equal("{\"a\":1}", val[0])
	assert.Equal("{\"b\":2}", val[1])
}

func TestPushToOpenSearchCacheLock(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试成加锁的情况
	err := service.Redis.Del(pushToOpenSearchCacheLockKey).Err()
	require.NoError(err)
	unlock, ok := pushToOpenSearchCacheLock()
	require.True(ok)
	val, err := service.Redis.Exists(pushToOpenSearchCacheLockKey).Result()
	require.NoError(err)
	require.NotZero(val)
	// 验证使用 unlock 可以解锁
	unlock()
	val, err = service.Redis.Exists(pushToOpenSearchCacheLockKey).Result()
	require.NoError(err)
	assert.Zero(val)

	// 测试加锁时失败，重试成功的情况
	_, err = service.Redis.SetNX(pushToOpenSearchCacheLockKey, "1", 300*time.Millisecond).Result()
	require.NoError(err)
	_, ok = pushToOpenSearchCacheLock()
	require.True(ok)
	val, err = service.Redis.Exists(pushToOpenSearchCacheLockKey).Result()
	require.NoError(err)
	assert.NotZero(val)

	// 测试加锁失败的情况
	_, err = service.Redis.SetNX(pushToOpenSearchCacheLockKey, "1", 1*time.Second).Result()
	require.NoError(err)
	_, ok = pushToOpenSearchCacheLock()
	require.False(ok)
}
