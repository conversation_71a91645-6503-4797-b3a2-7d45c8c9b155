package consumer

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

type playLimit struct {
	msg *databus.Message
}

func handlePlayLimit(msg *databus.Message) {
	if !params.UserEquipPlayLimit.Enable {
		return
	}
	p := playLimit{msg: msg}
	p.updatePlayLimit()
}

func (p *playLimit) updatePlayLimit() {
	if !keys.DatabusKeySoundPlayLog2.MatchKey(p.msg.Key) {
		return
	}
	info := playSoundInfo{}
	err := json.Unmarshal(p.msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.UserID == 0 ||
		info.EquipID == "" ||
		// 免费非会员音不需要进行播放限制
		(info.PayType == sound.SoundFree && info.Vip != dramaepisode.VipLimit) {
		return
	}
	equip := util.NewEquipment(info.UserAgent)
	if equip.IsAppOlderThan("6.3.6", "6.3.6") {
		// WORKAROUND: 低版本客户端无心跳上报，不计入限制设备号
		return
	}
	key := serviceredis.KeyEquipPlay1.Format(info.UserID)
	endPlayTypeList := []int{playlogOperationTypePause, playlogOperationTypeEnd, playlogWebOperationTypeClose}
	if util.HasElem(endPlayTypeList, info.OperationType) {
		_, err = service.Redis.ZRem(key, info.EquipID).Result()
		if err != nil {
			logger.Error(err)
		}
		return
	}
	startPlayTypeList := []int{playlogOperationTypeStart, playlogOperationTypeContinue, playlogOperationTypeCycle, playlogOperationType10s}
	if !util.HasElem(startPlayTypeList, info.OperationType) {
		return
	}
	timeNow := util.TimeNow()
	timeRange := &redis.ZRangeBy{
		Min: strconv.FormatInt(timeNow.Add(time.Duration(-params.UserEquipPlayLimit.EffectiveDuration)*time.Second).Unix(), 10),
		Max: strconv.FormatInt(timeNow.Unix(), 10)}
	equipList, err := service.Redis.ZRangeByScore(key, timeRange).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	equipNum := len(equipList)
	if equipNum > params.UserEquipPlayLimit.EquipMaxNum {
		// EquipMaxNum 值改变等原因造成用户当前设备数量超出限制时，暂不将新的设备号写入该用户当前正在播放中的设备白名单，
		// 等待该用户设备信息自然过期后，重新写入符合限制数量的设备号，
		// 避免在已超限的情况下持续更新此名单，致使名单中设备一直可用
		return
	}
	if equipNum == params.UserEquipPlayLimit.EquipMaxNum && !util.HasElem(equipList, info.EquipID) {
		return
	}
	pipe := service.Redis.TxPipeline()
	pipe.ZAdd(key, &redis.Z{Score: float64(timeNow.Unix()), Member: info.EquipID})
	// 考虑到网络延迟等问题，key 有效期在 EffectiveDuration 的基础上增加 5s 冗余
	keyExpireDuration := time.Duration(params.UserEquipPlayLimit.EffectiveDuration + 5)
	pipe.Expire(key, keyExpireDuration*time.Second)
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		return
	}
}
