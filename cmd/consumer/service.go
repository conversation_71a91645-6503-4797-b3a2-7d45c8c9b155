package consumer

import (
	"errors"
	"math/rand"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Init services
func initService(conf *service.Config) error {
	rand.Seed(util.TimeNow().UnixNano())

	var err error
	dbDefaultConfig := servicedb.Config{}
	service.DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		return err
	}
	if conf.LogDB != dbDefaultConfig {
		service.LogDB, err = servicedb.InitDatabase(&conf.LogDB)
		if err != nil {
			return err
		}
	}
	service.DramaDB, err = servicedb.InitDatabase(&conf.DramaDB)
	if err != nil {
		return err
	}
	service.MainDB, err = servicedb.InitDatabase(&conf.MainDB)
	if err != nil {
		return err
	}

	service.Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		return err
	}
	service.LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		return err
	}

	databusConf, ok := conf.Databus["app_log_sub"]
	if ok {
		service.Databus.AppLogSub = databus.New(&databusConf)
	} else {
		return errors.New("databus config app_log_sub not found")
	}

	service.MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		return err
	}

	service.GeoIP, err = serviceutil.NewIPIPReader(&conf.IPIP)
	if err != nil {
		return err
	}

	service.OpenSearch, err = search.NewClient(&conf.OpenSearch)
	if err != nil {
		return err
	}

	return nil
}
