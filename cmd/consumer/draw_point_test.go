package consumer

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-go/models/drawpoint"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestHandleDrawPoint(t *testing.T) {
	assert := assert.New(t)

	msg := databus.Message{}
	assert.NotPanics(func() { handleDrawPoint(&msg) })

	service.Cache5Min.Set(keys.LocalKeyDrawPointConfig1.Format(205), &drawpoint.PointConfig{}, 0)
	assert.NotPanics(func() { handleDrawPoint(&msg) })
}

func TestEvent205QuestsFinishQuestUserPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventID := int64(205)
	msg := &databus.Message{Key: "test"}
	q := event205Quests{
		Msg: msg,
	}
	q.PointConfig()
	q.pc = nil
	q.finishQuestUserPoint()

	msg.Key = keys.DatabusKeyUserPointDetailLog1.Format(12)
	q.finishQuestUserPoint() // value 异常

	var info struct {
		Type       int64 `json:"type"`
		UserID     int64 `json:"user_id"`
		Num        int64 `json:"num"`
		CreateTime int64 `json:"create_time"`
	}
	info.UserID = 12
	rawMessage := func() json.RawMessage {
		r, err := json.Marshal(info)
		require.NoError(err)
		return r
	}

	msg.Value = rawMessage()
	q.finishQuestUserPoint() // type 不对

	info.Type = 6
	msg.Value = rawMessage()
	q.finishQuestUserPoint() // num 不对

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	q.pc = &drawpoint.PointConfig{
		DrawPointStartTime: now.Unix() - 100,
		DrawPointEndTime:   now.Unix() + 100,

		EventID:      drawpoint.EventIDMaoMaoStar,
		EventEndTime: now.Unix() + 100,
	}

	info.Num = -1
	msg.Value = rawMessage()
	q.finishQuestUserPoint() // num 不对

	keyQuest := keys.KeyMaoMaoPointUserID1.Format(info.UserID)
	keyPoint := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	quest1 := drawpoint.MaoMaoQuestFieldUserPoint1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	pipe := service.Redis.Pipeline()
	pipe.HDel(keyQuest, quest1)
	pipe.ZRem(keyPoint, info.UserID)
	_, err := pipe.Exec()
	require.NoError(err)

	info.CreateTime = now.Unix()
	msg.Value = rawMessage()
	q.finishQuestUserPoint() // 正常加积分
	dp := drawpoint.Param{
		EventID:     eventID,
		UserID:      info.UserID,
		CurrentTime: q.pc.NowUnix(),
	}
	point, err := dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(25, point)

	q.finishQuestUserPoint() // 重复完成不加积分
	point, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(25, point)
}

func TestEvent205QuestsFinishQuestsShare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	eventID := int64(205)
	msg := &databus.Message{
		Key: "test",
	}
	q := event205Quests{
		Msg: msg,
	}
	q.PointConfig()
	q.pc = nil
	q.finishQuestsShare()

	msg.Key = keys.DatabusKeyShareDetailLog1.Format(now.Unix())
	q.finishQuestsShare() // value 异常

	var info struct {
		Type       int    `json:"type"`
		UserID     int64  `json:"user_id"`
		ElementID  int64  `json:"element_id"`
		URL        string `json:"url"`
		CreateTime int64  `json:"create_time"`
	}
	rawMessage := func() json.RawMessage {
		r, err := json.Marshal(info)
		require.NoError(err)
		return r
	}

	msg.Value = rawMessage()
	q.finishQuestsShare() // 用户 ID 为 0

	q.pc = &drawpoint.PointConfig{
		DrawPointStartTime: now.Unix() - 100,
		DrawPointEndTime:   now.Unix() + 100,

		EventID:      drawpoint.EventIDMaoMaoStar,
		EventEndTime: now.Unix() + 100,
	}
	info.UserID = 12
	msg.Value = rawMessage()
	q.finishQuestsShare() // 时间不对

	dp := drawpoint.Param{
		EventID:     eventID,
		UserID:      info.UserID,
		CurrentTime: q.pc.NowUnix(),
	}
	before, err := dp.DrawPoint()
	require.NoError(err)

	clearQuest := func() {
		key := keys.KeyMaoMaoPointUserID1.Format(info.UserID)
		questSound := drawpoint.MaoMaoQuestFieldShareSound1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
		questEvent := drawpoint.MaoMaoQuestFieldShareEvent.Format()
		questLive := drawpoint.MaoMaoQuestFieldShareLive.Format()
		require.NoError(service.Redis.HDel(key,
			questSound, questEvent, questLive).Err())
	}

	// 老版本分享音频和直播间
	clearQuest()
	info.CreateTime = now.Unix()
	msg.Value = rawMessage()
	q.finishQuestsShare()
	q.finishQuestsShare()
	q.finishQuestsShare()
	after, err := dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(30+50, after-before)

	// 新版本分享直播间
	before = after
	clearQuest()
	info.URL = "https://fm.missevan.com/live/172227909"
	msg.Value = rawMessage()
	q.finishQuestsShare()
	q.finishQuestsShare()
	q.finishQuestsShare()
	after, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(50, after-before)
	// 分享直播间不是猫耳LIVE
	before = after
	clearQuest()
	info.URL = "https://fm.uat.missevan.com/live/172227909"
	msg.Value = rawMessage()
	q.finishQuestsShare() // 分享 UAT 的猫耳LIVE
	clearQuest()
	info.URL = "https://fm.missevan.com/live/123"
	msg.Value = rawMessage()
	q.finishQuestsShare() // 分享不是猫耳LIVE
	after, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(50, after-before)

	// 新版本分享音频
	before = after
	clearQuest()
	info.Type = 1
	info.ElementID = 123
	info.URL = ""
	msg.Value = rawMessage()
	q.finishQuestsShare()
	q.finishQuestsShare()
	q.finishQuestsShare()
	after, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(30, after-before)

	// 分享活动
	before = after
	clearQuest()
	info.Type = 4
	info.ElementID = 123
	msg.Value = rawMessage()
	q.finishQuestsShare() // 不是主会场
	info.ElementID = 199
	msg.Value = rawMessage()
	q.finishQuestsShare() // 主会场
	after, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(50, after-before)

	// 后续的分享直播间
	before = after
	clearQuest()
	info.Type = 5
	info.ElementID = 172227909
	msg.Value = rawMessage()
	q.finishQuestsShare()
	info.ElementID = 123
	msg.Value = rawMessage()
	q.finishQuestsShare() // 分享的不是猫耳LIVE
	after, err = dp.DrawPoint()
	require.NoError(err)
	assert.EqualValues(50, after-before)
}

func TestEventTheatreQuestsPointConfig(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	key := keys.LocalKeyDrawPointConfig1.Format(drawpoint.EventIDTheatreVI)
	service.Cache5Min.SetDefault(key, nil)

	var q eventTheatreQuests
	assert.Nil(q.PointConfig(now))
	q.pc = &drawpoint.PointConfig{
		EventID: drawpoint.EventIDTheatreVI,
	}
	assert.Nil(q.PointConfig(now))

	q.pc.DrawPointStartTime = now.Unix() - 10
	q.pc.DrawPointEndTime = now.Unix() + 10
	assert.NotNil(q.PointConfig(now))
}

func TestEventDramaPointQuests_FinishQuestFollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID := int64(12)

	// 测试 databus key 不匹配
	q := eventTheatreQuests{
		EventID: drawpoint.EventIDTheatreVI,
		Msg:     &databus.Message{},
	}
	q.finishQuestFollowUser()

	newMessage := func(i *followUserInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyUserFollowLog1.Format(userID),
			Value: v,
		}
	}

	// 测试用户 ID 为 0
	q.Msg = newMessage(&followUserInfo{})
	q.finishQuestFollowUser()

	// 测试 follow_type 不正确
	q.Msg = newMessage(&followUserInfo{UserID: userID})
	q.finishQuestFollowUser()

	// 测试被关注人不是官方账号
	q.Msg = newMessage(&followUserInfo{UserID: userID, FollowType: person.FollowTypeFollow})
	q.finishQuestFollowUser()

	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: theatreOfficialUserID,
		FollowType:   person.FollowTypeFollow,
	})

	// 测试未到获取活动积分时间
	q.PointConfig(now)
	q.pc = nil
	q.finishQuestFollowUser()

	q.pc = &drawpoint.PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            q.EventID,
		EventEndTime:       now.Unix(),
	}
	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: theatreOfficialUserID,
		FollowType:   person.FollowTypeFollow,
		CreateTime:   now.Unix(),
	})
	questKey := keys.KeyDrawPointUserQuests2.Format(q.EventID, userID)
	questField := drawpoint.TheatreQuestFieldFollowUser0.Format()
	require.NoError(service.Redis.HDel(questKey, questField).Err())

	// 测试正常返回
	q.finishQuestFollowUser()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_FinishQuestSubscribeOneDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID := int64(12)

	// 测试 databus key 不匹配
	q := eventTheatreQuests{
		EventID: drawpoint.EventIDTheatreVI,
		Msg:     &databus.Message{},
	}
	q.finishQuestSubscribeOneDrama()

	newMessage := func(i *subscribeDramaInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySubscribeDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试用户 ID 为 0
	q.Msg = newMessage(&subscribeDramaInfo{})
	q.finishQuestSubscribeOneDrama()

	// 测试 type 不正确
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID})
	q.finishQuestSubscribeOneDrama()

	// 测试剧集 ID 不是本次上新剧集
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID, Type: SubscribeDramaTypeSubscribe})
	q.finishQuestSubscribeOneDrama()

	dramaID := int64(1234)
	require.NoError(service.Redis.SAdd(keys.KeyTheatreNewDramaIDs0.Format(), dramaID).Err())
	q.Msg = newMessage(&subscribeDramaInfo{DramaID: dramaID, UserID: userID, Type: SubscribeDramaTypeSubscribe})

	// 测试未到获取活动积分时间
	q.PointConfig(now)
	q.pc = nil
	q.finishQuestSubscribeOneDrama()

	q.pc = &drawpoint.PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            q.EventID,
		EventEndTime:       now.Unix(),
	}
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	questKey := keys.KeyDrawPointUserQuests2.Format(q.EventID, userID)
	questField := drawpoint.TheatreQuestFieldSubscribeOneDrama0.Format()
	require.NoError(service.Redis.HDel(questKey, questField).Err())

	// 测试正常返回
	q.finishQuestSubscribeOneDrama()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_FinishQuestSubscribeSeveralDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID := int64(12)

	subscribeKey := drawpoint.KeyTheatreSubscribeDramaIDs(drawpoint.EventIDTheatreVI, userID)
	require.NoError(service.Redis.Del(subscribeKey).Err())
	dramaIDsKey := keys.KeyTheatreNewDramaIDs0.Format()
	require.NoError(service.Redis.Del(dramaIDsKey).Err())

	// 测试 databus key 不匹配
	q := eventTheatreQuests{
		EventID: drawpoint.EventIDTheatreVI,
		Msg:     &databus.Message{},
	}
	q.finishQuestSubscribeSeveralDrama()

	newMessage := func(i *subscribeDramaInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySubscribeDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试用户 ID 为 0
	q.Msg = newMessage(&subscribeDramaInfo{})
	q.finishQuestSubscribeSeveralDrama()

	// 测试 type 不正确
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID})
	q.finishQuestSubscribeSeveralDrama()

	// 测试剧集 ID 不是本次上新剧集
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID, Type: SubscribeDramaTypeSubscribe})
	q.finishQuestSubscribeSeveralDrama()

	dramaID := int64(1234)
	require.NoError(service.Redis.SAdd(dramaIDsKey, dramaID).Err())
	q.Msg = newMessage(&subscribeDramaInfo{DramaID: dramaID, UserID: userID, Type: SubscribeDramaTypeSubscribe})

	// 测试未到获取活动积分时间
	q.PointConfig(now)
	q.pc = nil
	q.finishQuestSubscribeSeveralDrama()

	q.pc = &drawpoint.PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            q.EventID,
		EventEndTime:       now.Unix(),
	}
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	questKey := keys.KeyDrawPointUserQuests2.Format(q.EventID, userID)
	questField := drawpoint.TheatreQuestFieldSubscribeSeveralDrama0.Format()
	require.NoError(service.Redis.HDel(questKey, questField).Err())

	// 测试追剧数量达不到指定数量
	require.NoError(service.Redis.HIncrBy(questKey, questField, drawpoint.TheatreTaskSubscribeDramaNum-2).Err())
	q.finishQuestSubscribeSeveralDrama()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(drawpoint.TheatreTaskSubscribeDramaNum-1, progress)

	// 重复追剧不加进度
	q.finishQuestSubscribeSeveralDrama()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(drawpoint.TheatreTaskSubscribeDramaNum-1, progress)

	// 测试正常返回
	dramaID = int64(5678)
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	require.NoError(service.Redis.SAdd(dramaIDsKey, dramaID).Err())
	q.finishQuestSubscribeSeveralDrama()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(drawpoint.TheatreTaskSubscribeDramaNum, progress)
}

func TestEventDramaPointQuests_finishQuestSubscribe(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(12)
	eventID := drawpoint.EventID428

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestSubscribe()

	newMessage := func(i *subscribeDramaInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySubscribeDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	q.Msg = newMessage(&subscribeDramaInfo{})
	q.finishQuestSubscribe()

	// 测试 type 不为追剧的情况
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID})
	q.finishQuestSubscribe()

	// 测试订阅非活动剧集 ID 的情况
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID, Type: SubscribeDramaTypeSubscribe, DramaID: 99999999})
	q.finishQuestSubscribe()

	// 测试正常加积分的情况
	dramaID := int64(1)
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	q.finishQuestSubscribe()
	questField := drawpoint.NewVoiceQuestFieldSubscribe0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaQuests_finishQuestPlayedDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(12)
	eventID := drawpoint.EventID669

	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(finishKey).Err()
	require.NoError(err)

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestPlayedDuration()

	newMessage := func(i *playSoundInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySoundPlayLog2.Format("web", userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	q.Msg = newMessage(&playSoundInfo{})
	q.finishQuestPlayedDuration()

	// 测试播放时长为 0 的情况
	nowTime := now.Unix()
	nowTimeMilli := util.TimeUnixMilli(nowTime * 1000) // 不通过 NewTimeUnixMilli() 获取避免有毫秒上的差异
	q.Msg = newMessage(&playSoundInfo{UserID: userID, StartTime: nowTimeMilli, EndTime: nowTimeMilli})
	q.finishQuestPlayedDuration()

	// 测试 operation_type 需要忽略的情况
	q.Msg = newMessage(&playSoundInfo{UserID: userID, StartTime: nowTimeMilli - 233, EndTime: nowTimeMilli, OperationType: 9999})
	q.finishQuestPlayedDuration()

	// 测试收听的不是活动剧集的情况
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: 99999999})
	q.finishQuestPlayedDuration()

	// 测试收听第一季剧集
	// 测试累计播放时长未到加积分最低值的情况
	dramaID := int64(52347)
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: nowTimeMilli - 599999, EndTime: nowTimeMilli, OperationType: playlogOperationTypeEnd, CreateTime: nowTime})
	q.finishQuestPlayedDuration()
	questField := drawpoint.QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(599999, progress)
	// 断言完成次数
	finishField := drawpoint.QuestFieldPlayDurationDailyTotal0
	finishNum, err := service.Redis.HGet(finishKey, finishField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.EqualValues(0, finishNum)

	// 测试累计播放时长达到加积分最低值的情况
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: nowTimeMilli - 1, EndTime: nowTimeMilli, OperationType: playlogOperationTypeEnd, CreateTime: nowTime})
	q.finishQuestPlayedDuration()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(600000, progress)
	// 断言完成次数
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.EqualValues(1, finishNum)

	// 测试第二天累计播放时长未达到加积分最低值
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	today := util.BeginningOfDay(now)
	tomorrow := today.AddDate(0, 0, 1)
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: util.NewTimeUnixMilli(tomorrow), EndTime: util.NewTimeUnixMilli(tomorrow) + 1, OperationType: playlogOperationTypeEnd, CreateTime: tomorrow.Unix()})
	q.finishQuestPlayedDuration()
	questField = drawpoint.QuestFieldPlayDurationDaily1.Format(tomorrow.Format(util.TimeFormatYMDWithNoSpace))
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(1, progress)
	// 断言完成次数
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.EqualValues(1, finishNum)

	// 测试第二天累计播放时长达到加积分最低值
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: util.NewTimeUnixMilli(tomorrow), EndTime: util.NewTimeUnixMilli(tomorrow) + 600000, OperationType: playlogOperationTypeEnd, CreateTime: tomorrow.Unix()})
	q.finishQuestPlayedDuration()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(600001, progress)
	// 断言完成次数
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.EqualValues(2, finishNum)

	// 测试第二季剧集
	// 测试累计播放时长达到加积分最低值的情况
	dramaID = int64(71605)
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: nowTimeMilli - 600000, EndTime: nowTimeMilli, OperationType: playlogOperationTypeEnd, CreateTime: nowTime})
	q.finishQuestPlayedDuration()
	questField = drawpoint.QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(600000, progress)
	// 断言完成次数
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.EqualValues(3, finishNum)

	// 测试跨天的情况
	err = service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&playSoundInfo{
		UserID:        userID,
		DramaID:       dramaID,
		StartTime:     util.NewTimeUnixMilli(today) - 700000,
		EndTime:       util.NewTimeUnixMilli(today) + 1,
		OperationType: playlogOperationTypeEnd,
		CreateTime:    today.Unix(),
	})
	q.finishQuestPlayedDuration()
	yesterday := today.AddDate(0, 0, -1)
	yesterdayQuestField := drawpoint.QuestFieldPlayDurationDaily1.Format(yesterday.Format(util.TimeFormatYMDWithNoSpace))
	todayQuestField := drawpoint.QuestFieldPlayDurationDaily1.Format(today.Format(util.TimeFormatYMDWithNoSpace))
	info, err := service.Redis.HMGet(questKey, yesterdayQuestField, todayQuestField).Result()
	require.NoError(err)
	assert.EqualValues(strconv.Itoa(700000), info[0])
	assert.EqualValues(strconv.Itoa(1), info[1])
	// 断言完成次数
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.EqualValues(1, finishNum)

	// 测试活动未配置收听剧集任务的情况
	eventID = 590
	q = eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	questKey = keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: dramaID, StartTime: nowTimeMilli - 600000, EndTime: nowTimeMilli, OperationType: playlogOperationTypeEnd, CreateTime: nowTime})
	q.finishQuestPlayedDuration()
	questField = drawpoint.QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.EqualValues(0, progress)
	finishField = drawpoint.QuestFieldPlayDurationDailyTotal0
	finishKey = keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	finishNum, err = service.Redis.HGet(finishKey, finishField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.EqualValues(0, finishNum)
}

func TestEventDramaPointQuests_finishQuestUserPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(12)
	eventID := drawpoint.EventID428

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestUserPoint()

	newMessage := func(i *userPointDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyUserPointDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试不是投食音频
	q.Msg = newMessage(&userPointDetailLog{UserID: userID, Type: 1})
	q.finishQuestUserPoint()

	// 测试投食非活动剧集内的音频
	q.Msg = newMessage(&userPointDetailLog{UserID: userID, Type: 6, Num: -1, More: &userPointMore{SoundID: 99999999}})
	q.finishQuestUserPoint()

	// 测试正常添加积分
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())
	q.Msg = newMessage(&userPointDetailLog{UserID: userID, Type: 6, Num: -1, More: &userPointMore{SoundID: 1}})
	q.finishQuestUserPoint()
	questField := drawpoint.NewVoiceQuestFieldUserPoint0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_finishQuestShareEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(12)
	eventID := drawpoint.EventID428

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestShareEvent()

	newMessage := func(i *shareDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyShareDetailLog1.Format(now.Unix()),
			Value: v,
		}
	}
	// 测试游客分享
	q.Msg = newMessage(&shareDetailLog{})
	q.finishQuestShareEvent()

	// 测试分享的类型不是活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID})
	q.finishQuestShareEvent()

	// 测试分享的活动不是指定活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent})
	q.finishQuestShareEvent()

	// 测试正常添加积分
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, CreateTime: now.Unix()})
	q.finishQuestShareEvent()
	questField := drawpoint.NewVoiceQuestFieldShare0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventYglQuests_finishQuestShare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()

	// 测试 key 不匹配的情况
	q := eventYglQuest{
		eventID: drawpoint.EventID400,
		Msg:     &databus.Message{},
	}
	q.finishQuestShare()

	newMessage := func(i *shareDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyShareDetailLog1.Format(now.Unix()),
			Value: v,
		}
	}
	// 测试游客分享
	q.Msg = newMessage(&shareDetailLog{})
	q.finishQuestShare()

	// 测试分享的类型不是活动的情况
	userID := int64(12)
	q.Msg = newMessage(&shareDetailLog{UserID: userID})
	q.finishQuestShare()

	// 测试分享的活动不是指定活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent})
	q.finishQuestShare()

	eventID := drawpoint.EventID400
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试正常添加积分
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, CreateTime: now.Unix()})
	q.finishQuestShare()
	questField := drawpoint.YglQuestFieldShare0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventYglQuest_finishQuestFollowUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()

	userID := int64(12)
	eventID := drawpoint.EventID400
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试 key 不匹配的情况
	q := eventYglQuest{
		eventID: drawpoint.EventID400,
		Msg:     &databus.Message{},
	}
	q.finishQuestFollowUser()

	now := util.TimeNow()
	newMessage := func(i *followUserInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyUserFollowLog1.Format(now.Unix()),
			Value: v,
		}
	}

	// 测试不是关注摇光录站内号
	q.Msg = newMessage(&followUserInfo{
		FollowType: person.FollowTypeFollow,
		UserID:     userID,
		CreateTime: now.Unix(),
	})
	q.finishQuestFollowUser()
	followKey := drawpoint.YglQuestFieldFollowUser0.Format()
	exists, err := service.Redis.HExists(questKey, followKey).Result()
	require.NoError(err)
	assert.False(exists)

	q.Msg = newMessage(&followUserInfo{
		FollowType:   person.FollowTypeFollow,
		UserID:       userID,
		FollowUserID: yglOfficialUserID,
		CreateTime:   now.Unix(),
	})
	q.finishQuestFollowUser()
	exists, err = service.Redis.HExists(questKey, followKey).Result()
	require.NoError(err)
	assert.True(exists)
}

func TestEventDramaPointQuests_finishQuestSubscribeOneDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID669

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestSubscribeOneDrama()

	newMessage := func(i *subscribeDramaInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySubscribeDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	q.Msg = newMessage(&subscribeDramaInfo{})
	q.finishQuestSubscribeOneDrama()
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	questField := drawpoint.QuestFieldFirstSeasonSubscribed0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试 type 不为追剧的情况
	dramaID := int64(52347)
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID, Type: SubscribeDramaTypeCancel, DramaID: dramaID})
	q.finishQuestSubscribeOneDrama()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试订阅非活动剧集 ID 的情况
	dramaID = 99999999
	q.Msg = newMessage(&subscribeDramaInfo{UserID: userID, Type: SubscribeDramaTypeSubscribe, DramaID: dramaID})
	q.finishQuestSubscribeOneDrama()
	questField = drawpoint.QuestFieldSubscribeSpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试订阅第一季剧集正常加积分的情况
	dramaID = 52347
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	q.finishQuestSubscribeOneDrama()
	questField = drawpoint.QuestFieldSubscribeSpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)

	// 测试订阅第二季下剧集正常加积分的情况
	dramaID = int64(76557)
	q.Msg = newMessage(&subscribeDramaInfo{
		DramaID:    dramaID,
		UserID:     userID,
		Type:       SubscribeDramaTypeSubscribe,
		CreateTime: now.Unix(),
	})
	q.finishQuestSubscribeOneDrama()
	questField = drawpoint.QuestFieldSubscribeSpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_finishQuestBuyOneDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	userID := int64(1)
	eventID := drawpoint.EventID669
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey).Err()
		cancel()
	}()

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestBuyOneDrama()

	newMessage := func(i *buyDramaDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyBuyDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	dramaID := int64(52347)
	q.Msg = newMessage(&buyDramaDetailLog{DramaID: dramaID})
	q.finishQuestBuyOneDrama()
	questField := drawpoint.QuestFieldBuySpecifiedDrama1.Format(dramaID)
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试购买非活动剧集 ID 的情况
	q.Msg = newMessage(&buyDramaDetailLog{UserID: userID, DramaID: 99999999})
	q.finishQuestBuyOneDrama()
	questField = drawpoint.QuestFieldBuySpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试购买第一季剧集正常加积分的情况
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&buyDramaDetailLog{
		DramaID:    dramaID,
		UserID:     userID,
		CreateTime: now.Unix(),
	})
	q.finishQuestBuyOneDrama()
	questField = drawpoint.QuestFieldBuySpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)

	// 测试购买第二季下剧集正常加积分的情况
	dramaID = int64(76557)
	q.Msg = newMessage(&buyDramaDetailLog{
		DramaID:    dramaID,
		UserID:     userID,
		CreateTime: now.Unix(),
	})
	q.finishQuestBuyOneDrama()
	questField = drawpoint.QuestFieldBuySpecifiedDrama1.Format(dramaID)
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_finishQuestBought(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer util.SetTimeNow(nil)
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID446

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestBought()

	newMessage := func(i *buyDramaDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyBuyDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	q.Msg = newMessage(&buyDramaDetailLog{})
	q.finishQuestBought()

	// 测试购买非活动剧集 ID 的情况
	q.Msg = newMessage(&buyDramaDetailLog{UserID: userID, DramaID: 99999999})
	q.finishQuestBought()

	// 测试购买第一季剧集正常加积分的情况
	dramaID := int64(6)
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&buyDramaDetailLog{
		DramaID:    dramaID,
		UserID:     userID,
		CreateTime: now.Unix(),
	})
	q.finishQuestBought()
	questField := drawpoint.QuestFieldFirstSeasonBought0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)

	// 测试购买第二季剧集正常加积分的情况
	dramaID = int64(7)
	q.Msg = newMessage(&buyDramaDetailLog{
		DramaID:    dramaID,
		UserID:     userID,
		CreateTime: now.Unix(),
	})
	q.finishQuestBought()
	questField = drawpoint.QuestFieldSecondSeasonBought0.Format()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventDramaPointQuests_finishQuestShare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID669

	// 测试 key 不匹配的情况
	q := eventDramaPointQuests{
		eventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestShareEvent()

	newMessage := func(i *shareDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyShareDetailLog1.Format(now.Unix()),
			Value: v,
		}
	}
	// 测试游客分享
	q.Msg = newMessage(&shareDetailLog{})
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	questField := drawpoint.QuestFieldShareEvent0.Format()
	err := service.Redis.Del(questKey, questField).Err()
	require.NoError(err)
	q.finishQuestShare()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试分享的类型不是活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID})
	q.finishQuestShare()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试分享的活动不是指定活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent})
	q.finishQuestShare()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Equal(0, progress)

	// 测试正常添加积分
	err = service.Redis.Del(questKey).Err()
	require.NoError(err)
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, CreateTime: now.Unix()})
	q.finishQuestShare()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)

	// 测试完成每日分享任务
	eventID = drawpoint.EventID614
	questKeyDaily := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	questKeyTotal := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err = service.Redis.Del(questKeyDaily, questKeyTotal).Err()
	require.NoError(err)
	q = eventDramaPointQuests{
		eventID: eventID,
		Msg:     newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, CreateTime: now.Unix()}),
	}
	q.finishQuestShare()
	questFieldTotal := drawpoint.QuestFieldShareEventDailyTotal0
	questFieldDaily := drawpoint.QuestFieldShareEventDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, questFieldTotal, questFieldDaily, eventID, userID, 1, 1)
}

func assertDailyProgress(t *testing.T, questFieldTotal, questFieldDaily string, eventID, userID, progressTotal, progressDaily int64) {
	assert := assert.New(t)
	require := require.New(t)

	// 断言更新总进度
	questKeyTotal := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	resultTotal, err := service.Redis.HGet(questKeyTotal, questFieldTotal).Int64()
	if err != nil {
		require.True(serviceredis.IsRedisNil(err))
	}
	assert.Equal(progressTotal, resultTotal)
	// 断言更新当日进度
	questKeyDaily := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	resultDaily, err := service.Redis.HGet(questKeyDaily, questFieldDaily).Int64()
	if err != nil {
		require.True(serviceredis.IsRedisNil(err))
	}
	assert.Equal(progressDaily, resultDaily)
}

func TestBuyDramaDetailLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试整剧付费
	msg := json.RawMessage(`{"user_id":1,"drama_id":30719,"pay_type":1,"origin":2,"create_time":1697532864}`)
	var info buyDramaDetailLog
	err := json.Unmarshal(msg, &info)
	require.NoError(err)
	expected := buyDramaDetailLog{
		UserID:     1,
		DramaID:    30719,
		PayType:    1,
		Origin:     2,
		CreateTime: 1697532864,
	}
	assert.Equal(expected, info)

	// 测试单集付费
	msg = json.RawMessage(`{"user_id":1,"drama_id":30719,"pay_type":1,"origin":2,"create_time":1697532864,"more":{"sound_ids":[1475733,1475732]}}`)
	err = json.Unmarshal(msg, &info)
	require.NoError(err)
	expected = buyDramaDetailLog{
		UserID:     1,
		DramaID:    30719,
		PayType:    1,
		Origin:     2,
		CreateTime: 1697532864,
		More: &buyDramaMore{
			SoundIDs: []int64{1475733, 1475732},
		},
	}
	assert.Equal(expected, info)
}

func TestEventCvfesQuests_finishQuestFollowUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID, followUserID1, followUserID2, followUserID3 := int64(12), int64(80), int64(81), int64(82)

	questKey := keys.KeyDrawPointUserQuests2.Format(drawpoint.EventID553, userID)
	questField := drawpoint.QuestFieldFollowUser0.Format()
	err := service.Redis.HDel(questKey, questField).Err()
	require.NoError(err)
	userIDsKey := drawpoint.KeyFollowUserIDs(drawpoint.EventID553, userID)
	err = service.Redis.Del(userIDsKey).Err()
	require.NoError(err)

	q := eventCvfesQuests{
		EventID: drawpoint.EventID553,
		Msg:     &databus.Message{},
		pc: &drawpoint.PointConfig{
			DrawPointStartTime: now.Unix() - 10,
			DrawPointEndTime:   now.Unix() + 10,
			EventID:            drawpoint.EventID553,
			EventEndTime:       now.Unix() + 10,
			DrawTaskConfig: drawpoint.DrawTaskConfig{
				FollowUserIDs: []int64{followUserID1, followUserID2},
			},
		},
	}

	newMessage := func(i *followUserInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyUserFollowLog1.Format(userID),
			Value: v,
		}
	}

	// 测试关注用户 ID 不是本次活动参演用户
	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: followUserID3,
		FollowType:   person.FollowTypeFollow,
		CreateTime:   now.Unix(),
	})
	q.finishQuestFollowUser()
	_, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))

	// 测试未全部关注时不加进度
	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: followUserID1,
		FollowType:   person.FollowTypeFollow,
		CreateTime:   now.Unix(),
	})
	q.finishQuestFollowUser()
	_, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))

	// 测试正常完成任务
	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: followUserID2,
		FollowType:   person.FollowTypeFollow,
		CreateTime:   now.Unix(),
	})
	q.finishQuestFollowUser()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)

	// 测试完成后取关不会减进度
	q.Msg = newMessage(&followUserInfo{
		UserID:       userID,
		FollowUserID: followUserID1,
		FollowType:   person.FollowTypeUnfollow,
		CreateTime:   now.Unix(),
	})
	q.finishQuestFollowUser()
	progress, err = service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventCvfesQuests_finishQuestBuyDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID, dramaID1, dramaID2 := int64(1200), int64(91), int64(9100)

	questKey := keys.KeyDrawPointUserQuests2.Format(drawpoint.EventID553, userID)
	questField := drawpoint.QuestFieldBuyDrama0.Format()
	err := service.Redis.HDel(questKey, questField).Err()
	require.NoError(err)

	q := eventCvfesQuests{
		EventID: drawpoint.EventID553,
		Msg:     &databus.Message{},
		pc: &drawpoint.PointConfig{
			DrawPointStartTime: now.Unix() - 10,
			DrawPointEndTime:   now.Unix() + 10,
			EventID:            drawpoint.EventID553,
			EventEndTime:       now.Unix() + 10,
		},
	}
	newMessage := func(i *buyDramaDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyBuyDramaDetailLog1.Format(userID),
			Value: v,
		}
	}

	// 测试购买非活动剧集不加进度
	q.Msg = newMessage(&buyDramaDetailLog{
		UserID:     userID,
		DramaID:    dramaID2,
		CreateTime: now.Unix(),
	})

	q.finishQuestBuyDrama()
	_, err = service.Redis.HGet(questKey, questField).Int()
	require.True(serviceredis.IsRedisNil(err))

	// 测试正常更新任务进度
	q.Msg = newMessage(&buyDramaDetailLog{
		UserID:     userID,
		DramaID:    dramaID1,
		CreateTime: now.Unix(),
	})
	q.finishQuestBuyDrama()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
}

func TestEventCvfesQuests_finishQuestShareBadge(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 2}, nil
	})
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID555
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试 key 不匹配的情况
	q := eventCvfesQuests{
		EventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestShareBadge()
	exists, err := service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	newMessage := func(i *shareDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyShareDetailLog1.Format(now.Unix()),
			Value: v,
		}
	}

	// 测试游客分享
	q.Msg = newMessage(&shareDetailLog{})
	q.finishQuestShareBadge()
	exists, err = service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	// 测试分享的类型不是活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID})
	q.finishQuestShareBadge()
	exists, err = service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	// 测试分享的活动不是指定活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent})
	q.finishQuestShareBadge()
	exists, err = service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	// 测试分享活动不是分享称号
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, URL: "https://www.missevan.com/mevent/555", CreateTime: now.Unix()})
	q.finishQuestShareBadge()
	exists, err = service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	// 测试完成任务
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, URL: "https://www.missevan.com/mevent/555?from_badge_id=1", CreateTime: now.Unix()})
	q.finishQuestShareBadge()
	questField := drawpoint.QuestFieldShareBadge0.Format()
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.EqualValues(1, progress)
}

func TestEventCvfesQuests_finishQuestShareEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试 key 不匹配的情况
	q := eventCvfesQuests{
		EventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestShareEvent()
	assertProgressKeysNotExist(t, eventID, userID)

	newMessage := func(i *shareDetailLog) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeyShareDetailLog1.Format(now.Unix()),
			Value: v,
		}
	}

	// 测试游客分享
	q.Msg = newMessage(&shareDetailLog{})
	q.finishQuestShareEvent()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试分享的类型不是活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID})
	q.finishQuestShareEvent()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试分享的活动不是指定活动的情况
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent})
	q.finishQuestShareEvent()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试完成任务
	q.Msg = newMessage(&shareDetailLog{UserID: userID, Type: ShareTypeEvent, ElementID: eventID, CreateTime: now.Unix()})
	q.finishQuestShareEvent()
	dailyQuestField := drawpoint.QuestFieldShareEvent1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	res, err := service.Redis.HGet(questKey, dailyQuestField).Int()
	require.NoError(err)
	assert.EqualValues(1, res)
	res, err = service.Redis.HGet(finishKey, drawpoint.ShareEventFinishNum).Int()
	require.NoError(err)
	assert.EqualValues(1, res)
}

func assertProgressKeysNotExist(t *testing.T, eventID, userID int64) {
	assert := assert.New(t)
	require := require.New(t)

	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	exists, err := service.Redis.Exists(questKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)
	exists, err = service.Redis.Exists(finishKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)
}

func TestEventCvfesQuests_finishQuestPlayVideo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()
	userID := int64(1)
	eventID := drawpoint.EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试 key 不匹配的情况
	q := eventCvfesQuests{
		EventID: eventID,
		Msg:     &databus.Message{},
	}
	q.finishQuestPlayVideo()
	assertProgressKeysNotExist(t, eventID, userID)

	newMessage := func(i *playSoundInfo) *databus.Message {
		v, err := json.Marshal(i)
		require.NoError(err)
		return &databus.Message{
			Key:   keys.DatabusKeySoundPlayLog2.Format("web", userID),
			Value: v,
		}
	}

	// 测试 user_id 为 0 的情况
	q.Msg = newMessage(&playSoundInfo{})
	q.finishQuestPlayVideo()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试 operation_type 不是开始播放的情况
	q.Msg = newMessage(&playSoundInfo{UserID: userID, OperationType: playlogOperationTypeEnd})
	q.finishQuestPlayVideo()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试收听的不是活动音频的情况
	q.Msg = newMessage(&playSoundInfo{UserID: userID, DramaID: 99999999})
	q.finishQuestPlayVideo()
	assertProgressKeysNotExist(t, eventID, userID)

	// 测试观看视频
	soundID := int64(1)
	q.Msg = newMessage(&playSoundInfo{UserID: userID, SoundID: soundID, OperationType: playlogOperationTypeStart, CreateTime: now.Unix()})
	q.finishQuestPlayVideo()
	questField := drawpoint.QuestFieldPlayVideo1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	progress, err := service.Redis.HGet(questKey, questField).Int()
	require.NoError(err)
	assert.Equal(1, progress)
	// 断言完成次数
	finishField := drawpoint.PlayVideoFinishNum
	finishNum, err := service.Redis.HGet(finishKey, finishField).Int()
	require.NoError(err)
	assert.Equal(1, finishNum)
}
