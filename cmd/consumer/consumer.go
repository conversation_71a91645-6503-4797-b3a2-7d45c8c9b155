package consumer

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/search"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/lancer"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/web"
)

// Command consumer
type Command struct {
	lock sync.Mutex
	msgs []*databus.Message
}

// Name of the Command
func (*Command) Name() string {
	return "consumer"
}

func (c *Command) initMsgSize() {
	c.msgs = make([]*databus.Message, 0, 1024)
}

func (c *Command) fetchSearchClicks() []*search.Click {
	var err error

	data := make([]*search.Click, 0, len(c.msgs))

	for _, m := range c.msgs {
		if !keys.DatabusKeySearchClicks1.MatchKey(m.Key) {
			m.Commit()
			continue
		}
		var click search.Click
		err = json.Unmarshal(m.Value, &click)
		if err != nil {
			logger.Errorf("search click json unmarshal error: %v", err)
			continue
		}
		data = append(data, &click)
		m.Commit()
	}

	return data
}

// Do action
func (c *Command) Do() {
	var (
		searchClicks   []*search.Click
		opensearchData []util.Dict
	)

	c.lock.Lock()

	msgSize := len(c.msgs)
	if msgSize == 0 {
		c.lock.Unlock()
		return
	}

	searchClicks = c.fetchSearchClicks()
	c.initMsgSize()

	c.lock.Unlock()

	for _, click := range searchClicks {
		eventType := -1
		if click.EventType != nil {
			eventType = *click.EventType
		}
		switch eventType {
		case search.EventTypeClickItem:
			if click.OpsRequestMisc != "" {
				searchClickRecord := getOpenSearchClickRecord(click)
				if searchClickRecord != nil {
					opensearchData = append(opensearchData, searchClickRecord)
				}
			}
		}
	}
	pushToOpenSearch(opensearchData)
}

// Run the Command
func (c *Command) Run(conf *config.Config) error {
	err := initService(&conf.Service)
	if err != nil {
		logger.Fatalf("service error: %v", err)
		return err
	}

	srv := newServer(conf)

	c.initMsgSize()

	g, ctx := errgroup.WithContext(context.Background())
	msgs := service.Databus.AppLogSub.Messages()

	lancerClient, err := lancer.NewClient(conf.Service.Lancer)
	if err != nil {
		logger.Fatalf("lancer new client error: %v", err)
		return err
	}
	g.Go(func() error {
		return lancer.Run(ctx, lancerClient)
	})

	g.Go(func() error {
		t := time.NewTicker(time.Second * 30)
		loop := true
		for loop {
			select {
			case m := <-msgs:
				handleDrawPoint(m)
				handleCollectUserInfo(m)
				handleUserSettings(m)
				handlePlayLimit(m)
				handleRecommendLog(m, lancerClient, conf.Service.Lancer.LogID.RecommendLog)
				// TODO: 消息 append 前应该先判断下 key
				c.lock.Lock()
				c.msgs = append(c.msgs, m)
				msgSize := len(c.msgs)
				c.lock.Unlock()
				if msgSize >= 1000 {
					c.Do()
				}
			case <-ctx.Done():
				loop = false
			case <-t.C:
				c.Do()
			}
		}
		return nil
	})
	g.Go(func() error {
		logger.Debugf("Listening and serving HTTP on %s\n", srv.Addr)
		return srv.ListenAndServe()
	})

	osCh := make(chan os.Signal, 1)
	signal.Notify(osCh, os.Interrupt, syscall.SIGTERM)
	select {
	case <-ctx.Done():
		err = ctx.Err()
		if err != nil {
			return err
		}
	case <-osCh:
	}

	shutdownServer(srv)
	_ = g.Wait()

	// TODO: degrade service
	_ = service.Databus.AppLogSub.Close()
	return nil
}

func newServer(conf *config.Config) *http.Server {
	r := web.NewRouterEngine(conf)
	srv := web.NewServer(conf, r)
	return srv
}

func shutdownServer(srv *http.Server) {
	closeCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(closeCtx); err != nil {
		logger.Errorf("Shutdown http server failed: %v", err)
	}
}
