package consumer

import (
	"encoding/json"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/lancer"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestHandleRecommendLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	log := recommendLog{
		UserID:      2985440,
		OS:          int(util.IOS),
		EquipID:     "7138b28b-98bb-40ca-a0a1-a5de70d6a788",
		Buvid:       "Z34D7138B28B98BB40CAA0A1A5DE70D6A788",
		Channel:     "",
		AppVersion:  "6.2.1",
		UserAgent:   "MissEvanApp/6.2.1 (iOS;18.1.1;iPhone13,3)",
		IP:          "**************",
		Network:     0,
		RefreshType: 1,
		RefreshNum:  3,
		CreateTime:  util.TimeNow().Unix(),
		TrackID:     "ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0",
		UserFeature: "{\"dp_blk_dp\":\"0\",\"invalid_dp_div_sid\":\"0\",\"invalid_dp_ogv_sid\":\"3\"}",
		ShowList: []recommendItem{
			{
				ID:        12345,
				Source:    "offline_icf",
				Pos:       0,
				Goto:      "sound",
				AvFeature: "{\"real_matchtype\":\"|real_matchtype 4\",\"source_len\":\"|source_len 1\",\"nonclick_show_region_num\":\"|nonclick_show_region_num 0\"}",
			},
			{
				ID:        67890,
				Source:    "offline_pegasus_i2v",
				Pos:       1,
				Goto:      "sound",
				AvFeature: "{\"real_matchtype\":\"|real_matchtype 4\",\"source_len\":\"|source_len 1\",\"nonclick_show_region_num\":\"|nonclick_show_region_num 0\"}",
			},
		},
		Env:   "uat",
		Scene: recommendSceneGuessYouLike,
	}

	logBytes, err := json.Marshal(log)
	require.NoError(err)

	endpoint, cleanup := lancer.RunTestServer()
	defer cleanup()

	client, err := lancer.NewClient(&lancer.Config{
		Endpoint: endpoint,
		Timeout:  "1s",
		Interval: "2s",
	})
	require.NoError(err)

	handleRecommendLog(&databus.Message{
		Key:   "recommend_exposure_log:2985440",
		Value: logBytes,
		Topic: "AppLog-T",
	}, client, "300123")

	content := client.BufferContent()
	assert.True(strings.HasPrefix(content, "300123"))
	assert.Contains(content, log.Buvid)
	assert.True(strings.HasSuffix(content, recommendSceneGuessYouLike+"\x03"))
}
