package consumer

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/lancer"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

const (
	recommendSceneGuessYouLike     = "guess_you_like"     // 猜你喜欢
	recommendSceneLiveRecommendTop = "live_recommend_top" // 直播推荐
)

type recommendLog struct {
	UserID      int64           `json:"user_id"`
	OS          int             `json:"os"`
	EquipID     string          `json:"equip_id"`
	Buvid       string          `json:"buvid"`
	Channel     string          `json:"channel"`
	AppVersion  string          `json:"app_version"`
	UserAgent   string          `json:"user_agent"`
	IP          string          `json:"ip"`
	Network     int             `json:"network"`
	RefreshType int             `json:"refresh_type"`
	RefreshNum  int             `json:"refresh_num"`
	CreateTime  int64           `json:"create_time"`
	TrackID     string          `json:"track_id"`
	UserFeature string          `json:"user_feature"`
	ShowList    []recommendItem `json:"showlist"`
	Env         string          `json:"env"`
	Scene       string          `json:"scene"`
}

type recommendItem struct {
	ID        int64  `json:"id"`
	Goto      string `json:"goto"`
	Pos       int    `json:"pos"`
	Source    string `json:"source"`
	AvFeature string `json:"av_feature"`
	Attr      int    `json:"attr"`
	OldPos    *int   `json:"old_pos,omitempty"`
}

type locationInfo struct {
	CountryName string `json:"country_name"`
	CountryCode string `json:"country_code"`
	RegionName  string `json:"region_name"`
	CityName    string `json:"city_name"`
	ISP         string `json:"isp"`
}

// 接入文档：https://info.bilibili.co/pages/viewpage.action?pageId=94017407
// lancer 集成任务：https://berserker.bilibili.co/#/lancer/list/job/173286393420201
func handleRecommendLog(m *databus.Message, client *lancer.Client, logID string) {
	if !keys.DatabusKeyRecommendExposureLog1.MatchKey(m.Key) {
		return
	}

	var recommendLog recommendLog
	var err error
	err = json.Unmarshal(m.Value, &recommendLog)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}

	// IP 归属地
	record, err := service.GeoIP.Get(recommendLog.IP, serviceutil.IPIPLanguageEN)
	var locationInfoStr string
	if err == nil {
		locationInfoBytes, err := json.Marshal(locationInfo{
			CountryName: record.CountryName(),
			CountryCode: record.CountryIsoCode(),
			RegionName:  record.RegionName(),
			CityName:    record.CityName(),
			ISP:         record.ISPName(),
		})
		if err == nil {
			locationInfoStr = string(locationInfoBytes)
		} else {
			logger.WithField("ip", recommendLog.IP).Error(err)
			// PASS
		}
	} else {
		logger.WithField("ip", recommendLog.IP).Error(err)
		// PASS
	}

	// 卡片列表
	var showList string
	showListBytes, err := json.Marshal(recommendLog.ShowList)
	if err == nil {
		showList = string(showListBytes)
	} else {
		logger.Error(err)
		// PASS
	}

	// TODO: 生产端（missevan-app）上线后去除此兼容
	if recommendLog.Scene == "" {
		recommendLog.Scene = recommendSceneGuessYouLike
	}

	err = client.Collect(
		logID,
		recommendLog.UserID,
		recommendLog.OS,
		recommendLog.EquipID,
		recommendLog.Buvid,
		recommendLog.Channel,
		recommendLog.AppVersion,
		recommendLog.UserAgent,
		recommendLog.IP,
		locationInfoStr,
		recommendLog.Network,
		recommendLog.RefreshType,
		recommendLog.RefreshNum,
		recommendLog.CreateTime,
		recommendLog.TrackID,
		recommendLog.UserFeature,
		showList,
		recommendLog.Env,
		recommendLog.Scene,
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
