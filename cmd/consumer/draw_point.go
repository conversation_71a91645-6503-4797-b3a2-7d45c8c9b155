package consumer

import (
	"encoding/json"
	"strings"
	"sync"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drawpoint"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/missevan/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 分享类型
const (
	ShareTypeSound = iota + 1
	ShareTypeHypnosis
	ShareTypeTheatre
	ShareTypeEvent
	ShareTypeLive
)

// 追剧类型
const (
	SubscribeDramaTypeCancel = iota
	SubscribeDramaTypeSubscribe
)

// 播放行为类型，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=109686026
const (
	playlogOperationTypeStart    = 1 // 开始播放，由未播状态变为播放状态
	playlogOperationTypePause    = 2 // 音频播放暂停
	playlogOperationTypeEnd      = 3 // 音频播放结束
	playlogWebOperationTypeClose = 4 // Web 音频播放时浏览器关闭
	playlogOperationTypeContinue = 5 // 音频继续播放
	playlogOperationTypeCycle    = 6 // 音频播放过程中定时上报
	playlogOperationType10s      = 7 // 音频播放时长满 10s（播放音频期间仅会上报一次）
)

// theatreOfficialUserID 盲盒剧场官方账号 ID
const theatreOfficialUserID int64 = 13689177

// yglOfficialUserID 摇光录站内账号 ID
const yglOfficialUserID int64 = 22125072

// 小鱼干操作类型，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=53188232
const (
	PointTypeNewUserRegister = iota + 1 // 新用户注册
	PointTypeTaskSign                   // 日常任务 - 签到
	PointTypeTaskGetPoint               // 日常任务 - 摸鱼
	PointTypeTaskComment                // 日常任务 - 评论
	PointTypeTaskTs                     // 日常任务 - 投食
	PointTypeSoundTs                    // 音频投食
	PointTypeTaskShare                  // 日常任务 - 分享
	PointTypeUpdateUserInfo             // 修改用户信息（用户昵称、用户封面图等）
	PointTypeAdminGrant                 // 后台管理员发放
	PointTypeActivity                   // 活动
	PointTypeTaskPatchSign              // 日常任务 - 补签
)

func handleDrawPoint(msg *databus.Message) {
	// 摇光录分享抽奖活动
	// NOTICE: 摇光录分享和关注站内号加积分任务已经废弃，但是活动 ID 还在活动中心配置活动页面对外使用，所以这里注释掉
	// q1 := eventYglQuest{
	// 	eventID: drawpoint.EventID400,
	// 	Msg:     msg,
	// }
	// q1.finishQuestShare()
	// q1.finishQuestFollowUser()

	// 盲盒剧场抽奖活动第六期积分任务
	// q2 := eventTheatreQuests{
	// 	EventID: drawpoint.EventIDTheatreVI,
	// 	Msg:     msg,
	// }
	// q2.finishQuestFollowUser()
	// q2.finishQuestSubscribeOneDrama()
	// q2.finishQuestSubscribeSeveralDrama()
	// q2.finishQuestPlayedDuration()

	// 声优纪活动积分任务
	q3 := eventCvfesQuests{
		EventID: drawpoint.EventID553,
		Msg:     msg,
	}
	q3.finishQuestFollowUser()
	q3.finishQuestBuyDrama()
	q3.finishQuestShareBadge()
	q3.finishQuestShareEvent()
	q3.finishQuestPlayVideo()

	// 669 乱世权臣剧集活动
	q4 := eventDramaPointQuests{
		eventID: drawpoint.EventID669,
		Msg:     msg,
	}
	q4.finishQuestShare()
	q4.finishQuestBuyOneDrama()
	q4.finishQuestPlayedDuration()
}

// 需求文档：https://info.missevan.com/pages/viewpage.action?pageId=68454452
type event205Quests struct {
	Msg  *databus.Message
	pc   *drawpoint.PointConfig
	once sync.Once
}

func (*event205Quests) eventID() int64 {
	return drawpoint.EventIDMaoMaoStar
}

func (q *event205Quests) PointConfig() *drawpoint.PointConfig {
	q.once.Do(func() {
		var err error
		q.pc, err = drawpoint.FindPointConfig(q.eventID())
		if err != nil {
			logger.Error(err)
			return
		}
	})
	return q.pc
}

func (q *event205Quests) finishQuestUserPoint() {
	if !keys.DatabusKeyUserPointDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info struct {
		Type       int64 `json:"type"`
		UserID     int64 `json:"user_id"`
		Num        int64 `json:"num"`
		CreateTime int64 `json:"create_time"`
	}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	// 只有用户投食（type 6，num 负数）触发任务
	if info.UserID == 0 || info.Type != 6 || info.Num >= 0 {
		return
	}
	pc := q.PointConfig()
	if pc == nil {
		return
	}
	logTime := time.Unix(info.CreateTime, 0)
	if !pc.InTimeRange(logTime) {
		return
	}
	point, err := drawpoint.FinishMaoMaoQuestUserPoint(pc, info.UserID, logTime)
	if err != nil {
		logger.Error(err)
		return
	}

	if point == 0 {
		return
	}

	dp := drawpoint.Param{
		EventID:     q.eventID(),
		UserID:      info.UserID,
		CurrentTime: pc.NowUnix(),
		DailyUpdate: pc.DrawPointDailyUpdate,
	}
	_, err = dp.AddDrawPoint(point, pc.ExpireDuration())
	if err != nil {
		logger.Error(err)
		return
	}
}

func (q *event205Quests) finishQuestsShare() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info struct {
		Type       int    `json:"type"`    // 分享类型
		UserID     int64  `json:"user_id"` // 分享不一定要登录用户
		ElementID  int64  `json:"element_id"`
		URL        string `json:"url"`
		CreateTime int64  `json:"create_time"`
	}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.UserID == 0 {
		return
	}
	pc := q.PointConfig()
	if pc == nil {
		return
	}
	logTime := time.Unix(info.CreateTime, 0)
	if !pc.InTimeRange(logTime) {
		return
	}
	var addPoint int64
	switch info.Type {
	case 0: // 老版本分享音频、直播间，新版本分享直播间
		// 新版本分享，只能完成直播间
		if info.URL != "" {
			if strings.Contains(info.URL, ".missevan.com/live/172227909") {
				addPoint, err = drawpoint.FinishMaoMaoQuestShareLive(pc, info.UserID, logTime)
				if err != nil {
					logger.Error(err)
					return
				}
			}
			break
		}
		// 老版本分享，同时可以完成分享音频和直播间
		// 分享直播间
		tmpPoint, err := drawpoint.FinishMaoMaoQuestShareLive(pc, info.UserID, logTime)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if tmpPoint != 0 {
			addPoint += tmpPoint
		}
		// 分享音频
		tmpPoint, err = drawpoint.FinishMaoMaoQuestShareSound(pc, info.UserID, logTime)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if tmpPoint != 0 {
			addPoint += tmpPoint
		}
	case 1: // 新版本分享音频
		addPoint, err = drawpoint.FinishMaoMaoQuestShareSound(pc, info.UserID, logTime)
		if err != nil {
			logger.Error(err)
			return
		}
	case 4: // 分享活动
		// 分享活动只有 missevan-web 的按钮，不需要考虑客户端兼容
		// 199: 712 主会场活动 ID
		if info.ElementID == 199 {
			addPoint, err = drawpoint.FinishMaoMaoQuestShareEvent(pc, info.UserID, logTime)
			if err != nil {
				logger.Error(err)
				return
			}
		}
	case 5: // 分享直播间，线上暂无
		if info.ElementID == 172227909 {
			addPoint, err = drawpoint.FinishMaoMaoQuestShareLive(pc, info.UserID, logTime)
			if err != nil {
				logger.Error(err)
				return
			}
		}
	}
	if addPoint == 0 {
		return
	}

	dp := drawpoint.Param{
		EventID:     q.eventID(),
		UserID:      info.UserID,
		CurrentTime: pc.NowUnix(),
		DailyUpdate: pc.DrawPointDailyUpdate,
	}
	_, err = dp.AddDrawPoint(addPoint, pc.ExpireDuration())
	if err != nil {
		logger.Error(err)
		return
	}
}

type eventTheatreQuests struct {
	EventID int64

	Msg  *databus.Message
	pc   *drawpoint.PointConfig
	once sync.Once
}

func (q *eventTheatreQuests) PointConfig(now time.Time) *drawpoint.PointConfig {
	q.once.Do(func() {
		var err error
		q.pc, err = drawpoint.FindPointConfig(q.EventID)
		if err != nil {
			logger.Error(err)
			return
		}
	})
	if q.pc == nil || !q.pc.InTimeRange(now) {
		return nil
	}
	return q.pc
}

// followUserInfo 关注用户 databus 信息结构体
type followUserInfo struct {
	UserID       int64 `json:"user_id"`        // 当前用户 ID
	FollowUserID int64 `json:"follow_user_id"` // 被关注用户 ID
	FollowFrom   int   `json:"follow_from"`    // 关注来源
	FollowType   int   `json:"follow_type"`    // 类型。0：关注；1：取关
	CreateTime   int64 `json:"create_time"`    // 创建时间。单位：秒
}

// finishQuestFollowUser 完成积分任务 - 关注剧场官方账号
func (q *eventTheatreQuests) finishQuestFollowUser() {
	if !keys.DatabusKeyUserFollowLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := followUserInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.FollowType != person.FollowTypeFollow || info.FollowUserID != theatreOfficialUserID {
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil {
		return
	}
	err = drawpoint.FinishTheatreQuestFollowUser(pc, q.EventID, info.UserID, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// TODO: 补充对其他任务类型的支持

// finishQuestSubscribeOneDrama 完成积分任务 - 追剧本次上新的任意一部剧集
func (q *eventTheatreQuests) finishQuestSubscribeOneDrama() {
	if !keys.DatabusKeySubscribeDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := subscribeDramaInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.Type != SubscribeDramaTypeSubscribe || !dramainfo.IsTheatreNewDrama(info.DramaID) {
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil {
		return
	}
	err = drawpoint.FinishTheatreQuestSubscribeOneDrama(pc, q.EventID, info.UserID, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// finishQuestSubscribeSeveralDrama 完成积分任务 - 追剧剧场内本次上新的剧集达到指定数量
func (q *eventTheatreQuests) finishQuestSubscribeSeveralDrama() {
	if !keys.DatabusKeySubscribeDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := subscribeDramaInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.Type != SubscribeDramaTypeSubscribe || !dramainfo.IsTheatreNewDrama(info.DramaID) {
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil {
		return
	}
	err = drawpoint.FinishTheatreQuestSubscribeSeveralDrama(pc, q.EventID, info.UserID, info.DramaID, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// eventDramaPointQuests 剧集抽奖活动配置
type eventDramaPointQuests struct {
	eventID int64

	Msg  *databus.Message
	pc   *drawpoint.PointConfig
	once sync.Once
}

// PointConfig 初始化剧集抽奖活动配置
func (q *eventDramaPointQuests) PointConfig(now time.Time) *drawpoint.PointConfig {
	q.once.Do(func() {
		var err error
		q.pc, err = drawpoint.FindPointConfig(q.eventID)
		if err != nil {
			logger.Error(err)
			return
		}
	})
	if q.pc == nil || !q.pc.InTimeRange(now) {
		return nil
	}
	return q.pc
}

// subscribeDramaInfo 追剧 databus 消息内容
type subscribeDramaInfo struct {
	DramaID    int64 `json:"drama_id"`
	UserID     int64 `json:"user_id"`
	Type       int   `json:"type"`
	CreateTime int64 `json:"create_time"` // 创建时间戳，单位：秒
}

// finishQuestSubscribe 完成积分任务 - 订阅活动剧集
func (q *eventDramaPointQuests) finishQuestSubscribe() {
	if !keys.DatabusKeySubscribeDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := subscribeDramaInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.Type != SubscribeDramaTypeSubscribe {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil || !pc.IsMatchEventDrama(info.DramaID) {
		return
	}
	err = drawpoint.FinishQuestSubscribe(pc, q.eventID, info.UserID, createTime)
	if err != nil {
		logger.Error(err)
		return
	}
}

// playSoundInfo 音频播放 databus 消息内容
type playSoundInfo struct {
	SoundID       int64              `json:"sound_id"`
	DramaID       int64              `json:"drama_id"`
	UserID        int64              `json:"user_id"`
	UserAgent     string             `json:"user_agent"`
	OperationType int                `json:"operation_type"`
	StartTime     util.TimeUnixMilli `json:"start_time"`  // 记录每次开始或继续播放的时间戳，单位：毫秒
	EndTime       util.TimeUnixMilli `json:"end_time"`    // 每次行为结束记录的时间戳，单位：毫秒
	CreateTime    int64              `json:"create_time"` // 创建时间戳，单位：秒
	PayType       int                `json:"pay_type"`    // 音频付费类型
	EquipID       string             `json:"equip_id"`    // 设备号
	Vip           int                `json:"vip"`         // 会员音频状态
}

// finishQuestPlayedDuration 完成积分任务 - 收听活动剧集
func (q *eventDramaPointQuests) finishQuestPlayedDuration() {
	if !keys.DatabusKeySoundPlayLog2.MatchKey(q.Msg.Key) {
		return
	}
	info := playSoundInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	createTimeMilli := util.NewTimeUnixMilli(time.Unix(info.CreateTime, 0))
	if info.EndTime == 0 {
		// WORKAROUND: App 未上报 end_time，暂时使用 create_time 作为 end_time
		info.EndTime = createTimeMilli
	} else if (serviceutil.AbsInt(int64(info.EndTime - createTimeMilli))) > 600000 {
		// 若 end_time 与 create_time 绝对值相差超过 10 分钟，视作异常数据，直接返回（异常数据与活动收听任务时长无关）
		return
	}
	// NOTICE: 播放时长使用 end_time 与 start_time 的差值，避免多次暂停时，直接使用上报的 played_duration 出现重复累计问题
	// FIXME: 定时上报时 end_time 与 start_time 的差值固定为一次定时上报周期时长，后续客户端及前端增加上报 play_start_time 字段后，
	// 定时上报应使用 play_start_time 替代 start_time，以计算最近一次开始播放（包含开始播放和继续播放）至本次心跳上报的总时长
	playedDuration := int64(info.EndTime - info.StartTime)
	if !(info.UserID != 0 && playedDuration > 0 &&
		// 播放暂停或结束时或 Web 在播放音频过程中关闭浏览器时或在定时上报时或播放满 10s 上报时累计播放时长，
		// 定时上报和播放满 10s 上报支持的版本为客户端双端版本 >= 6.1.8 及网页端
		(info.OperationType == playlogOperationTypePause || info.OperationType == playlogOperationTypeEnd ||
			info.OperationType == playlogWebOperationTypeClose || info.OperationType == playlogOperationTypeCycle ||
			info.OperationType == playlogOperationType10s)) {
		return
	}
	now := info.EndTime.ToTime()
	pc := q.PointConfig(now)
	if pc == nil || !pc.IsMatchEventDrama(info.DramaID) {
		return
	}
	today := util.BeginningOfDay(now)
	// FIXME: todayUnixMilli 在活动时间有偏移时可能出现问题
	todayUnixMilli := util.NewTimeUnixMilli(today)
	yesterdayPlayedDuration := int64(0)
	todayPlayedDuration := playedDuration
	if info.StartTime < todayUnixMilli {
		// 若开始时间在零点前，则需要将零点前后的播放时长分 2 天累计（忽略跨 3 天及以上的极端情况）
		drawPointStartTimeMilli := util.NewTimeUnixMilli(time.Unix(pc.DrawPointStartTime, 0))
		// 若开始时间在积分活动开始后，则前一天播放时长为当天零点时间减去开始时间
		// timeline 示意 -----draw_point_time-----start_time-----today-----end_time---->
		// 若开始时间在积分活动开始前，则前一天播放时长为当天零点时间减去积分活动开始时间
		// timeline 示意 -----start_time-----draw_point_time-----today-----end_time---->
		yesterdayPlayedDuration = int64(todayUnixMilli - util.TimeUnixMilli(util.MaxInt64(int64(info.StartTime), int64(drawPointStartTimeMilli))))
		todayPlayedDuration = int64(info.EndTime - todayUnixMilli)
	}
	if yesterdayPlayedDuration > 0 {
		err = drawpoint.FinishQuestPlayDuration(pc, q.eventID, info.UserID, yesterdayPlayedDuration, today.Add(-1*time.Second))
		if err != nil {
			logger.Error(err)
			// PASS: 出错时忽略，避免影响后一天的时长计入
		}
	}
	err = drawpoint.FinishQuestPlayDuration(pc, q.eventID, info.UserID, todayPlayedDuration, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// userPointDetailLog 音频投食 databus 消息内容
type userPointDetailLog struct {
	Type       int            `json:"type"`
	UserID     int64          `json:"user_id"`
	Num        int64          `json:"num"`
	CreateTime int64          `json:"create_time"`
	More       *userPointMore `json:"more,omitempty"`
}

// userPointMore 投食 databus 日志更多信息
type userPointMore struct {
	SoundID int64 `json:"sound_id"`
}

// finishQuestUserPoint 完成积分任务 - 投食活动剧集下的任意音频
func (q *eventDramaPointQuests) finishQuestUserPoint() {
	if !keys.DatabusKeyUserPointDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var log userPointDetailLog
	err := json.Unmarshal(q.Msg.Value, &log)
	if err != nil {
		logger.Error(err)
		return
	}
	// 只有用户投食（type 6，num 负数）触发任务
	if log.UserID == 0 || log.Type != PointTypeSoundTs || log.Num >= 0 || log.More == nil {
		return
	}
	now := time.Unix(log.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil || !dramaepisode.IsDramaSound(q.pc.DrawTaskConfig.DramaID, log.More.SoundID) {
		return
	}
	err = drawpoint.FinishQuestUserPoint(pc, q.eventID, log.UserID, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// shareDetailLog 分享 databus 消息内容
type shareDetailLog struct {
	Type       int    `json:"type"` // 分享类型
	UserID     int64  `json:"user_id"`
	ElementID  int64  `json:"element_id"`
	URL        string `json:"url"` // 分享链接，消息里的是未转义的 URL
	CreateTime int64  `json:"create_time"`
}

// finishQuestShareEvent 完成积分任务 - 分享剧集抽奖活动
func (q *eventDramaPointQuests) finishQuestShareEvent() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info shareDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 || info.Type != ShareTypeEvent || info.ElementID != q.eventID {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil {
		return
	}
	err = drawpoint.FinishQuestShareEvent(pc, q.eventID, info.UserID, createTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

// eventYglQuest 摇光录分享活动配置
type eventYglQuest struct {
	eventID int64

	Msg  *databus.Message
	pc   *drawpoint.PointConfig
	once sync.Once
}

// PointConfig 初始化摇光录分享活动配置
func (q *eventYglQuest) PointConfig(now time.Time) *drawpoint.PointConfig {
	q.once.Do(func() {
		var err error
		q.pc, err = drawpoint.FindPointConfig(q.eventID)
		if err != nil {
			logger.Error(err)
			// PASS
			return
		}
	})
	if q.pc == nil || !q.pc.InTimeRange(now) {
		return nil
	}
	return q.pc
}

// finishQuestShare 完成积分任务 - 分享摇光录活动
func (q *eventYglQuest) finishQuestShare() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info shareDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 || info.Type != ShareTypeEvent || info.ElementID != q.eventID {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil {
		return
	}
	err = drawpoint.FinishYglQuestShare(pc, q.eventID, info.UserID, createTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

// finishQuestFollowUser 完成积分任务 - 关注摇光录站内账号
func (q *eventYglQuest) finishQuestFollowUser() {
	if !keys.DatabusKeyUserFollowLog1.MatchKey(q.Msg.Key) {
		return
	}

	var info followUserInfo
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}

	if info.FollowType != person.FollowTypeFollow || info.FollowUserID != yglOfficialUserID {
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil {
		return
	}
	err = drawpoint.FinishYglQuestFollowUser(pc, q.eventID, info.UserID, now)
	if err != nil {
		logger.Error(err)
		return
	}
}

// finishQuestSeasonDramaSubscribe 完成积分任务 - 订阅一部指定剧集
func (q *eventDramaPointQuests) finishQuestSubscribeOneDrama() {
	if !keys.DatabusKeySubscribeDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := subscribeDramaInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.Type != SubscribeDramaTypeSubscribe {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil || !pc.IsMatchEventDrama(info.DramaID) {
		return
	}
	err = drawpoint.FinishQuestSubscribeOneDrama(pc, q.eventID, info.UserID, info.DramaID, createTime)
	if err != nil {
		logger.Error(err)
		return
	}
}

// buyDramaDetailLog 购买剧集 databus 消息内容
type buyDramaDetailLog struct {
	UserID     int64         `json:"user_id"`
	DramaID    int64         `json:"drama_id"`
	PayType    int64         `json:"pay_type"`
	Origin     int64         `json:"origin"`
	CreateTime int64         `json:"create_time"`
	More       *buyDramaMore `json:"more,omitempty"`
}

// buyDramaMore 购买剧集 databus 日志更多信息
type buyDramaMore struct {
	SoundIDs []int64 `json:"sound_ids"`
}

// finishQuestBought 完成积分任务 - 购买活动剧集
func (q *eventDramaPointQuests) finishQuestBought() {
	if !keys.DatabusKeyBuyDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info buyDramaDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil || !pc.IsMatchEventDrama(info.DramaID) {
		return
	}
	err = drawpoint.FinishQuestSeasonDramaBought(pc, q.eventID, info.UserID, info.DramaID, createTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

// finishQuestShare 完成积分任务 - 分享剧集抽奖活动
func (q *eventDramaPointQuests) finishQuestShare() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info shareDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 || info.Type != ShareTypeEvent || info.ElementID != q.eventID {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil {
		return
	}
	err = drawpoint.FinishQuestShare(pc, q.eventID, info.UserID, createTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

// finishQuestBuyOneDrama 完成积分任务 - 购买一部指定剧集
func (q *eventDramaPointQuests) finishQuestBuyOneDrama() {
	if !keys.DatabusKeyBuyDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info buyDramaDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil || !pc.IsMatchEventDrama(info.DramaID) {
		return
	}
	err = drawpoint.FinishQuestBuyOneDrama(pc, q.eventID, info.UserID, info.DramaID, createTime)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

// eventCvfesQuests 声优纪活动
type eventCvfesQuests struct {
	EventID int64

	Msg  *databus.Message
	pc   *drawpoint.PointConfig
	once sync.Once
}

// finishQuestFollowUser 完成积分任务 - 关注参演嘉宾
func (q *eventCvfesQuests) finishQuestFollowUser() {
	if !keys.DatabusKeyUserFollowLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := followUserInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil || !util.HasElem(pc.DrawTaskConfig.FollowUserIDs, info.FollowUserID) {
		return
	}
	err = drawpoint.CvfesFinishQuestFollowUser(pc, q.EventID, info.UserID, info.FollowUserID, info.FollowType, now)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  info.UserID,
			"event_id": q.EventID,
		}).Error(err)
		// PASS
		return
	}
}

func (q *eventCvfesQuests) PointConfig(now time.Time) *drawpoint.PointConfig {
	q.once.Do(func() {
		var err error
		q.pc, err = drawpoint.FindPointConfig(q.EventID)
		if err != nil {
			logger.Error(err)
			return
		}
	})
	if q.pc == nil || !q.pc.InTimeRange(now) {
		return nil
	}
	return q.pc
}

// finishQuestBuyDrama 完成积分任务 - 购买剧集
func (q *eventCvfesQuests) finishQuestBuyDrama() {
	if !keys.DatabusKeyBuyDramaDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	info := buyDramaDetailLog{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil || !util.HasElem(pc.DrawTaskConfig.DramaIDs, info.DramaID) {
		return
	}
	err = drawpoint.CvfesFinishQuestBuyDrama(pc, q.EventID, info.UserID, now)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  info.UserID,
			"event_id": q.EventID,
		}).Error(err)
		// PASS
		return
	}
}

// finishQuestShareEvent 完成积分任务 - 分享十周年称号
func (q *eventCvfesQuests) finishQuestShareBadge() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info shareDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 || info.Type != ShareTypeEvent || info.ElementID != drawpoint.EventID555 || !drawpoint.IsShareBadge(info.URL) {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil {
		return
	}
	err = drawpoint.CvfesFinishQuestShareBadge(pc, q.EventID, info.UserID, createTime)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  info.UserID,
			"event_id": q.EventID,
		}).Error(err)
		// PASS
		return
	}
}

// finishQuestShareEvent 完成积分任务 - 分享主会场活动页
func (q *eventCvfesQuests) finishQuestShareEvent() {
	if !keys.DatabusKeyShareDetailLog1.MatchKey(q.Msg.Key) {
		return
	}
	var info shareDetailLog
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if info.UserID == 0 || info.Type != ShareTypeEvent || info.ElementID != q.EventID {
		return
	}
	createTime := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(createTime)
	if pc == nil {
		return
	}
	err = drawpoint.CvfesFinishQuestShareEvent(pc, q.EventID, info.UserID, createTime)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  info.UserID,
			"event_id": q.EventID,
		}).Error(err)
		// PASS
		return
	}
}

// finishQuestPlayVideo 完成积分任务 - 观看声优纪音视频
func (q *eventCvfesQuests) finishQuestPlayVideo() {
	if !keys.DatabusKeySoundPlayLog2.MatchKey(q.Msg.Key) {
		return
	}
	info := playSoundInfo{}
	err := json.Unmarshal(q.Msg.Value, &info)
	if err != nil {
		logger.Error(err)
		return
	}
	if info.UserID == 0 || info.OperationType != playlogOperationTypeStart {
		return
	}
	now := time.Unix(info.CreateTime, 0)
	pc := q.PointConfig(now)
	if pc == nil || !util.HasElem(pc.DrawTaskConfig.SoundIDs, info.SoundID) {
		return
	}
	err = drawpoint.CvfesFinishQuestPlayVideo(pc, q.EventID, info.UserID, now)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  info.UserID,
			"event_id": q.EventID,
			"sound_id": info.SoundID,
		}).Error(err)
		// PASS
		return
	}
}
