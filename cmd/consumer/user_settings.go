package consumer

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
)

func handleUserSettings(msg *databus.Message) {
	if !keys.DatabusKeySetBirthdayLog1.MatchKey(msg.Key) {
		return
	}
	us := userSettings{msg: msg}
	us.giveLiveUserBirthdayPriv()
}

type userSettings struct {
	msg *databus.Message

	birthdayMsg birthdayMsg
}

type birthdayMsg struct {
	UserID        int64  `json:"user_id"`
	Birthday      string `json:"birthday"`
	BirthdateMMDD string `json:"birthdate_mmdd"`
}

func (us *userSettings) giveLiveUserBirthdayPriv() {
	err := json.Unmarshal(us.msg.Value, &us.birthdayMsg)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	err = userapi.SendUserBirthdayPriv(us.birthdayMsg.UserID, us.birthdayMsg.BirthdateMMDD)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
