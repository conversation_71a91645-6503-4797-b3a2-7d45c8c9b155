package consumer

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
)

func TestUserSettings_giveLiveUserBirthdayPriv(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	success := false
	cancel := mrpc.SetMock(userapi.URILiveUserBirthdayPriv, func(input interface{}) (output interface{}, err error) {
		success = true
		return "success", nil
	})
	defer cancel()

	info := birthdayMsg{
		UserID:        1919,
		BirthdateMMDD: "0126",
	}
	rawMsg, err := json.Marshal(info)
	require.NoError(err)
	msg := &databus.Message{
		Key:   keys.DatabusKeySetBirthdayLog1.Format(info.UserID),
		Value: rawMsg,
	}
	us := userSettings{msg: msg}

	us.giveLiveUserBirthdayPriv()
	assert.True(success)
}
