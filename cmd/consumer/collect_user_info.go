package consumer

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

func handleCollectUserInfo(msg *databus.Message) {
	cu := collectUserIP{msg: msg}
	cu.updateUserAddendumIP()
}

type collectUserIP struct {
	msg *databus.Message
}

type collectUserIPInfo struct {
	UserID     int64  `json:"user_id"`
	IP         string `json:"ip"`
	CreateTime int64  `json:"create_time"`
	From       int    `json:"from"` // 用户信息的来源: 1 Web; 2 App
}

// updateUserAddendumIP 更新用户副表的 IP 和 IP 详情
func (c *collectUserIP) updateUserAddendumIP() {
	if !keys.DatabusKeyCollectUserIPLog1.MatchKey(c.msg.Key) {
		return
	}
	var info collectUserIPInfo
	err := json.Unmarshal(c.msg.Value, &info)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	// 丢弃掉非正常的数据
	if info.UserID == 0 || info.IP == "" {
		return
	}
	ipDetailParams, err := user.ParseIPAndGetIPDetailParams(info.IP, info.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	err = user.SaveIPAndIPDetail(info.UserID, info.IP, ipDetailParams)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}
