package cmd

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/config"
)

type testCommand struct {
}

func (*testCommand) Name() string {
	return "test"
}

func (c *testCommand) Run(conf *config.Config) error {
	return errors.New("ok")
}

func TestRun(t *testing.T) {
	assert := assert.New(t)

	commandMap := register(commands)
	var cmds []string
	for k := range commandMap {
		cmds = append(cmds, k)
	}
	assert.ElementsMatch([]string{"rpc", "web", "consumer"}, cmds)

	commandMap = register([]Command{&testCommand{}})
	config := config.BuildDefaultConf()
	for _, v := range commandMap {
		assert.Equal("ok", v.Run(&config).Error())
	}
}
