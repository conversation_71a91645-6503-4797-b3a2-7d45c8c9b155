package models

import (
	"github.com/jinzhu/gorm"
)

// MAlbum model
// TODO: add fields
type MAlbum struct {
	ID           int64  `gorm:"column:id"`
	Title        string `gorm:"column:title"`
	UserID       int64  `gorm:"column:user_id"`
	UserName     string `gorm:"column:username"`
	Refined      int    `gorm:"column:refined"`
	CommentCount int64  `gorm:"column:comment_count"`
}

// refined 定义
const (
	// RefinedRefined 加精
	RefinedRefined = 1
	// RefinedNotSetCover 音单未设置自定义封面图
	RefinedNotSetCover = 2
	// RefinedPrivateAlbum 私有音单
	RefinedPrivateAlbum = 5
)

// TableName returns the table name of the MAlbum model
func (MAlbum) TableName() string {
	return "m_album"
}

// TODO: AfterFind

// IncrCommentCount increments the comment count of the id album
func (m *MAlbum) IncrCommentCount(db *gorm.DB, id int64) error {
	return db.Table(m.TableName()).Where("id = ?", id).
		UpdateColumn("comment_count", gorm.Expr("comment_count + 1")).Error
}

// IncrSubCommentCount 实现 Commentable 接口，子评论数 +1
func (m *MAlbum) IncrSubCommentCount(db *gorm.DB, id int64) error {
	panic("MAlbum does not support IncrSubCommentCount")
}
