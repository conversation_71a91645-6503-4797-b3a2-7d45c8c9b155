package voice

// 卡片类型，0：普通卡片；1：节日卡；2：剧场卡；3：免费卡；4：热度福利；5：求签普通卡；6：求签小剧场
const (
	SpecialNormal         = 0
	SpecialFestival       = 1
	SpecialEpisode        = 2
	SpecialFree           = 3
	SpecialHotcard        = 4
	SpecialOmikuji        = 5
	SpecialOmikujiEpisode = 6
)

// Card model
type Card struct {
	ID            int64  `gorm:"column:id" json:"id"`
	CreateTime    int64  `gorm:"column:create_time" json:"create_time"`
	ModifiedTime  int64  `gorm:"column:modified_time" json:"modified_time"`
	Title         string `gorm:"column:title" json:"title"`
	UniqueAlias   string `gorm:"column:unique_alias" json:"unique_alias"`
	Icon          string `gorm:"column:icon" json:"icon"`
	Cover         string `gorm:"column:cover" json:"cover"`
	PlayCover     string `gorm:"column:play_cover" json:"play_cover"`
	Subtitles     string `gorm:"column:subtitles" json:"subtitles"`
	Intro         string `gorm:"column:intro" json:"intro"`
	Duration      int64  `gorm:"column:duration" json:"duration"`
	Voice         string `gorm:"column:voice" json:"voice"`
	Level         int    `gorm:"column:level" json:"level"`
	Special       int    `gorm:"column:special" json:"special"`
	Rank          int    `gorm:"column:rank" json:"rank"`
	Push          int    `gorm:"column:push" json:"push"`
	CardPackageID int64  `gorm:"column:card_package_id" json:"card_package_id"`
	Coupon        int    `gorm:"column:coupon" json:"coupon"`
	Price         int64  `gorm:"column:price" json:"price"`
	WorkID        int64  `gorm:"column:work_id" json:"work_id"`
	RoleID        int64  `gorm:"column:role_id" json:"role_id"`
	IsOnline      int    `gorm:"column:is_online" json:"is_online"`
	Pics          string `gorm:"column:pics" json:"pics"`
}

// TableName for current model
func (Card) TableName() string {
	return "card"
}
