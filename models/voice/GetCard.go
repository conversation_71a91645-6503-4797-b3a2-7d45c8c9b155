package voice

// GetCard model
type GetCard struct {
	ID           int64 `gorm:"column:id" json:"id"`
	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"modified_time"`
	AppearTime   int64 `gorm:"column:appear_time" json:"appear_time"`
	UserID       int64 `gorm:"column:user_id" json:"user_id"`
	CardID       int64 `gorm:"column:card_id" json:"card_id"`
	WorkID       int64 `gorm:"column:work_id" json:"work_id"`
	RoleID       int64 `gorm:"column:role_id" json:"role_id"`
	Level        int   `gorm:"column:level" json:"level"`
	Status       int   `gorm:"column:status" json:"status"`
	Special      int   `gorm:"column:special" json:"special"`
}

// TableName for current model
func (GetCard) TableName() string {
	return "get_card"
}
