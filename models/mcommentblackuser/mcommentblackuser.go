package mcommentblackuser

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// CommentBlackUser 评论黑名单用户
type CommentBlackUser struct {
	UserID int64 `gorm:"column:user_id"`
}

// DB mcommentblackuser from message db
func (CommentBlackUser) DB() *gorm.DB {
	return service.MessageDB.Table(CommentBlackUser{}.TableName())
}

// TableName mcommentblackuser table name
func (CommentBlackUser) TableName() string {
	return "m_comment_black_user"
}

// InCommentBlackUserList 查询用户是否在评论黑名单中
func InCommentBlackUserList(userID int64) (bool, error) {
	if userID == 0 {
		return false, nil
	}
	query := CommentBlackUser{}.DB().Where("user_id = ?", userID)
	return servicedb.Exists(query)
}
