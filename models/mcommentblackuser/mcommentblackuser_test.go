package mcommentblackuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(CommentBlackUser{}, "user_id")
}

func TestInCommentBlackUserList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	inBlacklist, err := InCommentBlackUserList(999999999)
	require.NoError(err)
	assert.True(inBlacklist)

	inBlacklist, err = InCommentBlackUserList(999999998)
	require.NoError(err)
	assert.False(inBlacklist)
}
