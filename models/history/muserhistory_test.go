package history

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(tableName, MUserHistory{}.TableName())
}

func TestBeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := new(MUserHistory)
	require.NoError(m.BeforeCreate(nil))
	assert.NotEqual(0, m.CreateTime)
	assert.NotEqual(0, m.ModifiedTime)
}

func TestBeforeSave(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := new(MUserHistory)
	require.NoError(m.BeforeSave(nil))
	assert.NotEqual(0, m.CreateTime)
	assert.NotEqual(0, m.ModifiedTime)
}

func TestAfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2333)
	// 新增音频历史记录
	more := "{\"last_play_sound\":{\"id\":5},\"node\":{\"id\":6,\"completion\":0.5}}"
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   999,
		ElementType: ElementTypeDrama,
		AccessTime:  1,
		More:        &more,
	}
	db := userHistory.DB()
	err := db.Save(&userHistory).Error
	require.NoError(err)
	history := MUserHistory{}
	// 查询数据并验证
	err = db.Where("id = ?", userHistory.ID).Take(&history).Error
	require.NoError(err)
	assert.Equal(more, *history.More)
	assert.Equal(int64(5), history.MoreInfo.LastPlaySound.ID)
	assert.Equal(int64(6), history.MoreInfo.Node.ID)
	assert.Equal(0.5, *history.MoreInfo.Node.Completion)
}

func TestAdd(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2334)
	// 清除历史数据
	db := MUserHistory{}.DB()
	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)

	// 测试无记录的时候
	more := "{\"completion\":0}"
	m := &MUserHistory{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: 1,
		AccessTime:  util.TimeUnixMilli(1),
		More:        &more,
	}
	err := m.add(nil)
	require.NoError(err)
	// 验证数据已新增
	var history MUserHistory
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		m.UserID, m.ElementID, m.ElementType, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(m.AccessTime, history.AccessTime)
	assert.Equal(m.More, history.More)

	// 测试更新记录的情况
	m.ID = 0
	m.AccessTime = 2
	more = "{\"completion\":0.9}"
	m.More = &more
	err = m.add(nil)
	require.NoError(err)
	// 验证数据已更新
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		m.UserID, m.ElementID, m.ElementType, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(m.AccessTime, history.AccessTime)
	assert.Equal(m.More, history.More)

	max := maxCount
	defer func() { maxCount = max }()
	maxCount = 5

	// 新增 MaxCount()+extraDeleteCount-1 条数据
	for i := int64(3); i < MaxCount()+extraDeleteCount+2; i++ {
		r := MUserHistory{
			UserID:      testUserID,
			ElementID:   i,
			ElementType: 1,
			AccessTime:  util.TimeUnixMilli(i),
			DeleteTime:  DeleteTimeNotDeleted,
			More:        &more,
		}
		require.NoError(db.Create(&r).Error)
	}
	m.ElementID = MaxCount() + extraDeleteCount + 3
	m.AccessTime = util.TimeUnixMilli(MaxCount() + extraDeleteCount + 3)
	// 测试记录数量大于 MaxCount()+extraDeleteCount 的时候只保留最新 MaxCount() 条记录
	err = m.add(nil)
	require.NoError(err)

	var r []MUserHistory
	err = db.Model(&MUserHistory{}).
		Order("access_time DESC").
		Where("user_id = ?", m.UserID).
		Scan(&r).Error
	require.NoError(err)
	tutil.PrintJSON(r)
	require.EqualValues(len(r), MaxCount()+extraDeleteCount+1)
	// 最新的 MaxCount() 条记录未被删除
	for i := 0; i < int(MaxCount()); i++ {
		assert.Equal(DeleteTimeNotDeleted, r[i].DeleteTime)
	}
	// 最旧的 extraDeleteCount+1 条记录被删除
	for i := 0; i < extraDeleteCount+1; i++ {
		assert.Equal(DeleteTimeArchived, r[len(r)-1-i].DeleteTime)
	}
}

func TestAddSound(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2335)
	history := MUserHistory{}
	now := util.TimeNow()
	err := AddSound(testUserID, 3, now, 1, nil)
	require.NoError(err)
	// 验证数据已新增
	db := history.DB()
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 3, ElementTypeSound, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), history.AccessTime)
	assert.Equal("{\"completion\":1}", *history.More)
}

func TestAddDrama(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2336)
	// 测试普通剧集
	history := MUserHistory{}
	now := util.TimeNow()
	err := AddDrama(testUserID, 3, 4, 0, now, 0.5, nil)
	require.NoError(err)
	// 验证数据已新增
	db := history.DB()
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 3, ElementTypeDrama, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), history.AccessTime)
	assert.Equal("{\"last_play_sound\":{\"id\":4,\"completion\":0.5}}", *history.More)

	// 测试互动剧集
	err = AddDrama(testUserID, 4, 5, 6, now, 0.5, nil)
	require.NoError(err)
	// 验证数据已新增
	history = MUserHistory{}
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 4, ElementTypeDrama, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), history.AccessTime)
	assert.Equal("{\"last_play_sound\":{\"id\":5},\"node\":{\"id\":6,\"completion\":0.5}}", *history.More)
}

func TestAddLiveRoom(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2337)
	history := MUserHistory{}
	now := util.TimeNow()
	err := AddLiveRoom(testUserID, 1, now, nil)
	require.NoError(err)
	// 验证数据已新增
	db := history.DB()
	err = db.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		testUserID, 1, ElementTypeLiveRoom, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), history.AccessTime)
	assert.Nil(history.More)
}

func TestDel(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2338)
	// 新增音频历史记录
	now := util.TimeNow()
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   2,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.NewTimeUnixMilli(now),
	}
	db := userHistory.DB()
	err := db.Save(&userHistory).Error
	require.NoError(err)
	delNum, err := Del(testUserID, []int64{userHistory.ID}, nil)
	require.NoError(err)
	assert.Equal(int64(1), delNum)
	// 验证数据已被删除
	var r MUserHistory
	require.NoError(db.Where("id = ?", userHistory.ID).Scan(&r).Error)
	require.GreaterOrEqual(r.ModifiedTime, now.Unix())
	require.GreaterOrEqual(r.DeleteTime, now.Unix())
}

func TestDelLive(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2338)

	now := util.TimeNow().Unix()
	// 新增用户收听直播历史记录 1
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	userHistoryDB := userHistory.DB()
	require.NoError(userHistoryDB.Save(&userHistory).Error)

	// 新增用户收听直播历史记录 2
	userHistory2 := MUserHistory{
		UserID:      testUserID,
		ElementID:   2,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	require.NoError(userHistoryDB.Save(&userHistory2).Error)

	delNum, err := Del(testUserID, []int64{userHistory.ID, userHistory2.ID}, nil)
	require.NotNil(delNum)
	require.NoError(err)
	assert.Equal(int64(2), delNum)
	// 验证数据已被删除
	var r MUserHistory
	require.NoError(userHistoryDB.Where("id = ?", userHistory.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)

	require.NoError(userHistoryDB.Where("id = ?", userHistory2.ID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)
}

func TestFindUserLastRecord(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2349)
	now := util.TimeNow()
	max := maxCount
	defer func() { maxCount = max }()
	maxCount = 5
	db := MUserHistory{}.DB()

	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)
	r, err := findUserLastRecord(testUserID, nil)
	require.Nil(err)
	assert.Nil(r)

	records := []MUserHistory{
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(2 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(3 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(4 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(5 * time.Minute)),
		},
	}
	require.NoError(db.Create(&records[0]).Error)
	r, err = findUserLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[1]).Error)
	require.NoError(db.Create(&records[2]).Error)
	r, err = findUserLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[3]).Error)
	require.NoError(db.Create(&records[4]).Error)
	r, err = findUserLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[5]).Error)
	r, err = findUserLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now.Add(time.Minute)), r.AccessTime)
}

func TestClear(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2339)
	// 新增音频历史记录
	now := util.TimeNow()
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   3,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.NewTimeUnixMilli(now),
	}
	db := userHistory.DB()
	err := db.Save(&userHistory).Error
	require.NoError(err)
	err = Clear(testUserID)
	assert.NoError(err)
	// 验证数据已被删除
	var r MUserHistory
	require.NoError(db.Where("user_id = ?", testUserID).Scan(&r).Error)
	require.GreaterOrEqual(r.ModifiedTime, now.Unix())
	require.GreaterOrEqual(r.DeleteTime, now.Unix())
}

func TestClearLive(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2339)

	now := util.TimeNow().Unix()
	// 新增用户收听直播历史记录 1
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   1,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	userHistoryDB := userHistory.DB()
	require.NoError(userHistoryDB.Save(&userHistory).Error)

	// 新增用户收听直播历史记录 2
	userHistory2 := MUserHistory{
		UserID:      testUserID,
		ElementID:   2,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	require.NoError(userHistoryDB.Save(&userHistory2).Error)

	err := ClearLive(testUserID)
	require.NoError(err)
	// 验证数据已被删除
	var r MUserHistory
	require.NoError(userHistoryDB.Where("user_id = ?", testUserID).Scan(&r).Error)
	assert.GreaterOrEqual(r.ModifiedTime, now)
	assert.GreaterOrEqual(r.DeleteTime, now)
}

func TestUserHistoryCount(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2310)
	// 测试无记录时
	historyNum, err := userHistoryCount(9999999, nil)
	require.NoError(err)
	assert.Equal(int64(0), historyNum)

	now := util.TimeNow()
	// 新增音频历史记录
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   3,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.NewTimeUnixMilli(now),
	}
	db := MUserHistory{}.DB()
	err = db.Save(&userHistory).Error
	require.NoError(err)
	historyNum, err = userHistoryCount(testUserID, nil)
	require.NoError(err)
	assert.Equal(int64(1), historyNum)
}

func TestFindList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试无记录时
	testUserID := int64(2311)
	testUserID2 := int64(2313)
	db := MUserHistory{}.DB()
	require.NoError(db.Table(MUserHistory{}.TableName()).Where("user_id IN (?)", []int64{testUserID, testUserID2}).
		Delete("").Error)
	elementType := make([]int, 0)
	list, hasMore, err := FindList(testUserID, 20, 0, nil)
	require.NoError(err)
	assert.False(hasMore)
	assert.Empty(list)

	// 新增音频历史记录
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   3,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  2,
	}
	err = db.Save(&userHistory).Error
	require.NoError(err)
	list, hasMore, err = FindList(testUserID, 1, 0, elementType)
	require.NoError(err)
	assert.False(hasMore)
	require.Equal(1, len(list))
	assert.Equal(userHistory.ID, list[0].ID)

	// 新增 elementType 参数生效
	elementType = []int{ElementTypeDrama}
	list, hasMore, err = FindList(testUserID, 20, 0, elementType)
	require.NoError(err)
	assert.False(hasMore)
	assert.Empty(list)
	elementType = []int{ElementTypeLiveRoom}
	list, hasMore, err = FindList(testUserID, 20, 0, elementType)
	require.NoError(err)
	assert.False(hasMore)
	require.Equal(1, len(list))
	assert.Equal(userHistory.ID, list[0].ID)

	// 测试 lastAccessTime 参数生效
	list, hasMore, err = FindList(testUserID, 20, 1, elementType)
	require.NoError(err)
	require.NoError(err)
	assert.False(hasMore)
	assert.Empty(list)
	list, hasMore, err = FindList(testUserID, 20, 3, elementType)
	require.NoError(err)
	assert.False(hasMore)
	require.Equal(1, len(list))
	assert.Equal(userHistory.ID, list[0].ID)
}

func TestFindElementIDsByIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2339)

	now := util.TimeNow().Unix()
	// 新增用户收听直播历史记录 1
	userHistory := MUserHistory{
		UserID:      testUserID,
		ElementID:   99,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	userHistoryDB := userHistory.DB()
	require.NoError(userHistoryDB.Save(&userHistory).Error)

	// 新增用户收听直播历史记录 2
	userHistory2 := MUserHistory{
		UserID:      testUserID,
		ElementID:   100,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.TimeUnixMilli(now),
	}
	require.NoError(userHistoryDB.Save(&userHistory2).Error)

	// 测试获取成功的情况
	elementIDs, err := FindElementIDsByIDs(ElementTypeLiveRoom, []int64{userHistory.ID, userHistory2.ID})
	require.NoError(err)
	assert.Equal([]int64{99, 100}, elementIDs)
}
