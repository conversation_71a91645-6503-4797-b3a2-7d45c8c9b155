package history

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MUserHistory model
type MUserHistory struct {
	ID           int64              `gorm:"column:id" json:"id,omitempty"`
	CreateTime   int64              `gorm:"column:create_time" json:"-"`
	ModifiedTime int64              `gorm:"column:modified_time" json:"-"`
	DeleteTime   int64              `gorm:"column:delete_time" json:"-"`
	UserID       int64              `gorm:"column:user_id" json:"user_id"`
	ElementID    int64              `gorm:"column:element_id" json:"element_id"`
	ElementType  int                `gorm:"column:element_type" json:"element_type"`
	AccessTime   util.TimeUnixMilli `gorm:"column:access_time" json:"access_time"`
	More         *string            `gorm:"column:more" json:"-"` // 该字段类型实际为 JSON 类型

	MoreInfo More `gorm:"-" json:"more"`
}

// More 元素播放信息，不同的元素类型有不同的结构值，e.g.
// 音频 {"completion": 0.05} 其中 completion 为元素播放进度百分比（示例值为 5%），下同
// 剧集 {"last_play_sound": {"id": 201331, "completion": 0.0186}}
// 互动剧集 {"node": {"id": 277, "completion": 0}, "last_play_sound": {"id": 233}}
// 直播间 NULL
type More struct {
	Completion    *float64       `json:"completion,omitempty"`
	LastPlaySound *LastPlaySound `json:"last_play_sound,omitempty"`
	Node          *Node          `json:"node,omitempty"`
}

// Node 互动剧节点播放信息
type Node struct {
	ID         int64    `json:"id"`
	Completion *float64 `json:"completion,omitempty"`
}

// LastPlaySound 互动剧或剧集播放信息
type LastPlaySound struct {
	ID         int64    `json:"id"`
	Completion *float64 `json:"completion,omitempty"` // 若为互动剧，则音频无 completion
}

const tableName = "m_user_history"

// extraDeleteCount 历史记录数量大于 MaxCount() + extraDeleteCount 后删除非最新 MaxCount() 条的记录，避免达到上限后每次新增记录都要删除数据
const extraDeleteCount = 50

var maxCount int64 = 500

// MaxCount 历史记录最大条数
func MaxCount() int64 {
	return maxCount
}

// 元素类型：音频、剧集、直播间
const (
	ElementTypeSound int = iota + 1
	ElementTypeDrama
	ElementTypeLiveRoom
)

// delete_time 类型
const (
	// 已被归档
	DeleteTimeArchived int64 = iota - 1
	// 未被软删除
	DeleteTimeNotDeleted
)

// DB the db instance of MUserHistory model
func (m MUserHistory) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MUserHistory) TableName() string {
	return tableName
}

// BeforeCreate hook
func (m *MUserHistory) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now
	return nil
}

// BeforeSave hook
func (m *MUserHistory) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// AfterFind is a GORM hook for query
func (m *MUserHistory) AfterFind() error {
	if m.More != nil {
		moreByte := []byte(*m.More)
		err := json.Unmarshal(moreByte, &m.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// AddSound add sound history
func AddSound(userID, soundID int64, accessTime time.Time, completion float64, tx *gorm.DB) error {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	more := More{
		Completion: &completion,
	}
	moreBytes, err := json.Marshal(more)
	if err != nil {
		return err
	}
	moreJSON := string(moreBytes)
	m := MUserHistory{
		UserID:      userID,
		ElementID:   soundID,
		ElementType: ElementTypeSound,
		AccessTime:  util.NewTimeUnixMilli(accessTime),
		More:        &moreJSON,
	}
	return m.add(tx)
}

// AddDrama add drama history
func AddDrama(userID, dramaID, soundID, nodeID int64, accessTime time.Time, completion float64, tx *gorm.DB) error {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	more := More{
		LastPlaySound: &LastPlaySound{
			ID: soundID,
		},
	}
	if nodeID != 0 {
		more.Node = &Node{
			ID:         nodeID,
			Completion: &completion,
		}
	} else {
		more.LastPlaySound.Completion = &completion
	}
	moreBytes, err := json.Marshal(more)
	if err != nil {
		return err
	}
	moreJSON := string(moreBytes)
	m := MUserHistory{
		UserID:      userID,
		ElementID:   dramaID,
		ElementType: ElementTypeDrama,
		AccessTime:  util.NewTimeUnixMilli(accessTime),
		More:        &moreJSON,
	}
	return m.add(tx)
}

// AddLiveRoom add live room history
func AddLiveRoom(userID, roomID int64, accessTime time.Time, tx *gorm.DB) error {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	m := MUserHistory{
		UserID:      userID,
		ElementID:   roomID,
		ElementType: ElementTypeLiveRoom,
		AccessTime:  util.NewTimeUnixMilli(accessTime),
	}
	return m.add(tx)
}

// add user's play history
func (m *MUserHistory) add(tx *gorm.DB) error {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	if m.UserID <= 0 || m.ElementID <= 0 || m.ElementType <= 0 || m.AccessTime <= 0 {
		return errors.New("add history params error")
	}
	tx = tx.Table(m.TableName())
	var history MUserHistory
	err := tx.Where("user_id = ? AND element_id = ? AND element_type = ? AND delete_time = ?",
		m.UserID, m.ElementID, m.ElementType, DeleteTimeNotDeleted).Take(&history).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return err
	}
	if err == nil {
		// 若记录存在，则更新 access_time 和 more
		updates := map[string]interface{}{
			"access_time": m.AccessTime,
			"more":        m.More,
		}
		return tx.Where("id = ?", history.ID).Updates(updates).Error
	}
	// 无记录，则创建记录
	err = tx.Save(m).Error
	if err != nil {
		return err
	}
	// 超出最大条数的部分进行软删除
	currentCount, err := userHistoryCount(m.UserID, tx)
	if err != nil {
		return err
	}
	if currentCount > MaxCount()+extraDeleteCount {
		// 超出最大条数的部分标记为已被归档
		// delete_time != 0 的记录将通过每日的归档任务被归档至 oss_rds_main_main
		r, err := findUserLastRecord(m.UserID, tx)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if r == nil {
			return nil
		}
		updates := map[string]interface{}{
			"modified_time": util.TimeNow().Unix(),
			"delete_time":   DeleteTimeArchived,
		}
		err = tx.Where("user_id = ? AND delete_time = ? AND access_time < ?",
			m.UserID, DeleteTimeNotDeleted, r.AccessTime).
			Updates(updates).Error
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// findUserLastRecord 查询用户可见范围最旧的一条访问记录
func findUserLastRecord(userID int64, tx *gorm.DB) (*MUserHistory, error) {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	query := tx.Where("user_id = ? AND delete_time = ?", userID, DeleteTimeNotDeleted)
	var r MUserHistory
	err := query.Order("access_time DESC").Offset(MaxCount() - 1).Limit(1).Scan(&r).Error
	if servicedb.IsErrNoRows(err) {
		// 用户数据总量达不到 MaxCount()，获得用户所有记录中最旧一条
		if err = query.Order("access_time").Limit(1).Scan(&r).Error; servicedb.IsErrNoRows(err) {
			return nil, nil
		}
	}
	return &r, err
}

// Clear user's all history
func Clear(userID int64) (err error) {
	now := util.TimeNow().Unix()
	return MUserHistory{}.DB().
		Where("user_id = ? AND delete_time = ?", userID, DeleteTimeNotDeleted).
		Update(MUserHistory{ModifiedTime: now, DeleteTime: now}).Error
}

// ClearLive 删除用户所有收听直播历史记录
func ClearLive(userID int64) (err error) {
	now := util.TimeNow().Unix()
	return MUserHistory{}.DB().
		Where("element_type = ? AND user_id = ? AND delete_time = ?",
			ElementTypeLiveRoom, userID, DeleteTimeNotDeleted).
		Update(MUserHistory{ModifiedTime: now, DeleteTime: now}).Error
}

// Del history by ids
func Del(userID int64, ids []int64, tx *gorm.DB) (deleteRows int64, err error) {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	now := util.TimeNow().Unix()
	result := tx.Table(MUserHistory{}.TableName()).
		Where("user_id = ? AND delete_time = ? AND id IN (?)", userID, DeleteTimeNotDeleted, ids).
		Update(MUserHistory{ModifiedTime: now, DeleteTime: now})
	return result.RowsAffected, result.Error
}

// DelLive 删除收听直播历史记录
func DelLive(userID int64, roomIDs []int64, tx *gorm.DB) (deleteRows int64, err error) {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	now := util.TimeNow().Unix()
	result := tx.Table(MUserHistory{}.TableName()).
		Where("element_type = ? AND element_id IN (?) AND user_id = ? AND delete_time = ?",
			ElementTypeLiveRoom, roomIDs, userID, DeleteTimeNotDeleted).
		Update(MUserHistory{ModifiedTime: now, DeleteTime: now})
	return result.RowsAffected, result.Error
}

// userHistoryCount 获取用户历史记录条数（不含软删除及归档记录）
func userHistoryCount(userID int64, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = MUserHistory{}.DB()
	}
	err = tx.Model(&MUserHistory{}).
		Where("user_id = ? AND delete_time = ?", userID, DeleteTimeNotDeleted).
		Count(&count).Error
	return count, err
}

// FindList find user history list
func FindList(userID, pageSize int64, accessTime util.TimeUnixMilli, elementTypes []int) (historys []MUserHistory, hasMore bool, err error) {
	lastRecord, err := findUserLastRecord(userID, nil)
	if err != nil {
		logger.Error(err)
		return
	}
	if lastRecord == nil || (accessTime != 0 && accessTime < lastRecord.AccessTime) {
		// 若查询页数比可查看的历史记录数量多，则返回空
		return
	}
	query := MUserHistory{}.DB().Table(MUserHistory{}.TableName()).
		Select("id, element_type, element_id, access_time, more").
		Where("user_id = ? AND delete_time = ? AND access_time >= ?",
			userID, DeleteTimeNotDeleted, lastRecord.AccessTime)
	if len(elementTypes) > 0 {
		// 若指定类型，则只查询相关类型的记录
		query = query.Where("element_type IN (?)", elementTypes)
	}
	if accessTime != 0 {
		// 若存在最后访问时间，则获取这个时间点之前的内容
		query = query.Where("access_time < ?", accessTime)
	}
	historyRows := make([]MUserHistory, 0, pageSize)
	// 多查询一条记录，如果查询出来的数量超过 pageSize，则 hasMore = true
	err = query.Order("access_time DESC").Limit(pageSize + 1).Find(&historyRows).Error
	if err != nil {
		return
	}
	hasMore = int64(len(historyRows)) > pageSize
	if hasMore {
		historyRows = historyRows[:pageSize]
	}
	return historyRows, hasMore, nil
}

// FindElementIDsByIDs 通过历史记录 IDs 找到房间 IDs
func FindElementIDsByIDs(elementType int, ids []int64) ([]int64, error) {
	if !util.HasElem([]int{ElementTypeSound, ElementTypeDrama, ElementTypeLiveRoom}, elementType) ||
		len(ids) == 0 {
		return []int64{}, nil
	}
	var elementIDs []int64
	err := MUserHistory{}.DB().Select("element_id").
		Where("element_type = ? AND id IN (?)", elementType, ids).
		Pluck("element_id", &elementIDs).Error
	if err != nil {
		return nil, err
	}
	return elementIDs, nil
}
