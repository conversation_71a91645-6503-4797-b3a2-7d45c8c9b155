package history

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/mradiosound"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestRadioHistoryTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_radio_history", MRadioHistory{}.TableName())
}

func TestRadioHistoryBeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := new(MRadioHistory)
	require.NoError(m.BeforeCreate(nil))
	assert.NotEqual(0, m.CreateTime)
	assert.NotEqual(0, m.ModifiedTime)
}

func TestRadioHistoryBeforeSave(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	m := new(MRadioHistory)
	require.NoError(m.BeforeSave(nil))
	assert.NotEqual(0, m.CreateTime)
	assert.NotEqual(0, m.ModifiedTime)
}

func TestRadioHistoryAfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2333)
	// 新增音频历史记录
	more := "{\"completion\":0.5}"
	radioHistory := MRadioHistory{
		UserID:     testUserID,
		SoundID:    999,
		AccessTime: 1,
		More:       &more,
	}
	db := radioHistory.DB()
	err := db.Save(&radioHistory).Error
	require.NoError(err)
	history := MRadioHistory{}
	// 查询数据并验证
	err = db.Where("id = ?", radioHistory.ID).Take(&history).Error
	require.NoError(err)
	assert.Equal(more, *history.More)
	assert.Equal(0.5, *history.MoreInfo.Completion)
}

func TestAddRadioHistory(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2334)
	// 清除历史数据
	db := MRadioHistory{}.DB()
	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)

	// 测试无记录的时候
	testSoundID := 1
	more := "{\"completion\":0}"
	accessTime := util.TimeUnixMilli(1)
	err := AddRadioHistory(testUserID, 1, accessTime.ToTime(), 0, db)
	require.NoError(err)
	// 验证数据已新增
	var history MRadioHistory
	err = db.Where("user_id = ? AND sound_id = ? AND delete_time = ?",
		testUserID, testSoundID, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(accessTime, history.AccessTime)
	assert.Equal(&more, history.More)

	// 测试更新记录的情况
	more = "{\"completion\":0.9}"
	accessTime = util.TimeUnixMilli(2)
	err = AddRadioHistory(testUserID, 1, accessTime.ToTime(), 0.9, db)
	require.NoError(err)
	// 验证数据已更新
	err = db.Where("user_id = ? AND sound_id = ? AND delete_time = ?",
		testUserID, testSoundID, DeleteTimeNotDeleted).
		Take(&history).Error
	require.NoError(err)
	assert.Equal(accessTime, history.AccessTime)
	assert.Equal(&more, history.More)

	max := radioMaxCount
	defer func() { radioMaxCount = max }()
	radioMaxCount = 5

	// 新增 radioMaxCount+extraDeleteCount-1 条数据
	for i := int64(3); i < RadioMaxCount()+extraDeleteCount+2; i++ {
		r := MRadioHistory{
			UserID:     testUserID,
			SoundID:    i,
			AccessTime: util.TimeUnixMilli(i),
			DeleteTime: DeleteTimeNotDeleted,
			More:       &more,
		}
		require.NoError(db.Create(&r).Error)
	}
	soundID := RadioMaxCount() + extraDeleteCount + 3
	accessTime = util.TimeUnixMilli(RadioMaxCount() + extraDeleteCount + 3)
	// 测试记录数量大于 MaxCount()+extraDeleteCount 的时候只保留最新 MaxCount() 条记录
	err = AddRadioHistory(testUserID, soundID, accessTime.ToTime(), 0.9, db)
	require.NoError(err)

	var r []MRadioHistory
	err = db.Model(&MRadioHistory{}).
		Order("access_time DESC").
		Where("user_id = ?", testUserID).
		Scan(&r).Error
	require.NoError(err)
	tutil.PrintJSON(r)
	require.EqualValues(len(r), RadioMaxCount()+extraDeleteCount+1)
	// 最新的 RadioMaxCount() 条记录未被删除
	for i := 0; i < int(RadioMaxCount()); i++ {
		assert.Equal(DeleteTimeNotDeleted, r[i].DeleteTime)
	}

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	// 最旧的 extraDeleteCount+1 条记录被删除
	for i := 0; i < extraDeleteCount+1; i++ {
		assert.Equal(now.Unix(), r[len(r)-1-i].DeleteTime)
	}
}

func TestFindRadioLastRecord(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2349)
	now := util.TimeNow()
	max := radioMaxCount
	defer func() { radioMaxCount = max }()
	radioMaxCount = 5
	db := MRadioHistory{}.DB()

	require.NoError(db.Where("user_id = ?", testUserID).Delete("").Error)
	r, err := findRadioLastRecord(testUserID, nil)
	require.Nil(err)
	assert.Nil(r)

	records := []MRadioHistory{
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(2 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(3 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(4 * time.Minute)),
		},
		{
			UserID:     testUserID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(5 * time.Minute)),
		},
	}
	require.NoError(db.Create(&records[0]).Error)
	r, err = findRadioLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[1]).Error)
	require.NoError(db.Create(&records[2]).Error)
	r, err = findRadioLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[3]).Error)
	require.NoError(db.Create(&records[4]).Error)
	r, err = findRadioLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now), r.AccessTime)
	require.NoError(db.Create(&records[5]).Error)
	r, err = findRadioLastRecord(testUserID, nil)
	require.NoError(err)
	assert.Equal(util.NewTimeUnixMilli(now.Add(time.Minute)), r.AccessTime)
}

func TestRadioHistoryCount(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(2310)
	// 测试无记录时
	historyNum, err := radioHistoryCount(9999999, nil)
	require.NoError(err)
	assert.Equal(int64(0), historyNum)

	now := util.TimeNow()
	// 新增历史记录
	radioHistory := MRadioHistory{
		UserID:     testUserID,
		SoundID:    3,
		AccessTime: util.NewTimeUnixMilli(now),
	}
	db := MRadioHistory{}.DB()
	err = db.Save(&radioHistory).Error
	require.NoError(err)
	historyNum, err = radioHistoryCount(testUserID, nil)
	require.NoError(err)
	assert.Equal(int64(1), historyNum)
}

func TestFindRadioHistoryList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试无记录的情况
	const testUserID int64 = 23233
	const testSoundID int64 = 1
	const testSoundID2 int64 = 2
	db := MRadioHistory{}.DB()
	historyList, hasMore, err := FindRadioHistoryList(testUserID, 60, 0)
	require.NoError(err)
	assert.Empty(historyList)
	assert.False(hasMore)

	// 测试存在历史记录，但是催眠专享音频不存在的情况
	now := util.TimeNow()
	records := []MRadioHistory{
		{
			UserID:     testUserID,
			SoundID:    testSoundID,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now),
		},
		{
			UserID:     testUserID,
			SoundID:    testSoundID2,
			DeleteTime: DeleteTimeNotDeleted,
			AccessTime: util.NewTimeUnixMilli(now.Add(time.Minute)),
		},
	}
	require.NoError(db.Create(&records[0]).Error)
	historyList, hasMore, err = FindRadioHistoryList(testUserID, 60, 0)
	require.NoError(err)
	assert.Empty(historyList)
	assert.False(hasMore)

	// 测试存在历史记录与相关催眠专享音频记录都存在的情况
	radioSound := mradiosound.MRadioSound{
		ID:              testSoundID,
		Title:           "test",
		Cover:           "test://test/cover.png",
		BackgroundCover: "test://test/background.png",
		DeleteTime:      DeleteTimeNotDeleted,
	}
	require.NoError(mradiosound.MRadioSound{}.DB().Save(&radioSound).Error)
	historyList, hasMore, err = FindRadioHistoryList(testUserID, 60, 0)
	require.NoError(err)
	assert.Equal(1, len(historyList))
	assert.False(hasMore)

	// 测试 hasMore 为 true 的情况
	require.NoError(db.Create(&records[1]).Error)
	radioSound2 := mradiosound.MRadioSound{
		ID:              testSoundID2,
		Title:           "test2",
		Cover:           "test://test/cover.png",
		BackgroundCover: "test://test/background.png",
		DeleteTime:      DeleteTimeNotDeleted,
	}
	require.NoError(mradiosound.MRadioSound{}.DB().Save(&radioSound2).Error)
	historyList, hasMore, err = FindRadioHistoryList(testUserID, 1, 0)
	require.NoError(err)
	assert.Equal(1, len(historyList))
	assert.True(hasMore)
	// 验证返回值正确
	history := historyList[0]
	assert.Equal(testSoundID2, history.SoundID)
	assert.Equal("test2", history.Title)
	assert.Equal("http://static-test.missevan.com/test/cover.png", history.FrontCover)
	assert.Equal("http://static-test.missevan.com/test/background.png", history.BackgroundCover)
	assert.Equal(records[1].AccessTime, history.AccessTime)
}

func TestSetFullCover(t *testing.T) {
	assert := assert.New(t)

	// 测试无封面和背景图的情况
	history := RadioPlayHistory{
		Cover:           "",
		BackgroundCover: "",
	}
	history.setFullCover()
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", history.FrontCover)
	assert.Equal("", history.BackgroundCover)

	// 测试有封面和背景图的情况
	history = RadioPlayHistory{
		Cover:           "test://test/cover.png",
		BackgroundCover: "test://test/background.png",
	}
	history.setFullCover()
	assert.Equal("http://static-test.missevan.com/test/cover.png", history.FrontCover)
	assert.Equal("http://static-test.missevan.com/test/background.png", history.BackgroundCover)
}
