package history

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mradiosound"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MRadioHistory model
type MRadioHistory struct {
	ID           int64              `gorm:"column:id"`
	CreateTime   int64              `gorm:"column:create_time"`
	ModifiedTime int64              `gorm:"column:modified_time"`
	DeleteTime   int64              `gorm:"column:delete_time"`
	UserID       int64              `gorm:"column:user_id"`
	SoundID      int64              `gorm:"column:sound_id"`
	AccessTime   util.TimeUnixMilli `gorm:"column:access_time"`
	More         *string            `gorm:"column:more"` // 该字段实际类型为 JSON

	MoreInfo More `gorm:"-"`
}

// RadioPlayHistory 历史记录信息
type RadioPlayHistory struct {
	ID              int64              `gorm:"column:id" json:"id"`
	SoundID         int64              `gorm:"column:sound_id" json:"sound_id"`
	AccessTime      util.TimeUnixMilli `gorm:"column:access_time" json:"access_time"`
	Title           string             `gorm:"column:title" json:"title"`
	BackgroundCover string             `gorm:"column:background_cover" json:"background_cover,omitempty"`
	Cover           string             `gorm:"column:cover" json:"-"`
	FrontCover      string             `gorm:"-" json:"front_cover,omitempty"`
}

var radioMaxCount int64 = 60

// RadioMaxCount 历史记录最大条数
func RadioMaxCount() int64 {
	return radioMaxCount
}

// DB the db instance of MRadioHistory model
func (m MRadioHistory) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MRadioHistory) TableName() string {
	return "m_radio_history"
}

// BeforeCreate hook
func (m *MRadioHistory) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *MRadioHistory) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// AfterFind is a GORM hook for query
func (m *MRadioHistory) AfterFind() error {
	if m.More != nil {
		moreByte := []byte(*m.More)
		err := json.Unmarshal(moreByte, &m.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// AddRadioHistory add radio history
func AddRadioHistory(userID, soundID int64, accessTime time.Time, completion float64, tx *gorm.DB) error {
	more := More{
		Completion: &completion,
	}
	moreBytes, err := json.Marshal(more)
	if err != nil {
		return err
	}
	moreJSON := string(moreBytes)
	m := MRadioHistory{
		UserID:     userID,
		SoundID:    soundID,
		AccessTime: util.NewTimeUnixMilli(accessTime),
		More:       &moreJSON,
	}
	if tx == nil {
		tx = MRadioHistory{}.DB()
	}
	tx = tx.Table(m.TableName())
	var history MRadioHistory
	err = tx.Where("user_id = ? AND sound_id = ? AND delete_time = ?",
		m.UserID, m.SoundID, DeleteTimeNotDeleted).Take(&history).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return err
	}
	if err == nil {
		// 若记录存在，则更新 access_time 和 more
		updates := map[string]interface{}{
			"modified_time": util.TimeNow().Unix(),
			"access_time":   m.AccessTime,
			"more":          m.More,
		}
		return tx.Where("id = ?", history.ID).Updates(updates).Error
	}
	// 无记录，则创建记录
	err = tx.Create(&m).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			// 忽略唯一索引引发的错误
			return nil
		}
		return err
	}
	// 超出最大条数的部分进行软删除
	currentCount, err := radioHistoryCount(m.UserID, tx)
	if err != nil {
		return err
	}
	if currentCount > RadioMaxCount()+extraDeleteCount {
		// 超出最大条数的部分标记为已被归档
		// delete_time != 0 的记录将通过每日的归档任务被归档至 oss_rds_main_main
		r, err := findRadioLastRecord(m.UserID, tx)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if r == nil {
			return nil
		}
		timeNow := util.TimeNow().Unix()
		updates := map[string]interface{}{
			"modified_time": timeNow,
			"delete_time":   timeNow,
		}
		err = tx.Where("user_id = ? AND delete_time = ? AND access_time < ?",
			m.UserID, DeleteTimeNotDeleted, r.AccessTime).
			Updates(updates).Error
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// findRadioLastRecord 查询用户可见范围最旧的一条访问记录
func findRadioLastRecord(userID int64, tx *gorm.DB) (*MRadioHistory, error) {
	if tx == nil {
		tx = MRadioHistory{}.DB()
	}
	query := tx.Where("user_id = ? AND delete_time = ?", userID, DeleteTimeNotDeleted)
	var r MRadioHistory
	err := query.Order("access_time DESC").Offset(RadioMaxCount() - 1).Limit(1).Scan(&r).Error
	if servicedb.IsErrNoRows(err) {
		// 用户数据总量达不到 RadioMaxCount()，获得用户所有记录中最旧一条
		if err = query.Order("access_time ASC").Limit(1).Scan(&r).Error; servicedb.IsErrNoRows(err) {
			return nil, nil
		}
	}
	return &r, err
}

// radioHistoryCount 获取用户历史记录条数（不含软删除及归档记录）
func radioHistoryCount(userID int64, tx *gorm.DB) (count int64, err error) {
	if tx == nil {
		tx = MRadioHistory{}.DB()
	}
	err = tx.Model(&MRadioHistory{}).
		Where("user_id = ? AND delete_time = ?", userID, DeleteTimeNotDeleted).
		Count(&count).Error
	return
}

// FindRadioHistoryList find radio history list
func FindRadioHistoryList(userID, pageSize int64, lastAccessTime util.TimeUnixMilli) ([]RadioPlayHistory, bool, error) {
	hasMore := false
	lastRecord, err := findRadioLastRecord(userID, nil)
	if err != nil {
		return nil, hasMore, err
	}
	if lastRecord == nil || (lastAccessTime != 0 && lastAccessTime < lastRecord.AccessTime) {
		// 若查询页数比可查看的历史记录数量多，则返回空
		return []RadioPlayHistory{}, hasMore, nil
	}
	query := MRadioHistory{}.DB().
		Table(MRadioHistory{}.TableName()+" AS t").
		Joins("LEFT JOIN "+mradiosound.MRadioSound{}.TableName()+" AS t2 ON t.sound_id = t2.id").
		Select("t.id, t.sound_id, t.access_time, t2.title, t2.cover, t2.background_cover").
		Where("t.user_id = ? AND t.delete_time = ? AND t.access_time >= ? AND t2.delete_time = ?",
			userID, DeleteTimeNotDeleted, lastRecord.AccessTime, DeleteTimeNotDeleted)
	if lastAccessTime != 0 {
		// 若存在最后访问时间，则获取这个时间点之前的内容
		query = query.Where("access_time < ?", lastAccessTime)
	}
	var historyList []RadioPlayHistory
	// 多查询一条记录，如果查询出来的数量超过 pageSize，则 hasMore = true
	// FIXME: 因历史记录有最大数量限制，这儿 +1 可能存在问题，需要修复
	err = query.Order("access_time DESC").Limit(pageSize + 1).Find(&historyList).Error
	if err != nil {
		return historyList, hasMore, err
	}
	hasMore = int64(len(historyList)) > pageSize
	if hasMore {
		historyList = historyList[:pageSize]
	}
	for index := range historyList {
		// 处理资源地址
		historyList[index].setFullCover()
	}
	return historyList, hasMore, nil
}

func (r *RadioPlayHistory) setFullCover() {
	if r.Cover != "" {
		r.FrontCover = service.Storage.Parse(r.Cover)
	} else {
		// 若无封面图，需要显示默认封面
		r.FrontCover = service.Storage.Parse(params.URL.DefaultCoverURL)
	}
	if r.BackgroundCover != "" {
		r.BackgroundCover = service.Storage.Parse(r.BackgroundCover)
	}
}
