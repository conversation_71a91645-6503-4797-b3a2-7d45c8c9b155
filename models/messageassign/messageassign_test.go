package messageassign

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_message_assign", TableName())
	assert.Equal("m_message_assign", MessageAssign{}.TableName())
}

func TestSystemMessageAssign(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	recuid := int64(12)
	db := service.DB.Table(TableName()).
		Delete("", "recuid = ? AND title = ?", recuid, "test title")
	require.NoError(db.Error)
	now := util.TimeNow()
	assert.NoError(SystemMessageAssign(recuid, "test title", "test content"))
	m := new(MessageAssign)
	db.Where("recuid = ? AND title = ?", recuid, "test title").First(m)
	assert.LessOrEqual(now.Unix(), m.Time)
}

func TestSend(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	box := NewMessageBox(now)
	assert.NotNil(box)
	assert.IsType(&MessageBox{}, box)
	assert.Equal(0, len(box.Messages))

	box.AddMessage(778899, "test msg", "test content")
	box.AddMessage(889900, "test msg", "test content")
	box.AddMessage(990011, "test msg", "test content")

	assert.Equal(3, len(box.Messages))

	err := box.Send()
	assert.NoError(err)

	var msgCount int
	err = service.DB.Table(TableName()).Where("recuid IN (778899, 889900, 990011)").Count(&msgCount).Error
	assert.NoError(err)
	defer func() {
		service.DB.Table(TableName()).Delete("", "recuid IN (778899, 889900, 990011)")
	}()

	assert.Equal(3, msgCount)
}
