package messageassign

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 通知状态
const (
	StatusUnread int = iota
	StatusRead
)

const tableName = "m_message_assign"

// TableName table name
func TableName() string {
	return tableName
}

// MessageAssign 私信
type MessageAssign struct {
	ID      int64  `gorm:"column:id;primary_key" json:"id"`
	RecUID  int64  `gorm:"column:recuid" json:"recuid"`
	SendUID int64  `gorm:"column:send_uid" json:"send_uid"`
	Title   string `gorm:"column:title" json:"title"`
	Content string `gorm:"column:content" json:"content"`
	Status  int    `gorm:"column:status" json:"status"`
	Time    int64  `gorm:"column:time" json:"time"`
}

// TableName table name
func (MessageAssign) TableName() string {
	return tableName
}

// BeforeCreate automatically set field time
func (m *MessageAssign) BeforeCreate(scope *gorm.Scope) (err error) {
	err = scope.SetColumn("time", util.TimeNow().Unix())
	return err
}

// SystemMessageAssign 系统私信通知
// NOTICE: content 是 HTML 格式
func SystemMessageAssign(recuid int64, title, content string) error {
	m := &MessageAssign{
		RecUID:  recuid,
		Title:   title,
		Content: content,
	}
	err := service.DB.Create(m).Error
	return err
}

// MessageBox for batching send message
type MessageBox struct {
	Messages  []MessageAssign
	timeStamp int64
}

// NewMessageBox initiate MessageBox instance
func NewMessageBox(sendTime time.Time) *MessageBox {
	return &MessageBox{
		Messages:  []MessageAssign{},
		timeStamp: sendTime.Unix(),
	}
}

// AddMessage add message to box to send
func (b *MessageBox) AddMessage(recuid int64, title, content string) {
	b.Messages = append(b.Messages, MessageAssign{
		RecUID:  recuid,
		SendUID: 0,
		Title:   title,
		Content: content,
		Status:  StatusUnread,
		Time:    b.timeStamp,
	})
}

// Send send messages in box
func (b *MessageBox) Send() error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		return helper.BatchInsert(tx, TableName(), b.Messages)
	})
}
