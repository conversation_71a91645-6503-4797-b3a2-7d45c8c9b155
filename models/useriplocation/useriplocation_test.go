package useriplocation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestLocationTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(UserIPLocation{}, "id", "create_time", "modified_time", "delete_time", "user_id", "ip_location")
}

func TestLocation_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("user_ip_location", UserIPLocation{}.TableName())
}

func TestFindIPLocationByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyUsersIPLocation0.Format()
	clearCache := func() {
		err := service.LRURedis.Del(key).Err()
		require.NoError(err)
	}
	clearCache()
	defer clearCache()

	r, err := service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(r)
	var existsUserID1 int64 = 12
	ipLocation, err := FindIPLocationByUserID(existsUserID1)
	require.NoError(err)
	require.NotEmpty(ipLocation)

	r, err = service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.NotZero(r)
	var existsUserID2 int64 = 13
	ipLocation, err = FindIPLocationByUserID(existsUserID2)
	require.NoError(err)
	require.NotEmpty(ipLocation)

	var deletedUserID int64 = 14
	ipLocation, err = FindIPLocationByUserID(deletedUserID)
	require.NoError(err)
	assert.Empty(ipLocation)
}
