package useriplocation

import (
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

const deleteTimeNotDeleted = iota

// UserIPLocation 用户属地表
type UserIPLocation struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	DeleteTime   int64  `gorm:"column:delete_time"`
	UserID       int64  `gorm:"column:user_id"`
	IPLocation   string `gorm:"column:ip_location"`
}

// TableName for UserIPLocation model
func (UserIPLocation) TableName() string {
	return "user_ip_location"
}

// DB the db instance of UserIPLocation model
func (u UserIPLocation) DB() *gorm.DB {
	return service.MainDB.Table(u.TableName())
}

// FindIPLocationByUserID 根据用户 ID 获取用户的 IP 属地配置
func FindIPLocationByUserID(userID int64) (string, error) {
	key := keys.KeyUsersIPLocation0.Format()
	formattedUserID := strconv.FormatInt(userID, 10)
	values, err := service.LRURedis.HMGet(key, formattedUserID, "0").Result()
	if err != nil {
		logger.WithField("user_id", userID).
			Errorf("从缓存中获取用户 IP 属地配置失败，error: %v", err)
		return "", err
	}
	// 如果缓存存在，则直接返回 IP 属地信息
	if len(values) == 2 && values[1] != nil {
		if values[0] != nil {
			return values[0].(string), nil
		}
		return "", nil
	}

	// 如果缓存不存在，则从数据库中获取用户 IP 属地信息并设置用户 IP 属地缓存
	var userIPLocations []UserIPLocation
	err = UserIPLocation{}.DB().
		Select("user_id, ip_location").
		Where("delete_time = ?", deleteTimeNotDeleted).
		Scan(&userIPLocations).Error
	if err != nil {
		return "", err
	}
	usersIPLocationMap := make(map[string]interface{}, len(userIPLocations)+1)
	for _, userIPLocation := range userIPLocations {
		usersIPLocationMap[strconv.FormatInt(userIPLocation.UserID, 10)] = userIPLocation.IPLocation
	}
	// 防止缓存穿透
	usersIPLocationMap["0"] = ""
	pipe := service.LRURedis.TxPipeline()
	pipe.HMSet(key, usersIPLocationMap)
	pipe.Expire(key, 5*time.Minute)
	_, err = pipe.Exec()
	if err != nil {
		logger.Errorf("设置用户 IP 属地缓存失败，error: %v", err)
		// PASS
	}
	ipLocation, ok := usersIPLocationMap[formattedUserID]
	if !ok {
		return "", nil
	}
	return ipLocation.(string), nil
}
