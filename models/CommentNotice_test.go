package models

import (
	"errors"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/models/commentnotice"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestAddCommentNotice(t *testing.T) {
	assert := assert.New(t)

	_ = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := AddCommentNotice(tx, 0, "", nil, nil, 0, 0, 0, "title", commentnotice.IsNotSub)
		assert.NoError(err)

		err = AddCommentNotice(tx, 0, "", nil, map[int64]string{0: ""}, 0, 0, 0, "title", commentnotice.IsNotSub)
		assert.NoError(err)

		return errors.New("rollback")
	})
}
