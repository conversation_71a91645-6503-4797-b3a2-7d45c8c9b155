package catalog

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

const tableName = "catalog"

const (
	// StatusOnline 在线分类
	StatusOnline = "Y"
)

// Catalog 分类
type Catalog struct {
	ID          int64  `gorm:"column:id;primary_key" json:"catalog_id"`
	CatalogName string `gorm:"column:catalog_name" json:"catalog_name"`
	StatusIs    string `gorm:"status_is" json:"-"`
}

// DB the db instance of catalog model
func (c Catalog) DB() *gorm.DB {
	return service.DB.Table(c.TableName())
}

// TableName table name
func TableName() string {
	return tableName
}

// TableName table name
func (Catalog) TableName() string {
	return tableName
}
