package live

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(tableName, Live{}.TableName())
}

func TestAfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	node := Live{
		Cover: "test://path/test.jpg",
	}
	err := node.AfterFind()
	require.NoError(err)
	assert.Equal("http://static-test.missevan.com/path/test.jpg", node.CoverURL)

	node.Cover = ""
	err = node.AfterFind()
	require.NoError(err)
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", node.CoverURL)
}

func TestFindOpenLiveByUserID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	live, err := FindOpenLiveByUserID(int64(999999))
	require.NoError(err)
	assert.Nil(live)

	live, err = FindOpenLiveByUserID(int64(1))
	require.NoError(err)
	assert.Equal("测试直播间 4", live.Title)
}
