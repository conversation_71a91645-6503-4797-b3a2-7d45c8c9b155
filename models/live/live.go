package live

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// 直播间状态
const (
	StatusClose = iota // 房间未开启
	StatusOpen         // 房间开启
)

const tableName = "live"

// Live 直播用户关联表
type Live struct {
	ID            int64  `gorm:"column:id;primary_key" json:"id"`
	RoomID        int64  `gorm:"column:room_id" json:"room_id"`
	CatalogID     int64  `gorm:"column:catalog_id" json:"catalog_id"`
	Title         string `gorm:"column:title" json:"title"`
	Intro         string `gorm:"column:intro" json:"intro"`
	Cover         string `gorm:"column:cover" json:"cover"`
	Status        int    `gorm:"column:status" json:"status"`
	ContractID    int64  `gorm:"column:contract_id;default:1" json:"contract_id"`
	LiveStartTime int64  `gorm:"column:live_start_time" json:"live_start_time"`
	CreateTime    int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime  int64  `gorm:"column:modified_time" json:"-"`
	UserID        int64  `gorm:"column:user_id" json:"user_id"`
	// WORKAROUND: 使用指针防止热度为 0 时 save 会失败
	Score *int64 `gorm:"column:score;default" json:"-"`

	CoverURL string `gorm:"-" json:"cover_url"`
}

// DB the db instance of Live model
func (l Live) DB() *gorm.DB {
	return service.DB.Table(l.TableName())
}

// TableName table name
func (Live) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (l *Live) AfterFind() error {
	if l.Cover != "" {
		l.CoverURL = service.Storage.Parse(l.Cover)
	} else {
		l.CoverURL = service.Storage.Parse(params.URL.DefaultCoverURL)
	}
	return nil
}

// FindOpenLiveByUserID 根据用户 ID 获取用户开播信息
func FindOpenLiveByUserID(userID int64) (*Live, error) {
	var l Live
	err := Live{}.DB().Select("room_id, title, cover").
		Where("user_id = ? AND status = ?", userID, StatusOpen).Take(&l).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &l, nil
}
