package discovery

import (
	"github.com/MiaoSiLa/missevan-go/util"
)

// SpecialSearchItemResult 开放搜索 special_search_items 实例默认显示字段
type SpecialSearchItemResult struct {
	ID           string `json:"id"`
	Cover        string `json:"cover"`
	URL          string `json:"url"`
	CreateTime   string `json:"create_time"`
	ModifiedTime string `json:"modified_time"`
	DramaIDs     string `json:"drama_ids"`
	IPID         string `json:"ip_id"`
	Color        string `json:"color"`
	Title        string `json:"title"`
	Background   string `json:"background"`
	StartTime    string `json:"start_time"`
	Type         string `json:"type"`
	More         string `json:"more"` // 该字段实际类型为 JSON
}

// UPCardMore UP 主卡片配置
type UPCardMore struct {
	UserID      int64  `json:"user_id"`
	Intro       string `json:"intro"`
	IsShowDrama int    `json:"is_show_drama"`
	IsShowLive  int    `json:"is_show_live"`
}

// GameCardMore 游戏预约卡片配置
type GameCardMore struct {
	GameID   int64        `json:"game_id"`
	ShowOnOS util.BitMask `json:"show_on_os"`
}
