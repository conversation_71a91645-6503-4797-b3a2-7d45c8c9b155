package discovery

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
)

// SpecialSearchItem 特殊搜索结果表
type SpecialSearchItem struct {
	ID           int64  `gorm:"column:id" json:"id"`                       // 主键
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`     // 创建时间，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time" json:"modified_time"` // 更新时间，单位：秒
	DeleteTime   int64  `gorm:"column:delete_time" json:"delete_time"`     // 删除时间，单位：秒
	Title        string `gorm:"column:title" json:"title"`                 // 主标题
	SearchWords  string `gorm:"column:search_words" json:"search_words"`   // 搜索词，多个搜索词用半角逗号分隔
	DramaIDs     string `gorm:"column:drama_ids" json:"drama_ids"`         // 关联剧集，多个剧集 ID 用半角逗号分隔
	URL          string `gorm:"column:url" json:"url"`                     // 链接
	IPID         int64  `gorm:"column:ip_id" json:"ip_id"`                 // 关联周边 IP ID
	Background   string `gorm:"column:background" json:"background"`       // 背景图
	Cover        string `gorm:"column:cover" json:"cover"`                 // 封面图
	StartTime    int64  `gorm:"column:start_time" json:"start_time"`       // 上线时间，单位：秒
	Color        string `gorm:"column:color" json:"color"`                 // 主题色
	Status       int    `gorm:"column:status" json:"status"`               // 状态 0：已停用；1：启用
	Type         int    `gorm:"column:type" json:"type"`                   // 类型 0：搜索特型库；1：搜索专题卡；2：UP 主卡片
	More         string `gorm:"column:more" json:"more"`                   // 额外数据，opensearch 暂不支持 json 字段同步，暂时先使用 text 类型
}

// Status constants
const (
	StatusDisabled = 0 // 停用
	StatusEnabled  = 1 // 启用
)

// DB the db instance of SpecialSearchItem model
func (s SpecialSearchItem) DB() *gorm.DB {
	return service.MainDB.Table(s.TableName())
}

// TableName of SpecialSearchItem model
func (SpecialSearchItem) TableName() string {
	return "special_search_items"
}

// FindActiveSpecialTopicCard finds an active special search topic card using the search word and start time
func FindActiveSpecialTopicCard(searchWord string, startTime int64) (*SpecialSearchItem, error) {
	if searchWord == "" {
		return nil, errors.New("search word is empty")
	}

	item, ok := findSpecialSearchTopicCardFromRedis(searchWord)
	if ok {
		return item, nil
	}

	item, err := findSpecialSearchTopicCardFromDB(searchWord, startTime)
	if err != nil {
		return nil, err
	}

	saveSpecialSearchTopicCardToRedis(searchWord, item)

	return item, nil
}

func findSpecialSearchTopicCardFromRedis(searchWord string) (*SpecialSearchItem, bool) {
	key := keys.KeySpecialTopicCardSearchWord1.Format(searchWord)

	value, err := service.LRURedis.Get(key).Result()
	if serviceredis.IsRedisNil(err) {
		return nil, false
	}
	if err != nil {
		logger.WithField("search_word", searchWord).Errorf("get special topic card from redis failed, error: %v", err)
		return nil, false
	}

	if value == "" {
		return nil, false
	}

	var item *SpecialSearchItem
	err = json.Unmarshal([]byte(value), &item)
	if err != nil {
		logger.WithField("search_word", searchWord).Errorf("unmarshal special topic card from redis failed, error: %v", err)
		return nil, false
	}

	return item, true
}

func findSpecialSearchTopicCardFromDB(searchWord string, startTime int64) (*SpecialSearchItem, error) {
	var item SpecialSearchItem
	err := item.DB().
		Where("type = ?", search.SpecialTopicCard).
		Where("delete_time = ?", 0).
		Where("status = ?", StatusEnabled).
		Where("start_time <= ?", startTime).
		Where("FIND_IN_SET(?, search_words)", searchWord).
		First(&item).
		Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func saveSpecialSearchTopicCardToRedis(searchWord string, item *SpecialSearchItem) {
	// TODO: 等 missevan-backend 项目配置上 db3 的 lru redis 后，需要在 missevan-backend 项目涉及修改 SpecialSearchItem 的接口中使对应的缓存失效
	key := keys.KeySpecialTopicCardSearchWord1.Format(searchWord)

	bytes, err := json.Marshal(item)
	if err != nil {
		logger.WithField("search_word", searchWord).Errorf("marshal special topic card to redis failed, error: %v", err)
		// PASS
		return
	}

	err = service.LRURedis.Set(key, string(bytes), time.Minute*5).Err()
	if err != nil {
		logger.WithField("search_word", searchWord).Errorf("set special topic card to redis failed, error: %v", err)
		// PASS
		return
	}
}
