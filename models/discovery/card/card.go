package card

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/game"
	"github.com/MiaoSiLa/missevan-go/models/live"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/util"
)

// ShowTypeEnable UP 主卡片展示（剧集作品、直播回放）
const ShowTypeEnable = 1

// 卡片配置概述类型
const (
	WorkSummaryTypeSound = iota + 1 // 声音
	WorkSummaryTypeDrama            // 剧集作品
)

// 元素类型
const (
	ElementTypeDrama = 2 // 剧集
	ElementTypeSound = 3 // 音频
	ElementTypeLive  = 5 // 直播动态
)

// 标签类型
const (
	TagLive     = "直播"
	TagHotDrama = "热剧"
	TagNewDrama = "新剧"
	TagSound    = "音频"
)

// 标签颜色
const (
	TagColorLiveOrSound = "#FF0000" // 红色
	TagColorDrama       = "#ED7760" // 橙色
)

// 游戏预约卡片预约/已预约/下载按钮默认颜色
const (
	GameCardBtnColorDefault     = "#000000" // 黑色
	GameCardDarkBtnColorDefault = "#FFFFFF" // 白色
)

// SpecialCardItem 搜索特型库数据
type SpecialCardItem struct {
	ID    interface{} `json:"id"`
	URL   string      `json:"url"`
	Cover string      `json:"cover"`
}

// SpecialCard 搜索特型库
type SpecialCard struct {
	Data           []SpecialCardItem `json:"data"`
	Pagination     util.Pagination   `json:"pagination"`
	OpsRequestMisc string            `json:"ops_request_misc"`
}

// WorkSummary work summary
type WorkSummary struct {
	Type int   `json:"type"`
	Num  int64 `json:"num"`
}

// UpCard UP 主搜索卡片
type UpCard struct {
	ID            int64         `json:"id"`
	UserName      string        `json:"username"`
	IconURL       string        `json:"iconurl"`
	Authenticated uint          `json:"authenticated"`
	FansNum       int64         `json:"fans_num"`
	WorkSummary   *WorkSummary  `json:"work_summary"`
	Intro         string        `json:"intro"`
	Followed      *int          `json:"followed,omitempty"`
	WorkList      []interface{} `json:"work_list"`
}

// GameCard 游戏预约卡片
type GameCard struct {
	ID                 int64        `json:"id"`
	URL                string       `json:"url"`
	Cover              string       `json:"cover"`
	DarkCover          string       `json:"dark_cover"`
	Icon               string       `json:"icon"`
	BtnColor           string       `json:"btn_color"`
	DarkBtnColor       string       `json:"dark_btn_color"`
	Name               string       `json:"name"`
	Tag                string       `json:"tag"`
	Intro              string       `json:"intro"`
	Status             int          `json:"status"`
	ShowOnOS           util.BitMask `json:"show_on_os"`
	DownloadURL        string       `json:"download_url"`
	PackageName        string       `json:"package_name"`
	PackageVersionCode int64        `json:"package_version_code"`
}

// LiveWork 直播动态作品
type LiveWork struct {
	Type     int    `json:"type"`
	RoomID   int64  `json:"room_id"`
	Title    string `json:"title"`
	CoverURL string `json:"cover_url"`
	Tag      string `json:"tag"`
	TagColor string `json:"tag_color"`
}

// SoundWork 音频作品
type SoundWork struct {
	Type       int    `json:"type"`
	SoundID    int64  `json:"sound_id"`
	Soundstr   string `json:"soundstr"`
	Duration   int64  `json:"duration"` // 音频时长（单位：毫秒）
	ViewCount  int64  `json:"view_count"`
	FrontCover string `json:"front_cover"`
	Video      bool   `json:"video"`
	Tag        string `json:"tag"`
	TagColor   string `json:"tag_color"`
}

// DramaWork 剧集作品
type DramaWork struct {
	Type      int    `json:"type"`
	DramaID   int64  `json:"drama_id"`
	Name      string `json:"name"`
	Cover     string `json:"cover"`
	UserName  string `json:"username"`
	ViewCount int64  `json:"view_count"`
	PayType   uint32 `json:"pay_type"`
	Tag       string `json:"tag"`
	TagColor  string `json:"tag_color"`
}

// NewLiveWork new live work
func NewLiveWork(l *live.Live) *LiveWork {
	return &LiveWork{
		Type:     ElementTypeLive,
		RoomID:   l.RoomID,
		Title:    l.Title,
		CoverURL: l.CoverURL,
		Tag:      TagLive,
		TagColor: TagColorLiveOrSound,
	}
}

// NewDramaWork new drama work
func NewDramaWork(d *dramainfo.RadioDramaDramainfo, tag string) *DramaWork {
	return &DramaWork{
		Type:      ElementTypeDrama,
		DramaID:   d.ID,
		Name:      *d.Name,
		Cover:     d.CoverURL,
		UserName:  d.UserName,
		ViewCount: d.ViewCount,
		PayType:   d.PayType,
		Tag:       tag,
		TagColor:  TagColorDrama,
	}
}

// NewSoundWorks new sound works
func NewSoundWorks(mSounds []sound.MSound, tag string) []*SoundWork {
	soundCount := len(mSounds)
	soundIDs := make([]int64, 0, soundCount)
	soundWorks := make([]*SoundWork, 0, soundCount)
	for _, mSound := range mSounds {
		soundIDs = append(soundIDs, mSound.ID)
		soundWorks = append(soundWorks, &SoundWork{
			Type:       ElementTypeSound,
			SoundID:    mSound.ID,
			Soundstr:   mSound.Soundstr,
			Duration:   int64(mSound.Duration),
			ViewCount:  mSound.ViewCount,
			FrontCover: mSound.FrontCover,
			Tag:        tag,
			TagColor:   TagColorLiveOrSound,
		})
	}
	hasVideoSoundIDs, err := sound.FindHasVideoSoundIDs(soundIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(hasVideoSoundIDs) != 0 {
		for _, sw := range soundWorks {
			sw.Video = util.HasElem(hasVideoSoundIDs, sw.SoundID)
		}
	}
	return soundWorks
}

// GetGameCard 获取游戏预约卡片
func GetGameCard(userID, gameID int64, showOnOS util.BitMask) (*GameCard, error) {
	mGame, err := game.FindGameByID(gameID)
	if err != nil {
		return nil, err
	}
	if mGame == nil ||
		mGame.ExtendedFieldsInfo == nil ||
		mGame.ExtendedFieldsInfo.Card == nil {
		return nil, fmt.Errorf("游戏预约卡片配置不存在，game_id = %d", gameID)
	}
	// 下架游戏，不展示游戏预约卡片
	if mGame.ShutdownTime != 0 && mGame.ShutdownTime <= util.TimeNow().Unix() {
		return nil, nil
	}
	if mGame.ExtendedFieldsInfo.Card.BtnColor == "" {
		mGame.ExtendedFieldsInfo.Card.BtnColor = GameCardBtnColorDefault
	}
	if mGame.ExtendedFieldsInfo.Card.DarkBtnColor == "" {
		mGame.ExtendedFieldsInfo.Card.DarkBtnColor = GameCardDarkBtnColorDefault
	}
	cardStatus, err := mGame.GetCardStatus(userID)
	if err != nil {
		return nil, err
	}
	return &GameCard{
		ID:                 gameID,
		URL:                mGame.URL,
		Cover:              mGame.ExtendedFieldsInfo.Card.Cover,
		DarkCover:          mGame.ExtendedFieldsInfo.Card.DarkCover,
		Icon:               mGame.Icon,
		BtnColor:           mGame.ExtendedFieldsInfo.Card.BtnColor,
		DarkBtnColor:       mGame.ExtendedFieldsInfo.Card.DarkBtnColor,
		Name:               mGame.Name,
		Tag:                mGame.Tag,
		Intro:              mGame.Intro,
		Status:             cardStatus,
		ShowOnOS:           showOnOS,
		DownloadURL:        fmt.Sprintf("%sx/gamecenter/download?game_id=%d", params.URL.Main, gameID),
		PackageName:        mGame.ExtendedFieldsInfo.PackageName,
		PackageVersionCode: mGame.ExtendedFieldsInfo.PackageVersionCode,
	}, nil
}
