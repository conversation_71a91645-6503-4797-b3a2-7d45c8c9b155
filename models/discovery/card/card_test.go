package card

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/game"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(SpecialCardItem{}, "id", "url", "cover")
	kc.Check(SpecialCard{}, "data", "pagination", "ops_request_misc")
	kc.Check(WorkSummary{}, "type", "num")
	kc.Check(UpCard{},
		"id", "username", "iconurl", "authenticated", "fans_num", "work_summary", "intro", "followed", "work_list")
	kc.Check(GameCard{}, "id", "url", "cover", "dark_cover", "icon", "btn_color", "dark_btn_color", "name", "tag",
		"intro", "status", "show_on_os", "download_url", "package_name", "package_version_code")
	kc.Check(LiveWork{}, "type", "room_id", "title", "cover_url", "tag", "tag_color")
	kc.Check(SoundWork{}, "type", "sound_id", "soundstr", "duration", "view_count", "front_cover", "video", "tag", "tag_color")
	kc.Check(DramaWork{}, "type", "drama_id", "name", "cover", "username", "view_count", "pay_type", "tag", "tag_color")
}

func TestGetGameCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1679500800, 0) // 2023-03-24 00:00:00
	})
	defer util.SetTimeNow(nil)

	var (
		existsSubscribedUserID int64 = 12
		existsGameCardGameID   int64 = 1
	)
	gameCard, err := GetGameCard(existsSubscribedUserID, existsGameCardGameID, 2)
	require.NoError(err)
	assert.NotNil(gameCard)
	assert.Equal(existsGameCardGameID, gameCard.ID)
	assert.EqualValues(2, gameCard.ShowOnOS)
	assert.Equal(game.StatusSubscribed, gameCard.Status)
	assert.Equal("https://www.missevan.com/x/gamecenter/download?game_id=1", gameCard.DownloadURL)

	var notExistsGameCardGameID int64 = 999999
	_, err = GetGameCard(existsSubscribedUserID, notExistsGameCardGameID, 2)
	assert.EqualError(err, "游戏预约卡片配置不存在，game_id = 999999")

	var existsShutdownGameCardGameID int64 = 2
	gameCard, err = GetGameCard(existsSubscribedUserID, existsShutdownGameCardGameID, 2)
	require.NoError(err)
	assert.Nil(gameCard)
}
