package discovery

import (
	"testing"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestSpecialSearchItemResultTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(SpecialSearchItemResult{}, "id", "cover", "url", "create_time", "modified_time", "drama_ids", "ip_id", "color",
		"title", "background", "start_time", "type", "more")
	kc.Check(UPCardMore{}, "user_id", "intro", "is_show_drama", "is_show_live")
	kc.Check(GameCardMore{}, "game_id", "show_on_os")
}
