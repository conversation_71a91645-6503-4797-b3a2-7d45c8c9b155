package discovery

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestFindActiveSpecialTopicCard(t *testing.T) {
	mainDB := service.MainDB
	service.MainDB = mainDB.Begin()
	t.Cleanup(func() {
		service.MainDB.Rollback()
		service.MainDB = mainDB
	})

	items := []SpecialSearchItem{
		{
			ID:          1,
			Title:       "测试专题卡1",
			SearchWords: "测试搜索词1-1,测试搜索词1-2",
			StartTime:   1630000000,
			Status:      StatusEnabled,
			Type:        1,
		},
		{
			ID:          2,
			Title:       "测试专题卡2",
			SearchWords: "测试搜索词2-1,测试搜索词2-2",
			StartTime:   1640000000,
			Status:      StatusEnabled,
			Type:        1,
		},
		{
			ID:          3,
			Title:       "停用的专题卡",
			SearchWords: "停用的搜索词1-1,停用的搜索词1-2",
			StartTime:   1630000000,
			Status:      StatusDisabled,
			Type:        1,
		},
		{
			ID:          4,
			Title:       "已删除的专题卡",
			SearchWords: "已删除的搜索词1,已删除的搜索词2",
			StartTime:   1630000000,
			Status:      StatusEnabled,
			Type:        1,
			DeleteTime:  1635000000,
		},
	}

	for _, item := range items {
		require.NoError(t, item.DB().Create(&item).Error)
	}

	tests := []struct {
		name       string
		searchWord string
		startTime  int64
		wantItem   *SpecialSearchItem
		wantErr    error
	}{
		{
			name:       "ValidSearchWordAndTime",
			searchWord: "测试搜索词1-1",
			startTime:  1635000000,
			wantItem:   &items[0],
		},
		{
			name:       "InvalidSearchWord",
			searchWord: "不存在的搜索词",
			startTime:  1635000000,
			wantItem:   nil,
		},
		{
			name:       "EarlierThanStartTime",
			searchWord: "测试搜索词2-1",
			startTime:  1635000000,
			wantItem:   nil,
		},
		{
			name:       "DisabledTopicCard",
			searchWord: "停用的搜索词1-1",
			startTime:  1635000000,
			wantItem:   nil,
		},
		{
			name:       "EmptySearchWord",
			searchWord: "",
			startTime:  1635000000,
			wantItem:   nil,
			wantErr:    errors.New("search word is empty"),
		},
		{
			name:       "DeletedTopicCard",
			searchWord: "已删除的搜索词1",
			startTime:  1635000000,
			wantItem:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotItem, err := FindActiveSpecialTopicCard(tt.searchWord, tt.startTime)
			if tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
				assert.Nil(t, gotItem)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantItem, gotItem)
				// Assert that the redis cache is updated
				gotItemCache, err := service.LRURedis.Get(keys.KeySpecialTopicCardSearchWord1.Format(tt.searchWord)).Result()
				require.NoError(t, err)
				if tt.wantItem == nil {
					assert.Equal(t, "null", gotItemCache)
				} else {
					itemBytes, err := json.Marshal(tt.wantItem)
					require.NoError(t, err)
					assert.JSONEq(t, string(itemBytes), gotItemCache)
				}
			}
		})
	}
}
