package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Mowangsksoundseiy{},
		"id", "name", "icon", "profile", "gender", "initial", "birthyear", "birthmonth", "birthday",
		"birthmonthday", "bloodtype", "career", "group", "weibo", "weiboname", "baike", "baikename", "mid",
		"checked", "soundline1", "soundline2", "soundline3", "seiyalias")
}

func TestMowangsksoundseiy_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("mowangsksoundseiy", Mowangsksoundseiy{}.TableName())
}

func TestListSeiyIDsByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := ListSeiyIDsByUserID(int64(999999))
	require.NoError(err)
	assert.Nil(result)

	result, err = ListSeiyIDsByUserID(int64(346286))
	require.NoError(err)
	assert.Len(result, 1)
	assert.EqualValues(1, result[0])
}

func TestListSeiysByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有数据
	result, err := ListSeiysByIDs([]int64{23333})
	require.NoError(err)
	assert.Empty(result)

	// 测试获取声优列表
	result, err = ListSeiysByIDs([]int64{1, 2})
	require.NoError(err)
	require.Len(result, 1)
	assert.EqualValues(1, result[0].ID)
}
