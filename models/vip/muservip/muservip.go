package muservip

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "m_user_vip"

// 会员类型
const (
	// TypePlay 点播会员
	TypePlay = 4
)

// MUserVip model
type MUserVip struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 修改时间。单位：秒
	VipID        int64 `gorm:"column:vip_id"`
	UserID       int64 `gorm:"column:user_id"`
	Type         int   `gorm:"column:type"`       // vip 类型。4：点播会员，其他类型待定
	StartTime    int64 `gorm:"column:start_time"` // 开始时间。单位：秒
	EndTime      int64 `gorm:"column:end_time"`   // 过期时间。单位：秒
}

// DB the db instance of MUserVip model
func (m MUserVip) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MUserVip) TableName() string {
	return tableName
}

// IsVip 用户当前是否为会员
func IsVip(userID int64) (bool, error) {
	now := util.TimeNow().Unix()
	return servicedb.Exists(MUserVip{}.DB().
		Where("user_id = ? AND type = ? AND start_time <= ? AND end_time > ?", userID, TypePlay, now, now))
}
