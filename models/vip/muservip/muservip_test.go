package muservip

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserVip{},
		"id", "create_time", "modified_time", "vip_id", "user_id", "type", "start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableName, MUserVip{}.TableName())
}

func TestIsVip(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试当前非会员
	testUserID := int64(10)
	vip, err := IsVip(testUserID)
	require.NoError(err)
	assert.False(vip)

	// 测试当前为会员
	testUserID = 11
	vip, err = IsVip(testUserID)
	require.NoError(err)
	assert.True(vip)
}
