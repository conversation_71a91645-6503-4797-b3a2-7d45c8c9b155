package message

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/jinzhu/gorm"
	"github.com/rivo/uniseg"
	emoji "github.com/tmdvs/Go-Emoji-Utils"
	"gopkg.in/guregu/null.v3"

	"github.com/MiaoSiLa/missevan-go/models/forbiddenwords"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

const white = 0xffffff

const (
	// DanmakuPoolInferiorQuality 劣质弹幕
	DanmakuPoolInferiorQuality = 50
	// DanmakuPoolSpam 灌水弹幕
	DanmakuPoolSpam = 80
	// DanmakuPoolLowQuality 低质弹幕
	DanmakuPoolLowQuality = 90
	// DanmakuPoolNormal 普通弹幕
	DanmakuPoolNormal = 160
	// DanmakuPoolHighQuality 高质弹幕
	DanmakuPoolHighQuality = 195

	// DanmakuModeSlide 滚动弹幕
	DanmakuModeSlide = 1
	// DanmakuModeBottom 底部弹幕
	DanmakuModeBottom = 4
	// DanmakuModeTop 顶部弹幕
	DanmakuModeTop = 5

	// DanmakuSizeNormal 弹幕默认字体大小
	DanmakuSizeNormal = 25
)

const (
	// DanmakuMaxLength 弹幕字数最大上限
	DanmakuMaxLength = 35
	// DanmakuMaxEmojiLength 弹幕 emoji 上限（超过上限假发送）
	DanmakuMaxEmojiLength = 7
	// DanmakuMaxRepeatCount 用户在同一音频下发送相同弹幕的数量上限
	DanmakuMaxRepeatCount = 10
)

// MSoundComment model
type MSoundComment struct {
	ID       int64           `gorm:"column:id" json:"id,omitempty"`
	SoundID  int64           `gorm:"column:sound_id" json:"sound_id"`
	UserID   int64           `gorm:"column:user_id" json:"user_id"`
	Text     string          `gorm:"column:text" json:"text"`
	STime    string          `gorm:"column:stime" json:"stime"` // immutable field.
	Size     byte            `gorm:"column:size" json:"size"`
	Color    int             `gorm:"column:color" json:"color"`
	Mode     byte            `gorm:"column:mode" json:"mode"`
	Date     int64           `gorm:"column:date" json:"date"`
	Pool     byte            `gorm:"column:pool" json:"pool"`
	LikeNum  int64           `gorm:"column:like_num" json:"like_num"`
	IP       string          `gorm:"column:ip" json:"-"`        // 用户 IP
	IPDetail json.RawMessage `gorm:"column:ip_detail" json:"-"` // IP 详情（格式化 JSON）

	sTimeFloat null.Float `gorm:"-" sql:"-"`
	DramaID    int64      `gorm:"-" sql:"-" json:"-"`
}

// GetSTimeFloat converts m.STime string to float64.
// m.STime should be immutable
func (m *MSoundComment) GetSTimeFloat() (float64, error) {
	if m.sTimeFloat.Valid {
		return m.sTimeFloat.Float64, nil
	}
	stime, err := strconv.ParseFloat(m.STime, 64)
	if err != nil {
		return 0, err
	}
	m.sTimeFloat.SetValid(stime)
	return stime, nil
}

// UnmarshalJSON implements the Unmarshaler interface
func (m *MSoundComment) UnmarshalJSON(bytes []byte) error {
	var dummy struct {
		SoundID int64  `gorm:"column:sound_id" json:"sound_id"`
		UserID  int64  `gorm:"column:user_id" json:"user_id"`
		Text    string `gorm:"column:text" json:"text"`
		STime   string `gorm:"column:stime" json:"stime"` // immutable field.
		Size    byte   `gorm:"column:size" json:"size,omitempty"`
		Color   int    `gorm:"column:color" json:"color,omitempty"`
		Mode    byte   `gorm:"column:mode" json:"mode,omitempty"`
		Date    int64  `gorm:"column:date" json:"date,omitempty"`
		Pool    byte   `gorm:"column:pool" json:"pool,omitempty"`
	}
	m.Color = white
	err := json.Unmarshal(bytes, &dummy)
	if err != nil {
		return err
	}
	m.SoundID = dummy.SoundID
	m.UserID = dummy.UserID
	m.Text = dummy.Text
	m.STime = dummy.STime
	m.Size = dummy.Size
	m.Color = dummy.Color
	m.Mode = dummy.Mode
	m.Date = dummy.Date
	m.Pool = dummy.Pool
	m.FillData()
	return nil
}

// DB the db instance of MSoundComment model
func (m MSoundComment) DB() *gorm.DB {
	return service.MessageDB.Table(m.TableName())
}

// TableName for current model
func (MSoundComment) TableName() string {
	return "m_sound_comment"
}

// Insert m into database as a new record and m.ID is ignored
func (m *MSoundComment) Insert(db *gorm.DB) error {
	m.ID = 0
	err := m.DB().Create(m).Error
	if err != nil {
		return err
	}
	return nil
}

// Save saves the m into database and updates m.ID with the last_insert_id
func (m *MSoundComment) Save(db *gorm.DB) error {
	err := m.DB().Save(m).Error
	if err != nil {
		return err
	}
	return nil
}

// Validate validates m，见下面 checkText 中的 NOTICE! 返回值第一个参数为 true 假发送
func (m *MSoundComment) Validate() (bool, error) {
	if m.SoundID == 0 || m.UserID == 0 || m.Text == "" || m.STime == "" {
		return false, errors.New("missing required value")
	}
	if uniseg.GraphemeClusterCount(m.Text) > DanmakuMaxLength {
		return false, fmt.Errorf("最多输入 %d 字哦 _(:з」∠)_", DanmakuMaxLength)
	}
	// 弹幕中 emoji 数超过上限时假发送该弹幕
	var emojis int
	for _, v := range emoji.FindAll(m.Text) {
		emojis += v.Occurrences
		if emojis > DanmakuMaxEmojiLength {
			return true, nil
		}
	}

	if isBlocklistEmoji, err := checkEmojiAllowList(m.Text); err != nil {
		return false, err
	} else if isBlocklistEmoji {
		// 弹幕中含有不在白名单的 emoji 表情，假发送此条弹幕
		return true, nil
	}

	if m.DramaID > 0 {
		isBlocked, err := IsDramaMessageBlockUser(m.DramaID, m.UserID, BlockTypeDanmaku)
		if err != nil {
			return false, err
		}
		if isBlocked {
			// 用户被剧集弹幕拉黑，假发送此条弹幕
			return true, nil
		}
	}
	isShamSend, err := m.checkText()
	if err != nil {
		return isShamSend, err
	}
	return isShamSend, nil
}

// FillData 补充弹幕默认信息
func (m *MSoundComment) FillData() {
	if m.Size == 0 {
		m.Size = DanmakuSizeNormal
	}
	if m.Mode == 0 {
		m.Mode = DanmakuModeSlide
	}
	if m.Date == 0 {
		m.Date = util.TimeNow().Unix()
	}
	if m.Pool == 0 {
		m.Pool = DanmakuPoolNormal
	}
	// NOTICE: Android 在播放页进行全屏模式，发送弹幕，color 会为负值 -12544
	// 影响版本: Android < 5.5.2
	if m.Color < 0 || m.Color > white {
		m.Color = white
	}
}

// ErrViolationComment 弹幕含有违禁词
var ErrViolationComment = errors.New("输入的弹幕中含有违规词汇喔")

func (m *MSoundComment) checkText() (bool, error) {
	// NOTICE: 只能在 rpc 中使用，web 没有初始化 service.AntiSpam
	r, _, err := service.AntiSpam.CheckTexts([]string{m.Text}, m.UserID, scan.SceneDanmaku)
	if err != nil {
		return false, err
	}
	if !r[0].Pass {
		return false, ErrViolationComment
	}
	if util.HasElem(r[0].Labels, "evil") {
		return true, nil
	}
	if m.DramaID > 0 {
		isShamSend := forbiddenwords.CheckText(forbiddenwords.CheckTypeDramaDanmaku, m.DramaID, m.Text)
		return isShamSend, nil
	}
	return false, nil
}

// SaveDmData 添加弹幕信息
func SaveDmData(dm *MSoundComment, snd *sound.MSound) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := dm.Save(tx)
		if err != nil {
			return err
		}
		rowsAffected := tx.Model(&sound.MSound{ID: snd.ID}).
			Update("comment_count", gorm.Expr("comment_count + 1")).RowsAffected
		if rowsAffected == 0 {
			return errors.New("弹幕发送失败")
		}
		return nil
	})
}

// IncrLikeNum 点赞数或点踩数改动
func (m MSoundComment) IncrLikeNum(db *gorm.DB, id, addLikeNum int64) error {
	if addLikeNum == 0 {
		return nil
	}
	updateColumns := make(map[string]interface{}, 1)
	if addLikeNum > 0 {
		updateColumns["like_num"] = gorm.Expr("like_num + ?", addLikeNum)
	} else {
		// GREATEST(like_num, incrNum) - incrNum
		updateColumns["like_num"] = servicedb.SubSatExpr("like_num", int(-addLikeNum))
	}
	return db.Table(m.TableName()).Where("id = ?", id).Updates(updateColumns).Error
}

// GetNumberDanmakuBySoundDuration 根据音频长度获取弹幕数量
func GetNumberDanmakuBySoundDuration(soundDuration int64) (needDanmakus int) {
	switch duration := soundDuration / 1000; {
	case duration <= halfMinute:
		needDanmakus = DanmakuNum30Sec
	case duration <= oneMinute:
		needDanmakus = DanmakuNum60Sec
	case duration <= oneMinute*3:
		needDanmakus = DanmakuNum180Sec
	case duration <= tenMinute:
		needDanmakus = DanmakuNum600Sec
	case duration <= quarterHour:
		needDanmakus = DanmakuNum900Sec
	case duration <= oneMinute*40:
		needDanmakus = DanmakuNum2400Sec
	case duration <= oneHour:
		needDanmakus = DanmakuNum3600Sec
	default:
		needDanmakus = DanmakuMaxNum
	}
	return
}

// ListDanmaku 获取音频弹幕
// NOTICE: 调用该函数业务方需要实现弹幕缓存
func ListDanmaku(soundID int64, needCount int) ([]MSoundComment, error) {
	var danmakus []MSoundComment
	err := MSoundComment{}.DB().Where("sound_id = ? AND pool > ?", soundID, DanmakuPoolInferiorQuality).
		Order("pool DESC, id DESC").Limit(needCount).Find(&danmakus).Error
	if err != nil {
		return nil, err
	}
	return danmakus, nil
}

// CountRepeatDanmaku 查找用户在同一音频下相同的弹幕数
func (m *MSoundComment) CountRepeatDanmaku() (int64, error) {
	var count int64
	err := m.DB().
		Where("sound_id = ? AND user_id = ? AND text = ? AND pool >= ?",
			m.SoundID, m.UserID, m.Text, DanmakuPoolNormal).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
