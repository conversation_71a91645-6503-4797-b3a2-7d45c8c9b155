package message

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testUserID = 1

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestLikeNoticeTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_user_like_danmaku", MUserLikeDanmaku{}.TableName())
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)

	// 测试消息提醒不存在的情况
	assert.False(MUserLikeDanmaku{}.Exists(service.MessageDB, ElementTypeSound, 99999999999, testUserID))

	// 测试消息提醒存在的情况
	likeDanmaku := MUserLikeDanmaku{
		UserID:      testUserID,
		DanmakuID:   233,
		ElementID:   1,
		ElementType: ElementTypeSound,
	}
	err := service.MessageDB.Save(&likeDanmaku).Error
	require.NoError(err)
	assert.True(likeDanmaku.Exists(service.MessageDB, ElementTypeSound, 233, testUserID))

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)
}

func TestSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)
	likeDanmaku := MUserLikeDanmaku{
		UserID:      testUserID,
		DanmakuID:   233,
		ElementID:   1,
		ElementType: ElementTypeSound,
	}
	assert.NoError(likeDanmaku.Save(service.MessageDB))
	// 验证数据存在
	assert.True(likeDanmaku.Exists(service.MessageDB, ElementTypeSound, 233, testUserID))

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)
}

func TestDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)

	// 测试无数据时软删除
	assert.NoError(MUserLikeDanmaku{}.Delete(service.MessageDB, ElementTypeSound, 233, testUserID))

	// 测试有数据时软删除
	likeDanmaku := MUserLikeDanmaku{
		UserID:      testUserID,
		DanmakuID:   233,
		ElementID:   1,
		ElementType: ElementTypeSound,
	}
	err := service.MessageDB.Save(&likeDanmaku).Error
	require.NoError(err)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)
	assert.NoError(MUserLikeDanmaku{}.Delete(service.MessageDB, ElementTypeSound, 233, testUserID))
	// 验证数据是否已被软删除
	assert.False(likeDanmaku.Exists(service.MessageDB, ElementTypeSound, 233, testUserID))

	// 测试有重复数据时软删除
	likeDanmaku2 := MUserLikeDanmaku{
		UserID:      testUserID,
		DanmakuID:   233,
		ElementID:   1,
		ElementType: ElementTypeSound,
	}
	err = service.MessageDB.Save(&likeDanmaku2).Error
	require.NoError(err)
	assert.NoError(MUserLikeDanmaku{}.Delete(service.MessageDB, ElementTypeSound, 233, testUserID))

	// 删除测试数据
	require.NoError(service.MessageDB.Table(MUserLikeDanmaku{}.TableName()).Where("user_id = ?", testUserID).Delete("").Error)
}
