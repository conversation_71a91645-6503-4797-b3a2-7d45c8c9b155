package message

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestDeleteDanmaku(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 添加音频信息
	addSound := sound.MSound{
		ID:           2333,
		UserID:       14,
		CommentCount: 2,
	}
	err := service.DB.Table(addSound.TableName()).Create(&addSound).Error
	require.NoError(err)

	// 添加弹幕
	delDanmaku := MSoundComment{
		ID:      233,
		SoundID: 2333,
		UserID:  13,
		Text:    "测试",
	}
	err = service.MessageDB.Table(delDanmaku.TableName()).Create(delDanmaku).Error
	require.NoError(err)

	require.NoError(DeleteDanmaku(delDanmaku.ID, &delDanmaku, soundcomment.DeleteTypeBySelf))

	// 检查是否删除
	err = MSoundComment{}.DB().Where("id = ?", delDanmaku.ID).Find(&delDanmaku).Error
	assert.Equal(servicedb.IsErrNoRows(err), true)

	// 查看弹幕数是否更新正常,记录修改时间是否正常时间正常
	updateSound := sound.MSound{}
	err = service.DB.Model(sound.MSound{}).Where("id = ?", addSound.ID).Find(&updateSound).Error
	require.NoError(err)
	assert.NotEqual(addSound.LastUpdateTime, updateSound.LastUpdateTime)
	assert.Equal(addSound.CommentCount-1, updateSound.CommentCount)

	// 查看被删除弹幕是否入库
	deleteDanmakuExist := DeleteMSoundComment{}
	require.NoError(service.MessageDB.Table(deleteDanmakuExist.TableName()).Where("comment_id = ?", delDanmaku.ID).Find(&deleteDanmakuExist).Error)
	assert.Equal(deleteDanmakuExist.CommentID, delDanmaku.ID)
}

func TestDeleteMSoundCommentArchive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	delComment := DeleteMSoundComment{}
	delComment.Text = "hello world"
	delComment.DeleteTime = util.TimeNow().Unix()
	require.NoError(delComment.Archive())
	newDelComment := new(DeleteMSoundComment)
	require.NoError(service.MessageDB.Table(delComment.TableName()).Where("id = ?", delComment.ID).Find(newDelComment).Error)
	assert.Equal(delComment.Text, newDelComment.Text)
	assert.Equal(delComment.DeleteType, newDelComment.DeleteType)
}

func TestDeleteMSoundCommentTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	deleteSoundComment := DeleteMSoundComment{}
	kc.Check(deleteSoundComment, "comment_id", "delete_type", "delete_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(deleteSoundComment, "comment_id")
}
