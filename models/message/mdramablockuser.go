package message

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// MDramaMessageBlockUser model
type MDramaMessageBlockUser struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`
	DramaID      int64 `gorm:"column:drama_id"`
	UserID       int64 `gorm:"column:user_id"`
	BlockType    int   `gorm:"column:block_type"`
}

const (
	// BlockTypeDanmaku 拉黑剧集弹幕
	BlockTypeDanmaku = iota + 1
	// BlockTypeComment 拉黑剧集评论（暂时还没有支持）
	BlockTypeComment
)

// TableName for current model
func (MDramaMessageBlockUser) TableName() string {
	return "m_drama_message_block_user"
}

// DB the db instance of MDramaMessageBlockUser model
func (m MDramaMessageBlockUser) DB() *gorm.DB {
	return service.MessageDB.Table(m.TableName())
}

// IsDramaMessageBlockUser 拉黑发送剧集下音频评论或弹幕
func IsDramaMessageBlockUser(dramaID, userID int64, blockType int) (bool, error) {
	query := MDramaMessageBlockUser{}.DB().
		Where("drama_id = ? AND user_id = ? AND block_type = ? AND delete_time = 0", dramaID, userID, blockType)
	return servicedb.Exists(query)
}
