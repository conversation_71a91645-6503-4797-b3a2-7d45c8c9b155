package message

import (
	"database/sql"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 弹幕所属元素类型
const (
	ElementTypeSound int = iota + 1 // 音频
	ElementTypeNode                 // 互动剧节点
)

// MUserLikeDanmaku model
type MUserLikeDanmaku struct {
	ID           int64 `gorm:"column:id" json:"id,omitempty"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`
	UserID       int64 `gorm:"column:user_id"`
	DanmakuID    int64 `gorm:"column:danmaku_id"`
	ElementID    int64 `gorm:"column:element_id" json:"element_id"`
	ElementType  int   `gorm:"column:element_type" json:"element_type"`
}

// TableName for current model
func (MUserLikeDanmaku) TableName() string {
	return "m_user_like_danmaku"
}

// BeforeCreate hook
func (likeDanmaku *MUserLikeDanmaku) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	likeDanmaku.CreateTime = now
	return nil
}

// BeforeSave hook
func (likeDanmaku *MUserLikeDanmaku) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	likeDanmaku.ModifiedTime = now
	return nil
}

// Exists 返回用户是否点赞了弹幕（忽略已被软删除数据）
func (likeDanmaku MUserLikeDanmaku) Exists(db *gorm.DB, elementType int, danmakuID, userID int64) (bool, error) {
	var id int64
	err := db.Table(likeDanmaku.TableName()).Select("id").
		Where("element_type = ? AND danmaku_id = ? AND user_id = ? AND delete_time = ?", elementType, danmakuID, userID, 0).
		Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// Save 新增点赞数据
func (likeDanmaku *MUserLikeDanmaku) Save(db *gorm.DB) error {
	err := db.Create(likeDanmaku).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			return nil
		}
		return err
	}
	// 弹幕点赞数量加 1
	switch likeDanmaku.ElementType {
	case ElementTypeSound:
		return MSoundComment{}.IncrLikeNum(db, likeDanmaku.DanmakuID, 1)
	case ElementTypeNode:
		return MDanmaku{}.IncrLikeNum(db, likeDanmaku.DanmakuID, 1)
	default:
		return fmt.Errorf("wrong element_type: %d", likeDanmaku.ElementType)
	}
}

// Delete 软删除点赞数据
func (likeDanmaku MUserLikeDanmaku) Delete(db *gorm.DB, elementType int, danmakuID, userID int64) error {
	update := db.Table(likeDanmaku.TableName()).
		Where("element_type = ? AND danmaku_id = ? AND user_id = ? AND delete_time = ?", elementType, danmakuID, userID, 0).
		Update("delete_time", util.TimeNow().Unix())
	if err := update.Error; err != nil {
		if servicedb.IsUniqueError(err) {
			// 若为唯一索引异常（同一秒内被取消点赞两次），则说明行为异常，此时返回“取消成功”，需要记录日志
			logger.WithFields(logger.Fields{"element_type": elementType, "danmaku_id": danmakuID, "user_id": userID}).
				Warnf("用户点赞或取消点赞弹幕行为过于频繁")
			return nil
		}
		return err
	}
	if update.RowsAffected == 0 {
		return nil
	}
	// 弹幕点赞数量减 1
	switch elementType {
	case ElementTypeSound:
		return MSoundComment{}.IncrLikeNum(db, danmakuID, -1)
	case ElementTypeNode:
		return MDanmaku{}.IncrLikeNum(db, danmakuID, -1)
	default:
		return fmt.Errorf("wrong element_type: %d", elementType)
	}
}
