package message

import (
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/rivo/uniseg"
	emoji "github.com/tmdvs/Go-Emoji-Utils"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

// MAllowListEmoji model
type MAllowListEmoji struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Emoji        string `gorm:"column:emoji"`
	UnicodeEmoji string `gorm:"column:unicode_emoji"`
	Description  string `gorm:"column:description"`
}

// 缓存 emoji 白名单
var emojiAllowListCache = cache.New(3*time.Minute, 5*time.Minute)

// TableName for current model
func (MAllowListEmoji) TableName() string {
	return "m_allowlist_emoji"
}

// getEmojiAllowList 获取 emoji 白名单
func getEmojiAllowList() (*sync.Map, error) {
	cacheKey := keys.LocalKeyEmojiAllowList.Format()
	if v, ok := emojiAllowListCache.Get(cacheKey); ok {
		return v.(*sync.Map), nil
	}
	var slcEmoji []MAllowListEmoji
	if err := service.MessageDB.Select("emoji").Find(&slcEmoji).Error; err != nil {
		return nil, err
	}
	allowEmoji := &sync.Map{}
	for _, mEmoji := range slcEmoji {
		allowEmoji.Store(mEmoji.Emoji, struct{}{})
	}
	emojiAllowListCache.Set(cacheKey, allowEmoji, 0)
	return allowEmoji, nil
}

// checkEmojiAllowList 检查弹幕中是否含有非白名单 emoji 表情
func checkEmojiAllowList(text string) (bool, error) {
	allowEmoji, err := getEmojiAllowList()
	if err != nil {
		return false, err
	}
	// 分析并提取字符，细节解释请移步：https://github.com/rivo/uniseg
	gr := uniseg.NewGraphemes(text)
	for gr.Next() {
		// 判断字符是否为 emoji，并除去重复 emoji
		if _, err = emoji.LookupEmoji(gr.Str()); err == nil {
			if _, ok := allowEmoji.Load(gr.Str()); !ok {
				return true, nil
			}
		}
	}
	return false, nil
}
