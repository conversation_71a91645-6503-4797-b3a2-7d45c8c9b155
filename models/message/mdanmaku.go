package message

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MDanmaku model
type MDanmaku struct {
	ID           int64  `gorm:"column:id" json:"id,omitempty"`
	ElementID    int64  `gorm:"column:element_id" json:"element_id"`
	ElementType  int    `gorm:"column:element_type" json:"element_type"`
	UserID       int64  `gorm:"column:user_id" json:"user_id"`
	Text         string `gorm:"column:text" json:"text"`
	STime        string `gorm:"column:stime" json:"stime"`
	Size         byte   `gorm:"column:size" json:"size"`
	Color        int    `gorm:"column:color" json:"color"`
	Mode         byte   `gorm:"column:mode" json:"mode"`
	Pool         byte   `gorm:"column:pool" json:"pool"`
	LikeNum      int64  `gorm:"column:like_num" json:"like_num"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
}

// DB the db instance of MDanmaku model
func (m MDanmaku) DB() *gorm.DB {
	return service.MessageDB.Table(m.TableName())
}

// TableName for current model
func (MDanmaku) TableName() string {
	return "m_danmaku"
}

// BeforeCreate hook
func (m *MDanmaku) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now
	return nil
}

// BeforeSave hook
func (m *MDanmaku) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// IncrLikeNum 点赞数或点踩数改动
func (m MDanmaku) IncrLikeNum(db *gorm.DB, id, addLikeNum int64) error {
	if addLikeNum == 0 {
		return nil
	}
	updateColumns := make(map[string]interface{}, 1)
	if addLikeNum > 0 {
		updateColumns["like_num"] = gorm.Expr("like_num + ?", addLikeNum)
	} else {
		// GREATEST(like_num, incrNum) - incrNum
		updateColumns["like_num"] = servicedb.SubSatExpr("like_num", int(-addLikeNum))
	}
	return db.Table(m.TableName()).Where("id = ?", id).Updates(updateColumns).Error
}
