package message

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testBlockDramaID = 1024
	testBlockUserID  = 66233
)

func TestIsDramaMessageBlockUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := IsDramaMessageBlockUser(testDramaID, testUserID, BlockTypeDanmaku)
	require.NoError(err)
	assert.False(ok)

	ok, err = IsDramaMessageBlockUser(testBlockDramaID, testBlockUserID, BlockTypeDanmaku)
	require.NoError(err)
	assert.True(ok)
}
