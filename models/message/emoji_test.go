package message

import (
	"strconv"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCheckEmojiAllowList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isBlocklistEmoji, err := checkEmojiAllowList("test")
	require.NoError(err)
	assert.False(isBlocklistEmoji)

	isBlocklistEmoji, err = checkEmojiAllowList("test🤩")
	require.NoError(err)
	assert.False(isBlocklistEmoji)

	isBlocklistEmoji, err = checkEmojiAllowList("test🤩😂🤩😂")
	require.NoError(err)
	assert.True(isBlocklistEmoji)
}

func TestGetEmojiAllowList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	allowEmoji, err := getEmojiAllowList()
	require.NoError(err)
	_, ok := allowEmoji.Load("🤩")
	assert.True(ok)
}

func BenchmarkSlcEmojiElems(b *testing.B) {
	var elems []string
	for i := 1; i <= 160; i++ {
		elems = append(elems, strconv.Itoa(i))
	}
	emojis := []string{"160", "160", "160", "160", "160"}

	b.ResetTimer()
	for n := 0; n < b.N; n++ {
		for _, emoji := range emojis {
			var exist bool
			for _, elem := range elems {
				if emoji == elem {
					exist = true
					break
				}
			}
			if !exist {
				break
			}
		}
	}
}

func BenchmarkMapEmojiElems(b *testing.B) {
	var elems []string
	for i := 1; i <= 160; i++ {
		elems = append(elems, strconv.Itoa(i))
	}
	emojis := []string{"160", "160", "160", "160", "160"}

	elemsMap := sync.Map{}
	for _, elem := range elems {
		elemsMap.Store(elem, struct{}{})
	}
	b.ResetTimer()
	for n := 0; n < b.N; n++ {
		for _, emoji := range emojis {
			if _, ok := elemsMap.Load(emoji); ok {
				break
			}
		}
	}
}
