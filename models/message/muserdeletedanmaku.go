package message

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// DeleteMSoundComment model
type DeleteMSoundComment struct {
	MSoundComment
	CommentID  int64 `gorm:"column:comment_id" json:"comment_id"`
	DeleteType int   `gorm:"column:delete_type" json:"-"` // 使用 soundcomment.DeleteType 常量
	DeleteTime int64 `gorm:"column:delete_time" json:"-"`
}

// TableName for current model
func (DeleteMSoundComment) TableName() string {
	return "delete_m_sound_comment"
}

// DeleteDanmaku 删除弹幕并归档，更新 MSound{} 弹幕数
func DeleteDanmaku(dmID int64, danmaku *MSoundComment, deleteType int) (err error) {
	err = servicedb.Tx(service.MessageDB, func(tx *gorm.DB) (err error) {
		if err = tx.Table(MSoundComment{}.TableName()).Where("id = ?", dmID).Delete("").Error; err != nil {
			return err
		}
		deleteDanmaku := NewDeleteDanmaku(danmaku, deleteType)
		if err = tx.Create(deleteDanmaku).Error; err != nil {
			return err
		}

		updateColumns := make(map[string]interface{}, 2)
		updateColumns["comment_count"] = gorm.Expr(servicedb.GreatestExpr("comment_count", "?")+" - ?", 1, 1)
		updateColumns["last_update_time"] = util.TimeNow().Unix()
		if err = service.DB.Model(sound.MSound{}).Where("id = ?", danmaku.SoundID).
			Updates(updateColumns).Error; err != nil {
			return err
		}
		return
	})
	return
}

// NewDeleteDanmaku 新建删除弹幕
func NewDeleteDanmaku(danmaku *MSoundComment, deleteType int) *DeleteMSoundComment {
	deleteDanmaku := DeleteMSoundComment{
		MSoundComment: *danmaku,
		CommentID:     danmaku.ID,
		DeleteType:    deleteType,
		DeleteTime:    util.TimeNow().Unix(),
	}
	// 违禁或涉政删除的弹幕没有主键 ID，为防止主键冲突 ID 设置为 0
	deleteDanmaku.ID = 0
	return &deleteDanmaku
}

// Archive 违规弹幕归档
func (delDanmaku *DeleteMSoundComment) Archive() error {
	return service.MessageDB.Table(delDanmaku.TableName()).Create(delDanmaku).Error
}
