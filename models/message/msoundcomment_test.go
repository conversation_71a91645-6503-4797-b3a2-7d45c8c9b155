package message

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
)

const testDramaID = 39588
const testSoundID = 23333

func TestMSoundComment_GetSTimeFloat(t *testing.T) {
	assert := assert.New(t)
	var err error

	// 设置数值
	var comment MSoundComment
	comment.STime = "10.5"

	// 第一次获取 sTime
	stime, err := comment.GetSTimeFloat()
	assert.NoError(err)
	assert.Equal(10.5, stime)
	assert.Equal(10.5, comment.sTimeFloat.Float64)
	assert.True(comment.sTimeFloat.Valid)

	// 第二次以后获取 sTimeFloat
	comment.sTimeFloat.Float64 = 10.6
	stime, err = comment.GetSTimeFloat()
	assert.NoError(err)
	assert.Equal(10.6, stime)
}

func TestMSoundComment_UnmarshalJSON(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	var err error

	// 设置数值
	var comment MSoundComment
	changeComment := map[string]interface{}{
		"id":    107,
		"color": 1,
	}
	marshal, err := json.Marshal(changeComment)
	require.NoError(err)

	// 改变 comment 数值
	comment.ID = 108

	// unmarshal 并确认 comment 数值正确
	err = comment.UnmarshalJSON(marshal)
	assert.NoError(err)
	assert.Equal(int64(108), comment.ID)
	assert.Equal(1, comment.Color)
}

func TestMSoundComment_Validate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		comment MSoundComment
		err     error
	)
	// 测试数值缺失的情况
	_, err = comment.Validate()
	assert.EqualError(err, "missing required value")

	// 设置数值
	comment.SoundID = 1
	comment.UserID = 12
	comment.STime = "1"

	comment.Text = "你妈死了"
	_, err = comment.Validate()
	assert.EqualError(err, "输入的弹幕中含有违规词汇喔")

	// 测试 emoji 为一个字符
	comment.Text = strings.Repeat("🇨🇳", DanmakuMaxLength)
	_, err = comment.Validate()
	require.NoError(err)

	word := "hello world"
	for i := 0; i < 10; i++ {
		word += word
	}
	comment.Text = word
	_, err = comment.Validate()
	assert.EqualError(err, "最多输入 35 字哦 _(:з」∠)_")

	comment.Text = "你好"
	isShamSend, err := comment.Validate()
	require.NoError(err)
	assert.False(isShamSend)

	comment.DramaID = testDramaID
	comment.Text = "你好TeSt"
	isShamSend, err = comment.Validate()
	require.NoError(err)
	assert.True(isShamSend)

	comment.Text = "😎😎😎😎😎😎😎😎"
	isShamSend, err = comment.Validate()
	require.NoError(err)
	assert.True(isShamSend)

	comment.DramaID = testBlockDramaID
	comment.UserID = testBlockUserID
	comment.Text = "今天是个好天气"
	isShamSend, err = comment.Validate()
	require.NoError(err)
	assert.True(isShamSend)
}

func TestMSoundComment_Insert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	var err error

	// 插入数据
	var comment MSoundComment
	comment.Text = "hello world"
	comment.SoundID = 569121
	comment.UserID = 10086
	jsonStr, _ := json.Marshal(comment)
	err = comment.UnmarshalJSON(jsonStr)
	assert.NoError(err)
	err = comment.Insert(comment.DB())
	assert.NoError(err)

	// 获取数据进行验证
	var data MSoundComment
	err = data.DB().Where("sound_id = ? AND user_id = ?", comment.SoundID,
		comment.UserID).First(&data).Error
	require.NoError(err)
	assert.Equal(comment.Text, data.Text)

	// 删除
	err = data.DB().Where("sound_id = ? AND user_id = ?", comment.SoundID,
		comment.UserID).Delete("").Error
	require.NoError(err)
}

func TestSaveDmData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	snd := sound.MSound{
		ID: 999999,
	}
	dm := MSoundComment{
		SoundID: 44809,
		UserID:  346286,
		Text:    "text",
		STime:   "0.5",
	}
	// 测试弹幕发送失败
	err := SaveDmData(&dm, &snd)
	assert.Equal("弹幕发送失败", err.Error())

	snd = sound.MSound{
		ID: 44809,
	}
	// 测试弹幕发送成功
	err = SaveDmData(&dm, &snd)
	require.NoError(err)
}

func TestMSoundCommentIncrLikeNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试新增弹幕点赞
	danmaku := MSoundComment{
		SoundID: 1,
		UserID:  1,
		Text:    "测试弹幕 - go",
	}
	err := danmaku.Insert(danmaku.DB())
	require.NoError(err)
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, 2))
	// 验证确实添加了点赞数量
	var likeNum int64
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Debug().Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(2), likeNum)

	// 测试减少弹幕点赞
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, -1))
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(1), likeNum)

	// 测试减少弹幕点赞数大于当前点赞数
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, -2))
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(0), likeNum)

	// 删除测试数据
	require.NoError(danmaku.DB().Where("user_id = ? AND sound_id = ?", 1, 1).
		Delete("").Error)
}

func TestGetNumberDanmakuBySoundDuration(t *testing.T) {
	assert := assert.New(t)

	// 测试小于等于半分钟
	soundDuration := 30 * time.Second.Milliseconds()
	assert.Equal(DanmakuNum30Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 1 分钟
	soundDuration = 60 * time.Second.Milliseconds()
	assert.Equal(DanmakuNum60Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 3 分钟
	soundDuration = 3 * time.Minute.Milliseconds()
	assert.Equal(DanmakuNum180Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 10 分钟
	soundDuration = 10 * time.Minute.Milliseconds()
	assert.Equal(DanmakuNum600Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 15 分钟
	soundDuration = 15 * time.Minute.Milliseconds()
	assert.Equal(DanmakuNum900Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 40 分钟
	soundDuration = 40 * time.Minute.Milliseconds()
	assert.Equal(DanmakuNum2400Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试小于等于 1 小时
	soundDuration = time.Hour.Milliseconds()
	assert.Equal(DanmakuNum3600Sec, GetNumberDanmakuBySoundDuration(soundDuration))

	// 测试大于 1 一小时
	soundDuration = 2 * time.Hour.Milliseconds()
	assert.Equal(DanmakuMaxNum, GetNumberDanmakuBySoundDuration(soundDuration))
}

func TestListDanmaku(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mSound := new(sound.MSound)
	require.NoError(mSound.DB().Where("id = ?", testSoundID).Delete("").Error)
	// 创建音频
	mSound.ID = testSoundID
	mSound.CommentCount = 20
	mSound.Duration = 3 * 1e4
	require.NoError(service.DB.Create(mSound).Error)

	// 创建弹幕
	require.NoError(service.MessageDB.Table(MSoundComment{}.TableName()).
		Where("sound_id = ?", testSoundID).Delete("").Error)
	for i := 0; i < 20; i++ {
		danmaku := &MSoundComment{
			SoundID: testSoundID,
			Text:    fmt.Sprintf("23333_%d", i),
			Pool:    DanmakuPoolNormal,
		}
		if i < 10 {
			danmaku.Pool = DanmakuPoolInferiorQuality
		}
		require.NoError(service.MessageDB.Create(danmaku).Error)
	}
	danmaku, err := ListDanmaku(testSoundID, DanmakuMaxNum)
	require.NoError(err)
	assert.Equal(10, len(danmaku))
}

func TestMSoundComment_CountRepeatDanmaku(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.MessageDB.Table(MSoundComment{}.TableName()).
		Where("sound_id = ?", testSoundID).Delete("").Error)

	dm := MSoundComment{
		UserID:  testUserID,
		SoundID: testSoundID,
		Text:    "23332gogogo",
		Pool:    DanmakuPoolNormal,
	}
	res, err := dm.CountRepeatDanmaku()
	require.NoError(err)
	assert.Zero(res)

	require.NoError(service.MessageDB.Create(&dm).Error)
	res, err = dm.CountRepeatDanmaku()
	require.NoError(err)
	assert.Equal(int64(1), res)
}
