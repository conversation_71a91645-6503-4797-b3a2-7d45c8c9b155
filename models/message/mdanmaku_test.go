package message

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMDanmakuTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_danmaku", MDanmaku{}.TableName())
}

func TestIncrLikeNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试新增弹幕点赞
	danmaku := MDanmaku{
		ElementID:   1,
		ElementType: ElementTypeNode,
		UserID:      1,
		Text:        "测试弹幕 - go",
	}
	err := danmaku.DB().Create(&danmaku).Error
	require.NoError(err)
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, 2))
	// 验证确实添加了点赞数量
	var likeNum int64
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Debug().Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(2), likeNum)

	// 测试减少弹幕点赞
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, -1))
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(1), likeNum)

	// 测试减少弹幕点赞数大于当前点赞数
	assert.NoError(danmaku.IncrLikeNum(danmaku.DB(), danmaku.ID, -2))
	err = danmaku.DB().Select("like_num").
		Where("id = ?", danmaku.ID).Row().Scan(&likeNum)
	require.NoError(err)
	assert.Equal(int64(0), likeNum)

	// 删除测试数据
	require.NoError(danmaku.DB().Where("user_id = ? AND element_id = ?", 1, 1).
		Delete("").Error)
}
