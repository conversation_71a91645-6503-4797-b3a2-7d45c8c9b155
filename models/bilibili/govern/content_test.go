package govern

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestNewContentReplyParam(t *testing.T) {
	assert := assert.New(t)

	param := NewContentReplyParam()
	assert.Equal("maoer", param.Business)
	assert.Equal("reply", param.SubType)
	assert.Equal("maoerAddReply", param.Scene)
}

func TestNewContentDMParam(t *testing.T) {
	assert := assert.New(t)

	param := NewContentDMParam()
	assert.Equal("maoer", param.Business)
	assert.Equal("dm", param.SubType)
	assert.Equal("maoerAddDM", param.Scene)
}

func TestNewEquipment(t *testing.T) {
	assert := assert.New(t)

	ue := common.UserEquipment{
		IP:        "127.0.0.1",
		UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		Origin:    params.URL.Main,
		BUVID:     "BUVID",
	}
	equipment := NewEquipment(&ue)
	assert.Equal(Android, equipment.Platform)
	assert.Equal("5.4.5", equipment.Build)
	assert.Equal("BUVID", equipment.BUVID)

	ue.UserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36 Edg/89.0.774.48"
	equipment = NewEquipment(&ue)
	assert.Equal(Web, equipment.Platform)
	assert.Equal("", equipment.Build)
	assert.Equal(params.URL.Main, equipment.Origin)

	ue.UserAgent = ""
	equipment = NewEquipment(&ue)
	assert.Empty(equipment.UserAgent)
	assert.Empty(equipment.MobiApp)
	assert.Equal(PlatformUndefined, equipment.Platform)
}

func TestNewSubjectStr(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(`{"id":1,"type":2,"title":"aaa","url":"https://www.missevan.com/sound/1"}`, newSubjectStr(1, 2, "aaa"))
}

func TestExtraSoundUrl(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("https://www.missevan.com/sound/12", extraSoundURL(12))
}

func TestNewAddComment(t *testing.T) {
	assert := assert.New(t)

	comment := soundcomment.Comment{
		ID:        12,
		Content:   "abcd",
		Type:      1,
		ElementID: 1234,
		CTime:     1615887183,
		UserID:    99,
		Username:  "name",
	}
	ue := common.UserEquipment{
		IP:        "127.0.0.1",
		UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		Origin:    "aaa",
	}
	param := NewAddComment(comment, ue, 3456, 30698, "标题", "/rpc/message/add-comment")
	assert.Equal(&ContentParam{
		CommonParam: NewContentReplyParam(),
		Contents: map[string]*Content{
			"0_12": {
				Content: "abcd",
				Metadata: MetaData{
					MID:       99,
					UserName:  "name",
					Timestamp: 1615887183,
					UpMID:     3456,
					Caller:    "maoer.service.api",
					API:       "/rpc/message/add-comment",
					Equipment: &Equipment{
						Platform:  Android,
						UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
						Build:     "5.4.5",
						MobiApp:   common.MobiAppAndroid,
						Origin:    "aaa",
						IP:        "127.0.0.1",
					},
				},
				Extra: Extra{
					ParentID: "0",
					ReplyID:  "12",
					DramaID:  "30698",
					Subject:  newSubjectStr(1234, 1, "标题"),
				},
			},
		},
	}, param)
	assert.JSONEq(`{
    "business": "maoer",
    "sub_type": "reply",
    "scene": "maoerAddReply",
    "contents": {
        "0_12": {
            "content": "abcd",
            "metadata": {
                "mid": 99,
                "username": "name",
                "caller": "maoer.service.api",
                "api": "/rpc/message/add-comment",
                "timestamp": 1615887183,
                "up_mid": 3456,
                "ip": "127.0.0.1",
                "origin": "aaa",
                "platform": 1,
                "mobi_app": "android_missevan",
                "user_agent": "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
                "build": "5.4.5"
            },
            "extra": {
                "subject": "{\"id\":1234,\"type\":1,\"title\":\"标题\",\"url\":\"https://www.missevan.com/sound/1234\"}",
                "drama_id": "30698",
                "parent_id": "0",
                "reply_id": "12"
            }
        }
    }
  }`, tutil.SprintJSON(param))
}

func TestNewAddSubComment(t *testing.T) {
	assert := assert.New(t)

	comment := soundcomment.Comment{
		ID:        12,
		Type:      1,
		ElementID: 1234,
		UserID:    99,
	}
	subComment := soundcomment.SubComment{
		ID:       123,
		Ctime:    1615887183,
		UserID:   100,
		Username: "name",
	}
	ue := common.UserEquipment{
		IP:        "127.0.0.1",
		UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		Origin:    "aaa",
	}
	param := NewAddSubComment(comment, subComment, ue, 3456, 30698, "标题", "fasd", "/rpc/message/add-subcomment")
	assert.Equal(&ContentParam{
		CommonParam: NewContentReplyParam(),
		Contents: map[string]*Content{
			"12_123": {
				Content: "fasd",
				Metadata: MetaData{
					MID:       100,
					UserName:  "name",
					Timestamp: 1615887183,
					UpMID:     3456,
					Caller:    "maoer.service.api",
					API:       "/rpc/message/add-subcomment",
					Equipment: &Equipment{
						Platform:  Android,
						UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
						Build:     "5.4.5",
						MobiApp:   common.MobiAppAndroid,
						Origin:    "aaa",
						IP:        "127.0.0.1",
					},
				},
				Extra: Extra{
					ParentID: "12",
					ReplyID:  "123",
					DramaID:  "30698",
					Subject:  newSubjectStr(1234, 1, "标题"),
				},
			},
		},
	}, param)
	assert.JSONEq(`{
    "business": "maoer",
    "sub_type": "reply",
    "scene": "maoerAddReply",
    "contents": {
        "12_123": {
            "content": "fasd",
            "metadata": {
                "mid": 100,
                "username": "name",
                "caller": "maoer.service.api",
                "api": "/rpc/message/add-subcomment",
                "timestamp": 1615887183,
                "up_mid": 3456,
                "platform": 1,
                "ip": "127.0.0.1",
                "origin": "aaa",
                "mobi_app": "android_missevan",
                "user_agent": "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
                "build": "5.4.5"
            },
            "extra": {
                "subject": "{\"id\":1234,\"type\":1,\"title\":\"标题\",\"url\":\"https://www.missevan.com/sound/1234\"}",
                "drama_id": "30698",
                "parent_id": "12",
                "reply_id": "123"
            }
        }
    }
  }`, tutil.SprintJSON(param))
}

func TestNewAddDM(t *testing.T) {
	assert := assert.New(t)

	dm := message.MSoundComment{
		ID:     12,
		Text:   "abcd",
		Date:   1615887183,
		UserID: 99,
		STime:  "10.13",
	}
	snd := sound.MSound{
		ID:       123,
		UserID:   100,
		Username: "name",
		Soundstr: "标题",
	}
	ue := common.UserEquipment{
		IP:        "127.0.0.1",
		UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
		Origin:    "aaa",
	}
	param := NewAddDM(dm, snd, ue, 30698, "姓名", "/rpc/message/add-dm")
	assert.Equal(&ContentParam{
		CommonParam: NewContentDMParam(),
		Contents: map[string]*Content{
			"12": {
				Content: "abcd",
				Metadata: MetaData{
					MID:       99,
					UserName:  "姓名",
					Timestamp: 1615887183,
					UpMID:     100,
					Caller:    "maoer.service.api",
					API:       "/rpc/message/add-dm",
					Equipment: &Equipment{
						Platform:  Android,
						UserAgent: "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
						Build:     "5.4.5",
						MobiApp:   common.MobiAppAndroid,
						Origin:    "aaa",
						IP:        "127.0.0.1",
					},
				},
				Extra: Extra{
					Subject: newSubjectStr(123, 1, "标题"),
					DramaID: "30698",
					DmID:    "12",
					DmSTime: "10.13",
				},
			},
		},
	}, param)
	assert.JSONEq(`{
    "business": "maoer",
    "sub_type": "dm",
    "scene": "maoerAddDM",
    "contents": {
        "12": {
            "content": "abcd",
            "metadata": {
                "mid": 99,
                "username": "姓名",
                "caller": "maoer.service.api",
                "api": "/rpc/message/add-dm",
                "timestamp": 1615887183,
                "up_mid": 100,
                "platform": 1,
                "mobi_app": "android_missevan",
                "origin": "aaa",
                "ip": "127.0.0.1",
                "user_agent": "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)",
                "build": "5.4.5"
            },
            "extra": {
                "subject": "{\"id\":123,\"type\":1,\"title\":\"标题\",\"url\":\"https://www.missevan.com/sound/123\"}",
                "drama_id": "30698",
                "dm_id": "12",
                "dm_stime": "10.13"
            }
        }
    }
  }`, tutil.SprintJSON(param))
}
