package govern

import "strconv"

// ReportResultParam 结果上报参数
type ReportResultParam struct {
	*CommonParam

	Results map[string]string `json:"results"`
	Final   bool              `json:"final"`
}

// NewReportResult 构建报告结果接口的参数
func (param *CommonParam) NewReportResult(result string, uniqueIDs ...int64) *ReportResultParam {
	if len(uniqueIDs) == 0 {
		return nil
	}
	var res ReportResultParam
	res.CommonParam = param
	res.Results = make(map[string]string, len(uniqueIDs))
	for _, uniqueID := range uniqueIDs {
		res.Results[strconv.FormatInt(uniqueID, 10)] = result
	}
	return &res
}
