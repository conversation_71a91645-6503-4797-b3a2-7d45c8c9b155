package govern

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestNewReportResult(t *testing.T) {
	assert := assert.New(t)

	param := NewContentReplyParam().NewReportResult(ReportResultPass, 12, 123, 1234)
	assert.Equal(&ReportResultParam{
		CommonParam: NewContentReplyParam(),
		Results: map[string]string{
			"12":   ReportResultPass,
			"123":  ReportResultPass,
			"1234": ReportResultPass,
		},
	}, param)
	assert.JSONEq(`{
    "business": "maoer",
    "sub_type": "reply",
    "scene": "maoerAddReply",
    "results": {
        "12": "pass",
        "123": "pass",
        "1234": "pass"
    },
    "final": false
    }`, tutil.SprintJSON(param))

	param = NewContentDMParam().NewReportResult(ReportResultPass, 12, 123, 1234)
	assert.Equal(&ReportResultParam{
		CommonParam: NewContentDMParam(),
		Results: map[string]string{
			"12":   ReportResultPass,
			"123":  ReportResultPass,
			"1234": ReportResultPass,
		},
	}, param)
	assert.JSONEq(`{
    "business": "maoer",
    "sub_type": "dm",
    "scene": "maoerAddDM",
    "results": {
        "12": "pass",
        "123": "pass",
        "1234": "pass"
    },
    "final": false
    }`, tutil.SprintJSON(param))
}
