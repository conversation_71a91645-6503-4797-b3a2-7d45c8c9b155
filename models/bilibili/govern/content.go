package govern

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/models/message"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/soundcomment"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// BusinessMaoer 常量
const BusinessMaoer = "maoer"

// SubType 类型
const (
	SubTypeReply = "reply"
	SubTypeDM    = "dm"
)

// Scene 类型
const (
	SceneMaoerAddReply = "maoerAddReply"
	SceneMaoerAddDM    = "maoerAddDM"
)

// Caller 调用方标识
const Caller = "maoer.service.api"

// ReportResult 类型
const (
	ReportResultPass   = "pass"
	ReportResultReject = "reject"
)

// CommonParam 社区治理公共参数
type CommonParam struct {
	Business string `json:"business,omitempty"`
	SubType  string `json:"sub_type,omitempty"`
	Scene    string `json:"scene,omitempty"`
}

// NewContentReplyParam 评论初始化函数
func NewContentReplyParam() *CommonParam {
	return &CommonParam{Business: BusinessMaoer, SubType: SubTypeReply, Scene: SceneMaoerAddReply}
}

// NewContentDMParam 弹幕初始化函数
func NewContentDMParam() *CommonParam {
	return &CommonParam{Business: BusinessMaoer, SubType: SubTypeDM, Scene: SceneMaoerAddDM}
}

// ContentParam 社区治理上报参数
type ContentParam struct {
	*CommonParam

	Contents map[string]*Content `json:"contents,omitempty"`
	Retry    bool                `json:"retry,omitempty"`
}

// Content 上报内容
type Content struct {
	Content  string   `json:"content"`
	Metadata MetaData `json:"metadata"`
	Extra    Extra    `json:"extra"`
}

// MetaData 基本信息
type MetaData struct {
	MID       int64  `json:"mid,omitempty"`
	UserName  string `json:"username,omitempty"`
	Caller    string `json:"caller,omitempty"`
	API       string `json:"api,omitempty"`
	Timestamp int64  `json:"timestamp,omitempty"` // 秒级时间戳
	UpMID     int64  `json:"up_mid,omitempty"`

	*Equipment
}

// PlatformType 设备类型
type PlatformType int

// 设备类型枚举
const (
	PlatformUndefined PlatformType = iota
	Android
	IOS
	Web
	H5
	Windows
	HarmonyOS
)

// Equipment 设备信息
type Equipment struct {
	Platform  PlatformType `json:"platform,omitempty"`
	MobiApp   string       `json:"mobi_app,omitempty"`
	Origin    string       `json:"origin,omitempty"`
	AppKey    string       `json:"app_key,omitempty"`
	AccessKey string       `json:"access_key,omitempty"`
	BUVID     string       `json:"buvid,omitempty"`
	IP        string       `json:"ip,omitempty"`
	Referer   string       `json:"referer,omitempty"`
	UserAgent string       `json:"user_agent,omitempty"`
	Build     string       `json:"build,omitempty"`
}

// NewEquipment 创建设备信息参数
func NewEquipment(ue *common.UserEquipment) (equipment *Equipment) {
	if ue.BUVID == "" {
		ue.BUVID = ue.EquipID
	}
	equipment = &Equipment{
		IP:        ue.IP,
		BUVID:     ue.BUVID,
		UserAgent: ue.UserAgent,
		Origin:    ue.Origin,
		Platform:  PlatformUndefined,
	}
	if ue.UserAgent == "" {
		return
	}
	e := util.NewEquipment(ue.UserAgent)
	if e.FromApp {
		equipment.Build = e.AppVersion
	}
	switch e.OS {
	case util.Android:
		equipment.Platform = Android
		equipment.MobiApp = common.MobiAppAndroid
	case util.IOS:
		equipment.Platform = IOS
		equipment.MobiApp = common.MobiAppIOS
	case util.Web:
		equipment.Platform = Web
	case util.MobileWeb:
		equipment.Platform = H5
	case util.HarmonyOS:
		equipment.Platform = HarmonyOS
		equipment.MobiApp = common.MobiAppHarmonyOS
	}
	return
}

// Extra 上报额外信息
type Extra struct {
	Subject string `json:"subject"`
	DramaID string `json:"drama_id,omitempty"`

	// reply
	ParentID string `json:"parent_id,omitempty"`
	ReplyID  string `json:"reply_id,omitempty"`

	// dm
	DmID    string `json:"dm_id,omitempty"`
	DmSTime string `json:"dm_stime,omitempty"`
}

// subject 上报额外信息通用字段
type subject struct {
	ID    int64  `json:"id,omitempty"`
	Type  int64  `json:"type,omitempty"`
	Title string `json:"title,omitempty"`
	URL   string `json:"url,omitempty"`
}

func newSubjectStr(id int64, t int64, title string) string {
	subjectStr, _ := json.Marshal(subject{
		ID:    id,
		Type:  t,
		Title: title,
		URL:   extraSoundURL(id),
	})
	return string(subjectStr)
}

// Call 上报接口
func (param ContentParam) Call() {
	res, err := service.Govern.Content(param)
	if err != nil {
		logger.Error(err)
		return
	}
	list := make([]int64, 0, len(res))
	for _, v := range res {
		list = append(list, v.UniqueID)
	}
	reportResultParam := param.NewReportResult(ReportResultPass, list...)
	err = service.Govern.ReportResult(reportResultParam)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func extraSoundURL(soundID int64) string {
	return fmt.Sprintf("%ssound/%d", params.URL.Main, soundID)
}

// NewAddComment 构建评论参数
func NewAddComment(comment soundcomment.Comment, ue common.UserEquipment, upUserID, dramaID int64, title, url string) *ContentParam {
	var param ContentParam
	param.CommonParam = NewContentReplyParam()
	param.Contents = map[string]*Content{
		commentContentKey(0, comment.ID): {
			Content: comment.Content,
			Metadata: MetaData{
				MID:       comment.UserID,
				UserName:  comment.Username,
				Timestamp: comment.CTime,
				UpMID:     upUserID,
				Caller:    Caller,
				API:       url,
				Equipment: NewEquipment(&ue),
			},
			Extra: Extra{
				Subject: newSubjectStr(comment.ElementID, int64(comment.Type), title),
				DramaID: strconv.FormatInt(dramaID, 10),
				// 父评论时 parent_id = 0, reply_id = 父评论 ID
				ParentID: "0",
				ReplyID:  strconv.FormatInt(comment.ID, 10),
			},
		},
	}
	return &param
}

// NewAddSubComment 构建子评论参数
func NewAddSubComment(comment soundcomment.Comment, subComment soundcomment.SubComment, ue common.UserEquipment, upUserID, dramaID int64, title, commentContent, url string) *ContentParam {
	var param ContentParam
	param.CommonParam = NewContentReplyParam()
	param.Contents = map[string]*Content{
		commentContentKey(comment.ID, subComment.ID): {
			Content: commentContent,
			Metadata: MetaData{
				MID:       subComment.UserID,
				UserName:  subComment.Username,
				Timestamp: subComment.Ctime,
				UpMID:     upUserID,
				Caller:    Caller,
				API:       url,
				Equipment: NewEquipment(&ue),
			},
			Extra: Extra{
				Subject:  newSubjectStr(comment.ElementID, int64(comment.Type), title),
				DramaID:  strconv.FormatInt(dramaID, 10),
				ParentID: strconv.FormatInt(comment.ID, 10),
				ReplyID:  strconv.FormatInt(subComment.ID, 10),
			},
		},
	}
	return &param
}

func commentContentKey(parentID, replyID int64) string {
	return fmt.Sprintf("%d_%d", parentID, replyID)
}

// NewAddDM 构建弹幕参数
func NewAddDM(dm message.MSoundComment, snd sound.MSound, ue common.UserEquipment, dramaID int64, username, url string) *ContentParam {
	var param ContentParam
	param.CommonParam = NewContentDMParam()
	param.Contents = map[string]*Content{
		dmContentKey(dm.ID): {
			Content: dm.Text,
			Metadata: MetaData{
				MID:       dm.UserID,
				UserName:  username,
				Timestamp: dm.Date,
				UpMID:     snd.UserID,
				Caller:    Caller,
				API:       url,
				Equipment: NewEquipment(&ue),
			},
			Extra: Extra{
				Subject: newSubjectStr(snd.ID, soundcomment.TypeSound, snd.Soundstr),
				DramaID: strconv.FormatInt(dramaID, 10),
				DmID:    strconv.FormatInt(dm.ID, 10),
				DmSTime: dm.STime,
			},
		},
	}
	return &param
}

func dmContentKey(dmID int64) string {
	return strconv.FormatInt(dmID, 10)
}
