package gaia

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/antispam"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

// decisions
const (
	DecisionForbid   = "forbid"    // 屏蔽，即 blacklist 情况
	DecisionSelfView = "self_view" // 假发送，即 evil 情况
)

// 直播文本检查场景（猫耳业务自定义的）
const (
	IMSceneHorn     = "horn"      // 喇叭
	IMSceneMessage  = "message"   // 直播间聊天消息
	IMSceneDanmaku  = "danmaku"   // 直播（付费）弹幕
	IMSceneDiyGift  = "diy_gift"  // DIY 礼物
	IMSceneLuckyBag = "lucky_bag" // 福袋
	IMSceneQuestion = "question"  // 提问
)

// ParamLiveIM 直播间消息额外参数
type ParamLiveIM struct {
	ParamFilter

	RoomID        int64  `json:"room_id"`
	RoomCatalogID int64  `json:"room_catalog_id"`
	RoomCreatorID int64  `json:"room_creator_id"`
	MsgID         string `json:"msg_id"`
	MsgSentTime   string `json:"msg_sent_time"`
	UserIsAdmin   bool   `json:"user_is_admin"`
	UserHasMedal  bool   `json:"user_has_medal"`
	AllowAD       bool   `json:"allow_ad"`
	Pass          bool   `json:"pass"`
	AliyunLabels  string `json:"aliyun_labels"`
	Scene         string `json:"scene,omitempty"`
}

// Check 检查并补全、转换参数
func (param *ParamLiveIM) Check(c util.UserContext) bool {
	if param.MsgID == "" {
		return false
	}
	param.CTime = util.TimeNow().Format(util.TimeFormatHMS)
	param.MsgSentTime = param.CTime

	param.ContentID = param.MsgID
	param.ContentType = ContentTypeText
	param.Area = servicegaia.SceneLiveIM
	param.load(c)
	return true
}

// SetScanResult 设置文字检查结果
func (param *ParamLiveIM) SetScanResult(res []*scan.CheckResult, aliRes []antispam.AliyunTextRespElem) {
	param.Pass = true
	for i := range res {
		if !res[i].Pass {
			param.Pass = false
			break
		}
	}
	// NOTICE: aliRes 可能是 null
	labels, err := json.Marshal(aliRes)
	if err != nil {
		logger.Error(err)
		return
	}
	param.AliyunLabels = string(labels)
}
