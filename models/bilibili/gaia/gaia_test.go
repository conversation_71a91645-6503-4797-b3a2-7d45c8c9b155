package gaia

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(ParamFilter{}, "user_id", "mid", "equip_id", "buvid", "ip", "platform", "ctime",
		"action", "api", "origin", "referer", "user_agent", "build", "area",
		"content", "content_type", "content_id", "code")
	kc.Check(ParamLiveIM{}, "room_id", "room_catalog_id", "room_creator_id",
		"msg_id", "msg_sent_time", "user_is_admin", "user_has_medal", "allow_ad",
		"pass", "aliyun_labels", "scene")
	kc.CheckOmitEmpty(ParamLiveIM{}, "scene")
}

func TestParamFilterCheck(t *testing.T) {
	assert := assert.New(t)

	c := &util.SmartUserContext{}
	param := ParamFilter{}
	// web 端
	param.Referer = "http://www.missevan.com"
	param.load(c)
	assert.Equal(PlatFormWeb, param.Platform)
	assert.Empty(param.Build)
	// Android 端
	param.EquipID = "!23"
	param.UserAgent = "MissEvanApp/5.4.5 (Android;8.0.0;honor FRD-DL00 HWFRD)"
	param.load(c)
	assert.Equal(PlatFormAndroid, param.Platform)
	assert.Equal("5.4.5", param.Build)
	assert.Equal(common.MobiAppAndroid, param.Origin)
	assert.Empty(param.EquipID)
	// iOS 端
	param.UserID = 12
	c.UserEquipID = "test-equip-id"
	param.UserAgent = "MissEvanApp/4.4.5 (iOS;8.0.0;honor FRD-DL00 HWFRD)"
	param.load(c)
	assert.Equal(PlatFormIOS, param.Platform)
	assert.Equal(common.MobiAppIOS, param.Origin)
	assert.Equal("4.4.5", param.Build)
	assert.Zero(param.UserID)
	assert.Equal(int64(12), param.MID)
}

func TestParamFilterBeforeDo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := ParamFilter{
		clientIP: "127.0.0.1",
	}
	req := httptest.NewRequest(http.MethodPost, "/", nil)
	require.NoError(param.BeforeDo(req))
	assert.Equal(param.clientIP, req.Header.Get("x-Forwarded-For"))
}
