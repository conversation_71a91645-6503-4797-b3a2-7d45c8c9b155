package gaia

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service/antispam"
	servicegaia "github.com/MiaoSiLa/missevan-go/service/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveIMCheck(t *testing.T) {
	assert := assert.New(t)

	var c util.SmartUserContext
	var param ParamLiveIM
	assert.False(param.Check(c))
	param.UserAgent = "web"
	param.MsgID = "123"
	param.Referer = "http://www.missevan.com"
	assert.True(param.Check(c))
	assert.Equal(param.MsgID, param.ContentID)
	assert.Equal(ContentTypeText, param.ContentType)
	assert.Equal(servicegaia.SceneLiveIM, param.Area)
	assert.NotEmpty(param.CTime)
	assert.Equal(param.CTime, param.MsgSentTime)
}

func TestLiveIMSetScanResult(t *testing.T) {
	assert := assert.New(t)

	var param ParamLiveIM
	param.SetScanResult([]*scan.CheckResult{{Pass: true}}, []antispam.AliyunTextRespElem{})
	assert.True(param.Pass)
	assert.Equal("[]", param.AliyunLabels)
	param.SetScanResult([]*scan.CheckResult{{Pass: true}, {Pass: false}}, nil)
	assert.False(param.Pass)
	assert.Equal("null", param.AliyunLabels)
}
