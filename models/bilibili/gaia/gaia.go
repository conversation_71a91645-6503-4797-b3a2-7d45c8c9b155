// Package gaia 接入 B站内容过滤
// 文档：https://info.bilibili.co/pages/viewpage.action?pageId=138903280
package gaia

import (
	"net/http"

	"github.com/MiaoSiLa/missevan-go/models/bilibili/common"
	"github.com/MiaoSiLa/missevan-go/util"
)

// ContentType
const (
	ContentTypeText  = "text"
	ContextTypeImage = "image"
)

// Platform
const (
	PlatFormAndroid   = "android"
	PlatFormIOS       = "ios"
	PlatFormWeb       = "web"
	PlatFromHarmonyOS = "harmony"
)

// ParamFilter 风控引擎公共参数，已经加了 filter 参数
type ParamFilter struct {
	UserID      int64  `json:"user_id,omitempty"`
	MID         int64  `json:"mid"`
	EquipID     string `json:"equip_id,omitempty"`
	BUVID       string `json:"buvid"`
	IP          string `json:"ip"`
	Platform    string `json:"platform"`
	CTime       string `json:"ctime"`
	Action      string `json:"action"`
	API         string `json:"api"`
	Origin      string `json:"origin"`
	Referer     string `json:"referer"`
	UserAgent   string `json:"user_agent"`
	Build       string `json:"build"`
	Area        string `json:"area"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
	ContentID   string `json:"content_id"`

	Code int `json:"code"`

	clientIP string
}

// load 补全、转换必要参数
func (param *ParamFilter) load(c util.UserContext) {
	param.clientIP = c.ClientIP()
	param.MID = param.UserID
	// 不把 user_id 这个字段传给 gaia
	param.UserID = 0
	// 不把 equip_id 这个字段传给 gaia
	if param.EquipID != "" {
		param.BUVID = param.EquipID
		param.EquipID = ""
	}
	e := util.NewEquipment(param.UserAgent)
	switch e.OS {
	case util.Android:
		param.Platform = PlatFormAndroid
		// gaia 的 Origin 是 MobiApp
		param.Origin = common.MobiAppAndroid
	case util.IOS:
		param.Platform = PlatFormIOS
		param.Origin = common.MobiAppIOS
	case util.HarmonyOS:
		param.Platform = PlatFromHarmonyOS
		param.Origin = common.MobiAppHarmonyOS
	default:
		param.Platform = PlatFormWeb
		param.Origin = ""
	}
	if e.FromApp {
		param.Build = e.AppVersion
	}
}

// BeforeDo 客户端请求前执行函数
func (param *ParamFilter) BeforeDo(req *http.Request) error {
	v := param.clientIP
	if v != "" {
		req.Header.Add("X-Forwarded-For", v)
	}
	/* 因为是外部调用，所以不传 token 和 equip_id
	v = param.token
	if v != "" {
		req.AddCookie(&http.Cookie{Name: "token", Value: v})
	}
	v = param.EquipID
	if v != "" {
		req.AddCookie(&http.Cookie{Name: "equip_id", Value: v})
	}
	*/
	return nil
}
