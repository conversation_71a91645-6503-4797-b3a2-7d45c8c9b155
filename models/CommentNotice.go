package models

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/commentnotice"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/util"
)

// CommentNotice is the model struct of `commentnotice`
type CommentNotice struct {
	ID int64 `gorm:"column:id"`
	CommentNoticeFields
}

// CommentNoticeFields contains the fields of CommentNotice
type CommentNoticeFields struct {
	CUserID    int64  `gorm:"column:c_user_id"`
	CUserName  string `gorm:"column:c_user_name"`
	AUserID    int64  `gorm:"column:a_user_id"`
	AUserName  string `gorm:"column:a_user_name"`
	Type       int    `gorm:"column:type"`
	EID        int64  `gorm:"column:eId"`
	Title      string `gorm:"column:title"`
	CommentID  int64  `gorm:"column:comment_id"`
	Sub        int    `gorm:"column:sub"`
	IsRead     int    `gorm:"column:isread"`
	NoticeType int    `gorm:"column:notice_type"`
}

const commentNoticeTable = "commentnotice"

// TableName returns the table name of model CommentNotice
func (CommentNotice) TableName() string {
	return commentNoticeTable
}

// AddCommentNotice adds comment notices into database
// TODO: 后续 beRemindedUsers 参数考虑改为直接传入需要评论提醒的用户，避免在方法内做提醒类型判断
func AddCommentNotice(db *gorm.DB, userID int64, userName string, atUsers, beRemindedUsers map[int64]string, /* map[userID]username */
	commentType int, elementID int64, commentID int64, title string, isSub int) error {
	title = util.Substr(title, 0, 30)
	var notices []CommentNoticeFields
	for k, v := range beRemindedUsers {
		notice := CommentNoticeFields{
			CUserID:   userID,
			CUserName: userName,
			AUserID:   k,
			AUserName: v,
			Type:      commentType,
			EID:       elementID,
			CommentID: commentID,
			Title:     title,
			Sub:       isSub,
		}
		// 若存在被 @ 的用户 （使该条记录 notice_type 为 1 即为 @ 的提醒，其它为 0 评论提醒）
		if name, ok := atUsers[k]; ok {
			if name == v {
				notice.NoticeType = commentnotice.NoticeTypeAt
			}
		}
		notices = append(notices, notice)
	}
	if len(notices) == 0 {
		return nil
	}
	return helper.BatchInsert(db, commentNoticeTable, notices)
}
