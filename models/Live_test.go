package models

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

const testUserID = 6666666

// TestLiveTableName
func TestLiveTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live", Live{}.TableName())
}

func TestLiveExists(t *testing.T) {
	assert := assert.New(t)
	service.DB.Where("user_id = ?", testUserID).Delete("")
	assert.False(Live{}.Exists(testUserID))
	testCreateTestLive(testUserID)
	assert.True(Live{}.Exists(testUserID))
	service.DB.Where("user_id = ?", testUserID).Delete("")
}

// createTestLive 创建直播间
func testCreateTestLive(userID int64) {
	timeStamp := util.TimeNow().Unix()
	l := Live{
		UserID:       userID,
		RoomID:       timeStamp,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
		Title:        "直播间标题",
	}
	// 创建直播间
	service.DB.Save(&l)
}
