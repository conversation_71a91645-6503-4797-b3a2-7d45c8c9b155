package models

import (
	"regexp"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestListPrizes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	eventID := int64(148)

	ap0 := &AnPrize{
		Name:    "测试 test 图片地址",
		Pic:     "test://prize/202102/04/55a168d9d721c860614928ecd859eb6d203355.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap0).Error)
	ap1 := &AnPrize{
		Name:    "测试 http 图片地址",
		Pic:     "http://static.missevan.com/mimages/201610/25/83d35860a4919794761c3f308e9152a1194403.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap1).Error)
	ap2 := &AnPrize{
		Name:    "测试 https 图片地址",
		Pic:     "https://static.missevan.com/mimages/201610/25/83d35860a4919794761c3f308e9152a1194403.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap2).Error)
	prizes, err := ListPrizes(eventID)
	require.NoError(err)
	assert.GreaterOrEqual(len(prizes), 2)
	var ap0Index, ap1Index, ap2Index int
	var ap0Found, ap1Found, ap2Found bool
	for i, v := range prizes {
		if v.ID == ap0.ID {
			ap0Found = true
			ap0Index = i
		}
		if v.ID == ap1.ID {
			ap1Found = true
			ap1Index = i
		}
		if v.ID == ap2.ID {
			ap2Found = true
			ap2Index = i
		}
	}
	assert.True(ap0Found)
	assert.True(ap1Found)
	assert.True(ap2Found)
	tutil.PrintJSON(prizes[ap0Index])
	assert.False(strings.HasPrefix(prizes[ap0Index].Pic, "oss"))
	assert.Equal(ap1.Pic, prizes[ap1Index].Pic)
	assert.Equal(ap2.Pic, prizes[ap2Index].Pic)
	require.NoError(service.DB.Where("id in (?)", []int64{ap0.ID, ap1.ID, ap2.ID}).Delete(AnPrize{}).Error)
}

func TestIsPrize(t *testing.T) {
	assert := assert.New(t)

	var pointRegex = regexp.MustCompile(`^([1-9]\d*)\s*鱼干$`)

	n, ok := IsPrize(" 20 鱼干 ", pointRegex)
	assert.True(ok)
	assert.Equal(20, n)
	_, ok = IsPrize("二十鱼干", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" 99999999999999999999999 鱼干 ", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" 0 鱼干", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" -1 鱼干", pointRegex)
	assert.False(ok)
}
