package soundcomment

import (
	"context"
	"database/sql"
	"encoding/json"
	"regexp"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models"
	"github.com/MiaoSiLa/missevan-go/models/mcommentblackuser"
	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 被评论的元素类型
const (
	TypeSound       = 1
	TypeAlbum       = 2
	TypeNews        = 3
	TypeTag         = 4
	TypeTopic       = 6
	TypeEvent       = 7
	TypeVoiceCard   = 8
	TypeOmikujiCard = 9
)

// LabelOfType 评论类型对应的字符串
var LabelOfType = map[int]string{
	TypeSound:       "音频",
	TypeAlbum:       "音单",
	TypeNews:        "新闻",
	TypeTag:         "频道",
	TypeTopic:       "专题",
	TypeEvent:       "活动",
	TypeVoiceCard:   "语音包",
	TypeOmikujiCard: "求签包",
}

// 新老用户 每天最大评论数
const (
	NewUserCommentMaxNum = 20
	UserCommentMaxNum    = 500
)

// additional constants definition
const (
	CheckedDeleted   = -1 // 已被软删除的评论
	CheckedNormal    = 1  // 正常评论
	CheckedViolation = 2  // 违规评论

	threeDays int64 = 3600 * 24 * 3

	CommentMaxLength = 1000 // 评论、子评论长度最大 1000 个字符
	CommentMinLength = 1    // 评论、子评论长度最小 1 个字符
)

const (
	// DefaultScoreRatio 评论打分基础系数
	DefaultScoreRatio int64 = 1.3 * 1e2

	defaultScoreDislikeProportionRatio int64 = 1e2       // 评论打分点踩系数
	scoreBlacklistRatioBlack           int64 = 0.6 * 1e2 // 评论打分黑名单系数，命中黑名单后时系数
	scoreBlacklistRatioCommon          int64 = 1e2       // 评论打分黑名单系数，未命中黑名单时系数
)

var (
	regexCommentRepalceLine = regexp.MustCompile(`\n\s+\n`)
	regexCommentPunctuation = regexp.MustCompile(`^\p{P}$`)
)

// Comment is the model struct of `sound_comment`
type Comment struct {
	ID            int64           `gorm:"column:id" json:"id"`
	Content       string          `gorm:"column:comment_content" json:"comment_content"`
	Type          int             `gorm:"column:c_type" json:"c_type"`
	ElementID     int64           `gorm:"column:element_id" json:"element_id"`
	CTime         int64           `gorm:"column:ctime" json:"ctime"`
	UserID        int64           `gorm:"column:userid" json:"-"`
	Username      string          `gorm:"column:username" json:"-"`
	SubCommentNum int64           `gorm:"column:sub_comment_num" json:"sub_comment_num"`
	LikeNum       int64           `gorm:"column:like_num" json:"like_num"`
	DislikeNum    int64           `gorm:"column:dislike_num" json:"-"`
	ControlNum    int64           `gorm:"column:control_num" json:"-"`
	Floor         int64           `gorm:"column:floor" json:"floor"`
	Checked       int             `gorm:"column:checked" json:"-"`   // 评论属性，-1：软删除；1：正常评论；2：评论违规
	IP            string          `gorm:"column:ip" json:"-"`        // 用户 IP
	IPDetail      json.RawMessage `gorm:"column:ip_detail" json:"-"` // IP 详情（格式化 JSON）

	ScoreRatio                  int64 `gorm:"column:score_ratio" json:"-"`                    // 评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新评论基础分系数」更新）
	ScoreDislikeProportionRatio int64 `gorm:"column:score_dislike_proportion_ratio" json:"-"` // 评论打分点踩系数，默认 1e2，添加评论时 Redis.Get(comment:dislike:proportion)
	ScoreBlacklistRatio         int64 `gorm:"column:score_blacklist_ratio" json:"-"`          // 评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2
	// Score                       int64 `gorm:"-" json:"-"`                                     // 评论得分，虚拟列自动写入，计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540

	IPLocation string           `gorm:"-" json:"ip_location"` // IP 属地（境内：一级行政区名称，境外：国家名称）
	User       *CommentUserInfo `gorm:"-" json:"user"`
}

// CommentUserInfo 用户信息
type CommentUserInfo struct {
	UserID         int64  `json:"user_id"`                    // 用户 ID
	Username       string `json:"username"`                   // 用户昵称
	IconURL        string `json:"iconurl"`                    // 用户头像
	Authenticated  uint   `json:"authenticated"`              // 认证标识，1：黑 V；2：金 V；3：蓝 V
	AvatarFrameURL string `json:"avatar_frame_url,omitempty"` // 用户头像框地址，没有头像框时不下发该字段
	IsVip          int    `json:"is_vip"`                     // 当前评论用户是否为会员。0: 否；1: 是
}

const tableSoundComment = "sound_comment"

// DB the db instance of Comment model
func (c Comment) DB() *gorm.DB {
	return service.MessageDB.Table(c.TableName())
}

// TableName returns the table name of Comment model
func (Comment) TableName() string {
	return tableSoundComment
}

func (c *Comment) beforeSave() {
	c.CTime = util.TimeNow().Unix()
	if c.Checked == 0 {
		c.Checked = CheckedNormal
	}
}

// Commentable interface
type Commentable interface {
	IncrCommentCount(db *gorm.DB, id int64) error
	// TODO: DecrCommentCount(db *gorm.DB, id int64) error
	IncrSubCommentCount(db *gorm.DB, id int64) error
}

func (c *Comment) afterSave(db *gorm.DB) (err error) {
	if c.Checked == CheckedNormal {
		var commentable Commentable
		switch c.Type {
		case TypeSound:
			commentable = &sound.MSound{}
		case TypeAlbum:
			commentable = &models.MAlbum{}
		}
		if commentable != nil {
			err = commentable.IncrCommentCount(db, c.ElementID)
		}
	}
	return
}

// Save saves the comment into database and updates comment.ID with the last_insert_id
func (c *Comment) Save(db *gorm.DB) error {
	c.beforeSave()
	err := service.MessageDB.Table(c.TableName()).Save(c).Error
	if err != nil {
		return err
	}
	return c.afterSave(db)
}

// IncrCommentLikeOrDislikeNum 评论的点赞数或点踩数改动
func (c *Comment) IncrCommentLikeOrDislikeNum(db *gorm.DB, id int64, addLikeNum, addDislikeNum int64) error {
	updateColumns := make(map[string]interface{}, 2)
	if addLikeNum > 0 {
		updateColumns["like_num"] = gorm.Expr("like_num + ?", addLikeNum)
	} else if addLikeNum < 0 {
		updateColumns["like_num"] = gorm.Expr(
			servicedb.IFExpr("like_num > ?", "like_num - ?", "0"), -addLikeNum, -addLikeNum)
	}
	if addDislikeNum > 0 {
		updateColumns["dislike_num"] = gorm.Expr("dislike_num + ?", addDislikeNum)
	} else if addDislikeNum < 0 {
		updateColumns["dislike_num"] = gorm.Expr(
			servicedb.IFExpr("dislike_num > ?", "dislike_num - ?", "0"), -addDislikeNum, -addDislikeNum)
	}
	return service.MessageDB.Table(c.TableName()).Where("id = ?", id).
		UpdateColumn(updateColumns).Error
}

// IncrSubCommentNum id 评论的子评论数 +1
func IncrSubCommentNum(db *gorm.DB, id int64) error {
	return service.MessageDB.Table(tableSoundComment).Where("id = ?", id).
		UpdateColumn("sub_comment_num", gorm.Expr("sub_comment_num + 1")).Error
}

// CheckLimit 判断用户评论是否已达评论上限
func CheckLimit(userID int64) (bool, error) {
	now := util.TimeNow()
	ctime, err := user.MowangskUser{}.GetCTimeByID(userID)
	if err != nil {
		return false, err
	}

	var limitCount int
	if now.Unix()-ctime > threeDays {
		limitCount = UserCommentMaxNum
	} else {
		limitCount = NewUserCommentMaxNum
	}

	lock := serviceredis.LockUserComment1.Format(userID)
	count, err := service.Redis.Incr(lock).Result()
	if err != nil {
		return false, err
	}

	if count > int64(limitCount) {
		return false, nil
	}

	ttl, err := service.Redis.TTL(lock).Result()
	if err != nil {
		logger.Errorf("CheckLimit TTL error: %v", err)
		// PASS
	} else {
		if ttl < 0 {
			err = service.Redis.Expire(lock, util.NextDayTime(now).Sub(now)).Err()
			if err != nil {
				logger.Errorf("CheckLimit Expire error: %v", err)
				// PASS
			}
		}
	}

	return true, nil
}

// Exists 返回评论是否存在
func (c Comment) Exists(ID int64) (bool, error) {
	var id int64
	err := c.DB().Select("id").
		Where("id = ? AND checked <> ?", ID, CheckedDeleted).Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// ReplaceCommentLine 文字间最多空 1 行
func ReplaceCommentLine(content string) string {
	content = strings.TrimSpace(regexCommentRepalceLine.ReplaceAllString(content, "\n\n"))
	return content
}

// CheckCommentPunctuation 检查是否输入单个标点符号
func CheckCommentPunctuation(content string) bool {
	content = strings.TrimSpace(content)
	return !regexCommentPunctuation.MatchString(content)
}

// FindScoreDislikeProportionRatio 返回评论/子评论打分点踩系数
func FindScoreDislikeProportionRatio() int64 {
	k := serviceredis.KeyCommentDislikeProportion0.Format()
	val, err := service.Redis.Get(k).Int64()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		return defaultScoreDislikeProportionRatio
	}
	return val
}

// FindScoreBlacklistRatio 返回评论/子评论打分黑名单系数
func FindScoreBlacklistRatio(userID int64) int64 {
	if userID == 0 {
		return scoreBlacklistRatioCommon
	}
	inBlacklist, err := mcommentblackuser.InCommentBlackUserList(userID)
	if err != nil {
		logger.Error(err)
		// PASS
		return scoreBlacklistRatioCommon
	}
	if inBlacklist {
		return scoreBlacklistRatioBlack
	}
	return scoreBlacklistRatioCommon
}

// commentMore 发布评论 databus 消息中的扩展消息
type commentMore struct {
	DramaID int64 `json:"drama_id,omitempty"` // 剧集 ID。ElementType 为 1 时，需要记录单音所属剧集 ID
}

// CommentLog 发布评论 databus 消息
type CommentLog struct {
	UserID          int64        `json:"user_id"`
	CreateTime      int64        `json:"create_time"`  // 创建时间戳。单位：秒
	ElementType     int          `json:"element_type"` // 元素类型。1：单音评论，2：音单评论，3：新闻评论，4：为频道，5：用户评论，6：专题评论，7：活动评论，8：语音包评论，9：为求签语音评论
	ElementID       int64        `json:"element_id"`
	CommentID       int64        `json:"comment_id"` // 当前评论 ID
	IsSub           bool         `json:"is_sub,omitempty"`
	ParentCommentID int64        `json:"parent_comment_id,omitempty"` // 当前评论的父评论 ID，仅在当前评论为子评论时有值
	More            *commentMore `json:"more,omitempty"`              // 额外信息
}

// SendLog 添加发布评论 databus 消息
func SendLog(userID int64, elementType int, elementID, commentID int64, isSub bool, parentCommentID, dramaID int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	logInfo := CommentLog{
		UserID:          userID,
		CreateTime:      util.TimeNow().Unix(),
		ElementType:     elementType,
		ElementID:       elementID,
		CommentID:       commentID,
		IsSub:           isSub,
		ParentCommentID: parentCommentID,
	}
	if dramaID != 0 {
		logInfo.More = &commentMore{DramaID: dramaID}
	}
	key := keys.DatabusKeyCommentLog1.Format(userID)
	err := service.Databus.AppLogPub.Send(ctx, key, logInfo)
	if err != nil {
		return err
	}
	return nil
}
