package soundcomment

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestNewDeleteSubComment(t *testing.T) {
	assert := assert.New(t)

	soundSubComment := &SubComment{
		CommentContent: "test",
	}
	delSoundSubComment := NewDeleteSoundSubComment(soundSubComment, DeleteTypeBySelf)
	assert.Equal(soundSubComment.CommentContent, delSoundSubComment.CommentContent)
	assert.NotZero(delSoundSubComment.DeleteTime)
	assert.Equal(DeleteTypeBySelf, delSoundSubComment.DeleteType)
}

func TestSoundSubCommentArchive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	delSoundSubComment := DeleteSoundSubComment{}
	delSoundSubComment.CommentContent = "hello world"
	delSoundSubComment.DeleteType = DeleteTypeByAdmin
	delSoundSubComment.DeleteTime = util.TimeNow().Unix()
	require.NoError(delSoundSubComment.Archive())
	newDelSoundSubComment := DeleteSoundSubComment{}
	require.NoError(service.MessageDB.Where("id = ?", delSoundSubComment.ID).First(&newDelSoundSubComment).Error)
	assert.Equal(delSoundSubComment.CommentContent, newDelSoundSubComment.CommentContent)
	assert.Equal(delSoundSubComment.DeleteType, newDelSoundSubComment.DeleteType)
}

func TestDeleteSoundSubCommentTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	deleteSoundSubComment := DeleteSoundSubComment{}
	kc.Check(deleteSoundSubComment, "sub_comment_id", "delete_type", "delete_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(deleteSoundSubComment, "sub_comment_id")
}
