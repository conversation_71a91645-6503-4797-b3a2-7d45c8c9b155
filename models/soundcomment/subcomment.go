package soundcomment

import (
	"database/sql"
	"encoding/json"
	"errors"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/jinzhu/gorm"
	"github.com/rivo/uniseg"

	"github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// SubComment 子评论
type SubComment struct {
	ID                          int64           `gorm:"column:id" json:"id"`
	CommentContent              string          `gorm:"column:comment_content" json:"comment_content"`
	CommentID                   int64           `gorm:"column:comment_id" json:"comment_id"`
	Ctime                       int64           `gorm:"column:ctime" json:"ctime"`
	UserID                      int64           `gorm:"column:userid" json:"-"`
	Username                    string          `gorm:"column:username" json:"-"`
	LikeNum                     int64           `gorm:"column:like_num" json:"like_num"`
	DislikeNum                  int64           `gorm:"column:dislike_num" json:"-"`
	Floor                       int64           `gorm:"column:floor" json:"floor"`
	Checked                     int             `gorm:"column:checked" json:"-"`                        // 评论属性，-1：软删除；1：正常评论；2：评论违规
	IP                          string          `gorm:"column:ip" json:"-"`                             // 用户 IP
	IPDetail                    json.RawMessage `gorm:"column:ip_detail" json:"-"`                      // IP 详情（格式化 JSON）
	ScoreRatio                  int64           `gorm:"column:score_ratio" json:"-"`                    // 评论打分基础系数，默认 1.3*1e2，在 1 个自然小时后改为 1e2（使用新版任务编排「rds-评论库业务/每小时更新子评论基础分系数」更新）
	ScoreDislikeProportionRatio int64           `gorm:"column:score_dislike_proportion_ratio" json:"-"` // 评论打分点踩系数，默认 1e2，添加评论时 Redis.Get(comment:dislike:proportion)
	ScoreBlacklistRatio         int64           `gorm:"column:score_blacklist_ratio" json:"-"`          // 评论打分黑名单系数，默认 1e2，命中黑名单后系数后改为 0.6*1e2
	// Score                       int64           `gorm:"-" json:"-"`                                     // 评论得分，虚拟列自动写入，计算公式：https://info.missevan.com/pages/viewpage.action?pageId=15370540

	User *CommentUserInfo `gorm:"-" json:"user"`
}

// DB the db instance of SubComment model
func (s SubComment) DB() *gorm.DB {
	return service.MessageDB.Table(s.TableName())
}

// TableName returns the table name of SubComment model
func (SubComment) TableName() string {
	return "sound_sub_comment"
}

var regexReply = regexp.MustCompile("^回复 @.*?[:：]")

// Validate 验证某些字段，比如长度
func (s *SubComment) Validate() error {
	if s.CommentContent == "" || s.CommentID == 0 {
		return errors.New("评论不能为空~")
	}
	s.CommentContent = strings.TrimSpace(util.FilterSpecialCodes(s.CommentContent, false))
	// 优先进行空格和换行处理
	// 参考文档地址：https://info.missevan.com/pages/viewpage.action?pageId=28283751
	s.CommentContent = ReplaceCommentLine(s.CommentContent)
	if uniseg.GraphemeClusterCount(s.CommentContent) > CommentMaxLength {
		return errors.New("字数太多装不下啦~")
	}
	comment := strings.TrimSpace(regexReply.ReplaceAllString(s.CommentContent, ""))
	if utf8.RuneCountInString(comment) < CommentMinLength {
		return errors.New("评论不能为空~")
	}
	if ok := CheckCommentPunctuation(comment); !ok {
		return errors.New("不可发送单个标点符号")
	}
	return nil
}

// Save 保存一条数据
func (s *SubComment) Save(db *gorm.DB) error {
	s.beforeSave()
	err := service.MessageDB.Table(s.TableName()).Save(s).Error
	if err != nil {
		return err
	}
	return s.afterSave(db)
}

func (s *SubComment) beforeSave() {
	s.Ctime = util.TimeNow().Unix()
}

func (s *SubComment) afterSave(db *gorm.DB) error {
	if s.Checked == CheckedNormal {
		err := IncrSubCommentNum(db, s.CommentID)
		if err != nil {
			return err
		}
		var soundcomment Comment
		err = Comment{}.DB().Select("element_id, checked").Where("id = ? AND c_type = ?", s.CommentID, TypeSound).
			Take(&soundcomment).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				return nil
			}
			return err
		}
		if soundcomment.Checked == CheckedNormal {
			sound := sound.MSound{ID: soundcomment.ElementID}
			return sound.IncrSubCommentCount(db, soundcomment.ElementID)
		}
	}
	return nil
}

// IncrCommentLikeOrDislikeNum 子评论的点赞数或点踩数改动
func (s *SubComment) IncrCommentLikeOrDislikeNum(db *gorm.DB, id, addLikeNum, addDislikeNum int64) error {
	updateColumns := make(map[string]interface{}, 2)
	if addLikeNum > 0 {
		updateColumns["like_num"] = gorm.Expr("like_num + ?", addLikeNum)
	} else if addLikeNum < 0 {
		updateColumns["like_num"] = gorm.Expr(
			servicedb.IFExpr("like_num > ?", "like_num - ?", "0"), -addLikeNum, -addLikeNum)
	}
	if addDislikeNum > 0 {
		updateColumns["dislike_num"] = gorm.Expr("dislike_num + ?", addDislikeNum)
	} else if addDislikeNum < 0 {
		updateColumns["dislike_num"] = gorm.Expr(
			servicedb.IFExpr("dislike_num > ?", "dislike_num - ?", "0"), -addDislikeNum, -addDislikeNum)
	}
	return service.MessageDB.Table(s.TableName()).Where("id = ?", id).
		Updates(updateColumns).Error
}

// Exists 返回子评论是否存在
func (s SubComment) Exists(ID int64) (bool, error) {
	var id int64
	err := s.DB().Table(s.TableName()).Select("id").
		Where("id = ? AND checked <> ?", ID, CheckedDeleted).Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
