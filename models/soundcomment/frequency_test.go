package soundcomment

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestIsLimited(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clearFrequencyData()

	commentRule := NewCommentRule(CommentRuleComment, TypeSound, 1, 99, 100)
	checked, err := commentRule.IsLimited()
	require.NoError(err)
	assert.False(checked)

	for i := 0; i < 3; i++ {
		require.NoError(commentRule.addCooldown())
	}

	checked, err = commentRule.IsLimited()
	require.NoError(err)
	assert.True(checked)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time {
		return now.Add(time.Minute + time.Second)
	})
	defer util.SetTimeNow(nil)

	checked, err = commentRule.IsLimited()
	require.NoError(err)
	assert.False(checked)
}

func TestMaxFrequency(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clearFrequencyData()

	commentRule1 := NewCommentRule(CommentRuleComment, TypeSound, 1, 99, 100)
	count, err := commentRule1.maxFrequency()
	require.NoError(err)
	assert.Equal(int64(5), count)

	commentRule2 := NewCommentRule(CommentRuleComment, TypeSound, 1, 1, 100)
	count, err = commentRule2.maxFrequency()
	require.NoError(err)
	assert.Equal(int64(20), count)

	for i := 0; i < 3; i++ {
		require.NoError(commentRule1.addCooldown())
		require.NoError(commentRule2.addCooldown())
	}

	count, err = commentRule1.maxFrequency()
	require.NoError(err)
	assert.Zero(count)

	count, err = commentRule2.maxFrequency()
	require.NoError(err)
	assert.Zero(count)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time {
		return now.Add(time.Minute + time.Second)
	})
	defer util.SetTimeNow(nil)

	count, err = commentRule1.maxFrequency()
	require.NoError(err)
	assert.Equal(int64(3), count)

	count, err = commentRule2.maxFrequency()
	require.NoError(err)
	assert.Equal(int64(3), count)
}

func TestCommentRuleAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	clearFrequencyData()

	commentRule := NewCommentRule(CommentRuleComment, TypeSound, 1, 99, 100)
	checked, err := commentRule.IsLimited()
	require.NoError(err)
	assert.False(checked)

	for i := 0; i < 5; i++ {
		require.NoError(commentRule.Add(int64(i + 1)))
	}

	checked, err = commentRule.IsLimited()
	require.NoError(err)
	assert.True(checked)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time {
		return now.Add(time.Minute + time.Second)
	})
	defer util.SetTimeNow(nil)

	checked, err = commentRule.IsLimited()
	require.NoError(err)
	assert.False(checked)
}

func clearFrequencyData() {
	service.Redis.Del(serviceredis.KeyFrequencyTimes3.Format(1, TypeSound, 100))
	service.MessageDB.Delete(Cooldown{}, "1 = 1")
}

func TestIsRepeatComment(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := time.Unix(100000, 0)
	util.SetTimeNow(func() time.Time {
		return now
	})
	defer util.SetTimeNow(nil)

	wait := func(d time.Duration) {
		now = now.Add(d)
	}

	commentRule := NewCommentRule(CommentRuleComment, TypeSound, 1, 99, 100)
	f := func(elementID, userID int64, content string) error {
		return Comment{}.DB().Create(&Comment{
			Content:   content,
			Type:      TypeSound,
			ElementID: elementID,
			CTime:     util.TimeNow().Unix(),
			UserID:    userID,
			Checked:   1,
		}).Error
	}

	// 第一条可以评论
	checked, err := commentRule.IsRepeatComment(1, "哈哈哈")
	require.NoError(err)
	assert.False(checked)

	// 其他分区可以评论
	checked, err = commentRule.IsRepeatComment(2, "哈哈哈")
	require.NoError(err)
	assert.False(checked)

	// 子分区可以评论
	commentRule.t = CommentRuleSubComment
	checked, err = commentRule.IsRepeatComment(1, "哈哈哈")
	require.NoError(err)
	assert.False(checked)

	// 相同内容不能评论
	commentRule.t = CommentRuleComment
	require.NoError(f(1, 1, "哈哈哈"))
	checked, err = commentRule.IsRepeatComment(1, "哈哈哈")
	require.NoError(err)
	assert.True(checked)

	// 3 分钟后可以评论
	wait(3*time.Minute + time.Second)
	checked, err = commentRule.IsRepeatComment(1, "哈哈哈")
	require.NoError(err)
	assert.False(checked)

	// 隔条评论可以评论
	require.NoError(f(1, 1, "嘿嘿"))
	checked, err = commentRule.IsRepeatComment(1, "哈哈哈")
	require.NoError(err)
	assert.False(checked)
}
