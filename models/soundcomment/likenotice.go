package soundcomment

import (
	"database/sql"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// NoticeNotRead 消息未读
	NoticeNotRead int = 0
	// NoticeAlreadyRead 消息已读
	NoticeAlreadyRead int = 1
)

const likeContent = "赞了这条评论"

// LikeNotice is the model struct of `likenotice`
type LikeNotice struct {
	ID        int64  `gorm:"column:id"`
	CUserID   int64  `gorm:"column:c_user_id"`  // 点赞用户
	AUserID   int64  `gorm:"column:a_user_id"`  // 被点赞用户
	Type      int    `gorm:"column:type"`       // 资源类型：1：音频，2：专辑，3：新闻，4：频道，6：专题，7：活动
	CommentID int64  `gorm:"column:comment_id"` // 评论 ID
	Title     string `gorm:"column:title"`      // 评论内容
	IsRead    int    `gorm:"column:isread"`     // 是否已读
	Time      int64  `gorm:"column:time"`       // 点赞时间
	ElementID int64  `gorm:"column:eId"`        // 资源 ID
	Sub       int    `gorm:"column:sub"`        // 父评论或子评论
	Content   string `gorm:"column:content"`    // 消息内容
}

const likeNoticeTable = "like_notice"

// TableName returns the table name of model LikeNotice
func (LikeNotice) TableName() string {
	return likeNoticeTable
}

// BeforeCreate hook
func (likeNotice *LikeNotice) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	likeNotice.Time = now
	likeNotice.Content = likeContent
	return nil
}

// AddLikeNotice adds like comment notices into database，返回是否成功新增点赞提醒
func AddLikeNotice(db *gorm.DB, commentInterface interface{}, userID int64, isSub bool) (bool, error) {
	var likeNotice LikeNotice
	if isSub {
		subComment := commentInterface.(SubComment)
		if subComment.UserID == userID {
			// 若给自己的评论点赞，则不提醒
			return false, nil
		}
		// 查找父评论（不包含已被软删除的评论）
		comment := Comment{}
		err := comment.DB().Select("c_type, element_id").
			Where("id = ? AND checked <> ?", subComment.CommentID, CheckedDeleted).
			First(&comment).Error
		if err != nil {
			return false, err
		}
		likeNotice = LikeNotice{
			CUserID:   userID,
			AUserID:   subComment.UserID,
			Type:      comment.Type,
			CommentID: subComment.ID,
			Title:     subComment.CommentContent,
			ElementID: comment.ElementID,
			Sub:       CommentSub,
			IsRead:    NoticeNotRead,
			Content:   likeContent,
		}
	} else {
		comment := commentInterface.(Comment)
		if comment.UserID == userID {
			// 若给自己的评论点赞，则不提醒
			return false, nil
		}
		likeNotice = LikeNotice{
			CUserID:   userID,
			AUserID:   comment.UserID,
			Type:      comment.Type,
			CommentID: comment.ID,
			Title:     comment.Content,
			ElementID: comment.ElementID,
			IsRead:    NoticeNotRead,
		}
	}
	exists, err := likeNotice.Exists(db, likeNotice.CommentID, likeNotice.CUserID, likeNotice.Sub)
	if err != nil {
		return false, err
	}
	if exists {
		// 若消息已存在，不再创建新消息
		return false, nil
	}
	err = db.Create(&likeNotice).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

// Exists 返回用户评论点赞消息提醒是否存在
func (likeNotice LikeNotice) Exists(db *gorm.DB, commentID, userID int64, sub int) (bool, error) {
	var id int64
	err := db.Table(likeNotice.TableName()).Select("id").
		Where("comment_id = ? AND c_user_id = ? AND sub = ?", commentID, userID, sub).
		Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
