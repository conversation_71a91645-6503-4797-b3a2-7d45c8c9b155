package soundcomment

import (
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	// DeleteTypeBySelf 用户删除，包含 UP 主删除自己的评论或弹幕
	DeleteTypeBySelf = iota
	// DeleteTypeByUP UP 主删除
	DeleteTypeByUP
	// DeleteTypeByAdmin 管理员删除
	DeleteTypeByAdmin
	// DeleteTypeByViolation 违禁及涉政删除
	DeleteTypeByViolation
)

// DeleteComment model
type DeleteComment struct {
	Comment
	CommentID  int64 `gorm:"column:comment_id" json:"comment_id"`
	DeleteType int   `gorm:"column:delete_type" json:"-"`
	DeleteTime int64 `gorm:"column:delete_time" json:"-"`
}

// TableName for current model
func (DeleteComment) TableName() string {
	return "delete_sound_comment"
}

// NewDeleteComment 新建删除评论
func NewDeleteComment(comment *Comment, deleteType int) *DeleteComment {
	delComment := DeleteComment{
		Comment:    *comment,
		CommentID:  comment.ID,
		DeleteType: deleteType,
		DeleteTime: util.TimeNow().Unix(),
	}
	// 违禁或涉政删除的评论没有主键 ID，为防止主键冲突 ID 设置为 0
	delComment.ID = 0
	return &delComment
}

// Archive 违规评论归档
func (comment *DeleteComment) Archive() error {
	return service.MessageDB.Table(comment.TableName()).Create(comment).Error
}
