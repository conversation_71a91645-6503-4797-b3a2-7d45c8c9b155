package soundcomment

import (
	"database/sql"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
)

// CommentSub 子评论类型
const CommentSub int = 1

const (
	// TypeCommentLike 点赞类型
	TypeCommentLike = 1
	// TypeCommentDislike 点踩类型
	TypeCommentDislike = 2
)

// CommentLike is the model struct of `comment_like`
type CommentLike struct {
	ID           int64 `gorm:"column:id"`
	CommentID    int64 `gorm:"column:cid"`
	Sub          int   `gorm:"column:sub"`
	UserID       int64 `gorm:"column:userid"`
	Type         int   `gorm:"column:type"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
}

const tableCommentLike = "comment_like"

// TableName returns the table name of Comment model
func (CommentLike) TableName() string {
	return tableCommentLike
}

// CommentLikeAble interface
type CommentLikeAble interface {
	IncrCommentLikeOrDislikeNum(db *gorm.DB, id int64, addLikeNum, addDislikeNum int64) error
}

func (c *CommentLike) afterSave(db *gorm.DB) (err error) {
	var commenLikeAble CommentLikeAble
	switch c.Sub {
	case CommentSub:
		commenLikeAble = &SubComment{}
	default:
		commenLikeAble = &Comment{}
	}
	var addLikeNum, addDislikeNum int64
	if c.Type == TypeCommentLike {
		// 评论点赞数 +1
		addLikeNum = 1
		// 查询是否之前点过踩，若有则删除点踩记录且评论点踩数 -1
		hasDislike, err := c.Exists(db, c.CommentID, c.UserID, c.Sub, TypeCommentDislike)
		if err != nil {
			return err
		}
		if hasDislike {
			err = c.Delete(db, c.CommentID, c.UserID, c.Sub, TypeCommentDislike, false)
			if err != nil {
				return err
			}
			addDislikeNum = -1
		}
	} else {
		// 评论点踩数 +1
		addDislikeNum = 1
		// 查询是否之前点过赞，若有则则删除点赞记录且评论点赞数 -1
		hasLike, err := c.Exists(db, c.CommentID, c.UserID, c.Sub, TypeCommentLike)
		if err != nil {
			return err
		}
		if hasLike {
			err = c.Delete(db, c.CommentID, c.UserID, c.Sub, TypeCommentLike, false)
			if err != nil {
				return err
			}
			addLikeNum = -1
		}
	}
	return commenLikeAble.IncrCommentLikeOrDislikeNum(db, c.CommentID, addLikeNum, addDislikeNum)
}

// Exists 返回评论是否存在
func (c CommentLike) Exists(db *gorm.DB, commentID, userID int64, sub, likeType int) (bool, error) {
	var id int64
	err := db.Table(c.TableName()).Select("id").
		Where("cid = ? AND userid = ? AND sub = ? AND type = ?", commentID, userID, sub, likeType).
		Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// BeforeUpdate hook
func (c *CommentLike) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", util.TimeNow().Unix())
}

// BeforeCreate hook
func (c *CommentLike) BeforeCreate(scope *gorm.Scope) error {
	now := util.TimeNow().Unix()
	err := scope.SetColumn("create_time", now)
	if err == nil {
		err = scope.SetColumn("modified_time", now)
	}
	return err
}

// Save 保存记录
func (c *CommentLike) Save(db *gorm.DB) error {
	err := db.Save(c).Error
	if err != nil {
		return err
	}
	return c.afterSave(db)
}

// Delete 删除记录
func (c CommentLike) Delete(db *gorm.DB, commentID, userID int64, sub, likeType int, updateCommentLikeOrDislikeNum bool) error {
	delete := db.Table(c.TableName()).
		Where("cid = ? AND userid = ? AND sub = ? AND type = ?", commentID, userID, sub, likeType).
		Delete("")
	if delete.Error != nil {
		return delete.Error
	}
	if delete.RowsAffected == 0 || !updateCommentLikeOrDislikeNum {
		// 若删除记录数量为 0 或不需要更新冗余字段，则直接返回
		return nil
	}
	// 修改关联表冗余字段（评论表点踩数）
	var commentLikeAble CommentLikeAble
	switch sub {
	case CommentSub:
		commentLikeAble = &SubComment{}
	default:
		commentLikeAble = &Comment{}
	}
	var addLikeNum, addDislikeNum int64
	if likeType == TypeCommentLike {
		addLikeNum = -1
	} else {
		addDislikeNum = -1
	}
	err := commentLikeAble.IncrCommentLikeOrDislikeNum(db, commentID, addLikeNum, addDislikeNum)
	return err
}
