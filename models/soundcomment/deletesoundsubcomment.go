package soundcomment

import (
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// DeleteSoundSubComment 删除子评论
type DeleteSoundSubComment struct {
	SubComment
	SubCommentID int64 `gorm:"column:sub_comment_id" json:"sub_comment_id"`
	DeleteType   int   `gorm:"column:delete_type" json:"-"`
	DeleteTime   int64 `gorm:"column:delete_time" json:"-"`
}

// TableName returns the table name of DeleteSoundSubComment model
func (DeleteSoundSubComment) TableName() string {
	return "delete_sound_sub_comment"
}

// NewDeleteSoundSubComment 新建删除子评论
func NewDeleteSoundSubComment(subComment *SubComment, deleteType int) *DeleteSoundSubComment {
	delSubComment := DeleteSoundSubComment{
		SubComment:   *subComment,
		SubCommentID: subComment.ID,
		DeleteType:   deleteType,
		DeleteTime:   util.TimeNow().Unix(),
	}
	// 违禁或涉政删除的子评论没有主键 ID，为防止主键冲突 ID 设置为 0
	delSubComment.ID = 0
	return &delSubComment
}

// Archive 违规子评论归档
func (delSoundSubComment *DeleteSoundSubComment) Archive() error {
	return service.MessageDB.Create(delSoundSubComment).Error
}
