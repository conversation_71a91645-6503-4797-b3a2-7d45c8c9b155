package soundcomment

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestNewDeleteComment(t *testing.T) {
	assert := assert.New(t)

	soundComment := &Comment{
		Content: "test",
	}
	delComment := NewDeleteComment(soundComment, DeleteTypeBySelf)
	assert.Equal(soundComment.Content, delComment.Content)
	assert.NotZero(delComment.DeleteTime)
	assert.Equal(DeleteTypeBySelf, delComment.DeleteType)
}

func TestDeleteCommentArchive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	delComment := DeleteComment{}
	delComment.Content = "hello world"
	delComment.DeleteTime = util.TimeNow().Unix()
	require.NoError(delComment.Archive())
	newDelComment := new(DeleteComment)
	require.NoError(service.MessageDB.Table(delComment.TableName()).Where("id = ?", delComment.ID).Find(newDelComment).Error)
	assert.Equal(delComment.Content, newDelComment.Content)
	assert.Equal(delComment.DeleteType, newDelComment.DeleteType)
}

func TestDeleteCommentTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	deleteSoundComment := DeleteComment{}
	kc.Check(deleteSoundComment, "comment_id", "delete_type", "delete_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(deleteSoundComment, "comment_id")
}
