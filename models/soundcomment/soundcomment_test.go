package soundcomment

import (
	"encoding/json"
	"math"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
	// add the "teardown" code here
}

var testUserID int64 = 765325

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CommentUserInfo{}, "user_id", "username", "iconurl", "authenticated", "avatar_frame_url")
	kc.CheckOmitEmpty(CommentUserInfo{}, "avatar_frame_url")
}

func TestCheckLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// new user
	var user user.MowangskUser
	user.ID = testUserID
	user.CTime = util.TimeNow().Unix()

	// NOTICE: 没有创建 app_missevan_sso.m_user 可能有测试受影响
	err := service.DB.Where("id = ?", testUserID).Assign(user).FirstOrCreate(&user).Error
	require.NoError(err)

	ok, err := CheckLimit(testUserID)
	require.NoError(err)
	assert.True(ok)

	lock := serviceredis.LockUserComment1.Format(testUserID)
	defer service.Redis.Del(lock)

	_, err = service.Redis.Set(lock, NewUserCommentMaxNum, 0).Result()
	require.NoError(err)

	ok, err = CheckLimit(testUserID)
	require.NoError(err)
	assert.False(ok)

	// old user
	user.CTime = util.TimeNow().AddDate(0, 0, -4).Unix()
	err = service.DB.Where("id = ?", testUserID).Assign(user).FirstOrCreate(&user).Error
	require.NoError(err)

	ok, err = CheckLimit(testUserID)
	require.NoError(err)
	assert.True(ok)

	_, err = service.Redis.Set(lock, UserCommentMaxNum-1, 0).Result()
	require.NoError(err)

	ok, err = CheckLimit(testUserID)
	require.NoError(err)
	assert.True(ok)

	ok, err = CheckLimit(testUserID)
	require.NoError(err)
	assert.False(ok)

	ttl, err := service.Redis.TTL(lock).Result()
	require.NoError(err)
	now := util.TimeNow()
	secondTTL := util.NextDayTime(now).Unix() - now.Unix()

	abs := math.Abs(float64(int64(ttl/time.Second) - secondTTL))
	assert.LessOrEqual(abs, 2.0)
}

func TestSave(t *testing.T) {
	assert := assert.New(t)

	var comment Comment
	err := comment.Save(service.DB)
	assert.NoError(err)
	assert.NotZero(comment.ID)
}

func TestReplaceCommentLine(t *testing.T) {
	assert := assert.New(t)
	// 测试连续换行，文字间最多空 1 行
	content := "哈哈\n\n\n哈哈"
	assert.Equal("哈哈\n\n哈哈", ReplaceCommentLine(content))

	// 测试换行 + 空格，文字间最多空 1 行
	content = "哈哈\n\n \n 哈哈 "
	assert.Equal("哈哈\n\n 哈哈", ReplaceCommentLine(content))

	// 测试换行 + 空格，文字间最多空 1 行
	content = "\n哈哈\n\n 哈哈 \n"
	assert.Equal("哈哈\n\n 哈哈", ReplaceCommentLine(content))

	// 测试换行 + 空格，文字间最多空 1 行
	content = "哈哈 \n 哈哈"
	assert.Equal("哈哈 \n 哈哈", ReplaceCommentLine(content))

	// 测试换行 + 空格，文字间最多空 1 行
	content = "哈哈\n   \n \n  \n   哈哈"
	assert.Equal("哈哈\n\n   哈哈", ReplaceCommentLine(content))

	// 测试连续空格，显示原评
	content = "哈哈  哈哈"
	assert.Equal("哈哈  哈哈", ReplaceCommentLine(content))

	// 测试可以文字输入
	content = "哈哈"
	assert.Equal("哈哈", ReplaceCommentLine(content))
}

func TestCheckCommentPunctuation(t *testing.T) {
	assert := assert.New(t)

	// 测试不能输入单个标点符号
	content := "。"
	assert.False(CheckCommentPunctuation(content))

	// 测试不能输入单个标点符号
	content = " 。"
	assert.False(CheckCommentPunctuation(content))

	// 测试不能输入单个标点符号
	content = "。 "
	assert.False(CheckCommentPunctuation(content))

	// 测试不能输入单个标点符号
	content = "\n。"
	assert.False(CheckCommentPunctuation(content))

	// 测试不能输入单个标点符号
	content = "。\n"
	assert.False(CheckCommentPunctuation(content))

	// 测试不能输入单个标点符号
	content = " \n。\n "
	assert.False(CheckCommentPunctuation(content))

	// 测试可以输入多个相同的标点符号
	content = "。。。"
	assert.True(CheckCommentPunctuation(content))

	// 测试可以输入多个不同的标点符号
	content = "？。，"
	assert.True(CheckCommentPunctuation(content))

	// 测试可以文字输入
	content = "测试"
	assert.True(CheckCommentPunctuation(content))
}

func TestFindScoreDislikeProportionRatio(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	k := serviceredis.KeyCommentDislikeProportion0.Format()

	require.NoError(service.Redis.Del(k).Err())
	v := FindScoreDislikeProportionRatio()
	assert.EqualValues(defaultScoreDislikeProportionRatio, v)

	require.NoError(service.Redis.Set(k, "0.1", 0).Err())
	v = FindScoreDislikeProportionRatio()
	assert.EqualValues(defaultScoreDislikeProportionRatio, v)

	require.NoError(service.Redis.Set(k, "3", 0).Err())
	v = FindScoreDislikeProportionRatio()
	assert.EqualValues(3, v)
}

func TestFindScoreBlacklistRatio(t *testing.T) {
	assert := assert.New(t)

	v := FindScoreBlacklistRatio(0)
	assert.EqualValues(scoreBlacklistRatioCommon, v)

	v = FindScoreBlacklistRatio(999999998)
	assert.EqualValues(scoreBlacklistRatioCommon, v)

	v = FindScoreBlacklistRatio(999999999)
	assert.EqualValues(scoreBlacklistRatioBlack, v)
}

func TestSendLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	userID, parentCommentID, commentID, elementID, dramaID := int64(12), int64(1), int64(1001), int64(906), int64(90)
	commentType := TypeSound
	err := SendLog(userID, commentType, elementID, commentID, true, parentCommentID, dramaID)
	require.NoError(err)
	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	message := <-pubMsgs
	assert.Equal("comment_log:12", message.Key)
	expectBytes, err := json.Marshal(CommentLog{
		UserID:          userID,
		CreateTime:      util.TimeNow().Unix(),
		ElementType:     TypeSound,
		ElementID:       elementID,
		CommentID:       commentID,
		IsSub:           true,
		ParentCommentID: parentCommentID,
		More:            &commentMore{DramaID: dramaID},
	})
	require.NoError(err)
	actualBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, actualBytes)
}
