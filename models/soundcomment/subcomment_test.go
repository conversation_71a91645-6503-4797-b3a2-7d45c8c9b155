package soundcomment

import (
	"strings"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	msound "github.com/MiaoSiLa/missevan-go/models/sound"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestValidateAndSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var subComment SubComment
	err := subComment.Validate()
	assert.EqualError(err, "评论不能为空~")

	var comment Comment
	err = comment.DB().Take(&comment).Where("c_type = ?", TypeSound).Error
	require.NoError(err)

	subComment.CommentID = comment.ID
	subComment.CommentContent = strings.Repeat("1", CommentMaxLength+1)
	err = subComment.Validate()
	assert.EqualError(err, "字数太多装不下啦~")

	// 测试 emoji 为一个字符
	subComment.CommentContent = strings.Repeat("🇨🇳", CommentMaxLength)
	require.NoError(subComment.Validate())

	subComment.CommentContent = "回复 @user :"
	err = subComment.Validate()
	assert.EqualError(err, "评论不能为空~")

	subComment.CommentContent = "回复 @user："
	err = subComment.Validate()
	assert.EqualError(err, "评论不能为空~")

	tutil.TestRollbackTx(t, service.DB, func(db *gorm.DB) error {
		return subComment.Save(db)
	})
}

func TestSubCommentSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试违规子评论
	comment := SubComment{
		CommentID: 1,
		Checked:   CheckedViolation,
	}
	err := comment.Save(service.DB)
	assert.NoError(err)
	assert.NotZero(comment.ID)
	var subCommentsCount int64
	err = msound.MSound{}.DB().Select("sub_comments_count").Where("id = ?", 233).
		Row().Scan(&subCommentsCount)
	require.NoError(err)
	assert.Equal(int64(107), subCommentsCount)

	// 测试正常子评论
	comment = SubComment{
		CommentID: 1,
		Checked:   CheckedNormal,
	}
	err = comment.Save(service.DB)
	assert.NoError(err)
	assert.NotZero(comment.ID)
	err = msound.MSound{}.DB().Select("sub_comments_count").Where("id = ?", 233).
		Row().Scan(&subCommentsCount)
	require.NoError(err)
	assert.Equal(int64(108), subCommentsCount)
}
