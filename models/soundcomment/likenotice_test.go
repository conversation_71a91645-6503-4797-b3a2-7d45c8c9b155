package soundcomment

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestLikeNoticeTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(likeNoticeTable, LikeNotice{}.TableName())
}

func TestAddLikeNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建测试用评论
	comment := Comment{
		Content:   "测试评论内容 - go",
		Type:      TypeSound,
		ElementID: 1, // 该值不影响测试结果
		UserID:    TestUser2ID,
		Username:  "测试用户",
		Checked:   CheckedNormal,
	}
	err := comment.Save(service.DB)
	require.NoError(err)

	// 测试正常添加评论点赞提醒情况
	result, err := AddLikeNotice(service.DB, comment, TestUser1ID, false)
	require.NoError(err)
	assert.True(result)

	// 测试对同一个用户的评论添加点赞提醒时只添加一条提醒
	result, err = AddLikeNotice(service.DB, comment, TestUser1ID, false)
	require.NoError(err)
	assert.False(result)
	// 有提醒记录
	assert.True(LikeNotice{}.Exists(service.DB, comment.ID, TestUser1ID, 0))

	// 测试给自己的评论点赞
	result, err = AddLikeNotice(service.DB, comment, TestUser2ID, false)
	require.NoError(err)
	assert.False(result)
	// 无提醒记录
	assert.False(LikeNotice{}.Exists(service.DB, comment.ID, TestUser2ID, 0))

	// 测试给子评论添加点赞提醒
	subComment := SubComment{
		CommentContent: "Go - 测试子评论",
		CommentID:      comment.ID,
		UserID:         TestUser2ID,
		Username:       "test name",
		Checked:        CheckedNormal,
	}
	err = subComment.Save(service.DB)
	require.NoError(err)
	result, err = AddLikeNotice(service.DB, subComment, TestUser1ID, true)
	require.NoError(err)
	assert.True(result)
	// 有提醒记录
	assert.True(LikeNotice{}.Exists(service.DB, subComment.ID, TestUser1ID, 1))

	// 测试给自己的评论点赞
	result, err = AddLikeNotice(service.DB, subComment, TestUser2ID, true)
	require.NoError(err)
	assert.False(result)
	// 无提醒记录
	assert.False(LikeNotice{}.Exists(service.DB, subComment.ID, TestUser2ID, 1))

	require.NoError(comment.DB().Where("id = ?", comment.ID).Delete("").Error)
	require.NoError(subComment.DB().Where("id = ?", subComment.ID).Delete("").Error)
	require.NoError(service.DB.Table(LikeNotice{}.TableName()).Where("c_user_id = ?", TestUser1ID).
		Delete("").Error)
}

func TestCommentExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(service.DB.Table(LikeNotice{}.TableName()).Where("c_user_id = ?", TestUser1ID).Delete("").Error)

	// 测试消息提醒存在的情况
	likeNotice := LikeNotice{
		Content:   "测试内容 - go",
		CUserID:   TestUser1ID,
		AUserID:   233,
		ElementID: 1,
		CommentID: 233,
		Title:     "测试内容 - go",
	}
	err := service.DB.Save(&likeNotice).Error
	require.NoError(err)
	assert.True(LikeNotice{}.Exists(service.DB, 233, TestUser1ID, 0))

	// 测试消息提醒不存在的情况
	assert.False(LikeNotice{}.Exists(service.DB, 99999999999, TestUser1ID, 0))

	// 删除测试数据
	require.NoError(service.DB.Table(likeNotice.TableName()).Where("c_user_id = ?", TestUser1ID).Delete("").Error)
}
