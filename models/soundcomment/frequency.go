package soundcomment

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// CommentType 评论检测枚举
type CommentType int64

// 评论检测枚举
const (
	CommentRuleComment CommentType = iota
	CommentRuleSubComment
)

// CommentRule 评论规则检测
type CommentRule struct {
	t            CommentType
	frequencyKey string
	elementID    int64
	cType        int
	upUserID     int64
	userID       int64
}

// NewCommentRule new CommentRule
func NewCommentRule(t CommentType, cType int, userID, upUserID, elementID int64) *CommentRule {
	return &CommentRule{
		t:            t,
		frequencyKey: serviceredis.KeyFrequencyTimes3.Format(userID, cType, elementID),
		elementID:    elementID,
		cType:        cType,
		userID:       userID,
		upUserID:     upUserID,
	}
}

// IsLimited 判断用户是否触发 `评论限制`
func (f *CommentRule) IsLimited() (bool, error) {
	limit, err := f.maxFrequency()
	if err != nil {
		return true, err
	}
	// 「冷静期」没过
	if limit == 0 {
		return true, nil
	}

	now := util.TimeNow().Unix()
	count, err := service.Redis.ZCount(f.frequencyKey,
		strconv.FormatInt(now-util.SecondOneMinute, 10),
		strconv.FormatInt(now, 10),
	).Result()
	if err != nil {
		return true, err
	}
	if count >= limit {
		err = f.addCooldown()
		if err != nil {
			return true, err
		}
		return true, nil
	}
	return false, nil
}

// maxFrequency 最大评论频率
func (f CommentRule) maxFrequency() (int64, error) {
	now := util.TimeNow().Unix()
	var c Cooldown
	err := service.MessageDB.Model(Cooldown{}).Select("id").Where("user_id = ? AND end_time > ? AND start_time <= ?",
		f.userID, now, now).First(&c).Error
	if err == nil {
		return 0, nil
	}
	if !servicedb.IsErrNoRows(err) {
		return 0, err
	}

	// 获取用户在过去 12 小时内，触发「冷静期」的次数
	var count int64
	err = service.MessageDB.Model(Cooldown{}).Where("user_id = ? AND start_time >= ? AND start_time < ?",
		f.userID, now-12*util.SecondOneHour, now).Count(&count).Error
	if err != nil {
		return 0, err
	}

	// 刷屏嫌疑用户
	if count >= 3 {
		return 3, nil
	}

	// 正常用户
	if f.userID == f.upUserID {
		// 针对「自己稿件」的评论频率
		return 20, nil
	}
	// 针对「他人稿件」的评论频率
	return 5, nil
}

// Add 添加评论记录
func (f *CommentRule) Add(id int64) error {
	switch f.t {
	case CommentRuleComment:
	case CommentRuleSubComment:
		// 子评论用 - 开头
		id *= -1
	default:
		panic("CommentType 不存在")
	}
	pipe := service.Redis.TxPipeline()
	pipe.ZAdd(f.frequencyKey, &redis.Z{
		Score:  float64(util.TimeNow().Unix()),
		Member: id,
	})
	pipe.Expire(f.frequencyKey, 2*time.Minute)
	_, err := pipe.Exec()
	return err
}

// addCoolingOfTimes 添加「冷静期」记录
func (f CommentRule) addCooldown() error {
	now := util.TimeNow().Unix()
	return service.MessageDB.Create(&Cooldown{
		UserID:    f.userID,
		StartTime: now,
		EndTime:   now + util.SecondOneMinute,
	}).Error
}

// IsRepeatComment 检测重复评论，只判断三分钟内上一条评论是否是重复评论
// id: 根评论传 element_id，子评论传 `根评论 id`
func (f *CommentRule) IsRepeatComment(id int64, content string) (bool, error) {
	var (
		lastComment string
		err         error
	)
	db := service.MessageDB.Order("ctime DESC").
		Select("id, comment_content").Where("userid = ? AND ctime >= ? AND checked <> ?",
		f.userID, util.TimeNow().Add(-3*time.Minute).Unix(), CheckedDeleted)
	switch f.t {
	case CommentRuleComment:
		var comment Comment
		err = db.Where("element_id = ? AND c_type = ?", id, f.cType).Take(&comment).Error
		lastComment = comment.Content
	case CommentRuleSubComment:
		var comment SubComment
		err = db.Where("comment_id = ?", id).Take(&comment).Error
		lastComment = comment.CommentContent
	default:
		panic("CommentType 不存在")
	}
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return lastComment == content, nil
}

// Cooldown 冷静期
type Cooldown struct {
	ID           int64 `gorm:"column:id;primary"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	UserID       int64 `gorm:"column:user_id"`
	StartTime    int64 `gorm:"column:start_time"`
	EndTime      int64 `gorm:"column:end_time"`
}

// TableName table name
func (c *Cooldown) TableName() string {
	return "m_comment_cooldown"
}

// BeforeCreate create 钩子
func (c *Cooldown) BeforeCreate() error {
	c.CreateTime = util.TimeNow().Unix()
	return nil
}

// BeforeUpdate update 钩子
func (c *Cooldown) BeforeUpdate() error {
	c.ModifiedTime = util.TimeNow().Unix()
	return nil
}
