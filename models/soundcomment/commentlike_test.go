package soundcomment

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

const TestCommentID = 1
const TestUser1ID = 346286
const TestUser2ID = 3013620

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(tableCommentLike, CommentLike{}.TableName())
}

func TestCommentLikeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	commentLike := CommentLike{
		CommentID: TestCommentID,
		Sub:       CommentSub,
		UserID:    TestUser1ID,
		Type:      TypeCommentLike,
	}
	// 删除历史数据
	err := service.DB.Table(commentLike.TableName()).
		Where("cid = ? AND userid = ? AND sub = ? AND type = ?", TestCommentID, TestUser1ID, CommentSub, TypeCommentLike).
		Delete("").
		Error
	require.NoError(err)
	// 测试 Create()
	err = commentLike.Save(service.DB)
	assert.NoError(err)
	// 验证数据是否正确
	var id int64
	err = service.DB.Table(commentLike.TableName()).Select("id").
		Where("cid = ? AND userid = ? AND sub = ? AND type = ?", TestCommentID, TestUser1ID, CommentSub, TypeCommentLike).
		Row().Scan(&id)
	assert.NoError(err)
	assert.NotEqual(0, id)
}

func TestCommentLikeExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试数据存在的情况
	exists, err := CommentLike{}.Exists(service.DB, TestCommentID, TestUser1ID, CommentSub, TypeCommentLike)
	assert.NoError(err)
	assert.True(exists)

	// 测试数据不存在的情况
	err = service.DB.Table(CommentLike{}.TableName()).
		Where("cid = ? AND userid = ? AND sub = ? AND type = ?", TestCommentID, TestUser1ID, CommentSub, TypeCommentLike).
		Delete("").
		Error
	require.NoError(err)
	exists, err = CommentLike{}.Exists(service.DB, TestCommentID, TestUser1ID, CommentSub, TypeCommentLike)
	assert.NoError(err)
	assert.False(exists)
}

func TestCommentLikeDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试有数据时删除数据
	commentLike := CommentLike{
		CommentID: 1,
		Sub:       CommentSub,
		UserID:    TestUser1ID,
		Type:      TypeCommentLike,
	}
	err := commentLike.Save(service.DB)
	require.NoError(err)
	err = commentLike.Delete(service.DB, 1, TestUser1ID, CommentSub, TypeCommentLike, true)
	assert.NoError(err)

	// 测试无数据时删除数据
	err = commentLike.Delete(service.DB, 1, TestUser1ID, CommentSub, TypeCommentLike, true)
	assert.NoError(err)
}
