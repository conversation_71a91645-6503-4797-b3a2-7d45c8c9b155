package transaction

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestFindUserPaidSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids, err := FindUserPaidSoundIDs(3013091, []int64{91769})
	require.NoError(err)
	require.NotEmpty(ids)
	require.Len(ids, 1)
	assert.Equal(int64(91769), ids[0])
}

func TestGetPayerIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var id []int64
	err := TransactionLog{}.DB().Table(TransactionLog{}.TableName()).Select("DISTINCT(from_id) AS from_id").
		Where("status = ?", StatusSuccess).Limit(2).Pluck("from_id", &id).Error
	require.NoError(err)

	payer, _ := GetPayerIDs(id)
	assert.ElementsMatch(payer, id)
}
