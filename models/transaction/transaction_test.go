package transaction

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTransactionConstants(t *testing.T) {
	assert := assert.New(t)

	// 测试订单状态常量
	assert.Equal(-5, StatusIllegalTopup, "StatusIllegalTopup should be -5")
	assert.Equal(-4, StatusR<PERSON>undDiamond, "StatusRefundDiamond should be -4")
	assert.Equal(-3, <PERSON><PERSON><PERSON><PERSON>, "StatusRefund should be -3")
	assert.Equal(-2, <PERSON><PERSON><PERSON><PERSON>, "StatusCancel should be -2")
	assert.Equal(-1, StatusUndone, "StatusUndone should be -1")
	assert.Equal(0, StatusPending, "StatusPending should be 0")
	assert.Equal(1, StatusSuccess, "StatusSuccess should be 1")

	// 测试订单类型常量
	assert.Equal(1, TypeLive, "TypeLive should be 1")
	assert.Equal(2, TypeSound, "TypeSound should be 2")
	assert.Equal(3, TypeDrama, "TypeDrama should be 3")
	assert.Equal(4, <PERSON><PERSON><PERSON><PERSON>, "TypeBoyfriend should be 4")
	assert.Equal(5, Type<PERSON>raw<PERSON><PERSON>, "TypeDrawCard should be 5")
	assert.Equal(6, Type<PERSON>ard<PERSON>ackage, "TypeCardPackage should be 6")
	assert.Equal(7, TypeDramaReward, "TypeDramaReward should be 7")
	assert.Equal(8, TypeOmikuji, "TypeOmikuji should be 8")
	assert.Equal(9, TypeGuildLive, "TypeGuildLive should be 9")

	// 测试 live 订单属性常量
	assert.Equal(0, AttrCommon, "AttrCommon should be 0")
	assert.Equal(1, AttrLiveRenewNoble, "AttrLiveRenewNoble should be 1")
	assert.Equal(2, AttrLiveRegisterNoble, "AttrLiveRegisterNoble should be 2")
	assert.Equal(3, AttrLiveRebateGift, "AttrLiveRebateGift should be 3")
	assert.Equal(4, AttrLiveLuckyGift, "AttrLiveLuckyGift should be 4")
	assert.Equal(5, AttrLiveRegisterSuperFan, "AttrLiveRegisterSuperFan should be 5")
	assert.Equal(6, AttrLiveRenewSuperFan, "AttrLiveRenewSuperFan should be 6")
	assert.Equal(7, AttrLiveFukubukuro, "AttrLiveFukubukuro should be 7")
	assert.Equal(8, AttrLiveGashapon, "AttrLiveGashapon should be 8")
	assert.Equal(9, AttrLiveGashaponGift, "AttrLiveGashaponGift should be 9")
	assert.Equal(10, AttrLiveWishPool, "AttrLiveWishPool should be 10")
	assert.Equal(11, AttrLiveRedPacket, "AttrLiveRedPacket should be 11")
	assert.Equal(12, AttrLiveRegisterNobleTrial, "AttrLiveRegisterNobleTrial should be 12")
	assert.Equal(13, AttrLiveRenewNobleTrial, "AttrLiveRenewNobleTrial should be 13")
	assert.Equal(14, AttrLiveDanmaku, "AttrLiveDanmaku should be 14")
	assert.Equal(15, AttrLiveBuyLuckyBag, "AttrLiveBuyLuckyBag should be 15")

	// 测试 drama 订单属性常量
	assert.Equal(2, AttrDramaRedeem, "AttrDramaRedeem should be 2")
	assert.Equal(3, AttrDramaDoudian, "AttrDramaDoudian should be 3")
	assert.Equal(4, AttrDramaLuckyBag, "AttrDramaLuckyBag should be 4")
}
