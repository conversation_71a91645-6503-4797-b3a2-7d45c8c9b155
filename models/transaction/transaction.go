package transaction

// 订单状态
const (
	StatusIllegalTopup  = iota - 5 // 非法充值
	StatusRefundDiamond            // 已退款（猫耳钻石）
	StatusRefund                   // 已退款（人民币）
	StatusCancel                   // 直播问答取消
	StatusUndone                   // 未完成（目前仅用作“直播问答提问中”）
	StatusPending                  // 待完成
	StatusSuccess                  // 购买成功
)

// 订单类型
const (
	TypeLive        = iota + 1 // 散人直播收益
	TypeSound                  // 单集购买
	TypeDrama                  // 整剧购买
	TypeBoyfriend              // 微信男友
	TypeDrawCard               // 抽卡
	TypeCardPackage            // 购买卡片季包
	TypeDramaReward            // 剧集打赏
	TypeOmikuji                // 求签
	TypeGuildLive              // 公会直播收益
)

// live 订单属性
const (
	AttrCommon                 = iota // 直播间礼物（gift_id != 0）或问答（gift_id = 0）
	AttrLiveRenewNoble                // 直播贵族续费（type 为 1 或 9 时）
	AttrLiveRegisterNoble             // 直播贵族开通（type 为 1 或 9 时），平台统计收益时 开通费用*0.8，用户查看时不需要系数
	AttrLiveRebateGift                // 直播间白给礼物（type 为 1 或 9 时）
	AttrLiveLuckyGift                 // 直播间幸运签礼物（type 为 1 或 9 时）
	AttrLiveRegisterSuperFan          // 直播间开通超粉（type 为 1 或 9 时）
	AttrLiveRenewSuperFan             // 直播间续费超粉（type 为 1 或 9 时）
	AttrLiveFukubukuro                // 直播间购买福袋（type 为 1 或 9 时）
	AttrLiveGashapon                  // 直播间购买超能魔盒（type 为 1 或 9 时）
	AttrLiveGashaponGift              // 直播间超能魔盒礼物（type 为 1 或 9 时）
	AttrLiveWishPool                  // 直播间许愿池（type 为 1 或 9 时）
	AttrLiveRedPacket                 // 直播间礼物红包（type 为 1 或 9 时）
	AttrLiveRegisterNobleTrial        // 直播间开通体验贵族（type 为 1 或 9 时）
	AttrLiveRenewNobleTrial           // 直播间续费体验贵族（type 为 1 或 9 时）
	AttrLiveDanmaku                   // 直播间付费弹幕（type 为 1 或 9 时）
	AttrLiveBuyLuckyBag               // 直播间购买喵喵福袋
)

// drama 订单属性
const (
	AttrDramaRedeem   = iota + 2 // 剧集通过兑换而来（type 为 2 或 3）
	AttrDramaDoudian             // 剧集通过抖店购得（type 为 3）
	AttrDramaLuckyBag            // 剧集通过福袋兑换而来（type 为 3）
)
