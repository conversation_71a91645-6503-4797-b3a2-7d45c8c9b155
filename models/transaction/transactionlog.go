package transaction

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// TransactionSoundLog model
type TransactionSoundLog struct {
	ID      int64 `gorm:"column:id" json:"id"`
	SoundID int64 `gorm:"column:sound_id" json:"sound_id"`
	UserID  int64 `gorm:"column:user_id" json:"user_id"`
	DramaID int64 `gorm:"column:drama_id" json:"drama_id"`
	TID     int64 `gorm:"column:tid" json:"tid"`
}

// DB the db instance of TransactionSoundLog model
func (t TransactionSoundLog) DB() *gorm.DB {
	return service.PayDB.Table(t.TableName())
}

// TableName for current model
func (TransactionSoundLog) TableName() string {
	return "transaction_sound_log"
}

// TransactionLog model
type TransactionLog struct {
	ID           int64   `gorm:"column:id" json:"id"`
	FromID       int64   `gorm:"column:from_id" json:"from_id"`
	ToID         int64   `gorm:"column:to_id" json:"to_id"`
	CTime        int64   `gorm:"column:c_time" json:"c_time"`
	GiftID       int64   `gorm:"column:gift_id" json:"gift_id"`
	Title        string  `gorm:"column:title" json:"title"`
	IOSCoin      int64   `gorm:"column:ios_coin" json:"ios_coin"`
	AndroidCoin  int64   `gorm:"column:android_coin" json:"android_coin"`
	PayPalCoin   int64   `gorm:"column:paypal_coin" json:"paypal_coin"`
	Income       float64 `gorm:"column:income" json:"income"`
	Tax          float64 `gorm:"column:tax" json:"tax"`
	Rate         float64 `gorm:"column:rate" json:"rate"`
	Num          int64   `gorm:"column:num" json:"num"`
	Status       byte    `gorm:"column:status" json:"status"`
	Type         byte    `gorm:"column:type" json:"type"`
	SubordersNum int64   `gorm:"column:suborders_num" json:"suborders_num"`
}

// DB the db instance of TransactionLog model
func (t TransactionLog) DB() *gorm.DB {
	return service.PayDB.Table(t.TableName())
}

// TableName for current model
func (TransactionLog) TableName() string {
	return "transaction_log"
}

// TODO: add afterFind if you need

// FindUserPaidSoundIDs 返回用户已经购买的 Sound ID
func FindUserPaidSoundIDs(userID int64, payBySoundIDs []int64) ([]int64, error) {
	var paidIDs []int64
	err := TransactionSoundLog{}.DB().
		Select("sound_id").Where("user_id = ? AND sound_id IN (?)", userID, payBySoundIDs).
		Pluck("sound_id", &paidIDs).Error
	if err != nil {
		return nil, err
	}

	return paidIDs, nil
}

// GetPayerIDs 返回付费用户的 UserID
func GetPayerIDs(userIDs []int64) ([]int64, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	var payers []int64
	err := TransactionLog{}.DB().
		Where("from_id IN (?) AND status = ?", userIDs, StatusSuccess).
		Pluck("DISTINCT from_id", &payers).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return payers, nil
}
