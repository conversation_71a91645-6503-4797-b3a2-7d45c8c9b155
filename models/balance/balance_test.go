package balance

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
)

const testUserID = 6666666

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestBalanceTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("balance", Balance{}.TableName())
}

func TestGetAllConsumption(t *testing.T) {
	assert := assert.New(t)
	Balance{}.DB().Where("id = ?", testUserID).Update("all_consumption", 233)
	all, err := GetAllConsumption(testUserID)
	assert.NoError(err)
	assert.Equal(int64(233), all)
}
