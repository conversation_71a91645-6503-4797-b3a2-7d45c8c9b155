package balance

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// Balance 用户资产表
type Balance struct {
	ID                   int64   `gorm:"column:id;primary_key"`
	IOS                  int64   `gorm:"column:ios"`
	Android              int64   `gorm:"column:android"`
	PayPal               int64   `gorm:"column:paypal"`
	TMallIOS             int64   `gorm:"column:tmallios"`
	GooglePay            int64   `gorm:"column:googlepay"`
	InIOS                int64   `gorm:"column:in_ios"`
	InAndroid            int64   `gorm:"column:in_android"`
	InPayPal             int64   `gorm:"column:in_paypal"`
	InTMallIOS           int64   `gorm:"column:in_tmallios"`
	InGooglePay          int64   `gorm:"column:in_googlepay"`
	NewAllLiveProfit     int64   `gorm:"column:new_all_live_profit"`
	NewLiveProfit        int64   `gorm:"column:new_live_profit"`
	AllLiveProfit        int64   `gorm:"column:all_live_profit"`
	LiveProfit           int64   `gorm:"column:live_profit"`
	Profit               float64 `gorm:"column:profit"`
	DramaRewardProfit    int64   `gorm:"column:drama_reward_profit"`
	DramaBuyProfit       int64   `gorm:"column:drama_buy_profit"`
	OtherProfit          int64   `gorm:"column:other_profit"`
	AllDramaBuyProfit    int64   `gorm:"column:all_drama_buy_profit"`
	AllDramaRewardProfit int64   `gorm:"column:all_drama_reward_profit"`
	AllOtherProfit       int64   `gorm:"column:all_other_profit"`
	AllConsumption       int64   `gorm:"column:all_consumption"`
}

// DB the db instance of Balance model
func (Balance) DB() *gorm.DB {
	return service.PayDB.Table(Balance{}.TableName())
}

// TableName of Balance
func (Balance) TableName() string {
	return "balance"
}

// GetAllConsumption 获取用户钻石总消费
func GetAllConsumption(userID int64) (int64, error) {
	var all int64
	err := Balance{}.DB().Select("all_consumption").Where("id = ?", userID).Row().Scan(&all)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return all, nil
}
