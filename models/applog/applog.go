package applog

import (
	"unicode/utf8"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/util"
)

// 日志上报类型
const (
	// 反馈时发送日志
	TypeFeedback = iota + 1
	// 主动拉取上报
	TypeReport
)

// AppLog 用户日志
type AppLog struct {
	ID           int64         `gorm:"column:id"`
	CreateTime   int64         `gorm:"column:create_time"`
	ModifiedTime int64         `gorm:"column:modified_time"`
	UserID       int64         `gorm:"column:user_id"`
	EquipID      string        `gorm:"column:equip_id"`
	BUVID        string        `gorm:"column:buvid"`
	UploadType   int64         `gorm:"column:upload_type"`
	LogURL       string        `gorm:"column:log_url"`
	OS           util.Platform `gorm:"column:os"`
	Version      string        `gorm:"column:version"`
	UserAgent    string        `gorm:"column:user_agent"`
	TaskID       int64         `gorm:"column:task_id"`
}

// TableName table name
func TableName() string {
	return "m_app_log"
}

// TableName table name
func (AppLog) TableName() string {
	return TableName()
}

// BeforeCreate hook
func (u *AppLog) BeforeCreate(scope *gorm.Scope) error {
	now := util.TimeNow().Unix()
	return scope.SetColumn("create_time", now)
}

// BeforeSave hook
func (u *AppLog) BeforeSave(scope *gorm.Scope) error {
	// 数据库中的 user_agent 字段为 varchar(100)
	if utf8.RuneCountInString(u.UserAgent) > 100 {
		u.UserAgent = string([]rune(u.UserAgent)[:100])
	}
	now := util.TimeNow().Unix()
	return scope.SetColumn("modified_time", now)
}
