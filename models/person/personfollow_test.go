package person

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

const (
	testFansID   = 3013621
	testFollowID = 3013622
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, AttrFollowing)
	assert.Equal(2, AttrFans)

	assert.Equal(0, FollowFromDefault)
	assert.Equal(1, FollowFromWeb)
	assert.Equal(2, Follow<PERSON>romApp)
	assert.Equal(3, FollowFromLive)
	assert.Equal(4, FollowFromGameDownload)
}

func TestFollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var info MAttentionUser
	require.NoError(service.DB.Table(info.TableName()).Where("user_active = ? AND user_passtive = ?", testFansID, testFollowID).
		Delete("").Error)

	fans, err := user.FindByUserID(testFansID)
	require.NoError(err)
	require.NotNil(fans)

	follow, err := user.FindByUserID(testFollowID)
	require.NoError(err)
	require.NotNil(follow)

	require.NoError(Follow(testFansID, testFollowID, nil))

	assert.NoError(service.DB.Table(info.TableName()).
		Where("user_active = ? AND user_passtive = ?", testFansID, testFollowID).Find(&info).Error)
	updateFans, err := user.FindByUserID(testFansID)
	require.NoError(err)
	assert.Equal(fans.FollowNum+1, updateFans.FollowNum)

	updateFollow, err := user.FindByUserID(testFollowID)
	require.NoError(err)
	assert.Equal(follow.FansNum+1, updateFollow.FansNum)
}

func TestUnfollow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var info MAttentionUser
	require.NoError(service.DB.Table(info.TableName()).Where("user_active = ? AND user_passtive = ?", testFansID, testFollowID).
		Delete("").Error)
	require.NoError(Follow(testFansID, testFollowID, nil))
	fans, err := user.FindByUserID(testFansID)
	require.NoError(err)
	require.NotNil(fans)

	follow, err := user.FindByUserID(testFollowID)
	require.NoError(err)
	require.NotNil(follow)

	require.NoError(Unfollow(testFansID, testFollowID))
	assert.True(servicedb.IsErrNoRows(service.DB.Table(info.TableName()).
		Where("user_active = ? AND user_passtive = ?", testFansID, testFollowID).Find(&info).Error))

	updateFans, err := user.FindByUserID(testFansID)
	require.NoError(err)
	assert.Equal(fans.FollowNum-1, updateFans.FollowNum)

	updateFollow, err := user.FindByUserID(testFollowID)
	require.NoError(err)
	assert.Equal(follow.FansNum-1, updateFollow.FansNum)
}

func TestHasFollowed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info := MAttentionUser{
		UserActive:   testFansID,
		UserPasstive: testFollowID,
	}
	require.NoError(service.DB.Table(info.TableName()).Where("user_active = ?", info.UserActive).Delete("").Error)
	require.NoError(service.DB.Table(info.TableName()).Create(&info).Error)
	ok, err := HasFollowed(testFansID, testFollowID)
	require.NoError(err)
	assert.True(ok)
}
