package person

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MAttentionUserMore more info of MAttentionUser
type MAttentionUserMore struct {
	EventID     *string        `json:"event_id,omitempty"`      // 事件 ID
	EventIDFrom *string        `json:"event_id_from,omitempty"` // 事件来源 ID
	TrackID     *string        `json:"track_id,omitempty"`      // 跟踪 ID
	OS          *util.Platform `json:"os,omitempty"`            // 设备操作系统，由上游接口通过 equipment 信息获取后传入
	AutoFollow  bool           `json:"auto_follow,omitempty"`   // 是否系统自动关注。true 表示系统自动关注，false 表示用户手动关注
}

// Value converts MoreDetails to a json string for database storage
func (m MAttentionUserMore) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan parses MoreDetails from a json string stored in the database
func (m *MAttentionUserMore) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, m)
}

// MAttentionUser model
type MAttentionUser struct {
	ID           int64               `gorm:"column:id;primary_key"`
	UserActive   int64               `gorm:"column:user_active"`
	UserPasstive int64               `gorm:"column:user_passtive"`
	Time         int64               `gorm:"column:time"`
	More         *MAttentionUserMore `gorm:"column:more;type:json"`
}

const (
	// AttrFollowing 当前用户已关注被访问用户
	AttrFollowing = iota + 1
	// AttrFans 当前用户为被访问用户的粉丝
	AttrFans
)

// 关注来源
const (
	// FollowFromDefault 默认（其他）
	FollowFromDefault = iota
	// FollowFromWeb web
	FollowFromWeb
	// FollowFromApp app
	FollowFromApp
	// FollowFromLive live
	FollowFromLive
	// FollowFromGameDownload game download
	FollowFromGameDownload
)

// 自动关注类型
const (
	// AutoFollowFalse 手动关注
	AutoFollowFalse = iota
	// AutoFollowTrue 自动关注
	AutoFollowTrue
)

// TableName for current model
func (MAttentionUser) TableName() string {
	return "m_attention_user"
}

// Follow 关注用户
func Follow(userID int64, followUserID int64, more *MAttentionUserMore) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		var attentionInfo MAttentionUser
		err := tx.Table(attentionInfo.TableName()).Select("id").
			Where("user_active = ? AND user_passtive = ?", userID, followUserID).Find(&attentionInfo).Error
		if err != nil && !servicedb.IsErrNoRows(err) {
			return err
		}
		if attentionInfo.ID > 0 {
			// 关注者已经关注了被关注用户，无需重复关注
			return nil
		}

		attentionInfo = MAttentionUser{
			UserActive:   userID,
			UserPasstive: followUserID,
			Time:         util.TimeNow().Unix(),
			More:         more,
		}
		err = tx.Table(attentionInfo.TableName()).Create(&attentionInfo).Error
		if err != nil {
			if servicedb.IsUniqueError(err) {
				err = nil
			}
			return err
		}
		err = tx.Table(user.Simple{}.TableName()).Where("id = ?", followUserID).
			Update("fansnum", gorm.Expr("fansnum + ?", 1)).Error
		if err != nil {
			return err
		}
		return tx.Table(user.Simple{}.TableName()).Where("id = ?", userID).
			Update("follownum", gorm.Expr("follownum + ?", 1)).Error
	})
}

// Unfollow 取消关注用户
func Unfollow(userID int64, followUserID int64) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		var attentionInfo MAttentionUser
		err := tx.Table(attentionInfo.TableName()).Select("id").
			Where("user_active = ? AND user_passtive = ?", userID, followUserID).
			Find(&attentionInfo).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				// 关注着还未关注被关注者，无需取消关注
				err = nil
			}
			return err
		}

		db := tx.Table(attentionInfo.TableName()).Where("id = ?", attentionInfo.ID).
			Delete("")
		if db.Error != nil {
			return err
		} else if db.RowsAffected <= 0 {
			return nil
		}
		err = tx.Table(user.Simple{}.TableName()).Where("id = ?", followUserID).
			Update("fansnum", servicedb.SubSatExpr("fansnum", 1)).Error
		if err != nil {
			return err
		}
		return tx.Table(user.Simple{}.TableName()).Where("id = ?", userID).
			Update("follownum", servicedb.SubSatExpr("follownum", 1)).Error
	})
}

// HasFollowed userID 是否已经关注了目标用户
func HasFollowed(userID, targetUserID int64) (bool, error) {
	attentionInfo := new(MAttentionUser)
	err := service.DB.Table(attentionInfo.TableName()).Select("id").
		Where("user_active = ? AND user_passtive = ?", userID, targetUserID).
		Find(attentionInfo).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return false, err
	}
	if attentionInfo.ID <= 0 {
		return false, nil
	}
	return true, nil
}
