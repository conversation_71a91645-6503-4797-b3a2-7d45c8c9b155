package models

import (
	"database/sql"

	"github.com/MiaoSiLa/missevan-go/service"
)

// Live model
type Live struct {
	ID            int64  `gorm:"column:id;primary_key" json:"id"`
	UserID        int64  `gorm:"column:user_id" json:"user_id"`
	RoomID        int64  `gorm:"column:room_id" json:"room_id"`
	CatalogID     int64  `gorm:"column:catalog_id" json:"catalog_id"`
	Title         string `gorm:"column:title" json:"title"`
	Intro         string `gorm:"column:intro" json:"intro"`
	Status        int    `gorm:"column:status" json:"status"`
	ContractID    int64  `gorm:"column:contract_id;default:1" json:"-"`
	LiveStartTime int64  `gorm:"column:live_start_time" json:"live_start_time"`
	CreateTime    int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime  int64  `gorm:"column:modified_time" json:"-"`
}

// TableName returns the table name of the Live model
func (Live) TableName() string {
	return "live"
}

// Exists 返回直播间是否存在
func (l Live) Exists(userID int64) (bool, error) {
	var id int64
	err := service.DB.Table(Live{}.TableName()).Select("id").Where("user_id = ?", userID).Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
