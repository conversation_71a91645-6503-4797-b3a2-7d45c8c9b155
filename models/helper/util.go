package helper

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// TODO: 待依赖项目使用 servicedb/util 替换 model/helper 后、删除本模块

// SplitBatchInsert inserts records into db table.
// chunkSize = 65535 / average_num_of_columns_per_record. 1000 is recommended.
func SplitBatchInsert(db *gorm.DB, table string, records interface{}, chunkSize int, ignoreSQLErr bool) error {

	return servicedb.SplitBatchInsert(db, table, records, chunkSize, ignoreSQLErr)
}

// BatchInsert inserts rows into table (rows should be []T or []*T).
// NOTICE: Prepared statement has a limit of maximum 65535 placeholders. len(rows) should not be too large.
func BatchInsert(db *gorm.DB, table string, rows interface{}) (err error) {
	return servicedb.BatchInsert(db, table, rows)
}
