package helper

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

type Record struct {
	ID         int64  `json:"id" gorm:"column:id"`
	Name       string `json:"name" gorm:"column:name"`
	CreateTime int64  `json:"create_time" gorm:"column:create_time"`
}

func TestBatchInsert(t *testing.T) {
	require := require.New(t)
	row1 := Record{ID: 1, Name: "row1", CreateTime: 11}
	row2 := Record{ID: 2, Name: "row2", CreateTime: 12}

	service.LogDB.Exec(`CREATE TABLE IF NOT EXISTS collection (
		id BIGINT(10) NOT NULL,
		name VARCHAR(45) NULL,
		create_time BIGINT(10) NULL)`)
	err := BatchInsert(service.LogDB, "collection", []Record{row1, row2})
	require.NoError(err)
}

func TestSplitBatchInsert(t *testing.T) {
	require := require.New(t)
	row1 := Record{ID: 3, Name: "row1", CreateTime: 11}
	row2 := Record{ID: 4, Name: "row2", CreateTime: 12}
	records := []Record{row1, row2}

	err := SplitBatchInsert(service.LogDB, "collection", records, 1, true)
	require.NoError(err)

	err = SplitBatchInsert(service.LogDB, "collection", records, 3, false)
	require.NoError(err)
}
