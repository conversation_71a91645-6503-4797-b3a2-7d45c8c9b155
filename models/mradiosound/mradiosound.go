package mradiosound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// MRadioSound model
type MRadioSound struct {
	ID              int64   `gorm:"column:id"`
	CreateTime      int64   `gorm:"column:create_time"`
	ModifiedTime    int64   `gorm:"column:modified_time"`
	DeleteTime      int64   `gorm:"column:delete_time"`
	CatalogID       int64   `gorm:"column:catalog_id"`
	SoundID         int64   `gorm:"column:sound_id"`
	Title           string  `gorm:"column:title"`
	Cover           string  `gorm:"column:cover"`
	BackgroundCover string  `gorm:"column:background_cover"`
	BackgroundVideo string  `gorm:"column:background_video"`
	Sort            int64   `gorm:"column:sort"`
	More            *string `gorm:"column:more"` // 该字段类型实际为 JSON 类型
}

// DB the db instance of current model
func (m MRadioSound) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MRadioSound) TableName() string {
	return "m_radio_sound"
}
