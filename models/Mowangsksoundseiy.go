package models

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// Mowangsksoundseiy model
type Mowangsksoundseiy struct {
	ID            int64  `gorm:"column:id;primary_key"` // 声优 ID
	Name          string `gorm:"column:name"`           // 声优名称
	Icon          string `gorm:"column:icon"`           // 声优图片
	Profile       string `gorm:"column:profile"`        // 声优介绍
	Gender        int    `gorm:"column:gender"`         // 声优性别 1：男；2：女
	Initial       int    `gorm:"column:initial"`        // 首字母 0 其他，1 到 26 代表 A 到 Z
	Birthyear     int    `gorm:"column:birthyear"`      // 出生年份
	Birthmonth    int    `gorm:"column:birthmonth"`     // 出生月份
	Birthday      int    `gorm:"column:birthday"`       // 出生日
	Birthmonthday int    `gorm:"column:birthmonthday"`  // 出生日期
	Bloodtype     int    `gorm:"column:bloodtype"`      // 血型 0：A；1：B；2：AB；3：O；4：未知
	Career        int    `gorm:"column:career"`         // 职业 0：日本 CV；1：中国 CV
	Group         string `gorm:"column:group"`          // 社团
	Weibo         string `gorm:"column:weibo"`          // 微博
	Weiboname     string `gorm:"column:weiboname"`      // 微博名称
	Baike         string `gorm:"column:baike"`          // 百科
	Baikename     string `gorm:"column:baikename"`      // 百科名称
	MID           int64  `gorm:"column:mid"`            // 用户 ID
	Checked       int    `gorm:"column:checked"`        // 审核状态
	Soundline1    int64  `gorm:"column:soundline1"`     // 声线 1（对应音频 ID）
	Soundline2    int64  `gorm:"column:soundline2"`     // 声线 2（对应音频 ID）
	Soundline3    int64  `gorm:"column:soundline3"`     // 声线 3（对应音频 ID）
	Seiyalias     string `gorm:"column:seiyalias"`      // 别名
}

// DB the db instance of Mowangsksoundseiy model
func (m Mowangsksoundseiy) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName returns the table name of Mowangsksoundseiy model
func (Mowangsksoundseiy) TableName() string {
	return "mowangsksoundseiy"
}

// ListSeiyIDsByUserID 根据用户 ID 获取声优 IDs
func ListSeiyIDsByUserID(userID int64) ([]int64, error) {
	// 从 2022-08-08 开始，声优仅能绑定一个账号，但是此处考虑历史的兼容问题支持返回多个声优 ID
	var seiyIDs []int64
	err := Mowangsksoundseiy{}.DB().
		Select("id").
		Where("mid = ?", userID).
		Pluck("id", &seiyIDs).Error
	if err != nil {
		return nil, err
	}
	return seiyIDs, nil
}

// ListSeiysByIDs 根据声优 ID 获取声优信息列表
func ListSeiysByIDs(seiyIDs []int64) ([]Mowangsksoundseiy, error) {
	var seiys []Mowangsksoundseiy
	err := Mowangsksoundseiy{}.DB().Select("id, name").Where("id IN (?)", seiyIDs).Find(&seiys).Error
	if err != nil {
		return nil, err
	}
	return seiys, nil
}
