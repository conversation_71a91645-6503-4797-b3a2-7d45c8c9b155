package blackuser

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 拉黑状态位
const (
	StatusSmallBanBig = 1 + iota // 位 1: small_id 拉黑 big_id
	StatusBigBanSmall            // 位 2: big_id 拉黑 small_id
)

// Model of blackuser
type Model struct {
	BigID        int64        `gorm:"column:big_id;primary_key"`
	SmallID      int64        `gorm:"column:small_id;primary_key"`
	Status       util.BitMask `gorm:"column:status"` // status 位运算：1 位，small_id 拉黑 big_id；2 位，big_id 拉黑 small_id
	CreateTime   int64        `gorm:"column:create_time"`
	ModifiedTime int64        `gorm:"column:modified_time"`
}

// TableName of Model
func (Model) TableName() string {
	return "black_user"
}

// DB the db instance of BlackUser model
func (b Model) DB() *gorm.DB {
	return service.MessageDB.Table(b.TableName())
}

// BeforeCreate hook
func (m *Model) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.CreateTime = now
	return nil
}

// BeforeSave hook
func (m *Model) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	m.ModifiedTime = now
	return nil
}

// GetBlacklistRelation 获取 userID1 与 userID2 的黑名单状态
func GetBlacklistRelation(userID1, userID2 int64) (bool /* userID1 拉黑 userID2 */, bool /* userID2 拉黑 userID1 */, error) {
	if userID1 == 0 || userID2 == 0 || userID1 == userID2 {
		return false, false, nil
	}
	var smallID, bigID int64
	isBigger := userID1 > userID2
	if isBigger {
		bigID = userID1
		smallID = userID2
	} else {
		bigID = userID2
		smallID = userID1
	}
	var blacklistInfo Model
	err := service.MessageDB.Where("big_id = ? AND small_id = ?", bigID, smallID).
		Take(&blacklistInfo).Error
	if err != nil {
		if !servicedb.IsErrNoRows(err) {
			return false, false, err
		}
		return false, false, nil
	}
	bBanS, sBanB := blacklistInfo.Status.IsSet(StatusBigBanSmall), blacklistInfo.Status.IsSet(StatusSmallBanBig)
	if isBigger {
		return bBanS, sBanB, nil
	}
	return sBanB, bBanS, nil
}

// ListBlockedByUser 获取被该用户拉黑的所有用户 ID
func ListBlockedByUser(userID int64) ([]int64, error) {
	blacklist := make([]*Model, 0)
	err := service.MessageDB.Select("small_id, big_id, status").
		Or("small_id = ? AND status & ?", userID, StatusSmallBanBig).
		Or("big_id = ? AND status & ?", userID, StatusBigBanSmall).Find(&blacklist).Error
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(blacklist))
	for _, black := range blacklist {
		switch {
		case black.SmallID == userID && black.Status.IsSet(StatusSmallBanBig):
			userIDs = append(userIDs, black.BigID)
		case black.BigID == userID && black.Status.IsSet(StatusBigBanSmall):
			userIDs = append(userIDs, black.SmallID)
		}
	}
	return userIDs, nil
}

// ListWhoBlockedUser 获取用户被哪些用户拉黑的用户 ID
func ListWhoBlockedUser(userID int64) ([]int64, error) {
	blacklist := make([]*Model, 0)
	err := service.MessageDB.Select("small_id, big_id, status").
		Or("small_id = ? AND status & ?", userID, StatusBigBanSmall).
		Or("big_id = ? AND status & ?", userID, StatusSmallBanBig).Find(&blacklist).Error
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(blacklist))
	for _, black := range blacklist {
		switch {
		case black.SmallID == userID && black.Status.IsSet(StatusBigBanSmall):
			userIDs = append(userIDs, black.BigID)
		case black.BigID == userID && black.Status.IsSet(StatusSmallBanBig):
			userIDs = append(userIDs, black.SmallID)
		}
	}
	return userIDs, nil
}

// FindBlockUserIDs 获取 checkUserIDs 与 userID 之间的拉黑关系
// 依次返回拉黑 userID 的 checkUserIDs、被 userID 拉黑的 checkUserIDs
func FindBlockUserIDs(userID int64, checkUserIDs []int64) (blockUserList, userBlockList []int64, err error) {
	var smallerUserIDs, biggerUserIDs []int64
	for _, v := range checkUserIDs {
		if v > userID {
			biggerUserIDs = append(biggerUserIDs, v)
		} else if v < userID {
			smallerUserIDs = append(smallerUserIDs, v)
		}
	}
	var blockBiggerList, blockSmallerList []*Model
	if len(biggerUserIDs) > 0 {
		err = Model{}.DB().
			Select("big_id, status").
			Where("big_id IN (?) AND small_id = ?", biggerUserIDs, userID).
			Find(&blockBiggerList).Error
		if err != nil {
			return nil, nil, err
		}
		for _, block := range blockBiggerList {
			if block.Status.IsSet(StatusBigBanSmall) {
				blockUserList = append(blockUserList, block.BigID)
			}
			if block.Status.IsSet(StatusSmallBanBig) {
				userBlockList = append(userBlockList, block.BigID)
			}
		}
	}
	if len(smallerUserIDs) > 0 {
		err = Model{}.DB().
			Select("small_id, status").
			Where("big_id = ? AND small_id IN (?)", userID, smallerUserIDs).
			Find(&blockSmallerList).Error
		if err != nil {
			return nil, nil, err
		}
		for _, block := range blockSmallerList {
			if block.Status.IsSet(StatusBigBanSmall) {
				userBlockList = append(userBlockList, block.SmallID)
			}
			if block.Status.IsSet(StatusSmallBanBig) {
				blockUserList = append(blockUserList, block.SmallID)
			}
		}
	}
	if blockUserList == nil {
		blockUserList = []int64{}
	}
	if userBlockList == nil {
		userBlockList = []int64{}
	}
	return blockUserList, userBlockList, nil
}
