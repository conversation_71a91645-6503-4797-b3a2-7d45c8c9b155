package blackuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetBlacklistRelation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := Model{
		SmallID: 10,
		BigID:   100,
	}
	m.Status.Set(StatusSmallBanBig)
	err := service.MessageDB.Where("small_id = ? AND big_id = ?", m.SmallID, m.BigID).Assign(m).FirstOrCreate(&m).Error
	require.NoError(err)

	status1, status2, err := GetBlacklistRelation(10, 10)
	require.NoError(err)
	assert.False(status1)
	assert.False(status2)

	status1, status2, err = GetBlacklistRelation(10, 100)
	require.NoError(err)
	assert.True(status1)
	assert.False(status2)

	status1, status2, err = GetBlacklistRelation(100, 10)
	require.NoError(err)
	assert.False(status1)
	assert.True(status2)

	status1, status2, err = GetBlacklistRelation(10, -1)
	require.NoError(err)
	assert.False(status1)
	assert.False(status2)
}

func TestListBlockedByUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs, err := ListBlockedByUser(99)
	require.NoError(err)
	assert.Len(userIDs, 4)

	userIDs, err = ListBlockedByUser(20222022)
	require.NoError(err)
	assert.Empty(userIDs)
}

func TestListWhoBlockedUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs, err := ListWhoBlockedUser(99)
	require.NoError(err)
	assert.Len(userIDs, 2)

	userIDs, err = ListWhoBlockedUser(9999999)
	require.NoError(err)
	assert.Empty(userIDs)
}

func TestFindBlockUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户没有拉黑和被拉黑
	testUserID := int64(190)
	testCheckUserIDs := []int64{20240480, 20240481, 20240482, 20240483, 20240484, 18001, 18002, 18003, 18004}
	blackUserList, userBlockList, err := FindBlockUserIDs(testUserID, testCheckUserIDs)
	require.NoError(err)
	assert.Empty(blackUserList)
	assert.NotNil(blackUserList)
	assert.Empty(userBlockList)
	assert.NotNil(userBlockList)

	// 测试用户有拉黑和被拉黑
	testUserID = int64(19001)
	blackUserList, userBlockList, err = FindBlockUserIDs(testUserID, testCheckUserIDs)
	require.NoError(err)
	assert.Equal([]int64{20240480, 20240482, 18001}, userBlockList)
	assert.Equal([]int64{20240481, 20240482, 18002, 18003}, blackUserList)
}
