package sound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MSound model
type MSound struct {
	ID               int64  `gorm:"column:id" json:"id"`
	CatalogID        int    `gorm:"column:catalog_id" json:"catalog_id"`
	CreateTime       int64  `gorm:"column:create_time" json:"create_time"`
	LastUpdateTime   int64  `gorm:"column:last_update_time" json:"last_update_time"`
	Duration         int    `gorm:"column:duration" json:"duration"` // TODO: 该字段表示音频时长（单位：毫秒），字段类型需要改成 int64
	UserID           int64  `gorm:"column:user_id" json:"user_id"`
	Username         string `gorm:"column:username" json:"username"`
	CoverImage       string `gorm:"column:cover_image" json:"cover_image"`
	AnimationID      int64  `gorm:"column:animationid" json:"animationid"`
	CharacterID      int64  `gorm:"column:characterid" json:"characterid"`
	SeiyID           int64  `gorm:"column:seiyid" json:"seiyid"`
	Soundstr         string `gorm:"column:soundstr" json:"soundstr"`
	Intro            string `gorm:"column:intro" json:"intro"`
	Soundurl         string `gorm:"column:soundurl" json:"soundurl"`
	Soundurl32       string `gorm:"column:soundurl_32" json:"soundurl_32"`
	Soundurl64       string `gorm:"column:soundurl_64" json:"soundurl_64"`
	Soundurl128      string `gorm:"column:soundurl_128" json:"soundurl_128"`
	Downtimes        int64  `gorm:"column:downtimes" json:"downtimes"`
	Uptimes          int64  `gorm:"column:uptimes" json:"uptimes"`
	Checked          int    `gorm:"column:checked" json:"checked"`
	Source           byte   `gorm:"column:source" json:"source"`
	Download         byte   `gorm:"column:download" json:"download"`
	ViewCount        int64  `gorm:"column:view_count" json:"view_count"`
	CommentCount     int64  `gorm:"column:comment_count" json:"comment_count"` // 弹幕数
	FavoriteCount    int64  `gorm:"column:favorite_count" json:"favorite_count"`
	Point            int64  `gorm:"column:point" json:"point"`
	Push             byte   `gorm:"column:push" json:"push"`
	Refined          byte   `gorm:"column:refined" json:"refined"`
	CommentsCount    int64  `gorm:"column:comments_count" json:"comments_count"` // 评论数
	SubCommentsCount int64  `gorm:"column:sub_comments_count" json:"sub_comments_count"`
	PayType          byte   `gorm:"column:pay_type" json:"pay_type"`
	Type             int    `gorm:"column:type" json:"type"` // 音频类型

	FrontCover string `gorm:"-" json:"front_cover"`
}

// Add afterFind if needed

const tableName = "m_sound"

// TitleMaxLength 音频标题字符最大长度
const TitleMaxLength = 100

const (
	// LimitTypeEquipPay 同时播放设备限制
	LimitTypeEquipPay = iota + 1
	// LimitTypeVipPay 会员收听限制
	LimitTypeVipPay
	// LimitTypeDrama 整剧付费限制
	LimitTypeDrama
	// LimitTypeEpisode 单集付费限制
	LimitTypeEpisode
)

// DB the db instance of MSound model
func (m MSound) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MSound) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (m *MSound) AfterFind() {
	// TODO: 以后 image 要兼容协议地址
	image := m.CoverImage
	if image == "" {
		m.FrontCover = service.Storage.Parse(params.URL.DefaultCoverURL)
	} else {
		m.FrontCover = service.Storage.Parse(params.URL.CoverURL + image)
	}
}

// CheckNeedPay checks if user of userID needs to buy snd
func (m MSound) CheckNeedPay(clientIP string, userID int64) (int, error) {
	if SoundFree == m.PayType {
		return SoundFree, nil
	}
	paidIDs, err := m.GetPaidSoundIDs(clientIP, []MSound{m}, userID)
	if err != nil {
		return 0, err
	}
	if util.HasElem(paidIDs, m.ID) {
		return SoundPaid, nil
	}
	return SoundUnpaid, nil
}

// GetPaidSoundIDs get paid sound IDs
func (MSound) GetPaidSoundIDs(clientIP string, snd []MSound, userID int64) ([]int64, error) {
	if len(snd) == 0 || userID == 0 {
		return nil, nil
	}
	var payBySoundIDs []int64
	var payByDramaIDs []int64
	for _, v := range snd {
		if v.PayType == PayBySound {
			payBySoundIDs = append(payBySoundIDs, v.ID)
		}
		if v.PayType == PayByDrama {
			payByDramaIDs = append(payByDramaIDs, v.ID)
		}
	}

	var paidIDs []int64
	if len(payBySoundIDs) > 0 {
		var err error
		paidIDs, err = transaction.FindUserPaidSoundIDs(userID, payBySoundIDs)
		if err != nil {
			return nil, err
		}
	}
	if len(payByDramaIDs) > 0 {
		res, err := dramainfo.GetDramaIDsBySoundIDs(payByDramaIDs)
		if err != nil {
			return nil, err
		}

		var dramaIDs []int64
		for _, r := range res {
			dramaIDs = append(dramaIDs, r.DramaID)
		}

		var paidDramaIDs []int64
		if len(dramaIDs) > 0 {
			err = transaction.TransactionLog{}.DB().Select("gift_id").
				Where("gift_id IN (?) AND from_id = ? AND type = ? AND status = ?", dramaIDs, userID, transaction.TypeDrama, transaction.StatusSuccess).
				Pluck("gift_id", &paidDramaIDs).Error
			if err != nil {
				return nil, err
			}
		}

		for _, re := range res {
			for _, id := range paidDramaIDs {
				if re.DramaID == id {
					paidIDs = append(paidIDs, re.SoundID)
				}
			}
		}
	}
	return paidIDs, nil
}

// IncrCommentCount 实现 Commentable 接口，评论数 +1
func (m *MSound) IncrCommentCount(db *gorm.DB, id int64) error {
	// m_sound 这张表中有 comment_count 这个字段，它代表的是弹幕数，
	// 字段 comments_count 表示的才是父评论数。
	// 当前函数表示的是增加父评论数，为了避免歧义同时实现这个 Commentable 接口，
	// 我们这里额外增加了一个和字段名同名的函数。
	return m.incrCommentsCount(db, id)
}

// 评论数 +1
func (m *MSound) incrCommentsCount(db *gorm.DB, id int64) error {
	return db.Table(m.TableName()).Where("id = ?", id).
		UpdateColumn("comments_count", gorm.Expr("comments_count + 1")).Error
}

// 弹幕数 +1
func (m *MSound) incrCommentCount(db *gorm.DB, id int64) error { //nolint:unused
	return db.Table(m.TableName()).Where("id = ?", id).
		UpdateColumn("comment_count", gorm.Expr("comment_count + 1")).Error
}

// IncrSubCommentCount 实现 Commentable 接口，子评论数 +1
func (m *MSound) IncrSubCommentCount(db *gorm.DB, id int64) error {
	return db.Table(m.TableName()).Where("id = ?", id).
		UpdateColumn("sub_comments_count", gorm.Expr("sub_comments_count + 1")).Error
}

// FindMSound 查询音频信息
func FindMSound(id int64) (*MSound, error) {
	var snd MSound
	err := service.DB.Select("id, duration, user_id, soundstr, checked, pay_type").Take(&snd, "id = ?", id).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &snd, nil
}

// FindIDCheckedMap 获取音频的审核状态 map[ID]Checked
func FindIDCheckedMap(ids []int64) (map[int64]int, error) {
	if len(ids) == 0 {
		return map[int64]int{}, nil
	}
	var mSounds []*MSound
	db := MSound{}.DB().Select("id, checked").Where("id IN (?)", util.Uniq(ids))
	err := db.Find(&mSounds).Error
	if err != nil {
		return nil, err
	}
	idCheckedMap := make(map[int64]int, len(mSounds))
	for _, sound := range mSounds {
		idCheckedMap[sound.ID] = sound.Checked
	}
	return idCheckedMap, nil
}

// GetSoundCatalogIDs returns the corresponding catalog IDs from given sound IDs
func GetSoundCatalogIDs(soundIDs []int64) (map[int64]int, error) {
	soundCatalogs := make(map[int64]int)
	if len(soundIDs) == 0 {
		return soundCatalogs, nil
	}
	var soundInfo []struct {
		ID        int64 `gorm:"column:id"`
		CatalogID int   `gorm:"column:catalog_id"`
	}
	err := service.DB.Table(MSound{}.TableName()).Select("id, catalog_id").
		Where("id IN (?)", soundIDs).Find(&soundInfo).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return soundCatalogs, nil
		}
		return soundCatalogs, err
	}

	for _, v := range soundInfo {
		soundCatalogs[v.ID] = v.CatalogID
	}
	return soundCatalogs, nil
}

// ListSoundByUserID 根据用户 ID 获取音频信息（按创建时间倒序，且会过滤擦边球 1、擦边球 2、搜索隐藏音频）
func ListSoundByUserID(userID int64, soundTypes []int, limit int) ([]MSound, error) {
	if len(soundTypes) <= 0 {
		panic("invalid soundTypes length")
	}
	var snd []MSound
	err := service.DB.Table(MSound{}.TableName()).Select("id, cover_image, soundstr, duration, view_count").
		Where("user_id = ? AND type IN (?) AND checked = ? AND NOT (refined & ?)",
			userID, soundTypes, CheckedPass, RefinedNearDanger|RefinedBlock|RefinedSearchLimit).
		Order("create_time DESC, id DESC").Limit(limit).Find(&snd).Error
	if err != nil {
		return nil, err
	}
	return snd, nil
}

// CountUserSound 根据用户 ID、音频类型、音频属性获取用户审核通过音频的数量
func CountUserSound(userID int64, soundTypes []int, notRefined int) (count int64, err error) {
	err = MSound{}.DB().
		Where("user_id = ? AND type IN (?) AND checked = ? AND NOT (refined & ?)",
			userID, soundTypes, CheckedPass, notRefined).
		Count(&count).Error
	return
}

// GetLimitType 根据用户会员身份，客户端IP，用户ID获取音频播放限制类型；优先级：会员收听 > 付费 > 播放受限
func (m *MSound) GetLimitType(isVipUser bool, clientIP string, userID int64) (int, error) {
	if m.UserID == userID {
		// UP 主本人不受限
		return 0, nil
	}
	episode, err := dramaepisode.FindEpisodeBySoundID(m.ID)
	if err != nil {
		return 0, err
	}
	if episode == nil {
		return 0, nil
	}

	isVipLimit := episode.Vip == dramaepisode.VipLimit
	isPayLimit := util.HasElem([]int{PayByDrama, PayBySound}, int(m.PayType))
	isNeedPay, err := m.CheckNeedPay(clientIP, userID)
	if err != nil {
		return 0, err
	}
	if isVipLimit && !isVipUser && (!isPayLimit || isNeedPay == SoundUnpaid) {
		return LimitTypeVipPay, nil
	}
	if isPayLimit && isNeedPay == SoundUnpaid && !isVipLimit {
		if m.PayType == PayByDrama {
			return LimitTypeDrama, nil
		}
		return LimitTypeEpisode, nil
	}
	return 0, nil
}
