package sound

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/service"
)

const testUserID1, testUserID2 int64 = 1346281, 1346282
const testCatalogID1, testCatalogID2 int = 4, 5
const testSoundID1, testSoundID2, testSoundID3, testSoundID4, testSoundID5, testSoundID6, testSoundID7, testSoundIDBlack int64 = 100001, 100002, 100003, 100004, 100005, 100006, 100007, 1217690

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, recommendStrategyNone)
	assert.EqualValues(1, recommendStrategySimilarSoundID)
	assert.EqualValues(2, recommendStrategySameUserSound)
	assert.EqualValues(3, recommendStrategySameCatalogSound)
	assert.EqualValues(4, recommendStrategySameTagSound)
}

func TestMSound_recommendSoundCacheKey(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("sound_play_recommend_strategy_v2:id:1996", MSound{ID: 1996}.recommendSoundCacheKey())
	assert.Equal("sound_play_recommend_strategy_v2:id:233", MSound{ID: 233}.recommendSoundCacheKey())
}

func TestMSound_listSameUserSoundIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试查不到数据
	s := MSound{ID: testSoundID5, UserID: testUserID2}
	soundIDs, err := s.listSameUserSoundIDs(10)
	require.NoError(err)
	require.Nil(soundIDs)

	// 测试正常查到数据
	s = MSound{ID: testSoundID1, UserID: testUserID1}
	soundIDs, err = s.listSameUserSoundIDs(10)
	require.NoError(err)
	require.NotNil(soundIDs)
	assert.Equal([]int64{testSoundID2, testSoundID3, testSoundID4}, soundIDs)
}

func TestMSound_listSameCatalogSoundIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试查不到数据
	s := MSound{ID: testSoundID4, CatalogID: testCatalogID2}
	soundIDs, err := s.listSameCatalogSoundIDs(10)
	require.NoError(err)
	require.Nil(soundIDs)

	// 测试正常查到数据
	s = MSound{ID: testSoundID1, CatalogID: testCatalogID1}
	soundIDs, err = s.listSameCatalogSoundIDs(10)
	require.NoError(err)
	require.NotNil(soundIDs)
	assert.Equal([]int64{testSoundID2, testSoundID3}, soundIDs)
}

func TestMSound_listSameTagSoundIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试查不到数据
	s := MSound{ID: testSoundID3}
	soundIDs, err := s.listSameTagSoundIDs(10)
	require.NoError(err)
	require.Nil(soundIDs)

	// 测试正常查到数据
	s = MSound{ID: testSoundID1}
	soundIDs, err = s.listSameTagSoundIDs(10)
	require.NoError(err)
	require.NotNil(soundIDs)
	assert.Equal([]int64{testSoundID2, testSoundID6}, soundIDs)
}

func TestMSound_getOrSetRecommendSoundMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试无缓存的情况
	s := MSound{ID: testSoundID1, UserID: testUserID1, CatalogID: testCatalogID1}
	key := s.recommendSoundCacheKey()
	require.NoError(service.LRURedis.Del(key).Err())
	val := service.LRURedis.Exists(key).Val()
	require.Zero(val)
	soundMap, err := s.getOrSetRecommendSoundMap(10, 10, 20)
	require.NoError(err)
	exceptMap := RecommendSoundIDsMap{
		UserSoundIDs:    []int64{testSoundID2, testSoundID3, testSoundID4},
		CatalogSoundIDs: []int64{testSoundID2, testSoundID3},
		TagSoundIDs:     []int64{testSoundID2, testSoundID6},
	}
	assert.Equal(exceptMap, soundMap)
	// 验证缓存正常生成
	soundIDStr, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	var soundIDsMap RecommendSoundIDsMap
	err = json.Unmarshal([]byte(soundIDStr), &soundIDsMap)
	require.NoError(err)
	assert.Equal(exceptMap, soundIDsMap)

	// 测试有缓存：缓存正常的情况
	soundMap, err = s.getOrSetRecommendSoundMap(10, 10, 20)
	require.NoError(err)
	assert.Equal(exceptMap, soundMap)
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试有缓存：缓存异常的情况
	require.NoError(service.LRURedis.Set(key, "{1, 3, 5", 5*time.Minute).Err())
	soundMap, err = s.getOrSetRecommendSoundMap(10, 10, 20)
	require.NoError(err)
	assert.Equal(exceptMap, soundMap)
	// 验证重新生成了正常缓存
	soundIDStr, err = service.LRURedis.Get(key).Result()
	require.NoError(err)
	err = json.Unmarshal([]byte(soundIDStr), &soundIDsMap)
	require.NoError(err)
	assert.Equal(exceptMap, soundIDsMap)
	require.NoError(service.LRURedis.Del(key).Err())
}

func TestMergeRecommendSoundIDs(t *testing.T) {
	assert := assert.New(t)

	// 测试部分没有数据的情况
	soundIDsMap := RecommendSoundIDsMap{
		UserSoundIDs:    []int64{},
		CatalogSoundIDs: []int64{3},
		TagSoundIDs:     []int64{},
	}
	list := mergeRecommendSoundIDs(soundIDsMap)
	assert.Equal([]int64{3}, list)

	// 测试有重复值的情况
	soundIDsMap = RecommendSoundIDsMap{
		UserSoundIDs:    []int64{1, 2, 3, 4},
		CatalogSoundIDs: []int64{4, 3, 5, 6, 8, 9},
		TagSoundIDs:     []int64{3, 6, 7, 9, 10, 11},
	}
	list = mergeRecommendSoundIDs(soundIDsMap)
	assert.ElementsMatch([]int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}, list)
}

func TestListRecommendSoundIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := MSound{ID: testSoundID1}.recommendSoundCacheKey()
	require.NoError(service.LRURedis.Del(key).Err())
	key = MSound{ID: testSoundID7}.recommendSoundCacheKey()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试推荐音频为空的情况
	list, maps, err := listRecommendSoundIDs(testSoundID7, 10)
	require.NoError(err)
	assert.Equal([]int64{}, list)
	assert.Equal(RecommendSoundIDsMap{}, maps)

	//测试所需数量超出实际拥有数量的情况
	list, maps, err = listRecommendSoundIDs(testSoundID1, 10)
	require.NoError(err)
	assert.ElementsMatch([]int64{testSoundID2, testSoundID3, testSoundID4, testSoundID6}, list)
	exceptMaps := RecommendSoundIDsMap{
		UserSoundIDs:    []int64{testSoundID2, testSoundID3, testSoundID4},
		CatalogSoundIDs: []int64{testSoundID2, testSoundID3},
		TagSoundIDs:     []int64{testSoundID2, testSoundID6},
	}
	assert.Equal(exceptMaps, maps)

	// 测试所需数量等于实际拥有数量的情况
	list, maps, err = listRecommendSoundIDs(testSoundID1, 4)
	require.NoError(err)
	assert.ElementsMatch([]int64{testSoundID2, testSoundID3, testSoundID4, testSoundID6}, list)
	assert.Equal(exceptMaps, maps)

	// 测试所需数量小于实际拥有数量的情况
	list, maps, err = listRecommendSoundIDs(testSoundID1, 3)
	require.NoError(err)
	require.Equal(3, len(list))
	assert.Equal(exceptMaps, maps)
}

func TestRecommendSoundIDsMap_getRecommendStrategyID(t *testing.T) {
	assert := assert.New(t)

	soundIDsMap := RecommendSoundIDsMap{
		UserSoundIDs:    []int64{testSoundID2, testSoundID3},
		CatalogSoundIDs: []int64{testSoundID3, testSoundID4},
		TagSoundIDs:     []int64{testSoundID6},
	}
	strategyID := soundIDsMap.getRecommendStrategyID(testSoundID3)
	assert.Equal(recommendStrategySameUserSound, strategyID)

	strategyID = soundIDsMap.getRecommendStrategyID(testSoundID4)
	assert.Equal(recommendStrategySameCatalogSound, strategyID)

	strategyID = soundIDsMap.getRecommendStrategyID(testSoundID6)
	assert.Equal(recommendStrategySameTagSound, strategyID)

	strategyID = soundIDsMap.getRecommendStrategyID(testSoundID5)
	assert.Equal(recommendStrategyNone, strategyID)
}

func TestFindRecommendSounds(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	params.NoticeSoundIDs = []int64{testSoundIDBlack}
	key := MSound{ID: testSoundID1}.recommendSoundCacheKey()
	require.NoError(service.LRURedis.Del(key).Err())
	require.NoError(MSound{}.DB().Where("id = ?", testSoundIDBlack).Update("user_id", testUserID1).Error)
	recommendSoundList, err := FindRecommendSounds(testSoundID1, 7)
	require.NoError(err)
	assert.NotNil(recommendSoundList)
	exceptSoundList := []RecommendSound{
		{
			ID:          testSoundID2,
			Soundstr:    "测试推荐音频",
			ViewCount:   67939,
			AllComments: 343,
			FrontCover:  service.Storage.Parse(params.URL.CoverURL + "201701/24/test.png"),
			Video:       false,
			StrategyID:  recommendStrategySameUserSound,
		},
		{
			ID:          testSoundID3,
			Soundstr:    "测试推荐音频",
			ViewCount:   67939,
			AllComments: 343,
			FrontCover:  service.Storage.Parse(params.URL.CoverURL + "201701/24/test.png"),
			Video:       false,
			StrategyID:  recommendStrategySameUserSound,
		},
		{
			ID:          testSoundID4,
			Soundstr:    "测试推荐音频",
			ViewCount:   67939,
			AllComments: 343,
			FrontCover:  service.Storage.Parse(params.URL.CoverURL + "201701/24/test.png"),
			Video:       false,
			StrategyID:  recommendStrategySameUserSound,
		},
		{
			ID:          testSoundID6,
			Soundstr:    "测试推荐音频",
			ViewCount:   67939,
			AllComments: 343,
			FrontCover:  service.Storage.Parse(params.URL.CoverURL + "201701/24/test.png"),
			Video:       false,
			StrategyID:  recommendStrategySameTagSound,
		},
		{
			ID:          testSoundIDBlack,
			Soundstr:    "测试推荐音频",
			ViewCount:   67939,
			AllComments: 0,
			FrontCover:  service.Storage.Parse(params.URL.CoverURL + "201701/24/test.png"),
			Video:       false,
			StrategyID:  recommendStrategySameUserSound,
		},
	}
	assert.ElementsMatch(exceptSoundList, recommendSoundList)
}
