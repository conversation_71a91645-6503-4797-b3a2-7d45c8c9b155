package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestCheckSoundID(t *testing.T) {
	assert := assert.New(t)

	falseID := [4]int64{SoundNotExists, SoundTranscoding, SoundUnchecked, SoundUpgrade}
	for i := 0; i < 4; i++ {
		assert.False(CheckSoundID(falseID[i]))
	}
	assert.True(CheckSoundID(100))
}
