package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFindHasVideoSoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	soundIDs, err := FindHasVideoSoundIDs([]int64{testSoundID, testNotSoundID})
	require.NoError(err)
	require.Len(soundIDs, 1)
	assert.Equal(testSoundID, soundIDs[0])

	soundIDs, err = FindHasVideoSoundIDs([]int64{})
	require.NoError(err)
	assert.Empty(soundIDs)
}
