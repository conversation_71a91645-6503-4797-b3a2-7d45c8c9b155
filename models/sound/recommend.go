package sound

import (
	"encoding/json"
	"math/rand"
	"time"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// RecommendSoundIDsMap 指定音频的推荐音频信息
type RecommendSoundIDsMap struct {
	UserSoundIDs    []int64 `json:"user_sound_ids,omitempty"`
	CatalogSoundIDs []int64 `json:"catalog_sound_ids,omitempty"`
	TagSoundIDs     []int64 `json:"tag_sound_ids,omitempty"`
}

// RecommendSound 指定音频的推荐音频信息
type RecommendSound struct {
	ID          int64  `json:"id"`
	Soundstr    string `json:"soundstr"`
	ViewCount   int64  `json:"view_count"`
	AllComments int64  `json:"all_comments"`
	FrontCover  string `json:"front_cover"`
	StrategyID  int    `json:"strategy_id"`
	Video       bool   `json:"video"`
}

// 根据发布者、分类、标签进行推荐时查询推荐音频的数量
const (
	recommendUserSoundNum    int64 = 10
	recommendCatalogSoundNum int64 = 20
	recommendTagSoundNum     int64 = 30
)

// 音频推荐策略
const (
	recommendStrategyNone             = iota // 无
	recommendStrategySimilarSoundID          // 相似音频推荐
	recommendStrategySameUserSound           // 同发布者推荐
	recommendStrategySameCatalogSound        // 同分类推荐
	recommendStrategySameTagSound            // 同标签推荐
)

// recommendSoundCacheKey 指定音频的推荐音频缓存 key
func (m MSound) recommendSoundCacheKey() string {
	return keys.KeySoundPlayRecommendStrategySounds1.Format(m.ID)
}

// listSameUserSoundIDs 获取指定音频同发布者的其他过审音频 IDs
func (m MSound) listSameUserSoundIDs(num int64) (soundIDs []int64, err error) {
	err = m.DB().Where("user_id = ? AND id <> ? AND checked = ? AND (NOT refined & ?)",
		m.UserID, m.ID, CheckedPass, RefinedNearDangerOrBlocK).
		Order("point DESC").Limit(num).Pluck("id", &soundIDs).Error
	if err != nil {
		return nil, err
	}
	return
}

// listSameCatalogSoundIDs 获取指定音频同分类的其他过审音频 IDs
func (m MSound) listSameCatalogSoundIDs(num int64) (soundIDs []int64, err error) {
	err = m.DB().Where("catalog_id = ? AND id <> ? AND checked = ? AND (NOT refined & ?)",
		m.CatalogID, m.ID, CheckedPass, RefinedNearDangerOrBlocK).
		Order("point DESC").Limit(num).Pluck("id", &soundIDs).Error
	if err != nil {
		return nil, err
	}
	return
}

// listSameTagSoundIDs 获取指定音频同标签的其他过审音频 IDs
func (m MSound) listSameTagSoundIDs(num int64) (soundIDs []int64, err error) {
	var tagList []int64
	err = MTagSoundMap{}.DB().Where("sound_id = ?", m.ID).Pluck("tag_id", &tagList).Error
	if err != nil {
		return nil, err
	}
	if len(tagList) == 0 {
		return nil, nil
	}
	err = service.DB.Table(m.TableName()+" AS s").
		Joins("LEFT JOIN "+MTagSoundMap{}.TableName()+" AS t ON s.id = t.sound_id").
		Where("t.tag_id IN (?) AND s.id <> ? AND s.checked = ? AND (NOT s.refined & ?)",
			tagList, m.ID, CheckedPass, RefinedNearDangerOrBlocK).
		Order("s.point DESC").Limit(num).Pluck("DISTINCT s.id", &soundIDs).Error
	if err != nil {
		return nil, err
	}
	return
}

// getOrSetRecommendSoundMap 获取或设置指定音频的推荐音频 map
func (m MSound) getOrSetRecommendSoundMap(userSoundNum, catalogSoundNum, tagSoundNum int64) (soundIDsMap RecommendSoundIDsMap, err error) {
	key := m.recommendSoundCacheKey()
	soundIDStr, err := service.LRURedis.Get(key).Result()
	if err == nil {
		err = json.Unmarshal([]byte(soundIDStr), &soundIDsMap)
		if err == nil {
			return soundIDsMap, nil
		}
		logger.WithField("sound_id", m.ID).Errorf("缓存的推荐音频转回 IDs map 失败: %v", err)
		// PASS
	} else if !serviceredis.IsRedisNil(err) {
		logger.WithField("sound_id", m.ID).Errorf("从缓存获取推荐音频 IDs 失败: %v", err)
		// PASS
	}
	userSoundIDs, err := m.listSameUserSoundIDs(userSoundNum)
	if err != nil {
		return soundIDsMap, err
	}
	soundIDsMap.UserSoundIDs = userSoundIDs
	catalogSoundIDs, err := m.listSameCatalogSoundIDs(catalogSoundNum)
	if err != nil {
		return soundIDsMap, err
	}
	soundIDsMap.CatalogSoundIDs = catalogSoundIDs
	tagSoundIDs, err := m.listSameTagSoundIDs(tagSoundNum)
	if err != nil {
		return soundIDsMap, err
	}
	soundIDsMap.TagSoundIDs = tagSoundIDs
	soundIDsJSON, err := json.Marshal(soundIDsMap)
	if err != nil {
		logger.WithField("sound_id", m.ID).Errorf("推荐音频 IDs 转 json 失败: %v", err)
		// PASS
	}
	err = service.LRURedis.Set(key, string(soundIDsJSON), 5*time.Minute).Err()
	if err != nil {
		logger.WithField("sound_id", m.ID).Errorf("设置推荐音频缓存失败: %v", err)
		// PASS
	}
	return soundIDsMap, nil
}

// mergeRecommendSoundIDs 合并待推荐音频 IDs 并去重
func mergeRecommendSoundIDs(soundIDsMap RecommendSoundIDsMap) []int64 {
	merged := make(map[int64]struct{}, len(soundIDsMap.UserSoundIDs)+len(soundIDsMap.CatalogSoundIDs)+len(soundIDsMap.TagSoundIDs))
	for _, id := range soundIDsMap.UserSoundIDs {
		merged[id] = struct{}{}
	}
	for _, id := range soundIDsMap.CatalogSoundIDs {
		merged[id] = struct{}{}
	}
	for _, id := range soundIDsMap.TagSoundIDs {
		merged[id] = struct{}{}
	}
	result := make([]int64, 0, len(merged))
	for id := range merged {
		result = append(result, id)
	}
	return result
}

// listRecommendSoundIDs 获取指定音频的推荐音频 IDs 及其推荐来源，推荐来源用于数据库查出数据后为其绑定推荐策略
func listRecommendSoundIDs(id, num int64) (soundIDs []int64, soundIDsMap RecommendSoundIDsMap, err error) {
	var m MSound
	err = m.DB().Take(&m, id).Error
	if err != nil {
		return nil, RecommendSoundIDsMap{}, err
	}

	var recommendSoundNum = recommendUserSoundNum + recommendCatalogSoundNum + recommendTagSoundNum
	if recommendSoundNum < num {
		logger.WithField("sound_id", id).Errorf("音频所需推荐音频数量 %d 已超出最大查询数量 %d", num, recommendSoundNum)
		// PASS
	}
	soundIDsMap, err = m.getOrSetRecommendSoundMap(recommendUserSoundNum, recommendCatalogSoundNum, recommendTagSoundNum)
	if err != nil {
		return nil, RecommendSoundIDsMap{}, err
	}
	soundIDs = mergeRecommendSoundIDs(soundIDsMap)
	soundNum := int64(len(soundIDs))
	if soundNum < num {
		logger.WithField("sound_id", id).Errorf("音频所需推荐音频数量 %d 已超出符合条件的音频总量 %d", num, soundNum)
		// PASS
	} else if soundNum > num {
		rand.Shuffle(len(soundIDs), func(i, j int) {
			soundIDs[i], soundIDs[j] = soundIDs[j], soundIDs[i]
		})
		soundIDs = soundIDs[:num]
	}
	return
}

// getRecommendStrategyID 获取音频推荐来源 ID
func (soundIDsMap RecommendSoundIDsMap) getRecommendStrategyID(soundID int64) int {
	if util.HasElem(soundIDsMap.UserSoundIDs, soundID) {
		return recommendStrategySameUserSound
	}
	if util.HasElem(soundIDsMap.CatalogSoundIDs, soundID) {
		return recommendStrategySameCatalogSound
	}
	if util.HasElem(soundIDsMap.TagSoundIDs, soundID) {
		return recommendStrategySameTagSound
	}
	return recommendStrategyNone
}

// FindRecommendSounds 获取推荐音频列表
// TODO: 目前仅根据用户、分类或标签进行推荐时调用 rpc 接口，需完善 recommend 表相关推荐策略后改为所有推荐策略调 rpc 接口进行推荐
func FindRecommendSounds(id, num int64) ([]RecommendSound, error) {
	soundIDs, soundIDsMap, err := listRecommendSoundIDs(id, num)
	if err != nil {
		return nil, err
	}
	if len(soundIDs) == 0 {
		return nil, nil
	}
	var sounds []MSound
	err = MSound{}.DB().Select("id, cover_image, soundstr, view_count, comment_count, comments_count, sub_comments_count").
		Where("id IN (?) AND checked = ?", soundIDs, CheckedPass).Find(&sounds).Error
	if err != nil {
		return nil, err
	}
	if len(sounds) == 0 {
		return nil, nil
	}

	videoSoundIDs, err := FindHasVideoSoundIDs(soundIDs)
	if err != nil {
		return nil, err
	}
	videoSoundIDsMap := make(map[int64]bool, len(videoSoundIDs))
	for _, id := range videoSoundIDs {
		videoSoundIDsMap[id] = true
	}
	noticeSoundIDsMap := make(map[int64]bool, len(params.NoticeSoundIDs))
	for _, id := range params.NoticeSoundIDs {
		noticeSoundIDsMap[id] = true
	}
	recommendSoundList := make([]RecommendSound, 0, len(sounds))
	for _, sound := range sounds {
		allComments := sound.CommentCount + sound.CommentsCount + sound.SubCommentsCount
		if noticeSoundIDsMap[sound.ID] {
			allComments = 0
		}
		recommendSound := RecommendSound{
			ID:          sound.ID,
			Soundstr:    sound.Soundstr,
			ViewCount:   sound.ViewCount,
			AllComments: allComments,
			FrontCover:  sound.FrontCover,
			Video:       videoSoundIDsMap[sound.ID],
			StrategyID:  soundIDsMap.getRecommendStrategyID(sound.ID),
		}
		recommendSoundList = append(recommendSoundList, recommendSound)
	}
	return recommendSoundList, nil
}
