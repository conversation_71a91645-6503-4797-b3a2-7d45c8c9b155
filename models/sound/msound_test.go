package sound

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const testSoundID = int64(44809)
const testNotSoundID = int64(999999)

func TestAfterFind(t *testing.T) {
	assert := assert.New(t)

	m := &MSound{
		CoverImage: "",
	}
	m.AfterFind()
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", m.FrontCover)
	m.CoverImage = "201701/24/feb3e83efe8e66447791422826f303ab092714.png"
	m.AfterFind()
	assert.Equal("http://static-test.missevan.com/coversmini/201701/24/feb3e83efe8e66447791422826f303ab092714.png", m.FrontCover)
}

func TestCheckNeedPay(t *testing.T) {
	require := require.New(t)
	require.NotNil(require)
	assert := assert.New(t)
	assert.NotNil(assert)

	// begin test
	{
		snd := MSound{PayType: SoundFree}
		payType, err := snd.CheckNeedPay("127.0.0.1", 0)
		assert.NoError(err)
		assert.Equal(SoundFree, payType)
	}

	{
		dramaIDPayBySound := int64(888777666)
		cancel := mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(interface{}) (interface{}, error) {
			type res struct {
				DramaID int64 `json:"drama_id"`
			}
			return []res{{dramaIDPayBySound}}, nil
		})
		defer cancel()

		soundTransLog := transaction.TransactionSoundLog{
			UserID:  1,
			DramaID: dramaIDPayBySound,
		}
		var s MSound
		require.NoError(service.DB.Where("pay_type = ?", PayBySound).First(&s).Error)
		soundTransLog.SoundID = s.ID
		require.NoError(soundTransLog.DB().FirstOrCreate(&soundTransLog, soundTransLog).Error)

		snd := MSound{ID: soundTransLog.SoundID, PayType: PayBySound}
		payType, err := snd.CheckNeedPay("127.0.0.1", soundTransLog.UserID)
		assert.NoError(err)
		assert.Equal(SoundPaid, payType)
	}

	{
		dramaIDPayByDrama := int64(999888777)
		cancel := mrpc.SetMock("drama://api/get-dramaid-by-soundid", func(interface{}) (interface{}, error) {
			type res struct {
				DramaID int64 `json:"drama_id"`
			}
			return []res{{dramaIDPayByDrama}}, nil
		})
		defer cancel()

		trans := transaction.TransactionLog{
			FromID: 1,
			GiftID: dramaIDPayByDrama,
			Type:   transaction.TypeDrama,
			Status: transaction.StatusSuccess,
		}
		require.NoError(trans.DB().FirstOrCreate(&trans, trans).Error)

		snd := MSound{PayType: PayByDrama}
		payType, err := snd.CheckNeedPay("127.0.0.1", 1)
		assert.NoError(err)
		assert.Equal(SoundPaid, payType)

		payType, err = snd.CheckNeedPay("127.0.0.1", -1)
		assert.NoError(err)
		assert.Equal(SoundUnpaid, payType)
	}
}

func TestFindMSound(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试音频 ID 存在
	snd, err := FindMSound(testSoundID)
	require.NoError(err)
	assert.Equal(testSoundID, snd.ID)
	assert.Equal("金牌助理之弯弯没想到 S02E00", snd.Soundstr)

	// 测试音频 ID 不存在
	snd, err = FindMSound(testNotSoundID)
	require.NoError(err)
	assert.Empty(snd)

	// 测试转码失败音频
	snd, err = FindMSound(99999)
	require.NoError(err)
	assert.NotEmpty(snd)
}

func TestFindIDCheckedMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	idCheckedMap, err := FindIDCheckedMap([]int64{testSoundID})
	require.NoError(err)
	assert.Len(idCheckedMap, 1)

	idCheckedMap, err = FindIDCheckedMap([]int64{})
	require.NoError(err)
	assert.Empty(idCheckedMap)

	idCheckedMap, err = FindIDCheckedMap([]int64{testNotSoundID})
	require.NoError(err)
	assert.Empty(idCheckedMap)
}

func TestGetSoundCatalogIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var id []int64
	err := service.DB.Table(MSound{}.TableName()).Select("id").
		Limit(2).Pluck("id", &id).Error
	require.NoError(err)

	mapCatalog, err := GetSoundCatalogIDs(id)
	require.NoError(err)
	assert.Equal(len(mapCatalog), len(id))
}

func TestListSoundByUserID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	snd, err := ListSoundByUserID(int64(999999), []int{TypeLive}, 1)
	require.NoError(err)
	assert.Empty(snd)

	snd, err = ListSoundByUserID(int64(346286), []int{TypeLive}, 1)
	require.NoError(err)
	assert.Len(snd, 1)
}

func TestCountUserSound(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	count, err := CountUserSound(346286, []int{TypeLive}, RefinedBlock)
	require.NoError(err)
	assert.EqualValues(1, count)

	count, err = CountUserSound(999999, []int{TypeLive}, RefinedBlock)
	require.NoError(err)
	assert.Zero(count)
}

func TestGetLimitType(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	clientIP := "127.0.0.1"
	testUserID := int64(11)
	episodeSoundID := int64(3680)
	episodeVipLimiteSoundID := int64(3679)
	// 默认 err 返回
	snd := &MSound{
		ID:      episodeVipLimiteSoundID,
		PayType: 0,
	}
	limitType, err := snd.GetLimitType(true, clientIP, testUserID)
	require.NoError(err)
	assert.Equal(limitType, 0)
	// 回会员收听限制 LimitTypeVipPay
	snd = &MSound{
		ID:      episodeVipLimiteSoundID,
		PayType: 0,
	}
	limitType, err = snd.GetLimitType(false, clientIP, testUserID)
	require.NoError(err)
	assert.Equal(limitType, 2)
	// 整剧付费限制 LimitTypeDrama
	snd = &MSound{
		ID:      episodeSoundID,
		PayType: 2,
	}
	limitType, err = snd.GetLimitType(false, clientIP, testUserID)
	require.NoError(err)
	assert.Equal(limitType, 3)
	// 单集付费限制 LimitTypeEpisode
	snd = &MSound{
		ID:      episodeSoundID,
		PayType: 1,
	}
	limitType, err = snd.GetLimitType(false, clientIP, testUserID)
	require.NoError(err)
	assert.Equal(limitType, 4)

	// 测试 UP 主本人不受限
	snd = &MSound{
		ID:      episodeSoundID,
		PayType: 1,
		UserID:  testUserID,
	}
	limitType, err = snd.GetLimitType(false, clientIP, testUserID)
	require.NoError(err)
	assert.Equal(limitType, 0)
}
