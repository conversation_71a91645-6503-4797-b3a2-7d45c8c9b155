package sound

// 剧集的付费情况
const (
	SoundFree   = 0 // 免费
	SoundUnpaid = 1 // 付费剧集未付费
	SoundPaid   = 2 // 付费剧集已付费

)

// 付费类型
const (
	PayBySound = 1 // 单音付费
	PayByDrama = 2 // 剧集付费
)

// 提示客户端需要升级的音频 ID
const (
	SoundUpgrade = 518008
)

// 音频过审状态
const (
	CheckedTranscodeFailed = -3 // 转码失败
	CheckedDubTranscode    = -2 // 配音未转码
	CheckedSoundTranscode  = -1 // 未转码
	CheckedUnpass          = 0  // 审核中
	CheckedPass            = 1  // 已审核通过
	CheckedPolice          = 2  // 报警
	CheckedDiscontinued    = 3  // 下架
	CheckedContractExpired = 4  // 合约期满
)

// 问题单音 ID
const (
	SoundNotExists   = 75854
	SoundTranscoding = 115193
	SoundUnchecked   = 223692
)

// 新老用户每日发送弹幕最大数量
const (
	NewUserDMMaxNum = 50  // 新用户（注册时间不满 3 天）每日发送弹幕最大数量
	UserDMMaxNum    = 500 // 老用户每日发送弹幕最大数量
)

// 字段 refined 状态（按位处理）
const (
	// 普通
	RefinedCommon = 0
	// 加精
	RefinedRefined = 1 << (iota - 1)
	// 擦边球 1
	RefinedNearDanger
	// 无法被搜索到
	RefinedSearchLimit
	// 擦边球 2，在分区、标签及用户主页不可见
	RefinedBlock
)

// RefinedNearDangerOrBlocK 擦边球 1 或 擦边球 2
const RefinedNearDangerOrBlocK = RefinedNearDanger | RefinedBlock

// 音频类型
const (
	TypeNormal      = iota // 普通音频
	TypeMusic              // 音乐集音频
	TypeInteractive        // 互动广播剧音频
	TypeLive               // 直播回放
)

// CheckSoundID checks sound IDs
func CheckSoundID(soundID int64) bool {
	switch soundID {
	case SoundNotExists, SoundTranscoding, SoundUnchecked, SoundUpgrade:
		return false
	default:
		return true
	}
}
