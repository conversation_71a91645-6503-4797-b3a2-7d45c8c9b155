package msoundnode

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(tableName, MSoundNode{}.TableName())
}

func TestAfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	node := MSoundNode{
		Cover: "test://path/test.jpg",
	}
	err := node.AfterFind()
	require.NoError(err)
	assert.Equal("http://static-test.missevan.com/path/test.jpg", node.CoverURL)

	node.Cover = ""
	node.Soundurl = "test://path/sound.m4a"
	err = node.AfterFind()
	require.NoError(err)
	assert.Equal("http://static-test.missevan.com/coversmini/nocover.png", node.CoverURL)
}
