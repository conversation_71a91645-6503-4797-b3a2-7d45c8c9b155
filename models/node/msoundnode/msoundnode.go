package msoundnode

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/service"
)

const tableName = "m_sound_node"

// MSoundNode model
type MSoundNode struct {
	ID             int64  `gorm:"column:id"`
	CreateTime     int64  `gorm:"column:create_time"`
	ModifiedTime   int64  `gorm:"column:modified_time"`
	SoundID        int64  `gorm:"column:sound_id"`
	NodeType       int    `gorm:"column:node_type"`
	Attr           int64  `gorm:"column:attr"`
	StayDuration   int64  `gorm:"column:stay_duration"`
	Title          string `gorm:"column:title"`
	Question       string `gorm:"column:question"`
	Option         string `gorm:"column:option"`
	SoundurlUser   string `gorm:"column:soundurl_user"`
	Soundurl       string `gorm:"column:soundurl"`
	Soundurl128    string `gorm:"column:soundurl_128"`
	Soundurl192    string `gorm:"column:soundurl_192"`
	Cover          string `gorm:"column:cover"`
	Duration       int64  `gorm:"column:duration"`
	PayType        int    `gorm:"column:pay_type"`
	ScoreThreshold int    `gorm:"column:score_threshold"`
	ButtonColor    int    `gorm:"column:button_color"`
	ButtonImage    string `gorm:"column:button_image"`
	Checked        int    `gorm:"column:checked"`

	CoverURL string `gorm:"-"`
}

// DB the db instance of MSoundNode model
func (m MSoundNode) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MSoundNode) TableName() string {
	return tableName
}

// AfterFind is a GORM hook for query
func (m *MSoundNode) AfterFind() error {
	if m.Cover != "" {
		m.CoverURL = service.Storage.Parse(m.Cover)
	} else {
		m.CoverURL = service.Storage.Parse(params.URL.DefaultCoverURL)
	}
	return nil
}
