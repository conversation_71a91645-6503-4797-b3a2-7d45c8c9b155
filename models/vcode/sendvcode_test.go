package vcode

import (
	"errors"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestRegionMobile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := RegionMobile("13811111111", DefaultRegion)
	require.NoError(err)
	assert.Equal("8613811111111", r.RegionMobile)
	assert.Equal(86, r.RegionCode)
	assert.Equal("13811111111", r.Mobile)
	assert.Equal(DefaultRegion, r.RegionName)

	_, err = RegionMobile("13855555", DefaultRegion)
	assert.Equal(ErrInvalidMobile, err)
}

func TestMobileNumber_MosaicMobile(t *testing.T) {
	assert := assert.New(t)

	m := MobileNumber{
		Mobile: "13333333333",
	}
	assert.Equal("133******33", m.MosaicMobile())
}

func TestSendSmsScene(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := SendSmsScene(ObjectiveTypeCreateGuild)
	require.NoError(err)
	assert.Equal("create_guild", r)

	r, err = SendSmsScene(ObjectiveTypeSmsLogin)
	require.NoError(err)
	assert.Equal("sms_login", r)

	r, err = SendSmsScene(ObjectiveTypeOperateGuild)
	require.NoError(err)
	assert.Equal("operate_guild", r)

	_, err = SendSmsScene(999)
	assert.Equal(ErrInvalidPostType, err)
}

func TestCheckTimes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mobile := "8613312341234"
	lock := serviceredis.LockMobileVCode1.Format(mobile)
	service.Redis.Del(lock)
	require.NoError(checkTimes(mobile))
	assert.Equal(ErrSendVCodeMoreTimesPerMinute, checkTimes(mobile))
}

func TestCheckCounterVCode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := util.TimeNow().Unix()
	ip := strconv.FormatInt(userID, 10)
	KeyIPSendCount := serviceredis.KeyCounterVCodeIP1.Format(ip)
	KeyUserSendCount := serviceredis.KeyCounterVCodeUID1.Format(userID)

	require.NoError(checkCounterVCode(ip, userID))

	require.NoError(service.Redis.Set(KeyIPSendCount, LimitVCodeIPCount, 5*time.Second).Err())
	assert.Equal(ErrSendVCodeMoreTimesDaily, checkCounterVCode(ip, userID), "超过 IP 次数限制")

	require.NoError(service.Redis.Del(KeyIPSendCount).Err())
	require.NoError(checkCounterVCode(ip, userID))
	require.NoError(service.Redis.Set(KeyUserSendCount, LimitVCodeUserCount, 5*time.Second).Err())
	assert.Equal(ErrSendVCodeMoreTimesDaily, checkCounterVCode(ip, userID), "超过 userID 次数限制")
}

func TestSendSms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock("pushservice://api/sms", func(input interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cleanup()

	p := SendVCodeParam{
		RegionMobile: "8613512341234",
		RegionCode:   86,
		IP:           "127.0.0.1",
		UserID:       0,
		PostType:     ObjectiveTypeCreateGuild,
		Scene:        "create_guild",
	}
	err := p.SendSms()
	require.NoError(err)
	identityInfo, err := service.Redis.HMGet("mobile_8613512341234", FieldVCodeObjective).Result()
	require.NoError(err)
	assert.Equal(strconv.Itoa(ObjectiveTypeCreateGuild), identityInfo[0])
}

func TestIsValidateLimitErr(t *testing.T) {
	assert := assert.New(t)

	// 测试错误不是发送验证码之前的限制时
	assert.False(IsValidateLimitErr(errors.New("test error")))

	// 测试错误是发送验证码之前的限制时
	assert.True(IsValidateLimitErr(ErrSendVCodeMoreTimesPerMinute))
	assert.True(IsValidateLimitErr(ErrSendVCodeMoreTimesDaily))
}
