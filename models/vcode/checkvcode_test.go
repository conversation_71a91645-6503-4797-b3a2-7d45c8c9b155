package vcode

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestCheckIdentifyCode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mobile := "8613211111111"
	key := "mobile_" + mobile
	service.Redis.Del(key)
	redisParam := make(map[string]interface{})
	redisParam[FieldVCode] = "555555"
	redisParam[FieldVCodeCounter] = 0
	redisParam[FieldVCodeObjective] = ObjectiveTypeCreateGuild
	service.Redis.HMSet(key, redisParam)
	// 验证验证码key不存在
	ok, err := CheckIdentifyCode("86132222222222", "555555", ObjectiveTypeCreateGuild)
	require.NoError(err)
	assert.False(ok)
	// 验证错误的验证码
	ok, err = CheckIdentifyCode(mobile, "55555", ObjectiveTypeCreateGuild)
	require.NoError(err)
	assert.False(ok)
	// 验证正确验证码
	ok, err = CheckIdentifyCode(mobile, "555555", ObjectiveTypeCreateGuild)
	require.NoError(err)
	assert.True(ok)
	// 验证次数太多
	redisParam[FieldVCodeCounter] = 11
	service.Redis.HMSet(key, redisParam)
	ok, err = CheckIdentifyCode(mobile, "555555", ObjectiveTypeCreateGuild)
	require.NoError(err)
	assert.False(ok)
}

func TestDelIdentifyCode(t *testing.T) {
	assert := assert.New(t)
	mobile := "8613311111111"
	key := "mobile_" + mobile
	redisParam := make(map[string]interface{})
	redisParam[FieldVCode] = "555555"
	redisParam[FieldVCodeCounter] = 0
	redisParam[FieldVCodeObjective] = ObjectiveTypeCreateGuild
	service.Redis.HMSet(key, redisParam)
	DelIdentifyCode(mobile)
	assert.Equal(int64(0), service.Redis.Exists(key).Val())
}
