package vcode

import (
	"errors"
	"regexp"
	"time"
)

// 大陆手机号正则
var mobileReg = regexp.MustCompile(`^1[3-9]\d{9}$`)

// check code error
var (
	ErrInvalidPostType             = errors.New("invalid postType")
	ErrInvalidMobile               = errors.New("手机号错误")
	ErrSendVCodeMoreTimesPerMinute = errors.New("操作频繁，请稍后再试")
	ErrSendVCodeMoreTimesDaily     = errors.New("您获取验证码次数太多，请明天再来尝试")
)

// post type
// https://info.missevan.com/pages/viewpage.action?pageId=15371761
const (
	ObjectiveTypeCreateGuild  = 14 // 校验验证码的动作，创建直播公会
	ObjectiveTypeSmsLogin     = 16 // 一键登录
	ObjectiveTypeOperateGuild = 18 // 公会相关操作验证码
	ObjectiveTypeDramaRevenue = 19 // 查看剧集收益后台身份认证
)

const (
	// CNRegionNumber 中国大陆电话区号
	CNRegionNumber = "86"
	// DefaultRegion 中国大陆地区
	DefaultRegion = "CN"
)

// KeyMobileVCode1 fields
const (
	// FieldVCodeObjective 验证码目的的 field
	FieldVCodeObjective = "objective"
	// FieldVCodeCounter 已验证验证码次数的 field
	FieldVCodeCounter = "checktimer"
	// FieldVCode 验证码的 field
	FieldVCode = "vcode"
)

const (
	// LimitVCodeCheckTimes 验证码比对次限制次数
	LimitVCodeCheckTimes = 10
	// LimitVCodeIPCount IP 在一定时间内获取验证码的次数限制
	LimitVCodeIPCount = 50
	// LimitVCodeUserCount 用户在一定时间内获取验证码的次数限制
	LimitVCodeUserCount = 50
	// LimitExpire 限制过期时间
	LimitExpire = 12 * time.Hour
)
