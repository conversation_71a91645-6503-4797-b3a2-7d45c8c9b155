package vcode

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// SendVCodeParam param
type SendVCodeParam struct {
	RegionMobile string
	RegionCode   int
	IP           string
	UserID       int64
	PostType     int
	Scene        string
}

// MobileNumber mobile number
type MobileNumber struct {
	RegionMobile string // regionNum + mobile
	RegionCode   int
	Mobile       string
	RegionName   string
}

// RegionMobile returns regionNum + mobile & region code
// TODO: 暂仅支持中国大陆地区手机号
func RegionMobile(mobile, region string) (MobileNumber, error) {
	if region != DefaultRegion || !mobileReg.MatchString(mobile) {
		return MobileNumber{}, ErrInvalidMobile
	}
	regionCode, _ := strconv.Atoi(CNRegionNumber)
	return MobileNumber{
		RegionMobile: CNRegionNumber + mobile,
		RegionCode:   regionCode,
		Mobile:       mobile,
		RegionName:   region,
	}, nil
}

// MosaicMobile 给手机号打码
func (m MobileNumber) MosaicMobile() string {
	return util.MosaicString(m.Mobile, util.MosaicPhoneNumber)
}

// SendSmsScene 获取验证码 scene
func SendSmsScene(postType int) (string, error) {
	switch postType {
	case ObjectiveTypeCreateGuild:
		return "create_guild", nil
	case ObjectiveTypeSmsLogin:
		return "sms_login", nil
	case ObjectiveTypeOperateGuild:
		return "operate_guild", nil
	case ObjectiveTypeDramaRevenue:
		return "drama_revenue", nil
	default:
		return "", ErrInvalidPostType
	}
}

// Validate 发送验证码之前的相关校验
// TODO: 后边 IP 等限制会放到 mpush 服务中
func (p *SendVCodeParam) Validate() (err error) {
	// check lock
	if err = checkTimes(p.RegionMobile); err != nil {
		return err
	}
	if err = checkCounterVCode(p.IP, p.UserID); err != nil {
		return err
	}
	return
}

// IsValidateLimitErr 判断错误是否是发送验证码之前的限制
func IsValidateLimitErr(err error) bool {
	if err == ErrSendVCodeMoreTimesDaily || err == ErrSendVCodeMoreTimesPerMinute {
		return true
	}
	return false
}

// SendSms 发送手机验证码, 需要先进行参数的 Validate
func (p *SendVCodeParam) SendSms() error {
	key := identifyKey(p.RegionMobile)
	identity := util.RandomCode(6)
	redisParam := make(map[string]interface{})
	redisParam[FieldVCode] = identity
	redisParam[FieldVCodeCounter] = 0
	redisParam[FieldVCodeObjective] = p.PostType
	pipe := service.Redis.Pipeline()
	pipe.HMSet(key, redisParam)
	pipe.Expire(key, 10*time.Minute)
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	sms := pushservice.SMS{
		To:         "+" + p.RegionMobile,
		RegionCode: p.RegionCode,
		Scene:      p.Scene,
		Payload:    map[string]interface{}{"code": identity},
	}
	if err = service.PushService.SendSMS(sms); err != nil {
		return err
	}
	return nil
}

// 获取完整的加区号的验证码 key
func identifyKey(regionMobile string) string {
	identifyKey := serviceredis.KeyMobileVCode1.Format(regionMobile)
	return identifyKey
}

// 发送验证码锁
func checkTimes(regionMobile string) error {
	lock := serviceredis.LockMobileVCode1.Format(regionMobile)
	ok, err := service.Redis.SetNX(lock, "1", time.Minute).Result()
	if err != nil {
		return err
	}
	if !ok {
		return ErrSendVCodeMoreTimesPerMinute
	}
	return nil
}

// 验证码次数校验
func checkCounterVCode(ip string, userID int64) error {
	KeyIPSendCount := serviceredis.KeyCounterVCodeIP1.Format(ip)
	pipe := service.Redis.Pipeline()
	pipe.Incr(KeyIPSendCount)
	pipe.Expire(KeyIPSendCount, LimitExpire)
	if userID != 0 {
		KeyUserSendCount := serviceredis.KeyCounterVCodeUID1.Format(userID)
		pipe.Incr(KeyUserSendCount)
		pipe.Expire(KeyUserSendCount, LimitExpire)
	}
	m, err := pipe.Exec()
	if err != nil {
		return err
	}

	if n := m[0].(*redis.IntCmd).Val(); n > LimitVCodeIPCount {
		return ErrSendVCodeMoreTimesDaily
	}
	if userID != 0 {
		n := m[2].(*redis.IntCmd).Val()
		if n > LimitVCodeUserCount {
			return ErrSendVCodeMoreTimesDaily
		}
	}
	return nil
}
