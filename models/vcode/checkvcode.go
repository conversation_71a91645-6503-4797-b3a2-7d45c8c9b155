package vcode

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
)

// CheckIdentifyCode 校验手机验证码
// 没有错误的时候通过布尔值的返回值来判断是否通过验证
func CheckIdentifyCode(regionMobile, vCode string, objectiveType int) (bool, error) {
	vCodeKey := identifyKey(regionMobile)
	res, err := service.Redis.Exists(vCodeKey).Result()
	if err != nil {
		return false, err
	}
	// vCodeKey 不存在
	if res == 0 {
		return false, nil
	}
	// 每比对 check 一次则变量加一，避免用户短时间内多次重复比对而试出验证码，
	// 如果超过指定次数（默认为 10）则删除之前的验证码，需重新获取
	count, err := service.Redis.HIncrBy(vCodeKey, FieldVCodeCounter, 1).Result()
	if err != nil {
		return false, err
	}
	if count > LimitVCodeCheckTimes {
		DelIdentifyCode(regionMobile)
		return false, nil
	}
	identityInfo, err := service.Redis.HMGet(vCodeKey, FieldVCodeObjective, FieldVCode).Result()
	if err != nil {
		return false, err
	}
	// 验证验证码
	if identityInfo[0] != strconv.Itoa(objectiveType) || identityInfo[1] != vCode {
		return false, nil
	}
	return true, nil
}

// DelIdentifyCode 删除使用过的验证码
func DelIdentifyCode(regionMobile string) {
	vCodeKey := identifyKey(regionMobile)
	err := service.Redis.Del(vCodeKey).Err()
	if err != nil {
		logger.Errorf("redis del error: %v", err)
		// PASS
	}
}
