package persona

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// 用户画像（用于 App 及 Web 首页显示）
// NOTICE: 女性画像 ID 需要为奇数
const (
	TypeGeneral    = 1  // 大众
	TypeBoy        = 2  // 普通男
	TypeGirl       = 3  // 普通女
	TypeFujoshi    = 7  // 腐女
	TypeOtome      = 9  // 乙女
	TypeManualGirl = 11 // 人工维护女性画像
	TypeManualBoy  = 12 // 人工维护男性画像
)

// Persona model
type Persona struct {
	ID           int64  `gorm:"column:id;primary_key"`
	EquipID      string `gorm:"column:equip_id"`
	Persona      int    `gorm:"column:persona"`
	UserID       int64  `gorm:"column:user_id"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	BUVID        string `gorm:"column:buvid"`
	Points       string `gorm:"column:points"`

	PointMap map[int]int64 `gorm:"-"`
}

// TableName for current model
func (Persona) TableName() string {
	return "persona"
}

// DB the db instance of MPersona model
func (p Persona) DB() *gorm.DB {
	return service.DB.Table(p.TableName())
}

// AfterFind is a GORM hook for query
func (p *Persona) AfterFind() error {
	if p.Points != "" {
		err := json.Unmarshal([]byte(p.Points), &p.PointMap)
		if err != nil {
			return err
		}
	}
	return nil
}
