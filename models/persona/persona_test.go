package persona

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Persona{}, "id", "equip_id", "persona", "user_id", "create_time", "modified_time", "buvid", "points")
}

func TestPersona_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("persona", Persona{}.TableName())
}

func TestPersona_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &Persona{
		Points: "{\"3\":2333,\"7\":3222}",
	}
	require.NoError(p.AfterFind())
	require.NotEmpty(p.PointMap)
	point, ok := p.PointMap[3]
	require.True(ok)
	assert.EqualValues(2333, point)
}
