package memoteexclusiveelement

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(-1, StatusOffline)
	assert.Equal(0, StatusLocked)
	assert.Equal(1, StatusUnlock)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MEmoteExclusiveElement{}, "id", "create_time", "modified_time", "element_id",
		"element_type", "package_id", "start_time", "end_time", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(More{}, "unlock", "unlock_score", "tip")
}

func TestGetExclusiveEmotes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有专属表情
	res, err := GetExclusiveEmotes(2333, ElementTypeDrama)
	require.NoError(err)
	require.Empty(res)

	// 测试获取专属表情
	res, err = GetExclusiveEmotes(52347, ElementTypeDrama)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(52347, res[0].ElementID)
	assert.EqualValues(2333, res[0].PackageID)
}
