package forbiddenwords

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testType := Type(-999)
	testKey := RedisKey(testType)
	err := service.Redis.SAdd(testKey, "test_member").Err()
	require.NoError(err)
	defer service.Redis.SRem(testKey, "test_member")

	_, ok := localCache.Get(testKey)
	assert.False(ok)

	words, err := GetAll(testType)
	require.NoError(err)

	assert.True(util.HasElem(words, "test_member"))

	cachedWords, ok := localCache.Get(testKey)
	assert.True(ok)
	assert.True(util.HasElem(cachedWords, "test_member"))
}

func TestGetAllRegexp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testType := Type(-999)
	testKey := RedisKey(testType)
	testLocalCacheKey := getLocalCacheKey(testType, true)
	err := service.Redis.SAdd(testKey, "test.*member").Err()
	require.NoError(err)
	defer service.Redis.SRem(testKey, "test.*member")

	_, ok := localCache.Get(testLocalCacheKey)
	assert.False(ok)

	regexps, err := GetAllRegexp(testType)
	require.NoError(err)

	// 验证正则表达式对象是否正确
	found := false
	for _, re := range regexps {
		if re.String() == "(?i)test.*member" {
			found = true
			break
		}
	}
	assert.True(found)

	// 验证缓存中是否存储了正则表达式对象
	cachedRegexps, ok := localCache.Get(testLocalCacheKey)
	require.True(ok)

	regexps, ok = cachedRegexps.([]*regexp.Regexp)
	require.True(ok)

	found = false
	for _, re := range regexps {
		if re.String() == "(?i)test.*member" {
			found = true
			break
		}
	}
	assert.True(found)
}

func TestHasForbiddenWords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试非弹幕评论屏蔽词
	testType := Type(-1000)
	testKey := RedisKey(testType)

	err := service.Redis.SAdd(testKey, "test member").Err()
	require.NoError(err)
	defer service.Redis.SRem(testKey, "test member")

	ok, err := HasForbiddenWords(testType, "Test Members")
	require.NoError(err)
	assert.True(ok)

	// 测试弹幕评论屏蔽词
	commentNoticeType := ForbiddenWordTypeCommentNotice
	commentNoticeKey := RedisKey(commentNoticeType)

	err = service.Redis.SAdd(commentNoticeKey, "测试.*内容").Err()
	require.NoError(err)
	defer service.Redis.SRem(commentNoticeKey, "测试.*内容")

	ok, err = HasForbiddenWords(commentNoticeType, "这是一个测试评论内容")
	require.NoError(err)
	assert.True(ok)

	ok, err = HasForbiddenWords(commentNoticeType, "这是一个测试")
	require.NoError(err)
	assert.False(ok)
}

func TestGetMatchedForbiddenWords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试非弹幕评论屏蔽词
	testType := Type(-1000)
	testKey := RedisKey(testType)

	err := service.Redis.SAdd(testKey, "test member").Err()
	require.NoError(err)
	defer service.Redis.SRem(testKey, "test member")

	ok, matchedWords, err := GetMatchedForbiddenWord(testType, "Test Members")
	require.NoError(err)
	assert.True(ok)
	assert.Contains(matchedWords, "test member")

	// 测试弹幕评论屏蔽词
	commentNoticeType := ForbiddenWordTypeCommentNotice
	commentNoticeKey := RedisKey(commentNoticeType)

	err = service.Redis.SAdd(commentNoticeKey, "测试.*内容").Err()
	require.NoError(err)
	defer service.Redis.SRem(commentNoticeKey, "测试.*内容")

	ok, matchedWords, err = GetMatchedForbiddenWord(commentNoticeType, "这是一个测试评论内容")
	require.NoError(err)
	assert.True(ok)
	assert.Contains(matchedWords, "测试.*内容")

	ok, matchedWords, err = GetMatchedForbiddenWord(commentNoticeType, "这是一个测试")
	require.NoError(err)
	assert.False(ok)
	assert.Empty(matchedWords)
}

func TestRedisKey(t *testing.T) {
	assert.Equal(t, "forbidden_words:0", RedisKey(0))
}

func TestGetLocalCacheKey(t *testing.T) {
	assert.Equal(t, "local_forbidden_words:0", getLocalCacheKey(0, false))
	assert.Equal(t, "local_forbidden_words:0:regexp", getLocalCacheKey(0, true))
}
