package forbiddenwords

import (
	"regexp"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// Type 屏蔽词类型
type Type int

// 屏蔽词类型
const (
	ForbiddenWordTypeSearch                 Type = iota // 搜索
	ForbiddenWordTypeAlbum                              // 音单
	ForbiddenWordTypeHotSearchWords                     // 搜索热词
	ForbiddenWordTypeSound                              // 音频擦边球
	ForbiddenWordTypePMNotify                           // 违规隐藏
	ForbiddenWordTypeDrama                              // 剧集
	ForbiddenWordTypePrivateMessageFakeSend             // 私信
	ForbiddenWordTypeCommentNotice                      // 评论弹幕
)

// RedisKey returns the key name of the specified type of forbidden words
func RedisKey(t Type) string {
	return serviceredis.KeyForbiddenWords1.Format(t)
}

// localCache 返回本地缓存的 key
func getLocalCacheKey(t Type, isRegexp bool) string {
	if isRegexp {
		return keys.LocalKeyForbiddenWords1.Format(t) + ":regexp"
	}
	return keys.LocalKeyForbiddenWords1.Format(t)
}

var localCache = cache.New(5*time.Minute, 10*time.Minute)

// GetAll 返回某类型 t 所有的屏蔽词
func GetAll(t Type) ([]string, error) {
	key := RedisKey(t)
	cachedWords, ok := localCache.Get(key)
	if ok {
		return cachedWords.([]string), nil
	}
	words, err := service.Redis.SMembers(key).Result()
	if err != nil {
		return nil, err
	}
	localCache.Set(key, words, 0)
	return words, nil
}

// GetAllRegexp 返回某类型 t 所有的屏蔽词的正则表达式
func GetAllRegexp(t Type) ([]*regexp.Regexp, error) {
	key := RedisKey(t)
	localCacheKey := getLocalCacheKey(t, true)
	var regexps []*regexp.Regexp
	// 从缓存中获取正则表达式
	if v, ok := localCache.Get(localCacheKey); ok {
		return v.([]*regexp.Regexp), nil
	}
	forbiddenwords, err := service.Redis.SMembers(key).Result()
	if err != nil {
		return nil, err
	}
	regexps = make([]*regexp.Regexp, 0, len(forbiddenwords))
	for _, word := range forbiddenwords {
		re, err := regexp.Compile("(?i)" + word)
		if err != nil {
			logger.WithField("word", word).Error(err)
			continue
		}
		regexps = append(regexps, re)
	}
	localCache.Set(localCacheKey, regexps, 0)
	return regexps, nil
}

// HasForbiddenWords 检查 text 是否包含 t 类型的屏蔽词
func HasForbiddenWords(t Type, text string) (bool, error) {
	text = strings.ToLower(text)
	if t == ForbiddenWordTypeCommentNotice {
		regexps, err := GetAllRegexp(t)
		if err != nil {
			return false, err
		}
		// 匹配正则表达式
		for _, re := range regexps {
			if re.MatchString(text) {
				return true, nil
			}
		}
	} else {
		forbiddenwords, err := GetAll(t)
		if err != nil {
			return false, err
		}
		for _, word := range forbiddenwords {
			if strings.Contains(text, strings.ToLower(word)) {
				return true, nil
			}
		}
	}
	return false, nil
}

// GetMatchedForbiddenWord 检查 text 是否包含 t 类型的屏蔽词，并返回第一个匹配的词语
func GetMatchedForbiddenWord(t Type, text string) (bool, string, error) {
	text = strings.ToLower(text)

	if t == ForbiddenWordTypeCommentNotice {
		regexps, err := GetAllRegexp(t)
		if err != nil {
			return false, "", err
		}
		// 匹配正则表达式
		for _, re := range regexps {
			if re.MatchString(text) {
				pattern := strings.TrimPrefix(re.String(), "(?i)")
				return true, pattern, nil
			}
		}
	} else {
		forbiddenwords, err := GetAll(t)
		if err != nil {
			return false, "", err
		}
		for _, word := range forbiddenwords {
			if strings.Contains(text, strings.ToLower(word)) {
				return true, word, nil
			}
		}
	}

	return false, "", nil
}
