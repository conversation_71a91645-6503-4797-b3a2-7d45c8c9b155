package forbiddenwords

import (
	"encoding/json"
	"regexp"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

const (
	// CheckTypeDramaComment 剧集下评论
	CheckTypeDramaComment = iota + 1
	// CheckTypeDramaDanmaku 剧集下弹幕
	CheckTypeDramaDanmaku
)

// MForbiddenWords model forbiddenWords
type MForbiddenWords struct {
	ID           int64  `gorm:"column:id"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	ElementID    int64  `gorm:"column:element_id"`
	CheckType    int    `gorm:"column:check_type"`
	Words        string `gorm:"column:words"` // 该字段类型实际为 json 类型
}

// TableName returns the table name of forbiddenWords model
func (MForbiddenWords) TableName() string {
	return "m_forbidden_words"
}

const cache60s = time.Minute

func getForbiddenWords(checkType int, elementID int64) ([]*regexp.Regexp, error) {
	var forbiddenWordsRegex []*regexp.Regexp
	cacheKey := keys.LocalKeyForbiddenWords2.Format(elementID, checkType)
	if v, ok := service.Cache5Min.Get(cacheKey); ok {
		forbiddenWordsRegex = v.([]*regexp.Regexp)
	} else {
		var s MForbiddenWords
		err := service.MainDB.Model(MForbiddenWords{}).Where("element_id = ? AND check_type = ?", elementID, checkType).Find(&s).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				service.Cache5Min.SetDefault(cacheKey, []*regexp.Regexp{})
				return []*regexp.Regexp{}, nil
			}
			// 出错缓存空数据一分钟
			service.Cache5Min.Set(cacheKey, []*regexp.Regexp{}, cache60s)
			return []*regexp.Regexp{}, err
		}
		var w []string
		err = json.Unmarshal([]byte(s.Words), &w)
		if err != nil {
			// 出错缓存空数据一分钟
			service.Cache5Min.Set(cacheKey, []*regexp.Regexp{}, cache60s)
			return []*regexp.Regexp{}, err
		}
		// 屏蔽词匹配不区分大小写
		for _, word := range w {
			r, err := regexp.Compile("(?i)" + word)
			if err != nil {
				logger.WithField("word", word).Error(err)
				continue
			}
			forbiddenWordsRegex = append(forbiddenWordsRegex, r)
		}
		service.Cache5Min.SetDefault(cacheKey, forbiddenWordsRegex)
	}

	res := make([]*regexp.Regexp, len(forbiddenWordsRegex))
	copy(res, forbiddenWordsRegex)
	return res, nil
}

// CheckText check message forbidden words
func CheckText(checkType int, elementID int64, text string) bool {
	forbiddenWordsRegex, err := getForbiddenWords(checkType, elementID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"element_id": elementID,
			"check_type": checkType,
		}).Error(err)
		// PASS
		return false
	}

	for _, regex := range forbiddenWordsRegex {
		if regex.MatchString(text) {
			return true
		}
	}
	return false
}
