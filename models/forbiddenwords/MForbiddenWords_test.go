package forbiddenwords

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

const testDramaID = 39588

func TestGetForbiddenWords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取剧集弹幕屏蔽词
	forbiddenWordsRegex, err := getForbiddenWords(CheckTypeDramaDanmaku, testDramaID)
	require.NoError(err)
	assert.NotEmpty(forbiddenWordsRegex)

	// 获取缓存的剧集弹幕屏蔽词
	cacheData, ok := service.Cache5Min.Get(keys.LocalKeyForbiddenWords2.Format(testDramaID, CheckTypeDramaDanmaku))
	require.True(ok)
	forbiddenWordsRegex, ok = cacheData.([]*regexp.Regexp)
	require.True(ok)
	assert.NotEmpty(forbiddenWordsRegex)
}

func TestCheckText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试文本中包含剧集评论屏蔽词
	isShamSend := CheckText(CheckTypeDramaComment, testDramaID, "公费追星")
	assert.True(isShamSend)

	// 删除数据库屏蔽词
	require.NoError(service.MainDB.Table(MForbiddenWords{}.TableName()).
		Where("element_id = ? AND check_type = ?", testDramaID, 1).Delete("").Error)

	// 验证缓存数据是否存在
	_, ok := service.Cache5Min.Get(keys.LocalKeyForbiddenWords2.Format(testDramaID, CheckTypeDramaComment))
	require.True(ok)

	// 测试文本中包含缓存剧集评论屏蔽词
	isShamSend = CheckText(CheckTypeDramaComment, testDramaID, "公费追星")
	assert.True(isShamSend)

	// 测试文本中不包含剧集评论屏蔽词
	isShamSend = CheckText(CheckTypeDramaComment, testDramaID, "你好")
	assert.False(isShamSend)

	// 测试识别屏蔽词忽略大小写
	isShamSend = CheckText(CheckTypeDramaComment, testDramaID, "你好TeSt")
	assert.True(isShamSend)

	// 测试文本中不包含剧集弹幕屏蔽词
	isShamSend = CheckText(CheckTypeDramaDanmaku, testDramaID, "瑾梨")
	assert.False(isShamSend)

	// 测试文本中包含剧集弹幕屏蔽词
	isShamSend = CheckText(CheckTypeDramaDanmaku, testDramaID, "hello")
	assert.True(isShamSend)

	// 测试剧集正则屏蔽词
	isShamSend = CheckText(CheckTypeDramaDanmaku, testDramaID, "嘻嘻golang哈哈")
	assert.True(isShamSend)

	isShamSend = CheckText(CheckTypeDramaDanmaku, testDramaID, "嘻嘻 (哈哈")
	assert.True(isShamSend)
}
