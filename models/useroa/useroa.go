package useroa

import (
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// 绑定状态
const (
	StatusBound  = 1  // 已绑定
	StatusUnbind = -1 // 取消绑定
)

const tableName = "user_oa"

// UserOA OA 账号和用户 ID 关联表
type UserOA struct {
	ID           int64  `gorm:"column:id;primary_key"`
	OAName       string `gorm:"column:oa_name"`
	UserID       int64  `gorm:"column:user_id"`
	Status       int    `gorm:"column:status"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	DeleteTime   int64  `gorm:"column:delete_time"`
}

// TableName table name
func (UserOA) TableName() string {
	return tableName
}

// FindByUserID 通过 UserOA.UserID 获取用户 OA 信息
func FindByUserID(userID int64) (*UserOA, error) {
	userOA := new(UserOA)
	err := service.DB.
		Find(userOA, "user_id = ? AND status = ? AND delete_time = 0", userID, StatusBound).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return userOA, nil
}
