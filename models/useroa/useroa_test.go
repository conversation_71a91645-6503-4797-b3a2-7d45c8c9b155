package useroa

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testUserID = 2

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("user_oa", UserOA{}.TableName())
}

func TestFindByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	u, err := FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	assert.Equal("测试 OA 名称", u.OAName)

	// 测试取消绑定（已解绑）
	nowStamp := goutil.TimeNow().Unix()
	uo := UserOA{Status: StatusUnbind, DeleteTime: nowStamp}
	service.DB.Table(UserOA{}.TableName()).
		Where("user_id = ? AND status = ?", testUserID, StatusBound).Update(&uo)
	empty, err := FindByUserID(testUserID)
	require.NoError(err)
	assert.Nil(empty)

	// 测试不存在时
	empty, err = FindByUserID(0)
	require.NoError(err)
	assert.Nil(empty)
}
