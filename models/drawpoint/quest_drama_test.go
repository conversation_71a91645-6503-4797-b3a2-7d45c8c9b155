package drawpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestFinishQuestSubscribe(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(15646)
	eventID := EventID428
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSubscribe(pc, eventID, userID, now) })
	questField := NewVoiceQuestFieldSubscribe0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	require.NoError(FinishQuestSubscribe(pc, eventID, userID, now))
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestPlayedDuration(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(15646)
	eventID := EventID428
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestPlayedDuration(pc, eventID, userID, 1, now) })
	questField := NewVoiceQuestFieldPlayedDuration0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	require.NoError(FinishQuestPlayedDuration(pc, eventID, userID, 1, now))
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestUserPoint(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(15646)
	eventID := EventID428
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestUserPoint(pc, eventID, userID, now) })
	questField := NewVoiceQuestFieldUserPoint0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	require.NoError(FinishQuestUserPoint(pc, eventID, userID, now))
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestShareEvent(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(15646)
	eventID := EventID428
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestShareEvent(pc, eventID, userID, now) })
	questField := NewVoiceQuestFieldShare0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	require.NoError(FinishQuestShareEvent(pc, eventID, userID, now))
	assertProgress(t, questField, eventID, userID, 1)
}
