package drawpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// EventIDTheatre 盲盒剧场活动 ID
const (
	EventIDTheatreVI int64 = 505 // 盲盒剧场抽奖活动六期：https://info.missevan.com/pages/viewpage.action?pageId=109681361
)

// 盲盒剧场抽奖任务
const (
	TheatreQuestFieldFollowUser0            cache.KeyFormat = "theatre_follow_user"             // 任务：关注剧场官方账号
	TheatreQuestFieldSubscribeOneDrama0     cache.KeyFormat = "theatre_subscribe_one_drama"     // 任务：追剧剧场内任意一部本次上新剧集
	TheatreQuestFieldSubscribeSeveralDrama0 cache.KeyFormat = "theatre_subscribe_several_drama" // 任务：追剧剧场内本次上新剧集达到指定数量
)

// 任务完成触发条件
const (
	TheatreTaskSubscribeDramaNum int64 = 4 // 追剧剧场内本次上新剧集达到指定数量，V6 活动为 4 部
)

// FinishTheatreQuestFollowUser 完成任务 - 关注剧场官方账号
func FinishTheatreQuestFollowUser(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: TheatreQuestFieldFollowUser0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishTheatreQuestSubscribeOneDrama 完成任务 - 追剧剧场内本次上新的任意一部剧集
func FinishTheatreQuestSubscribeOneDrama(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: TheatreQuestFieldSubscribeOneDrama0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishTheatreQuestSubscribeSeveralDrama 完成任务 - 追剧剧场内本次上新的剧集达到指定数量
func FinishTheatreQuestSubscribeSeveralDrama(pc *PointConfig, eventID, userID, dramaID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: TheatreQuestFieldSubscribeSeveralDrama0,
		trigger:          TheatreTaskSubscribeDramaNum,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	// 限制用户订阅剧集每个只能订阅一次
	added, err := qc.AddTheatreSubscribeDramaID(eventID, userID, dramaID)
	if err != nil {
		return err
	}
	if !added {
		return nil
	}

	return qc.finishQuest(1)
}
