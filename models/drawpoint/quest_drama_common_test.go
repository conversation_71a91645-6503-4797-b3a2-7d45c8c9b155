package drawpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func assertDailyProgress(t *testing.T, questFieldTotal, questFieldDaily string, eventID, userID, progressTotal, progressDaily int64) {
	assert := assert.New(t)
	require := require.New(t)

	// 断言更新总进度
	questKeyTotal := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	resultTotal, err := service.Redis.HGet(questKeyTotal, questFieldTotal).Int64()
	if err != nil {
		require.True(serviceredis.IsRedisNil(err))
	}
	assert.Equal(progressTotal, resultTotal)
	// 断言更新当日进度
	questKeyDaily := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	resultDaily, err := service.Redis.HGet(questKeyDaily, questFieldDaily).Int64()
	if err != nil {
		require.True(serviceredis.IsRedisNil(err))
	}
	assert.Equal(progressDaily, resultDaily)
}

func TestFinishQuestSubscribeDramaDaily(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(1)
	eventID := EventID608
	questKeyDaily := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	questKeyTotal := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(questKeyDaily, questKeyTotal).Err()
	require.NoError(err)

	// 活动无关注剧集相关配置
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            EventID608,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("未配置关注剧集任务", func() { _ = FinishQuestSubscribeDramaDaily(pc, eventID, userID, now) })
	questFieldDaily := QuestFieldSubscribeDramaDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, QuestFieldSubscribeDramaDailyTotal0, questFieldDaily, eventID, userID, 0, 0)

	// 积分配置和活动任务不匹配
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            EventID446,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			Tasks: Tasks{
				SubscribeOneDrama: &Task{
					RewardPoint: 3,
					Limit:       7,
					Trigger:     1,
					DailyLimit:  1,
				},
			},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSubscribeDramaDaily(pc, eventID, userID, now) })
	assertDailyProgress(t, QuestFieldSubscribeDramaDailyTotal0, questFieldDaily, eventID, userID, 0, 0)

	// 成功完成日更追剧任务
	pc.EventID = EventID608
	err = FinishQuestSubscribeDramaDaily(pc, eventID, userID, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldSubscribeDramaDailyTotal0, questFieldDaily, eventID, userID, 1, 1)

	// 重复完成日更追剧任务
	err = FinishQuestSubscribeDramaDaily(pc, eventID, userID, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldSubscribeDramaDailyTotal0, questFieldDaily, eventID, userID, 1, 2)
}

func TestFinishQuestShare(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID446
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypeShare,
					RewardPoint: 1,
					Limit:       1,
					Trigger:     1,
				},
			},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestShare(pc, eventID, userID, now) })
	questField := QuestFieldShareEvent0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常完成任务
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypeShare,
					RewardPoint: 1,
					Limit:       1,
					Trigger:     1,
				},
			},
		},
	}
	err = FinishQuestShare(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestShareNormal(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID446
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypeShare,
					RewardPoint: 1,
					Limit:       1,
					Trigger:     1,
				},
			},
		},
	}
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypeShare)
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = finishQuestShareNormal(pc, taskConf, eventID, userID, now) })
	questField := QuestFieldShareEvent0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常完成任务
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypeShare,
					RewardPoint: 1,
					Limit:       1,
					Trigger:     1,
				},
			},
		},
	}
	err = finishQuestShareNormal(pc, taskConf, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestShareDaily(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	userID := int64(1)
	eventID := EventID608
	questKeyDaily := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	questKeyTotal := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKeyDaily, questKeyTotal).Err()
		cancel()
	}()
	err := service.Redis.Del(questKeyDaily, questKeyTotal).Err()
	require.NoError(err)

	// 积分配置和活动任务不匹配
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            EventID446,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypeShare,
					RewardPoint: 3,
					Limit:       7,
					Trigger:     1,
					DailyLimit:  1,
				},
			},
		},
	}
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypeShare)
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = finishQuestShareDaily(pc, taskConf, eventID, userID, now) })
	questFieldDaily := QuestFieldShareEventDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, QuestFieldShareEventDailyTotal0, questFieldDaily, eventID, userID, 0, 0)

	// 成功完成日更分享任务
	pc.EventID = EventID608
	err = finishQuestShareDaily(pc, taskConf, eventID, userID, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldShareEventDailyTotal0, questFieldDaily, eventID, userID, 1, 1)

	// 重复完成日更分享任务
	err = finishQuestShareDaily(pc, taskConf, eventID, userID, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldShareEventDailyTotal0, questFieldDaily, eventID, userID, 1, 2)
}

func TestFinishQuestPlayDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	userID := int64(1)
	eventID := EventID608
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey, finishKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	testDuration := int64(600000)
	testDramaIDs := []int64{52347, 71605, 76557}
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: testDramaIDs,
			TaskList: []Task{
				{
					TaskType:    TaskTypePlay,
					Trigger:     testDuration,
					Limit:       7,
					DailyLimit:  1,
					RewardPoint: 1,
				},
			},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() {
		_ = FinishQuestPlayDuration(pc, eventID, userID, 1, now)
	})
	questFieldDaily := QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 0, 0)

	// 测试日更收听任务没有达到收听时长
	pc.EventID = eventID
	err = FinishQuestPlayDuration(pc, eventID, userID, 1, now)
	require.NoError(err)
	questFieldDaily = QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 0, 1)

	// 测试日更收听任务收听剧集正常完成任务
	err = FinishQuestPlayDuration(pc, eventID, userID, testDuration, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 1, 600001)

	// 测试日更收听任务达到完成次数上限，不再累计完成次数
	err = service.Redis.HSet(finishKey, QuestFieldPlayDurationDailyTotal0, 7).Err()
	require.NoError(err)
	err = FinishQuestPlayDuration(pc, eventID, userID, testDuration, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 7, 600001)

	// 测试非日更收听任务没有达到收听时长
	err = service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)
	pc.DrawTaskConfig.TaskList[0].DailyLimit = 0
	pc.EventID = eventID
	err = FinishQuestPlayDuration(pc, eventID, userID, 1, now)
	require.NoError(err)

	questField := QuestFieldPlayDuration0.Format()
	resultTotal, err := service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(1, resultTotal)

	// 测试非日更收听任务收听剧集正常完成任务
	err = FinishQuestPlayDuration(pc, eventID, userID, testDuration, now)
	require.NoError(err)
	resultTotal, err = service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(testDuration+1, resultTotal)
}

func TestFinishQuestPlayDurationNormal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	userID := int64(1)
	eventID := EventID709
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey, finishKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	testDuration := int64(300000)
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			TaskList: []Task{
				{
					TaskType:    TaskTypePlay,
					Trigger:     testDuration,
					Limit:       1,
					RewardPoint: 1,
				},
			},
		},
	}
	taskConf := &Task{
		TaskType:    TaskTypePlay,
		Trigger:     testDuration,
		Limit:       1,
		RewardPoint: 1,
	}
	err = FinishQuestPlayDurationNormal(pc, taskConf, eventID, userID, 1, now)
	require.NoError(err)

	questField := QuestFieldPlayDuration0.Format()
	resultTotal, err := service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(1, resultTotal)

	// 测试非日更收听任务收听剧集正常完成任务
	err = FinishQuestPlayDurationNormal(pc, taskConf, eventID, userID, testDuration, now)
	require.NoError(err)
	resultTotal, err = service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(testDuration+1, resultTotal)
}

func TestFinishQuestPlayDurationDaily(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	userID := int64(1)
	eventID := EventID608
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey, finishKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	testDuration := int64(600000)
	testDramaIDs := []int64{52347, 71605, 76557}
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: testDramaIDs,
			TaskList: []Task{
				{
					TaskType:    TaskTypePlay,
					Trigger:     testDuration,
					Limit:       7,
					DailyLimit:  1,
					RewardPoint: 1,
				},
			},
		},
	}
	taskConf := &Task{
		TaskType:    TaskTypePlay,
		Trigger:     testDuration,
		Limit:       7,
		DailyLimit:  1,
		RewardPoint: 1,
	}

	// 测试没有达到收听时长
	err = FinishQuestPlayDurationDaily(pc, taskConf, eventID, userID, 1, now)
	require.NoError(err)
	questFieldDaily := QuestFieldPlayDurationDaily1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 0, 1)

	// 测试收听剧集正常完成任务
	err = FinishQuestPlayDurationDaily(pc, taskConf, eventID, userID, testDuration, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 1, 600001)

	// 测试达到完成次数上限，不再累计完成次数
	err = service.Redis.HSet(finishKey, QuestFieldPlayDurationDailyTotal0, 7).Err()
	require.NoError(err)
	err = FinishQuestPlayDurationDaily(pc, taskConf, eventID, userID, testDuration, now)
	require.NoError(err)
	assertDailyProgress(t, QuestFieldPlayDurationDailyTotal0, questFieldDaily, eventID, userID, 7, 600001)
}

func TestFinishQuestBuyOneDrama(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID608
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 3}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	testDramaIDs := []int64{52347, 71605, 76557}
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: testDramaIDs,
			TaskList: []Task{
				{
					TaskType:    TaskTypeBuyOneDrama,
					RewardPoint: 3,
					Limit:       1,
					Trigger:     1,
					DailyLimit:  1,
				},
			},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestBuyOneDrama(pc, eventID, userID, testDramaIDs[0], now) })
	questField := QuestFieldBuySpecifiedDrama1.Format(testDramaIDs[0])
	assertProgress(t, questField, eventID, userID, 0)

	// 测试购买第一部活动剧集正常完成任务
	pc.EventID = eventID
	err = FinishQuestBuyOneDrama(pc, eventID, userID, testDramaIDs[0], now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 测试购买第三部活动剧集正常完成任务
	err = FinishQuestBuyOneDrama(pc, eventID, userID, testDramaIDs[2], now)
	require.NoError(err)
	questField = QuestFieldBuySpecifiedDrama1.Format(testDramaIDs[2])
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestSubscribeOneDrama(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID591
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	testDramaIDs := []int64{52347, 71605, 76557}
	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: testDramaIDs,
			TaskList: []Task{
				{
					TaskType:    TaskTypeSubscribeOneDrama,
					RewardPoint: 1,
					Limit:       1,
					Trigger:     1,
					DailyLimit:  1,
				},
			},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSubscribeOneDrama(pc, eventID, userID, testDramaIDs[0], now) })
	questField := QuestFieldSubscribeSpecifiedDrama1.Format(testDramaIDs[0])
	assertProgress(t, questField, eventID, userID, 0)

	// 测试关注第一部活动剧集正常完成任务
	pc.EventID = eventID
	err = FinishQuestSubscribeOneDrama(pc, eventID, userID, testDramaIDs[0], now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 测试关注第三部活动剧集正常完成任务
	err = FinishQuestSubscribeOneDrama(pc, eventID, userID, testDramaIDs[2], now)
	require.NoError(err)
	questField = QuestFieldSubscribeSpecifiedDrama1.Format(testDramaIDs[2])
	assertProgress(t, questField, eventID, userID, 1)
}
