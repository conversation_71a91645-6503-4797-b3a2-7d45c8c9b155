package drawpoint

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	questTypeNormal = iota
	questTypeDaily  // 日常
	questTypeWeekly // 周常
)

type questConfig struct {
	questType        int             // 任务类型
	questFieldFormat cache.KeyFormat // 任务 field 名称
	trigger          int64           // 触发条件
	repeatLimit      int64           // 任务能重复完成的次数
	questReward      int64           // 任务积分
	finishField      string          // 任务完成次数 field 名称（任务类型为日常和周常时配置，用于共享日常和周常任务的完成次数）
	maxFinishNum     int64           // 完成次数上限（任务类型为日常和周常时配置）

	pc *PointConfig // 抽奖配置

	userID int64
	now    time.Time // 根据活动偏移后的“当前时间”

	callback func() error // 增加积分成功后执行的回调函数
}

func (qc questConfig) questsKey() string {
	// WORKAROUND: 猫猫星球抽奖任务特殊处理
	if qc.pc.EventID == EventIDMaoMaoStar {
		return keys.KeyMaoMaoPointUserID1.Format(qc.userID)
	}
	return keys.KeyDrawPointUserQuests2.Format(qc.pc.EventID, qc.userID)
}

// finishKey 积分任务完成次数 key
func (qc questConfig) finishKey() string {
	return keys.KeyEventPointTaskInfo2.Format(qc.pc.EventID, qc.userID)
}

// TODO: 支持传入剧集 ID 等信息，并根据传入信息格式化普通任务的 field
func (qc questConfig) questField() string {
	switch qc.questType {
	case questTypeNormal:
		return qc.questFieldFormat.Format()
	case questTypeDaily:
		return qc.questFieldFormat.Format(qc.now.Format(util.TimeFormatYMDWithNoSpace))
	case questTypeWeekly:
		st := util.BeginningOfWeek(qc.now)
		return qc.questFieldFormat.Format(st.Format(util.TimeFormatYMDWithNoSpace))
	default:
		panic(fmt.Sprintf("不支持的任务类型：%d", qc.questType))
	}
}

// doTheQuest 进行任务，返回任务进度
func (qc questConfig) doTheQuest(num int64, expire time.Duration) (int64, error) {
	pipe := service.Redis.TxPipeline()
	key := qc.questsKey()
	cmd := pipe.HIncrBy(key, qc.questField(), num)
	pipe.Expire(key, expire)
	_, err := pipe.Exec()
	if err != nil {
		return 0, err
	}
	return cmd.Val(), nil
}

// receiveReward 领取任务报酬
func (qc questConfig) receiveReward(before, after int64) int64 {
	limit := qc.repeatLimit * qc.trigger
	if before >= limit {
		return 0
	}
	if after > limit {
		after = limit
	}

	return (after/qc.trigger - before/qc.trigger) * qc.questReward
}

func (qc questConfig) finishQuest(num int64) error {
	expireDuration := qc.pc.ExpireDuration()
	after, err := qc.doTheQuest(num, expireDuration)
	if err != nil {
		return err
	}
	point := qc.receiveReward(after-num, after)
	if point == 0 {
		return nil
	}
	_, err = Param{
		EventID:     qc.pc.EventID,
		UserID:      qc.userID,
		CurrentTime: qc.now.Unix(),
		DailyUpdate: qc.pc.DrawPointDailyUpdate,
	}.AddDrawPoint(point, expireDuration)
	if err != nil {
		return err
	}
	if qc.callback != nil {
		return qc.callback()
	}
	return nil
}

// AddTheatreSubscribeDramaID 记录盲盒剧场订阅剧集 ID
func (qc *questConfig) AddTheatreSubscribeDramaID(eventID, userID, dramaID int64) (bool, error) {
	key := KeyTheatreSubscribeDramaIDs(eventID, userID)
	pipe := service.Redis.TxPipeline()
	cmdAdd := pipe.SAdd(key, dramaID)
	pipe.Expire(key, qc.pc.ExpireDuration())
	if _, err := pipe.Exec(); err != nil {
		return false, err
	}
	if cmdAdd.Val() == 0 {
		return false, nil
	}

	return true, nil
}

// KeyTheatreSubscribeDramaIDs 盲盒剧场活动订阅剧集 ID
func KeyTheatreSubscribeDramaIDs(eventID, userID int64) string {
	return keys.KeyTheatreSubscribeDramaIDs2.Format(eventID, userID)
}

func matchQuest(pc *PointConfig, eventID int64) *PointConfig {
	if pc.EventID != eventID {
		panic("积分配置和活动任务不匹配")
	}
	return pc
}

// finishDailyQuest 完成每日积分任务
func (qc questConfig) finishDailyQuest(num int64) error {
	ok, err := qc.isFinishedQuest()
	if err != nil {
		return err
	}
	if ok {
		return nil
	}
	expireDuration := qc.pc.ExpireDuration()
	after, err := qc.doTheQuest(num, expireDuration)
	if err != nil {
		return err
	}
	point := qc.receiveReward(after-num, after)
	if point == 0 {
		return nil
	}
	ok, err = qc.addFinishNum(expireDuration)
	if err != nil {
		return err
	}
	if !ok {
		return nil
	}
	_, err = Param{
		EventID:     qc.pc.EventID,
		UserID:      qc.userID,
		CurrentTime: qc.now.Unix(),
		DailyUpdate: qc.pc.DrawPointDailyUpdate,
	}.AddDrawPoint(point, expireDuration)
	if err != nil {
		return err
	}
	if qc.callback != nil {
		return qc.callback()
	}
	return nil
}

// isFinishedQuest 是否完成积分任务
func (qc questConfig) isFinishedQuest() (bool, error) {
	key := qc.finishKey()
	finishNum, err := service.Redis.HGet(key, qc.finishField).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return false, err
	}
	if finishNum >= qc.maxFinishNum {
		return true, nil
	}
	return false, nil
}

// addFinishNum 累计完成次数
func (qc questConfig) addFinishNum(expire time.Duration) (bool, error) {
	pipe := service.Redis.TxPipeline()
	key := qc.finishKey()
	cmd := pipe.HIncrBy(key, qc.finishField, 1)
	pipe.Expire(key, expire)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	if cmd.Val() > qc.maxFinishNum {
		// 因完成次数不会影响展示，暂时不做扣除完成次数处理
		return false, nil
	}
	return true, nil
}
