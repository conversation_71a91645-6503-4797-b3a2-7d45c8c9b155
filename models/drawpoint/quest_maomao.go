package drawpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// EventIDMaoMaoStar 猫猫星球抽奖活动
const EventIDMaoMaoStar int64 = 205

// 712 猫猫星球抽奖任务
const (
	// 每日任务：xxx_20220621
	MaoMaoQuestFieldUserPoint1  cache.KeyFormat = "user_point_%s"  // 每日任务：对任意音频投食小鱼干
	MaoMaoQuestFieldAddDm1      cache.KeyFormat = "add_dm_%s"      // 每日任务：对任意音频发弹幕 1 次
	MaoMaoQuestFieldShareSound1 cache.KeyFormat = "share_sound_%s" // 每日任务：分享 3 个音频到社交网站

	MaoMaoQuestFieldShareEvent       cache.KeyFormat = "share_event_199"             // 任务：分享 712 主会场
	MaoMaoQuestFieldShareLive        cache.KeyFormat = "share_maoer_live"            // 任务：分享猫耳LIVE直播间
	maoMaoQuestFieldDramaConsumption cache.KeyFormat = "drama_consume_total_balance" // 任务：剧集消费每满 300 钻
)

// FinishMaoMaoQuestUserPoint 完成每日任务-对任意音频投食小鱼干
func FinishMaoMaoQuestUserPoint(pc *PointConfig, userID int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: MaoMaoQuestFieldUserPoint1,
		trigger:          1,
		repeatLimit:      1,
		questReward:      25,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(1)
}

// FinishMaoMaoQuestAddDm 完成每日任务-对任意音频发弹幕 1 次
func FinishMaoMaoQuestAddDm(pc *PointConfig, userID int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: MaoMaoQuestFieldAddDm1,
		trigger:          1,
		repeatLimit:      1,
		questReward:      25,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(1)
}

// FinishMaoMaoQuestShareSound 完成每日任务-分享 3 个音频到社交网站
func FinishMaoMaoQuestShareSound(pc *PointConfig, userID int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: MaoMaoQuestFieldShareSound1,
		trigger:          3,
		repeatLimit:      1,
		questReward:      30,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(1)
}

// FinishMaoMaoQuestShareEvent 完成任务-分享 712 主会场
func FinishMaoMaoQuestShareEvent(pc *PointConfig, userID int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: MaoMaoQuestFieldShareEvent,
		trigger:          1,
		repeatLimit:      1,
		questReward:      50,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(1)
}

// FinishMaoMaoQuestShareLive 完成任务-分享猫耳LIVE直播间
func FinishMaoMaoQuestShareLive(pc *PointConfig, userID int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: MaoMaoQuestFieldShareLive,
		trigger:          1,
		repeatLimit:      1,
		questReward:      50,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(1)
}

// FinishMaoMaoQuestDramaConsumption 完成任务-剧集消费每满 300 钻
// NOTICE: missevan-go 上并没有直接完成任务，在 missevan-app 完成了任务
func FinishMaoMaoQuestDramaConsumption(pc *PointConfig, userID int64, num int64, now time.Time) (int64, error) {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: maoMaoQuestFieldDramaConsumption,
		trigger:          300,
		repeatLimit:      20,
		questReward:      100,

		pc: matchQuest(pc, EventIDMaoMaoStar),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return 0, qc.finishQuest(num)
}
