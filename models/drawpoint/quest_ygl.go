package drawpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

const (
	// EventID366 摇光录分享活动 ID https://info.missevan.com/pages/viewpage.action?pageId=97888860
	EventID366 int64 = 366
	// EventID400 摇光录下载抽奖活动 ID  https://info.missevan.com/pages/viewpage.action?pageId=97895777
	EventID400 int64 = 400
)

// 摇光录抽奖任务
const (
	YglQuestFieldShare0      cache.KeyFormat = "ygl_event_share" // 任务：分享摇光录活动
	YglQuestFieldFollowUser0 cache.KeyFormat = "ygl_follow_user" // 任务：关注摇光录站内账号
)

// FinishYglQuestShare 完成积分任务 - 分享摇光录活动一次
func FinishYglQuestShare(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: YglQuestFieldShare0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      2,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishYglQuestFollowUser 完成积分任务 - 关注摇光录站内账号
func FinishYglQuestFollowUser(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: YglQuestFieldFollowUser0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}
