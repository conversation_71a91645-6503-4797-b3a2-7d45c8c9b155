package drawpoint

import (
	"errors"
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// ErrDramaNotMatched 活动剧集异常
var ErrDramaNotMatched = errors.New("drama not matched")

// EventID446 活动 ID
const EventID446 int64 = 446 // 《放学等我》剧集活动 ID https://www.tapd.bilibili.co/35612194/prong/stories/view/1135612194003089335

// 《放学等我》第一季和第二季剧集 ID
const (
	FirstSeasonDramaID  int64 = 64911
	SecondSeasonDramaID int64 = 68837
)

// FangXueTaskMinPlayedDuration 每日完成收听任务最少播放时长（10 分钟），单位：毫秒
const FangXueTaskMinPlayedDuration int64 = 600000

// FangXueTaskMaxPlayedNum 收听任务完成次数上限
const FangXueTaskMaxPlayedNum = 2

// 《放学等我》剧集抽奖活动任务
const (
	QuestFieldFirstSeasonSubscribed0      cache.KeyFormat = "first_season_subscribed"          // 任务：追剧第一季剧集
	QuestFieldSecondSeasonSubscribed0     cache.KeyFormat = "second_season_subscribed"         // 任务：追剧第二季剧集
	QuestFieldFirstSeasonBought0          cache.KeyFormat = "first_season_bought"              // 任务：购买第一季剧集
	QuestFieldSecondSeasonBought0         cache.KeyFormat = "second_season_bought"             // 任务：购买第二季剧集
	QuestFieldFirstSeasonPlayedDuration1  cache.KeyFormat = "first_season_played_duration_%s"  // 任务：每日累计收听一定时长第一季剧集下音频
	QuestFieldSecondSeasonPlayedDuration1 cache.KeyFormat = "second_season_played_duration_%s" // 任务：每日累计收听一定时长第二季剧集下音频
	QuestFieldShareEvent0                 cache.KeyFormat = "share_event"                      // 任务：分享活动页面

	FirstSeasonPlayedFinishNum  string = "first_season_played_finish_num"  // 完成次数：每日累计收听一定时长第一季剧集下音频
	SecondSeasonPlayedFinishNum string = "second_season_played_finish_num" // 完成次数：每日累计收听一定时长第二季剧集下音频
)

// SubscribeQuestFieldList 追剧任务列表
var SubscribeQuestFieldList = []cache.KeyFormat{QuestFieldFirstSeasonSubscribed0, QuestFieldSecondSeasonSubscribed0}

// BoughtQuestFieldList 购买任务列表
var BoughtQuestFieldList = []cache.KeyFormat{QuestFieldFirstSeasonBought0, QuestFieldSecondSeasonBought0}

// PlayedQuestFieldList 收听任务列表
var PlayedQuestFieldList = []cache.KeyFormat{QuestFieldFirstSeasonPlayedDuration1, QuestFieldSecondSeasonPlayedDuration1}

// PlayedQuestFinishedFieldList 收听任务完成次数列表
var PlayedQuestFinishedFieldList = []string{FirstSeasonPlayedFinishNum, SecondSeasonPlayedFinishNum}

// FinishQuestSeasonDramaSubscribe 完成任务 - 追剧活动剧集
func FinishQuestSeasonDramaSubscribe(pc *PointConfig, eventID, userID, dramaID int64, now time.Time) error {
	var questFieldFormat cache.KeyFormat
	for i, v := range pc.DrawTaskConfig.DramaIDs {
		if i >= len(SubscribeQuestFieldList) {
			return ErrDramaNotMatched
		}
		if v == dramaID {
			questFieldFormat = SubscribeQuestFieldList[i]
		}
	}

	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: questFieldFormat,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishQuestSeasonDramaBought 完成任务 - 购买活动剧集
func FinishQuestSeasonDramaBought(pc *PointConfig, eventID, userID, dramaID int64, now time.Time) error {
	var questFieldFormat cache.KeyFormat
	for i, v := range pc.DrawTaskConfig.DramaIDs {
		if i >= len(BoughtQuestFieldList) {
			return ErrDramaNotMatched
		}
		if v == dramaID {
			questFieldFormat = BoughtQuestFieldList[i]
		}
	}

	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: questFieldFormat,
		trigger:          1,
		repeatLimit:      1,
		questReward:      2,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishQuestSeasonDramaPlayedDuration 完成任务 - 每日收听活动剧集音频达到 10 分钟
func FinishQuestSeasonDramaPlayedDuration(pc *PointConfig, eventID, userID, dramaID, playedDuration int64, now time.Time) error {
	var questFieldFormat cache.KeyFormat
	var finishNumField string
	for i, v := range pc.DrawTaskConfig.DramaIDs {
		if i >= len(PlayedQuestFieldList) || i >= len(PlayedQuestFinishedFieldList) {
			return ErrDramaNotMatched
		}
		if v == dramaID {
			questFieldFormat = PlayedQuestFieldList[i]
			finishNumField = PlayedQuestFinishedFieldList[i]
		}
	}

	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: questFieldFormat,
		trigger:          FangXueTaskMinPlayedDuration,
		repeatLimit:      1,
		questReward:      1,
		finishField:      finishNumField,
		maxFinishNum:     FangXueTaskMaxPlayedNum,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishDailyQuest(playedDuration)
}
