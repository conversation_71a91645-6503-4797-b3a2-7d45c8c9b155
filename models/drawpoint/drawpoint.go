package drawpoint

import (
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 活动任务类型
const (
	TaskTypeBuyOneDrama           = "buy_one_drama"           // 购买一部指定剧集
	TaskTypeBuyAnyOneDrama        = "buy_any_one_drama"       // 购买任意一部剧集
	TaskTypeBuySeveralDrama       = "buy_several_drama"       // 购买任意多部剧集
	TaskTypeSubscribeOneDrama     = "subscribe_one_drama"     // 追剧一部指定剧集
	TaskTypeSubscribeAnyOneDrama  = "subscribe_any_one_drama" // 追剧任意一部剧集
	TaskTypeSubscribeSeveralDrama = "subscribe_several_drama" // 追剧任意多部剧集
	TaskTypePlay                  = "play"                    // 播放剧集下音频达指定时长
	TaskTypeShare                 = "share"                   // 分享活动
	TaskTypeCheckIn               = "check_in"                // 每日打卡
	TaskTypeSubscribe             = "subscribe"               // 订阅活动
)

// Param 抽奖积分相关参数
type Param struct {
	EventID     int64
	UserID      int64
	CurrentTime int64
	DailyUpdate bool // 积分是否是每日更新
}

// DrawPoint 获得抽奖积分
// TODO: 因活动抽奖模块已迁移至 minigame 项目，需要提 PR 删除活动抽奖相关处理
func (p Param) DrawPoint() (int64, error) {
	key := p.drawPointKey()
	point, err := service.Redis.ZScore(key, strconv.FormatInt(p.UserID, 10)).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	return int64(point), nil
}

// AddDrawPoint 增加抽奖积分
func (p Param) AddDrawPoint(point int64, expireDuration time.Duration) (int64, error) {
	res, err := userapi.UpdateEventDrawPoint(p.EventID, p.UserID, point)
	if err != nil {
		return 0, err
	}
	return res.Point, nil
}

// MinusDrawPoint 减抽奖积分
func (p Param) MinusDrawPoint(point int64, expireDuration time.Duration) (int64, error) {
	point = -point
	res, err := userapi.UpdateEventDrawPoint(p.EventID, p.UserID, point)
	if err != nil {
		return 0, err
	}
	return res.Point, nil
}

func (p Param) drawPointKey() string {
	var key string
	if p.DailyUpdate {
		date := time.Unix(p.CurrentTime, 0).Format("20060102")
		key = serviceredis.KeyActivityDailyUserDrawPointEvent2.Format(p.EventID, date)
	} else {
		key = serviceredis.KeyActivityUserDrawPointEvent1.Format(p.EventID)
	}
	return key
}

// PointConfig 可添加抽奖积分的开始时间和结束时间
type PointConfig struct {
	DrawPointStartTime   int64 `json:"draw_point_start_time"`
	DrawPointEndTime     int64 `json:"draw_point_end_time"`
	DrawPointDailyUpdate bool  `json:"draw_point_daily_update,omitempty"`
	TimeOffset           int64 `json:"time_offset,omitempty"`

	EventID      int64 `json:"-"` // 活动 ID
	EventEndTime int64 `json:"-"` // 活动结束时间

	DrawTaskConfig DrawTaskConfig `json:"draw_task_config"` // 抽奖任务配置
}

// DrawTaskConfig 抽奖任务配置
type DrawTaskConfig struct {
	// TODO: 428 活动结束后，删除 DramaID 属性
	DramaID       int64   `json:"drama_id"`        // 参与积分任务的剧集 ID
	DramaIDs      []int64 `json:"drama_ids"`       // 参与积分任务的剧集 IDs
	FollowUserIDs []int64 `json:"follow_user_ids"` // 参演用户 IDs
	SoundIDs      []int64 `json:"sound_ids"`       // 参与积分任务的音频 IDs
	Tasks         Tasks   `json:"tasks"`           // 任务配置 Deprecated: 2024-09-08 线上 617 吞海剧集活动结束后删除此配置
	TaskList      []Task  `json:"task_list"`       // 任务配置
}

// Tasks 抽奖任务类型配置
type Tasks struct {
	// TODO: 目前只是列出可能的类型，仅部分支持配置，后续会进行完善
	BuyOneDrama           *Task `json:"buy_one_drama"`           // 购买一部剧集，固定为活动期间仅能完成一次的任务，当前支持配置可获得的积分、活动剧集
	BuySeveralDrama       *Task `json:"buy_several_drama"`       // 购买多部剧集，当前不支持配置
	SubscribeOneDrama     *Task `json:"subscribe_one_drama"`     // 追剧一部剧集，固定为活动期间仅能完成一次的任务，当前支持配置可获得的积分、活动剧集
	SubscribeSeveralDrama *Task `json:"subscribe_several_drama"` // 追剧多部剧集，当前不支持配置
	PlayDramaDuration     *Task `json:"play_drama_duration"`     // 播放剧集达到指定时长，固定为日更任务，当前支持配置任务可获得积分、活动期间可完成次数、每天可完成次数、目标播放时长
	ShareEvent            *Task `json:"share_event"`             // 分享活动页面，固定为非日更任务，当前支持配置任务可获得积分、活动期间可完成次数
}

// Task 单个任务配置
type Task struct {
	TaskType    string `json:"task_type"`             // 任务类型
	RewardPoint int64  `json:"reward_point"`          // 完成任务可获得积分
	Limit       int64  `json:"limit"`                 // 活动期间可重复完成次数
	Trigger     int64  `json:"trigger"`               // 触发加积分的目标值，该目标值根据任务类型不同含义也会有变化，如对于收听时长任务，该值为触发加积分的最低收听时长（单位：毫秒）
	DailyLimit  int64  `json:"daily_limit,omitempty"` // 单日可重复完成次数，仅在日更任务中配置
}

// NowUnix now unix
func (pc *PointConfig) NowUnix() int64 {
	return util.TimeNow().Unix() + pc.TimeOffset
}

// ApplyTimeOffset 增加时间偏移
func (pc *PointConfig) ApplyTimeOffset(now time.Time) time.Time {
	if pc.TimeOffset == 0 {
		return now
	}
	return now.Add(time.Duration(pc.TimeOffset) * time.Second)
}

// InTimeRange 判断 now 时间是否在在抽奖积分增加时间范围内
func (pc *PointConfig) InTimeRange(now time.Time) bool {
	nowUnix := pc.ApplyTimeOffset(now).Unix()
	return nowUnix >= pc.DrawPointStartTime && nowUnix < pc.DrawPointEndTime
}

// ExpireDuration 积分过期时间
func (pc *PointConfig) ExpireDuration() time.Duration {
	if pc.EventEndTime <= 0 {
		panic("活动结束时间异常")
	}
	return time.Duration(pc.EventEndTime+30*util.SecondOneDay-util.TimeNow().Unix()) * time.Second
}

// IsMatchEventDrama 是否是活动剧集
func (pc *PointConfig) IsMatchEventDrama(dramaID int64) bool {
	if dramaID == 0 {
		return false
	}
	if len(pc.DrawTaskConfig.DramaIDs) == 0 {
		// 不配置 pc.DrawTaskConfig.DramaIDs 时表示全平台剧集均可参与该活动
		return true
	}
	return util.HasElem(pc.DrawTaskConfig.DramaIDs, dramaID)
}

// FindPointConfig 查询活动增加积分配置
// NOTICE: 不根据时间判断合理性，因为具体业务（databus）的时间有一定特殊性
func FindPointConfig(eventID int64) (*PointConfig, error) {
	key := keys.LocalKeyDrawPointConfig1.Format(eventID)
	v, ok := service.Cache5Min.Get(key)
	if ok {
		if v == nil {
			return nil, nil
		}
		res := *v.(*PointConfig)
		return &res, nil
	}
	var pc PointConfig
	s, err := mevent.FindSimpleWithExtendedFields(eventID, &pc)
	if err != nil {
		return nil, err
	}
	if s == nil {
		service.Cache5Min.SetDefault(key, nil)
		return nil, nil
	}

	pc.EventID = s.ID
	pc.EventEndTime = s.EndTime
	service.Cache5Min.SetDefault(key, &pc)
	res := pc
	return &res, nil
}

// GetTaskConfig 获取当前任务列表中指定任务类型的配置
func (d *DrawTaskConfig) GetTaskConfig(taskType string) *Task {
	for _, task := range d.TaskList {
		if task.TaskType == taskType {
			return &task
		}
	}
	return nil
}
