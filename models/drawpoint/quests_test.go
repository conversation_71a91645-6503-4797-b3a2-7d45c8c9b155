package drawpoint

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestQuestConfigQuestsKey(t *testing.T) {
	assert := assert.New(t)

	qc := questConfig{
		pc:     &PointConfig{EventID: 205},
		userID: 12,
	}
	assert.Equal("maomao_point:user_id:12", qc.questsKey())
	qc.pc.EventID = 204
	assert.Equal("draw_point:quests:event_id:204:user_id:12", qc.questsKey())
}

func TestQuestConfigQuestField(t *testing.T) {
	assert := assert.New(t)

	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: "normal",
		now:              time.Date(2022, 06, 29, 0, 0, 0, 0, time.Local), // 周三
	}
	assert.Equal("normal", qc.questField())
	qc.questType = questTypeDaily
	qc.questFieldFormat = "daily_%s"
	assert.Equal("daily_20220629", qc.questField())
	qc.questType = questTypeWeekly
	qc.questFieldFormat = "weekly_%s"
	assert.Equal("weekly_20220627", qc.questField())
	qc.questType = -1
	assert.PanicsWithValue("不支持的任务类型：-1", func() { qc.questField() })
}

func TestQuestConfigDoTheQuest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	userID := int64(1234)
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: "user_point_%s",
		trigger:          1,
		repeatLimit:      1,
		questReward:      25,

		pc: &PointConfig{EventID: 204},

		userID: userID,
		now:    now,
	}
	num1, err := qc.doTheQuest(1, 10*time.Second)
	require.NoError(err)
	num2, err := qc.doTheQuest(1, 10*time.Second)
	require.NoError(err)
	assert.EqualValues(1, num2-num1)
}

func TestQuestConfigReceiveReward(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	userID := int64(1234)

	// 消费 300 钻的任务
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: "spend",
		trigger:          300,
		repeatLimit:      20,
		questReward:      100,

		userID: userID,
		now:    now,
	}

	assert.EqualValues(2000, qc.receiveReward(299, 300*21))
	assert.Zero(qc.receiveReward(300*20, 300*22))

	// 对任意音频发弹幕 1 次
	qc = questConfig{
		questType:        questTypeDaily,
		questFieldFormat: "add_dm_%s",
		trigger:          1,
		repeatLimit:      1,
		questReward:      25,

		userID: userID,
		now:    now,
	}
	assert.EqualValues(25, qc.receiveReward(0, 1))
	assert.EqualValues(0, qc.receiveReward(1, 2))
}

func TestQuestConfigFinishQuest(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	userID := int64(1234)
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: "test_quest",
		trigger:          10,
		repeatLimit:      3,
		questReward:      25,

		pc: &PointConfig{
			EventID:      196,
			EventEndTime: now.Unix() + 10,
		},

		userID: userID,
		now:    now,
	}
	key := qc.questsKey()
	require.NoError(service.Redis.Del(key).Err())
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()

	err := qc.finishQuest(1)
	require.NoError(err)

	// 测试回调函数出错
	qc.callback = func() error {
		return errors.New("callback error")
	}
	err = qc.finishQuest(99999999)
	require.Errorf(err, "callback error")
}

func TestQuestConfig_finishDailyQuest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	userID := int64(1)
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: "test_quest",
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,
		finishField:      "test_quest_finish",
		maxFinishNum:     2,

		pc: &PointConfig{
			EventID:      1,
			EventEndTime: now.Unix() + 10,
		},

		userID: userID,
		now:    now,
	}
	questKey := qc.questsKey()
	finishKey := qc.finishKey()
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer cancel()
	// 测试每天正常完成任务
	err = qc.finishDailyQuest(1)
	require.NoError(err)
	// 断言任务完成详情和完成次数
	info, err := service.Redis.HGet(questKey, qc.questField()).Int64()
	require.NoError(err)
	assert.EqualValues(1, info)
	finishNum, err := service.Redis.HGet(finishKey, qc.finishField).Int64()
	require.NoError(err)
	assert.EqualValues(1, finishNum)

	// 测试每天重复完成任务
	err = qc.finishDailyQuest(1)
	require.NoError(err)
	// 断言任务完成详情和完成次数
	info, err = service.Redis.HGet(questKey, qc.questField()).Int64()
	require.NoError(err)
	assert.EqualValues(2, info)
	finishNum, err = service.Redis.HGet(finishKey, qc.finishField).Int64()
	require.NoError(err)
	assert.EqualValues(1, finishNum)

	// 测试达到完成次数上限
	qc.now = now.AddDate(0, 0, 1)
	err = qc.finishDailyQuest(1)
	require.NoError(err)
	// 断言任务完成详情和完成次数
	info, err = service.Redis.HGet(questKey, qc.questField()).Int64()
	require.NoError(err)
	assert.EqualValues(1, info)
	finishNum, err = service.Redis.HGet(finishKey, qc.finishField).Int64()
	require.NoError(err)
	assert.EqualValues(2, finishNum)

	// 测试达到完成次数上限后重复完成任务
	qc.now = now.AddDate(0, 0, 2)
	err = qc.finishDailyQuest(1)
	require.NoError(err)
	// 断言任务完成详情和完成次数
	_, err = service.Redis.HGet(questKey, qc.questField()).Int64()
	require.True(serviceredis.IsRedisNil(err))
	finishNum, err = service.Redis.HGet(finishKey, qc.finishField).Int64()
	require.NoError(err)
	assert.EqualValues(2, finishNum)
}

func TestQuestConfig_isFinishedQuest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1)
	qc := questConfig{
		finishField:  "test_quest_finish",
		maxFinishNum: 1,

		pc: &PointConfig{EventID: 1},

		userID: userID,
	}
	finishKey := qc.finishKey()
	err := service.Redis.Del(finishKey).Err()
	require.NoError(err)

	// 测试任务未完成
	ok, err := qc.isFinishedQuest()
	require.NoError(err)
	assert.False(ok)

	// 测试任务已完成
	err = service.Redis.HSet(finishKey, qc.finishField, 1).Err()
	require.NoError(err)
	ok, err = qc.isFinishedQuest()
	require.NoError(err)
	assert.True(ok)
}

func TestQuestConfig_addFinishNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1)
	qc := questConfig{
		finishField:  "test_quest_finish",
		maxFinishNum: 1,

		pc: &PointConfig{EventID: 1},

		userID: userID,
	}
	finishKey := qc.finishKey()
	err := service.Redis.Del(finishKey).Err()
	require.NoError(err)

	// 测试正常累计
	ok, err := qc.addFinishNum(10 * time.Second)
	require.NoError(err)
	assert.True(ok)

	// 测试超过完成次数上限
	ok, err = qc.addFinishNum(10 * time.Second)
	require.NoError(err)
	assert.False(ok)
}

func TestQuestConfig_AddTheatreSubscribeDramaID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testEventID := int64(407)
	testUserID := int64(9074509)
	key := KeyTheatreSubscribeDramaIDs(testEventID, testUserID)
	require.NoError(service.Redis.Del(key).Err())

	qc := questConfig{
		pc: &PointConfig{
			EventEndTime: util.TimeNow().Unix() + 10,
		},
	}
	added, err := qc.AddTheatreSubscribeDramaID(testEventID, testUserID, 123)
	require.NoError(err)
	assert.True(added)

	added, err = qc.AddTheatreSubscribeDramaID(testEventID, testUserID, 123)
	require.NoError(err)
	assert.False(added)
}

func TestKeyTheatreSubscribeDramaIDs(t *testing.T) {
	assert := assert.New(t)

	key := "theatre_subscribe_drama_ids:event_id:407:user_id:9074509"
	assert.Equal(key, KeyTheatreSubscribeDramaIDs(407, 9074509))
}
