package drawpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// 活动 ID
const (
	EventID591 int64 = 591 // 《我在无限游戏里封神》剧集活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004299772
	EventID608 int64 = 608 // 猫耳夏日狂欢季抽奖活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004328464
	EventID614 int64 = 614 // 暑期每日打卡抽奖活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004333984
	EventID617 int64 = 617 // 吞海剧集活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004339010
	EventID669 int64 = 669 // 再世权臣活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004408885
	EventID709 int64 = 709 // 盲盒剧场 V9 活动 ID https://www.tapd.cn/35612194/prong/stories/view/1135612194004451174
)

// 剧集抽奖活动任务：日更活动任务
const (
	QuestFieldSubscribeDramaDaily1 cache.KeyFormat = "subscribe_drama_daily_%s" // 每日完成次数：追剧，%s 为写入任务记录当天日期，格式：20060102
	QuestFieldShareEventDaily1     cache.KeyFormat = "share_event_daily_%s"     // 每日完成次数：分享活动，%s 为写入任务记录当天日期，格式：20060102
	QuestFieldPlayDurationDaily1   cache.KeyFormat = "play_duration_daily_%s"   // 每日完成次数：收听一定时长剧集下音频，%s 为写入任务记录当天日期，格式：20060102

	QuestFieldSubscribeDramaDailyTotal0 string = "subscribe_drama_daily_total" // 总完成次数：追剧
	QuestFieldShareEventDailyTotal0     string = "share_event_daily_total"     // 总完成次数：分享活动
	QuestFieldPlayDurationDailyTotal0   string = "play_duration_daily_total"   // 总完成次数：收听一定时长剧集下音频

	QuestFieldSubscribeSpecifiedDrama1 cache.KeyFormat = "subscribe_drama_%d" // 任务：订阅一部指定剧集，%d 为剧集 ID
	QuestFieldBuySpecifiedDrama1       cache.KeyFormat = "buy_drama_%d"       // 任务：购买一部指定剧集，%d 为剧集 ID
	QuestFieldPlayDuration0            cache.KeyFormat = "play_duration"      // 任务：收听一定时长剧集下音频
)

// FinishQuestSubscribeDramaDaily 完成任务 - 每日订阅活动剧集
// TODO: 目前未考虑订阅指定剧集的情况
func FinishQuestSubscribeDramaDaily(pc *PointConfig, eventID, userID int64, now time.Time) error {
	if pc.DrawTaskConfig.Tasks.SubscribeOneDrama == nil {
		panic("未配置关注剧集任务")
	}
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: QuestFieldSubscribeDramaDaily1,
		trigger:          pc.DrawTaskConfig.Tasks.SubscribeOneDrama.Trigger,
		repeatLimit:      pc.DrawTaskConfig.Tasks.SubscribeOneDrama.DailyLimit,
		questReward:      pc.DrawTaskConfig.Tasks.SubscribeOneDrama.RewardPoint,
		finishField:      QuestFieldSubscribeDramaDailyTotal0,
		maxFinishNum:     pc.DrawTaskConfig.Tasks.SubscribeOneDrama.Limit,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}
	return qc.finishDailyQuest(1)
}

// FinishQuestShare 完成任务 - 分享活动页
func FinishQuestShare(pc *PointConfig, eventID, userID int64, createTime time.Time) error {
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypeShare)
	if taskConf == nil {
		return nil
	}
	if taskConf.DailyLimit == 0 {
		// 普通分享活动页任务处理
		return finishQuestShareNormal(pc, taskConf, eventID, userID, createTime)
	}
	return finishQuestShareDaily(pc, taskConf, eventID, userID, createTime)
}

// finishQuestShareNormal 完成任务 - 分享活动页
func finishQuestShareNormal(pc *PointConfig, taskConf *Task, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: QuestFieldShareEvent0,
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.Limit,
		questReward:      taskConf.RewardPoint,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// finishQuestShareDaily 完成任务 - 每日分享活动页
func finishQuestShareDaily(pc *PointConfig, taskConf *Task, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: QuestFieldShareEventDaily1,
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.DailyLimit,
		questReward:      taskConf.RewardPoint,
		finishField:      QuestFieldShareEventDailyTotal0,
		maxFinishNum:     taskConf.Limit,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}
	return qc.finishDailyQuest(1)
}

// FinishQuestPlayDuration 完成任务 - 收听活动剧集音频达到指定时长。playedDuration 本次播放时长，单位：毫秒
func FinishQuestPlayDuration(pc *PointConfig, eventID, userID, playedDuration int64, createTime time.Time) error {
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypePlay)
	if taskConf == nil {
		return nil
	}
	if taskConf.DailyLimit == 0 {
		// 非日更收听活动剧集音频任务处理
		return FinishQuestPlayDurationNormal(pc, taskConf, eventID, userID, playedDuration, createTime)
	}
	return FinishQuestPlayDurationDaily(pc, taskConf, eventID, userID, playedDuration, createTime)
}

// FinishQuestPlayDurationNormal 完成任务 - 活动期间收听活动剧集音频达到指定时长。playedDuration 本次播放时长，单位：毫秒
// TODO: 目前未考虑区分剧集进行统计的情况
func FinishQuestPlayDurationNormal(pc *PointConfig, taskConf *Task, eventID, userID, playedDuration int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: QuestFieldPlayDuration0,
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.Limit,
		questReward:      taskConf.RewardPoint,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}
	return qc.finishQuest(playedDuration)
}

// FinishQuestPlayDurationDaily 完成任务 - 每日收听活动剧集音频达到指定时长。playedDuration 本次播放时长，单位：毫秒
// TODO: 目前未考虑区分剧集进行统计的情况
func FinishQuestPlayDurationDaily(pc *PointConfig, taskConf *Task, eventID, userID, playedDuration int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: QuestFieldPlayDurationDaily1,
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.DailyLimit,
		questReward:      taskConf.RewardPoint,
		finishField:      QuestFieldPlayDurationDailyTotal0,
		maxFinishNum:     taskConf.Limit,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishDailyQuest(playedDuration)
}

// FinishQuestBuyOneDrama 完成任务 - 购买一部指定剧集
func FinishQuestBuyOneDrama(pc *PointConfig, eventID, userID, dramaID int64, now time.Time) error {
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypeBuyOneDrama)
	if taskConf == nil {
		return nil
	}
	qc := questConfig{
		questType: questTypeNormal,
		// 普通任务默认没有参数，这里需要将单个剧集 ID 处理进 field 才需要特殊处理
		questFieldFormat: cache.KeyFormat(QuestFieldBuySpecifiedDrama1.Format(dramaID)),
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.Limit,
		questReward:      taskConf.RewardPoint,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishQuestSubscribeOneDrama 完成任务 - 追剧一部指定剧集
func FinishQuestSubscribeOneDrama(pc *PointConfig, eventID, userID, dramaID int64, now time.Time) error {
	taskConf := pc.DrawTaskConfig.GetTaskConfig(TaskTypeSubscribeOneDrama)
	if taskConf == nil {
		return nil
	}
	qc := questConfig{
		questType: questTypeNormal,
		// 普通任务默认没有参数，这里需要将单个剧集 ID 处理进 field 才需要特殊处理
		questFieldFormat: cache.KeyFormat(QuestFieldSubscribeSpecifiedDrama1.Format(dramaID)),
		trigger:          taskConf.Trigger,
		repeatLimit:      taskConf.Limit,
		questReward:      taskConf.RewardPoint,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}
