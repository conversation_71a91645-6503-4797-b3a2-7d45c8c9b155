package drawpoint

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestDrawPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(2000)
	eventID := int64(151)
	key := serviceredis.KeyActivityUserDrawPointEvent1.Format(eventID)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(userID, 10)).Err())
	now := util.TimeNow()
	p := Param{eventID, userID, now.Unix(), false}
	point, err := p.DrawPoint()
	require.NoError(err)
	assert.Equal(point, int64(0))
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: float64(100), Member: strconv.FormatInt(userID, 10)}).Err())
	point, err = p.DrawPoint()
	require.NoError(err)
	assert.Equal(point, int64(100))
}

func TestAddDrawPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventID := int64(11223344)
	userID := int64(5000)
	now := util.TimeNow()
	p := Param{eventID, userID, now.Unix(), false}
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 200}, nil
	})
	defer cancel()
	point, err := p.AddDrawPoint(100, time.Minute)
	require.NoError(err)
	assert.Equal(int64(200), point)
}

func TestMinusDrawPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	eventID := int64(11223344)
	userID := int64(2000)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 0}, nil
	})
	defer cancel()

	now := util.TimeNow()
	p := Param{eventID, userID, now.Unix(), false}
	point, err := p.MinusDrawPoint(100, time.Minute)
	require.NoError(err)
	assert.Equal(int64(0), point)
}

func TestDrawPointKey(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	p := Param{EventID: 100, DailyUpdate: true, CurrentTime: now.Unix()}
	date := time.Unix(p.CurrentTime, 0).Format("20060102")
	key := serviceredis.KeyActivityDailyUserDrawPointEvent2.Format(p.EventID, date)
	assert.Equal(key, p.drawPointKey())
	p.DailyUpdate = false
	assert.Equal(serviceredis.KeyActivityUserDrawPointEvent1.Format(p.EventID), p.drawPointKey())
}

func TestPointConfigTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(PointConfig{}, "draw_point_start_time", "draw_point_end_time",
		"draw_point_daily_update", "time_offset", "draw_task_config")
}

func TestPointConfigNowUnix(t *testing.T) {
	assert := assert.New(t)

	pc := PointConfig{TimeOffset: 10}
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	assert.Equal(now.Unix()+10, pc.NowUnix())
}

func TestPointConfigApplyTimeOffset(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	pc := PointConfig{}
	assert.Equal(now, pc.ApplyTimeOffset(now))
	pc.TimeOffset = 10
	assert.Equal(now.Add(10*time.Second), pc.ApplyTimeOffset(now))
}

func TestPointConfigInTimeRange(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	pc := PointConfig{
		DrawPointStartTime: now.Unix(),
		DrawPointEndTime:   now.Unix() + 10,
	}
	assert.True(pc.InTimeRange(now))
	assert.False(pc.InTimeRange(now.Add(10 * time.Second)))
}

func TestPointConfigExpireDuration(t *testing.T) {
	assert := assert.New(t)

	var pc PointConfig
	assert.PanicsWithValue("活动结束时间异常", func() { pc.ExpireDuration() })
	pc.EventEndTime = -1
	assert.PanicsWithValue("活动结束时间异常", func() { pc.ExpireDuration() })

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	pc.EventEndTime = now.Unix() + 100
	d := pc.ExpireDuration()
	assert.Equal(100+30*24*3600, int(d.Seconds()))
}

func TestFindPointConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 数据库获取到数据
	service.Cache5Min.Flush()
	pc, err := FindPointConfig(151)
	require.NoError(err)
	assert.NotNil(pc)
	// 从缓存获取到数据
	pc, err = FindPointConfig(151)
	require.NoError(err)
	assert.NotNil(pc)

	// 数据库获取不到数据
	pc, err = FindPointConfig(1)
	require.NoError(err)
	assert.Nil(pc)
	// 从缓存获取不到数据
	pc, err = FindPointConfig(1)
	require.NoError(err)
	assert.Nil(pc)
}

func TestPointConfig_IsMatchEventDrama(t *testing.T) {
	assert := assert.New(t)

	// 测试剧集 ID 为 0
	var pc PointConfig
	ok := pc.IsMatchEventDrama(0)
	assert.False(ok)

	// 测试没有配置 dramaIDs
	dramaID := int64(1)
	ok = pc.IsMatchEventDrama(dramaID)
	assert.True(ok)

	// 测试配置了 dramaIDs
	pc.DrawTaskConfig.DramaIDs = []int64{1, 2}
	ok = pc.IsMatchEventDrama(dramaID)
	assert.True(ok)
	pc.DrawTaskConfig.DramaIDs = []int64{2, 3}
	ok = pc.IsMatchEventDrama(dramaID)
	assert.False(ok)
}

func TestPointConfig_GetTaskConfig(t *testing.T) {
	assert := assert.New(t)

	buyDramaTask := Task{
		TaskType:    TaskTypeBuyOneDrama,
		RewardPoint: 1,
		Limit:       1,
		Trigger:     1,
	}
	buyDramaTask2 := Task{
		TaskType:    TaskTypeBuyOneDrama,
		RewardPoint: 2,
		Limit:       1,
		Trigger:     1,
	}
	shareTask := Task{
		TaskType:    TaskTypeShare,
		RewardPoint: 1,
		Limit:       1,
		Trigger:     1,
	}
	playTask := Task{
		TaskType:    TaskTypePlay,
		RewardPoint: 1,
		Limit:       1,
		Trigger:     600000,
	}
	d := &DrawTaskConfig{
		TaskList: []Task{
			buyDramaTask,
			playTask,
			buyDramaTask2,
			shareTask,
			playTask,
		},
	}

	assert.Equal(&buyDramaTask, d.GetTaskConfig(TaskTypeBuyOneDrama))
	assert.Equal(&shareTask, d.GetTaskConfig(TaskTypeShare))
	assert.Equal(&playTask, d.GetTaskConfig(TaskTypePlay))
	assert.Nil(d.GetTaskConfig(TaskTypeSubscribeOneDrama))
}
