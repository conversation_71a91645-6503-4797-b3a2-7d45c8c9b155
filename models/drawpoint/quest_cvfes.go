package drawpoint

import (
	"net/url"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 声优纪活动 ID: https://info.missevan.com/pages/viewpage.action?pageId=109696115
const (
	EventID553 int64 = 553 // 声优纪主会场活动 ID
	EventID555 int64 = 555 // 声优纪分会场活动 ID（十周年称号活动页）
)

// 声优纪活动任务
const (
	QuestFieldFollowUser0 cache.KeyFormat = "follow_user"          // 任务：关注参演用户
	QuestFieldBuyDrama0   cache.KeyFormat = "buy_drama"            // 任务：购买活动剧集
	QuestFieldShareBadge0 cache.KeyFormat = "share_badge"          // 任务：分享十周年称号
	QuestFieldShareEvent1 cache.KeyFormat = "daily_share_event_%s" // 任务：每日分享主会场
	QuestFieldPlayVideo1  cache.KeyFormat = "daily_play_video_%s"  // 任务：每日观看声优纪视频

	ShareEventFinishNum string = "daily_share_event" // 完成次数：活动期间累计分享主会场次数
	PlayVideoFinishNum  string = "daily_play_video"  // 完成次数：活动期间累计观看声优纪视频次数
)

const (
	cvfesTaskMaxShareEventNum = 10 // 分享活动主会场任务完成次数上限
	cvfesTaskMaxPlayVideoNum  = 2  // 观看声优纪视频任务完成次数上限
)

// KeyFollowUserIDs 用户已关注参演用户 IDs
func KeyFollowUserIDs(eventID, userID int64) string {
	return keys.KeyCvfesFollowUserIDs2.Format(eventID, userID)
}

// CvfesFinishQuestFollowUser 更新声优纪活动关注参演用户任务进度
func CvfesFinishQuestFollowUser(pc *PointConfig, eventID, userID, followUserID int64, followType int, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: QuestFieldFollowUser0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      2,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}
	finished, err := service.Redis.HGet(qc.questsKey(), qc.questField()).Int()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return err
	}
	if finished >= 1 {
		return nil
	}
	updated, followNum, err := qc.updateFollowedUserIDs(eventID, userID, followUserID, followType)
	if err != nil {
		return err
	}
	if !updated ||
		// 关注数量要求：普通用户需要关注所有参演用户，参演用户需要关注除自己外的其他所有参演用户
		followNum < qc.getFollowedUserNum(userID) {
		return nil
	}
	return qc.finishQuest(1)
}

// updateFollowedUserIDs 更新关注参演用户 IDs，返回是否完成本次更新和更新后的关注数量，更新后的关注数量只在动作类型为关注时才有意义
// 活动开始前的关注加积分及更新任务进度在 missevan-minigame 项目 /x/event/taskview 接口处理
func (qc *questConfig) updateFollowedUserIDs(eventID, userID, followUserID int64, followType int) (bool, int64, error) {
	key := KeyFollowUserIDs(eventID, userID)
	pipe := service.Redis.TxPipeline()
	var cmdAdd, cmdNum *redis.IntCmd
	isFollow := followType == person.FollowTypeFollow
	if isFollow {
		cmdAdd = pipe.SAdd(key, followUserID)
		cmdNum = pipe.SCard(key)
	} else {
		cmdAdd = pipe.SRem(key, followUserID)
	}
	pipe.Expire(key, qc.pc.ExpireDuration())
	if _, err := pipe.Exec(); err != nil {
		return false, 0, err
	}
	if cmdAdd.Val() == 0 {
		return false, 0, nil
	}
	followNum := int64(0)
	if isFollow {
		followNum = cmdNum.Val()
	}
	return true, followNum, nil
}

// getFollowedUserNum 获取用户需要关注的参演用户数量：普通用户需要关注所有参演用户，参演用户需要关注除自己外的其他所有参演用户
func (qc *questConfig) getFollowedUserNum(userID int64) int64 {
	taskFollowUserNum := int64(len(qc.pc.DrawTaskConfig.FollowUserIDs))
	if util.HasElem(qc.pc.DrawTaskConfig.FollowUserIDs, userID) {
		taskFollowUserNum--
	}
	return taskFollowUserNum
}

// CvfesFinishQuestBuyDrama 完成任务 - 购买活动剧集
func CvfesFinishQuestBuyDrama(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: QuestFieldBuyDrama0,
		trigger:          3,
		repeatLimit:      1,
		questReward:      4,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// CvfesFinishQuestShareBadge 完成任务 - 分享十周年称号
func CvfesFinishQuestShareBadge(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: QuestFieldShareBadge0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      2,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// CvfesFinishQuestShareEvent 完成任务 - 每日分享主会场活动页
func CvfesFinishQuestShareEvent(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: QuestFieldShareEvent1,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,
		finishField:      ShareEventFinishNum,
		maxFinishNum:     cvfesTaskMaxShareEventNum,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishDailyQuest(1)
}

// CvfesFinishQuestPlayVideo 完成任务 - 每日观看声优纪音视频
func CvfesFinishQuestPlayVideo(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeDaily,
		questFieldFormat: QuestFieldPlayVideo1,
		trigger:          1,
		repeatLimit:      1,
		questReward:      2,
		finishField:      PlayVideoFinishNum,
		maxFinishNum:     cvfesTaskMaxPlayVideoNum,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishDailyQuest(1)
}

// IsShareBadge 是否为分享称号
func IsShareBadge(shareURL string) bool {
	if shareURL == "" {
		return false
	}
	u, err := url.Parse(shareURL)
	if err != nil {
		logger.WithField("url", shareURL).Error(err)
		return false
	}
	return u.Query().Has("from_badge_id")
}
