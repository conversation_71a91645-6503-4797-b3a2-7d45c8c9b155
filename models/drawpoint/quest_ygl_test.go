package drawpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestFinishYglQuestShare(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })

	triggered := false
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		triggered = true
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()

	userID := int64(12)
	eventID := EventID366
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishYglQuestShare(pc, eventID, userID, now) })
	questField := YglQuestFieldShare0.Format()
	result, err := service.Redis.HGet(questKey, questField).Int64()
	require.True(serviceredis.IsRedisNil(err))
	assert.EqualValues(0, result)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	err = FinishYglQuestShare(pc, eventID, userID, now)
	require.NoError(err)
	result, err = service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(1, result)
	assert.True(triggered)
}

func TestFinishYglQuestFollowUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()

	userID := int64(12)
	eventID := EventID400
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	require.NoError(service.Redis.Del(questKey).Err())

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishYglQuestFollowUser(pc, eventID, userID, now) })
	questField := YglQuestFieldFollowUser0.Format()
	result, err := service.Redis.HGet(questKey, questField).Int64()
	require.True(serviceredis.IsRedisNil(err))
	assert.EqualValues(0, result)

	// 测试正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	err = FinishYglQuestFollowUser(pc, eventID, userID, now)
	require.NoError(err)
	result, err = service.Redis.HGet(questKey, questField).Int64()
	require.NoError(err)
	assert.EqualValues(1, result)
}
