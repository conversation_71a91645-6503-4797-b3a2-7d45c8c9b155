package drawpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func assertProgress(t *testing.T, questField string, eventID, userID, progress int64) {
	assert := assert.New(t)
	require := require.New(t)

	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	result, err := service.Redis.HGet(questKey, questField).Int64()
	if err != nil {
		require.True(serviceredis.IsRedisNil(err))
	}
	assert.Equal(progress, result)
}

func TestFinishQuestSeasonDramaSubscribe(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID446
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSeasonDramaSubscribe(pc, eventID, userID, FirstSeasonDramaID, now) })
	questField := QuestFieldFirstSeasonSubscribed0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试追剧第一季剧集正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: []int64{FirstSeasonDramaID, SecondSeasonDramaID},
		},
	}
	err = FinishQuestSeasonDramaSubscribe(pc, eventID, userID, FirstSeasonDramaID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 测试追剧第二季剧集正常加积分
	err = FinishQuestSeasonDramaSubscribe(pc, eventID, userID, SecondSeasonDramaID, now)
	require.NoError(err)
	questField = QuestFieldSecondSeasonSubscribed0.Format()
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestSeasonDramaBought(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID446
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSeasonDramaBought(pc, eventID, userID, FirstSeasonDramaID, now) })
	questField := QuestFieldFirstSeasonBought0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试购买第一季剧集正常加积分
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: []int64{FirstSeasonDramaID, SecondSeasonDramaID},
		},
	}
	err = FinishQuestSeasonDramaBought(pc, eventID, userID, FirstSeasonDramaID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 测试购买第二季剧集正常加积分
	err = FinishQuestSeasonDramaBought(pc, eventID, userID, SecondSeasonDramaID, now)
	require.NoError(err)
	questField = QuestFieldSecondSeasonBought0.Format()
	assertProgress(t, questField, eventID, userID, 1)
}

func TestFinishQuestSeasonDramaPlayedDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(1)
	eventID := EventID446
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = FinishQuestSeasonDramaPlayedDuration(pc, eventID, userID, FirstSeasonDramaID, 1, now) })
	questField := QuestFieldFirstSeasonPlayedDuration1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertProgress(t, questField, eventID, userID, 0)

	// 测试没有达到收听时长
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			DramaIDs: []int64{FirstSeasonDramaID, SecondSeasonDramaID},
		},
	}
	err = FinishQuestSeasonDramaPlayedDuration(pc, eventID, userID, FirstSeasonDramaID, 1, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)
	// 断言没有完成次数
	_, err = service.Redis.HGet(finishKey, FirstSeasonPlayedFinishNum).Int64()
	require.True(serviceredis.IsRedisNil(err))

	// 测试收听第一季剧集正常加积分
	err = FinishQuestSeasonDramaPlayedDuration(pc, eventID, userID, FirstSeasonDramaID, FangXueTaskMinPlayedDuration, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, FangXueTaskMinPlayedDuration+1)
	// 断言完成次数
	result, err := service.Redis.HGet(finishKey, FirstSeasonPlayedFinishNum).Int64()
	require.NoError(err)
	assert.Equal(int64(1), result)

	// 测试收听第二季剧集正常加积分
	err = FinishQuestSeasonDramaPlayedDuration(pc, eventID, userID, SecondSeasonDramaID, FangXueTaskMinPlayedDuration, now)
	require.NoError(err)
	questField = QuestFieldSecondSeasonPlayedDuration1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertProgress(t, questField, eventID, userID, FangXueTaskMinPlayedDuration)
	// 断言完成次数
	result, err = service.Redis.HGet(finishKey, SecondSeasonPlayedFinishNum).Int64()
	require.NoError(err)
	assert.Equal(int64(1), result)

	// 测试达到完成次数上限，不再累计完成次数
	err = service.Redis.HSet(finishKey, SecondSeasonPlayedFinishNum, 2).Err()
	require.NoError(err)
	err = FinishQuestSeasonDramaPlayedDuration(pc, eventID, userID, SecondSeasonDramaID, FangXueTaskMinPlayedDuration, now)
	require.NoError(err)
	// 断言完成次数
	result, err = service.Redis.HGet(finishKey, SecondSeasonPlayedFinishNum).Int64()
	require.NoError(err)
	assert.Equal(int64(2), result)
}
