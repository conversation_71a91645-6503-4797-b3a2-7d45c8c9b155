package drawpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/rpc/person"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(553), EventID553)
}

func TestCvfesFinishQuestFollowUser(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	userID, followUserID, followUserID2, followUserID3 := int64(81), int64(82), int64(83), int64(84)

	eventID := EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	userIDsKey := KeyFollowUserIDs(eventID, userID)
	err := service.Redis.Del(questKey, userIDsKey).Err()
	require.NoError(err)
	err = service.Redis.SAdd(userIDsKey, followUserID, followUserID2).Err()
	require.NoError(err)

	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 2}, nil
	})
	defer cancel()

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
		DrawTaskConfig: DrawTaskConfig{
			FollowUserIDs: []int64{followUserID, followUserID2, followUserID3},
		},
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() {
		_ = CvfesFinishQuestFollowUser(pc, eventID, userID, followUserID, person.FollowTypeFollow, now)
	})
	questField := QuestFieldFollowUser0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试任务完成前取关
	pc.EventID = eventID
	err = CvfesFinishQuestFollowUser(pc, eventID, userID, followUserID, person.FollowTypeUnfollow, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 0)

	// 测试任务完成前重新关注
	err = CvfesFinishQuestFollowUser(pc, eventID, userID, followUserID, person.FollowTypeFollow, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 0)

	// 测试成功完成任务
	err = CvfesFinishQuestFollowUser(pc, eventID, userID, followUserID3, person.FollowTypeFollow, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 测试重复取关不会减进度
	err = CvfesFinishQuestFollowUser(pc, eventID, userID, followUserID3, person.FollowTypeUnfollow, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	// 参演用户关注除自己外其他所有参演用户即可完成任务
	questKey = keys.KeyDrawPointUserQuests2.Format(eventID, followUserID2)
	userIDsKey = KeyFollowUserIDs(eventID, followUserID2)
	err = service.Redis.Del(questKey, userIDsKey).Err()
	require.NoError(err)
	err = service.Redis.SAdd(userIDsKey, followUserID).Err()
	require.NoError(err)

	err = CvfesFinishQuestFollowUser(pc, eventID, followUserID2, followUserID3, person.FollowTypeFollow, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, followUserID2, 1)
}

func TestQuestConfig_updateFollowedUserIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(1881)
	testFollowUserID := int64(1882)
	key := KeyFollowUserIDs(EventID553, testUserID)
	err := service.Redis.Del(key).Err()
	require.NoError(err)
	qc := questConfig{
		pc: &PointConfig{
			EventEndTime: util.TimeNow().Unix() + 10,
		},
	}

	// 未关注该用户时无法成功取关
	result, num, err := qc.updateFollowedUserIDs(EventID553, testUserID, testFollowUserID, person.FollowTypeUnfollow)
	require.NoError(err)
	assert.False(result)
	assert.Zero(num)
	exists, err := service.Redis.SIsMember(key, testFollowUserID).Result()
	require.NoError(err)
	assert.False(exists)

	// 未关注该用户时可以成功关注
	result, num, err = qc.updateFollowedUserIDs(EventID553, testUserID, testFollowUserID, person.FollowTypeFollow)
	require.NoError(err)
	assert.True(result)
	assert.Equal(int64(1), num)
	exists, err = service.Redis.SIsMember(key, testFollowUserID).Result()
	require.NoError(err)
	assert.True(exists)

	// 已关注该用户时无法重复关注
	result, num, err = qc.updateFollowedUserIDs(EventID553, testUserID, testFollowUserID, person.FollowTypeFollow)
	require.NoError(err)
	assert.False(result)
	assert.Zero(num)
	exists, err = service.Redis.SIsMember(key, testFollowUserID).Result()
	require.NoError(err)
	assert.True(exists)

	// 已关注该用户时可以成功取关
	result, num, err = qc.updateFollowedUserIDs(EventID553, testUserID, testFollowUserID, person.FollowTypeUnfollow)
	require.NoError(err)
	assert.True(result)
	assert.Zero(num)
	exists, err = service.Redis.SIsMember(key, testFollowUserID).Result()
	require.NoError(err)
	assert.False(exists)
}

func TestQuestConfig_getFollowedUserNum(t *testing.T) {
	assert := assert.New(t)

	qc := questConfig{
		pc: &PointConfig{
			EventEndTime: util.TimeNow().Unix() + 10,
			DrawTaskConfig: DrawTaskConfig{
				FollowUserIDs: []int64{990, 991, 992},
			},
		},
	}

	// 测试普通用户需要关注所有参演用户
	assert.Equal(int64(3), qc.getFollowedUserNum(880))

	// 测试参演用户需要关注除自己外其他所有参演用户
	assert.Equal(int64(2), qc.getFollowedUserNum(991))
}

func TestCvfesFinishQuestBuyDrama(t *testing.T) {
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 4}, nil
	})
	defer cancel()

	key := keys.KeyDrawPointUserQuests2.Format(EventID553, 12)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}

	eventID := EventID553
	userID := int64(12)
	require.PanicsWithValue("积分配置和活动任务不匹配", func() {
		_ = CvfesFinishQuestBuyDrama(pc, eventID, userID, now)
	})
	questField := QuestFieldBuyDrama0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试正常更新进度
	pc.EventID = EventID553
	err = CvfesFinishQuestBuyDrama(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)

	err = CvfesFinishQuestBuyDrama(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 2)

	err = CvfesFinishQuestBuyDrama(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 3)
}

func TestCvfesFinishQuestShareBadge(t *testing.T) {
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 2}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		service.Redis.Del(questKey)
		cancel()
	}()
	err := service.Redis.Del(questKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = CvfesFinishQuestShareBadge(pc, eventID, userID, now) })
	questField := QuestFieldShareBadge0.Format()
	assertProgress(t, questField, eventID, userID, 0)

	// 测试完成任务
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	err = CvfesFinishQuestShareBadge(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)
}

func TestCvfesFinishQuestShareEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	userID := int64(1)
	eventID := EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 2}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		_ = service.Redis.Del(questKey, finishKey).Err()
		cancel()
	}()
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}

	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = CvfesFinishQuestShareEvent(pc, eventID, userID, now) })
	dailyQuestField := QuestFieldShareEvent1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertProgress(t, dailyQuestField, eventID, userID, 0)
	exists, err := service.Redis.Exists(finishKey).Result()
	require.NoError(err)
	assert.EqualValues(0, exists)

	// 测试完成任务
	pc = &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            eventID,
		EventEndTime:       now.Unix(),
	}
	err = CvfesFinishQuestShareEvent(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, dailyQuestField, eventID, userID, 1)
	// 断言完成次数
	res, err := service.Redis.HGet(finishKey, ShareEventFinishNum).Int()
	require.NoError(err)
	assert.EqualValues(1, res)

	// 测试当天重复完成每日任务
	err = CvfesFinishQuestShareEvent(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, dailyQuestField, eventID, userID, 2)
	// 断言完成次数
	res, err = service.Redis.HGet(finishKey, ShareEventFinishNum).Int()
	require.NoError(err)
	assert.EqualValues(1, res)

	// 测试达到完成次数上限
	err = service.Redis.HSet(finishKey, ShareEventFinishNum, cvfesTaskMaxShareEventNum).Err()
	require.NoError(err)
	err = CvfesFinishQuestShareEvent(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, dailyQuestField, eventID, userID, 2)
	// 断言完成次数
	res, err = service.Redis.HGet(finishKey, ShareEventFinishNum).Int()
	require.NoError(err)
	assert.EqualValues(cvfesTaskMaxShareEventNum, res)
}

func TestCvfesFinishQuestPlayVideo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	cancel := mrpc.SetMock(userapi.URIActivityDrawPointUpdate, func(input interface{}) (output interface{}, err error) {
		return map[string]interface{}{"point": 1}, nil
	})
	defer func() {
		util.SetTimeNow(nil)
		cancel()
	}()
	userID := int64(1)
	eventID := EventID553
	questKey := keys.KeyDrawPointUserQuests2.Format(eventID, userID)
	finishKey := keys.KeyEventPointTaskInfo2.Format(eventID, userID)
	err := service.Redis.Del(questKey, finishKey).Err()
	require.NoError(err)

	// 测试活动 ID 不匹配的情况
	pc := &PointConfig{
		DrawPointStartTime: now.Unix() - 10,
		DrawPointEndTime:   now.Unix() + 10,
		EventID:            99999,
		EventEndTime:       now.Unix(),
	}
	require.PanicsWithValue("积分配置和活动任务不匹配", func() { _ = CvfesFinishQuestPlayVideo(pc, eventID, userID, now) })
	questField := QuestFieldPlayVideo1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	assertProgress(t, questField, eventID, userID, 0)

	// 测试完成任务
	pc.EventID = eventID
	err = CvfesFinishQuestPlayVideo(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 1)
	// 断言完成次数
	result, err := service.Redis.HGet(finishKey, PlayVideoFinishNum).Int64()
	require.NoError(err)
	assert.EqualValues(1, result)

	// 测试当天重复完成
	err = CvfesFinishQuestPlayVideo(pc, eventID, userID, now)
	require.NoError(err)
	assertProgress(t, questField, eventID, userID, 2)
	// 断言完成次数
	result, err = service.Redis.HGet(finishKey, PlayVideoFinishNum).Int64()
	require.NoError(err)
	assert.EqualValues(1, result)

	// 测试达到完成次数上限
	err = service.Redis.HSet(finishKey, PlayVideoFinishNum, cvfesTaskMaxPlayVideoNum).Err()
	require.NoError(err)
	err = CvfesFinishQuestPlayVideo(pc, eventID, userID, now)
	require.NoError(err)
	// 断言完成次数
	result, err = service.Redis.HGet(finishKey, PlayVideoFinishNum).Int64()
	require.NoError(err)
	assert.EqualValues(cvfesTaskMaxPlayVideoNum, result)
}

func TestIsShareBadge(t *testing.T) {
	assert := assert.New(t)

	// 测试分享链接为空
	res := IsShareBadge("")
	assert.False(res)

	// 测试分享活动
	res = IsShareBadge("https://www.missevan.com/mevent/555")
	assert.False(res)

	// 测试分享称号
	res = IsShareBadge("https://www.missevan.com/mevent/555?from_badge_id=1")
	assert.True(res)
}
