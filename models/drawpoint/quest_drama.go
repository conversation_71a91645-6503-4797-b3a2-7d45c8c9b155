package drawpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// EventID428 活动 ID
// 《时代新声力配音大赛》的剧集活动 ID https://www.tapd.bilibili.co/35612194/prong/stories/view/1135612194003072748
const EventID428 int64 = 428

// TaskMinPlayedDuration 累计收听任务完成最少播放时长（5 分钟），单位：毫秒
const TaskMinPlayedDuration int64 = 300000

// 时代新声力配音活动任务
const (
	NewVoiceQuestFieldSubscribe0      cache.KeyFormat = "new_voice_subscribe"       // 任务：追剧活动剧集
	NewVoiceQuestFieldPlayedDuration0 cache.KeyFormat = "new_voice_played_duration" // 任务：累计收听一定时长该活动剧集下音频
	NewVoiceQuestFieldUserPoint0      cache.KeyFormat = "new_voice_user_point"      // 任务：投食活动剧集下的任意音频
	NewVoiceQuestFieldShare0          cache.KeyFormat = "new_voice_share"           // 任务：分享活动页面
)

// FinishQuestSubscribe 完成任务 - 追剧活动剧集
func FinishQuestSubscribe(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: NewVoiceQuestFieldSubscribe0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishQuestPlayedDuration 完成任务 - 收听活动剧集音频达到 5 分钟
func FinishQuestPlayedDuration(pc *PointConfig, eventID, userID, playedDuration int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: NewVoiceQuestFieldPlayedDuration0,
		trigger:          TaskMinPlayedDuration,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(playedDuration)
}

// FinishQuestUserPoint 完成任务 - 对活动剧集下的任意音频投食小鱼干一次
func FinishQuestUserPoint(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: NewVoiceQuestFieldUserPoint0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}

// FinishQuestShareEvent 完成任务 - 分享活动页一次
func FinishQuestShareEvent(pc *PointConfig, eventID, userID int64, now time.Time) error {
	qc := questConfig{
		questType:        questTypeNormal,
		questFieldFormat: NewVoiceQuestFieldShare0,
		trigger:          1,
		repeatLimit:      1,
		questReward:      1,

		pc: matchQuest(pc, eventID),

		userID: userID,
		now:    pc.ApplyTimeOffset(now),
	}

	return qc.finishQuest(1)
}
