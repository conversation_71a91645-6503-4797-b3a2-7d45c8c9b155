package user

import (
	"encoding/json"
	"fmt"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

const (
	// AppConfigDisable 禁止
	AppConfigDisable = iota
	// AppConfigEnable 允许
	AppConfigEnable
)

// 互动消息类型
const (
	AppPushTypeLikeComment = iota // 评论点赞
	AppPushTypeAt                 // @ 我
	AppPushTypeComment            // 评论
)

// MUserConfig 用户 App 设置信息
type MUserConfig struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	UserID       int64  `gorm:"column:user_id"`
	Buvid        string `gorm:"column:buvid"`
	AppConfig    string `gorm:"column:app_config"`

	AppConfigInfo *AppConfig `gorm:"-"`
}

// AppConfig 用户 App 设置
type AppConfig struct {
	MessageNotification MessageNotification `json:"message_notification"`
}

// MessageNotification 用户 App 推送设置
type MessageNotification struct {
	Like    *int `json:"like,omitempty"`    // 点赞
	AtMe    *int `json:"at_me,omitempty"`   // @ 我
	Comment *int `json:"comment,omitempty"` // 评论
}

// TableName table name
func (MUserConfig) TableName() string {
	return "m_user_config"
}

// AfterFind is a GORM hook for query
func (m *MUserConfig) AfterFind() error {
	if m.AppConfig != "" {
		err := json.Unmarshal([]byte(m.AppConfig), &m.AppConfigInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// findUserAppConfig 查找用户 App 设置
func findUserAppConfig(userID int64) (*MUserConfig, error) {
	var uc MUserConfig
	err := service.MainDB.Where("user_id = ?", userID).Take(&uc).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &uc, nil
}

// IsEnablePush 是否允许推送消息，无相关配置时默认允许发推送
func (m *MUserConfig) IsEnablePush(actionType int) bool {
	if !util.HasElem([]int{AppPushTypeLikeComment, AppPushTypeAt, AppPushTypeComment}, actionType) {
		panic(fmt.Sprintf("获取是否允许推送消息配置时传入了错误的参数 %d", actionType))
	}
	if m == nil || m.AppConfigInfo == nil {
		// 无相关配置时默认允许发推送
		return true
	}
	messageConfig := m.AppConfigInfo.MessageNotification
	var enable bool
	switch actionType {
	case AppPushTypeLikeComment:
		enable = messageConfig.Like == nil || *messageConfig.Like == AppConfigEnable
	case AppPushTypeAt:
		enable = messageConfig.AtMe == nil || *messageConfig.AtMe == AppConfigEnable
	case AppPushTypeComment:
		enable = messageConfig.Comment == nil || *messageConfig.Comment == AppConfigEnable
	}
	return enable
}

// IsMessagePushAllowed 是否可以给用户推送互动消息提醒
func IsMessagePushAllowed(userID int64, actionType int) (bool, error) {
	conf, err := findUserAppConfig(userID)
	if err != nil {
		return false, err
	}
	enable := conf.IsEnablePush(actionType)
	return enable, nil
}

// findUsersAppConfig 查找指定用户们的 App 设置
func findUsersAppConfig(userIDs []int64) ([]MUserConfig, error) {
	var uc []MUserConfig
	err := service.MainDB.Where("user_id IN (?)", userIDs).Find(&uc).Error
	if err != nil {
		return nil, err
	}
	return uc, nil
}

// AllowedPushUserIDs 获取允许推送互动消息用户 IDs
func AllowedPushUserIDs(userIDs []int64, actionType int) ([]int64, error) {
	configs, err := findUsersAppConfig(userIDs)
	if err != nil {
		return []int64{}, err
	}
	var disableUserIDs []int64
	for _, config := range configs {
		if !config.IsEnablePush(actionType) {
			disableUserIDs = append(disableUserIDs, config.UserID)
		}
	}
	// 用户在 m_user_config 表无配置信息时也需要默认允许开启推送消息，因此排除拒绝推送的用户外均为允许推送的用户
	return sets.Diff(userIDs, disableUserIDs), nil
}
