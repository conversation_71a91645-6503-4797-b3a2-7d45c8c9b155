package user

import (
	"fmt"
	"strconv"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// constants
const (
	// BanTimeForeverListen 永封用户（在 BanTimeForever 的基础上新增禁听音）
	BanTimeForeverListen = -2
	// BanTimeForever 永封用户（禁私信、评论和弹幕）
	BanTimeForever = -1
)

// 黑名单业务类型
const (
	// BlackTypeUserComment 用户评论（评论、子评论、弹幕）
	BlackTypeUserComment = iota
	// BlackTypePrivateMessage 用户私信
	BlackTypePrivateMessage
)

// InBlacklist checks if User.UserID is in black list
func InBlacklist(userID int64, blackType int) (bool, error) {
	if userID == 0 {
		return false, nil
	}
	expire, err := service.Redis.ZScore(serviceredis.KeyBlackList0.Format(), strconv.FormatInt(userID, 10)).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return false, nil
		}
		return false, err
	}
	// TODO: 后续可能支持其他黑名单业务类型
	switch blackType {
	case BlackTypeUserComment, BlackTypePrivateMessage:
		return util.HasElem([]int64{BanTimeForever, BanTimeForeverListen}, int64(expire)) || int64(expire) > util.TimeNow().Unix(), nil
	default:
		return false, fmt.Errorf("错误的黑名单业务类型：%v", blackType)
	}
}
