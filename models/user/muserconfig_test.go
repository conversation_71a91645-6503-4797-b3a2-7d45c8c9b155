package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MUserConfig{}, "id", "create_time", "modified_time", "user_id", "buvid", "app_config")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(AppConfig{}, "message_notification")
	kc.Check(MessageNotification{}, "comment", "at_me", "like")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, AppConfigDisable)
	assert.Equal(1, AppConfigEnable)

	assert.Equal(0, AppPushTypeLikeComment)
	assert.Equal(1, AppPushTypeAt)
	assert.Equal(2, AppPushTypeComment)
}

func TestUserAppSendPushConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID, testUser2ID, testUser3ID int64 = 3013622, 3013623, 3013624
	testUserIDs := []int64{testUserID, testUser2ID, testUser3ID}
	err := service.MainDB.Delete(MUserConfig{}, "user_id IN (?)", testUserIDs).Error
	require.NoError(err)

	// 测试所有用户没有设置记录
	config, err := findUserAppConfig(testUserID)
	require.NoError(err)
	assert.Nil(config)
	testPushLike, testPushAt, testPushComment := AppPushTypeLikeComment, AppPushTypeAt, AppPushTypeComment
	enable := config.IsEnablePush(testPushLike)
	assert.True(enable)
	allowPush, err := IsMessagePushAllowed(testUserID, testPushLike)
	require.NoError(err)
	assert.True(allowPush)
	usersAppConfig, err := findUsersAppConfig(testUserIDs)
	require.NoError(err)
	assert.Equal(usersAppConfig, []MUserConfig{})
	allowedPushUserIDs, err := AllowedPushUserIDs(testUserIDs, AppPushTypeLikeComment)
	require.NoError(err)
	assert.ElementsMatch(allowedPushUserIDs, testUserIDs)

	// 测试部分用户有 @ 我设置记录、无点赞推送相关设置
	testConfig := MUserConfig{
		UserID:    testUserID,
		AppConfig: `{"message_notification":{"at_me":1}}`,
	}
	err = service.MainDB.Create(&testConfig).Error
	require.NoError(err)
	// 断言 @ 我相关
	config, err = findUserAppConfig(testUserID)
	require.NoError(err)
	require.NotNil(config)
	enable = config.IsEnablePush(testPushAt)
	assert.True(enable)
	allowPush, err = IsMessagePushAllowed(testUserID, testPushAt)
	require.NoError(err)
	assert.True(allowPush)
	// 断言点赞相关
	assert.Nil(config.AppConfigInfo.MessageNotification.Like)
	enable = config.IsEnablePush(testPushLike)
	assert.True(enable)
	allowPush, err = IsMessagePushAllowed(testUserID, testPushLike)
	require.NoError(err)
	assert.True(allowPush)
	// 断言评论相关
	assert.Nil(config.AppConfigInfo.MessageNotification.Comment)
	enable = config.IsEnablePush(testPushComment)
	assert.True(enable)
	allowPush, err = IsMessagePushAllowed(testUserID, testPushComment)
	require.NoError(err)
	assert.True(allowPush)
	usersAppConfig, err = findUsersAppConfig(testUserIDs)
	require.NoError(err)
	assert.Equal(len(usersAppConfig), 1)
	assert.Equal(usersAppConfig[0].UserID, testUserID)
	allowedPushUserIDs, err = AllowedPushUserIDs(testUserIDs, AppPushTypeLikeComment)
	require.NoError(err)
	assert.ElementsMatch(allowedPushUserIDs, testUserIDs)

	// 测试该用户有点赞相关设置：用户关闭点赞推送
	err = service.MainDB.Model(&MUserConfig{}).Where("user_id = ?", testUserID).
		Update("app_config", `{"message_notification":{"at_me":1,"like":0}}`).Error
	require.NoError(err)
	config, err = findUserAppConfig(testUserID)
	require.NoError(err)
	require.NotNil(config)
	assert.Equal(AppConfigDisable, *config.AppConfigInfo.MessageNotification.Like)
	enable = config.IsEnablePush(testPushLike)
	assert.False(enable)
	allowPush, err = IsMessagePushAllowed(testUserID, testPushLike)
	require.NoError(err)
	assert.False(allowPush)
	usersAppConfig, err = findUsersAppConfig(testUserIDs)
	require.NoError(err)
	assert.Equal(len(usersAppConfig), 1)
	assert.Equal(usersAppConfig[0].UserID, testUserID)
	allowedPushUserIDs, err = AllowedPushUserIDs(testUserIDs, AppPushTypeLikeComment)
	require.NoError(err)
	assert.ElementsMatch(allowedPushUserIDs, []int64{testUser2ID, testUser3ID})

	// 测试该用户有点赞相关设置：用户开启点赞推送
	err = service.MainDB.Model(&MUserConfig{}).Where("user_id = ?", testUserID).
		Update("app_config", `{"message_notification":{"at_me":1,"like":1}}`).Error
	require.NoError(err)
	config, err = findUserAppConfig(testUserID)
	require.NoError(err)
	require.NotNil(config)
	assert.Equal(AppConfigEnable, *config.AppConfigInfo.MessageNotification.Like)
	enable = config.IsEnablePush(testPushLike)
	assert.True(enable)
	allowPush, err = IsMessagePushAllowed(testUserID, testPushLike)
	require.NoError(err)
	assert.True(allowPush)

	// 测试参数有误
	assert.Panics(func() {
		_ = config.IsEnablePush(3)
	})
	assert.Panics(func() {
		_, _ = IsMessagePushAllowed(testUserID, 3)
	})
	assert.Panics(func() {
		_, _ = AllowedPushUserIDs(testUserIDs, 3)
	})
}
