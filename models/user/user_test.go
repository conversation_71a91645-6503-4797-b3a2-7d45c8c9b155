package user

import (
	"encoding/json"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Simple{}, "id", "confirm", "username", "iconurl", "userintro", "avatar", "icontype", "boardiconurl")
	kc.Check(MowangskUser{},
		"cip", "uip", "ctime", "utime", "quanxian", "teamid", "teamname", "ban", "ustr", "uint", "uagi",
		"point", "nowsound", "iconid", "iconcolor", "subtitle", "boardiconid", "boardiconcolor", "coverid",
		"coverurl", "isnewmsg", "userintro_audio", "likenum", "fansnum", "follownum", "soundnum", "albumnum", "imagenum",
		"feednum", "soundnumchecked", "imagenumchecked", "mlevel", "coverurl_new")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Simple{}, "id", "confirm", "username", "iconurl", "userintro", "avatar", "icontype", "boardiconurl")
	kc.Check(MowangskUser{}, "cip", "uip", "ctime", "utime", "quanxian", "teamid", "teamname", "ban", "ustr",
		"uint", "uagi", "point", "nowsound", "iconid", "iconcolor", "subtitle", "boardiconid", "boardiconcolor",
		"coverid", "coverurl", "isnewmsg", "userintro_audio", "likenum", "fansnum", "follownum", "soundnum", "albumnum",
		"imagenum", "feednum", "soundnumchecked", "imagenumchecked", "mlevel", "coverurl_new")
}

func TestGetUser(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)

	var u MowangskUser
	err := service.DB.Where("id > 0").First(&u).Error
	assert.NoError(err)
	assert.NotEmpty(u.IconURL)
}

func TestGetAuthenticated(t *testing.T) {
	assert := assert.New(t)

	testGoldenVIP := uint(33554512)
	testBlueVIP := uint(50331733)
	data := GetAuthenticated(testGoldenVIP)
	assert.EqualValues(ConfirmGoldenVIP, data)
	data = GetAuthenticated(testBlueVIP)
	assert.EqualValues(ConfirmBlueVIP, data)
}

func TestMowangskUser_AfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	var user MowangskUser
	var err error
	user.ID = 3013742
	user.UserName = "测试用户 afterFind"
	user.IconType = UserIconTypeCartoon

	// 插入数据库
	err = service.DB.Table(user.TableName()).FirstOrCreate(&user, user).Error
	require.NoError(err)

	// 测试卡通头像，并确认 AfterFind 是正确的 hook
	err = service.DB.Table(user.TableName()).Where("username = ?", user.UserName).First(&user).Error
	require.Nil(err)
	url := user.IconURL
	suffix := "profile/icon01.png"
	assert.Equal(suffix, url[len(url)-len(suffix):])

	// 测试正常头像
	user.IconType = UserIconTypeNormal
	err = user.AfterFind()
	require.Nil(err)
	url = user.IconURL
	suffix = "avatars/icon01.png"
	assert.Equal(suffix, url[len(url)-len(suffix):])

	// 测试特殊头像
	user.IconURL = "icon"
	user.IconType = UserIconTypeSpecial
	err = user.AfterFind()
	require.Nil(err)
	url = user.IconURL
	assert.Equal("icon", url)

	// 从数据库删除
	err = service.DB.Table(user.TableName()).Where("id = ? AND username = ?", user.ID,
		user.UserName).Delete("").Error
	require.NoError(err)
}

func TestGetConfirmByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := MowangskUser{}
	service.DB.Table(MowangskUser{}.TableName()).Where("id = ?", 3013620).UpdateColumn("confirm", gorm.Expr("confirm | ?", ConfirmMsgNoLimit))
	confirm, err := m.GetConfirmByID(3013620)
	require.NoError(err)
	assert.Equal(uint(ConfirmMsgNoLimit), confirm&ConfirmMsgNoLimit)

	_, err = m.GetConfirmByID(-1)
	assert.Equal(ErrUserNotFound, err)
	service.DB.Table(MowangskUser{}.TableName()).Where("id = ?", 3013620).UpdateColumn("confirm", gorm.Expr("confirm &~ ?", ConfirmMsgNoLimit))
}

func TestAddUserPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	user := MowangskUser{}
	require.NoError(service.DB.Table(user.TableName()).Find(&user, "id = ?", 3013620).Error)
	expected := user.Point + 1
	err := AddUserPoint(3013620, 1)
	require.NoError(err)
	require.NoError(service.DB.Table(user.TableName()).Find(&user, "id = ?", 3013620).Error)
	assert.Equal(expected, user.Point)
}

func TestSubUserPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(3013097)

	u, err := FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	expectedPoint := u.Point

	ok, err := SubUserPoint(testUserID, 1)
	require.NoError(err)
	assert.True(ok)

	u, err = FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	assert.Equal(expectedPoint-1, u.Point)

	ok, err = SubUserPoint(testUserID, 1000000)
	require.NoError(err)
	assert.False(ok)
}

func TestFindByUserID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	u, err := FindByUserID(12)
	require.Nil(err)
	assert.NotNil(u)
	assert.Equal(int64(12), u.ID)
	u, err = FindByUserID(99999999)
	require.NoError(err)
	assert.Nil(u)
}

func TestFindSimpleList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	users, err := FindSimpleList([]int64{12, 999999999, 3013623})
	require.NoError(err)
	assert.Len(users, 2)
	assert.NotEmpty(users[0].IconURL)
	// 已注销用户
	assert.EqualValues(4096, users[1].Confirm)
}

func TestFindSimpleMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	users, err := FindSimpleMap([]int64{12, 999999999, 3013623})
	require.NoError(err)
	assert.Len(users, 2)
	assert.NotNil(users[12])
	assert.NotEmpty(users[12].IconURL)
	// 已注销用户
	assert.NotNil(users[3013623])
	assert.EqualValues(4096, users[3013623].Confirm)
}

func TestPointDetailLog_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)

	key := keys.DatabusKeyUserPointDetailLog1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	log := PointDetailLog{
		Type:   1,
		UserID: testUserID,
		Num:    1,
		Origin: 1,
	}
	require.NoError(log.send())
	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(key, m.Key)

	var message PointDetailLog
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.Equal(log, message)
}

func TestPointDetailLog_UpdatePoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(3013097)

	key := keys.DatabusKeyUserPointDetailLog1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	u, err := FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	expectedPoint := u.Point

	p := PointDetailLog{
		UserID: testUserID,
		Num:    1,
	}
	ok, err := p.UpdatePoint()
	require.NoError(err)
	assert.True(ok)
	msgs := service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	assert.Equal(key, m.Key)
	u, err = FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	assert.Equal(expectedPoint+1, u.Point)

	p.Num = -1
	ok, err = p.UpdatePoint()
	require.NoError(err)
	assert.True(ok)
	msgs = service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m = <-msgs
	assert.Equal(key, m.Key)
	u, err = FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	assert.Equal(expectedPoint, u.Point)

	p.Num = -1000000
	ok, err = p.UpdatePoint()
	require.NoError(err)
	assert.False(ok)
	msgs = service.Databus.AppLogPub.DebugPubMsgs()
	require.Len(msgs, 0)
}

func TestIsDeleted(t *testing.T) {
	assert := assert.New(t)

	// 测试用户未注销
	assert.False(IsDeleted(4095))

	// 测试用户已注销
	assert.True(IsDeleted(4096))
}

func TestIsPrivacy(t *testing.T) {
	assert := assert.New(t)

	// 测试用户未设置隐私
	assert.False(IsPrivacy(255))

	// 测试用户已设置隐私
	assert.True(IsPrivacy(256))
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无用户的情况
	exists, err := Exists(999999999)
	require.NoError(err)
	assert.False(exists)

	// 测试有用户的情况
	exists, err = Exists(12)
	require.NoError(err)
	assert.True(exists)
}
