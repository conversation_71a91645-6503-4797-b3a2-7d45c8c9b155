package user

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestInBlacklist(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(233322)
	)

	// 测试用户 ID 为 0
	inBlacklist, err := InBlacklist(0, BlackTypeUserComment)
	require.NoError(err)
	assert.False(inBlacklist)

	// 测试用户没有在黑名单中
	key := serviceredis.KeyBlackList0.Format()
	_, err = service.Redis.ZRem(key, strconv.FormatInt(testUserID, 10)).Result()
	require.NoError(err)
	inBlacklist, err = InBlacklist(testUserID, BlackTypeUserComment)
	require.NoError(err)
	assert.False(inBlacklist)

	// 测试用户被永久封禁（禁私信与评论）
	_, err = service.Redis.ZAdd(key, &redis.Z{Score: BanTimeForever, Member: strconv.FormatInt(testUserID, 10)}).Result()
	require.NoError(err)
	inBlacklist, err = InBlacklist(testUserID, BlackTypeUserComment)
	require.NoError(err)
	assert.True(inBlacklist)

	// 测试用户被永久封禁（禁私信、评论与听音）
	_, err = service.Redis.ZAdd(key, &redis.Z{Score: BanTimeForeverListen, Member: strconv.FormatInt(testUserID, 10)}).Result()
	require.NoError(err)
	inBlacklist, err = InBlacklist(testUserID, BlackTypeUserComment)
	require.NoError(err)
	assert.True(inBlacklist)

	// 测试用户被临时封禁
	_, err = service.Redis.ZAdd(key, &redis.Z{Score: float64(util.TimeNow().Add(time.Hour).Unix()),
		Member: strconv.FormatInt(testUserID, 10)}).Result()
	require.NoError(err)
	inBlacklist, err = InBlacklist(testUserID, BlackTypeUserComment)
	require.NoError(err)
	assert.True(inBlacklist)

	// 测试用户临时封禁时间过期
	_, err = service.Redis.ZAdd(key, &redis.Z{Score: float64(util.TimeNow().Add(-time.Hour).Unix()),
		Member: strconv.FormatInt(testUserID, 10)}).Result()
	require.NoError(err)
	inBlacklist, err = InBlacklist(testUserID, BlackTypeUserComment)
	require.NoError(err)
	assert.False(inBlacklist)
}
