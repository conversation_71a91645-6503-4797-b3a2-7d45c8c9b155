package user

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/gorm"
	null "gopkg.in/guregu/null.v3"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 小鱼干日志类型
// https://info.missevan.com/pages/viewpage.action?pageId=53188232
const (
	TypePointNewUserRegister = iota + 1 // 新用户注册
	TypeTaskSign                        // 签到
	TypeTaskGetPoint                    // 摸鱼
	TypeTaskComment                     // 评论
	TypeTaskTs                          // 每日任务投食
	TypeSoundTs                         // 音频投食
	TypeTaskShare                       // 分享
	TypeUpdateUserInfo                  // 修改用户信息（用户昵称、用户封面图等）
	TypeAdminGrant                      // 后台管理员发放
	TypeActivity                        // 活动
	TypeTaskPatchSign                   // 补签
)

// 小鱼干操作来源
const (
	TypeOriginWeb       = iota // Web
	TypeOriginMobileWeb        // 手机网页
	TypeOriginApp              // App
)

// Simple 简单用户信息结构
type Simple struct {
	ID           int64  `gorm:"column:id" json:"id"`
	Confirm      uint   `gorm:"column:confirm" json:"confirm"`
	UserName     string `gorm:"column:username" json:"username"`
	IconURL      string `gorm:"column:iconurl" json:"iconurl"`
	UserIntro    string `gorm:"column:userintro" json:"userintro"`
	Avatar       string `gorm:"column:avatar" json:"avatar"`
	IconType     int64  `gorm:"column:icontype" json:"icontype"`
	BoardIconURL string `gorm:"column:boardiconurl" json:"boardiconurl"`

	// Deprecated: Using IconURL instead.
	BoardIconURL2 string `gorm:"-" json:"-"`
}

// MowangskUser model
type MowangskUser struct {
	Simple

	CIP             string `gorm:"column:cip" json:"cip"`
	UIP             string `gorm:"column:uip" json:"uip"`
	CTime           int64  `gorm:"column:ctime" json:"ctime"`
	UTime           int64  `gorm:"column:utime" json:"utime"`
	Quanxian        string `gorm:"column:quanxian" json:"quanxian"`
	TeamID          int64  `gorm:"column:teamid" json:"teamid"`
	TeamName        string `gorm:"column:teamname" json:"teamname"`
	Ban             int    `gorm:"column:ban" json:"ban"`
	Ustr            int64  `gorm:"column:ustr" json:"ustr"`
	Uint            int64  `gorm:"column:uint" json:"uint"`
	Uagi            int64  `gorm:"column:uagi" json:"uagi"`
	Point           int64  `gorm:"column:point" json:"point"`
	NowSound        int64  `gorm:"column:nowsound" json:"nowsound"`
	IconID          int64  `gorm:"column:iconid" json:"iconid"`
	IconColor       string `gorm:"column:iconcolor" json:"iconcolor"`
	Subtitle        string `gorm:"column:subtitle" json:"subtitle"`
	BoardIconID     int64  `gorm:"column:boardiconid" json:"boardiconid"`
	BoardIconColor  string `gorm:"column:boardiconcolor" json:"boardiconcolor"`
	CoverID         int64  `gorm:"column:coverid" json:"coverid"`
	CoverURL        string `gorm:"column:coverurl" json:"coverurl"`
	IsNewMsg        int    `gorm:"column:isnewmsg" json:"isnewmsg"`
	UserIntroAudio  int64  `gorm:"column:userintro_audio" json:"userintro_audio"`
	LikeNum         int64  `gorm:"column:likenum" json:"likenum"`
	FansNum         int64  `gorm:"column:fansnum" json:"fansnum"`
	FollowNum       int64  `gorm:"column:follownum" json:"follownum"`
	SoundNum        int64  `gorm:"column:soundnum" json:"soundnum"`
	AlbumNum        int64  `gorm:"column:albumnum" json:"albumnum"`
	ImageNum        int64  `gorm:"column:imagenum" json:"imagenum"`
	FeedNum         int64  `gorm:"column:feednum" json:"feednum"`
	SoundNumChecked int64  `gorm:"column:soundnumchecked" json:"soundnumchecked"`
	ImageNumChecked int64  `gorm:"column:imagenumchecked" json:"imagenumchecked"`
	MLevel          int    `gorm:"column:mlevel" json:"mlevel"`
	CoverURLNew     string `gorm:"column:coverurl_new" json:"coverurl_new"`

	Authenticated uint `gorm:"-" json:"-"`
	Bind          bool `gorm:"-" json:"-"`
}

// TableName for current model
func (Simple) TableName() string {
	return "mowangskuser"
}

// DB the db instance of MowangskUser model
func (m MowangskUser) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// TableName for current model
func (MowangskUser) TableName() string {
	return "mowangskuser"
}

// 用户头像类型，0：二次元头像；1：普通头像；2：特殊头像
const (
	UserIconTypeCartoon = 0
	UserIconTypeNormal  = 1
	UserIconTypeSpecial = 2
)

// 加 V 认证标识：
// https://github.com/MiaoSiLa/missevan-doc/blob/master/product/用户_confirm_字段值约定.md
const (
	// 金 V 认证标识值
	ConfirmGoldenVIP = 2
	// 蓝 V 认证标识值
	ConfirmBlueVIP = 3
	// 用户设置隐私
	ConfirmPrivacy = 1 << 8
	// 私信发送无限制
	ConfirmMsgNoLimit = 1 << 11
	// 用户已注销
	ConfirmDeleted = 1 << 12
)

// MinutesOfLevel 用户每等级允许上传音频总分钟数
const MinutesOfLevel = 500

// MaxLevel 用户等级上限
const MaxLevel = 100

// AfterFind is a GORM hook for query
// TODO: 需要与 live-service 同名模型的头像处理函数重新进行整合
func (s *Simple) AfterFind() error {
	switch s.IconType {
	case UserIconTypeCartoon:
		boardIconURL := s.BoardIconURL
		if boardIconURL == "" {
			boardIconURL = "icon01.png"
		}
		s.BoardIconURL2 = service.Storage.Parse(params.URL.ProfileURL + boardIconURL)
	case UserIconTypeNormal:
		avatar := s.Avatar
		if avatar == "" {
			avatar = "icon01.png"
		}
		s.BoardIconURL2 = service.Storage.Parse(params.URL.AvatarURL + avatar)
	default:
		s.BoardIconURL2 = s.IconURL
	}
	s.IconURL = s.BoardIconURL2

	return nil
}

// AfterFind is a GORM hook for query
func (m *MowangskUser) AfterFind() error {
	_ = m.Simple.AfterFind()
	m.Authenticated = GetAuthenticated(m.Confirm)
	return nil
}

var (
	// ErrUserNotFound user not found error
	ErrUserNotFound = errors.New("user not found")
)

// FindByUserID 通过 UserID 查找单个用户
func FindByUserID(userID int64) (*MowangskUser, error) {
	user := new(MowangskUser)
	err := service.DB.Table(MowangskUser{}.TableName()).Where("id = ?", userID).Take(&user).Error
	if servicedb.IsErrNoRows(err) {
		return nil, nil
	}
	return user, err
}

// GetCTimeByID 获取用户创建时间
func (m MowangskUser) GetCTimeByID(id int64) (int64, error) {
	var ctime []null.Int
	// <=> SELECT ctime FROM mowangskuser WHERE id = ?
	err := service.DB.Table(m.TableName()).
		Select("ctime").Where("id = ?", id).Pluck("ctime", &ctime).Error
	if err != nil {
		return 0, err
	}
	if len(ctime) == 0 || !ctime[0].Valid {
		return 0, ErrUserNotFound
	}
	return ctime[0].Int64, nil
}

// GetConfirmByID 获取用户 confirm
func (m MowangskUser) GetConfirmByID(id int64) (uint, error) {
	var confirm uint
	err := service.DB.Table(m.TableName()).
		Select("confirm").Where("id = ?", id).Row().Scan(&confirm)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, ErrUserNotFound
		}
		return 0, err
	}
	return confirm, nil
}

var vipBitOffsetMask = []uint{24, 0xffff}

// GetAuthenticated 获取加 V 认证标识（供前端使用）
// 2：金 V；3：蓝 V
func GetAuthenticated(confirm uint) uint {
	v := (confirm >> vipBitOffsetMask[0]) & vipBitOffsetMask[1]
	return v
}

// AddUserPoint 用户账户添加小鱼干
// NOTICE: 需要在调用侧发送 DataBus 日志消息
func AddUserPoint(userID int64, point int) error {
	err := service.DB.Table(MowangskUser{}.TableName()).Where("id = ?", userID).
		Update("point", gorm.Expr("point + ?", point)).Error
	if err != nil {
		return err
	}
	return nil
}

// SubUserPoint 用户账户减少小鱼干，返回是否扣除成功和错误信息
// NOTICE: 需要在调用侧发送 DataBus 日志消息
func SubUserPoint(userID int64, point int) (bool, error) {
	db := service.DB.Table(MowangskUser{}.TableName()).Where("id = ? AND point >= ?", userID, point).
		Update("point", gorm.Expr("point - ?", point))
	if err := db.Error; err != nil {
		return false, err
	}

	return db.RowsAffected > 0, nil
}

// FindSimpleList 通过 UserIDs 查找简单用户信息
func FindSimpleList(userIDs []int64) ([]*Simple, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	var users []*Simple
	err := service.DB.Select("id, confirm, username, iconurl, userintro, avatar, icontype, boardiconurl").
		Find(&users, "id IN (?)", userIDs).Error
	return users, err
}

// FindSimpleMap 查询用户简单用户信息，返回 map[userID]*Simple
func FindSimpleMap(userIDs []int64) (map[int64]*Simple /* map[userID]*Simple */, error) {
	userIDs = util.Uniq(userIDs)
	tmp, err := FindSimpleList(userIDs)
	if err != nil {
		return nil, err
	}
	return util.ToMap(tmp, "ID").(map[int64]*Simple), nil
}

// PointDetailLog 用户小鱼干变动日志结构
// https://info.missevan.com/pages/viewpage.action?pageId=53188232
type PointDetailLog struct {
	CreateTime int64           `json:"create_time"`
	UserID     int64           `json:"user_id"`
	Num        int             `json:"num"`
	Origin     int             `json:"origin"`
	Type       int             `json:"type"`
	More       json.RawMessage `json:"more,omitempty"` // 用户小鱼干变动日志结构中的额外信息
}

// send 发送用户小鱼干变动日志
func (p *PointDetailLog) send() error {
	key := keys.DatabusKeyUserPointDetailLog1.Format(p.UserID)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return service.Databus.AppLogPub.Send(ctx, key, p)
}

// UpdatePoint 更新小鱼干数量，并且发送日志消息
// 返回是否扣除成功和错误信息
func (p *PointDetailLog) UpdatePoint() (bool, error) {
	if p.Num > 0 {
		if err := AddUserPoint(p.UserID, p.Num); err != nil {
			return false, err
		}
	} else {
		ok, err := SubUserPoint(p.UserID, -p.Num)
		if err != nil {
			return false, err
		}
		if !ok {
			return false, nil
		}
	}

	if err := p.send(); err != nil {
		logger.Error(err)
		// PASS
	}

	return true, nil
}

// IsDeleted 判断用户是否已注销
func IsDeleted(confirm uint) bool {
	return (confirm & ConfirmDeleted) == ConfirmDeleted
}

// IsPrivacy 判断用户是否设置隐私
func IsPrivacy(confirm uint) bool {
	return (confirm & ConfirmPrivacy) == ConfirmPrivacy
}

// Exists 返回用户是否存在
func Exists(userID int64) (bool, error) {
	return servicedb.Exists(MowangskUser{}.DB().Where("id = ?", userID))
}
