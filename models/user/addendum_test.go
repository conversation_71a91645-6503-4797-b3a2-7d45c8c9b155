package user

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestAddendumTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Addendum{}, "id", "sex", "birthday", "qq", "weibo", "wechat", "bilibili", "apple",
		"message_config", "ip", "ip_detail", "birthdate_mmdd")
}

func TestAddendum_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("user_addendum", Addendum{}.TableName())
}

func TestFormatRegionName(t *testing.T) {
	assert := assert.New(t)

	regionName := formatRegionName("")
	assert.Empty(regionName)

	regionName = formatRegionName("中国台湾")
	assert.Equal("中国台湾", regionName)

	regionName = formatRegionName("台湾")
	assert.Equal("中国台湾", regionName)

	regionName = formatRegionName("上海")
	assert.Equal("上海", regionName)
}

func TestParseIPAndGetIPDetailParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var notExistsUserLocationUserID int64 = 99999
	ipDetailParams, err := ParseIPAndGetIPDetailParams("**************", notExistsUserLocationUserID)
	require.NoError(err)
	require.NotNil(ipDetailParams)
	assert.Equal("中国", ipDetailParams.CountryName)
	assert.Equal("山东", ipDetailParams.RegionName)
	assert.Equal("济南", ipDetailParams.CityName)

	// 测试中国地区香港增加了“中国”前缀
	ipDetailParams, err = ParseIPAndGetIPDetailParams("***************", notExistsUserLocationUserID)
	require.NoError(err)
	require.NotNil(ipDetailParams)
	assert.Equal("中国香港", ipDetailParams.RegionName)

	var existsUserLocationUserID int64 = 12
	ipDetailParams, err = ParseIPAndGetIPDetailParams("***************", existsUserLocationUserID)
	require.NoError(err)
	require.NotNil(ipDetailParams)
	assert.Equal("中国香港", ipDetailParams.RegionName)
	assert.Equal("山东", ipDetailParams.ShowLocation)

	_, err = ParseIPAndGetIPDetailParams("113.11128.90.161", notExistsUserLocationUserID)
	assert.Equal(serviceutil.ErrInvalidIP, err)
}

func TestSaveIPAndIPDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var existsID int64 = 1
	err := SaveIPAndIPDetail(existsID, "***********", &IPDetailParams{})
	require.NoError(err)
	var testExistsAddendum Addendum
	err = Addendum{}.DB().Where("id = ?", existsID).Take(&testExistsAddendum).Error
	require.NoError(err)
	assert.Equal("***********", testExistsAddendum.IP)
	assert.NotNil(testExistsAddendum.IPDetail)
	assert.Nil(testExistsAddendum.Birthday)
	assert.Nil(testExistsAddendum.Sex)

	var notExistsID int64 = 99999999
	err = SaveIPAndIPDetail(notExistsID, "*************", &IPDetailParams{})
	require.NoError(err)
	var testNotExistsAddendum Addendum
	err = Addendum{}.DB().Where("id = ?", notExistsID).Take(&testNotExistsAddendum).Error
	require.NoError(err)
	assert.Equal("*************", testNotExistsAddendum.IP)

	// 测试用户属地表有配置时的 user_addendum.ipDetail
	var existsUserLocationUserID int64 = 12
	err = SaveIPAndIPDetail(existsUserLocationUserID, "***************", &IPDetailParams{
		CountryCode:  "US",
		CountryName:  "美国",
		RegionName:   "洛杉矶",
		ShowLocation: "山东",
	})
	require.NoError(err)
	var testExistsUserLocationAddendum Addendum
	err = Addendum{}.DB().Where("id = ?", existsUserLocationUserID).Take(&testExistsUserLocationAddendum).Error
	require.NoError(err)
	require.NotNil(testNotExistsAddendum.IPDetail)
	var params IPDetailParams
	err = json.Unmarshal([]byte(*testExistsUserLocationAddendum.IPDetail), &params)
	require.NoError(err)
	assert.Equal("US", params.CountryCode)
	assert.Equal("美国", params.CountryName)
	assert.Equal("洛杉矶", params.RegionName)
	assert.Equal("山东", params.ShowLocation)
}

func TestSendIPLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)
	service.Databus.AppLogPub.ClearDebugPubMsgs()

	var (
		testUserID int64 = 12
		testIP           = "127.0.0.1"
	)
	err := SendIPLog(testUserID, testIP, FromApp)
	require.NoError(err)
	pubMsgs := service.Databus.AppLogPub.DebugPubMsgs()
	message := <-pubMsgs
	assert.Equal(keys.DatabusKeyCollectUserIPLog1.Format(testUserID), message.Key)
	expectBytes, err := json.Marshal(ipLogParams{
		UserID:     testUserID,
		IP:         testIP,
		CreateTime: now.Unix(),
		From:       FromApp,
	})
	require.NoError(err)
	actualBytes, err := message.Value.MarshalJSON()
	require.NoError(err)
	assert.Equal(expectBytes, actualBytes)

	err = SendIPLog(testUserID, testIP, 0)
	assert.EqualError(err, "invalid log form, user_id: 12, from: 0")
}

func TestIPLocationFromIPDetail(t *testing.T) {
	assert := assert.New(t)

	ipLocation := IPLocationFromIPDetailParams("1000.10.10.10", &IPDetailParams{})
	assert.Empty(ipLocation)

	ipDetailParams := &IPDetailParams{
		ShowLocation: "山东",
	}
	ipLocation = IPLocationFromIPDetailParams("1000.10.10.10", ipDetailParams)
	assert.Equal("山东", ipLocation)

	ipDetailParams = &IPDetailParams{
		CountryName: "局域网",
	}
	ipLocation = IPLocationFromIPDetailParams("************", ipDetailParams)
	assert.Equal("未知", ipLocation)

	ipDetailParams = &IPDetailParams{
		CountryCode: "CN",
		CountryName: serviceutil.CountryNameChina,
		RegionName:  "山东省",
	}
	ipLocation = IPLocationFromIPDetailParams("**************", ipDetailParams)
	assert.Equal(ipDetailParams.RegionName, ipLocation)

	ipDetailParams = &IPDetailParams{
		CountryCode: "FA",
		CountryName: "法国",
	}
	ipLocation = IPLocationFromIPDetailParams("**************", ipDetailParams)
	assert.Equal(ipDetailParams.CountryName, ipLocation)
}

func TestNeedLeapYearProcess(t *testing.T) {
	assert := assert.New(t)

	// 闰年无需处理
	assert.False(NeedLeapYearProcess(2024, time.February, 28, LeapYearLookForward))
	// 2 月 28 号处理
	assert.True(NeedLeapYearProcess(2023, time.February, 28, LeapYearLookForward))
	// 3 月 1 号处理
	assert.True(NeedLeapYearProcess(2023, time.March, 1, LeapYearLookBackward))
	// 正常日期
	assert.False(NeedLeapYearProcess(2023, time.February, 27, LeapYearLookForward))
	// 不做处理
	assert.False(NeedLeapYearProcess(2023, time.February, 28, LeapYearDoNothing))
}

func TestIsUserBirthday(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 正常日期
	now := time.Date(2023, 1, 26, 0, 0, 0, 0, time.Local)
	res, err := IsUserBirthday(1, now, LeapYearLookForward)
	require.NoError(err)
	assert.True(res)

	// 闰年 2 月 28 号
	now = time.Date(2024, 2, 28, 0, 0, 0, 0, time.Local)
	res, err = IsUserBirthday(2, now, LeapYearLookForward)
	require.NoError(err)
	assert.True(res)

	// 非闰年 2 月 28 号（提前发放）
	now = time.Date(2023, 2, 28, 0, 0, 0, 0, time.Local)
	res, err = IsUserBirthday(3, now, LeapYearLookForward)
	require.NoError(err)
	assert.True(res)

	// 非闰年 3 月 1 号（回头发放）
	now = time.Date(2023, 3, 1, 0, 0, 0, 0, time.Local)
	res, err = IsUserBirthday(4, now, LeapYearLookBackward)
	require.NoError(err)
	assert.True(res)
}

func TestFindUserIDsByBirthday(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 正常日期
	now := time.Date(2023, 1, 26, 0, 0, 0, 0, time.Local)
	res, err := FindUserIDsByBirthday(now, LeapYearLookForward)
	require.NoError(err)
	assert.Len(res, 1)

	// 表里没有记录的日期
	now = time.Date(2024, 1, 27, 0, 0, 0, 0, time.Local)
	res, err = FindUserIDsByBirthday(now, LeapYearLookForward)
	require.NoError(err)
	assert.Len(res, 0)

	// 闰年 2 月 28 号
	now = time.Date(2024, 2, 28, 0, 0, 0, 0, time.Local)
	res, err = FindUserIDsByBirthday(now, LeapYearLookForward)
	require.NoError(err)
	assert.Len(res, 1)

	// 非闰年 2 月 28 号（提前发放）
	now = time.Date(2023, 2, 28, 0, 0, 0, 0, time.Local)
	res, err = FindUserIDsByBirthday(now, LeapYearLookForward)
	require.NoError(err)
	assert.Len(res, 2)

	// 非闰年 3 月 1 号（回头发放）
	now = time.Date(2023, 3, 1, 0, 0, 0, 0, time.Local)
	res, err = FindUserIDsByBirthday(now, LeapYearLookBackward)
	require.NoError(err)
	assert.Len(res, 2)
}
