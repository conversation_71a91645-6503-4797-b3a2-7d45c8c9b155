package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/useriplocation"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/util"
)

const regionPrefixCN = "中国"

// 用户信息来源
const (
	FromWeb = iota + 1
	FromApp
)

// 闰年生日的处理方法
// TODO: 将常量名改得更直观些
const (
	LeapYearDoNothing    = iota // 不进行处理
	LeapYearLookForward         // 在 2 月 28 号提前处理
	LeapYearLookBackward        // 在 3 月 1 号回头处理
)

// LeapYearSpecialDateMMDD 需要进行特殊处理的闰年日期（2 月 29）
const LeapYearSpecialDateMMDD = "0229"

// Addendum 用户副表
type Addendum struct {
	ID            int64      `gorm:"column:id;primary_key"`
	Sex           *int       `gorm:"column:sex"`
	Birthday      *time.Time `gorm:"column:birthday"`
	QQ            *string    `gorm:"column:qq"`
	Weibo         *string    `gorm:"column:weibo"`
	Wechat        *string    `gorm:"column:wechat"`
	BiliBili      *string    `gorm:"column:bilibili"`
	Apple         *string    `gorm:"column:apple"`
	MessageConfig *string    `gorm:"column:message_config"`
	IP            string     `gorm:"column:ip"`
	IPDetail      *string    `gorm:"column:ip_detail"`
	BirthdateMMDD *string    `gorm:"column:birthdate_mmdd"`
}

// TableName for Addendum model
func (Addendum) TableName() string {
	return "user_addendum"
}

// DB the db instance of Addendum model
func (a Addendum) DB() *gorm.DB {
	return service.DB.Table(a.TableName())
}

// 中国地区列表
var cnRegionList = []string{"澳门", "香港", "台湾"}

// formatRegionName 包含在中国地区列表中的地区增加“中国”前缀
// 业务场景：展示用户 IP 属地的省份/州
func formatRegionName(regionName string) string {
	if regionName == "" {
		return regionName
	}
	if util.HasElem(cnRegionList, regionName) {
		return regionPrefixCN + regionName
	}
	return regionName
}

// IPDetailParams IP 地理位置详情参数
type IPDetailParams struct {
	CountryCode  string `json:"country_code"`            // 国家 2 位代码，例："CN"
	CountryName  string `json:"country_name"`            // 国家名称
	RegionName   string `json:"region_name"`             // 一级行政区名称
	CityName     string `json:"city_name"`               // 城市名称
	ShowLocation string `json:"show_location,omitempty"` // 用于展示的 IP 属地
}

// ParseIPAndGetIPDetailParams 解析 IP 返回 IP 地理位置详情参数
// TODO: 后续按需支持其他语言
func ParseIPAndGetIPDetailParams(ipAddr string, userID int64) (*IPDetailParams, error) {
	record, err := service.GeoIP.Get(ipAddr, serviceutil.IPIPLanguageCN)
	if err != nil {
		return nil, err
	}
	ipDetailParams := &IPDetailParams{
		CountryCode: record.CountryIsoCode(),
		CountryName: record.CountryName(),
		RegionName:  formatRegionName(record.RegionName()),
		CityName:    record.CityName(),
	}
	showIPLocation, err := useriplocation.FindIPLocationByUserID(userID)
	if err != nil {
		return nil, err
	}
	if showIPLocation != "" {
		ipDetailParams.ShowLocation = showIPLocation
	}
	return ipDetailParams, nil
}

// SaveIPAndIPDetail 保存用户的 IP 和 IP 详情
func SaveIPAndIPDetail(userID int64, ip string, ipDetailParams *IPDetailParams) error {
	ipDetailBytes, err := json.Marshal(ipDetailParams)
	if err != nil {
		return err
	}
	ipDetail := string(ipDetailBytes)
	query := Addendum{}.DB().Where("id = ?", userID)
	exists, err := servicedb.Exists(query)
	if err != nil {
		return err
	}
	updateIPAndIPDetail := func() error {
		return query.Updates(map[string]interface{}{
			"ip":        ip,
			"ip_detail": ipDetail,
		}).Error
	}
	if exists {
		return updateIPAndIPDetail()
	}

	err = Addendum{}.DB().Create(&Addendum{
		ID:       userID,
		IP:       ip,
		IPDetail: &ipDetail,
	}).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			return updateIPAndIPDetail()
		}
		return err
	}
	return nil
}

type ipLogParams struct {
	UserID     int64  `json:"user_id"`
	IP         string `json:"ip"`
	CreateTime int64  `json:"create_time"`
	From       int    `json:"from"`
}

// SendIPLog 发送用户的 IP databus 消息
func SendIPLog(userID int64, ip string, from int) error {
	if !util.HasElem([]int{FromWeb, FromApp}, from) {
		return fmt.Errorf("invalid log form, user_id: %d, from: %d", userID, from)
	}
	log := &ipLogParams{
		UserID:     userID,
		IP:         ip,
		CreateTime: util.TimeNow().Unix(),
		From:       from,
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	key := keys.DatabusKeyCollectUserIPLog1.Format(userID)
	return service.Databus.AppLogPub.Send(ctx, key, log)
}

// IPLocationFromIPDetailParams 根据 IP 地理位置详情参数获取 IP 属地。境内：一级行政区名称，境外：国家名称
func IPLocationFromIPDetailParams(ip string, ipDetailParams *IPDetailParams) string {
	if ipDetailParams == nil {
		return ""
	}
	if ipDetailParams.ShowLocation != "" {
		return ipDetailParams.ShowLocation
	}
	if ipDetailParams.CountryName == "" {
		return ""
	}
	// IP 为本地地址、保留地址、局域网等情况
	if ipDetailParams.CountryCode == "" {
		logger.WithField("ip", ip).Debugf("IP 详情异常：%+v", ipDetailParams)
		return "未知"
	}
	// 境内（包括港澳台）选择一级行政区名称
	if ipDetailParams.CountryName == serviceutil.CountryNameChina {
		return ipDetailParams.RegionName
	}
	return ipDetailParams.CountryName
}

// NeedLeapYearProcess 判断当前日期是否需要进行闰年相关的特殊处理
func NeedLeapYearProcess(year int, month time.Month, day int, leapMethod int) bool {
	if util.IsLeapYear(year) {
		// 闰年无需特殊处理，正常处理
		return false
	}
	switch leapMethod {
	case LeapYearLookForward:
		if month == time.February && day == 28 {
			return true
		}
	case LeapYearLookBackward:
		if month == time.March && day == 1 {
			return true
		}
	default:
		return false
	}
	return false
}

// IsUserBirthday 确认今日是否是用户生日
func IsUserBirthday(userID int64, now time.Time, leapMethod int) (bool, error) {
	year, month, day := now.Date()
	birthDateMMDDs := []string{now.Format("0102")}
	if NeedLeapYearProcess(year, month, day, leapMethod) {
		// 为闰年生日的用户进行特殊处理
		birthDateMMDDs = append(birthDateMMDDs, LeapYearSpecialDateMMDD)
	}
	db := Addendum{}.DB().Where("id = ? AND birthdate_mmdd IN (?)", userID, birthDateMMDDs)
	return servicedb.Exists(db)
}

// FindUserIDsByBirthday 根据生日日期获取用户 ID 列表
func FindUserIDsByBirthday(now time.Time, leapMethod int) ([]int64, error) {
	year, month, day := now.Date()
	birthDateMMDDs := []string{now.Format("0102")}
	if NeedLeapYearProcess(year, month, day, leapMethod) {
		// 为闰年生日的用户进行特殊处理
		birthDateMMDDs = append(birthDateMMDDs, LeapYearSpecialDateMMDD)
	}
	var ids []int64
	err := Addendum{}.DB().Where("birthdate_mmdd IN (?)", birthDateMMDDs).Pluck("id", &ids).Error
	if err != nil {
		return []int64{}, err
	}
	return ids, nil
}
