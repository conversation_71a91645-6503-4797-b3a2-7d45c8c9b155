package models

// TODO: 待使用 anprize 包替换

import (
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/service"
)

// AnPrize 活动奖品配置
type AnPrize struct {
	ID          int64  `gorm:"column:id" json:"id"`
	Name        string `gorm:"column:name" json:"name"`
	Pic         string `gorm:"column:pic" json:"pic"`
	EventID     int64  `gorm:"column:event_id" json:"event_id"`
	Probability int    `gorm:"column:probability" json:"-"` // 中奖概率
	Num         int    `gorm:"column:num" json:"-"`         // 奖品数量
}

// TableName of AnPrize
func (AnPrize) TableName() string {
	return "an_prize"
}

// AfterFind is a GORM hook for query
func (a *AnPrize) AfterFind() error {
	if a.Pic == "" {
		return nil
	}
	// WORKAROUND: 兼容老数据，以后可以只用 Storage.Parse
	url, err := url.Parse(a.Pic)
	if err != nil {
		return err
	}
	switch url.Scheme {
	case "http":
	case "https":
	default:
		a.Pic = service.Storage.Parse(a.Pic)
	}
	return nil
}

// ListPrizes 获取 eventID 的奖品设置
func ListPrizes(eventID int64) (prizes []AnPrize, err error) {
	err = service.DB.Where("event_id = ?", eventID).Find(&prizes).Error
	return
}

// IsPrize 根据正则表达式提取 name 中的第一个匹配项
func IsPrize(name string, regex *regexp.Regexp) (int, bool) {
	s := regex.FindStringSubmatch(strings.TrimSpace(name))
	if len(s) < 2 {
		return 0, false
	}
	n, err := strconv.Atoi(s[1])
	if err != nil {
		return 0, false
	}
	return n, true
}
