package eventuserprize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestListMyPrize(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	eventID := int64(100)
	userID := int64(12)
	prizeKey1 := keys.LocalKeyEventAnPrize1.Format(eventID)
	prizes := []anprize.AnPrize{
		{
			ID:       123,
			Pic:      "http://test.png",
			NameInfo: anprize.PrizeNameInfo{Name: "test"},
		},
	}
	service.Cache5Min.SetDefault(prizeKey1, prizes)
	require.NoError(service.DB.Where("user_id = ? AND event_id = ?",
		userID, eventID).Delete(EventUserPrize{}).Error)
	data, pa, err := ListMyPrize(eventID, userID, 1, 20)
	require.NoError(err)
	assert.Zero(pa.Count)
	assert.Empty(data)
	assert.NotNil(data)

	eventID = 214
	prizeKey1 = keys.LocalKeyEventAnPrize1.Format(eventID)
	prizes = []anprize.AnPrize{
		{
			ID:       123,
			Pic:      "http://test.png",
			NameInfo: anprize.PrizeNameInfo{Name: "test"},
		},
	}
	service.Cache5Min.SetDefault(prizeKey1, prizes)
	now := util.TimeNow()
	data = []EventUserPrize{
		{
			UserID:  userID,
			EventID: eventID,
			PrizeID: prizes[0].ID,
		},
		{
			UserID:     userID,
			EventID:    eventID,
			PrizeID:    prizes[0].ID,
			CreateTime: now.Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.DB, data[0].TableName(), data))
	data, pa, err = ListMyPrize(eventID, userID, 1, 20)
	require.NoError(err)
	assert.EqualValues(1, pa.Count)
	require.NotEmpty(data)
	assert.Equal(data[0].Name, prizes[0].NameInfo.Name)
	assert.Equal(data[0].ImageURL, prizes[0].Pic)
}
