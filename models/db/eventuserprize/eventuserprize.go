package eventuserprize

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// EventUserPrize m_event_user_prize
type EventUserPrize struct {
	ID           int64   `gorm:"column:id" json:"id"`
	UserID       int64   `gorm:"column:user_id" json:"user_id"`
	PrizeID      int64   `gorm:"column:prize_id" json:"prize_id"`
	EventID      int64   `gorm:"column:event_id" json:"-"`
	GameCode     *string `gorm:"column:game_code" json:"-"`
	CreateTime   int64   `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64   `gorm:"column:modified_time" json:"-"`

	Name     string `gorm:"-" json:"name"`
	ImageURL string `gorm:"-" json:"image_url"`
}

// TableName of EventUserPrize model
func (EventUserPrize) TableName() string {
	return "m_event_user_prize"
}

// NewEventUserPrize new EventUserPrize
func NewEventUserPrize(userID int64, prize *anprize.AnPrize, eventID int64) *EventUserPrize {
	return &EventUserPrize{
		UserID:   userID,
		PrizeID:  prize.ID,
		EventID:  eventID,
		Name:     prize.NameInfo.Name,
		ImageURL: prize.Pic,
	}
}

// BeforeCreate hook
func (u *EventUserPrize) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	u.CreateTime = now
	return nil
}

// BeforeSave hook
func (u *EventUserPrize) BeforeSave(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	u.ModifiedTime = now
	return nil
}

// Create 插入用户抽奖记录
func (u *EventUserPrize) Create() error {
	err := service.DB.Create(&u).Error
	if err != nil {
		return err
	}
	return nil
}

// Find 根据 userID 和 eventID 查找用户获奖记录
func (u *EventUserPrize) Find(prizeIDs []int64, p, pageSize int64) ([]EventUserPrize, util.Pagination, error) {
	db := service.DB.Table(EventUserPrize{}.TableName()).Where("user_id = ? AND event_id = ?", u.UserID, u.EventID)
	if len(prizeIDs) != 0 {
		db = db.Where("prize_id IN (?)", prizeIDs)
	}
	// WORKAROUND: 214 活动只查询当月的记录
	if u.EventID == 214 {
		db = db.Where("create_time >= ?", util.BeginningOfMonth(util.TimeNow()).Unix())
	}
	var totalCount int64
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, util.Pagination{}, err
	}
	pa := util.MakePagination(totalCount, p, pageSize)
	myPrizes := make([]EventUserPrize, 0, pageSize)
	if !pa.Valid() {
		return myPrizes, pa, nil
	}
	db = pa.ApplyTo(db)
	err = db.Order("id DESC").Scan(&myPrizes).Error
	if err != nil {
		return nil, util.Pagination{}, err
	}
	return myPrizes, pa, nil
}

// ListMyPrize 查询抽奖记录
func ListMyPrize(eventID, userID, p, pageSize int64) ([]EventUserPrize, util.Pagination, error) {
	prizes, err := anprize.FindEventPrizes(eventID)
	if err != nil {
		return nil, util.Pagination{}, err
	}
	if len(prizes) == 0 {
		return []EventUserPrize{}, util.MakePagination(0, p, pageSize), nil
	}
	prizeIDs := make([]int64, len(prizes))
	prizeMap := make(map[int64]*anprize.AnPrize, len(prizes))
	for i := range prizes {
		prizeIDs[i] = prizes[i].ID
		prizeMap[prizes[i].ID] = &prizes[i]
	}
	up := EventUserPrize{
		EventID: eventID,
		UserID:  userID,
	}
	data, pa, err := up.Find(prizeIDs, p, pageSize)
	if err != nil {
		return nil, util.Pagination{}, err
	}
	for i := range data {
		if prize := prizeMap[data[i].PrizeID]; prize != nil {
			data[i].Name = prize.NameInfo.Name
			data[i].ImageURL = prize.Pic
		}
	}
	return data, pa, nil
}
