package anprize

import (
	"errors"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

// ErrInvalidName 奖品名异常
var ErrInvalidName = errors.New("prize: invalid prize name")

// AnPrize 活动奖品配置
type AnPrize struct {
	ID          int64  `gorm:"column:id" json:"id"`
	Name        string `gorm:"column:name" json:"name"`
	Pic         string `gorm:"column:pic" json:"pic"`
	EventID     int64  `gorm:"column:event_id" json:"event_id"`
	Probability int    `gorm:"column:probability" json:"-"` // 中奖概率
	Num         int    `gorm:"column:num" json:"-"`         // 奖品数量

	NameInfo PrizeNameInfo `gorm:"-" json:"-"`
}

// PrizeNameInfo 奖品名信息
type PrizeNameInfo struct {
	PoolID  int64
	GoodsID int64
	Name    string
}

// TableName of AnPrize
func (AnPrize) TableName() string {
	return "an_prize"
}

// AfterFind is a GORM hook for query
func (ap *AnPrize) AfterFind() error {
	if ap.Pic == "" {
		return nil
	}
	// WORKAROUND: 兼容老数据，以后可以只用 Storage.Parse
	url, err := url.Parse(ap.Pic)
	if err != nil {
		return err
	}
	switch url.Scheme {
	case "http":
	case "https":
	default:
		ap.Pic = service.Storage.Parse(ap.Pic)
	}
	return nil
}

// listPrizes 获取 eventID 的奖品设置
func listPrizes(eventID int64) ([]AnPrize, error) {
	var prizes []AnPrize
	err := service.DB.Where("event_id = ?", eventID).Find(&prizes).Error
	return prizes, err
}

// IsPrize 根据正则表达式提取 name 中的第一个匹配项
func IsPrize(name string, regex *regexp.Regexp) (int, bool) {
	s := regex.FindStringSubmatch(strings.TrimSpace(name))
	if len(s) < 2 {
		return 0, false
	}
	n, err := strconv.Atoi(s[1])
	if err != nil {
		return 0, false
	}
	return n, true
}

// ParseName 解析名称
/*
	格式 1：${name}
	格式 2：#${pool_id},${goods_id} ${name}
	格式 2 的 ${pool_id} ${goods_id} 可以不存在
*/
func (ap *AnPrize) ParseName() error {
	if !strings.HasPrefix(ap.Name, "#") {
		// 格式 1
		ap.NameInfo.Name = ap.Name
		return nil
	}
	r := strings.SplitN(ap.Name, " ", 2)
	if len(r) != 2 {
		return ErrInvalidName
	}
	ap.NameInfo.Name = strings.TrimSpace(r[1])
	if r[0] == "#" {
		// 单独 # 的情况
		return nil
	}
	s := strings.Split(r[0][1:], ",")
	for i := 0; i < 2 && i < len(s); i++ {
		num, err := strconv.ParseInt(s[i], 10, 64)
		if err != nil {
			logger.Error(err)
			return ErrInvalidName
		}
		switch i {
		case 0:
			ap.NameInfo.PoolID = num
		case 1:
			ap.NameInfo.GoodsID = num
		}
	}
	return nil
}

// FindEventPrizes 查询活动奖品（解析过 name）
func FindEventPrizes(eventID int64) ([]AnPrize, error) {
	key := keys.LocalKeyEventAnPrize1.Format(eventID)
	v, ok := service.Cache5Min.Get(key)
	if ok {
		prizes := v.([]AnPrize)
		ret := make([]AnPrize, len(prizes))
		copy(ret, prizes)
		return ret, nil
	}
	allPrizes, err := listPrizes(eventID)
	if err != nil {
		return nil, err
	}
	for i := range allPrizes {
		err = allPrizes[i].ParseName()
		if err != nil {
			return nil, err
		}
	}
	sort.Slice(allPrizes, func(i, j int) bool {
		return allPrizes[i].Probability < allPrizes[j].Probability
	})
	service.Cache5Min.SetDefault(key, allPrizes)
	ret := make([]AnPrize, len(allPrizes))
	copy(ret, allPrizes)
	return ret, nil
}

// FindPrizesByPoolID 通过奖池 ID 查询商品 iD
func FindPrizesByPoolID(eventID int64, poolID int64) ([]AnPrize, error) {
	key := keys.LocalKeyEventAnPrizeByPoolID2.Format(eventID, poolID)
	v, ok := service.Cache5Min.Get(key)
	if ok {
		prizes := v.([]AnPrize)
		ret := make([]AnPrize, len(prizes))
		copy(ret, prizes)
		return ret, nil
	}

	allPrizes, err := FindEventPrizes(eventID)
	if err != nil {
		return nil, err
	}
	poolIDPrizes := make(map[int64][]AnPrize)
	for i := range allPrizes {
		poolIDPrizes[allPrizes[i].NameInfo.PoolID] = append(
			poolIDPrizes[allPrizes[i].NameInfo.PoolID], allPrizes[i])
	}

	// 存入缓存
	ret := make([]AnPrize, 0)
	for p, prizes := range poolIDPrizes {
		sort.Slice(prizes, func(i, j int) bool {
			return prizes[i].Probability < prizes[j].Probability
		})
		poolKey := keys.LocalKeyEventAnPrizeByPoolID2.Format(eventID, p)
		service.Cache5Min.SetDefault(poolKey, prizes)
		if p == poolID {
			ret = make([]AnPrize, len(prizes))
			copy(ret, prizes)
		}
	}
	if len(ret) == 0 {
		// 在没有查询到的情况下防止重复查询此奖池的时候多次访问数据库
		service.Cache5Min.SetDefault(key, make([]AnPrize, 0))
	}
	return ret, nil
}

// FindPrizeByGoodsID 通过商品 ID 查询奖励
func FindPrizeByGoodsID(eventID int64, goodsID int64) (*AnPrize, error) {
	allPrizes, err := FindEventPrizes(eventID)
	if err != nil {
		return nil, err
	}
	for i := range allPrizes {
		if allPrizes[i].NameInfo.GoodsID == goodsID {
			return &allPrizes[i], nil
		}
	}
	return nil, nil
}
