package anprize

import (
	"regexp"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestListPrizes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	eventID := int64(148)

	ap0 := &AnPrize{
		Name:    "测试 test 图片地址",
		Pic:     "test://prize/202102/04/55a168d9d721c860614928ecd859eb6d203355.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap0).Error)
	ap1 := &AnPrize{
		Name:    "测试 http 图片地址",
		Pic:     "http://static.missevan.com/mimages/201610/25/83d35860a4919794761c3f308e9152a1194403.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap1).Error)
	ap2 := &AnPrize{
		Name:    "测试 https 图片地址",
		Pic:     "https://static.missevan.com/mimages/201610/25/83d35860a4919794761c3f308e9152a1194403.png",
		EventID: eventID,
	}
	require.NoError(service.DB.Create(&ap2).Error)
	prizes, err := listPrizes(eventID)
	require.NoError(err)
	assert.GreaterOrEqual(len(prizes), 2)
	var ap0Index, ap1Index, ap2Index int
	var ap0Found, ap1Found, ap2Found bool
	for i, v := range prizes {
		if v.ID == ap0.ID {
			ap0Found = true
			ap0Index = i
		}
		if v.ID == ap1.ID {
			ap1Found = true
			ap1Index = i
		}
		if v.ID == ap2.ID {
			ap2Found = true
			ap2Index = i
		}
	}
	assert.True(ap0Found)
	assert.True(ap1Found)
	assert.True(ap2Found)
	tutil.PrintJSON(prizes[ap0Index])
	assert.False(strings.HasPrefix(prizes[ap0Index].Pic, "oss"))
	assert.Equal(ap1.Pic, prizes[ap1Index].Pic)
	assert.Equal(ap2.Pic, prizes[ap2Index].Pic)
	require.NoError(service.DB.Where("id in (?)", []int64{ap0.ID, ap1.ID, ap2.ID}).Delete(AnPrize{}).Error)
}

func TestIsPrize(t *testing.T) {
	assert := assert.New(t)

	var pointRegex = regexp.MustCompile(`^([1-9]\d*)\s*鱼干$`)

	n, ok := IsPrize(" 20 鱼干 ", pointRegex)
	assert.True(ok)
	assert.Equal(20, n)
	_, ok = IsPrize("二十鱼干", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" 99999999999999999999999 鱼干 ", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" 0 鱼干", pointRegex)
	assert.False(ok)
	_, ok = IsPrize(" -1 鱼干", pointRegex)
	assert.False(ok)
}

func TestAnPrizeParseName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ap := AnPrize{
		Name: "test",
	}
	require.NoError(ap.ParseName())
	assert.Equal(PrizeNameInfo{Name: "test"}, ap.NameInfo)

	ap.Name = "#test"
	assert.Equal(ErrInvalidName, ap.ParseName())

	ap.Name = "# test"
	require.NoError(ap.ParseName())
	assert.Equal(PrizeNameInfo{Name: "test"}, ap.NameInfo)

	ap.Name = "#2 test"
	require.NoError(ap.ParseName())
	assert.Equal(PrizeNameInfo{Name: "test", PoolID: 2}, ap.NameInfo)

	ap.Name = "#1,2,3 test"
	require.NoError(ap.ParseName())
	assert.Equal(PrizeNameInfo{
		PoolID:  1,
		GoodsID: 2,
		Name:    "test",
	}, ap.NameInfo)

	ap.Name = "#a,2,3 test"
	assert.Equal(ErrInvalidName, ap.ParseName())
}

func TestFindEventPrizes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p1, err := FindEventPrizes(205)
	require.NoError(err)
	p2, err := FindEventPrizes(205)
	require.NoError(err)
	require.NotEmpty(p2)
	require.Equal(p1, p2)
	p2[0].EventID = 1
	assert.NotEqual(p1, p2)
}

func TestFindPrizesByPoolID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p1, err := FindPrizesByPoolID(205, 1)
	require.NoError(err)
	p2, err := FindPrizesByPoolID(205, 1)
	require.NoError(err)
	require.NotEmpty(p2)
	require.Equal(p1, p2)
	p2[0].EventID = 1
	assert.NotEqual(p1, p2)

	p1, err = FindPrizesByPoolID(205, 3)
	require.NoError(err)
	assert.Empty(p1)
	assert.NotNil(p1)
	p1, err = FindPrizesByPoolID(205, 3)
	require.NoError(err)
	assert.Empty(p1)
	assert.NotNil(p1)
}

func TestFindPrizeByGoodsID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p, err := FindPrizeByGoodsID(204, 1)
	require.NoError(err)
	assert.NotNil(p)
	p, err = FindPrizeByGoodsID(204, -1)
	require.NoError(err)
	assert.Nil(p)
}
