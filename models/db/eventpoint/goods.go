package eventpoint

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 兑换商品类型
const (
	GoodsTypeBackpackGift = iota + 1
	GoodsTypeAppearance
	// TODO: 定制礼物赠送资格
)

// Goods 商品
type Goods struct {
	ID    int64 `json:"id"`              // 兑换 ID
	Price int64 `json:"price"`           // 兑换所需积分
	Limit int64 `json:"limit,omitempty"` // 本期兑换次数限制

	// 商品类型
	GoodsType int   `json:"goods_type"` // 兑换商品类型（背包礼物，外观等）
	ElementID int64 `json:"element_id"` // 待兑换的物品的 ID（礼物 ID、外观 ID 等）
	Duration  int64 `json:"duration"`   // 商品持续时间（单位：毫秒）
	// 具体商品的类型下的类型
	// 对应关系：外观 - 外观类型
	// TODO: rpc 接口后面改成不需要传外观类型
	ElementType int `json:"element_type,omitempty"`
	// 礼物数量
	Num int `json:"num,omitempty"` // 礼物数量

	prize *anprize.AnPrize // 奖品信息
}

// Send 送出对应商品给用户
func (g Goods) Send(userID int64) {
	switch g.GoodsType {
	case GoodsTypeBackpackGift:
		now := util.TimeNow()
		err := userapi.AddBackpackGift(userID, g.ElementID, g.Num,
			now.Add(time.Millisecond*time.Duration(g.Duration)))
		if err != nil {
			logger.Error(err)
			return
		}
		return
	case GoodsTypeAppearance:
		err := userapi.AddAppearance(userID, g.ElementID, g.ElementType, g.Duration)
		if err != nil {
			logger.Error(err)
			return
		}
		return
	default:
		logger.WithFields(logger.Fields{
			"goods_name": g.GoodsName(),
			"goods_type": g.GoodsType,
		}).Error("未定义的商品类型")
	}
}

// GoodsName 商品名称
func (g Goods) GoodsName() string {
	return g.prize.NameInfo.Name
}
