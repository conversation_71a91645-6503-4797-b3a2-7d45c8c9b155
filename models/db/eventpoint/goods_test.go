package eventpoint

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGoodsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(Goods{}, "id", "price", "limit", "goods_type", "element_id", "duration",
		"element_type", "num")
}

func TestGoodsSend(t *testing.T) {
	assert := assert.New(t)

	var count int
	cancel := mrpc.SetMock(userapi.URIUserBackpackAdd, func(input interface{}) (output interface{}, err error) {
		count++
		return "success", nil
	})

	g := Goods{
		GoodsType: GoodsTypeBackpackGift,
		ElementID: 123,
		Num:       10,
		Duration:  1000,
	}
	g.Send(12)
	assert.Equal(1, count)
	cancel()

	cancel = mrpc.SetMock(userapi.URIUserAppearanceAdd, func(input interface{}) (output interface{}, err error) {
		count++
		return "success", nil
	})
	defer cancel()
	g = Goods{
		GoodsType:   GoodsTypeAppearance,
		ElementID:   123,
		ElementType: 1,
		Duration:    1000,
		prize:       &anprize.AnPrize{},
	}
	g.Send(12)
	assert.Equal(2, count)

	g.GoodsType = 0
	assert.NotPanics(func() { g.Send(12) })
}

func TestGoodsGoodsName(t *testing.T) {
	assert := assert.New(t)

	g := Goods{
		prize: &anprize.AnPrize{
			NameInfo: anprize.PrizeNameInfo{
				Name: "<>",
			},
		},
	}
	assert.Equal("<>", g.GoodsName())
}
