package eventpoint

import (
	"errors"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/models/db/eventuserprize"
	"github.com/MiaoSiLa/missevan-go/models/drawpoint"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 奖池类型
const (
	PoolTypeDraw     = iota + 1 // 抽奖奖池
	PoolTypeExchange            // 兑换奖池
)

// 奖池属性
const (
	PoolAttrTimeLimited = iota + 1 // 限时奖池
)

var (
	randSource = util.NewLockedSource(util.TimeNow().Unix())
)

// DrawConfig 活动积分抽奖配置
type DrawConfig struct {
	TimeOffset int64 `json:"time_offset,omitempty"` // 活动时间偏移，单位：秒

	DrawPools []*PoolConfig `json:"draw_pools"`

	// 抽奖积分相关
	// TODO: 待迁移
	DrawPointDailyUpdate bool `json:"draw_point_daily_update,omitempty"` // 抽奖积分是否是每日更新
}

// PoolConfig 奖池配置
type PoolConfig struct {
	PoolID   int64        `json:"pool_id"`
	PoolType int          `json:"pool_type"`
	Attr     util.BitMask `json:"attr"` // 属性

	Cost int64 `json:"cost,omitempty"` // 每次抽奖价格

	// 限时奖池属性，单位：秒
	StartTime int64 `json:"start_time,omitempty"`
	EndTime   int64 `json:"end_time,omitempty"`

	// 兑换奖池商品
	ShopGoods []*Goods `json:"shop_goods,omitempty"`
}

// UserDrawInfo 用户抽奖积分
type UserDrawInfo struct {
	FreeTimes     int64 `json:"free_times"`     // 免费抽奖的次数
	PurchaseTimes int64 `json:"purchase_times"` // 非免费抽奖的次数
	SupportFree   bool  `json:"support_free"`   // 当前活动是否支持免费抽奖
	Point         int64 `json:"point"`          // 剩余抽奖积分
}

// TimeNow 偏移后的当前时间
func (c *DrawConfig) TimeNow() time.Time {
	if c.TimeOffset == 0 {
		return util.TimeNow()
	}
	return util.TimeNow().Add(time.Duration(c.TimeOffset) * time.Second)
}

// keyDailyFreeDrawUsers 每日免费抽奖过的用户 key
func keyDailyFreeDrawUsers(eventID int64, now time.Time) string {
	date := now.Format(util.TimeFormatYMDWithNoSpace)
	return keys.KeyDailyFreeDrawUsers2.Format(eventID, date)
}

// CurrentDrawPool 当前抽奖奖池
// 抽奖奖池只支持同时生效两个奖池
func (c *DrawConfig) CurrentDrawPool() (pointPool, freePool *PoolConfig) {
	attr := 0
	now := c.TimeNow()
	for _, p := range c.DrawPools {
		if p.PoolType != PoolTypeDraw || !p.InTimeRange(now) {
			continue
		}
		if p.Cost != 0 {
			// 付费奖池
			if (attr & 1) == 0 {
				attr |= 1
				pointPool = p
			} else {
				// TODO: 未记录活动 ID
				logger.Error("同时多个积分奖池生效")
			}
		} else {
			// 免费奖池
			if (attr & 2) == 0 {
				attr |= 2
				freePool = p
			} else {
				// TODO: 未记录活动 ID
				logger.Error("同时多个免费奖池生效")
			}
		}
	}
	return
}

// CurrentExchangePool 当前兑换奖池
func (c *DrawConfig) CurrentExchangePool() *PoolConfig {
	var res *PoolConfig
	now := c.TimeNow()
	for _, p := range c.DrawPools {
		if p.PoolType != PoolTypeExchange || !p.InTimeRange(now) {
			continue
		}
		if res == nil {
			res = p
		} else {
			// TODO: 未记录活动 ID
			logger.Error("同时多个兑换奖池生效")
		}
	}
	return res
}

// InTimeRange 在时间范围内
func (p *PoolConfig) InTimeRange(when time.Time) bool {
	if p.Attr.IsSet(PoolAttrTimeLimited) {
		unix := when.Unix()
		return unix >= p.StartTime && unix < p.EndTime
	}
	return true
}

// DrawParam 抽奖参数
type DrawParam struct {
	Event      *mevent.Simple
	UserID     int64
	DrawConfig *DrawConfig

	PointPool *PoolConfig
	FreePool  *PoolConfig
}

// NewDrawParam new DrawParam
func NewDrawParam(event *mevent.Simple, userID int64, drawConfig *DrawConfig) *DrawParam {
	param := &DrawParam{
		Event:      event,
		UserID:     userID,
		DrawConfig: drawConfig,
	}
	param.PointPool, param.FreePool = drawConfig.CurrentDrawPool()
	return param
}

// UserDrawInfo 用户抽奖信息
// point: 用户总的抽奖积分
func (param *DrawParam) UserDrawInfo() (*UserDrawInfo, error) {
	c := param.DrawConfig
	now := c.TimeNow()
	info := new(UserDrawInfo)
	p := drawpoint.Param{
		EventID:     param.Event.ID,
		UserID:      param.UserID,
		CurrentTime: now.Unix(),
		DailyUpdate: c.DrawPointDailyUpdate,
	}
	point, err := p.DrawPoint()
	if err != nil {
		return nil, err
	}
	info.Point = point
	if param.PointPool != nil {
		info.PurchaseTimes = point / param.PointPool.Cost
	}

	if param.FreePool != nil {
		info.SupportFree = true
		// 目前只支持一天免费抽奖一次
		key := keyDailyFreeDrawUsers(p.EventID, now)
		exists, err := service.Redis.SIsMember(key, param.UserID).Result()
		if err != nil {
			return nil, err
		}
		if !exists {
			info.FreeTimes = 1
		}
	}

	return info, nil
}

// Draw 用户抽奖
// 奖品为 nil 说明抽奖失败
func (param *DrawParam) Draw() (*UserDrawInfo, *eventuserprize.EventUserPrize, error) {
	info, err := param.UserDrawInfo()
	if err != nil {
		return nil, nil, err
	}

	draw := func(poolID int64) (*eventuserprize.EventUserPrize, error) {
		prizes, err := anprize.FindPrizesByPoolID(param.Event.ID, poolID)
		if err != nil {
			return nil, err
		}
		if len(prizes) == 0 {
			logger.WithFields(logger.Fields{
				"event_id": param.Event.ID,
				"pool_id":  poolID,
			}).Error("奖池奖品未配置")
			return nil, errors.New("奖品未配置")
		}
		infinitePrizeIndex := -1
		w := make([]int, len(prizes))
		for i := range prizes {
			w[i] = prizes[i].Probability
			if prizes[i].Num == -1 {
				infinitePrizeIndex = i
			}
		}
		if infinitePrizeIndex == -1 {
			return nil, errors.New("奖品配置异常")
		}
		d, err := util.NewDiscreteDistribution(w, randSource, false)
		if err != nil {
			return nil, err
		}
		prize, err := param.selectPrize(prizes[d.NextInt()], prizes[infinitePrizeIndex])
		if err != nil {
			return nil, err
		}
		up := eventuserprize.NewEventUserPrize(param.UserID,
			prize, param.Event.ID)
		err = up.Create()
		if err != nil {
			return nil, err
		}
		return up, nil
	}

	if info.FreeTimes > 0 {
		// 优先使用免费抽奖
		prize, err := draw(param.FreePool.PoolID)
		if err != nil {
			return nil, nil, err
		}
		key := keyDailyFreeDrawUsers(param.Event.ID, param.DrawConfig.TimeNow())
		// 使用事务操作，保证 SAdd 的时候的 key 总是有过期时间
		pipe := service.Redis.TxPipeline()
		pipe.SAdd(key, strconv.FormatInt(param.UserID, 10))
		pipe.Expire(key, param.Event.ExpireDuration())
		_, err = pipe.Exec()
		if err != nil {
			return nil, nil, err
		}
		info.FreeTimes--
		return info, prize, nil
	}
	// 积分抽奖
	if info.PurchaseTimes <= 0 {
		// 抽奖次数不足，直接返回
		return info, nil, nil
	}

	prize, err := draw(param.PointPool.PoolID)
	if err != nil {
		return nil, nil, err
	}
	dp := drawpoint.Param{
		EventID:     param.Event.ID,
		UserID:      param.UserID,
		CurrentTime: param.DrawConfig.TimeNow().Unix(),
		DailyUpdate: param.DrawConfig.DrawPointDailyUpdate,
	}
	point, err := dp.MinusDrawPoint(param.PointPool.Cost,
		param.Event.ExpireDuration())
	if err != nil {
		return nil, nil, err
	}
	info.Point = point
	info.PurchaseTimes--
	return info, prize, nil
}

// ListMyPrize 中奖记录
func (param *DrawParam) ListMyPrize(p,
	pageSize int64) ([]eventuserprize.EventUserPrize, util.Pagination, error) {
	prizes, err := anprize.FindEventPrizes(param.Event.ID)
	if err != nil {
		return nil, util.Pagination{}, err
	}
	if len(prizes) == 0 {
		return []eventuserprize.EventUserPrize{}, util.MakePagination(0, p, pageSize), nil
	}
	prizeIDs := make([]int64, len(prizes))
	prizeMap := make(map[int64]*anprize.AnPrize, len(prizes))
	for i := range prizes {
		prizeIDs[i] = prizes[i].ID
		prizeMap[prizes[i].ID] = &prizes[i]
	}
	up := eventuserprize.EventUserPrize{
		EventID: param.Event.ID,
		UserID:  param.UserID,
	}
	data, pa, err := up.Find(prizeIDs, p, pageSize)
	if err != nil {
		return nil, util.Pagination{}, err
	}
	for i := range data {
		if prize := prizeMap[data[i].PrizeID]; prize != nil {
			data[i].Name = prize.NameInfo.Name
			data[i].ImageURL = prize.Pic
		}
	}
	return data, pa, nil
}

func (param *DrawParam) selectPrize(prize, infinitePrize anprize.AnPrize) (*anprize.AnPrize, error) {
	if prize.Num == -1 {
		return &prize, nil
	}
	key := keys.KeyEventPrizes1.Format(param.Event.ID)
	field := strconv.FormatInt(prize.ID, 10)
	// 先判断该奖品是否还能被抽出来，不能抽出来则替换成不限数量的奖品
	count, err := service.Redis.HGet(key, field).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	if count >= int64(prize.Num) {
		logger.WithFields(logger.Fields{
			"prize_id": prize.ID,
			"user_id":  param.UserID,
		}).Warn("奖品已抽完，设置返回某个不限数量的奖品")
		return &infinitePrize, nil
	}
	pipe := service.Redis.TxPipeline()
	cmd := pipe.HIncrBy(key, field, 1)
	pipe.Expire(key,
		time.Unix(param.Event.EndTime+30*util.SecondOneDay, 0).Sub(util.TimeNow()))
	_, err = pipe.Exec()
	if err != nil {
		return nil, err
	}
	if cmd.Val() > int64(prize.Num) {
		// 替换成没有数量限制的礼物
		logger.WithFields(logger.Fields{
			"prize_id": prize.ID,
			"user_id":  param.UserID,
		}).Warn("奖品已抽完，设置返回某个不限数量的奖品")
		// 将多余的次数扣回
		err = service.Redis.HIncrBy(key, field, -1).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return &infinitePrize, nil
	}
	return &prize, nil
}

// ExchangeParam 兑换参数
type ExchangeParam struct {
	Event      *mevent.Simple
	UserID     int64
	DrawConfig *DrawConfig

	exchangePool *PoolConfig
	udi          *UserDrawInfo
}

// NewExchangeParam new ExchangeParam
func NewExchangeParam(event *mevent.Simple, userID int64, drawConfig *DrawConfig) *ExchangeParam {
	param := &ExchangeParam{
		Event:        event,
		UserID:       userID,
		DrawConfig:   drawConfig,
		exchangePool: drawConfig.CurrentExchangePool(),
	}
	return param
}

// Goods 通过 id 获取 goods
func (param *ExchangeParam) Goods(id int64) (*Goods, error) {
	if param.exchangePool == nil {
		return nil, nil
	}
	for _, g := range param.exchangePool.ShopGoods {
		if g.ID == id {
			p, err := anprize.FindPrizeByGoodsID(param.Event.ID, id)
			if err != nil {
				return nil, err
			}
			if p == nil {
				return nil, nil
			}
			g.prize = p
			return g, nil
		}
	}
	return nil, nil
}

// Exchange 兑换商品
func (param *ExchangeParam) Exchange(g *Goods) (*UserDrawInfo, *eventuserprize.EventUserPrize, error) {
	info, err := param.UserDrawInfo()
	if err != nil {
		return nil, nil, err
	}
	if info.Point < g.Price {
		return info, nil, nil
	}

	up := eventuserprize.NewEventUserPrize(param.UserID, g.prize, param.Event.ID)
	err = up.Create()
	if err != nil {
		return nil, nil, err
	}
	g.Send(param.UserID)

	err = param.incExchangeNum(g)
	if err != nil {
		return nil, nil, err
	}

	dp := drawpoint.Param{
		EventID:     param.Event.ID,
		UserID:      param.UserID,
		CurrentTime: param.DrawConfig.TimeNow().Unix(),
		DailyUpdate: param.DrawConfig.DrawPointDailyUpdate,
	}
	point, err := dp.MinusDrawPoint(g.Price, param.Event.ExpireDuration())
	if err != nil {
		return nil, nil, err
	}
	info.Point = point
	return info, up, nil
}

// UserDrawInfo 兑换商店的积分信息
func (param *ExchangeParam) UserDrawInfo() (*UserDrawInfo, error) {
	if param.udi != nil {
		return param.udi, nil
	}
	c := param.DrawConfig
	now := c.TimeNow()
	p := drawpoint.Param{
		EventID:     param.Event.ID,
		UserID:      param.UserID,
		CurrentTime: now.Unix(),
	}
	point, err := p.DrawPoint()
	if err != nil {
		return nil, err
	}
	param.udi = &UserDrawInfo{Point: point}
	return param.udi, nil
}

// incExchangeNum 增加兑换次数
func (param *ExchangeParam) incExchangeNum(g *Goods) error {
	key := keys.KeyEventUserExchangeCount2.Format(param.Event.ID, param.UserID)
	field := strconv.FormatInt(g.ID, 10)
	pipe := service.Redis.TxPipeline()
	pipe.HIncrBy(key, field, 1)
	pipe.Expire(key, param.Event.ExpireDuration())
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	return nil
}

// ExchangeNum 兑换数量
func (param *ExchangeParam) ExchangeNum(g *Goods) (int64, error) {
	key := keys.KeyEventUserExchangeCount2.Format(param.Event.ID, param.UserID)
	field := strconv.FormatInt(g.ID, 10)
	num, err := service.Redis.HGet(key, field).Int64()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			err = nil
		}
		return 0, err
	}
	return num, nil
}
