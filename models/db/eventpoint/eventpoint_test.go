package eventpoint

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/db/anprize"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(DrawConfig{}, "time_offset", "draw_pools", "draw_point_daily_update")
	kc.Check(PoolConfig{}, "pool_id", "pool_type", "attr", "cost", "start_time", "end_time", "shop_goods")
	kc.Check(UserDrawInfo{}, "free_times", "purchase_times", "support_free", "point")
}

func TestDrawConfigTimeNow(t *testing.T) {
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time { return time.Unix(0, 0) })
	defer util.SetTimeNow(nil)

	c := DrawConfig{TimeOffset: 0}
	assert.Equal(time.Unix(0, 0), c.TimeNow())
	c.TimeOffset = 10
	assert.Equal(time.Unix(10, 0), c.TimeNow())
}

func TestKeyDailyFreeDrawUsers(t *testing.T) {
	assert := assert.New(t)

	now := time.Date(2022, 06, 02, 0, 0, 0, 0, time.Local)
	key := keyDailyFreeDrawUsers(1, now)
	assert.Equal("daily_free_draw_users:event_id:1:20220602", key)
}

func TestDrawConfigCurrentDrawPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	c := DrawConfig{
		DrawPools: []*PoolConfig{
			{
				PoolID:   1,
				PoolType: PoolTypeExchange,
			},
			{
				PoolID:   2,
				PoolType: PoolTypeDraw,
			},
			{
				PoolID:    3,
				PoolType:  PoolTypeDraw,
				Attr:      1, // 限时
				Cost:      1,
				StartTime: now.Unix() - 10,
				EndTime:   now.Unix() - 1,
			},
			{
				PoolID:    4,
				PoolType:  PoolTypeDraw,
				Attr:      1, // 限时
				Cost:      1,
				StartTime: now.Unix() - 1,
				EndTime:   now.Unix() + 10,
			},
			{
				PoolID:   5, // 重复的免费奖池
				PoolType: PoolTypeDraw,
			},
			{
				PoolID:   6, // 重复的付费奖池
				PoolType: PoolTypeDraw,
				Cost:     1,
			},
		},
	}
	pointPool, freePool := c.CurrentDrawPool()
	require.NotNil(pointPool)
	require.NotNil(freePool)
	assert.EqualValues(4, pointPool.PoolID)
	assert.EqualValues(2, freePool.PoolID)
}

func TestDrawConfigCurrentExchangePool(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	c := DrawConfig{
		DrawPools: []*PoolConfig{
			{
				PoolID:   1,
				PoolType: PoolTypeExchange,
			},
			{
				PoolID:   2,
				PoolType: PoolTypeDraw,
			},
			{
				PoolID:    3,
				PoolType:  PoolTypeExchange,
				Attr:      1, // 限时
				Cost:      1,
				StartTime: now.Unix() - 10,
				EndTime:   now.Unix() + 1,
			},
		},
	}
	pointPool := c.CurrentExchangePool()
	assert.NotNil(pointPool)
}

func TestPoolConfigInTimeRange(t *testing.T) {
	assert := assert.New(t)

	p := PoolConfig{}
	now := util.TimeNow()
	assert.True(p.InTimeRange(now))
	p.Attr.Set(PoolAttrTimeLimited)
	assert.False(p.InTimeRange(now))
	p.StartTime = now.Unix()
	p.EndTime = now.Unix() + 10
	assert.True(p.InTimeRange(now))
}

func TestNewDrawParam(t *testing.T) {
	assert := assert.New(t)

	param := NewDrawParam(&mevent.Simple{}, 12, &DrawConfig{
		DrawPools: []*PoolConfig{{PoolType: PoolTypeDraw}},
	})
	assert.NotNil(param.FreePool)
}

func TestDrawParamUserDrawInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 06, 28, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)
	param := DrawParam{
		Event:      &mevent.Simple{ID: 100},
		UserID:     12,
		DrawConfig: &DrawConfig{},
		PointPool:  &PoolConfig{Cost: 2},
		FreePool:   &PoolConfig{},
	}

	freeKey := keyDailyFreeDrawUsers(param.Event.ID, param.DrawConfig.TimeNow())
	require.NoError(service.Redis.SRem(freeKey, param.UserID).Err())
	pointKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(param.Event.ID)
	require.NoError(service.Redis.ZAdd(pointKey, &redis.Z{Score: 5, Member: param.UserID}).Err())

	info, err := param.UserDrawInfo()
	require.NoError(err)
	assert.True(info.SupportFree)
	assert.EqualValues(1, info.FreeTimes)
	assert.EqualValues(2, info.PurchaseTimes)
	assert.EqualValues(5, info.Point)
}

func TestDrawParamDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 06, 28, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	now := util.TimeNow()
	pointPool := &PoolConfig{PoolID: 1, Cost: 2}
	param := DrawParam{
		Event:      &mevent.Simple{ID: 100, EndTime: now.Unix() + 10},
		UserID:     12,
		DrawConfig: &DrawConfig{},
		PointPool:  pointPool,
		FreePool:   &PoolConfig{PoolID: 2},
	}
	freeKey := keyDailyFreeDrawUsers(param.Event.ID, param.DrawConfig.TimeNow())
	require.NoError(service.Redis.SRem(freeKey, param.UserID).Err())
	pointKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(param.Event.ID)
	require.NoError(service.Redis.ZRem(pointKey, param.UserID).Err())

	_, _, err := param.Draw()
	assert.EqualError(err, "奖品未配置")

	prizeKey1 := keys.LocalKeyEventAnPrizeByPoolID2.Format(param.Event.ID,
		pointPool.PoolID)
	prizeKey2 := keys.LocalKeyEventAnPrizeByPoolID2.Format(param.Event.ID,
		param.FreePool.PoolID)
	// 未配置无限奖品
	service.Cache5Min.SetDefault(prizeKey2, []anprize.AnPrize{
		{Num: 100, Probability: 1, NameInfo: anprize.PrizeNameInfo{Name: "free"}},
	})
	_, _, err = param.Draw()
	assert.EqualError(err, "奖品配置异常")

	service.Cache5Min.SetDefault(prizeKey1, []anprize.AnPrize{
		{Num: 100, Probability: 1, NameInfo: anprize.PrizeNameInfo{Name: "point"}},
		{Num: -1, Probability: 1, NameInfo: anprize.PrizeNameInfo{Name: "point"}},
	})
	service.Cache5Min.SetDefault(prizeKey2, []anprize.AnPrize{
		{Num: 100, Probability: 1, NameInfo: anprize.PrizeNameInfo{Name: "free"}},
		{Num: -1, Probability: 1, NameInfo: anprize.PrizeNameInfo{Name: "free"}},
	})

	// 免费抽奖
	info, prize, err := param.Draw()
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal("free", prize.Name)
	require.NotNil(info)
	assert.True(info.SupportFree)
	assert.Zero(info.FreeTimes)
	// 当日第二次免费抽
	_, prize, err = param.Draw()
	require.NoError(err)
	assert.Nil(prize)
	// 没有付费奖池的情况
	param.PointPool = nil
	_, prize, err = param.Draw()
	require.NoError(err)
	assert.Nil(prize)

	// 积分抽奖
	param.PointPool = pointPool
	require.NoError(service.Redis.ZIncrBy(pointKey, float64(pointPool.Cost)*2+1,
		strconv.FormatInt(param.UserID, 10)).Err())
	info, prize, err = param.Draw()
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal("point", prize.Name)
	require.NotNil(info)
	assert.EqualValues(1, info.PurchaseTimes)
	assert.EqualValues(3, info.Point)
	// 没有免费奖池的情况
	param.FreePool = nil
	info, prize, err = param.Draw()
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal("point", prize.Name)
	require.NotNil(info)
	assert.Zero(info.PurchaseTimes)
	assert.EqualValues(1, info.Point)
	// 抽奖次数用光
	info, prize, err = param.Draw()
	require.NoError(err)
	assert.Nil(prize)
	require.NotNil(info)
	assert.Zero(info.PurchaseTimes)
	assert.EqualValues(1, info.Point)
}

func TestDrawParamSelectPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := DrawParam{
		Event: &mevent.Simple{ID: 100, EndTime: util.TimeNow().Unix()},
	}
	normalPrize := anprize.AnPrize{
		Num:  1,
		Name: "normal",
	}
	infinitePrize := anprize.AnPrize{
		Num:  -1,
		Name: "infinite",
	}
	prize, err := param.selectPrize(infinitePrize, infinitePrize)
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal(infinitePrize, *prize)

	key := keys.KeyEventPrizes1.Format(param.Event.ID)
	field := strconv.FormatInt(normalPrize.ID, 10)
	require.NoError(service.Redis.HDel(key, field).Err())

	prize, err = param.selectPrize(normalPrize, infinitePrize)
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal(normalPrize, *prize)

	prize, err = param.selectPrize(normalPrize, infinitePrize)
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal(infinitePrize, *prize)
}

func TestNewExchangeParam(t *testing.T) {
	assert := assert.New(t)

	param := NewExchangeParam(&mevent.Simple{}, 12, &DrawConfig{
		DrawPools: []*PoolConfig{{PoolType: PoolTypeExchange}},
	})
	assert.NotNil(param.exchangePool)
}

func TestExchangeParamGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := ExchangeParam{
		Event: &mevent.Simple{ID: 204},
		exchangePool: &PoolConfig{
			ShopGoods: []*Goods{
				{ID: 1},
				{ID: 2},
			},
		},
	}
	// 正常查询
	g, err := param.Goods(1)
	require.NoError(err)
	assert.NotNil(g)
	// an_prize 没有对应数据
	g, err = param.Goods(2)
	require.NoError(err)
	assert.Nil(g)
	// 配置没有对应数据
	g, err = param.Goods(3)
	require.NoError(err)
	assert.Nil(g)
	// 没有存在的奖池
	param.exchangePool = nil
	g, err = param.Goods(1)
	require.NoError(err)
	assert.Nil(g)
}

func TestExchangeParamExchange(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 06, 28, 0, 0, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)

	now := util.TimeNow()
	pool := &PoolConfig{PoolID: 1, PoolType: PoolTypeExchange}
	g := &Goods{
		GoodsType: 0,
		Price:     10,
		prize:     &anprize.AnPrize{ID: 1},
	}
	param := ExchangeParam{
		Event:        &mevent.Simple{ID: 100, EndTime: now.Unix() + 10},
		UserID:       12,
		DrawConfig:   &DrawConfig{},
		exchangePool: pool,
	}

	pointKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(param.Event.ID)
	require.NoError(service.Redis.ZRem(pointKey, param.UserID).Err())
	_, prize, err := param.Exchange(g)
	require.NoError(err)
	assert.Nil(prize, "积分不足")

	require.NoError(service.Redis.ZIncrBy(pointKey, float64(g.Price),
		strconv.FormatInt(param.UserID, 10)).Err())
	param.udi = nil
	info, prize, err := param.Exchange(g)
	require.NoError(err)
	assert.NotNil(info)
	assert.NotNil(prize)
}

func TestExchangeParamUserDrawInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := ExchangeParam{
		Event:      &mevent.Simple{ID: 100},
		UserID:     12,
		DrawConfig: &DrawConfig{},
	}
	pointKey := serviceredis.KeyActivityUserDrawPointEvent1.Format(param.Event.ID)
	require.NoError(service.Redis.ZRem(pointKey, param.UserID).Err())

	info, err := param.UserDrawInfo()
	require.NoError(err)
	require.NotNil(info)
	assert.NotNil(param.udi)
	assert.Zero(info.Point)

	param.udi.Point = 10
	info, err = param.UserDrawInfo()
	require.NoError(err)
	require.NotNil(info)
	assert.EqualValues(10, info.Point)
}

func TestExchangeParamExchangeNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := ExchangeParam{
		Event:  &mevent.Simple{ID: 100, EndTime: util.TimeNow().Unix()},
		UserID: 12,
	}

	g := &Goods{ID: 10}
	before, err := param.ExchangeNum(g)
	require.NoError(err)

	err = param.incExchangeNum(g)
	require.NoError(err)

	after, err := param.ExchangeNum(g)
	require.NoError(err)
	assert.Equal(after, before+1)
}
