package anmsg

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// status position
const (
	StatusPosterBigID = 1 << 2
)

const tableName = "an_msg"

// AnMsg model
type AnMsg struct {
	ID        int64  `gorm:"column:id;primary_key"`
	SmallID   int64  `gorm:"column:small_id"` // 会话双方中小的 user_id
	BigID     int64  `gorm:"column:big_id"`   // 会话双方中大的 user_id
	PostName  string `gorm:"column:post_name"`
	PostIcon  string `gorm:"column:post_icon"`
	PostColor string `gorm:"column:post_color"`
	// status 二进制运算：
	// 位 1 是否已读（0：已读，1：未读）
	// 位 2 是否是客服（0：非客服，1：客服）
	// 位 3 发信方（0：small_id，1：big_id）
	// 位 4、位 5 关闭会话方（位 4 small_id 关闭房间，位 5 big_id 关闭房间）
	// 位 6、位 7 是否显示这条消息（0：显示；1：不显示）（位 6 small_id 显示，位 7 big_id 显示）
	// 位 8、位 9 位是否是拉黑的消息（0：显示；1：拉黑）（位 8 small_id 拉黑 big_id，位 9 big_id 拉黑 small_id
	Status util.BitMask `gorm:"column:status"` // 消息属性
	Msg    string       `gorm:"column:msg"`    // 私信内容
	Type   int          `gorm:"column:type"`   // 私信内容的类型，0：纯文本，1：HTML
	CTime  int64        `gorm:"column:ctime"`  // 发送时间
}

// DB the db instance of AnMsg model
func (AnMsg) DB() *gorm.DB {
	return service.MessageDB.Table(AnMsg{}.TableName())
}

// TableName for AnMsg model
func (AnMsg) TableName() string {
	return tableName
}

// BeforeCreate hook
func (s *AnMsg) BeforeCreate(scope *gorm.Scope) error {
	now := util.TimeNow().Unix()
	s.CTime = now
	return nil
}

// Exists fromUserID 是否给 toUserID 发过私信
func Exists(fromUserID, toUserID int64) (bool, error) {
	smallID, bigID := fromUserID, toUserID
	status := 0
	if fromUserID > toUserID {
		smallID, bigID = toUserID, fromUserID
		status = StatusPosterBigID
	}
	query := AnMsg{}.DB().Where("small_id = ? AND big_id = ? AND (status & ? = ?)", smallID, bigID, StatusPosterBigID, status)
	exists, err := servicedb.Exists(query)
	if err != nil {
		return false, err
	}
	return exists, nil
}
