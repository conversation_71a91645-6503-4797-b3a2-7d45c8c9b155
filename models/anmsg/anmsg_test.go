package anmsg

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除私信
	require.NoError(service.MessageDB.Delete(AnMsg{}, "small_id = ? AND big_id = ?", 12, 13).Error)

	// 测试发信人未给收信人发送私信
	ok, err := Exists(12, 13)
	require.NoError(err)
	assert.False(ok)

	// 插入数据
	msg := &AnMsg{
		SmallID: 12,
		BigID:   13,
		Msg:     "SmallID 给 BigID 发私信",
		Status:  0}
	require.NoError(service.MessageDB.Create(msg).Error)

	// 测试发信人发私信给收信人
	ok, err = Exists(12, 13)
	require.NoError(err)
	assert.True(ok)
}
