package role

import (
	"database/sql"
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
)

// Role 角色名
type Role string

// 角色定义
const (
	LiveAdmin     Role = "liveadmin"     // 直播管理员
	LiveFinance   Role = "livefinance"   // 直播财务
	LiveJudgement Role = "livejudgement" // 直播风纪委员
	LiveOperator  Role = "liveoperator"  // 直播运营
	LiveResource  Role = "live_resource" // 直播 - 资源组

	GuildAccountAdmin Role = "guildaccountadmin" // 公会账户管理员

	AuditGroup Role = "auditgroup" // 审核组
	AuditLive  Role = "audit_live" // 审核 - 直播组

	ServiceGroup  Role = "servicegroup"  // 客服组
	QAGroup       Role = "qagroup"       // 测试组
	Developer     Role = "developer"     // 开发技术人员
	SubtitleAdmin Role = "subtitleadmin" // 字幕管理员

	YearlyReportAdmin Role = "yearlyreportadmin" // 年度报告管理员（可看其他用户数据）

	OperationSeniorPlatformEdit   Role = "operation_senior_platform_edit"   // 运营 - 高级平台编辑
	OperationSuperiorPlatformEdit Role = "operation_superior_platform_edit" // 运营 - 资深平台编辑
)

// 表名常量
const (
	tableAssignment = "authassignment"
)

// IsRole userID 的用户是否具有角色 r 其中之一
func IsRole(userID int64, r ...Role) (bool, error) {
	if len(r) == 0 {
		return false, nil
	}
	id := strconv.FormatInt(userID, 10)
	err := filterAuthAssignmentByPK(service.DB, userID, r...).
		Select("userid").Row().Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// AuthAssignment model
type AuthAssignment struct {
	ItemName string `gorm:"column:itemname"`
	UserID   string `gorm:"column:userid"`
}

// TableName of AuthAssignment model
func (AuthAssignment) TableName() string {
	return tableAssignment
}

// DeleteAuthAssignment deletes one record
func DeleteAuthAssignment(userID int64, r Role) error {
	return filterAuthAssignmentByPK(service.DB, userID, r).Delete("").Error
}

func filterAuthAssignmentByPK(db *gorm.DB, userID int64, r ...Role) *gorm.DB {
	id := strconv.FormatInt(userID, 10)
	db = db.Table(tableAssignment).Where("userid = ? AND itemname IN (?)", id, r)
	return db
}

// FindRolesByUserID find roles by userID
func FindRolesByUserID(userID int64) ([]string, error) {
	var itemNameList []string
	err := service.DB.Table(tableAssignment).Where("userid = ?", userID).Pluck("itemname", &itemNameList).Error
	if err != nil {
		return nil, err
	}
	if itemNameList == nil {
		itemNameList = []string{}
	}
	return itemNameList, nil
}

// FindUserIDsByRoles 查询拥有某些角色的用户 ID
func FindUserIDsByRoles(roles []string) ([]int64, error) {
	var userIDStr []string
	err := service.DB.Table(tableAssignment).
		Where("itemname IN (?)", roles).Pluck("DISTINCT userid", &userIDStr).Error
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(userIDStr))
	for i := range userIDStr {
		userID, err := strconv.ParseInt(userIDStr[i], 10, 64)
		if err != nil {
			logger.WithField("userid", userIDStr[i]).Error(err)
			continue
			// PASS
		}
		userIDs = append(userIDs, userID)
	}
	return userIDs, nil
}
