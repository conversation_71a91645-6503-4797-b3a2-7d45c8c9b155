package role

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestIsRole(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testID    int64 = 12
		testIDstr       = "12"
	)

	assignment := AuthAssignment{
		UserID:   testIDstr,
		ItemName: string(LiveAdmin),
	}
	err := service.DB.FirstOrCreate(&assignment, assignment).Error
	require.NoError(err)

	assert.True(IsRole(testID, LiveAdmin))
	assert.True(IsRole(testID, LiveAdmin, LiveJudgement))

	assert.False(IsRole(999, LiveAdmin))
	assert.False(IsRole(999, LiveAdmin, LiveJudgement))
}

func TestFindRolesByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roles, err := FindRolesByUserID(9074508)
	require.NoError(err)
	assert.NotNil(roles)
	assert.Zero(len(roles))

	roles, err = FindRolesByUserID(9074509)
	require.NoError(err)
	assert.Equal(3, len(roles))
}

func TestFindUserIDsByRoles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(9074509)
	roles, err := FindRolesByUserID(userID)
	require.NoError(err)
	assert.Greater(len(roles), 2)

	userIDs, err := FindUserIDsByRoles([]string{string(roles[0]), string(roles[1])})
	require.NoError(err)
	var count int
	for i := range userIDs {
		if userIDs[i] == userID {
			count++
		}
	}
	assert.Equal(1, count)
}
