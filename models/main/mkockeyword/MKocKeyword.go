package mkockeyword

import (
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "m_koc_keyword"

// MKocKeyword KOC 关键词
type MKocKeyword struct {
	ID           int64  `gorm:"column:id" json:"-"`
	CreateTime   int64  `gorm:"column:create_time" json:"-"`           // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`         // 更新时间
	Keyword      string `gorm:"column:keyword" json:"keyword"`         // 关键词
	TargetWord   string `gorm:"column:target_word" json:"target_word"` // 承接内容原名
	TargetID     int64  `gorm:"column:target_id" json:"target_id"`     // 承接内容原名关联 ID
	Status       int    `gorm:"column:status" json:"-"`                // 状态 0：已停止；1：生效中
	Attr         int    `gorm:"column:attr" json:"-"`                  // KOC 关键词属性：1 位是否计入 koc 报表（1 为是）
}

// DB the db instance of MKocKeyword model
func (mkk MKocKeyword) DB() *gorm.DB {
	return service.MainDB.Table(mkk.TableName())
}

// TableName for MKocKeyword model
func (mkk MKocKeyword) TableName() string {
	return tableName
}

// Reassign 使用承接内容原名获取置顶剧集的剧集 ID
func Reassign(p *search.Params) {
	defer func() {
		if p.AdapterKeywordTargetID == nil {
			// 标记经过承接逻辑，但未找到 target_id
			p.AdapterKeywordTargetID = util.NewInt64(0)
		}
	}()
	key := serviceredis.KeyOpenSearchKocKeywordTargetID0.Format()

	tws, err := service.LRURedis.HMGet(key, p.Keyword, "").Result()
	if err != nil {
		logger.WithField("keyword", p.Keyword).Error("从 redis 获取 koc 关键词数据失败")
		return
	}
	targetIDStr := tws[0]
	existKey := tws[1]
	// existKey 不等于 nil 说明 redis key 存在
	if existKey != nil {
		if targetIDStr != nil {
			targetID, err := strconv.ParseInt(targetIDStr.(string), 10, 64)
			if err != nil {
				logger.WithField("keyword", p.Keyword).Error("redis 数据异常")
				return
			}
			p.AdapterKeywordTargetID = &targetID
		}
		return
	}

	// 设置并发锁，防止生成缓存时的并发情况
	lockKey := serviceredis.LockOpenSearchKocKeywordTargetWord0.Format()
	lock, err := service.LRURedis.SetNX(lockKey, 1, 10*time.Second).Result()
	if err != nil {
		logger.WithField("keyword", p.Keyword).Error("redis 设置并发锁失败")
		// PASS
	}
	if !lock {
		return
	}
	// 删除锁
	defer service.LRURedis.Del(lockKey)

	var kk []MKocKeyword
	err = MKocKeyword{}.DB().Select("keyword, target_id").Where("status = ?", StatusActive).Find(&kk).Error
	if err != nil {
		logger.WithField("keyword", p.Keyword).Error("从数据库获取 koc 关键词数据失败")
		// PASS
	}

	keywordMap := make(map[string]interface{}, len(kk))
	for _, i := range kk {
		keywordMap[i.Keyword] = i.TargetID
	}

	// 总是设置空字符串缓存，防止缓存穿透
	keywordMap[""] = 0

	pipe := service.LRURedis.TxPipeline()
	pipe.HMSet(key, keywordMap)
	pipe.Expire(key, 12*time.Hour)
	_, err = pipe.Exec()
	if err != nil {
		logger.WithField("keyword", p.Keyword).Error("redis 设置 koc 关键词数据失败")
		// PASS
	}

	if val, ok := keywordMap[p.Keyword]; ok {
		p.AdapterKeywordTargetID = util.NewInt64(val.(int64))
	}
}
