package mkockeyword

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMKocKeywordTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MKocKeyword{}, "id", "create_time", "modified_time", "keyword", "target_word", "target_id", "status", "attr")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MKocKeyword{}, "keyword", "target_word", "target_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_koc_keyword", tableName)
	assert.Equal("m_koc_keyword", MKocKeyword{}.TableName())
}

func TestReassign(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := serviceredis.KeyOpenSearchKocKeywordTargetID0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试 KOC 关键词生效中，无缓存
	searchParam := search.Params{
		Keyword: "傻狗男友",
	}
	Reassign(&searchParam)
	assert.Equal(int64(1), *searchParam.AdapterKeywordTargetID)
	// 测试 KOC 关键词生效中，有缓存
	searchParam = search.Params{
		Keyword: "楚白之恋",
	}
	Reassign(&searchParam)
	assert.Equal(int64(3), *searchParam.AdapterKeywordTargetID)
	// 测试缓存是否生成
	tw, err := service.LRURedis.HGet(key, "傻狗男友").Result()
	assert.NoError(err)
	assert.Equal("1", tw)
	tw, err = service.LRURedis.HGet(key, "楚白之恋").Result()
	assert.NoError(err)
	assert.Equal("3", tw)
	// 测试缓存时间
	ttl := service.LRURedis.TTL(key).Val()
	assert.Equal(ttl, 12*time.Hour)

	// 测试 KOC 关键词已停用
	searchParam.Keyword = "糖醋小排骨"
	Reassign(&searchParam)
	assert.Equal("糖醋小排骨", searchParam.Keyword)
	_, err2 := service.LRURedis.HGet(key, "糖醋小排骨").Result()
	assert.Error(redis.Nil, err2)

	// 测试关键词不在 KOC 关键词表中
	searchParam.Keyword = "测试不在 KOC 关键词表中"
	Reassign(&searchParam)
	assert.Equal("测试不在 KOC 关键词表中", searchParam.Keyword)
	_, err3 := service.LRURedis.HGet(key, "测试不在 KOC 关键词表中").Result()
	assert.Error(redis.Nil, err3)

	// 测试缓存穿透
	// 清空缓存和数据库数据
	require.NoError(service.LRURedis.Del(key).Err())
	require.NoError(MKocKeyword{}.DB().Where("status = ?", StatusActive).Delete("").Error)
	searchParam.Keyword = "测试缓存穿透"
	Reassign(&searchParam)
	assert.Equal("测试缓存穿透", searchParam.Keyword)
	// 确认空缓存是否生成
	res := service.LRURedis.Exists(key).Val()
	assert.Equal(int64(1), res)
	m, err := service.LRURedis.HGetAll(key).Result()
	assert.NoError(err)
	assert.Len(m, 1)
	val, ok := m[""]
	assert.Equal("0", val)
	assert.True(ok)
}
