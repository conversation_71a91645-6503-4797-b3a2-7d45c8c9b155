package msearchinterventionkeyword

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestMSearchInterventionKeywordTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MSearchInterventionKeyword{}, "id", "create_time", "modified_time", "keyword",
		"target_id", "target_index", "target_type", "status", "start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_search_intervention_keyword", MSearchInterventionKeyword{}.TableName())
}

func TestFindActiveFixedParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	getTestRedisKey := func(searchType int, keyword string) string {
		return serviceredis.KeyOpenSearchSearchInterventionTypeKeywordMD5FixedParams2.Format(searchType, util.MD5(keyword))
	}

	// 测试无缓存
	util.SetTimeNow(func() time.Time {
		return time.Unix(1657679280, 0)
	})
	defer util.SetTimeNow(nil)
	testRedisKey := getTestRedisKey(search.TypeDrama, "貌合神离")
	val := service.LRURedis.Exists(testRedisKey).Val()
	require.Zero(val)
	activeFixedParams := findActiveFixedParams(search.TypeDrama, "貌合神离")
	assert.Len(activeFixedParams, 3)
	for i := 1; i < len(activeFixedParams); i++ {
		assert.Greater(activeFixedParams[i].TargetIndex, activeFixedParams[i-1].TargetIndex)
	}
	defer service.LRURedis.Del(testRedisKey)

	// 测试有缓存
	val = service.LRURedis.Exists(testRedisKey).Val()
	require.Greater(val, int64(0))
	activeFixedParams = findActiveFixedParams(search.TypeDrama, "貌合神离")
	assert.Len(activeFixedParams, 3)
	// 测试配置参数中的数据都是生效数据
	targetIDs := make([]int64, 0, len(activeFixedParams))
	for _, param := range activeFixedParams {
		targetIDs = append(targetIDs, param.TargetID)
	}
	var testMSearchInterventionKeywords []MSearchInterventionKeyword
	err := MSearchInterventionKeyword{}.DB().Where("id IN (?)", targetIDs).
		Find(&testMSearchInterventionKeywords).Error
	require.NoError(err)
	require.Len(testMSearchInterventionKeywords, len(activeFixedParams))
	for _, searchIntervention := range testMSearchInterventionKeywords {
		assert.Equal(searchIntervention.Status, StatusActive)
	}

	// 模拟 key 过期生成新的缓存
	err = service.LRURedis.Del(testRedisKey).Err()
	require.NoError(err)
	util.SetTimeNow(func() time.Time {
		return time.Unix(1658831747, 0)
	})
	activeFixedParams = findActiveFixedParams(search.TypeDrama, "貌合神离")
	assert.Len(activeFixedParams, 3)

	// 测试生成空数组缓存
	testRedisKey = getTestRedisKey(search.TypeAlbum, "貌合神离")
	val = service.LRURedis.Exists(testRedisKey).Val()
	require.Zero(val)
	activeFixedParams = findActiveFixedParams(search.TypeAlbum, "貌合神离")
	assert.Len(activeFixedParams, 0)
	defer service.LRURedis.Del(testRedisKey)

	// 测试缓存的空数组
	val = service.LRURedis.Exists(testRedisKey).Val()
	require.Greater(val, int64(0))
	activeFixedParams = findActiveFixedParams(search.TypeAlbum, "貌合神离")
	assert.Len(activeFixedParams, 0)
}

func TestSetInsertParams(t *testing.T) {
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1657679280, 0)
	})
	defer util.SetTimeNow(nil)

	// 测试第一页
	params := search.Params{
		Keyword:    "貌合神离",
		SearchType: search.TypeDrama,
		Page:       1,
		PageSize:   20,
	}
	SetInsertParams(&params)
	assert.Len(params.AllFixedTargetIDs, 3)
	assert.Len(params.CurrentActiveFixedParams, 3)
	assert.Equal(0, params.PreFixedNum)

	// 测试第二页
	params = search.Params{
		Keyword:    "貌合神离",
		SearchType: search.TypeDrama,
		Page:       2,
		PageSize:   3,
	}
	SetInsertParams(&params)
	assert.Len(params.AllFixedTargetIDs, 3)
	assert.Len(params.CurrentActiveFixedParams, 1)
	assert.Equal(1, params.PreFixedNum)

	// 测试无数据
	params = search.Params{
		Keyword:    "貌合神离",
		SearchType: search.TypeAlbum,
		Page:       1,
		PageSize:   3,
	}
	SetInsertParams(&params)
	assert.Len(params.AllFixedTargetIDs, 0)
	assert.Len(params.CurrentActiveFixedParams, 0)
}

func TestFindActiveInterventions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	activeInterventions, err := FindActiveInterventions()
	require.NoError(err)
	assert.Len(activeInterventions, 6)
}

func TestCleanSearchIntervention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanNum, err := CleanSearchIntervention(map[int][]int64{5: {999999999}})
	require.NoError(err)
	assert.EqualValues(cleanNum, 1)
}
