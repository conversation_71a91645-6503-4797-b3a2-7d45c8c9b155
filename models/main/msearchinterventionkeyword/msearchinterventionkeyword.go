package msearchinterventionkeyword

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 关键词干预配置状态
const (
	StatusDisabled = iota // 已停止
	StatusActive          // 生效中
)

// MSearchInterventionKeyword 搜索干预关键词
type MSearchInterventionKeyword struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time"` // 更新时间
	Keyword      string `gorm:"column:keyword"`       // 关键词
	TargetID     int64  `gorm:"column:target_id"`     // 被固定内容的关联 ID
	TargetIndex  int    `gorm:"column:target_index"`  // 在搜索结果中的位置
	TargetType   int    `gorm:"column:target_type"`   // 干预搜索类型
	Status       int    `gorm:"column:status"`        // 状态 0：已停止；1：生效中
	StartTime    int64  `gorm:"column:start_time"`    // 开始时间
	EndTime      int64  `gorm:"column:end_time"`      // 结束时间
}

// DB the db instance of MSearchInterventionKeyword model
func (msik MSearchInterventionKeyword) DB() *gorm.DB {
	return service.MainDB.Table(msik.TableName())
}

// TableName for MSearchInterventionKeyword model
func (MSearchInterventionKeyword) TableName() string {
	return "m_search_intervention_keyword"
}

// SetInsertParams 通过关键词获取固定承接内容需要的参数
func SetInsertParams(p *search.Params) {
	activeFixedParams := findActiveFixedParams(p.SearchType, p.Keyword)

	startIndex, endIndex := (p.Page-1)*p.PageSize, p.Page*p.PageSize
	p.AllFixedTargetIDs = make([]string, 0, len(activeFixedParams))
	p.CurrentActiveFixedParams = make([]search.FixedParam, 0, len(activeFixedParams))
	for _, fixedParam := range activeFixedParams {
		// 所有配置
		p.AllFixedTargetIDs = append(p.AllFixedTargetIDs, strconv.FormatInt(fixedParam.TargetID, 10))
		// 当前页之前
		if fixedParam.TargetIndex < startIndex {
			p.PreFixedNum++
		}
		// 当前页
		if fixedParam.TargetIndex >= startIndex && fixedParam.TargetIndex < endIndex {
			fixedParam.TargetIndex -= startIndex
			p.CurrentActiveFixedParams = append(p.CurrentActiveFixedParams, fixedParam)
		}
	}
}

// findActiveFixedParams
func findActiveFixedParams(searchType int, keyword string) []search.FixedParam {
	key := serviceredis.KeyOpenSearchSearchInterventionTypeKeywordMD5FixedParams2.Format(searchType, util.MD5(keyword))
	resStr, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithFields(logger.Fields{
			"type":    searchType,
			"keyword": keyword,
		}).Errorf("从 redis 获取关键词的干预搜索配置失败: %v", err)
		// PASS
		return []search.FixedParam{}
	}
	if err == nil {
		cacheFixedParams := make([]search.FixedParam, 0)
		err = json.Unmarshal([]byte(resStr), &cacheFixedParams)
		if err != nil {
			logger.Error(err)
			// PASS
			return []search.FixedParam{}
		}
		return cacheFixedParams
	}

	var msiks []MSearchInterventionKeyword
	unixTimestamp := util.TimeNow().Unix()
	// TODO: 后续若查询比较耗时，考虑加锁解决缓存失效时并发查询的问题
	err = MSearchInterventionKeyword{}.DB().Select("target_id, target_index").
		Where("target_type = ? AND keyword = ? AND status = ? AND start_time <= ? AND end_time > ?",
			searchType, keyword, StatusActive, unixTimestamp, unixTimestamp).
		Find(&msiks).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"type":    searchType,
			"keyword": keyword,
		}).Errorf("从数据库获取关键词的干预搜索配置数据失败: %v", err)
		// PASS
		return []search.FixedParam{}
	}

	fixedParams := make([]search.FixedParam, 0, len(msiks))
	for _, msik := range msiks {
		fixedParams = append(fixedParams, search.FixedParam{
			TargetIndex: msik.TargetIndex,
			TargetID:    msik.TargetID,
		})
	}
	// 正序排序，保证之后对数据的插入是正确的
	sort.Slice(fixedParams, func(i, j int) bool {
		return fixedParams[i].TargetIndex < fixedParams[j].TargetIndex
	})

	fixedParamsBytes, err := json.Marshal(fixedParams)
	if err != nil {
		logger.Error(err)
		// PASS
		return fixedParams
	}
	err = service.LRURedis.Set(key, fixedParamsBytes, 30*time.Second).Err()
	if err != nil {
		logger.WithFields(logger.Fields{
			"type":    searchType,
			"keyword": keyword,
		}).Errorf("redis 设置干预搜索配置失败: %v", err)
		// PASS
		return fixedParams
	}
	return fixedParams
}

// FindActiveInterventions 查询正在生效的干预配置
func FindActiveInterventions() ([]MSearchInterventionKeyword, error) {
	var msiks []MSearchInterventionKeyword
	unixTimestamp := util.TimeNow().Unix()
	err := MSearchInterventionKeyword{}.DB().Select("target_type, target_id").
		Where("status = ? AND start_time <= ? AND end_time > ?",
			StatusActive, unixTimestamp, unixTimestamp).
		Find(&msiks).Error
	if err != nil {
		return nil, err
	}
	return msiks, nil
}

// CleanSearchIntervention 清理正在生效的无效配置
// typeIDsMap: map[targetType]targetIDs
func CleanSearchIntervention(typeIDsMap map[int][]int64) (int64, error) {
	orWheres := make([]string, 0, len(typeIDsMap))
	for targetType, targetIDs := range typeIDsMap {
		if len(targetIDs) == 0 {
			continue
		}
		orWheres = append(orWheres,
			fmt.Sprintf("(target_type = %d AND target_id IN (%s))", targetType, util.JoinInt64Array(targetIDs, ",")))
	}
	if len(orWheres) == 0 {
		return 0, nil
	}

	updateDB := MSearchInterventionKeyword{}.DB().
		Where("status = ?", StatusActive).Where(strings.Join(orWheres, " OR ")).
		Updates(map[string]interface{}{
			"status":        StatusDisabled,
			"modified_time": util.TimeNow().Unix(),
		})
	if updateDB.Error != nil {
		return 0, updateDB.Error
	}
	return updateDB.RowsAffected, nil
}
