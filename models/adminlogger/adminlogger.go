package adminlogger

import (
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "m_admin_logger"

// 管理员操作日志文档: https://info.missevan.com/pages/viewpage.action?pageId=4523227
const (
	// CatalogPass 审核通过
	CatalogPass = 1
	// CatalogRefuse 审核拒绝通过
	CatalogRefuse = 2
	// CatalogUpdate 进行修改操作
	CatalogUpdate = 3
	// CatalogDelete 删除操作
	CatalogDelete = 4
	// CatalogSetUploadLog 操作日志拉取名单
	CatalogSetUploadLog = 187
	// CatalogUserViolationImage 用户违规图片
	CatalogUserViolationImage = 251
	// CatalogKeywordSearchIntervention 管理关键词搜索干预
	CatalogKeywordSearchIntervention = 264
)

// AdminLog 管理员日志
type AdminLog struct {
	UserID     int64  `gorm:"column:user_id" json:"user_id"`         // 用户 ID
	Catalog    int    `gorm:"column:catalog" json:"catalog"`         // 类型
	ChannelID  int64  `gorm:"column:channel_id" json:"channel_id"`   // 频道 ID
	URL        string `gorm:"column:url" json:"url"`                 // URL
	Intro      string `gorm:"column:intro" json:"intro"`             // 操作
	IP         string `gorm:"column:ip" json:"ip"`                   // 操作 IP
	CreateTime int64  `gorm:"column:create_time" json:"create_time"` // 操作时间
}

// TableName table name
func (AdminLog) TableName() string {
	return tableName
}

// InsertAdminLog 添加管理员日志
func (l *AdminLog) InsertAdminLog() error {
	l.CreateTime = util.TimeNow().Unix()
	return service.LogDB.Create(l).Error
}
