package mcollectdramalist

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 收藏状态
const (
	StatusNotCollected = iota // 未收藏
	StatusCollected           // 已收藏
)

// MCollectDramalist 用户收藏剧单表
type MCollectDramalist struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 修改时间。单位：秒
	DramalistID  int64 `gorm:"column:dramalist_id"`   // 剧单 ID
	UserID       int64 `gorm:"column:user_id"`        // 用户 ID
}

// DB the db instance of MCollectDramalist model
func (m MCollectDramalist) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MCollectDramalist) TableName() string {
	return "m_collect_dramalist"
}

// BeforeSave gorm hook
func (m *MCollectDramalist) BeforeSave() error {
	nowTime := goutil.TimeNow().Unix()
	if service.DB.NewRecord(m) {
		m.CreateTime = nowTime
	}
	m.ModifiedTime = nowTime
	return nil
}

// IsCollected 判断用户是否收藏过剧单
func IsCollected(userID, dramalistID int64) (bool, error) {
	mCollectDramalist := new(MCollectDramalist)
	err := MCollectDramalist{}.DB().Select("id").
		Where("user_id = ? AND dramalist_id = ?", userID, dramalistID).
		Take(mCollectDramalist).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// CollectLister 获取用户收藏的剧单信息列表参数
type CollectLister struct {
	P        int64
	PageSize int64
	UserID   int64
}

// List 根据参数获取用户收藏的剧单信息列表
func (param CollectLister) List() ([]*mdramalist.MDramalist, goutil.Pagination, error) {
	db := service.MainDB.Table(MCollectDramalist{}.TableName()+" AS a").
		Select("b.id, b.title, b.collect_count").
		Joins(fmt.Sprintf("INNER JOIN %s AS b ON a.dramalist_id = b.id AND b.delete_time = 0",
			mdramalist.MDramalist{}.TableName())).
		Where("a.user_id = ?", param.UserID).
		Order("a.create_time DESC")

	var count int64
	var pa goutil.Pagination
	if err := db.Count(&count).Error; err != nil {
		return nil, pa, err
	}

	pa = goutil.MakePagination(count, param.P, param.PageSize)
	if !pa.Valid() {
		return []*mdramalist.MDramalist{}, pa, nil
	}

	var collectList []*mdramalist.MDramalist
	err := pa.ApplyTo(db).Find(&collectList).Error
	if err != nil {
		return nil, pa, err
	}

	return collectList, pa, nil
}
