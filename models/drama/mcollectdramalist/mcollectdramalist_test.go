package mcollectdramalist

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, StatusNotCollected)
	assert.Equal(1, StatusCollected)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MCollectDramalist{}, "id", "create_time", "modified_time", "dramalist_id", "user_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_collect_dramalist", MCollectDramalist{}.TableName())
}

func TestMCollectDramalist_BeforeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &MCollectDramalist{
		DramalistID: 100,
		UserID:      12,
	}
	require.NoError(l.BeforeSave())
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
}

func TestIsCollected(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户已收藏剧单
	collected, err := IsCollected(1, 1)
	require.NoError(err)
	assert.True(collected)

	// 测试用户未收藏剧单
	collected, err = IsCollected(1, 10000)
	require.NoError(err)
	assert.False(collected)
}

func TestCollectLister_List(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := CollectLister{
		P:        1,
		PageSize: 2,
		UserID:   820591,
	}
	// 测试没有收藏的剧单时
	collectList, pagination, err := param.List()
	require.NoError(err)
	assert.Empty(collectList)
	assert.EqualValues(0, pagination.Count)
	assert.EqualValues(0, pagination.MaxPage)
	assert.EqualValues(1, pagination.P)
	assert.EqualValues(2, pagination.PageSize)

	// 测试有收藏的剧单时
	param.UserID = 820592
	collectList, pagination, err = param.List()
	require.NoError(err)
	assert.NotNil(collectList)
	assert.NotNil(pagination)
	assert.Len(collectList, 2)

	assert.EqualValues(12, collectList[0].ID)
	assert.Equal("测试剧单标题 12", collectList[0].Title)
	assert.EqualValues(2, collectList[0].CollectCount)

	assert.EqualValues(11, collectList[1].ID)
	assert.Equal("测试剧单标题 11", collectList[1].Title)
	assert.EqualValues(2, collectList[1].CollectCount)

	assert.EqualValues(3, pagination.Count)
	assert.EqualValues(2, pagination.MaxPage)
	assert.EqualValues(1, pagination.P)
	assert.EqualValues(2, pagination.PageSize)

	// 测试获取第二页
	param.P = 2
	collectList, pagination, err = param.List()
	require.NoError(err)
	assert.NotNil(collectList)
	assert.NotNil(pagination)
	assert.Len(collectList, 1)

	assert.EqualValues(10, collectList[0].ID)
	assert.Equal("测试剧单标题 10", collectList[0].Title)
	assert.EqualValues(1, collectList[0].CollectCount)

	assert.EqualValues(3, pagination.Count)
	assert.EqualValues(2, pagination.MaxPage)
	assert.EqualValues(2, pagination.P)
	assert.EqualValues(2, pagination.PageSize)
}
