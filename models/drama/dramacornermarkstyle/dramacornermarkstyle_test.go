package dramacornermarkstyle

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/drama/dramacopyright"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramacornermark"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaCornerMark(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(DramaCornerMarkStyle{},
		"id", "create_time", "modified_time", "text", "text_color", "text_start_color", "text_end_color",
		"bg_start_color", "bg_end_color", "left_icon_url", "type")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CornerMark{}, "text", "text_color", "text_start_color", "text_end_color",
		"bg_start_color", "bg_end_color", "left_icon_url")
	kc.CheckOmitEmpty(CornerMark{}, "text_start_color", "text_end_color", "left_icon_url")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("drama_corner_mark_style", DramaCornerMarkStyle{}.TableName())
}

func TestListAllMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无缓存
	key := keys.KeyDramaCornerMarkStyle0.Format()
	require.NoError(service.LRURedis.Del(key).Err())
	val := service.LRURedis.Exists(key).Val()
	require.Zero(val)
	result := ListAllMap()
	assert.Len(result, 8)
	assert.Equal("已购", result[1].Text)
	assert.Equal("热播", result[2].Text)
	assert.Equal("http://static-test.missevan.com/cornermark/corner_mark.png", result[2].LeftIconURL)
	defer service.LRURedis.Del(key)

	// 测试有缓存
	val = service.LRURedis.Exists(key).Val()
	require.NotZero(val)
	result = ListAllMap()
	assert.Len(result, 8)
	assert.Equal("已购", result[1].Text)
	assert.Equal("热播", result[2].Text)
	assert.Equal("http://static-test.missevan.com/cornermark/corner_mark.png", result[2].LeftIconURL)

	// 模拟 key 过期生成新的缓存
	err := service.LRURedis.Del(key).Err()
	require.NoError(err)
	result = ListAllMap()
	assert.Len(result, 8)
}

func TestGetType(t *testing.T) {
	assert := assert.New(t)

	// 测试获取热播角标类型
	drama := dramainfo.RadioDramaDramainfo{
		ID:      1,
		PayType: dramainfo.PayTypeFree,
	}
	cornerMarkMap := map[int64]dramacornermark.RadioDramaCornerMark{
		1: {
			DramaID: 1,
			Type:    dramacornermark.TypeOriginal,
		},
	}
	copyrightMap := map[int64]dramacopyright.RadioDramaDramacopyright{
		1: {
			DramaID: 1,
			Type:    dramacopyright.TypeExclusive,
		},
	}
	util.SetTimeNow(func() time.Time {
		return time.Date(2022, 10, 9, 6, 6, 0, 0, time.Local)
	})
	defer util.SetTimeNow(nil)
	result := GetType(drama, cornerMarkMap, copyrightMap)
	assert.Equal(TypeHot, result)
	util.SetTimeNow(nil)

	// 测试获取首发角标类型
	integrity := dramainfo.IntegrityNameSerializing
	drama = dramainfo.RadioDramaDramainfo{
		ID:        1,
		PayType:   dramainfo.PayTypeFree,
		Integrity: &integrity,
	}
	cornerMarkMap = map[int64]dramacornermark.RadioDramaCornerMark{
		1: {
			DramaID: 1,
			Type:    dramacornermark.TypeSelected,
		},
	}
	copyrightMap = map[int64]dramacopyright.RadioDramaDramacopyright{
		1: {
			DramaID: 1,
			Type:    dramacopyright.TypeFirstLaunch,
		},
	}
	result = GetType(drama, cornerMarkMap, copyrightMap)
	assert.Equal(TypeFirstLaunch, result)

	// 测试无角标
	drama = dramainfo.RadioDramaDramainfo{
		ID:      1,
		PayType: dramainfo.PayTypeFree,
		Refined: dramainfo.RefinedRisking,
	}
	cornerMarkMap = map[int64]dramacornermark.RadioDramaCornerMark{
		1: {
			DramaID: 1,
			Type:    dramacornermark.TypeSelected,
		},
	}
	copyrightMap = map[int64]dramacopyright.RadioDramaDramacopyright{
		1: {
			DramaID: 1,
			Type:    dramacopyright.TypeFirstLaunch,
		},
	}
	result = GetType(drama, cornerMarkMap, copyrightMap)
	assert.Equal(TypeNone, result)
}

func TestGetDramaCornerMark(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramas := []dramainfo.RadioDramaDramainfo{
		{
			ID:      1,
			PayType: dramainfo.PayTypeEpisode,
			Refined: dramainfo.RefinedRisking,
		},
		{
			ID:      2,
			PayType: dramainfo.PayTypeEpisode,
			Refined: dramainfo.RefinedInteractive,
		},
		{
			ID:      3,
			PayType: dramainfo.PayTypeEpisode,
			Refined: dramainfo.RefinedInteractive,
			Vip:     dramainfo.VipDrama,
		},
	}
	cornerMarkMap, err := GetDramaCornerMark(12, dramas)
	require.NoError(err)
	cm, ok := cornerMarkMap[1]
	require.True(ok)
	assert.Equal("付费", cm.Text)
	cm, ok = cornerMarkMap[2]
	require.True(ok)
	assert.Equal("已购", cm.Text)
	cm, ok = cornerMarkMap[3]
	require.True(ok)
	assert.Equal("会员", cm.Text)
}
