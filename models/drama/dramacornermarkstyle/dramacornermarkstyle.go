package dramacornermarkstyle

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramacopyright"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramacornermark"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaweeklyhot"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 角标样式类型
const (
	TypeNone        = iota // 无角标
	TypeBought             // 已购
	TypeHot                // 热播
	TypeOriginal           // 原创
	TypeExclusive          // 独播
	TypeFirstLaunch        // 首发
	TypePay                // 付费
	TypeSelected           // 精选
	TypeVip                // 会员
)

// DramaCornerMarkStyle 剧集角标样式信息
type DramaCornerMarkStyle struct {
	ID             int64  `gorm:"column:id;primary_key" json:"-"`
	CreateTime     int64  `gorm:"column:create_time" json:"-"`                               // 创建时间
	ModifiedTime   int64  `gorm:"column:modified_time" json:"-"`                             // 修改时间
	Type           int    `gorm:"column:type" json:"-"`                                      // 类型 1: 已购; 2: 热播; 3: 原创; 4: 独播; 5: 首发; 6: 付费; 7: 精选; 8: 会员
	Text           string `gorm:"column:text" json:"text"`                                   // 角标文本
	TextColor      string `gorm:"column:text_color" json:"text_color"`                       // 角标文本背景色
	TextStartColor string `gorm:"column:text_start_color" json:"text_start_color,omitempty"` // 角标文本渐变起始颜色
	TextEndColor   string `gorm:"column:text_end_color" json:"text_end_color,omitempty"`     // 角标文本渐变结束颜色
	BgStartColor   string `gorm:"column:bg_start_color" json:"bg_start_color"`               // 渐变填充色色值
	BgEndColor     string `gorm:"column:bg_end_color" json:"bg_end_color"`                   // 渐变填充色色值
	LeftIconURL    string `gorm:"column:left_icon_url" json:"left_icon_url,omitempty"`       // 左侧图标
}

// CornerMark 剧集角标信息
type CornerMark struct {
	Text           string `json:"text"`
	TextColor      string `json:"text_color"`
	TextStartColor string `json:"text_start_color,omitempty"`
	TextEndColor   string `json:"text_end_color,omitempty"`
	BgStartColor   string `json:"bg_start_color"`
	BgEndColor     string `json:"bg_end_color"`
	LeftIconURL    string `json:"left_icon_url,omitempty"`
}

// DB the db instance of RadioDramaCornerStyle model
func (c DramaCornerMarkStyle) DB() *gorm.DB {
	return service.DramaDB.Table(c.TableName())
}

// TableName for RadioDramaCornerStyle model
func (DramaCornerMarkStyle) TableName() string {
	return "drama_corner_mark_style"
}

// ListAllMap 获取全部剧集角标样式
func ListAllMap() map[int]*DramaCornerMarkStyle {
	key := keys.KeyDramaCornerMarkStyle0.Format()
	resStr, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Errorf("从 redis 获取剧集角标样式失败: %v", err)
		// PASS
		return map[int]*DramaCornerMarkStyle{}
	}
	cornerMarkStyles := make(map[int]*DramaCornerMarkStyle)
	if err == nil {
		err = json.Unmarshal([]byte(resStr), &cornerMarkStyles)
		if err != nil {
			logger.Error(err)
			// PASS
			return map[int]*DramaCornerMarkStyle{}
		}
	} else if serviceredis.IsRedisNil(err) {
		var styles []*DramaCornerMarkStyle
		err = DramaCornerMarkStyle{}.DB().Select("type, text, text_color, text_start_color, text_end_color, bg_start_color, bg_end_color, left_icon_url").Find(&styles).Error
		if err != nil {
			logger.Errorf("从数据库获取剧集角标样式失败: %v", err)
			// PASS
			return map[int]*DramaCornerMarkStyle{}
		}
		for _, style := range styles {
			cornerMarkStyles[style.Type] = style
		}

		cornerMarkStylesBytes, err := json.Marshal(cornerMarkStyles)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			err = service.LRURedis.Set(key, cornerMarkStylesBytes, 5*time.Minute).Err()
			if err != nil {
				logger.Errorf("从 redis 设置剧集角标样式失败: %v", err)
				// PASS
			}
		}
	}
	return fillIconURL(cornerMarkStyles)
}

// fillIconURL
func fillIconURL(cornerMarkStyles map[int]*DramaCornerMarkStyle) map[int]*DramaCornerMarkStyle {
	for _, style := range cornerMarkStyles {
		if style.LeftIconURL != "" {
			style.LeftIconURL = service.Storage.Parse(style.LeftIconURL)
		}
	}
	return cornerMarkStyles
}

// GetType 获取角标样式类型（剧集角标优先级: 已购 > 热播 > 会员 > 原创 > 独播 > 首发 > 付费 > 精选）
func GetType(drama dramainfo.RadioDramaDramainfo, cornerMarkMap map[int64]dramacornermark.RadioDramaCornerMark,
	copyrightMap map[int64]dramacopyright.RadioDramaDramacopyright) int {
	if drama.PayType != dramainfo.PayTypeFree && drama.NeedPay != nil && *drama.NeedPay == dramainfo.NeedPayPaid {
		return TypeBought
	}
	cornerMark, cornerMarkOk := cornerMarkMap[drama.ID]
	// 剧集属性为擦边球的剧集不展示对应角标（热播、原创、独播、首发、精选）
	var viewRankDramaIDs []int64
	risking := drama.Refined.IsSet(dramainfo.RefinedRisking)
	if !risking {
		// 获取上周热播剧 IDs
		viewRankDramaIDs = dramaweeklyhot.FindWeeklyHotDramaIDs()
		if util.HasElem(viewRankDramaIDs, drama.ID) {
			return TypeHot
		}
		if drama.Vip == dramainfo.VipDrama {
			return TypeVip
		}
		if cornerMarkOk && cornerMark.Type == dramacornermark.TypeOriginal {
			return TypeOriginal
		}
		copyright, ok := copyrightMap[drama.ID]
		if ok {
			// 当版权管理分发方式为独播时，返回独播角标
			if copyright.Type == dramacopyright.TypeExclusive {
				return TypeExclusive
			}
			// 当版权管理分发方式为首发并且剧集未完结时，返回首发角标
			if copyright.Type == dramacopyright.TypeFirstLaunch && drama.Integrity != nil &&
				*drama.Integrity == dramainfo.IntegrityNameSerializing {
				return TypeFirstLaunch
			}
		}
	}
	if drama.Vip == dramainfo.VipDrama {
		return TypeVip
	}
	if drama.PayType != dramainfo.PayTypeFree {
		return TypePay
	}
	if cornerMarkOk && cornerMark.Type == dramacornermark.TypeSelected && !risking {
		return TypeSelected
	}
	return TypeNone
}

// GetDramaCornerMark 获取剧集角标信息
func GetDramaCornerMark(userID int64, dramas []dramainfo.RadioDramaDramainfo) (map[int64]CornerMark, error) {
	if len(dramas) == 0 {
		return nil, nil
	}

	// 剧集添加 need_pay 信息
	err := dramainfo.CheckNeedPay(dramas, userID)
	if err != nil {
		return nil, err
	}

	// 获取剧集角标管理信息
	var dramaIDs []int64
	for _, drama := range dramas {
		dramaIDs = append(dramaIDs, drama.ID)
	}
	cornerMarks, err := dramacornermark.FindCornerMarkByDramaIDs(dramaIDs)
	if err != nil {
		return nil, err
	}
	cornerMarkMap := util.ToMap(cornerMarks, "DramaID").(map[int64]dramacornermark.RadioDramaCornerMark)

	// 获取剧集版权管理信息
	copyrights, err := dramacopyright.FindCopyrightByDramaIDs(dramaIDs)
	if err != nil {
		return nil, err
	}
	copyrightMap := util.ToMap(copyrights, "DramaID").(map[int64]dramacopyright.RadioDramaDramacopyright)

	// 获取全部剧集角标样式
	styles := ListAllMap()

	result := make(map[int64]CornerMark, len(dramas))
	for _, dramaInfo := range dramas {
		styleType := GetType(dramaInfo, cornerMarkMap, copyrightMap)
		if styleType == TypeNone {
			continue
		}
		if cornerMarkStyle, ok := styles[styleType]; ok {
			result[dramaInfo.ID] = CornerMark{
				Text:           cornerMarkStyle.Text,
				TextColor:      cornerMarkStyle.TextColor,
				TextStartColor: cornerMarkStyle.TextStartColor,
				TextEndColor:   cornerMarkStyle.TextEndColor,
				BgStartColor:   cornerMarkStyle.BgStartColor,
				BgEndColor:     cornerMarkStyle.BgEndColor,
				LeftIconURL:    cornerMarkStyle.LeftIconURL,
			}
		}
	}
	return result, nil
}
