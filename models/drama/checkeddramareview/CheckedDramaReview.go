package checkeddramareview

import (
	"encoding/json"
	"reflect"
	"sort"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "checked_drama_review"

// CheckedDramaReview 剧集审核表
type CheckedDramaReview struct {
	ID             int64   `gorm:"column:id" json:"id"`
	DramaID        int64   `gorm:"column:drama_id" json:"drama_id"`               // 剧集 ID
	Name           *string `gorm:"column:name" json:"name"`                       // 剧集名称
	Cover          *string `gorm:"column:cover" json:"cover"`                     // 剧集海报
	CoverColor     int     `gorm:"column:cover_color" json:"cover_color"`         // RGB 颜色值
	Abstract       *string `gorm:"column:abstract" json:"abstract"`               // 剧集简介
	Integrity      *int8   `gorm:"column:integrity" json:"integrity"`             // 完结度
	Age            *int8   `gorm:"column:age" json:"age"`                         // 年代
	Origin         *int8   `gorm:"column:origin" json:"origin"`                   // 创作类型
	Author         *string `gorm:"column:author" json:"author"`                   // 原作者
	Birthday       int8    `gorm:"column:birthday" json:"birthday"`               // 生日剧
	CV             *string `gorm:"column:cv" json:"cv"`                           // 生日 CV
	IP             int8    `gorm:"column:ip" json:"ip"`                           // 同人剧
	IPName         *string `gorm:"column:ipname" json:"ipname"`                   // 原作标签
	Type           int32   `gorm:"column:type" json:"type"`                       // 分类
	Newest         string  `gorm:"column:newest" json:"newest"`                   // 更新至
	OrganizationID int64   `gorm:"column:organization_id" json:"organization_id"` // 社团 ID
	UserID         int64   `gorm:"column:user_id" json:"user_id"`                 // 所属用户 ID
	UserName       string  `gorm:"column:username" json:"username"`               // 所属用户名
	Catalog        int64   `gorm:"column:catalog" json:"catalog"`                 // 分类 ID
	Alias          *string `gorm:"column:alias" json:"alias"`                     // 别名
	Episodes       string  `gorm:"column:episodes" json:"episodes"`               // 添加、删除或修改的单集
	Tags           string  `gorm:"column:tags" json:"tags"`                       // 标签
}

// DB the db instance of CheckedDramaReview model
func (cdr CheckedDramaReview) DB() *gorm.DB {
	return service.DramaDB.Table(cdr.TableName())
}

// TableName for CheckedDramaReview model
func (cdr CheckedDramaReview) TableName() string {
	return tableName
}

// Episodes 添加、删除或修改的单集
type Episodes struct {
	Create []*Episode `json:"create,omitempty"`
	Update []*Episode `json:"update,omitempty"`
	Delete []*Episode `json:"delete,omitempty"`
}

// Episode 单集信息
type Episode struct {
	dramaepisode.RadioDramaEpisode `json:",inline"`
	EpisodeID                      int64 `json:"episode_id"` // 单集 ID
}

// UnmarshalEpisodes 解析 Episodes 的 JSON 结构
func (cdr CheckedDramaReview) UnmarshalEpisodes() (*Episodes, error) {
	if cdr.Episodes == "" {
		return nil, nil
	}
	var episodes Episodes
	err := json.Unmarshal([]byte(cdr.Episodes), &episodes)
	if err != nil {
		return nil, err
	}
	return &episodes, nil
}

// ReassignDramaInfo 根据再审剧集信息重新赋值剧集信息
func (cdr CheckedDramaReview) ReassignDramaInfo(di *dramainfo.RadioDramaDramainfo) {
	if checkReviewInfo(di.Name, cdr.Name) {
		di.Name = cdr.Name
	}
	if checkReviewInfo(di.Cover, cdr.Cover) {
		di.Cover = cdr.Cover
		di.CoverURL = service.Storage.Parse(params.URL.DramaCoverURL + *cdr.Cover)
	}
	if checkReviewInfo(di.CoverColor, cdr.CoverColor) {
		di.CoverColor = cdr.CoverColor
	}
	if checkReviewInfo(di.Abstract, cdr.Abstract) {
		di.Abstract = cdr.Abstract
	}
	if checkReviewInfo(di.Integrity, cdr.Integrity) {
		di.Integrity = cdr.Integrity
	}
	if checkReviewInfo(di.Age, cdr.Age) {
		di.Age = cdr.Age
	}
	if checkReviewInfo(di.Origin, cdr.Origin) {
		di.Origin = cdr.Origin
	}
	if checkReviewInfo(di.Author, cdr.Author) {
		di.Author = cdr.Author
	}
	if checkReviewInfo(di.Birthday, cdr.Birthday) {
		di.Birthday = cdr.Birthday
	}
	if checkReviewInfo(di.CV, cdr.CV) {
		di.CV = cdr.CV
	}
	if checkReviewInfo(di.IP, cdr.IP) {
		di.IP = cdr.IP
	}
	if checkReviewInfo(di.IPName, cdr.IPName) {
		di.IPName = cdr.IPName
	}
	if checkReviewInfo(di.Type, cdr.Type) {
		di.Type = cdr.Type
	}
	if checkReviewInfo(di.Newest, cdr.Newest) {
		di.Newest = cdr.Newest
	}
	if checkReviewInfo(di.OrganizationID, cdr.OrganizationID) {
		di.OrganizationID = cdr.OrganizationID
	}
	if checkReviewInfo(di.UserID, cdr.UserID) {
		di.UserID = cdr.UserID
	}
	if checkReviewInfo(di.UserName, cdr.UserName) {
		di.UserName = cdr.UserName
	}
	if checkReviewInfo(di.Catalog, cdr.Catalog) {
		di.Catalog = cdr.Catalog
	}
	if checkReviewInfo(di.Alias, cdr.Alias) {
		di.Alias = cdr.Alias
	}
}

// ReassignDramaEpisodesInfo 根据再审单集信息重新赋值单集信息
func (cdr CheckedDramaReview) ReassignDramaEpisodesInfo(rde *[]dramaepisode.RadioDramaEpisode) error {
	// 解析待审核单集信息的 JSON 结构
	cer, err := cdr.UnmarshalEpisodes()
	if err != nil {
		return err
	}
	if cer == nil {
		return nil
	}

	epUpdate := map[int64]*Episode{}
	if len(cer.Update) != 0 {
		epUpdate = util.ToMap(cer.Update, "EpisodeID").(map[int64]*Episode)
	}
	epDelete := map[int64]*Episode{}
	if len(cer.Delete) != 0 {
		epDelete = util.ToMap(cer.Delete, "EpisodeID").(map[int64]*Episode)
	}
	deps := make([]dramaepisode.RadioDramaEpisode, 0, len(*rde)+len(cer.Create))
	for _, episode := range *rde {
		// 如果单集被修改，则使用修改后的单集信息，并加入到新剧集数组
		if s := epUpdate[episode.ID]; s != nil {
			s.setEpisode(&episode, false)
			deps = append(deps, episode)
		} else {
			// 单集没有被删除，则加入到新剧集数组
			if d := epDelete[episode.ID]; d == nil {
				deps = append(deps, episode)
			}
		}
	}

	// 有新增剧集，则加入到新剧集数组
	if len(cer.Create) != 0 {
		for _, espCreate := range cer.Create {
			var episode dramaepisode.RadioDramaEpisode
			espCreate.setEpisode(&episode, true)
			deps = append(deps, episode)
		}
	}

	sort.Slice(deps, func(i, j int) bool {
		return deps[i].Order < deps[j].Order
	})
	*rde = deps

	return nil
}

func (s *Episode) setEpisode(episode *dramaepisode.RadioDramaEpisode, createScene bool) {
	if checkReviewInfo(episode.Name, s.Name) {
		episode.Name = s.Name
	}
	if checkReviewInfo(episode.DramaID, s.DramaID) {
		episode.DramaID = s.DramaID
	}
	if checkReviewInfo(episode.SoundID, s.SoundID) {
		episode.SoundID = s.SoundID
	}
	if checkReviewInfo(episode.Date, s.Date) {
		episode.Date = s.Date
	}
	// Order, Type, PayType, Subtitle 字段可以重新赋值对应类型的零值，不用 checkReviewInfo 方法判断
	if createScene && episode.Order != s.Order {
		// 由于修改单集 Order 是直接更新，不会进入再审，所以再审信息是修改的剧集信息时使用单集表的 Order
		// 再审信息是新增的剧集信息时才需要赋值 Order
		episode.Order = s.Order
	}
	if episode.Type != s.Type {
		episode.Type = s.Type
	}
	if episode.PayType != s.PayType {
		episode.PayType = s.PayType
	}
	if episode.Subtitle != s.Subtitle {
		episode.Subtitle = s.Subtitle
	}
}

func checkReviewInfo(l, r interface{}) bool {
	if reflect.ValueOf(r).IsZero() {
		return false
	}

	return !reflect.DeepEqual(l, r)
}
