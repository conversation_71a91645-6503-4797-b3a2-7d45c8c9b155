package dramaprice

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestRadioDramaPriceTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaPrice{},
		"drama_id", "price", "rate", "user_id", "name", "type", "rewardable")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaPrice{},
		"drama_id", "price", "rate", "user_id", "name", "type", "rewardable")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_price", tableName)
	assert.Equal("radio_drama_price", RadioDramaPrice{}.TableName())
}
