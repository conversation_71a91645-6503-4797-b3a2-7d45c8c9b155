package dramaprice

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

const tableName = "radio_drama_price"

// RadioDramaPrice 剧集价格表
type RadioDramaPrice struct {
	DramaID    int64   `gorm:"column:drama_id" json:"drama_id"`
	Price      int64   `gorm:"column:price" json:"price"`
	Rate       float32 `gorm:"column:rate" json:"rate"`
	UserID     int64   `gorm:"column:user_id" json:"user_id"`
	Name       string  `gorm:"column:name" json:"name"`
	Type       int8    `gorm:"column:type" json:"type"`
	Rewardable int     `gorm:"column:rewardable" json:"rewardable"`
}

// DB the db instance of RadioDramaPrice model
func (rdp RadioDramaPrice) DB() *gorm.DB {
	return service.DramaDB.Table(rdp.TableName())
}

// TableName for RadioDramaPrice model
func (RadioDramaPrice) TableName() string {
	return tableName
}
