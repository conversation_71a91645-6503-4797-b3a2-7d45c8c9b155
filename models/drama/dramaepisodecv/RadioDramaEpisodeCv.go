package dramaepisodecv

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// 参演角色类型
const (
	// TypeCharacterMain 主役
	TypeCharacterMain = iota + 1
	// TypeCharacterMinor 协役
	TypeCharacterMinor
	// TypeCharacterCarefree 龙套
	TypeCharacterCarefree
)

// RadioDramaEpisodeCv model
type RadioDramaEpisodeCv struct {
	ID        int64  `gorm:"column:id;primary_key"`
	EpisodeID int64  `gorm:"column:episode_id"` // 单集 ID
	CvID      int64  `gorm:"column:cv_id"`      // 声优 ID（mowangsksoundseiy.id）
	Character string `gorm:"column:character"`  // 出演角色
	Main      int    `gorm:"column:main"`       // 角色类型 1：主役；2：协役；3：龙套
	DramaID   int64  `gorm:"column:drama_id"`   // 剧集 ID
}

// DB the db instance of RadioDramaEpisodeCv model
func (rde RadioDramaEpisodeCv) DB() *gorm.DB {
	return service.DramaDB.Table(rde.TableName())
}

// TableName for RadioDramaEpisodeCv model
func (RadioDramaEpisodeCv) TableName() string {
	return "radio_drama_episode_cv"
}

// ListDramaCVByDramaIDs 获取剧集关联声优 ID 列表
func ListDramaCVByDramaIDs(dramaIDs []int64, mainType int) ([]RadioDramaEpisodeCv, error) {
	var cvs []RadioDramaEpisodeCv
	err := RadioDramaEpisodeCv{}.DB().
		Select("MIN(id) AS id, drama_id, cv_id").
		Where("main = ? AND drama_id IN (?)", mainType, dramaIDs).
		// 过滤掉同一剧集下的相同 CV，不同剧集下的相同 CV 不会被过滤
		Group("drama_id, cv_id").
		Order("id ASC").
		Find(&cvs).Error
	if err != nil {
		return nil, err
	}
	return cvs, nil
}
