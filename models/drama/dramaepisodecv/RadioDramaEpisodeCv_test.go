package dramaepisodecv

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()

	m.Run()
}

func TestConst(t *testing.T) {
	require := require.New(t)

	require.Equal(1, TypeCharacterMain)
	require.Equal(2, TypeCharacterMinor)
	require.Equal(3, TypeCharacterCarefree)
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaEpisodeCv{},
		"id", "episode_id", "cv_id", "character", "main", "drama_id")
}

func TestRadioDramaEpisodeCv_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_episode_cv", RadioDramaEpisodeCv{}.TableName())
}

func TestListDramaCVByDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有声优信息
	res, err := ListDramaCVByDramaIDs([]int64{3}, TypeCharacterMain)
	require.NoError(err)
	assert.Empty(res)

	// 获取主役声优列表
	res, err = ListDramaCVByDramaIDs([]int64{1, 2, 3}, TypeCharacterMain)
	require.NoError(err)
	require.Len(res, 2)
	assert.EqualValues(1, res[0].DramaID)
	assert.EqualValues(1, res[0].CvID)
	assert.EqualValues(2, res[1].DramaID)
	assert.EqualValues(1, res[1].CvID)

	// 获取从役声优列表
	res, err = ListDramaCVByDramaIDs([]int64{1, 2, 3}, TypeCharacterMinor)
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(2, res[0].DramaID)

	// 获取龙套声优列表
	res, err = ListDramaCVByDramaIDs([]int64{1, 2, 3}, TypeCharacterCarefree)
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(3, res[0].DramaID)

	// 测试剧集声优列表顺序与单集插入声优顺序相同
	res, err = ListDramaCVByDramaIDs([]int64{4}, TypeCharacterMain)
	require.NoError(err)
	require.Len(res, 2)
	assert.EqualValues(4, res[0].CvID)
	assert.EqualValues(3, res[1].CvID)
}
