package mdramalistdramamap

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/drama/mdramalist"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const testDramalistID = 123456

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MDramalistDramaMap{}, "id", "create_time", "modified_time", "dramalist_id", "drama_id", "intro", "sort")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_dramalist_drama_map", MDramalistDramaMap{}.TableName())
}

func TestListDramalistDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有数据
	res, err := ListDramalistDramaIDs(2333)
	require.NoError(err)
	assert.Empty(res)

	// 测试获取剧单内所有剧集 ID
	res, err = ListDramalistDramaIDs(8)
	require.NoError(err)
	require.Len(res, 3)
	assert.EqualValues(1, res[0])
}

func TestFindDramaIDsMapByDramalistIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧单 IDs 长度为 0
	dramalistIDs := make([]int64, 0)
	dramaIDsMap, err := FindDramaIDsMapByDramalistIDs(dramalistIDs)
	require.NoError(err)
	assert.Nil(dramaIDsMap)

	// 测试剧单 IDs 长度不为 0 但剧单不存在时
	dramalistIDs = []int64{1000000000}
	dramaIDsMap, err = FindDramaIDsMapByDramalistIDs(dramalistIDs)
	require.NoError(err)
	assert.Empty(dramaIDsMap)

	// 测试剧单 IDs 长度不为 0 且剧单存在时
	dramalistIDs = []int64{10, 11, 12}
	dramaIDsMap, err = FindDramaIDsMapByDramalistIDs(dramalistIDs)
	require.NoError(err)
	assert.NotEmpty(dramaIDsMap)
	assert.Len(dramaIDsMap, 3)
	require.NotNil(dramaIDsMap[10])
	assert.Len(dramaIDsMap[10], 2)
	assert.Equal(dramaIDsMap[10], []int64{52348, 52349})

	require.NotNil(dramaIDsMap[11])
	assert.Len(dramaIDsMap[11], 1)
	assert.Equal(dramaIDsMap[11], []int64{52349})

	require.NotNil(dramaIDsMap[12])
	assert.Len(dramaIDsMap[12], 1)
	assert.Equal(dramaIDsMap[12], []int64{52350})
}

func TestDramalistDramasListParam_List(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramalistinfo := mdramalist.MDramalist{
		ID:           testDramalistID,
		Title:        "测试剧单标题",
		Intro:        "测试剧单简介",
		UserID:       233,
		CollectCount: 123,
	}
	require.NoError(dramalistinfo.DB().Delete("", "id = ?", testDramalistID).Error)
	require.NoError(dramalistinfo.DB().Create(dramalistinfo).Error)

	var dramalistDramas []MDramalistDramaMap
	for i := 1; i < 4; i++ {
		drama := MDramalistDramaMap{
			DramalistID: testDramalistID,
			DramaID:     int64(i),
			Intro:       fmt.Sprintf("测试剧集简介 %d", i),
			Sort:        int64(i),
		}
		dramalistDramas = append(dramalistDramas, drama)
	}
	require.NoError(dramalistDramas[0].DB().Delete("", "dramalist_id = ?", testDramalistID).Error)
	require.NoError(servicedb.BatchInsert(service.MainDB, dramalistDramas[0].TableName(), dramalistDramas))

	// 测试获取第一页数据
	param := DramalistDramasListParam{
		DramalistID: testDramalistID,
		Page:        1,
		PageSize:    2,
	}
	res, pa, err := param.List()
	require.NoError(err)
	assert.EqualValues(3, pa.Count)
	require.Len(res, 2)
	assert.EqualValues(1, res[0].DramaID)
	assert.EqualValues(2, res[1].DramaID)

	// 测试获取第二页数据
	param.Page = 2
	res, pa, err = param.List()
	require.NoError(err)
	assert.EqualValues(3, pa.Count)
	require.Len(res, 1)
	assert.EqualValues(3, res[0].DramaID)

	// 测试没有数据
	param.Page = 3
	res, pa, err = param.List()
	require.NoError(err)
	require.Empty(res)
	assert.EqualValues(3, pa.Count)
}
