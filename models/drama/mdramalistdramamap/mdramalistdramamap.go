package mdramalistdramamap

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// DramalistMaxDramaNumber 剧单最大收录剧集数
const DramalistMaxDramaNumber = 100

// MDramalistDramaMap 剧单收录剧集表
type MDramalistDramaMap struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 修改时间。单位：秒
	DramalistID  int64  `gorm:"column:dramalist_id"`   // 剧单 ID
	DramaID      int64  `gorm:"column:drama_id"`       // 剧集 ID
	Intro        string `gorm:"column:intro"`          // 剧集介绍（推荐）语
	Sort         int64  `gorm:"column:sort"`           // 排序，值越小排在越前面
}

// DB the db instance of MDramalistDramaMap model
func (m MDramalistDramaMap) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MDramalistDramaMap) TableName() string {
	return "m_dramalist_drama_map"
}

// ListDramalistDramaIDs 获取剧单内所有剧集 ID
func ListDramalistDramaIDs(dramalistID int64) ([]int64, error) {
	var dramaIDs []int64
	err := MDramalistDramaMap{}.DB().Select("drama_id").
		Where("dramalist_id = ?", dramalistID).Order("sort ASC").
		Limit(DramalistMaxDramaNumber).Pluck("drama_id", &dramaIDs).Error
	if err != nil {
		return nil, err
	}
	return dramaIDs, nil
}

// FindDramaIDsMapByDramalistIDs 根据剧单 IDs 获取剧集 IDs map（以剧单 ID 为 key）
func FindDramaIDsMapByDramalistIDs(dramalistIDs []int64) (map[int64][]int64, error) {
	if len(dramalistIDs) == 0 {
		return nil, nil
	}

	var mDramalistDramaMaps []*MDramalistDramaMap
	err := MDramalistDramaMap{}.DB().
		Select("dramalist_id, drama_id").
		Where("dramalist_id IN (?)", dramalistIDs).
		Order("sort ASC").
		Find(&mDramalistDramaMaps).Error
	if err != nil {
		return nil, err
	}

	dramaIDsMap := make(map[int64][]int64, len(mDramalistDramaMaps))
	for _, m := range mDramalistDramaMaps {
		dramaIDsMap[m.DramalistID] = append(dramaIDsMap[m.DramalistID], m.DramaID)
	}

	return dramaIDsMap, nil
}

// DramalistDramasListParam 获取剧单中剧集列表参数
type DramalistDramasListParam struct {
	DramalistID int64
	Page        int64
	PageSize    int64
}

// List 根据剧单 ID 分页获取剧单内剧集信息
func (param *DramalistDramasListParam) List() ([]MDramalistDramaMap, util.Pagination, error) {
	db := MDramalistDramaMap{}.DB().Select("drama_id, intro").
		Where("dramalist_id = ?", param.DramalistID)
	var totalCount int64
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, util.Pagination{}, err
	}
	pa := util.MakePagination(totalCount, param.Page, param.PageSize)
	if !pa.Valid() {
		return []MDramalistDramaMap{}, pa, nil
	}
	db = pa.ApplyTo(db)
	var dramas []MDramalistDramaMap
	err = db.Order("sort ASC").Find(&dramas).Error
	if err != nil {
		return nil, util.Pagination{}, err
	}
	return dramas, pa, nil
}
