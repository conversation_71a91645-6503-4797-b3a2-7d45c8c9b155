package dramatagdrama

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

const tableName = "radio_drama_tag_drama"

// RadioDramaTagDrama 剧集标签和剧集 ID 关联表
type RadioDramaTagDrama struct {
	DramaID int64 `gorm:"column:drama_id" json:"drama_id"`
	TagID   int64 `gorm:"column:tag_id" json:"tag_id"`
}

// DB the db instance of RadioDramaTagDrama model
func (rdtd RadioDramaTagDrama) DB() *gorm.DB {
	return service.DramaDB.Table(rdtd.TableName())
}

// TableName of RadioDramaTagDrama model
func (rdtd RadioDramaTagDrama) TableName() string {
	return tableName
}
