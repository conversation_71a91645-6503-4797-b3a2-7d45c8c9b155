package dramatagdrama

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestRadioDramaTagDramaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaTagDrama{}, "drama_id", "tag_id")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaTagDrama{}, "drama_id", "tag_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_tag_drama", tableName)
	assert.Equal("radio_drama_tag_drama", RadioDramaTagDrama{}.TableName())
}
