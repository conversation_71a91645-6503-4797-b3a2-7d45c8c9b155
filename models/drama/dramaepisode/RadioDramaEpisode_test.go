package dramaepisode

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, VipNot)
	assert.Equal(1, VipNotLimit)
	assert.Equal(2, VipLimit)

	assert.Equal(0, PayTypeFree)
	assert.Equal(1, PayTypeEpisode)
	assert.Equal(2, PayTypeDrama)
}

func TestRadioDramaEpisodeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaEpisode{},
		"id", "name", "drama_id", "sound_id", "date", "order", "type", "pay_type", "subtitle", "vip",
		"create_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaEpisode{},
		"id", "name", "drama_id", "sound_id", "date", "order", "type", "pay_type", "subtitle", "vip",
		"create_time", "modified_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_episode", tableName)
	assert.Equal("radio_drama_episode", RadioDramaEpisode{}.TableName())
}

func TestIsDramaSound(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaID := int64(1)
	soundIDExist := int64(1)
	soundIDNotFound := int64(9999999)

	// 测试缓存不存在
	key := keys.LocalKeyDramaSoundIDs1.Format(dramaID)
	service.Cache5Min.Delete(key)
	v, ok := service.Cache5Min.Get(key)
	require.False(ok)
	assert.Nil(v)

	// 测试音频不在剧集中
	res := IsDramaSound(dramaID, soundIDNotFound)
	assert.False(res)
	// 断言缓存存在
	v, ok = service.Cache5Min.Get(key)
	require.True(ok)
	assert.NotNil(v)

	// 测试缓存存在，音频在剧集中
	res = IsDramaSound(dramaID, soundIDExist)
	assert.True(res)
}

func TestGetSoundIDByEpisodeID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试音频不存在
	episodeID := int64(99999)
	soundID, err := GetSoundIDByEpisodeID(episodeID)
	require.NoError(err)
	assert.EqualValues(0, soundID)

	// 测试音频存在
	episodeID = int64(1)
	soundID, err = GetSoundIDByEpisodeID(episodeID)
	require.NoError(err)
	assert.EqualValues(1, soundID)
}

func TestFindEpisodeBySoundID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试单集不存在
	soundID := int64(99999)
	episode, err := FindEpisodeBySoundID(soundID)
	require.NoError(err)
	assert.Nil(episode)

	// 测试单集存在
	soundID = int64(1)
	episode, err = FindEpisodeBySoundID(soundID)
	require.NoError(err)
	assert.EqualValues(VipLimit, episode.Vip)
	assert.EqualValues(PayTypeDrama, episode.PayType)
}
