package dramaepisode

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

const tableName = "radio_drama_episode"

// 会员单集状态
const (
	VipNot      = iota // 非会员单集
	VipNotLimit        // 会员剧下的试听单集
	VipLimit           // 会员剧下的非试听单集
)

// 剧集付费类型
const (
	PayTypeFree    = iota // 免费
	PayTypeEpisode        // 单集付费
	PayTypeDrama          // 整剧付费
)

// RadioDramaEpisode 单集信息表
type RadioDramaEpisode struct {
	ID           int64  `gorm:"column:id" json:"id"`                       // 单集 ID
	Name         string `gorm:"column:name" json:"name"`                   // 单集名称
	DramaID      int64  `gorm:"column:drama_id" json:"drama_id"`           // 剧集 ID
	SoundID      int64  `gorm:"column:sound_id" json:"sound_id"`           // 音频 ID
	Date         int64  `gorm:"column:date" json:"date"`                   // 发表日期
	Order        int    `gorm:"column:order" json:"order"`                 // 序号
	Type         int    `gorm:"column:type" json:"type"`                   // 类型
	PayType      int    `gorm:"column:pay_type" json:"pay_type"`           // 付费类型
	Subtitle     string `gorm:"column:subtitle" json:"subtitle"`           // 单集副标题
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`     // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time" json:"modified_time"` // 更新时间
	Vip          int    `gorm:"column:vip" json:"vip"`                     // 会员单集状态
}

// DB the db instance of RadioDramaEpisode model
func (d RadioDramaEpisode) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaEpisode model
func (d RadioDramaEpisode) TableName() string {
	return tableName
}

// IsDramaSound 是否是剧集下的音频
func IsDramaSound(dramaID, soundID int64) bool {
	key := keys.LocalKeyDramaSoundIDs1.Format(dramaID)
	v, ok := service.Cache5Min.Get(key)
	if ok {
		soundIDSet := v.(map[int64]struct{})
		_, ok = soundIDSet[soundID]
		return ok
	}
	var soundIDs []int64
	err := service.DramaDB.Table(tableName).Select("sound_id").
		Where("drama_id = ?", dramaID).Pluck("sound_id", &soundIDs).Error
	if err != nil {
		logger.Error(err)
		return false
	}
	soundIDSet := make(map[int64]struct{}, len(soundIDs))
	for i := range soundIDs {
		soundIDSet[soundIDs[i]] = struct{}{}
	}
	service.Cache5Min.SetDefault(key, soundIDSet)
	_, ok = soundIDSet[soundID]
	return ok
}

// GetSoundIDByEpisodeID 根据主键获取音频 ID
func GetSoundIDByEpisodeID(episodeID int64) (int64, error) {
	var soundID int64
	err := service.DramaDB.Table(tableName).Select("sound_id").
		Where("id = ?", episodeID).Row().Scan(&soundID)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	return soundID, nil
}

// FindEpisodeBySoundID 获取音频的会员单集状态
func FindEpisodeBySoundID(soundID int64) (*RadioDramaEpisode, error) {
	var episode RadioDramaEpisode
	err := RadioDramaEpisode{}.DB().Where("sound_id = ?", soundID).Take(&episode).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &episode, nil
}
