package dramasubscription

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramainfo"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

const tableName = "radio_drama_subscription"

// RadioDramaSubscription 剧集订阅表
type RadioDramaSubscription struct {
	ID           int64  `gorm:"column:id" json:"id"`
	UserID       int64  `gorm:"column:user_id" json:"user_id"`               // 用户 ID
	DramaID      int64  `gorm:"column:drama_id" json:"drama_id"`             // 剧集 ID
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`       // 追剧时间（单位：秒）
	IsTop        int    `gorm:"column:is_top" json:"is_top"`                 // 是否置顶
	UpdateTime   int    `gorm:"column:update_time" json:"update_time"`       // 上次观看更新时间（单位：秒）
	SawEpisode   string `gorm:"column:saw_episode" json:"saw_episode"`       // 上次观看的哪一期
	IsSaw        int    `gorm:"column:is_saw" json:"is_saw"`                 // 剧集的更新是否已查看（1 为已查看，0 为未查看）
	SawEpisodeID int64  `gorm:"column:saw_episode_id" json:"saw_episode_id"` // 上次观看的哪一期对应的 episode ID
}

// DB the db instance of RadioDramaSubscription model
func (rds RadioDramaSubscription) DB() *gorm.DB {
	return service.DramaDB.Table(rds.TableName())
}

// TableName for RadioDramaSubscription model
func (rds RadioDramaSubscription) TableName() string {
	return tableName
}

// IsUserSubscribed 用户是否已订阅剧集
func IsUserSubscribed(userID, dramaID int64) (bool, error) {
	rds := new(RadioDramaSubscription)
	err := rds.DB().Where("user_id = ? AND drama_id = ?", userID, dramaID).Take(rds).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// IsIPRDramasSubscribed 用户是否订阅该 IPR 下的剧集
func IsIPRDramasSubscribed(iprID, userID int64) (bool, error) {
	subscribed := new(RadioDramaSubscription)
	err := service.DramaDB.Select("info.id").
		Table(fmt.Sprintf("%s AS info", dramainfo.RadioDramaDramainfo{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS sub ON sub.drama_id = info.id", RadioDramaSubscription{}.TableName())).
		Where("info.ip_id = ? AND sub.user_id = ?", iprID, userID).
		Take(subscribed).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// SubscribeDramaFeed 订阅的剧集动态
type SubscribeDramaFeed struct {
	dramainfo.RadioDramaDramainfo

	IsSaw        int    `gorm:"column:is_saw"`
	SawEpisode   string `gorm:"column:saw_episode"`
	SawEpisodeID int64  `gorm:"column:saw_episode_id"`

	ViewCount30Days int64 `gorm:"-"`                  // 剧集近 30 天内播放次数
	UpdateTime      int64 `gorm:"column:update_time"` // 上次观看时间
}

// AfterFind is a GORM hook for query
func (s *SubscribeDramaFeed) AfterFind() error {
	if s.Cover != nil && *s.Cover != "" {
		s.CoverURL = service.Storage.Parse(params.URL.DramaCoverURL + *s.Cover)
	} else {
		s.CoverURL = service.Storage.Parse(params.URL.DefaultCoverURL)
	}
	return nil
}

// GetFeedDramas 获取有更新的订阅剧集信息
func GetFeedDramas(userID int64, startTime time.Time) ([]SubscribeDramaFeed, error) {
	var resp []SubscribeDramaFeed
	// 获取上次请求「我听」页数据后有更新的订阅剧集
	err := service.DramaDB.Select("t.*, t1.update_time, t1.is_saw, t1.saw_episode, t1.saw_episode_id").
		Table(fmt.Sprintf("%s AS t", dramainfo.RadioDramaDramainfo{}.TableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS t1 ON t1.drama_id = t.id", RadioDramaSubscription{}.TableName())).
		Where("t1.user_id = ? AND t.checked = ? AND t.lastupdate_time > ?", userID, dramainfo.CheckedPass, startTime.Unix()).
		Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CountUserSubscribedDramas 获取用户订阅剧集数
func CountUserSubscribedDramas(userID int64, dramaIDs []int64) (int64, error) {
	if len(dramaIDs) == 0 {
		return 0, nil
	}
	var count int64
	err := RadioDramaSubscription{}.DB().Where("user_id = ? AND drama_id IN (?)", userID, dramaIDs).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// UserSubscribedDramaMap 获取用户订阅的剧集 IDs
func UserSubscribedDramaMap(userID int64, dramaIDs []int64) (map[int64]struct{}, error) {
	if len(dramaIDs) == 0 {
		return nil, nil
	}

	var userSubscribedDramaIDs []int64
	err := RadioDramaSubscription{}.DB().Select("drama_id").
		Where("user_id = ? AND drama_id IN (?)", userID, dramaIDs).
		Pluck("drama_id", &userSubscribedDramaIDs).Error
	if err != nil {
		return nil, err
	}
	if len(userSubscribedDramaIDs) == 0 {
		return nil, nil
	}

	userSubscribedDramaMap := make(map[int64]struct{}, len(userSubscribedDramaIDs))
	for _, dramaID := range userSubscribedDramaIDs {
		userSubscribedDramaMap[dramaID] = struct{}{}
	}
	return userSubscribedDramaMap, nil
}
