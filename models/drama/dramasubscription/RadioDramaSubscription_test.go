package dramasubscription

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaSubscriptionTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaSubscription{},
		"id", "user_id", "drama_id", "create_time", "is_top", "update_time", "saw_episode",
		"is_saw", "saw_episode_id")

	kc = tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaSubscription{},
		"id", "user_id", "drama_id", "create_time", "is_top", "update_time", "saw_episode",
		"is_saw", "saw_episode_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_subscription", tableName)
	assert.Equal("radio_drama_subscription", RadioDramaSubscription{}.TableName())
}

func TestIsUserSubscribed(t *testing.T) {
	assert := assert.New(t)

	// 测试用户未订阅
	su, err := IsUserSubscribed(12, 2)
	assert.NoError(err)
	assert.False(su)

	// 测试用户已订阅
	su, err = IsUserSubscribed(100, 10)
	assert.NoError(err)
	assert.True(su)
}

func TestIsIPRDramasSubscribed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	subscribed, err := IsIPRDramasSubscribed(1, 100)
	require.NoError(err)
	assert.True(subscribed)

	subscribed, err = IsIPRDramasSubscribed(100, 100)
	require.NoError(err)
	assert.False(subscribed)
}

func TestSubscribeDramaFeed_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s := SubscribeDramaFeed{}
	err := s.AfterFind()
	require.NoError(err)
	assert.Equal(service.Storage.Parse(params.URL.DefaultCoverURL), s.CoverURL)

	cover := "20210/15/9964ffc7ece0d56e65221d36dd9566b60830999.jpg"
	s.Cover = &cover
	err = s.AfterFind()
	require.NoError(err)
	assert.Equal(service.Storage.Parse(params.URL.DramaCoverURL)+*s.Cover, s.CoverURL)
}

func TestGetFeedDramas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(3010224)
	startTime := time.Unix(0, 0)

	resp, err := GetFeedDramas(userID, startTime)
	require.NoError(err)
	assert.NotNil(resp)
	assert.EqualValues(6, resp[0].ID)
	assert.EqualValues(7, resp[1].ID)
	assert.EqualValues(8, resp[2].ID)
	assert.EqualValues(9, resp[3].ID)
	assert.EqualValues(12434877, resp[3].CoverColor)
	assert.EqualValues(2, resp[3].PayType)
	assert.Equal("第四期", resp[3].Newest)
	assert.EqualValues(1530502200, resp[3].LastUpdateTime)
	assert.Equal("测试", resp[3].SawEpisode)
	assert.EqualValues(1, resp[3].IsSaw)
	assert.EqualValues(10, resp[3].SawEpisodeID)
}

func TestCountUserSubscribedDramas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试传入剧集 ID 列表为空
	userID := int64(3010224)
	res, err := CountUserSubscribedDramas(userID, []int64{})
	require.NoError(err)
	assert.EqualValues(0, res)

	// 测试用户没有订阅剧集
	res, err = CountUserSubscribedDramas(userID, []int64{1, 2, 3})
	require.NoError(err)
	assert.EqualValues(0, res)

	// 测试获取用户订阅剧集列表
	res, err = CountUserSubscribedDramas(userID, []int64{6, 7, 8})
	require.NoError(err)
	assert.EqualValues(3, res)
}

func TestUserSubscribedDramaMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集 IDs 长度为 0
	ids := make([]int64, 0)
	dramaMap, err := UserSubscribedDramaMap(820592, ids)
	require.NoError(err)
	assert.Nil(dramaMap)

	// 测试剧集 IDs 长度不为 0 但剧集不存在时
	ids = []int64{1000000000}
	dramaMap, err = UserSubscribedDramaMap(820592, ids)
	require.NoError(err)
	assert.Nil(dramaMap)

	// 测试剧集 IDs 长度不为 0 且剧集存在时
	ids = []int64{52348, 52349, 52350}
	dramaMap, err = UserSubscribedDramaMap(820592, ids)
	require.NoError(err)
	require.NotNil(dramaMap)
	assert.Len(dramaMap, 2)
	_, ok := dramaMap[52348]
	require.True(ok)
	_, ok = dramaMap[52349]
	require.True(ok)
	_, ok = dramaMap[52350]
	require.False(ok)
}
