package dramasubscribeplaylog

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "radio_drama_subscribe_play_log_30day"

// RadioDramaSubscribePlayLog30Day 用户近 30 天的订阅剧集收听记录表
// 以 create_time 为分区，每个分区记录 30 天前至当天 0 点（T-30 ~ T-1）用户收听的订阅剧集 ID 和总播放量
// 例：分区 p20230423, create_time 的值为 2023-04-23 00:00:00 时间戳，该分区内的数据为 2023-03-24 00:00:00 - 2023-04-23 00:00:00 用户收听的订阅剧集 ID 和总播放量
type RadioDramaSubscribePlayLog30Day struct {
	ID           int64 `gorm:"column:id" json:"id"`
	UserID       int64 `gorm:"column:user_id" json:"user_id"`             // 用户 ID
	DramaID      int64 `gorm:"column:drama_id" json:"drama_id"`           // 用户 30 天内收听过的订阅剧集 ID
	ViewCount    int64 `gorm:"column:view_count" json:"view_count"`       // 30 天内播放次数
	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`     // 创建时间（分区时间，单位：秒）
	ModifiedTime int64 `gorm:"column:modified_time" json:"modified_time"` // 更新时间（单位：秒）
}

// DB the db instance of radio_drama_subscribe_play_log_30day model
func (rds RadioDramaSubscribePlayLog30Day) DB() *gorm.DB {
	return service.DramaDB.Table(rds.TableName())
}

// TableName for radio_drama_subscribe_play_log_30day model
func (rds RadioDramaSubscribePlayLog30Day) TableName() string {
	return tableName
}

// GetUserSubscribeDramaPlayLogs 获取用户订阅剧集播放记录
func GetUserSubscribeDramaPlayLogs(dramaIDs, partitions []int64, userID int64) ([]*RadioDramaSubscribePlayLog30Day, error) {
	if len(dramaIDs) == 0 || userID <= 0 {
		return nil, nil
	}
	var playLogs []*RadioDramaSubscribePlayLog30Day
	err := RadioDramaSubscribePlayLog30Day{}.DB().
		Select("drama_id, view_count, create_time").
		Where("user_id = ? AND drama_id IN (?) AND create_time IN (?)", userID, dramaIDs, partitions).
		Find(&playLogs).Error
	if err != nil {
		return nil, err
	}
	return playLogs, nil
}

// GetUserSubscribeDramaViewCount 获取用户订阅剧集 30 天内的播放量
func GetUserSubscribeDramaViewCount(dramaIDs []int64, userID int64) (map[int64]int64, error) {
	// 获取当前分区时间戳
	now := util.TimeNow()
	// 当前分区
	partition := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location()).Unix()
	// 当前分区没有播放记录时，需要获取前一个分区的播放记录
	lastPartition := time.Date(now.Year(), now.Month(), now.Day()-2, 0, 0, 0, 0, now.Location()).Unix()
	playLogs, err := GetUserSubscribeDramaPlayLogs(dramaIDs, []int64{partition, lastPartition}, userID)
	if err != nil {
		return nil, err
	}
	if len(playLogs) == 0 {
		return nil, nil
	}
	var partitionPlayLogs, lastPartitionPlayLogs []*RadioDramaSubscribePlayLog30Day
	for _, v := range playLogs {
		switch v.CreateTime {
		case partition:
			partitionPlayLogs = append(partitionPlayLogs, v)
		case lastPartition:
			lastPartitionPlayLogs = append(lastPartitionPlayLogs, v)
		}
	}
	if len(partitionPlayLogs) == 0 && len(lastPartitionPlayLogs) == 0 {
		return nil, nil
	} else if len(partitionPlayLogs) == 0 {
		// 当前分区没有播放记录时，则获取前一个分区的播放记录
		partitionPlayLogs = lastPartitionPlayLogs
	}
	playLogMap := make(map[int64]int64, len(partitionPlayLogs))
	for _, log := range playLogs {
		playLogMap[log.DramaID] = log.ViewCount
	}
	return playLogMap, nil
}
