package dramasubscribeplaylog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetUserSubscribeDramaPlayLogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaIDs := []int64{6, 7, 8}
	userID := int64(3010224)
	partition := []int64{1681747200}

	dramaPlayLogs, err := GetUserSubscribeDramaPlayLogs(dramaIDs, partition, userID)
	require.NoError(err)
	assert.NotNil(dramaPlayLogs)
}

func TestGetUserSubscribeDramaViewCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaIDs := []int64{6, 7, 8}
	userID := int64(3010224)

	// 测试获取当前分区的
	now := util.TimeNow()
	partition := util.BeginningOfDay(now).Unix()
	err := RadioDramaSubscribePlayLog30Day{}.DB().
		Where("id IN (?)", []int64{1, 2, 3}).
		Updates(map[string]interface{}{"create_time": partition - 86400}).Error
	require.NoError(err)
	dramaViewCountMap, err := GetUserSubscribeDramaViewCount(dramaIDs, userID)
	require.NoError(err)
	assert.Equal(map[int64]int64{6: 2, 7: 2, 8: 20}, dramaViewCountMap)

	// 测试获取前一个分区的
	err = RadioDramaSubscribePlayLog30Day{}.DB().
		Where("id IN (?)", []int64{1, 2, 3}).
		Updates(map[string]interface{}{"create_time": partition - 86400 - 86400}).Error
	require.NoError(err)
	dramaViewCountMap, err = GetUserSubscribeDramaViewCount(dramaIDs, userID)
	require.NoError(err)
	assert.Equal(map[int64]int64{6: 2, 7: 2, 8: 20}, dramaViewCountMap)
}
