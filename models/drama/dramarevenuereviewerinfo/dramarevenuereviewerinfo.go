package dramarevenuereviewerinfo

import (
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// DramaRevenueReviewerInfo 剧集收益后台用户信息表
type DramaRevenueReviewerInfo struct {
	ID            int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime    int64  `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime  int64  `gorm:"column:modified_time"`  // 修改时间。单位：秒
	UserID        int64  `gorm:"column:user_id"`        // 用户 ID
	MobileEncrypt string `gorm:"column:mobile_encrypt"` // 手机号码（加密）
	Region        int    `gorm:"column:region"`         // 国际电话区号

	Mobile string `gorm:"-"` // 解密后的手机号码
}

// DB the db instance of DramaRevenueReviewerInfo model
func (d DramaRevenueReviewerInfo) DB() *gorm.DB {
	return service.MainDB.Table(d.TableName())
}

// TableName for current model
func (DramaRevenueReviewerInfo) TableName() string {
	return "drama_revenue_reviewer_info"
}

// BeforeCreate hook
func (d *DramaRevenueReviewerInfo) BeforeCreate(scope *gorm.Scope) (err error) {
	now := util.TimeNow().Unix()
	d.CreateTime = now
	d.ModifiedTime = now
	if d.Mobile != "" {
		// 手机号加密保存
		d.MobileEncrypt = util.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, strconv.FormatInt(d.CreateTime, 10), d.Mobile)
	}

	return nil
}

// AfterFind is a GORM hook for query
func (d *DramaRevenueReviewerInfo) AfterFind() error {
	if d.MobileEncrypt != "" && d.CreateTime != 0 {
		// 手机号码解密
		mobile, err := util.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, strconv.FormatInt(d.CreateTime, 10), d.MobileEncrypt)
		if err != nil {
			return err
		}
		d.Mobile = mobile
	}

	return nil
}

// FindByUserID 根据用户 ID 查询
func FindByUserID(userID int64) (*DramaRevenueReviewerInfo, error) {
	dramaRevenueReviewerInfo := new(DramaRevenueReviewerInfo)
	err := DramaRevenueReviewerInfo{}.DB().
		Where("user_id = ?", userID).
		Take(dramaRevenueReviewerInfo).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return dramaRevenueReviewerInfo, nil
}
