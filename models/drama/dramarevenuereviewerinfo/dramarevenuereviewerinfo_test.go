package dramarevenuereviewerinfo

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/config"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestDramaRevenueReviewerInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(DramaRevenueReviewerInfo{},
		"id", "create_time", "modified_time", "user_id", "mobile_encrypt", "region")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("drama_revenue_reviewer_info", DramaRevenueReviewerInfo{}.TableName())
}

func TestBeforeCreate(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	d := new(DramaRevenueReviewerInfo)
	mobile := "18510086543"
	d.Mobile = mobile
	require.NoError(d.BeforeCreate(nil))
	assert.Equal(now.Unix(), d.CreateTime)
	assert.Equal(now.Unix(), d.ModifiedTime)
	assert.NotEqual(mobile, d.MobileEncrypt)
	m, err := util.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, strconv.FormatInt(now.Unix(), 10), d.MobileEncrypt)
	require.NoError(err)
	assert.Equal(mobile, m)
}

func TestAfterFind(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := util.TimeNow().Unix()
	d := DramaRevenueReviewerInfo{
		UserID:        100,
		MobileEncrypt: util.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, strconv.FormatInt(now, 10), "18510086543"),
		Region:        86,
		CreateTime:    now,
	}
	require.NoError(d.AfterFind())
	assert.Equal("18510086543", d.Mobile)
	assert.Equal(86, d.Region)
	assert.EqualValues(100, d.UserID)
	assert.Equal(now, d.CreateTime)
}

func TestFindByUserID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := util.TimeNow()
	util.SetTimeNow(func() time.Time { return now })
	defer util.SetTimeNow(nil)

	// 删除测试数据
	require.NoError(DramaRevenueReviewerInfo{}.DB().Delete("", "user_id = ?", 100).Error)

	// 测试用户数据不存在时
	reviewerInfo, err := FindByUserID(100)
	require.NoError(err)
	assert.Nil(reviewerInfo)

	// 创建测试数据
	require.NoError(DramaRevenueReviewerInfo{}.DB().Create(&DramaRevenueReviewerInfo{
		UserID: 100,
		Mobile: "18510086543",
		Region: 86,
	}).Error)

	// 测试用户数据存在时
	reviewerInfo, err = FindByUserID(100)
	require.NoError(err)
	require.NotNil(reviewerInfo)
	assert.Equal("18510086543", reviewerInfo.Mobile)
	assert.Equal(86, reviewerInfo.Region)
	assert.EqualValues(100, reviewerInfo.UserID)
	assert.Equal(now.Unix(), reviewerInfo.CreateTime)
	assert.Equal(now.Unix(), reviewerInfo.ModifiedTime)
}
