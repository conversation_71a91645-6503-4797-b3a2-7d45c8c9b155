package dramaipr

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaIPRTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaIPR{}, "id", "name", "user_id", "seasons", "create_time", "modified_time")
}

func TestFindOneByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ipr, err := FindOneByID(1)
	require.NoError(err)
	assert.NotNil(ipr)

	ipr, err = FindOneByID(10)
	require.NoError(err)
	assert.Nil(ipr)
}
