package dramaipr

import (
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// RadioDramaIPR 剧集 IPR 信息表
type RadioDramaIPR struct {
	ID      int64  `gorm:"column:id"`
	Name    string `gorm:"column:name"`    // IPR 名
	Seasons string `gorm:"column:seasons"` // 季度信息 (格式为 JSON)
	UserID  int64  `gorm:"column:user_id"` // 创建者 ID

	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
}

// TableName for RadioDramaIPR model
func (RadioDramaIPR) TableName() string {
	return "radio_drama_ip"
}

// FindOneByID 根据主键查询一条记录
func FindOneByID(id int64) (*RadioDramaIPR, error) {
	ipr := new(RadioDramaIPR)
	err := service.DramaDB.Where("id = ?", id).Take(ipr).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return ipr, nil
}
