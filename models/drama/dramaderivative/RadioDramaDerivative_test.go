package dramaderivative

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaDerivative(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaDerivative{},
		"id", "ip_id", "user_id", "element_id", "type", "title", "intro", "cover",
		"tag", "url", "sort", "create_time", "modified_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_derivative", RadioDramaDerivative{}.TableName())
}

func TestFindDerivativesByIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := FindDerivativesByIP(int64(1), 10)
	require.NoError(err)
	require.NotEmpty(result)
	assert.Equal(int64(1), result[0].ElementID)
}

func TestTypeName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("商品", TypeName(1, "测试"))

	assert.Equal("测试", TypeName(4, "测试"))
}
