package dramaderivative

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// 推荐周边分类
const (
	TypeGoods   = iota + 1 // 商品
	TypeOmikuji            // 求签包
	TypeVoice              // 语音包
)

const sortHidden = -1 // 周边隐藏（下架）

var typeNameMap = map[int]string{
	TypeGoods:   "商品",
	TypeOmikuji: "求签包",
	TypeVoice:   "语音包",
}

// RadioDramaDerivative 周边信息表
type RadioDramaDerivative struct {
	ID           int64  `gorm:"column:id;primary_key"`
	IPID         int64  `gorm:"column:ip_id"`         // 推荐元素所属剧集 IP 的 ID
	UserID       int64  `gorm:"column:user_id"`       // 用户 ID
	ElementID    int64  `gorm:"column:element_id"`    // 推荐元素 ID
	Type         int    `gorm:"column:type"`          // 元素分类 1：商品；2：求签包；3：语音包；4：通用
	Title        string `gorm:"column:title"`         // 标题
	Intro        string `gorm:"column:intro"`         // 简介
	Cover        string `gorm:"column:cover"`         // 封面图
	Tag          string `gorm:"column:tag"`           // 标签
	URL          string `gorm:"column:url"`           // 链接
	Sort         int    `gorm:"column:sort"`          // 排序
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time"` // 最后修改时间
}

// DB the db instance of RadioDramaDerivative model
func (rdd RadioDramaDerivative) DB() *gorm.DB {
	return service.DramaDB.Table(rdd.TableName())
}

// TableName for RadioDramaDerivative model
func (RadioDramaDerivative) TableName() string {
	return "radio_drama_derivative"
}

// AfterFind is a GORM hook for query
func (rdd *RadioDramaDerivative) AfterFind() error {
	if rdd.Cover != "" {
		rdd.Cover = service.Storage.Parse(rdd.Cover)
	}
	return nil
}

// FindDerivativesByIP 获取周边信息
func FindDerivativesByIP(ipID int64, count int) ([]*RadioDramaDerivative, error) {
	var derivatives []*RadioDramaDerivative
	err := RadioDramaDerivative{}.DB().Select("type, element_id, title, intro, tag, url, cover").
		Where("ip_id = ? AND sort <> ?", ipID, sortHidden).
		Order("sort DESC").Limit(count).Find(&derivatives).Error
	if err != nil {
		return nil, err
	}
	return derivatives, nil
}

// TypeName 周边信息分类名
func TypeName(derivativeType int, derivativeTag string) string {
	typeName, ok := typeNameMap[derivativeType]
	if ok {
		return typeName
	}
	return derivativeTag
}
