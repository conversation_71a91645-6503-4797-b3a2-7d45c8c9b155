package dramainfo

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/service"
)

// SoundIDDramaID sound_id 和对应的 drama_id
type SoundIDDramaID struct {
	DramaID int64 `json:"drama_id"`
	SoundID int64 `json:"sound_id"`
}

// GetDramaIDsBySoundIDs 获取 sound_id 对应的 drama_id
func GetDramaIDsBySoundIDs(soundIDs []int64) (ids []SoundIDDramaID, err error) {
	err = service.DramaDB.Table(dramaepisode.RadioDramaEpisode{}.TableName()+" AS e").
		Select("e.drama_id, e.sound_id").
		Joins(fmt.Sprintf("LEFT JOIN %s AS a ON a.id = e.drama_id",
			tableName)).
		Where("e.sound_id IN (?) AND a.checked IN (?)",
			soundIDs, []int8{CheckedPass, CheckedContractExpired}).
		Find(&ids).Error
	if ids == nil {
		ids = make([]SoundIDDramaID, 0)
	}
	return
}
