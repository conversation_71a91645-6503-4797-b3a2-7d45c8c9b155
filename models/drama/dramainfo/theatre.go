package dramainfo

import (
	"strconv"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

// IsTheatreDrama 是否是盲盒剧场剧集
func IsTheatreDrama(dramaID int64) bool {
	key := keys.KeyTheatreDramaIDs0.Format()
	exists, err := service.Redis.SIsMember(key, dramaID).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	return exists
}

// IsTheatreDramaSound 是否是盲盒剧场的音频
func IsTheatreDramaSound(soundID int64) bool {
	key := keys.LocalKeyTheatreSoundIDs0.Format()
	v, ok := service.Cache5Min.Get(key)
	if ok {
		soundIDSet := v.(map[int64]struct{})
		_, ok = soundIDSet[soundID]
		return ok
	}
	dramaKey := keys.KeyTheatreDramaIDs0.Format()
	members, err := service.Redis.SMembers(dramaKey).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	dramaIDs := make([]int64, 0, len(members))
	for i := range members {
		dramaID, err := strconv.ParseInt(members[i], 10, 64)
		if err != nil {
			// PASS
			logger.Error(err)
			continue
		}
		dramaIDs = append(dramaIDs, dramaID)
	}
	if len(dramaIDs) == 0 {
		service.Cache5Min.SetDefault(key, map[int64]struct{}{})
		return false
	}
	var soundIDs []int64
	err = service.DramaDB.Table(dramaepisode.RadioDramaEpisode{}.TableName()).Select("sound_id").
		Where("drama_id IN (?)", dramaIDs).Pluck("sound_id", &soundIDs).Error
	if err != nil {
		logger.Error(err)
		return false
	}
	soundIDSet := make(map[int64]struct{}, len(soundIDs))
	for i := range soundIDs {
		soundIDSet[soundIDs[i]] = struct{}{}
	}
	service.Cache5Min.SetDefault(key, soundIDSet)
	_, ok = soundIDSet[soundID]
	return ok
}

// IsTheatreNewDrama 是否是盲盒剧场新作速递星盒里的剧集
func IsTheatreNewDrama(dramaID int64) bool {
	key := keys.KeyTheatreNewDramaIDs0.Format()
	exists, err := service.Redis.SIsMember(key, dramaID).Result()
	if err != nil {
		logger.Error(err)
		return false
	}
	return exists
}
