package dramainfo

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/config/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramaprice"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramatag"
	"github.com/MiaoSiLa/missevan-go/models/drama/dramatagdrama"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 剧集的付费类型 0：免费；1：单集付费；2：整剧付费
const (
	PayTypeFree uint32 = iota
	PayTypeEpisode
	PayTypeDrama
)

// 剧集的付费情况 0：免费；1：付费剧集未付费；2：付费剧集已付费
const (
	NeedPayFree int = iota
	NeedPayUnpaid
	NeedPayPaid
)

const tableName = "radio_drama_dramainfo"

// 剧集属性类型
const (
	DramaTypeRisking         = "risking"           // 擦边球
	DramaTypeJapanForbidden  = "japan_forbidden"   // 日本禁听
	DramaTypeInteractive     = "interactive"       // 互动广播剧
	DramaTypeLossless        = "lossless"          // 无损音质广播剧
	DramaTypeSpecial         = "special"           // 特殊剧集
	DramaTypeSensitive       = "sensitive"         // 敏感剧集
	DramaTypeNoJapanSale     = "no_japan_sale"     // 日本禁购
	DramaTypeNoLiveRecommend = "no_live_recommend" // 无推荐直播模块的剧集
	DramaTypeLimitAddDanmaku = "limit_add_danmaku" // 限制发送弹幕
	DramaTypeSearchHidden    = "search_hidden"     // 搜索隐藏
)

// refined 定义，此字段定义值与音频表（m_sound.refined）不同，注意区分
const (
	// RefinedRisking 是否为擦边球（比特位第一位为 1）
	RefinedRisking = iota + 1
	// RefinedJapanForbidden 是否为日本禁听（比特位第二位为 1）
	RefinedJapanForbidden
	// RefinedInteractive 是否为互动广播剧（比特位第三位为 1）
	RefinedInteractive
	// RefinedLossless 是否为无损音质广播剧（比特位第四位为 1）
	RefinedLossless
	// RefinedSpecial 是否为特殊剧集（比特位第五位为 1）
	RefinedSpecial
	// RefinedSensitive 是否为敏感剧集（比特位第六位为 1）
	RefinedSensitive
	// RefinedNoJapanSale 是否为日本地区禁购剧集（比特位第七位为 1）
	RefinedNoJapanSale
	// RefinedNoLiveRecommend 是否为无推荐直播模块剧集（比特位第八位为 1）
	RefinedNoLiveRecommend
	// RefinedLimitAddDanmaku 是否为限制用户发送弹幕（比特位第九位为 1）
	RefinedLimitAddDanmaku
	// RefinedSearchHidden 是否为搜索隐藏（比特位第十五位为 1）
	RefinedSearchHidden = 15
)

// FindDramaMaxBatchSize 批量查询剧集的最大条数
const FindDramaMaxBatchSize = 1000

// 是否为会员剧 0：否；1：是
const (
	VipNot = iota
	VipDrama
)

// RadioDramaDramainfo 剧集信息表
type RadioDramaDramainfo struct {
	ID              int64        `gorm:"column:id" json:"id"`                             // 剧集 ID
	Name            *string      `gorm:"column:name" json:"name"`                         // 剧集名称
	Cover           *string      `gorm:"column:cover" json:"-"`                           // 剧集海报
	CoverURL        string       `gorm:"-" json:"cover"`                                  // 剧集海报的全路径
	CoverColor      int          `gorm:"column:cover_color" json:"cover_color"`           // RGB 颜色值
	Abstract        *string      `gorm:"column:abstract" json:"abstract"`                 // 剧集简介
	Integrity       *int8        `gorm:"column:integrity" json:"integrity"`               // 完结度
	IntegrityName   string       `gorm:"-" json:"integrity_name"`                         // 完结度命名
	Age             *int8        `gorm:"column:age" json:"age"`                           // 年代
	Origin          *int8        `gorm:"column:origin" json:"origin"`                     // 创作类型
	Author          *string      `gorm:"column:author" json:"author"`                     // 原作者
	Birthday        int8         `gorm:"column:birthday" json:"birthday"`                 // 生日剧
	CV              *string      `gorm:"column:cv" json:"cv"`                             // 生日 CV
	IP              int8         `gorm:"column:ip" json:"ip"`                             // 同人剧
	IPName          *string      `gorm:"column:ipname" json:"ipname"`                     // 原作标签
	Type            int32        `gorm:"column:type" json:"type"`                         // 分类类型
	TypeName        string       `gorm:"-" json:"type_name"`                              // 分类类型名称
	Newest          string       `gorm:"column:newest" json:"newest"`                     // 更新至
	OrganizationID  int64        `gorm:"column:organization_id" json:"organization_id"`   // 社团 ID
	UserID          int64        `gorm:"column:user_id" json:"user_id"`                   // 所属用户 ID
	UserName        string       `gorm:"column:username" json:"username"`                 // 所属用户名
	Checked         int8         `gorm:"column:checked" json:"checked"`                   // 审核状态（0 为未审核，1 为审核通过，2 为审核未通过）
	CreateTime      int64        `gorm:"column:create_time" json:"create_time"`           // 发布时间
	LastUpdateTime  int64        `gorm:"column:lastupdate_time" json:"lastupdate_time"`   // 最后编辑时间
	ViewCount       int64        `gorm:"column:view_count" json:"view_count"`             // 查看次数
	Catalog         int64        `gorm:"column:catalog" json:"catalog"`                   // 剧集分类
	CatalogName     string       `gorm:"-" json:"catalog_name"`                           // 剧集分类名称
	Alias           *string      `gorm:"column:alias" json:"alias"`                       // 别名
	PayType         uint32       `gorm:"column:pay_type" json:"pay_type"`                 // 付费类型（0 免费，1 单音付费，2 剧集付费）
	Push            int8         `gorm:"column:push" json:"push"`                         // 新增单集是否过审推送
	Refined         util.BitMask `gorm:"column:refined" json:"refined"`                   // 属性，比特位第一位为 1 时标识擦边球
	Police          int8         `gorm:"column:police" json:"police"`                     // 是否报警
	IPID            int64        `gorm:"column:ip_id" json:"ip_id"`                       // 剧集所属 ip 的 ID
	SubscriptionNum float64      `gorm:"column:subscription_num" json:"subscription_num"` // 订阅（追剧）人数
	Vip             int          `gorm:"column:vip" json:"vip"`                           // 是否为会员剧（0：否；1：是）

	Tags      []dramatag.RadioDramaTag `gorm:"-" json:"tags"`
	Price     *int64                   `gorm:"-" json:"price"`
	Purchased *bool                    `gorm:"-" json:"purchased,omitempty"` // 用户是否购买过该剧集，之后应使用 need_pay
	NeedPay   *int                     `gorm:"-" json:"need_pay,omitempty"`  // 用户对剧集的付费状态
}

// DB the db instance of RadioDramaDramainfo model
func (d RadioDramaDramainfo) DB() *gorm.DB {
	return service.DramaDB.Table(d.TableName())
}

// TableName for RadioDramaDramainfo model
func (d RadioDramaDramainfo) TableName() string {
	return tableName
}

func integrityName(integrity int8) string {
	switch integrity {
	case IntegrityNameSerializing:
		return "长篇未完结"
	case IntegrityNameEnd:
		return "长篇完结"
	case IntegrityNameOne:
		return "全一期"
	case IntegrityNameMini:
		return "微小剧"
	default:
		return ""
	}
}

func catalogName(catalogID int64) string {
	// TODO: 后续从 catalog 表中获取剧集类型名
	switch catalogID {
	case CatalogIDCnRadioDrama:
		return "中文广播剧"
	case CatalogIDCnCartoon:
		return "中文有声漫画"
	case CatalogIDJapanRadioDrama:
		return "日文广播剧"
	case CatalogIDJapanAudioComics:
		return "日文有声漫画"
	case CatalogIDRadio:
		return "播客"
	case CatalogIDAudioBookLightNovel:
		return "轻小说"
	case CatalogIDAudioBookNetwork:
		return "网络小说"
	case CatalogIDMusic:
		return "音乐"
	case CatalogIDAsmr:
		return "催眠"
	case CatalogIDAudioBookChildren:
		return "儿童"
	case CatalogIDSoundLovers:
		return "声音恋人"
	default:
		return ""
	}
}

func dramaTypeName(dramaType int32) string {
	switch dramaType {
	case TypeGeneralAudience:
		return "全年龄"
	case TypeBoysLove:
		return "纯爱"
	case TypeLesbian:
		return "双女主"
	case TypeRomantic:
		return "言情"
	case TypeSerializing:
		return "未完结"
	case TypeEnd:
		return "完结"
	case TypeOneAndMini:
		return "全一期"
	default:
		return "未知"
	}
}

func japanDramaTypeName(dramaType int32) string {
	switch dramaType {
	case TypeGeneralAudience:
		return "一般"
	case TypeBoysLove:
		return "纯爱"
	case TypeRomantic:
		return "乙女"
	case TypeSerializing:
		return "未完结"
	case TypeEnd:
		return "完结"
	case TypeOneAndMini:
		return "全一期"
	default:
		return "未知"
	}
}

// AfterFind is a GORM hook for query
func (d *RadioDramaDramainfo) AfterFind() error {
	// TODO: catalog
	if d.Cover != nil && *d.Cover != "" {
		d.CoverURL = service.Storage.Parse(params.URL.DramaCoverURL + *d.Cover)
	} else {
		d.CoverURL = service.Storage.Parse(params.URL.DefaultCoverURL)
	}
	if d.Integrity != nil {
		d.IntegrityName = integrityName(*d.Integrity)
	}

	// 获取类型名称
	if d.Catalog != 0 {
		d.CatalogName = catalogName(d.Catalog)
	}

	// 获取分类名称
	if d.Type != 0 {
		d.TypeName = typeName(d.Catalog, d.Type, false)
	}

	return nil
}

// JoinRadioDramaDramainfo 补充 dramas 的 tag, price 等信息
func JoinRadioDramaDramainfo(dramas []RadioDramaDramainfo) error {
	if len(dramas) == 0 {
		return nil
	}
	ids := make([]int64, len(dramas))
	for i, v := range dramas {
		ids[i] = v.ID
	}

	var tags []struct {
		DramaID int64 `gorm:"column:drama_id"`
		dramatag.RadioDramaTag
	}
	err := service.DramaDB.Table(dramatag.RadioDramaTag{}.TableName()+" AS t").
		Joins("LEFT JOIN "+dramatagdrama.RadioDramaTagDrama{}.TableName()+" AS td ON t.id = td.tag_id").
		Where("td.drama_id IN (?)", ids).
		Select("td.drama_id, t.*").
		Scan(&tags).Error
	if err != nil {
		return err
	}
	mapIDTags := make(map[int64][]dramatag.RadioDramaTag)
	for _, v := range tags {
		mapIDTags[v.DramaID] = append(mapIDTags[v.DramaID], v.RadioDramaTag)
	}

	var prices []struct {
		DramaID int64 `gorm:"column:drama_id"`
		Price   int64 `gorm:"column:price"`
	}
	err = service.DramaDB.Table(dramaprice.RadioDramaPrice{}.TableName()).Where("drama_id IN (?)", ids).
		Select("drama_id, price").Scan(&prices).Error
	if err != nil {
		return err
	}
	mapIDPrice := make(map[int64]int64)
	for _, v := range prices {
		mapIDPrice[v.DramaID] = v.Price
	}

	for i, v := range dramas {
		dramas[i].Tags = mapIDTags[v.ID]

		price, ok := mapIDPrice[v.ID]
		if ok {
			dramas[i].Price = &price
		} else {
			dramas[i].Price = nil
		}
	}
	return nil
}

// IncreaseViewCount 修改剧集查看次数（num 为负数时最后的差值结果不会为负数）
func IncreaseViewCount(dramaID, num int64, tx ...*gorm.DB) error {
	if num == 0 {
		panic("num cannot be equal to 0")
	}

	var db *gorm.DB
	if len(tx) != 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = RadioDramaDramainfo{}.DB()
	}

	db = db.Table(RadioDramaDramainfo{}.TableName()).Where("id = ?", dramaID)
	// 前台和后台往剧集里面添加单音时更新剧集的更新时间（而修改剧集信息时剧集更新时间不进行修改）
	if num > 0 {
		db = db.Update(map[string]interface{}{
			"view_count": gorm.Expr("view_count + ?", num),
		})
	} else {
		db = db.Update(map[string]interface{}{
			"view_count": servicedb.SubSatExpr("view_count", int(-num)),
		})
	}
	if err := db.Error; err != nil {
		return err
	}
	ra := db.RowsAffected
	if ra == 0 {
		logger.WithField("drama_id", dramaID).Error("剧集查看次数更新失败")
		// PASS
	}

	return nil
}

func typeName(dramaCatalog int64, dramaType int32, isGeneral bool) string {
	// 若为获取泛称则返回相应的泛称
	// 目前仅中文广播剧或中文有声漫画有泛称
	if isGeneral {
		switch dramaCatalog {
		case CatalogIDCnRadioDrama:
			return "广播剧"
		case CatalogIDCnCartoon:
			return "有声漫画"
		}
	}

	// 88：播客
	if dramaCatalog == CatalogIDRadio {
		return "播客"
	}

	// 94：音乐；26：娱乐；87：催眠；114：声音恋人
	var czj = []int64{CatalogIDMusic, CatalogIDEntertainment, CatalogIDAsmr, CatalogIDSoundLovers}
	if util.HasElem(czj, dramaCatalog) {
		return "专辑"
	}

	// 96：中文有声漫画；89：中文广播剧；97：日文有声漫画；93：网络小说；91：轻小说
	var ca = []int64{
		CatalogIDCnCartoon,
		CatalogIDCnRadioDrama,
		CatalogIDJapanAudioComics,
		CatalogIDAudioBookNetwork,
		CatalogIDAudioBookLightNovel,
	}

	if util.HasElem(ca, dramaCatalog) {
		dt := dramaTypeName(dramaType)
		return dt
	}

	dt := japanDramaTypeName(dramaType)
	return dt
}

// IsUserPurchased 用户是否已购买过该剧集
func (d *RadioDramaDramainfo) IsUserPurchased(userID int64) (bool, error) {
	tran := new(transaction.TransactionLog)
	err := tran.DB().
		Where("from_id = ? AND gift_id = ? AND type = ? AND status = ?",
			userID, d.ID, transaction.TypeDrama, transaction.StatusSuccess).
		Take(tran).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// CheckRefinedType 获取剧集属性
func CheckRefinedType(cType []string) (map[int]string, error) {
	dramaType := make(map[int]string, len(cType))
	for index := range cType {
		switch cType[index] {
		case DramaTypeRisking:
			dramaType[RefinedRisking] = DramaTypeRisking
		case DramaTypeJapanForbidden:
			dramaType[RefinedJapanForbidden] = DramaTypeJapanForbidden
		case DramaTypeInteractive:
			dramaType[RefinedInteractive] = DramaTypeInteractive
		case DramaTypeLossless:
			dramaType[RefinedLossless] = DramaTypeLossless
		case DramaTypeSpecial:
			dramaType[RefinedSpecial] = DramaTypeSpecial
		case DramaTypeSensitive:
			dramaType[RefinedSensitive] = DramaTypeSensitive
		case DramaTypeNoJapanSale:
			dramaType[RefinedNoJapanSale] = DramaTypeNoJapanSale
		case DramaTypeNoLiveRecommend:
			dramaType[RefinedNoLiveRecommend] = DramaTypeNoLiveRecommend
		case DramaTypeLimitAddDanmaku:
			dramaType[RefinedLimitAddDanmaku] = DramaTypeLimitAddDanmaku
		case DramaTypeSearchHidden:
			dramaType[RefinedSearchHidden] = DramaTypeSearchHidden
		default:
			return dramaType, fmt.Errorf("错误的剧集属性：%v", cType[index])
		}
	}
	return dramaType, nil
}

// CheckInfo 剧集属性详情
type CheckInfo map[int64]checkDetails

type checkDetails struct {
	CheckDetails map[string]bool `json:"check_details"`
}

// CheckRefined 判断剧集是否为某种属性
func CheckRefined(dramaIDs []int64, dramaTypes map[int]string) (CheckInfo, error) {
	// 获取剧集属性
	var dramas []RadioDramaDramainfo
	if err := service.DramaDB.Model(RadioDramaDramainfo{}).Select("id, refined").
		Where("id IN (?)", dramaIDs).Find(&dramas).Error; err != nil {
		return nil, err
	}

	// 判断剧集属性
	info := make(CheckInfo, len(dramaIDs))
	dramaTypeLens := len(dramaTypes)
	for i := range dramas {
		details := make(map[string]bool, dramaTypeLens)
		for bitMask, dr := range dramaTypes {
			details[dr] = dramas[i].Refined.IsSet(bitMask)
		}
		info[dramas[i].ID] = checkDetails{CheckDetails: details}
	}
	return info, nil
}

// CheckRefinedMap 获取剧集属性信息
func CheckRefinedMap(dramaID int64, cType []string) (map[string]bool, error) {
	dramaTypes, err := CheckRefinedType(cType)
	if err != nil {
		return nil, err
	}
	checkInfo, err := CheckRefined([]int64{dramaID}, dramaTypes)
	if err != nil {
		return nil, err
	}
	info := make(map[string]bool, len(cType))
	if details, ok := checkInfo[dramaID]; ok {
		info = details.CheckDetails
	}
	return info, nil
}

// CheckNeedPay 给剧集添加用户付费情况
func CheckNeedPay(dramas []RadioDramaDramainfo, userID int64) error {
	dramasLen := len(dramas)
	if dramasLen == 0 {
		return nil
	}
	if userID == 0 {
		for index, drama := range dramas {
			if drama.PayType == PayTypeFree {
				dramas[index].NeedPay = util.NewInt(NeedPayFree)
			} else {
				dramas[index].NeedPay = util.NewInt(NeedPayUnpaid)
			}
		}
		return nil
	}
	// 需要检查付费状态的剧集 ID
	checkPayDramaIDs := make([]int64, 0, dramasLen)
	for index, drama := range dramas {
		if drama.PayType == PayTypeFree {
			dramas[index].NeedPay = util.NewInt(NeedPayFree)
		} else {
			checkPayDramaIDs = append(checkPayDramaIDs, drama.ID)
		}
	}
	// 获取已付费剧集 ID
	if len(checkPayDramaIDs) == 0 {
		return nil
	}
	paidDramaIDs := make([]int64, 0, len(checkPayDramaIDs))
	dramPaidTypes := []int{transaction.TypeSound, transaction.TypeDrama}
	err := transaction.TransactionLog{}.DB().Select("gift_id").
		Where("from_id = ? AND status = ? AND gift_id IN (?) AND type IN (?)", userID, transaction.StatusSuccess,
			checkPayDramaIDs, dramPaidTypes).
		Pluck("gift_id", &paidDramaIDs).Error
	if err != nil {
		return err
	}
	paidDramaIDsMap := make(map[int64]bool, len(paidDramaIDs))
	for _, id := range paidDramaIDs {
		paidDramaIDsMap[id] = true
	}
	for index, drama := range dramas {
		if drama.PayType != PayTypeFree {
			if _, ok := paidDramaIDsMap[drama.ID]; ok {
				// 剧集需要付费，用户已付费
				dramas[index].NeedPay = util.NewInt(NeedPayPaid)
			} else {
				dramas[index].NeedPay = util.NewInt(NeedPayUnpaid)
			}
		}
	}
	return nil
}

// FindActiveDramaIDs 筛选过审未报警剧集 IDs
func FindActiveDramaIDs(dramaIDs []int64) ([]int64, error) {
	if len(dramaIDs) == 0 {
		return []int64{}, nil
	}

	var activeDramaIDs []int64
	err := RadioDramaDramainfo{}.DB().
		Where("id IN (?)", dramaIDs).
		Where("checked = ? AND police = 0", CheckedPass).
		Pluck("id", &activeDramaIDs).Error
	if err != nil {
		return nil, err
	}
	return activeDramaIDs, nil
}

// FindDramaPayInfo 获取剧集付费信息
func FindDramaPayInfo(dramaIDs []int64, userID int64) ([]RadioDramaDramainfo, error) {
	var dramas []RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, pay_type, integrity, refined, vip").
		Where("id IN (?) AND checked IN (?)", dramaIDs, []int8{CheckedPass, CheckedContractExpired}).
		Find(&dramas).Error
	if err != nil {
		return nil, err
	}
	// 剧集添加 need_pay 信息
	err = CheckNeedPay(dramas, userID)
	if err != nil {
		return nil, err
	}
	return dramas, nil
}

// ListDramaByUserID 根据用户 ID 获取过审剧集信息（按创建时间倒序）
func ListDramaByUserID(userID int64, limit int) ([]RadioDramaDramainfo, error) {
	var dramas []RadioDramaDramainfo
	err := RadioDramaDramainfo{}.DB().
		Select("id, name, cover, cover_color, username, view_count, pay_type").
		Where("user_id = ? AND checked = ?", userID, CheckedPass).
		Order("create_time DESC").
		Limit(limit).
		Find(&dramas).Error
	if err != nil {
		return nil, err
	}
	return dramas, nil
}

// CountUserDrama 根据用户 ID 以及参演 cvIDs 获取用户创建和参演中过审并且未报警剧集的数量
func CountUserDrama(userID int64, cvIDs []int64) (count int64, err error) {
	err = service.DramaDB.Table(RadioDramaDramainfo{}.TableName()+" AS a").
		Joins(fmt.Sprintf("LEFT JOIN %s AS b ON a.id = b.drama_id",
			dramaepisodecv.RadioDramaEpisodeCv{}.TableName())).
		Where("a.user_id = ? OR b.cv_id IN (?)", userID, cvIDs).
		Where("a.checked = ? AND a.police = 0", CheckedPass).
		Group("a.id").Count(&count).Error
	return
}

// GetDramaViewCount 根据剧集 ID 获取播放量
func GetDramaViewCount(dramaID int64) (int64, error) {
	var viewCount int64
	err := RadioDramaDramainfo{}.DB().Select("view_count").Where("id = ?", dramaID).Row().Scan(&viewCount)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	return viewCount, nil
}

// ListDramaInfoByIDs 根据剧集 IDs 分批获取过审剧集信息
func ListDramaInfoByIDs(dramaIDs []int64) ([]RadioDramaDramainfo, error) {
	dramaIDCount := len(dramaIDs)
	allDramaInfos := make([]RadioDramaDramainfo, 0, dramaIDCount)
	for offset := 0; offset < dramaIDCount; offset += FindDramaMaxBatchSize {
		// 分批获取过审剧集信息
		leftBorder, rightBorder := offset, offset+FindDramaMaxBatchSize
		var batchDramaID []int64
		if rightBorder >= dramaIDCount {
			batchDramaID = dramaIDs[leftBorder:]
		} else {
			batchDramaID = dramaIDs[leftBorder:rightBorder]
		}

		var dramaInfos []RadioDramaDramainfo
		err := RadioDramaDramainfo{}.DB().
			Select("id, name, cover, cover_color, view_count, pay_type, catalog, type, integrity, refined, vip").
			Where("id IN (?) AND checked = ? AND police = 0", batchDramaID, CheckedPass).
			Find(&dramaInfos).Error
		if err != nil {
			return nil, err
		}

		allDramaInfos = append(allDramaInfos, dramaInfos...)
	}

	return allDramaInfos, nil
}

// FindDramaCatalogID 获取剧集分区 ID
func FindDramaCatalogID(dramaID int64) (int64, error) {
	if dramaID <= 0 {
		return 0, nil
	}
	var catalogID int64
	err := RadioDramaDramainfo{}.DB().Select("catalog").Where("id = ?", dramaID).Row().
		Scan(&catalogID)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	return catalogID, nil
}
