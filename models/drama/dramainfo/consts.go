package dramainfo

// 剧集审核状态
const (
	CheckedPending         int8 = iota // 未审核
	CheckedPass                        // 审核通过
	CheckedDiscontinued                // 审核未通过（临时下架）
	CheckedPolice                      // 报警（待整合）
	CheckedContractExpired             // 合约到期下架
)

// 完结度
const (
	IntegrityNameSerializing int8 = iota + 1 // 长篇未完结
	IntegrityNameEnd                         // 长篇完结
	IntegrityNameOne                         // 全一期
	IntegrityNameMini                        // 微小剧
)

// 剧集分区 ID
const (
	CatalogIDEntertainment       int64 = 26  // 娱乐
	CatalogIDAudioBook           int64 = 86  // 听书
	CatalogIDAsmr                int64 = 87  // 催眠
	CatalogIDRadio               int64 = 88  // 播客
	CatalogIDCnRadioDrama        int64 = 89  // 中文广播剧
	CatalogIDJapanRadioDrama     int64 = 90  // 日文广播剧
	CatalogIDAudioBookLightNovel int64 = 91  // 轻小说
	CatalogIDAudioBookNetwork    int64 = 93  // 网络小说
	CatalogIDMusic               int64 = 94  // 音乐
	CatalogIDCartoon             int64 = 95  // 有声漫画
	CatalogIDCnCartoon           int64 = 96  // 中文有声漫画
	CatalogIDJapanAudioComics    int64 = 97  // 日文有声漫画
	CatalogIDAudioBookChildren   int64 = 98  // 儿童
	CatalogIDSoundLovers         int64 = 114 // 声音恋人
)

// 剧集类型
const (
	TypeGeneralAudience int32 = iota + 3 // 全年龄
	TypeBoysLove                         // 纯爱
	TypeLesbian                          // 双女主
	TypeRomantic                         // 言情
	TypeSerializing                      // 未完结
	TypeEnd                              // 完结
	TypeOneAndMini                       // 全一期
)
