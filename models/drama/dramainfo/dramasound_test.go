package dramainfo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/service"
)

func TestGetDramaIDsBySoundIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	type SimpleDrama struct {
		ID      int64 `gorm:"column:id"`
		Checked int   `gorm:"column:checked"`
	}
	var dramas []SimpleDrama
	err := service.DramaDB.Table(tableName).
		Select("id, checked").
		Where("id IN (?)", []int64{12, 13}).
		Order("id ASC").
		Scan(&dramas).Error
	require.NoError(err)
	require.Len(dramas, 2)

	var samples []SoundIDDramaID
	err = service.DramaDB.Table(dramaepisode.RadioDramaEpisode{}.TableName()).
		Select("drama_id, sound_id").
		Where("drama_id IN (?)", []int64{dramas[0].ID, dramas[1].ID}).
		Scan(&samples).Error
	require.NoError(err)
	require.Len(samples, 2)

	// 第一个音频对应的剧集未过审，第二个过审
	result, err := GetDramaIDsBySoundIDs([]int64{samples[0].SoundID, samples[1].SoundID})
	require.NoError(err)
	require.Len(result, 1)
	assert.Equal(result[0].DramaID, samples[1].DramaID)

	// 音频不存在
	result, err = GetDramaIDsBySoundIDs([]int64{-9999})
	require.NoError(err)
	assert.NotNil(result)
	assert.Empty(result)
}
