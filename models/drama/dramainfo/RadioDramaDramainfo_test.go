package dramainfo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisodecv"
	"github.com/MiaoSiLa/missevan-go/models/transaction"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaDramainfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaDramainfo{},
		"id", "name", "cover", "cover_color", "abstract", "integrity", "age", "origin", "author",
		"birthday", "cv", "ip", "ipname", "type", "newest", "organization_id", "user_id", "username",
		"checked", "create_time", "lastupdate_time", "view_count", "catalog", "alias",
		"pay_type", "push", "refined", "police", "ip_id", "subscription_num", "vip")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaDramainfo{},
		"id", "name", "cover", "cover_color", "abstract", "integrity", "age", "origin", "author",
		"birthday", "cv", "ip", "ipname", "type", "newest", "organization_id", "user_id", "username",
		"checked", "create_time", "lastupdate_time", "view_count", "catalog", "catalog_name", "alias",
		"pay_type", "push", "refined", "police", "ip_id", "subscription_num", "integrity_name",
		"price", "tags", "type_name", "purchased", "need_pay", "vip")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_dramainfo", tableName)
	assert.Equal("radio_drama_dramainfo", RadioDramaDramainfo{}.TableName())
}

func TestRadioDramaDramainfoAfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var d RadioDramaDramainfo
	err := service.DramaDB.Take(&d).Error
	require.NoError(err)

	assert.NotEmpty(d.CoverURL)
}

func TestJoinRadioDramaDramainfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	price := int64(-1)
	dramas := []RadioDramaDramainfo{{ID: -4321, Price: &price}}
	err := JoinRadioDramaDramainfo(dramas)
	require.NoError(err)
	assert.Nil(dramas[0].Price)

	err = JoinRadioDramaDramainfo(nil)
	require.NoError(err)
}

func TestIncreaseViewCount(t *testing.T) {
	assert := assert.New(t)

	dri := new(RadioDramaDramainfo)
	dramaID := int64(1)
	assert.NoError(dri.DB().First(dri, dramaID).Error)
	num := dri.ViewCount

	plus := int64(7)
	err := IncreaseViewCount(dri.ID, plus)
	assert.NoError(err)
	assert.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(num+plus, dri.ViewCount)

	minus := -dri.ViewCount + 1
	err = IncreaseViewCount(dri.ID, minus)
	assert.NoError(err)
	assert.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(1), dri.ViewCount)

	minus = -dri.ViewCount - 1
	err = IncreaseViewCount(dri.ID, minus)
	assert.NoError(err)
	assert.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(0), dri.ViewCount)

	minus = -dri.ViewCount - 1
	err = IncreaseViewCount(dri.ID, minus)
	assert.NoError(err)
	assert.NoError(dri.DB().First(dri, dramaID).Error)
	assert.Equal(int64(0), dri.ViewCount)

	// 测试 panic
	assert.PanicsWithValue("num cannot be equal to 0", func() {
		err = IncreaseViewCount(dri.ID, 0)
	})
}

func TestIsUserPurchased(t *testing.T) {
	assert := assert.New(t)
	rdd := new(RadioDramaDramainfo)

	// 测试剧集 ID 为 0
	pu, err := rdd.IsUserPurchased(12)
	assert.NoError(err)
	assert.False(pu)

	// 测试用户购买过剧集
	assert.NoError(rdd.DB().First(rdd, 2).Error)
	pu, err = rdd.IsUserPurchased(12)
	assert.NoError(err)
	assert.True(pu)
}

func TestCheckRefinedType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 错误参数
	_, err := CheckRefinedType([]string{"test"})
	assert.EqualError(err, "错误的剧集属性：test")

	// 正常参数
	res, err := CheckRefinedType([]string{DramaTypeRisking, DramaTypeJapanForbidden, DramaTypeInteractive})
	require.NoError(err)
	assert.Len(res, 3)
}

func TestCheckRefined(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaIds := []int64{1, 4, 5, 6}
	dramaType := map[int]string{
		1: "risking",
		2: "japan_forbidden",
		3: "interactive",
		4: "lossless",
	}
	res, err := CheckRefined(dramaIds, dramaType)
	require.NoError(err)
	assert.True(res[1].CheckDetails[DramaTypeRisking])
	assert.True(res[4].CheckDetails[DramaTypeJapanForbidden])
	assert.True(res[5].CheckDetails[DramaTypeInteractive])
	assert.True(res[6].CheckDetails[DramaTypeLossless])
}

func TestCheckRefinedMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info, err := CheckRefinedMap(8, []string{DramaTypeSpecial, DramaTypeSensitive})
	require.NoError(err)
	assert.True(info[DramaTypeSensitive])
}

func TestCheckNeedPay(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testDramaID := int64(1233)
	testUserID := int64(45795)
	require.NoError(transaction.TransactionLog{}.DB().Where("gift_id = ? AND from_id = ?", testDramaID, testUserID).Delete("").Error)

	// 测试剧集免费的情况
	drama := RadioDramaDramainfo{
		ID:      testDramaID,
		PayType: PayTypeFree,
	}
	dramas := []RadioDramaDramainfo{drama}
	require.NoError(CheckNeedPay(dramas, testUserID))
	assert.Equal(NeedPayFree, *dramas[0].NeedPay)
	require.NoError(CheckNeedPay(dramas, 0))
	assert.Equal(NeedPayFree, *dramas[0].NeedPay)

	require.NoError(transaction.TransactionLog{}.DB().Delete(transaction.TransactionLog{}, "gift_id = ? AND from_id = ?", testDramaID, testUserID).Error)
	// 测试剧集付费用户未付费的情况
	drama.PayType = PayTypeDrama
	dramas = []RadioDramaDramainfo{drama}
	require.NoError(CheckNeedPay(dramas, testUserID))
	assert.Equal(NeedPayUnpaid, *dramas[0].NeedPay)
	require.NoError(CheckNeedPay(dramas, 0))
	assert.Equal(NeedPayUnpaid, *dramas[0].NeedPay)

	// 测试剧集付费用户已付费的情况
	// 新增付费记录
	transactionLog := transaction.TransactionLog{
		GiftID: testDramaID,
		FromID: testUserID,
		Type:   transaction.TypeDrama,
		Status: transaction.StatusSuccess,
	}
	require.NoError(transactionLog.DB().Save(&transactionLog).Error)
	require.NoError(CheckNeedPay(dramas, testUserID))
	assert.Equal(NeedPayPaid, *dramas[0].NeedPay)
}

func TestFindActiveDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	activeDramaIDs, err := FindActiveDramaIDs([]int64{1, 2, 3})
	require.NoError(err)
	assert.EqualValues([]int64{1, 2}, activeDramaIDs)
}

func TestFindDramaPayInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := FindDramaPayInfo([]int64{1}, int64(346286))
	require.NoError(err)
	require.Len(result, 1)
	assert.Equal(PayTypeEpisode, result[0].PayType)
	require.NotNil(result[0].NeedPay)
	assert.Equal(NeedPayUnpaid, *result[0].NeedPay)
	require.NotNil(result[0].Integrity)
	assert.Equal(IntegrityNameSerializing, *result[0].Integrity)
	assert.Equal(util.BitMask(RefinedRisking), result[0].Refined)
}

func TestListDramaByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := ListDramaByUserID(int64(999999), 1)
	require.NoError(err)
	assert.Empty(result)

	result, err = ListDramaByUserID(int64(346286), 1)
	require.NoError(err)
	assert.Len(result, 1)
	assert.EqualValues(1, result[0].ID)
}

func TestCountUserDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1)
	cvID := int64(2)
	err := service.DramaDB.Table(tableName).Where("user_id = ?", userID).Delete("").Error
	require.NoError(err)
	err = service.DramaDB.Table(dramaepisodecv.RadioDramaEpisodeCv{}.TableName()).Where("cv_id = ?", cvID).Delete("").Error
	require.NoError(err)

	// userID 创建的剧集测试数据
	integrity := IntegrityNameEnd
	age := int8(1)
	origin := int8(0)
	dramaInfo := RadioDramaDramainfo{
		Integrity: &integrity,
		Age:       &age,
		Origin:    &origin,
		Type:      TypeEnd,
		UserID:    userID,
		UserName:  "test",
		Checked:   CheckedPass,
	}
	err = service.DramaDB.Table(tableName).Create(&dramaInfo).Error
	require.NoError(err)
	// cvID 参演的剧集测试数据
	dramaCV := dramaepisodecv.RadioDramaEpisodeCv{
		EpisodeID: 1,
		CvID:      cvID,
		Character: "test",
		Main:      1,
		DramaID:   5,
	}
	err = service.DramaDB.Table(dramaepisodecv.RadioDramaEpisodeCv{}.TableName()).Create(&dramaCV).Error
	require.NoError(err)

	result, err := CountUserDrama(userID, []int64{cvID})
	require.NoError(err)
	// 1 个创建 1 个参演
	assert.EqualValues(2, result)
}

func TestGetDramaViewCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试获取不存在剧集的播放量
	res, err := GetDramaViewCount(2333)
	require.NoError(err)
	assert.Zero(res)

	// 测试获取剧集播放量
	dramaID := int64(1)
	err = service.DramaDB.Table(tableName).Where("id = ?", dramaID).
		Update("view_count", 4).Error
	require.NoError(err)
	res, err = GetDramaViewCount(dramaID)
	require.NoError(err)
	assert.EqualValues(4, res)
}

func TestListDramaInfoByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集 IDs 长度为 0
	var ids []int64
	dramaInfos, err := ListDramaInfoByIDs(ids)
	require.NoError(err)
	assert.Empty(dramaInfos)

	// 测试剧集 IDs 长度不为 0 但剧集不存在时
	ids = []int64{1000000000}
	dramaInfos, err = ListDramaInfoByIDs(ids)
	require.NoError(err)
	assert.Empty(dramaInfos)

	// 测试剧集 IDs 长度不为 0 且剧集存在时
	ids = []int64{52348, 52349}
	dramaInfos, err = ListDramaInfoByIDs(ids)
	require.NoError(err)
	assert.NotEmpty(dramaInfos)
	assert.Len(dramaInfos, 2)
}

func TestCatalogName(t *testing.T) {
	require := require.New(t)

	require.Equal("中文广播剧", catalogName(CatalogIDCnRadioDrama))
	require.Equal("中文有声漫画", catalogName(CatalogIDCnCartoon))
	require.Equal("日文广播剧", catalogName(CatalogIDJapanRadioDrama))
	require.Equal("日文有声漫画", catalogName(CatalogIDJapanAudioComics))
	require.Equal("播客", catalogName(CatalogIDRadio))
	require.Equal("轻小说", catalogName(CatalogIDAudioBookLightNovel))
	require.Equal("网络小说", catalogName(CatalogIDAudioBookNetwork))
	require.Equal("音乐", catalogName(CatalogIDMusic))
	require.Equal("催眠", catalogName(CatalogIDAsmr))
	require.Equal("儿童", catalogName(CatalogIDAudioBookChildren))
	require.Equal("声音恋人", catalogName(CatalogIDSoundLovers))
	require.Empty(catalogName(-1))
}

func TestDramaTypeName(t *testing.T) {
	require := require.New(t)

	require.Equal("全年龄", dramaTypeName(TypeGeneralAudience))
	require.Equal("纯爱", dramaTypeName(TypeBoysLove))
	require.Equal("双女主", dramaTypeName(TypeLesbian))
	require.Equal("言情", dramaTypeName(TypeRomantic))
	require.Equal("未完结", dramaTypeName(TypeSerializing))
	require.Equal("完结", dramaTypeName(TypeEnd))
	require.Equal("全一期", dramaTypeName(TypeOneAndMini))
	require.Equal("未知", dramaTypeName(-1))
}

func TestFindDramaCatalogID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧集 ID 为 0
	data, err := FindDramaCatalogID(0)
	require.NoError(err)
	assert.EqualValues(0, data)

	// 测试剧集不存在
	data, err = FindDramaCatalogID(999999)
	require.NoError(err)
	assert.EqualValues(0, data)

	// 测试正常返回
	data, err = FindDramaCatalogID(1)
	require.NoError(err)
	assert.EqualValues(89, data)
}
