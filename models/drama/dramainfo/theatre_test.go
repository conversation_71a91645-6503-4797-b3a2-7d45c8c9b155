package dramainfo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/drama/dramaepisode"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/keys"
)

func TestIsTheatreDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaID := int64(999)
	key := keys.KeyTheatreDramaIDs0.Format()
	require.NoError(service.Redis.SRem(key, dramaID).Err())
	assert.False(IsTheatreDrama(dramaID))
	require.NoError(service.Redis.SAdd(key, dramaID).Err())
	assert.True(IsTheatreDrama(dramaID))
}

func TestIsTheatreDramaSound(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var rde dramaepisode.RadioDramaEpisode
	require.NoError(service.DramaDB.Take(&rde).Error)
	key := keys.LocalKeyTheatreSoundIDs0.Format()
	service.Cache5Min.SetDefault(key, map[int64]struct{}{})
	assert.False(IsTheatreDramaSound(rde.SoundID))

	dramaKey := keys.KeyTheatreDramaIDs0.Format()
	require.NoError(service.Redis.SAdd(dramaKey, rde.DramaID).Err())
	service.Cache5Min.Flush()
	assert.True(IsTheatreDramaSound(rde.SoundID))
}

func TestIsTheatreNewDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaID := int64(1)
	key := keys.KeyTheatreNewDramaIDs0.Format()
	require.NoError(service.Redis.SRem(key, dramaID).Err())
	assert.False(IsTheatreNewDrama(dramaID))
	require.NoError(service.Redis.SAdd(key, dramaID).Err())
	assert.True(IsTheatreNewDrama(dramaID))
}
