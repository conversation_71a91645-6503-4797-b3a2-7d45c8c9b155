package mdramalist

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// MDramalist 剧单表
type MDramalist struct {
	ID           int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64  `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"`  // 修改时间。单位：秒
	DeleteTime   int64  `gorm:"column:delete_time"`    // 删除时间，不为 0 时表示已删除。单位：秒
	Title        string `gorm:"column:title"`          // 剧单标题
	Intro        string `gorm:"column:intro"`          // 剧单简介
	UserID       int64  `gorm:"column:user_id"`        // 剧单所属用户 ID
	CollectCount int64  `gorm:"column:collect_count"`  // 剧单被收藏数量
}

// DB the db instance of MDramalist model
func (m MDramalist) DB() *gorm.DB {
	return service.MainDB.Table(m.TableName())
}

// TableName for current model
func (MDramalist) TableName() string {
	return "m_dramalist"
}

// FindOne 根据 ID 查询剧单
func FindOne(dramalistID int64) (*MDramalist, error) {
	mDramalist := new(MDramalist)
	err := MDramalist{}.DB().
		Where("id = ? AND delete_time = 0", dramalistID).
		Take(mDramalist).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return mDramalist, nil
}

// DramalistExists 查询剧单是否存在
func DramalistExists(dramalistID int64) (bool, error) {
	return servicedb.Exists(MDramalist{}.DB().Where("id = ? AND delete_time = 0", dramalistID))
}
