package mdramalist

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(MDramalist{}, "id", "create_time", "modified_time", "delete_time",
		"title", "intro", "user_id", "collect_count")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_dramalist", MDramalist{}.TableName())
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧单存在
	mDramalist, err := FindOne(1)
	require.NoError(err)
	assert.NotNil(mDramalist)

	// 测试剧单不存在
	mDramalist, err = FindOne(10000)
	require.NoError(err)
	assert.Nil(mDramalist)
}

func TestDramalistExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试剧单存在
	exists, err := DramalistExists(1)
	require.NoError(err)
	assert.True(exists)

	// 测试剧单不存在
	exists, err = DramalistExists(10000)
	require.NoError(err)
	assert.False(exists)
}
