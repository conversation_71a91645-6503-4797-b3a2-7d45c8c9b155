package dramacornermark

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

// 角标类型
const (
	TypeOther    = iota // 其他，详见 more 字段
	TypeOriginal        // 原创
	TypeSelected        // 精选
)

// RadioDramaCornerMark 角标信息表
type RadioDramaCornerMark struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间
	DeleteTime   int64  `gorm:"column:delete_time"`   // 删除时间
	DramaID      int64  `gorm:"column:drama_id"`      // 剧集 ID
	Type         int    `gorm:"column:type"`          // 角标类型 0: 其他，详见 more 字段; 1: 原创; 2: 精选
	More         string `gorm:"column:more"`          // 特殊角标配置信息
}

// DB the db instance of RadioDramaCornerMark model
func (rdc RadioDramaCornerMark) DB() *gorm.DB {
	return service.DramaDB.Table(rdc.TableName())
}

// TableName for RadioDramaCornerMark model
func (RadioDramaCornerMark) TableName() string {
	return "radio_drama_corner_mark"
}

// FindCornerMarkByDramaIDs 根据剧集 IDs 获取剧集角标类型信息
func FindCornerMarkByDramaIDs(dramaIDs []int64) ([]RadioDramaCornerMark, error) {
	var cornerMarks []RadioDramaCornerMark
	err := RadioDramaCornerMark{}.DB().Select("drama_id, type").
		Where("drama_id IN (?) AND delete_time = ?", dramaIDs, 0).Find(&cornerMarks).Error
	if err != nil {
		return nil, err
	}
	return cornerMarks, nil
}
