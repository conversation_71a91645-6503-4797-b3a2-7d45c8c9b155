package dramacornermark

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestRadioDramaCornerMark(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RadioDramaCornerMark{},
		"id", "create_time", "modified_time", "delete_time", "drama_id", "type", "more")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_corner_mark", RadioDramaCornerMark{}.TableName())
}

func TestFindCornerMarkByDramaIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	result, err := FindCornerMarkByDramaIDs([]int64{1})
	require.NoError(err)
	assert.Len(result, 1)
}
