package dramatag

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestRadioDramaTagTags(t *testing.T) {
	kc := tutil.New<PERSON>eyChecker(t, tutil.GORM)
	kc.Check(RadioDramaTag{},
		"id", "name", "drama_num", "visible_on_create", "manga_num")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RadioDramaTag{},
		"id", "name", "drama_num", "visible_on_create", "manga_num")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("radio_drama_tag", tableName)
	assert.Equal("radio_drama_tag", RadioDramaTag{}.TableName())
}
