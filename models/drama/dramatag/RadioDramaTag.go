package dramatag

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
)

const tableName = "radio_drama_tag"

// RadioDramaTag 剧集标签表
type RadioDramaTag struct {
	ID              int64  `gorm:"column:id" json:"id"`
	Name            string `gorm:"column:name" json:"name"`
	DramaNum        int    `gorm:"column:drama_num" json:"drama_num"`
	VisibleOnCreate int8   `gorm:"column:visible_on_create" json:"visible_on_create"`
	MangaNum        int    `gorm:"column:manga_num" json:"manga_num"`
}

// DB the db instance of RadioDramaTag model
func (rdt RadioDramaTag) DB() *gorm.DB {
	return service.DramaDB.Table(rdt.TableName())
}

// TableName for RadioDramaTag model
func (RadioDramaTag) TableName() string {
	return tableName
}
