package game

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(MGameCenter{}, "id", "url", "cover", "icon", "name", "tag", "intro", "extended_fields",
		"sort", "create_time", "modified_time", "publish_time", "cooperative_mode", "package_version", "shutdown_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(ExtendedFields{}, "download_open_time", "download_url", "package_name", "package_version_code", "card")
	kc.Check(Card{}, "cover", "dark_cover", "btn_color", "dark_btn_color")
}

func TestMGameCenter_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_game_center", MGameCenter{}.TableName())
}

func TestMGameCenter_GetCardStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1679500800, 0) // 2023-03-24 00:00:00
	})
	defer util.SetTimeNow(nil)

	gameCenter := MGameCenter{
		ID: 1,
		ExtendedFieldsInfo: &ExtendedFields{
			DownloadOpenTime: 1679500800,
		},
	}
	var subscribedUserID int64 = 12
	status, err := gameCenter.GetCardStatus(subscribedUserID)
	require.NoError(err)
	assert.Equal(StatusOpenDownload, status)

	gameCenter.ExtendedFieldsInfo.DownloadOpenTime = 1679587200 // 2023-03-24 00:00:00
	status, err = gameCenter.GetCardStatus(0)
	require.NoError(err)
	assert.Equal(StatusUnsubscribed, status)

	status, err = gameCenter.GetCardStatus(subscribedUserID)
	require.NoError(err)
	assert.Equal(StatusSubscribed, status)

	var unsubscribedUserID int64 = 9999999
	status, err = gameCenter.GetCardStatus(unsubscribedUserID)
	require.NoError(err)
	assert.Equal(StatusUnsubscribed, status)
}

func TestFindGameByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testExistsGameID int64 = 1
	gameCenter, err := FindGameByID(testExistsGameID)
	require.NoError(err)
	assert.NotNil(gameCenter)

	var testNotExistsGameID int64 = 999999
	gameCenter, err = FindGameByID(testNotExistsGameID)
	require.NoError(err)
	assert.Nil(gameCenter)
}
