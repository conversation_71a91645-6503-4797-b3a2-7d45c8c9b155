package game

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 游戏预约卡片状态
const (
	StatusUnsubscribed = iota + 1 // 未预约
	StatusSubscribed              // 已预约
	StatusOpenDownload            // 开放下载
)

// MGameCenter 游戏中心
type MGameCenter struct {
	ID              int64  `gorm:"column:id;primary_key"`   // 主键
	URL             string `gorm:"column:url"`              // 跳转链接
	Cover           string `gorm:"column:cover"`            // 封面图
	Icon            string `gorm:"column:icon"`             // 图标
	Name            string `gorm:"column:name"`             // 游戏名
	Tag             string `gorm:"column:tag"`              // 标签名，用半角逗号分隔
	Intro           string `gorm:"column:intro"`            // 简介
	ExtendedFields  string `gorm:"column:extended_fields"`  // 额外数据，JSON format
	Sort            int64  `gorm:"column:sort"`             // 排序
	CreateTime      int64  `gorm:"column:create_time"`      // 创建时间（单位：秒）
	ModifiedTime    int64  `gorm:"column:modified_time"`    // 更新时间（单位：秒）
	PublishTime     int64  `gorm:"column:publish_time"`     // 上架时间（单位：秒）
	CooperativeMode int64  `gorm:"column:cooperative_mode"` // 合作模式 1：SDK；2：CPS
	PackageVersion  string `gorm:"column:package_version"`  // 游戏包版本号
	ShutdownTime    int64  `gorm:"column:shutdown_time"`    // 下架时间（单位：秒），0：未下架

	ExtendedFieldsInfo *ExtendedFields `gorm:"-"`
}

// Card struct for ExtendedFields.Card
type Card struct {
	Cover        string `json:"cover"`          // 背景图
	DarkCover    string `json:"dark_cover"`     // 黑夜模式背景图
	BtnColor     string `json:"btn_color"`      // 预约/未预约/下载按钮颜色
	DarkBtnColor string `json:"dark_btn_color"` // 黑夜模式预约/未预约/下载按钮颜色
}

// ExtendedFields struct for MGameCenter.ExtendedFieldsInfo
// TODO: add fields
type ExtendedFields struct {
	DownloadOpenTime   int64  `json:"download_open_time"`   // 开放下载时间戳（单位：秒）
	DownloadURL        string `json:"download_url"`         // Android 安装包下载地址
	PackageName        string `json:"package_name"`         // Android 安装包名
	PackageVersionCode int64  `json:"package_version_code"` // Android 安装包版本
	Card               *Card  `json:"card,omitempty"`       // 预约卡片信息
}

// TableName MGameCenter table name
func (MGameCenter) TableName() string {
	return "m_game_center"
}

// DB the db instance of MGameCenter model
func (mgc *MGameCenter) DB() *gorm.DB {
	return service.DB.Table(mgc.TableName())
}

// AfterFind is a GORM hook for query
func (mgc *MGameCenter) AfterFind() error {
	if mgc.Icon != "" {
		mgc.Icon = service.Storage.Parse(mgc.Icon)
	}
	// TODO: handle next_stage field
	if mgc.ExtendedFields != "" {
		err := json.Unmarshal([]byte(mgc.ExtendedFields), &mgc.ExtendedFieldsInfo)
		if err != nil {
			return err
		}
		// TODO: parse download url
		if mgc.ExtendedFieldsInfo.Card != nil {
			if mgc.ExtendedFieldsInfo.Card.Cover != "" {
				mgc.ExtendedFieldsInfo.Card.Cover = service.Storage.Parse(mgc.ExtendedFieldsInfo.Card.Cover)
			}
			if mgc.ExtendedFieldsInfo.Card.DarkCover != "" {
				mgc.ExtendedFieldsInfo.Card.DarkCover = service.Storage.Parse(mgc.ExtendedFieldsInfo.Card.DarkCover)
			}
		}
	}
	return nil
}

// GetCardStatus 获取游戏预约、下载状态
func (mgc *MGameCenter) GetCardStatus(userID int64) (int, error) {
	// 开放下载后，不需要判断用户是否预约
	if mgc.ExtendedFieldsInfo != nil &&
		mgc.ExtendedFieldsInfo.DownloadOpenTime > 0 &&
		util.TimeNow().Unix() >= mgc.ExtendedFieldsInfo.DownloadOpenTime {
		return StatusOpenDownload, nil
	}
	if userID <= 0 {
		return StatusUnsubscribed, nil
	}
	exists, err := MGameSubscribe{}.Exists(userID, mgc.ID)
	if err != nil {
		return 0, err
	}
	if exists {
		return StatusSubscribed, nil
	}
	return StatusUnsubscribed, nil
}

// FindGameByID 根据主键 ID 查找单个游戏配置
func FindGameByID(id int64) (*MGameCenter, error) {
	game := new(MGameCenter)
	err := game.DB().Where("id = ?", id).Take(game).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return game, nil
}
