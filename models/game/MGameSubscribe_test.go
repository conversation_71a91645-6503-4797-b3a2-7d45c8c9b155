package game

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMGameSubscribe_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("m_game_subscribe", MGameSubscribe{}.TableName())
}

func TestMGameSubscribe_Exists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := MGameSubscribe{}.Exists(12, 1)
	require.NoError(err)
	assert.True(exists)

	// 测试已取消预约
	exists, err = MGameSubscribe{}.Exists(12, 2)
	require.NoError(err)
	assert.False(exists)

	exists, err = MGameSubscribe{}.Exists(12, 9999999999)
	require.NoError(err)
	assert.False(exists)
}
