package game

import (
	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// 未软删除的 delete_time
const deleteTimeNotDeleted = iota

// MGameSubscribe model records the subscribe of user
type MGameSubscribe struct {
	ID           int64  `gorm:"column:id"`
	GameID       int64  `gorm:"column:game_id"`
	UserID       int64  `gorm:"column:user_id"`
	IP           string `gorm:"column:ip"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	DeleteTime   int64  `gorm:"column:delete_time"`
}

// TableName of MGameSubscribe model
func (MGameSubscribe) TableName() string {
	return "m_game_subscribe"
}

// Exists 判断用户是否预约游戏
func (mgs MGameSubscribe) Exists(userID, gameID int64) (bool, error) {
	query := service.DB.Table(mgs.TableName()).
		Where("game_id = ? AND user_id = ? AND delete_time = ?", gameID, userID, deleteTimeNotDeleted)
	exists, err := servicedb.Exists(query)
	if err != nil {
		return false, err
	}
	return exists, err
}
