package search

import (
	"errors"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 事件
const (
	EventTypeClickSearch = iota
	EventTypeClickSuggest
	EventTypeClickItem
	EventTypeClickTab
	EventTypeClickLimit
)

// 来源页
const (
	OriginHomepage      = iota //nolint:deadcode,varcheck
	OriginDiscoveryPage        //nolint:deadcode,varcheck
	OriginSearchPage
)

// 被点击项类型
const (
	itemTypeSound       = iota // 音频
	itemTypeUp                 // UP 主
	itemTypeAlbum              // 音单
	itemTypeGeneral            // 综合
	itemTypeCV                 // 声优
	itemTypeDrama              // 剧集
	itemTypeLive               // 直播
	itemTypeSpecialItem        // 特型
	itemTypeChannel            // 频道
	itemTypeLimit
)

const (
	itemTypeTopicCardMin = 1000
	itemTypeTopicCardMax = 1099
)

const (
	// ItemTypeTopicCardDrama 专题卡子项：剧集
	ItemTypeTopicCardDrama = 1000
	// ItemTypeTopicCardMore 专题卡子项：更多
	ItemTypeTopicCardMore = 1001
)

// 搜索结果来源
const (
	itemOriginDirect          = iota // 直接点击搜索按钮
	itemOriginSuggest                // 点击联想词
	itemOriginHotsearch              // 点击热搜词
	itemOriginPeopleListening        // 点击“大家都在听”
	itemOriginSearchHistory          // 搜索历史
	itemOriginDefaultSearch          // 默认搜索
	itemOriginDefaultLimit
)

// 分区名称
// 枚举值由 missevan-app discovery/search-config 接口下发
const (
	searchCatalogIDAll         = iota // 全部分区
	searchCatalogIDAudioComics        // 有声漫画
	searchCatalogIDDrama              // 广播剧
	searchCatalogIDMusic              // 音乐
	searchCatalogIDVoiceLover         // 声音恋人
	searchCatalogIDPodCast            // 播客
	searchCatalogIDJapanDrama         // 日抓
	searchCatalogIDAudioBook          // 听书
	searchCatalogIDRing               // 铃声
	searchCatalogIDASMR               // 放松
	searchCatalogIDLimit
)

// 搜索类型
const (
	searchTypeUP      = iota + 1 // UP 主
	searchTypeAlbum              // 音单
	searchTypeGeneral            // 综合
	searchTypeCV                 // 声优
	searchTypeDrama              // 剧集
	searchTypeLive               // 直播
	_
	searchTypeChannel // 频道
)

var (
	errUserID         = errors.New("could not get user_id")
	errEventType      = errors.New("unsupported event_type")
	errOrigin         = errors.New("unsupported origin")
	errEmptyInput     = errors.New("empty input")
	errEmptyQuery     = errors.New("empty query")
	errSearchType     = errors.New("unsupported search_type")
	errItemType       = errors.New("unsupported item_type")
	errItemOrigin     = errors.New("unsupported item_origin")
	errItemCatalogID  = errors.New("unsupported item_catalog_id")
	errNegativeNumber = errors.New("negative number")
)

// Click 搜索点击
type Click struct {
	EventType     *int   `json:"event_type"`               // 事件类型（0：搜索概况，1：点击联想词，2：点击结果项，3：点击 tab 页）
	HintCount     int    `json:"hint_count"`               // 联想词数量
	Input         string `json:"input"`                    // 输入框内容
	Query         string `json:"query"`                    // 实际搜索词
	IPV           int    `json:"ipv"`                      // 本次搜索点击的次数
	ItemID        int64  `json:"item_id"`                  // 被点击项的 ID
	ItemOrigin    int    `json:"item_origin"`              // 被点击项来源（0：直接搜索，1：联想词搜索，2：点击热搜词，3：大家都在听，4：搜索历史，5：默认搜索）
	ItemRank      int    `json:"item_rank"`                // 被点击项位置（从 1 开始）
	ItemRankType  int    `json:"item_rank_type"`           // 排序方式（0：默认排序，1：播放量，2：最新发布）
	ItemTitle     string `json:"item_title"`               // 被点击项的标题
	ItemType      int    `json:"item_type"`                // 被点击项类型（0：音频，1：UP 主，2：音单，4：声优，5：剧集，6：直播，7：特型，8：频道，10xx：专题卡子项）
	ItemCatalogID int    `json:"item_catalog_id"`          // 被点击项筛选分区（0：全部分区，1：有声漫画，2：广播剧，3：音乐，4：声音恋人，5：电台，6：日抓，7：听书，8：铃声，9：放松）
	ItemIsInsert  int    `json:"item_is_insert,omitempty"` // 被点击项是否被人工干预（0：否，1：是）
	Origin        int    `json:"origin"`                   // 来源页（0：首页，1 发现页，2：搜索结果页，3：大家都在听，5：索引页，6：其他）
	ResultCount   int    `json:"result_count"`             // 结果数量
	SearchType    int    `json:"search_type"`              // 搜索类型（1：UP 主，2：音单，3：综合，4：声优，5：剧集，6：直播，8：频道）
	UserID        *int64 `json:"user_id"`                  // 用户 ID
	// 搜索请求返回的 ops_request_misc 信息
	OpsRequestMisc string `json:"ops_request_misc"`

	// Generated parameters
	OS              int     `json:"os"`
	EquipID         string  `json:"equip_id"`
	BUVID           string  `json:"buvid"`
	Channel         string  `json:"channel"`
	SearchItemID    *int64  `json:"search_item_id,omitempty"`    // 点击专题卡子项时，设置为专题卡的 ID
	SearchItemTitle *string `json:"search_item_title,omitempty"` // 点击专题卡子项时，设置为专题卡的标题
	CreateTime      int64   `json:"create_time"`
	ModifiedTime    int64   `json:"modified_time"`

	// For push to OpenSearch parameters
	RequestID string `json:"request_id"`

	Staging bool `json:"staging,omitempty"` // 客户端上报的该数据是否是测试环境的数据
}

// Check check params
func (click *Click) Check(isOldAppData bool) error {
	// 未登录用户客户端会上传 user_id 为 0
	if click.UserID == nil {
		return errUserID
	}

	if click.EventType == nil || !isEventType(*click.EventType) {
		return errEventType
	}

	if click.Origin < 0 {
		return errOrigin
	}

	if isOldAppData {
		if click.Input == "" {
			return errEmptyInput
		}
	} else {
		if click.Query == "" {
			return errEmptyQuery
		}
		// TODO: 目前场景 2-本次搜索整体情况客户端上报处理比较复杂，后续需要补充对此场景的校验
		if click.EventType == nil || (*click.EventType == EventTypeClickSuggest && click.Input == "") {
			return errEmptyInput
		}
	}

	if click.SearchType != 0 {
		if !isSearchType(click.SearchType) {
			return errSearchType
		}
	}

	if click.ItemType != 0 {
		if !isItemType(click.ItemType) {
			return errItemType
		}
	}

	if click.ItemOrigin != 0 {
		if !isItemOrigin(click.ItemOrigin) {
			return errItemOrigin
		}
	}

	if click.ItemCatalogID != 0 {
		if !isItemCatalogID(click.ItemCatalogID) {
			return errItemCatalogID
		}
	}

	if click.HintCount < 0 ||
		click.ResultCount < 0 ||
		click.ItemID < 0 ||
		click.ItemRank < 0 ||
		click.IPV < 0 {
		return errNegativeNumber
	}
	return nil
}

// OpenSearchAppName get app name for opensearch
func (click Click) OpenSearchAppName() string {
	appParam, ok := service.OpenSearch.App(click.ItemType)
	if !ok {
		return ""
	}

	return appParam.Name
}

func isEventType(eventType int) bool {
	return eventType >= EventTypeClickSearch && eventType < EventTypeClickLimit
}

var searchTypeList = []int{
	searchTypeUP,
	searchTypeAlbum,
	searchTypeGeneral,
	searchTypeCV,
	searchTypeDrama,
	searchTypeLive,
	searchTypeChannel,
}

func isSearchType(searchType int) bool {
	return util.HasElem(searchTypeList, searchType)
}

// IsTopicCardItemType 被点击项是否是专题卡类型
func IsTopicCardItemType(itemType int) bool {
	return itemType >= itemTypeTopicCardMin && itemType <= itemTypeTopicCardMax
}

func isItemType(itemType int) bool {
	return (itemType >= itemTypeSound && itemType < itemTypeLimit) || IsTopicCardItemType(itemType)
}

func isItemOrigin(itemOrigin int) bool {
	return itemOrigin >= itemOriginDirect && itemOrigin < itemOriginDefaultLimit
}

func isItemCatalogID(itemCatalogID int) bool {
	return itemCatalogID >= searchCatalogIDAll && itemCatalogID < searchCatalogIDLimit
}
