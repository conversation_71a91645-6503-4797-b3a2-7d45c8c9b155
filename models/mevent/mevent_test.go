package mevent

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

const testEventID = 133

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(Simple{}, "id", "title", "type", "status", "draw_start_time", "draw_end_time", "attr", "start_time", "create_time", "end_time", "extended_fields")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Simple{}, "event_id", "title", "type", "status", "draw_start_time", "draw_end_time", "attr", "start_time", "end_time", "extended_fields")
	kc.Check(ExtendedFields{}, "time_offset", "draw_point_start_time", "draw_point_end_time")
}

func TestExpireDuration(t *testing.T) {
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time { return time.Unix(10, 0) })
	defer util.SetTimeNow(nil)
	e := Simple{
		EndTime: 1,
	}
	assert.Equal(time.Duration(1+15*util.SecondOneDay-10)*time.Second, e.ExpireDuration())
}

func TestEventTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startTime, endTime, err := EventTime(0)
	require.NoError(err)
	assert.Nil(startTime)
	assert.Nil(endTime)

	startTime, endTime, err = EventTime(testEventID)
	require.NoError(err)
	require.NotNil(startTime)
	assert.NotZero(*startTime)
	assert.NotNil(endTime)
}

const eventWithExtendFields = 148

func TestFindSimpleWithExtendedFields(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var drawInfo struct {
		DrawPrice int64 `json:"draw_price"`
	}

	e, err := FindSimpleWithExtendedFields(0, drawInfo)
	require.NoError(err)
	assert.Nil(e)
	assert.Empty(drawInfo)

	e, err = FindSimpleWithExtendedFields(eventWithExtendFields, &drawInfo)
	require.NoError(err)
	require.NotNil(e)
	assert.NotEmpty(e.ExtendedFields)
	assert.NotZero(drawInfo.DrawPrice)
}

func TestFindEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e, err := FindSimple(eventWithExtendFields)
	require.NoError(err)
	assert.NotNil(e)
}

func TestListOngoingSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e, err := ListOngoingSimple(nil)
	require.NoError(err)
	assert.NotEmpty(e)

	// 保证不会查询到
	eventType := 9
	e, err = ListOngoingSimple(&eventType)
	require.NoError(err)
	assert.NotNil(e)
	assert.Empty(e)
}

func TestListLiveRankOngoingSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e, err := ListLiveRankOngoingSimple()
	require.NoError(err)
	require.GreaterOrEqual(len(e), 1)
	assert.True(e[0].Attr.IsSet(AttrLiveRank))
}

func TestIsEventWithinValidTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testEventID := int64(8975465)
	require.NoError(Simple{}.DB().Delete("", "id = ?", testEventID).Error)
	res, err := IsEventWithinValidTime(testEventID)
	require.NoError(err)
	assert.False(res)

	// 测试活动在有效期间
	now := util.TimeNow().Unix()
	event := &Simple{
		ID:        testEventID,
		Title:     "测试活动",
		StartTime: now - 60,
		EndTime:   now + 60,
	}
	require.NoError(event.DB().Create(event).Error)
	res, err = IsEventWithinValidTime(testEventID)
	require.NoError(err)
	assert.True(res)

	// 测试活动不在有效期间
	util.SetTimeNow(func() time.Time {
		return time.Unix(now+600, 0)
	})
	defer util.SetTimeNow(nil)
	res, err = IsEventWithinValidTime(testEventID)
	require.NoError(err)
	assert.False(res)
}

func TestExtendedFields_TimeNow(t *testing.T) {
	assert := assert.New(t)

	util.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer util.SetTimeNow(nil)

	// 测试 time_offset 未设置时
	e := ExtendedFields{}
	assert.Equal(int64(1), e.TimeNow().Unix())

	// 测试 time_offset 设置时
	e = ExtendedFields{
		TimeOffset: 10,
	}
	assert.Equal(int64(11), e.TimeNow().Unix())

	// 测试 time_offset 为负值时
	e = ExtendedFields{
		TimeOffset: -1,
	}
	assert.Equal(int64(0), e.TimeNow().Unix())
}
