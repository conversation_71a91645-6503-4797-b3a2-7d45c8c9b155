package mevent

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 活动类型
const (
	TypeAudio = iota
	TypeImage
	TypeVideo
	TypeSpecial
	TypeSubscribe
	TypeLive
	TypeUpload
	TypeOther
	TypeLimit
)

// 活动显示状态
const (
	StatusApp = 1 + iota
	StatusAppSpecial
	StatusWebSpecial
	StatusWeb
	StatusListHidden
)

// 活动 attr
const (
	AttrComment = 1 + iota
	AttrVote
	AttrDraw
	AttrSubscribe
	AttrLiveRank
)

const simpleSelectExpr = "id, title, type, status, attr, draw_start_time, draw_end_time, start_time, create_time, end_time, extended_fields"

// TableName table name
func TableName() string {
	return "m_event"
}

// Simple 活动简要信息
type Simple struct {
	ID            int64        `gorm:"column:id" json:"event_id"`
	Title         string       `gorm:"column:title" json:"title"`
	Type          int          `gorm:"column:type" json:"type"`
	Status        util.BitMask `gorm:"column:status" json:"status"`
	DrawStartTime int64        `gorm:"column:draw_start_time" json:"draw_start_time"`
	DrawEndTime   int64        `gorm:"column:draw_end_time" json:"draw_end_time"`
	// TODO: 删除 CreateTime
	CreateTime int64 `gorm:"column:create_time" json:"-"`
	StartTime  int64 `gorm:"column:start_time" json:"start_time"`
	EndTime    int64 `gorm:"column:end_time" json:"end_time"`

	ExtendedFields string       `gorm:"column:extended_fields" json:"extended_fields"`
	Attr           util.BitMask `gorm:"column:attr" json:"attr"`
}

// DB the db instance of Simple model
func (s Simple) DB() *gorm.DB {
	return service.DB.Table(s.TableName())
}

// TableName table name
func (Simple) TableName() string {
	return TableName()
}

// ExpireDuration redis 过期时间（活动结束后 15 天）
func (s *Simple) ExpireDuration() time.Duration {
	return time.Unix(s.EndTime+15*util.SecondOneDay, 0).Sub(util.TimeNow())
}

// EventTime 活动开始结束时间
func EventTime(id int64) (st, ed *int64, err error) {
	s, err := FindSimple(id)
	if err != nil || s == nil {
		return nil, nil, err
	}
	return &s.StartTime, &s.EndTime, nil
}

// FindSimple 查询活动简要信息
func FindSimple(id int64) (*Simple, error) {
	var s Simple
	err := s.DB().Select(simpleSelectExpr).Where("id = ?", id).Take(&s).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &s, nil
}

// FindSimpleWithExtendedFields 查询活动信息，并 unmarshal extended_fields 到 v
func FindSimpleWithExtendedFields(id int64, v interface{}) (*Simple, error) {
	s, err := FindSimple(id)
	if err != nil || s == nil {
		return nil, err
	}
	if s.ExtendedFields != "" {
		err = json.Unmarshal([]byte(s.ExtendedFields), v)
		if err != nil {
			return nil, err
		}
	}
	return s, nil
}

// ListOngoingSimple 进行中的活动简要信息
// NOTICE: 这里的进行中的活动指的是未结束的活动，包括未开始的活动（即隐藏的活动）
func ListOngoingSimple(eventType *int) ([]*Simple, error) {
	now := util.TimeNow().Unix()
	db := service.DB.Select(simpleSelectExpr).Where("end_time > ?", now)
	if eventType != nil {
		db = db.Where("type = ?", *eventType)
	}
	var events []*Simple
	err := db.Find(&events).Error
	if err != nil {
		return nil, err
	}
	return events, nil
}

// ListLiveRankOngoingSimple 列出有直播打榜的活动简要信息
// NOTICE: 这里的进行中的活动指的是未结束的活动，包括未开始的活动（即隐藏的活动）
func ListLiveRankOngoingSimple() ([]*Simple, error) {
	var attr util.BitMask
	attr.Set(AttrLiveRank)
	now := util.TimeNow().Unix()
	db := service.DB.Select(simpleSelectExpr).Where("end_time > ?", now)
	db = db.Where("attr & ?", attr)
	var events []*Simple
	err := db.Find(&events).Error
	if err != nil {
		return nil, err
	}
	return events, nil
}

// IsEventWithinValidTime 是否为有效期间的活动
func IsEventWithinValidTime(eventID int64) (bool, error) {
	extendedFields := &ExtendedFields{}
	event, err := FindSimpleWithExtendedFields(eventID, extendedFields)
	if err != nil || event == nil {
		return false, err
	}
	now := extendedFields.TimeNow().Unix()
	return event.StartTime <= now && now < event.EndTime, nil
}

// ExtendedFields 活动配置
type ExtendedFields struct {
	// 通用配置
	TimeOffset int64 `json:"time_offset,omitempty"` // 支持配置活动时间偏移

	// 抽奖积分
	DrawPointStartTime int64 `json:"draw_point_start_time,omitempty"`
	DrawPointEndTime   int64 `json:"draw_point_end_time,omitempty"`
}

// TimeNow 当前时间（当前时间 + 时间偏移量），活动相关的时间获取需要使用该方法来获取
func (e *ExtendedFields) TimeNow() time.Time {
	if e.TimeOffset == 0 {
		return util.TimeNow()
	}
	return util.TimeNow().Add(time.Duration(e.TimeOffset) * time.Second)
}
