FROM golang:1.15-alpine3.12 AS geoip

ARG version
ARG proxy
ARG goproxy

# Download packages from aliyun mirrors
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk --update add --no-cache ca-certificates git curl \
  && mkdir -p /usr/share/GeoIP

# download ipdb files
# ************* bvc-nerve.bilibili.co
# ************ upos-sz-office.bilibili.co
RUN v4url=`curl -H "Host: bvc-nerve.bilibili.co" http://*************/ipipfile/stable_v4_flagship.ipdb -o /dev/null -s -v 2>&1 | grep -oE 'Location: (.*)' | awk '{print $2}' | sed -e 's/upos-sz-office\.bilibili\.co/************/'` \
  && curl -H 'Host: upos-sz-office.bilibili.co' "$v4url" -o /usr/share/GeoIP/stable_v4_flagship.ipdb \
  && v6url=`curl -H "Host: bvc-nerve.bilibili.co" http://*************/ipipfile/stable_v6_flagship.ipdb -o /dev/null -s -v 2>&1 | grep -oE 'Location: (.*)' | awk '{print $2}' | sed -e 's/upos-sz-office\.bilibili\.co/************/'` \
  && curl -H 'Host: upos-sz-office.bilibili.co' "$v6url" -o /usr/share/GeoIP/stable_v6_flagship.ipdb

RUN hp() { http_proxy=$proxy https_proxy=$proxy no_proxy=localhost,*********/8,10.0.0.0/8,**********/12,***********/16,**********/10,*********/4,240.0.0.0/4,docker00 $@; }; \
  hp git clone --depth 1 https://github.com/maxmind/geoipupdate /go/src/github.com/maxmind/geoipupdate \
  && cd /go/src/github.com/maxmind/geoipupdate/cmd/geoipupdate \
  && GO111MODULE=on GOPROXY=$goproxy go build -ldflags "-X main.defaultConfigFile=/etc/GeoIP.conf \
    -X main.defaultDatabaseDirectory=/usr/share/GeoIP" \
  && mv /go/src/github.com/maxmind/geoipupdate/cmd/geoipupdate/geoipupdate /go/bin/geoipupdate \
  && cp /go/src/github.com/maxmind/geoipupdate/conf/GeoIP.conf.default /etc/GeoIP.conf \
  && sed -i -e "s/^# \(Proxy\) .*/\1 ${proxy}/" \
    -e "s/^\(AccountID\) .*/\1 167370/" \
    -e "s/^\(LicenseKey\) .*/\1 8dtO5Pfg8Bkl1om1/" \
    -e 's/^# \(DatabaseDirectory\) .*/\1 \/usr\/share\/GeoIP/' /etc/GeoIP.conf

RUN geoipupdate -v

FROM scratch

COPY --from=geoip /usr/share/GeoIP /usr/share/GeoIP
